<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<?fileVersion 4.0.0?><cproject storage_type_id="org.eclipse.cdt.core.XmlProjectDescriptionStorage">
	<storageModule moduleId="org.eclipse.cdt.core.settings">
		<cconfiguration id="com.android.toolchain.gcc.230705466">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="com.android.toolchain.gcc.230705466" moduleId="org.eclipse.cdt.core.settings" name="Default">
				<externalSettings/>
				<extensions>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.MakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.VCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactName="${ProjName}" buildProperties="" description="" id="com.android.toolchain.gcc.230705466" name="Default" parent="org.eclipse.cdt.build.core.emptycfg">
					<folderInfo id="com.android.toolchain.gcc.230705466.655905133" name="/" resourcePath="">
						<toolChain id="com.android.toolchain.gcc.1159120355" name="com.android.toolchain.gcc" superClass="com.android.toolchain.gcc">
							<targetPlatform binaryParser="" id="com.android.targetPlatform.1432809250" isAbstract="false" superClass="com.android.targetPlatform"/>
							<builder buildPath="${workspace_loc:/IptAndroidDemo/jni}" command="ndk-build" id="com.android.builder.229978739" keepEnvironmentInBuildfile="false" managedBuildOn="false" name="Android Builder" superClass="com.android.builder"/>
							<tool id="com.android.gcc.compiler.1535656494" name="Android GCC Compiler" superClass="com.android.gcc.compiler">
								<option id="com.android.gcc.option.includePath.832854807" superClass="com.android.gcc.option.includePath" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/IptAndroidDemo/jni}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;D:\_WORK\_ANDROID_WORK\_IDE\android-ndk-r9d\platforms\android-19\arch-arm\usr\include&quot;"/>
									<listOptionValue builtIn="false" value="../../../source/_pub"/>
								</option>
								<inputType id="com.android.gcc.inputType.738659531" superClass="com.android.gcc.inputType"/>
							</tool>
						</toolChain>
					</folderInfo>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
	</storageModule>
	<storageModule moduleId="cdtBuildSystem" version="4.0.0">
		<project id="IptAndroidDemo.null.1752130790" name="IptAndroidDemo"/>
	</storageModule>
	<storageModule moduleId="refreshScope" versionNumber="1">
		<resource resourceType="PROJECT" workspacePath="/IptAndroidDemo"/>
	</storageModule>
	<storageModule moduleId="scannerConfiguration">
		<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		<scannerConfigBuildInfo instanceId="com.android.toolchain.gcc.230705466;com.android.toolchain.gcc.230705466.655905133;com.android.gcc.compiler.1535656494;com.android.gcc.inputType.738659531">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId="com.android.AndroidPerProjectProfile"/>
		</scannerConfigBuildInfo>
	</storageModule>
</cproject>
