#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
输入法内核替换脚本工具

使用方法：
1. 将内核产物直接放在工程目录下（可以直接在AndroidStudio的terminal中执行内核提测邮件中的wget命令）
2. 设置本机的NDKPATH
3. python CoreReplace.py

4. 执行后，根据terminal的输出提示，将so文件大小和词库md5值替换到代码指定位置
5. jni目录下的内容commit， msg含[corecompile]，线上编译so
6. 编译产物出来后，替换libs下的so文件，和其他内容一起提交

脚本会执行如下几步：
1. copy file: 将内核的.h和.a文件拷贝到工程目录下的正确位置
2. unzip Android.zip & rename: 解压内核词库文件，进行适当重命名操作
3. copy Android/* -> asset/ipt/* : 词库文件拷贝到asset的对应位置
4. NDK : ndk build编译so，并拷贝到libs目录
5. print so size and md5: 打印so的文件大小和词库的md5值，用于手动拷贝到代码中的对应位置

"""
__author__      = "ChenDanfeng"
__copyright__   = "Copyright 2019, Baidu Inc. Baidu Input Team"

import os
from shutil import copyfile
import zipfile
import subprocess
import hashlib

# 替换本机的NDKPATH
NDKPATH='/Users/<USER>/Library/Android/android-ndk-r10e'

origin_file = [
"app_map.bin",
"cz3.bin",
"bh4.bin",
"emoticon.kwd",
"ft2.bin",
"hz2.bin",
"idmap.idm",
"sylian.bin",
"uz2.bin",
"wb2.bin",
"wb298.bin",
"email.txt",
"hzmdl_dnn.bin"
]

new_file = [
"app_map_file.bin",
"cz3.cz",
"bh2.bin",
"yan.kwd",
"Ft.bin",
"Hz.bin",
"yan.idm",
"syml.bin",
"Uz2.bin",
"Wb.bin",
"Wb98.bin",
"email",
"iptwt_20151202.bin"
]

def getMd5(filePath):
    md5 = None
    if os.path.isfile(filePath):
        f = open(filePath,'rb')
        md5Obj = hashlib.md5()
        md5Obj.update(f.read())
        hashCode = md5Obj.hexdigest()
        f.close()
        md5 = str(hashCode)
    return md5


# main函数
if __name__ == "__main__":

    projectDir = os.path.abspath(".")
    outputDir = projectDir + '/output/corearm32/'
    jniDir = projectDir + '/inputcore/jni/ImeCore_NDK_Armeabi/jni/'
    bakCoreDir = projectDir + '/inputcore/jni/ImeCore_NDK_Armeabi/core/'
    wordLibPath = outputDir + '/Android/data/Android/'
    iptDir = projectDir + '/inputcore/src/release/assets/ipt/'

    srcIptPath = projectDir + '/inputcore/jni/ImeCore_NDK_Armeabi/libs/armeabi-v7a/libinputcore-new-hw.so'
    destIptPath = projectDir + '/inputcore/src/release/libs/armeabi-v7a/libinputcore.so'

    print '##################### Step0:: unzip core resources ########################'
    subprocess.call("tar zxvf output.tar.gz", shell=True)

    print '##################### Step1:: copy file ########################'

    copyfile(outputDir + '_pub/_pub_iptcore.h', jniDir + '_pub_iptcore.h')
    copyfile(outputDir + '_pub/_pub_debug.h', jniDir + '_pub_debug.h')
    copyfile(outputDir + '_pub/_pub_version.h', jniDir + '_pub_version.h')
    copyfile(outputDir + 'libiptcore_arm32.a', jniDir + 'libiptcore-new.a')
    copyfile(outputDir + 'libiptcore_arm32.a', bakCoreDir + 'debug/libiptcore_debug.a')
    copyfile(outputDir + 'libiptcore_arm32.a', bakCoreDir + 'release/libiptcore.a')
    print 'success'

    print '##################### Step2:: unzip Android.zip and Rename #####################'
    zip_ref = zipfile.ZipFile(outputDir + 'Android.zip', 'r')
    zip_ref.extractall(outputDir + 'Android')
    zip_ref.close()

    wordlibs = os.listdir(wordLibPath)
    for file in wordlibs:
        try:
            index = origin_file.index(file)
        except:
            index = -1
        else:
            if index >= 0:
                print "find::" + str(index) + ", origin:" + file + "->" + new_file[index]
                os.rename(wordLibPath + file, wordLibPath + new_file[index])
    print 'success'

    print '##################### Step3:: copy Android/* -> asset/ipt/* #####################'

    wordlibs = os.listdir(wordLibPath)
    for file in wordlibs:
        if os.path.isfile(iptDir + file):
            print "replace:: ./output/corearm32/Android/data/Android/" + file + "-> ./inputcore/assets/ipt/" + file
            copyfile(wordLibPath + file, iptDir + file)
    print 'success'

    print '##################### Step4:: NDK build #####################'
    cmd1 = 'cd %s' % jniDir
    cmd2 = '%s/ndk-build' % NDKPATH
    cmd = cmd1 + " && " + cmd2
    subprocess.call(cmd, shell=True)

    copyfile(srcIptPath, destIptPath)
    print 'success'

    print '##################### Step5:: print so size & md5 #####################'
    print 'replace this fileSize to InputCoreConstants.java...'
    print 'int FILE_DATA_INPUTCORE_FILE_SIZE = ' + str(os.path.getsize(destIptPath))

    iptWtFile = iptDir + "iptwt_20151202.bin"
    print 'int IPTFILE_WT_LENGTH = ' + str(os.path.getsize(iptWtFile))

    print ''
    print 'replace these md5s to InputCoreConstants.java...'
    print 'String czMd5 = "' + getMd5(iptDir + 'cz3.cz') + '";'
    print 'String hzMd5 = "' + getMd5(iptDir + 'Hz.bin') + '";'
    print 'String ftMD5 = "' + getMd5(iptDir + 'Ft.bin') + '";'
    print 'String bhMD5 = "' + getMd5(iptDir + 'bh2.bin') + '";'
    print 'String wbMD5 = "' + getMd5(iptDir + 'Wb.bin') + '";'
    print 'String enMD5 = "' + getMd5(iptDir + 'en2.bin') + '";'
    print 'String chCorMD5 = "' + getMd5(iptDir + 'ch_cor2.bin') + '";'
    print 'String kpMD5 = "' + getMd5(iptDir + 'kp.bin') + '";'
    print 'String xhyMD5 = "' + getMd5(iptDir + 'xhy.bin') + '";'
    print 'String hzLabelMD5 = "' + getMd5(iptDir + 'hz_label.bin') + '";'
    print 'String hzToneMD5 = "' + getMd5(iptDir + 'hz_tone.bin') + '";'
    print 'String iptwtMD5 = "' + getMd5(iptDir + 'iptwt_20151202.bin') + '";'