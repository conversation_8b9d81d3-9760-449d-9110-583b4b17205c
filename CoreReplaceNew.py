#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
输入法内核替换脚本工具

【准备工作】
1、请确认你的python版本为2.7，ndk版本为r17c。ndk版本必须严格为r17c，不可以更高或更低。
2、在local.properties中，添加自己的ndk路径，示例写法如下：ndk.dir=/Users/<USER>/Library/Android/android-ndk-r17c

【标准替换步骤】：
1. 将内核产物直接放在工程目录下（可以直接在AndroidStudio的terminal中执行内核提测邮件中的wget命令，也可以手动复制url到浏览器下载后，放在项目的根目录下）
2. 直接在terminal中运行 python CoreReplaceNew.py，脚本会自动化完成编译、常量替换等工作。请务必留意脚本运行中是否发生了异常。
3. jni目录下的内容commit， msg含[corecompile]，线上编译so
4. 编译产物出来后，替换libs下的所有so文件，包括32和64位两个版本。(除名称包含inputcore的其他几个so，包括libiptcore.so，以及paddle等)
5. 修改gradle.properties文件中的版本，更新RELEASE_NOTE.md,将so和其他内容一起提测。msg含[test][newcore]

【本地调试】
适用于本地debug，或者本地修改JNI代码的情况，需要本地编译so自测，步骤如下：
1. 同上述第1步
2. [Optional] 把build.gradle里面的useCmake变量从false改成true，同步一下（便于jni代码开发），写好jni后再改回false
3. python CoreReplaceNew.py # 不带参数，执行本地全量替换内核操作，包括：替换词典和内核产物->编译so->输出信息
4. 调试完毕后，再重新执行【标准替换步骤】

【本地Cmake调试】
本地cmake调试的目的是可以进行一定的native代码调试，即直接debug到native层（实际尝试下来没有上述本地调试方便）
1. 同上述第1步
2. 把build.gradle里面的useCmake变量从false改成true，同步一下
3. 把./iptcore/src/release/libs下面的各种so，拷贝到./iptcore/src/release/jniLibs下面，删除libiptcore.so（动态链接库需要放在jniLibs下面，编译生成的so是在build下面，根据这个原理理解这个步骤）
4. 调试完毕后，useCmake=false，再重新执行【标准替换步骤】
5. 注意：JNI目前比较简单，不一定每次都用cmake调试，因此可能存在编译不过的情况，需要开发同学定期维护

【打包内核体验包 - 手动替换需要的静态库，适用于内核同学调试】
1. 将输入法 APK 放到工程的 base-apk 目录下，重命名为：main_line_release.apk
2. 将需要替换的文件手动拷贝到iptcore/jni下，如替换jptcore/jni/lib下的.a文件来打印日志
3. 在项目根目录下执行脚本 CoreReplaceNew.py apkbuild-s。默认沙盒签名
4. OEM项目需要正式签名的情况下，增加-f参数。命令为：CoreReplaceNew.py apkbuild-s -f

【打包内核体验包 - 使用提测邮件下载的zip包替换，适用于客户端同学】
1. 将输入法 APK 放到工程的 base-apk 目录下，重命名为：main_line_release.apk
2. 将内核产物直接放在项目根目录下（可以直接在项目根目录下执行内核提测邮件中的 wget 命令）
3. 在项目根目录下执行脚本 CoreReplaceNew.py apkbuild-c。默认沙盒签名
4. OEM项目需要正式签名的情况下，增加-f参数。命令为：CoreReplaceNew.py apkbuild-c -f
具体技术细节详见：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/VuNIMoeyA2/UgW1aEIRvJ/8Iv5gWl0rUVezY

脚本会执行如下几步：
1. copy file: 将内核的.h和.a文件拷贝到工程目录下的正确位置
2. unzip Android.zip & rename: 解压内核词库文件，进行适当重命名操作
3. copy Android/* -> asset/ipt/* : 词库文件拷贝到asset的对应位置
4. NDK : ndk build编译so，并拷贝到libs目录
5. print so size and md5: 打印so的文件大小和词库的md5值，用于手动拷贝到代码中的对应位置

"""
__author__      = "ChenDanfeng"
__copyright__   = "Copyright 2019, Baidu Inc. Baidu Input Team"

import os
import shutil
import sys
from shutil import copyfile
from shutil import _samefile
import zipfile
import subprocess
import hashlib
import fileinput

ndk_dir = None

# 词库module定义的Flavor - 内核词库的压缩包名 - 解压后的路径
productFlavors = {
    'MainLine': ('Android', '/Android/data/Android/'),
    'Honor': ('Honor', '/Honor/data/Android_OEM/Honor/', {'cz5down.bin': 'cz5.bin', 'cz5.bin': 'xxxxxx.bin'}),
    'Huawei': ('Huawei', '/Huawei/data/Android_OEM/Huawei/', {'cz5down.bin': 'cz5.bin', 'cz5.bin': 'xxxxxx.bin'}),
    'Mi': ('Xiaomi', '/Xiaomi/data/Android_OEM/Xiaomi/'),
    'OPPO': ('Oppo', '/Oppo/data/Android_OEM/Oppo/', {'cz5down.bin': 'cz5.bin', 'cz5.bin': 'xxxxxx.bin'}),
    'VIVO': ('Vivo', '/Vivo/data/Android_OEM/Vivo/', {'cz5down.bin': 'cz5.bin', 'cz5.bin': 'xxxxxx.bin'}),
}

def getMd5(filePath):
    """
    获取文件md5
    Args:
    filePath: 文件路径
    """
    md5 = None
    if os.path.isfile(filePath):
        f = open(filePath, 'rb')
        md5Obj = hashlib.md5()
        md5Obj.update(f.read())
        hashCode = md5Obj.hexdigest()
        f.close()
        md5 = str(hashCode)
    return md5

def isM1Chip():
    """
    是否M1芯片
    :return: 如果是在Apple M1设备，则返回true
    """
    cmd = "/usr/sbin/sysctl -n machdep.cpu.brand_string"
    res = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE)
    result = res.stdout.read().strip()
    print result + ', isM1Chip : ' + str(result.find("Apple M"))
    return result.find("Apple M") >= 0

def help():
    print '''        ==** 脚本使用方法 **==
    1. python CoreReplaceNew.py 1 # 带一个参数，执行第1步，拷贝词典和内核产物
    2. python CoreReplaceNew.py 2 # 带一个参数，从服务端替换好编译的so之后，执行第2步，print 一些产物信息
    3. python CoreReplaceNew.py 4 # 带一个参数，仅执行 ndk 的编译，生成 so 文件
    4. python CoreReplaceNew.py 5 # 带一个参数，用于内核同学打包体验 apk 版本，需将 main_line_release.apk
       放到工程根目录 base-apk 文件夹下
    5. python CoreReplaceNew.py # 不带参数，用于本地调试，执行本地ndk编译，和完整替换词库流程
        '''

def writeMd5(classFile, dictPath, dictName, variableName, extraInfo=""):
    """
    自动为词库组件写入词库的MD5
    :param classFile: Java类文件
    :param dictPath: 词库所在的路径
    :param dictName: 词库的名字
    :param variableName: Java类中MD5的变量名称
    :param extraInfo: 额外的注释信息
    :return: 无
    """
    classFile.write('    // ' + dictName + extraInfo + '\n')
    if os.path.isfile(dictPath + dictName):
        classFile.write('    String ' + variableName + ' = "' + getMd5(dictPath + dictName) + '";\n\n')
    else:
        classFile.write('    String ' + variableName + ' = null;\n\n')

def getCoreVersion(filePath):
    """
    解析内核的版本号
    :param filePath:内核 _pub_version.h文件的路径
    :return: 内核的版本号，格式为W.X.Y.Z
    """
    for line in fileinput.input(filePath):
        if line.find('#define CORE_VERSION_VAL CORE_VERSION_DEF') >= 0:
            version = line[(line.find("(") + 1):line.find(")")].replace(" ", "").replace(",", ".")
            return version
    raise Exception("Can't Find Version!!!")



class Properties:
    """
    读取local.properties的工具类
    """

    def __init__(self, file_name):
        self.file_name = file_name
        self.properties = {}

        with open(self.file_name) as f:
            for line in f:
                line = line.strip()
                if line.find('=') > 0 and not line.startswith('#'):
                    strs = line.split('=')
                    self.properties[strs[0].strip()] = strs[1].strip()

    def get(self, key, default_value=''):
        """
        获取指定key的value
        :param key: 想要获取的key值
        :param default_value: 如果没有key，则返回的默认值
        :return: 获取的value
        """
        if key in self.properties:
            return self.properties[key]
        return default_value


def execute(doCopyFiles, doWriteMd5, doNdkBuild, doApkBuild, doFormalSign = False):
    """
    执行脚本替换任务
    """
    print("#### 开始执行脚本：stepCopyFiles=%s, stepWriteMD5=%s, stepNdk=%s, stepApk=%s ####"
          % (doCopyFiles, doWriteMd5, doNdkBuild, doApkBuild))
    projectDir = os.path.abspath(".")
    jniDir = projectDir + '/iptcore/jni/'
    iptDirFormat = projectDir + '/dict/product/%s/assets/dict/'
    iptDictConstFormat = projectDir + '/dict/product/%s/src/main/java/com/baidu/ImeCoreDictConsts.java'

    srcIptDir = projectDir + '/iptcore/libs/armeabi-v7a/'
    destIptDir = projectDir + '/iptcore/src/release/libs/armeabi-v7a/'
    wxDestIptDir = projectDir + '/iptcore-wenxin/libs/armeabi-v7a/'

    srcIptDir64 = projectDir + '/iptcore/libs/arm64-v8a/'
    destIptDir64 = projectDir + '/iptcore/src/release/libs/arm64-v8a/'
    wxDestIptDir64 = projectDir + '/iptcore-wenxin/libs/arm64-v8a/'

    # jar 存放目录
    jarDir = projectDir + '/iptcore/libs/'
    # ocr 前后缀
    bdsocrJarPrefix = 'bdsocr_'
    bdsocrJarSuffix = '.jar'

    if doCopyFiles:
        print '##################### Step0:: unzip core resources ########################'

        if (os.path.exists("coreandroid.tar.gz") and os.path.exists("output.tar.gz")):
            raise Exception("本地存在coreandroid和ouput两个内核文件。请确认你需要编译的内核并删除其中一个。")
        elif (os.path.exists("output.tar.gz")):
            rootFileName = 'output'
        elif (os.path.exists("coreandroid.tar.gz")):
            rootFileName = 'coreandroid'
        else:
            raise Exception("找不到待编译的内核文件")

        outputDir = projectDir + '/' + rootFileName + '/corearm32/'
        outputDir64 = projectDir + '/' + rootFileName + '/corearm64/'
        wordLibPathFormat = outputDir + '%s'

        print '##################### Delete Existed Files ########################'
        subprocess.call("rm -rf iptcore/libs/arm64-v8a", shell=True)
        subprocess.call("rm -rf iptcore/libs/armeabi-v7a", shell=True)
        subprocess.call("rm -rf iptcore/obj/local", shell=True)
        subprocess.call("rm -rf output", shell=True)

        subprocess.call("tar zxvf " + rootFileName + ".tar.gz", shell=True)

        print '##################### Step1:: copy file ########################'

        copyfile(outputDir + '_pub/_pub_iptcore.h', jniDir + '_pub_iptcore.h')
        copyfile(outputDir + '_pub/_pub_debug.h', jniDir + '_pub_debug.h')
        copyfile(outputDir + '_pub/_pub_version.h', jniDir + '_pub_version.h')
        copyfile(outputDir + '_pub/_pub_iptpad.h', jniDir + '_pub_iptpad.h')
        copyfile(outputDir + '_pub/_cyp_wrapper.h', jniDir + '_cyp_wrapper.h')
        copyfile(outputDir + '_pub/_pub_handwrite.h', jniDir + '_pub_handwrite.h')
        copyfile(outputDir + '_pub/_pub_util.h', jniDir + '_pub_util.h')

        copyfile(outputDir + 'libiptcore_arm32.a', jniDir + 'lib/libiptcore.a')
        copyfile(outputDir + 'libBaiduOcrSDK.so', jniDir + 'lib/libBaiduOcrSDK.so')
        copyfile(outputDir + 'libocr.so', jniDir + 'lib/libocr.so')

        copyfile(outputDir64 + 'libiptcore_arm64.a', jniDir + 'lib64/libiptcore.a')
        copyfile(outputDir64 + 'libBaiduOcrSDK.so', jniDir + 'lib64/libBaiduOcrSDK.so')
        copyfile(outputDir64 + 'libocr.so', jniDir + 'lib64/libocr.so')

        # 拷贝文心和分流模型到wenxin module
        # copyfile(outputDir64 + 'libflowmodel-lib.so', wxDestIptDir + 'libflowmodel-lib.so')
        # copyfile(outputDir64 + 'libflowmodel-lib.so', wxDestIptDir64 + 'libflowmodel-lib.so')
        # copyfile(outputDir64 + 'libpaddle_light_api_shared.so', wxDestIptDir + 'libpaddle_light_api_shared.so')
        # copyfile(outputDir64 + 'libpaddle_light_api_shared.so', wxDestIptDir64 + 'libpaddle_light_api_shared.so')

        bdsocrOldFileName = ""
        for filename in os.listdir(jarDir):
            if filename.startswith(bdsocrJarPrefix) and filename.startswith(bdsocrJarPrefix):
                bdsocrOldFileName = filename
                print(bdsocrOldFileName)
        # 删除旧文件
        if len(bdsocrOldFileName) > 0 and os.path.exists(jarDir + bdsocrOldFileName):
            os.remove(jarDir + bdsocrOldFileName)
        else:
            print("The file: " + jarDir + bdsocrOldFileName + " does not exist")

        bdsocrFileName = ""
        for filename in os.listdir(outputDir):
            if filename.startswith(bdsocrJarPrefix) and filename.startswith(bdsocrJarPrefix):
                bdsocrFileName = filename
                print(bdsocrFileName)
        # copy 新文件
        copyfile(outputDir + bdsocrFileName, jarDir + bdsocrFileName)

        print 'success'

        print '##################### Step2:: unzip Android.zip and Rename #####################'
        for flavor in productFlavors:
            zip_ref = zipfile.ZipFile(outputDir + productFlavors[flavor][0] + ".zip", 'r')
            zip_ref.extractall(outputDir + productFlavors[flavor][0])
            zip_ref.close()

        print 'success'

        print '##################### Step3:: copy Android/* -> asset/ipt/* #####################'

        for flavor in productFlavors:
            iptDir = iptDirFormat % flavor
            wordLibPath = wordLibPathFormat % productFlavors[flavor][1]
            wordlibs = os.listdir(wordLibPath)

            for file in wordlibs:
                targetFile = file
                if len(productFlavors[flavor]) > 2:
                    # 如果有需要替换的词库
                    for k, v in productFlavors[flavor][2].items():
                        if k == file:
                            targetFile = v
                if os.path.isfile(iptDir + targetFile):
                    if _samefile(wordLibPath + file, iptDir + targetFile):
                        print "continue for same file" + " from: " + wordLibPath + file + " to: " + iptDir + targetFile
                    else:
                        print "replace::" + wordLibPath + file + "->\n======== " + iptDir + targetFile
                        copyfile(wordLibPath + file, iptDir + targetFile)
        print 'success'

    if doNdkBuild:
        print '##################### Step4:: NDK build #####################'
        cmd1 = 'cd %s' % jniDir
        cmd2 = '%s/ndk-build' % ndk_dir
        if isM1Chip():
            print 'Chip: Apple M1'
            cmd2 = 'arch -x86_64 /bin/bash -c %s/ndk-build' % ndk_dir
        cmd = cmd1 + " && " + cmd2
        print "ndk build cmd: " + cmd
        subprocess.call(cmd, shell=True)

#         copyfile(outputDir + 'cuid-mini-1.1.0.jar', jarDir + 'cuid-mini-1.1.0.jar')

        copyfile(srcIptDir + 'libiptcore.so', destIptDir + 'libiptcore.so')
        copyfile(srcIptDir + 'libBaiduOcrSDK.so', destIptDir + 'libBaiduOcrSDK.so')
        copyfile(srcIptDir + 'libocr.so', destIptDir + 'libocr.so')
#         copyfile(srcIptDir + 'libc++_shared.so', destIptDir + 'libc++_shared.so')
        # copyfile(srcIptDir + 'libpaddle_light_api_shared.so', destIptDir + 'libpaddle_light_api_shared.so')
#         copyfile(srcIptDir + 'libnn-rank.so', destIptDir + 'libnn-rank.so')

        copyfile(srcIptDir64 + 'libiptcore.so', destIptDir64 + 'libiptcore.so')
        copyfile(srcIptDir64 + 'libBaiduOcrSDK.so', destIptDir64 + 'libBaiduOcrSDK.so')
        copyfile(srcIptDir64 + 'libocr.so', destIptDir64 + 'libocr.so')
#         copyfile(srcIptDir64 + 'libc++_shared.so', destIptDir64 + 'libc++_shared.so')
        # copyfile(srcIptDir64 + 'libpaddle_light_api_shared.so', destIptDir64 + 'libpaddle_light_api_shared.so')
        # copyfile(srcIptDir64 + 'libnn-rank.so', destIptDir64 + 'libnn-rank.so')
        print 'success'

    if doWriteMd5:
        print '##################### Step5:: auto write version & so size & dict md5 #####################'

        for flavor in productFlavors:
            iptDir = iptDirFormat % flavor
            iptDictConstPath = iptDictConstFormat % flavor

            dictConstFile = open(iptDictConstPath, 'w')

            dictConstFile.write("package com.baidu;\n\n")

            dictConstFile.write("public interface ImeCoreDictConsts {\n\n")

            dictConstFile.write('    // 词库大小：手写模板大小\n')
            dictConstFile.write('    int IPTFILE_WT_LENGTH = ' + str(os.path.getsize(iptDir + "hw7.bin")) + ';\n\n')
            dictConstFile.write('    // 词库大小：cz5.bin的大小\n')
            dictConstFile.write('    int IPTFILE_CZ_LENGTH = ' + str(os.path.getsize(iptDir + "cz5.bin"))  + ';\n\n')
            dictConstFile.write('    // 词库大小：ipt3sym_sys.bin 的大小 \n')
            dictConstFile.write('    int IPTFILE_SYS_LENGTH = ' + str(os.path.getsize(iptDir + "ipt3sym_sys.bin"))
            + ';\n\n')

            writeMd5(dictConstFile, iptDir, 'cz5.bin', 'czMd5')
            writeMd5(dictConstFile, iptDir, 'fanti3.bin', 'ftMD5')
            writeMd5(dictConstFile, iptDir, 'bh4.bin', 'bhMD5')
            writeMd5(dictConstFile, iptDir, 'wb486.bin', 'wbMD5', ', 五笔86的输入方案')
            writeMd5(dictConstFile, iptDir, 'wb498.bin', 'wb98MD5', ', 五笔98的输入方案')
            writeMd5(dictConstFile, iptDir, 'en5.bin', 'enMD5')
            writeMd5(dictConstFile, iptDir, 'hw7.bin', 'iptwtMD5')
            writeMd5(dictConstFile, iptDir, 'ipt3sym_sys.bin', 'symMD5')
            writeMd5(dictConstFile, iptDir, 'iec3dict.bin', 'correctMD5')
            writeMd5(dictConstFile, iptDir, 'py3zhuyin.bin', 'zhuyinMD5')
            writeMd5(dictConstFile, iptDir, 'blind_assistance.bin', 'blindAssistanceMD5')
            writeMd5(dictConstFile, iptDir, 'py3corr2_tone.bin', 'pyToneMD5')
            writeMd5(dictConstFile, iptDir, 'py3transprob.bin', 'py3transprobMD5')
            writeMd5(dictConstFile, iptDir, 'ttffilter.ini', 'ttffilterMD5')
            writeMd5(dictConstFile, iptDir, "sens.bin", "sensMD5")
            writeMd5(dictConstFile, iptDir, 'app_map2.bin', 'appMap2MD5')
            writeMd5(dictConstFile, iptDir, 'hw_lic7.bin', 'hwLic7MD5')

            # 这3个文件内核会做写入，校验也无意义，故不提供MD5
            # writeMd5(dictConstFile, iptDir, 'xhy2.bin', 'xhyMD5')
            # writeMd5(dictConstFile, iptDir, 'cj2dict.bin', 'cangjieMD5')
            # writeMd5(dictConstFile, iptDir, 'cj2dict_quick.bin', 'cangjie_quickMD5')

            dictConstFile.writelines("}")
            dictConstFile.close()

        # 写入词库版本和大小
        internalConstFile =\
            open(projectDir + '/iptcore/src/main/java/com/baidu/iptcore/ImeCoreInternalConsts.java', 'w')

        internalConstFile.write("package com.baidu.iptcore;\n\n")
        internalConstFile.write("interface ImeCoreInternalConsts {\n\n")

        internalConstFile.write('    // 内核文件的版本号\n')
        internalConstFile.write('    String FILE_DATA_IPTCORE_VERSION = "' \
                                + getCoreVersion(projectDir + '/iptcore/jni/_pub_version.h') + '";\n\n')

        internalConstFile.write('    // 32位so大小\n')
        internalConstFile.write('    int FILE_DATA_INPUTCORE_FILE_SIZE = ' \
                                + str(os.path.getsize(destIptDir + 'libiptcore.so')) + ';\n\n')
        internalConstFile.write('    // 64位so大小\n')
        internalConstFile.write('    int FILE_DATA_INPUTCORE_FILE_SIZE_64BIT = ' \
                                + str(os.path.getsize(destIptDir64 + 'libiptcore.so')) + ';\n\n')

        internalConstFile.writelines("}")
        internalConstFile.close()


    if doApkBuild:
        print '##################### Step6:: build demo apk #####################'
        baseApkPatch = './base-apk/'
        unZipApkPatch = baseApkPatch + 'main_line_release'
        unSignDemoApkName = 'main_line_demo_unsign.apk'
        demoApkReleaseName = 'main_line_demo_release_signed.apk'
        demoApkDebugName = 'main_line_demo_debug_signed.apk'
        subprocess.call('rm -rf ' + unZipApkPatch, shell=True)
        subprocess.call('rm -rf ' + baseApkPatch + unSignDemoApkName, shell=True)
        subprocess.call('rm -rf ' + baseApkPatch + demoApkReleaseName, shell=True)
        subprocess.call('rm -rf ' + baseApkPatch + demoApkDebugName, shell=True)
        subprocess.call('mkdir ' + unZipApkPatch, shell=True)
        subprocess.call('tar zxvf ' + baseApkPatch + 'main_line_release.apk -C ' + unZipApkPatch, shell=True)
        del_files_except(unZipApkPatch + '/META-INF', '')
        print 'copy so and dict……'
        so32 = unZipApkPatch + '/lib/armeabi-v7a/libiptcore.so'
        if (os.path.exists(so32)):
            copyfile(srcIptDir + 'libiptcore.so', so32)
        so64 = unZipApkPatch + '/lib/arm64-v8a/libiptcore.so'
        if (os.path.exists(so64)):
            copyfile(srcIptDir64 + 'libiptcore.so', so64)
        copyFiles(iptDirFormat % 'MainLine', unZipApkPatch + '/assets')
        print 'pack apk……'
        zipDir(unZipApkPatch, baseApkPatch + unSignDemoApkName)
        if doFormalSign:
            print 'online sign apk……'
            subprocess.call('java -jar ./server_signed_apk.jar ' + baseApkPatch + unSignDemoApkName \
                + ' ' + baseApkPatch + demoApkReleaseName, shell=True)
        else:
            print 'local sign apk……'
            subprocess.call('jarsigner -verbose -keystore ./imeurltest.keystore.jks -storepass baiduime -signedjar ' \
                + baseApkPatch + demoApkDebugName + ' ' + baseApkPatch \
                + unSignDemoApkName + ' imeurltest', shell=True)

        print 'success, demo apk path: ' + baseApkPatch + demoApkReleaseName + ' ' + baseApkPatch \
            + demoApkDebugName


def zipDir(dirpath, outFullName):
    """
    压缩指定文件夹
    :param dirpath: 目标文件夹路径
    :param outFullName: 压缩文件保存路径+xxxx.zip
    :return: 无
    """
    zip = zipfile.ZipFile(outFullName, "w", zipfile.ZIP_DEFLATED)
    for path, dirnames, filenames in os.walk(dirpath):
        # 去掉目标跟路径，只对目标文件夹下边的文件及文件夹进行压缩
        fpath = path.replace(dirpath, '')

        if path.find('assets') != -1:
            continue
        for filename in filenames:
            try:
                zip.write(os.path.join(path, filename), os.path.join(fpath, filename))
            except(ValueError) as e:
                # 文件timestamps小于1980的错误，尝试修改时间后再试一次
                os.utime(os.path.join(path, filename), None)
                zip.write(os.path.join(path, filename), os.path.join(fpath, filename))
    zip.close()

    # 不能压缩的assets文件
    zip = zipfile.ZipFile(outFullName, "a", zipfile.ZIP_STORED)
    for path, dirnames, filenames in os.walk(dirpath):
        # 去掉目标跟路径，只对目标文件夹下边的文件及文件夹进行压缩
        fpath = path.replace(dirpath, '')

        if path.find('assets') != -1:
            for filename in filenames:
                try:
                    zip.write(os.path.join(path, filename), os.path.join(fpath, filename))
                except(ValueError) as e:
                    # 文件timestamps小于1980的错误，尝试修改时间后再试一次
                    os.utime(os.path.join(path, filename), None)
                    zip.write(os.path.join(path, filename), os.path.join(fpath, filename))
    zip.close()

def copyFiles(src, dest):
    """
    拷贝文件到置顶目录
    :param src: 原路径
    :param dest: 目标路径
    :return: 无
    """
    src_files = os.listdir(src)
    for file_name in src_files:
        full_file_name = os.path.join(src, file_name)
        if os.path.isfile(full_file_name):
            shutil.copy(full_file_name, dest)

def del_files_except(path, excFile):
    """
    删除 path 路径下的文件，除了 excFile
    :param path: 删除的路径
    :param excFile: 例外（不进行删除）的文件 excFile
    :return: 无
    """
    for root, dirs, files in os.walk(path):
        for name in files:
            if name == excFile:
                pass
            else:
                os.remove(os.path.join(root, name))
        for name in dirs:
            shutil.rmtree(os.path.join(root, name))

# main函数
if __name__ == "__main__":

    doCopyFiles = False
    doWriteMd5 = False
    doNdkBuild = False
    doApkBuild = False

    print sys.argv

    properties = Properties("./local.properties")
    ndk_dir = properties.get("ndk.dir", None)
    if not ndk_dir:
        print("ERROR: make sure you have your ndk.dir in local.properties")
    else:
        if ndk_dir[-1] == '/':
            ndk_dir = ndk_dir[0:-1]

        if len(sys.argv) >= 2:
            if (str(sys.argv[1]) == '1'):
                doCopyFiles = True
                execute(doCopyFiles, doWriteMd5, doNdkBuild, doApkBuild)
            elif (str(sys.argv[1]) == '2'):
                doWriteMd5 = True
                execute(doCopyFiles, doWriteMd5, doNdkBuild, doApkBuild)
            elif (str(sys.argv[1]) == '3'):
                doNdkBuild = True
                execute(doCopyFiles, doWriteMd5, doNdkBuild, doApkBuild)
            elif (str(sys.argv[1]) == '4'):
                doApkBuild = True
                execute(doCopyFiles, doWriteMd5, doNdkBuild, doApkBuild)
            elif(str(sys.argv[1]) == 'apkbuild-s' or str(sys.argv[1]) == 'apkbuild-c'):
                doCopyFiles = str(sys.argv[1]) == 'apkbuild-c'
                doWriteMd5 = False
                doNdkBuild = True
                doApkBuild = True
                doFormalSign = len(sys.argv) == 3 and str(sys.argv[2]) == '-f'
                execute(doCopyFiles, doWriteMd5, doNdkBuild, doApkBuild, doFormalSign)
            else:
                help()
        elif len(sys.argv) < 2:
            doCopyFiles = True
            doWriteMd5 = True
            doNdkBuild = True
            execute(doCopyFiles, doWriteMd5, doNdkBuild, doApkBuild)
        else:
            help()