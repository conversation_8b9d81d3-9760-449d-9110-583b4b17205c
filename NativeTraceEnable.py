#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
内核接口耗时评测——iptcore-perf (NativeTrace集成)脚本
运行环境：python3

"""
__author__ = "ChenDanfeng"
__copyright__ = "Copyright 2021, Baidu Inc. Baidu Input Team"

import os
import sys
from shutil import copyfile
import zipfile
import subprocess
import hashlib


class Properties:
    """
    读取local.properties的工具类
    """

    def __init__(self, file_name):
        self.file_name = file_name
        self.properties = {}

        with open(self.file_name) as f:
            for line in f:
                line = line.strip()
                if line.find('=') > 0 and not line.startswith('#'):
                    strs = line.split('=')
                    self.properties[strs[0].strip()] = strs[1].strip()

    def get(self, key, default_value=''):
        """
        获取指定key的value
        :param key: 想要获取的key值
        :param default_value: 如果没有key，则返回的默认值
        :return: 获取的value
        """
        if key in self.properties:
            return self.properties[key]
        return default_value


def modify_android_mk(file_path):
    """
    修改android.mk文件
    """

    lines = []
    with open(file_path) as f:
        for line in f:
            if len(line) > 0 and line[-1] == '\n':
                line = line[0:-1]
            # 在LOCAL_PATH := $(call my-dir)后面添加nativetrace shared library
            if "$(call my-dir)" in line:
                lines.append(line)
                lines.append("")
                lines.append("include $(CLEAR_VARS)")
                lines.append("LOCAL_MODULE := backtrace")
                lines.append("LOCAL_SRC_FILES := lib/libxh_backtrace.so")
                lines.append("include $(PREBUILT_SHARED_LIBRARY)")

            # 在CFLAGS和CPPFLAGS后面添加"-finstrument-functions"
            elif line.startswith("CFLAGS") or line.startswith("CPPFLAGS"):
                line += " -finstrument-functions"
                lines.append(line)

            # 在LOCAL_SHARED_LIBRARIES后面添加"backtrace"
            elif line.startswith("LOCAL_SHARED_LIBRARIES"):
                line += " backtrace"
                lines.append(line)

            else:
                lines.append(line)

    with open(file_path, 'w') as f:
        for line in lines:
            f.write(line)
            f.write('\n')


# main函数
if __name__ == "__main__":
    print('##################### Step0:: 读取local.properties ########################')
    properties = Properties("./local.properties")
    ndk_dir = properties.get("ndk.dir", None)
    if not ndk_dir:
        print("ERROR: make sure you have your ndk.dir in local.properties")
    else:
        print('Step0 success')

        print('##################### Step1:: 编译nativetrace.so并拷贝到iptcore module下 ########################')
        subprocess.call("./gradlew :native_trace:assemble --rerun-tasks", shell=True)
        copyfile("./native_trace/build/intermediates/cmake/debug/obj/armeabi-v7a/libxh_backtrace.so",
                 "./iptcore/jni/lib/libxh_backtrace.so")
        print('Step1 success')

        print('##################### Step2:: 修改mk文件，添加nativetrace.so并添加插桩 ########################')
        path_mk = "./iptcore/jni/Android.mk"
        subprocess.call("git checkout " + path_mk, shell=True)  # 去除对mk文件的修改
        modify_android_mk(path_mk)

        cmd1 = 'cd %s' % './iptcore/jni'
        cmd2 = str(os.path.join(ndk_dir, "ndk-build"))
        cmd3 = 'cd ../../'
        cmd = cmd1 + " && " + cmd2 + " && " + cmd3
        subprocess.call(cmd, shell=True)

        so_dir = "./iptcore/libs/armeabi-v7a/"
        so_dst_dir = "./iptcore/src/release/libs/armeabi-v7a/"
        dirs = os.listdir(so_dir)
        for file in dirs:
            if file.endswith('so'):
                copyfile(os.path.join(so_dir, file), os.path.join(so_dst_dir, file))

        print('Step2 success')

        print('##################### Step3:: 编译native_trace.jar，替换到iptcore module ########################')
        subprocess.call("./gradlew :native_trace:makeJar", shell=True)
        copyfile("./native_trace/build/native_trace.jar", "./iptcore/libs/native_trace.jar")

        print('Step3 success')

        print('##################### Step4:: rollback Android.mk file ########################')
        subprocess.call("git checkout " + path_mk, shell=True)  # 去除对mk文件的修改

        print('Step4 success')
