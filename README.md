# 项目名称

输入法的输入内核组件，为输入法提供了基础输入相关的接口和对内核的封装。
该组件包含旧内核和新内核模块，接入新内核的分支已经废弃旧内核。

## 快速开始

开始开发前，请阅读如下文档：

1) 介绍这个组件的组织形式和基础知识
[组件使用说明](./doc/guide.md)

2) 【必需】[新内核替换脚本(CoreReplaceNew.py, 直接看脚本源码和注释)](./CoreReplaceNew.py)

3) 打包demo包使用，Android和内核的同学都会使用到
[编译客户端Demo包脚本说明](./buildApkDemo.sh)

4) 性能评测&准入
[使用NativeTrace工具评估内核接口连续输入性能](./doc/native_trace_tool.md)

## 测试
如何执行自动化测试

# ReleaseNote
请移步[RELEASE_NOTE.md](./RELEASE_NOTE.md)