# 编辑指南
1) 主版本号：当你做了不兼容的 API 修改，**或虽然API向下兼容，但内核的内部实现变更，要求宿主工程必须适配时**需要增加。此时，你必须说明组件的修改点、宿主工程的适配指南。 
2) 次版本号：当你做了向下兼容的功能性新增，需要增加（组件拉release时，至少升级一位次版本号或直接升级主版本号）。此时，你必须说明内核组件的修改点，并给出适配建议。
3) 修订号：当你做了向下兼容的问题修正，需要增加。此时，你必须说明问题、修改点。

# 职责
1) 负责开发Feature的同学，需要在Feature合入develop前，编辑Feature的release note，并描述Feature的改动、影响面、是否向下兼容、宿主工程如何修改等信息。标题为Feature xxxx
2) 版本负责人，在release版本发布前，修改所有该版本带入的feature的release note中的标题，统一整理为版本信息。标题为v 1.2.3

# 参考文档
1) 内核的更新记录：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/VuNIMoeyA2/S8sVqtJ1aF/xnMez0F9Dhzn-k

# ReleaseNote

## 组件版本8.16.3_F.9098.x 内核版本：5.69.0.0 主工程版本：荣耀
1) OEMinput-9223 [Story] 【荣耀】内核版本升级，5.59 -> 5.69
2) OEMinput-9098 [Feature] 【荣耀】智能填表服务
3) IMInput-5440 [Story] 【安卓】【内核】更换个性短语词库至phrase3.bin

## 组件版本8.16.3_F.9207.x 内核版本：5.59.2.23 主工程版本：荣耀
1) OEMinput-9208 [Story] 【荣耀】打字卡顿问题优化-内核组件优化；将误触接口改为主线程直接调用
2) OEMinput-9224 [Story] 【荣耀】内核组件适配神句配图开关 [Story] 【荣耀】内核组件适配神句配图开关

## 组件版本8.16.3_F.8349.1 内核版本：5.59.2.20 主工程版本：荣耀
1) OEMinput-8469 [Story] 【荣耀】笔记接入智能联想能力-内核实现
2) MICInput_common-4683 [Story] 【荣耀】拼音联想功能


## 组件版本8.16.3_F.8025.x 内核版本：5.59.2.17 主工程版本：荣耀
1) OEMinput-8025 [Story]【荣耀】【SUG】内核升级到支持新SUG的版本
2) 敏感词词库更新，去掉『吉林』
3) 敏感词卡顿问题优化 
4) getTouchedKey方法新增超时参数 timeoutMillis；
5) 修改 CoreThread 线程优先级为：Process.THREAD_PRIORITY_FOREGROUND
6) MICInput_common-4566 [Story] 【内核】新增耗时打点20240914
7) MICInput_common-4657 [Story] 【词库】2024年Q4词库更新 +【去世】不干预
8) MICInput_common-4669 [Story] @符号不出邮箱名问题
9) OEMinput-7894 [Story]【荣耀】面板和出词新增emoji实现 & 对齐华为版本的云联想发起条件
10) MICInput_common-4745 [Story] 【荣耀】内核5.59.2.15版本更新内容
11) MICInput_common-4574 [Story] 【Kernel】快速输入、emoji处理方式 
12) BAIDUINPUTBUG-175132 [Bug] 【Android】【荣耀新内核】【例行版本】【偶现】点击“h”和“j”中间位置，响应的是“g”
13) MICInput_common-4725 [Story] 【内核】误触模型更新


## 组件版本8.16.3_F.8001.x 内核版本：5.49.1.x 主工程版本：OPPO
1) OEMinput-8004 [Story]【OPPO】【SUG】内核升级到支持新SUG的版本

## 组件版本8.0.1_F.7328.2 内核版本：5.42.1.7 主工程版本：华为
1) 将cz5down词库文件提前内置为cz5
2) 修复华为新商店版本手写不能用的问题,增加手写对华为包名的鉴权

## 组件版本8.0.1_F.7642.x 内核版本： 主工程版本：荣耀
1) 同步feature_OEMinput-7893_emoji_extend分支关于bug、卡顿的一些问题
2) OEMinput-4339 [Story] 【荣耀】更新cz5down词库
3) OEMinput-7574 [Feature] 【荣耀】更新cz5down词库
   【词库】2023年Q4词库更新][词库版本101178] MICInput_common-4154
4) [5.42.1.9][bug][BAIDUINPUTBUG-151425][【荣耀】英文面板有输入码时，先跳转数字9键面板再跳转符号26键面板，点击任何符号没反应]
5) [5.42.1.9][bug][BAIDUINPUTBUG-151419][【荣耀】多次输入“什么书”，下次上屏“什么”后联想首位还是颜文字]
6) Revert "[5.42.1.8][bug][BAIDUINPUTBUG-149776][【Android】【华为新内核】英文26键按一次shift键，进入符号面板后返回，没有显示小写字母键盘]
7) [5.42.1.8][bug][BAIDUINPUTBUG-149776][【Android】【华为新内核】英文26键按一次shift键，进入符号面板后返回，没有显示小写字母键盘]
8) 纯净输入法统一断网及白名单适配(内核统一断网)
9) MICInput_common-4235 [Story] 【荣耀】增加整句预测开关和拆字放入主cand开关
10) 更新emoji内核联想需求
11) 荣耀云词进入cand
12) 性化训练阈值修改
13) 个性化文件惰性加载
14) 数字框手写优化
15) 同步emoji内核联想需求
16) OEMinput-7134 【OEM】【荣耀】防误触-高斯个性化
17) OEMinput-7135 【荣耀】内核词库增加荣耀品牌词

## 组件版本8.0.1_bugfix.7 内核版本：5.39.1.7 主工程版本：OPPO
1) 笔势模型hw_gesture.bin升级到v3.3版本。

## 组件版本8.0.1_bugfix.6 内核版本：5.39.1.8 主工程版本：VIVO
1) 解决通配符上屏错误问题：关闭【Z键通配开关】后，通配按钮也变成“Z”上屏了。

## 组件版本8.0.1_bugfix.5 内核版本：5.39.1.4 主工程版本：OPPO
1) OEMinput-7037 v3笔势模型合入
2) 
## 组件版本8.0.1_bugfix.4 内核版本：5.39.1.4 主工程版本：荣耀
1) OEMinput-6948 内核回退数字框手写的修改

## 组件版本8.0.1_bugfix.3 内核版本：5.39.1.3 主工程版本：荣耀
1) OEMinput-6948 内核更新emoji关联文件、数字输入框手写模式优化

## 组件版本8.0.1_bugfix.1 内核版本：******** 主工程版本：荣耀
1) OEMinput-6948 【荣耀】云词插入/替换候选词

## 组件版本8.16.x 内核版本：5.58.1.x 主工程版本：主线
1) 增加APP应用意图（见IMInput-4182）
2) 地图卡片切换到云端意图接口

## 组件版本8.14.x 内核版本：5.53.1.x 主工程版本：主线
1) 在OPPO意图的基础上（参见OEMinput-7738），新增了对主线意图的支持：
   1) IptIntentItem 中，新增展示文本和意图列表。
   2) 新增设置项REQ_CLOUD_INTENT，用于控制主线意图开关。

## 组件版本8.13.x 内核版本: 5.54.x.x 主工程版本: 主线12.4.3.x
1) IMInput-3949 [Story] 【SUG】接入穿山甲 ADX 资源实现应用拉新促活

## 组件版本8.12.x 内核版本: ******** 主工程版本: 主线12.3.5
1) IMInput-3880 [Story] 【Android】增加意图来源

## 组件版本8.7.x 内核版本: 5.47.2 主工程版本: 主线12.1.2
1) IMInput-2927 [Story] 【Android】【 触发词】通用触发词支持设置背景

## 组件版本8.6.x 内核版本: 5.46.1 组工程版本: 主线12.0.11
1) IMInput-2747 [Story] 【安卓】【云端触发词】云端触发词埋点增加参数
2) candInfo增加setTabId、setShowType方法

## 组件版本8.5.x 内核版本: 5.45.1 组工程版本: 主线12.0.10
1) Feature IMInput-2616 [Story] 【安卓】【内核/语音】内核提供语音识别结果记录到内核轨迹的接口 

## 组件版本8.4.x 内核版本: 5.44 组工程版本: 主线12.0.9
1) Feature IMInput-2584 [Story] 【安卓】【触发词】次cand展示超会写触发词——生成结果能力
2) bugfix：
   1) [BAIDUINPUTBUG-142480] 高情商场景，10字以内的意图不显示
   2) [BAIDUINPUTBUG-142416] 高情商场景，意图显示为空

## 组件版本8.3.x 内核版本: 5.43 组工程版本: 主线12.0.8
1) Feature 2288 云端触发词需求

## 组件版本8.1.x 内核版本：5.40 主工程版本：主线12.0.5
1) Feature 1867 通用触发词需求，地图触发词需求

## 组件版本8.0.x 内核版本：5.39 主工程版本：主线12.0.2, 12.0.3
1) Feature 473 文心大模型（兼容、API新增、宿主不启用则无需测试）
   1) 增加了获取文心so版本号、模型文件版本号、刷新/卸载文心模型等接口。
   2) 需要接入通知中心，下发文心大模型方可生效。OEM不下发大模型，无影响。
2) bugfix: [BAIDUINPUTBUG-139851]【Android】【用户反馈】中文26键输入airdrop，不在首位，点击后崩溃

## 组件版本v7.4.x 内核版本: 5.38.1.0 主工程版本: 主线11.8.0
1) IMInput-2096 [Story] 【安卓】【更换内核】更换内核5.38.1.0
   1) 本地删除分流模型
      1) 删除接口 cfg_set_aichat_request_pfd
      2) 删除接口 aichat_get_intention_style
   2) 云输入请求增加携带纠错后输入码 本地+云端改动，端上可以忽略
   3) bugfix: [BAIDUINPUTBUG-137699] 【内核】数字面板查询联想按未按智能联想开关控制

## 组件版本v7.3.x 内核版本: 5.36.1.0 主工程版本: 主线11.7.16
1) Story IMInput-1752 【AI助聊】移除语料Tab、校对Tab(兼容、API无修改、内核需测试)
   1) 主线AI助聊校对Tab移除, 次cand仍能出纠错提示, 但是只出单纠错, 不出多纠错

## 组件版本v7.2.x 内核版本：5.35.1.5 主工程版本：主线11.7.13
1) Story IMInput-1715 更换sug内核（兼容、API新增、内核需测试）
   1) Sug进Cand策略
      1) Cand条增加Sug候选。纯内核需求，无端上改动
   2) 新增设置Sug是否开启调试和获取Sug是否开启调试
      1) 设置Sug是否开启调试，开启调试会在Cand条候选词上添加类型标记
         1) jni 增加 void jni_cfg_set_display_candtype(JNIEnv* env, jobject obj, jboolean is_on)
         2) AsyncCoreConfig 增加 void setDisplayCandType(boolean enable)
      2) 获取Sug是否开启调试
         1) jni 增加 jboolean jni_cfg_get_display_candtype(JNIEnv* env, jobject obj)
         2) AsyncCoreConfig 增加 boolean getDisplayCandType()
   3) 五笔词库更新，支持四字节。无端上改动

## 组件版本v7.1.x 内核版本：5.33.110.0 主工程版本：主线11.7.0
1) Feature IMInput-746 双拼优化一期（Input码展示原码/双拼纠错/0号位修复/v输入修复）(兼容、API新增、无需测试)
   1) 修改了virtual bool cfg_get_is_shuangpin_nocvt() 、virtual void cfg_set_is_shuangpin_nocvt(bool is_on) 定义，
      这个两个接口之前只定义了，并未使用。现在用来设置双拼输入是否显示原码，接口对应的key为SHUANGPIN_NOCVT。
   2) 增加key FKEF_SPACE_0，此key表示9 键键盘上空格键与 0 键都映射在同一个键，用于 9 键双拼输入
2) Feature IMInput-413 国标GB18030-2022级别2生僻字（兼容、无API修改、无需测试）（mainline）
   1) 内核出词增加34个生僻字。纯内核需求，无端上代码改动
3) Feature OEMinput-6542【荣耀】部分输入框手写无法预上屏问题处理 (兼容、新增api，如果主工程接入新api，则需要测试，否则无需测试)
   1) CoreConfig 增加 setPreCommitType 方法，用于设置手写预上屏的方式(参数为 IPreCommit#TYPE_REPLACE_CAND_TEXT 或 IPreCommit#TYPE_SET_COMPOSING_TEXT)
      1) TYPE_REPLACE_CAND_TEXT 为现在所用的预上屏方式 （即使用 finishComposing + deleteSurroundingText + commitText + setComposingRegion实现预上屏）
      2) TYPE_SET_COMPOSING_TEXT 为新增的预上屏方式（使用 setComposingText 的方式）
4) Story IMInput-1388 代际新增 AI 问问功能（兼容、无API修改、无需测试）
   1) 内核部分常量字段新增 AI 问问
5) Story input-client-architecture-467（兼容、无API修改、无需测试）
   1) 内核重打包工具压缩文件区分assets目录。assets目录不压缩，其余目录压缩（解决OEM部分机型因为压缩导致的崩溃问题）
6) Story IMInput-1625
   1) 内核增加Cand是否显示候选词类型标记开关用于Sug体验(纯体验功能, 无需测试)
7) Story IMInput-1660（兼容、API新增、无需测试）
   1) CustomInputDataType中新增SEARCH_QUERY类型，用于内核记录统计搜索时的query

## 组件版本v7.1.2_F.6715 内核版本：5.32.100.x 主工程版本：oppo 8.5.30
1) Feature OEMinput-6481【OPPO8.5】全局手写 （兼容、增加API、支持全局手写项目需测试）
   1) OEMinput-6549 [Story] 【OPPO】手写笔势识别内核组件接口实现
      1) 词库：OPPO 项目增加 hw_gesture.bin 词库 （兼容，不影响其他项目，无API改动）
      2) 新增：IptCoreInterface.actTrackEnd 增加重载方法，比原 actTrackEnd 多一个 isGesture 参数，标记手写的笔迹是否要作为笔势交给内核识别（兼容，新增api，需要测试）
      3) 新增：InputDutyCallback.updateHwGesture 内核回调上层手写笔势识别结果。当有笔势识别结果时无手写候选，否则，有手写候选时，无笔势结果（兼容，新增api，需要测试）
1) Feature OEMinput-6715【OPPO8.5】生僻字键盘 （兼容、增加API、支持生僻字项目需测试）
   1) OEMinput-6083 [Story] 生僻字键盘-内核组件封装
      1) 词库：新增生僻字部首拼音拆字文件hanzi_pzi.bin和生僻字手写文件hw_rare.bin，均为后下发（其他项目 hw_rare.bin 为内置的，看项目需求），见 ImeCoreConsts.IPTCORE_DICT_HANZI_PZI（兼容，不影响其他项目，无API改动）
      2) 词库类型增加拆字文件类型，下载词库后刷新内核，调用 coreRefresh 时用到， 见: ImeCoreConsts.CFT_RARE_CHAIZI
      3) 注意下载生僻字文件后，调用 coreRefresh 让内核重新加载生僻字文件，参数CF_HandWrite重载手写文件（包括普通手写和生僻字手写），参数CFT_RARE_CHAIZI重载拆字文件。
      4) 新增获取生僻字手写版本号的方法，调用方式： ImeCoreManager.getCellConfig().getHwRareVersion()；
      5) 新增导出生僻字明文的方法，调用方式 ImeCoreManager.getCellConfig().exportRareUserWord();
      6) 新增面板类型：PAD_RARE_CHAIZI, PAD_RARE_HW, 调用内核切换面板的方法时用到：ImeCoreManager.getPad().switchKeyboard()
      7) 新增按键：FKEY_RARE_CHAIZI_HW, 生僻字面板【部/写】切换键；调用内核按键点击方法时用到 ImeCoreManager.getPad().actKeyClicked(keyId, ImeCoreConsts.INPUTTYPE_CLICK);
      8) 内核回调增加 REFL_RARE_CAND 标志，刷新最近的生僻字，见: ImeCoreCallback.updateRareCandState
      9) 增加 RareCandState 类，封装生僻字的 cand 数据，使用方式同原来的 IptCandState, 在 ImeCoreCallback.updateRareCandState 回调时作为参数回调给上层
      10) 增加 ImePad.actRareCandClicked 点击最近生僻字的候选词操作时调用 (点击类型需要是 RARE_CAND_CLICK )

## 组件版本v7.0.x 内核版本：5.30 主工程版本：主线11.7.0
1) Feature IMInput-472 代际产品 AI 助聊（不兼容、API修改、需要测试）
   1) 新增：意图模型，包括 libflowmodel-lib.so、libpaddle_light_api_shared.so、flowmodel.bin，目前 so 存放于主线工程，flowmodel.bin 后下发（兼容、无API修改、无需测试）
   2) 新增：ImeCoreManager.getConfig().setString 新增 USER_PROFILE 设置用户画像（兼容、无API修改、无需测试）
   3) 新增：ImeCoreManager.getConfig().setBoolean 新增 SET_HIGH_EQ_ENABLE 是否高情商启用设置（兼容、无API修改、无需测试）
   4) 新增：ImeCoreManager.getConfig().setInt 新增 XHS_BOX_ATTRI 设置小红书框属性（标题框、正文框）（兼容、无API修改、无需测试）
   5) 新增：refreshCore 新增分流模型 type（兼容、无API修改、无需测试）
   6) 新增：getIntentStyle 方法，在集成必要 so 和 bin 文件后可获取用户输入文字意图（兼容、无API修改、无需测试）
   7) 新增：cfg_get_flow_load_type 方法，用于判断分流模型是否加载成功，暂未添加 java 层调用（兼容、无API修改、无需测试）
   8) 新增：ImeCoreCallback 新增 isAllowMinorCandHighEQShow 方法，用于次 Cand 是否在 idle 状态下展示高情商提示（不兼容、API修改、若无次功能则返回 false 并无需测试）
   9) 新增如下 CandType（兼容、无API修改、无需测试）：
      a) CANDTYPE_CANCEL_AICAND_INSERT：用户点击次 Cand AI 助聊后会展示『撤销』按钮
      b) CANDTYPE_NLP_MMODEL：小红书用户输入标题后会展示联想结果的一半，例如标题框输入『如何编写 Android 代码』，次 Cand 展示『想学 Android，可以从……』，引导用户点击
      c) CANDTYPE_AICAND_XHS_HINT：小红书正文框次 Cand 提示文案
      d) CANDTYPE_AICAND_INTENTION：意图模型识别到用户输入的意图结果
   10) 修改 actAiFontGeneratedNotify 方法，新增点击后上屏内容和展示时间参数（不兼容、API修改、需测试）
   11) 修改 IptCoreCandInfo 类，新增变量 mServiceExtra 用于代替 uni 透传端上次 Cand 展示内容(对应 actAiFontGeneratedNotify 中的 showContent 参数传入内容)（兼容、无API修改、无需测试）
   12) 修改 IptCoreCandInfo 类，新增变量 mIntentStyle 用于获取本次 Cand 状态携带的意图信息（兼容、无API修改、无需测试）
   13) 修复手写不出 @ 问题（兼容、无API修改、无需测试）
   14) IMInput-1181 [Story] 【安卓】【高情商】内核3s意图提示策略更新（兼容、无API修改、无需测试）
   15) IMInput-1189 [Story] 【安卓】【高情商】内核：在AI助聊结果上屏后，3s闲置意图不出（兼容、无API修改、无需测试）
   16) IMInput-1261 [Story] 【种草文】更新内核 & 新增敏感词过滤词库。新增敏感词库，增加包大小约65K（兼容、无API修改、无需测试）
2) bugfix(兼容，无需测试)
   1) 修复极端情况下udp请求数量过多，导致线程池任务拒绝引发的崩溃

## 组件版本v6.1.x 内核版本：5.30 主工程版本：主线11.6.10
1) Feature IMInput-691 云输入传输层非对称加密+端口号保持（不兼容、API修改、无需测试）
   1) 云输入使用UDP通道，增加非对称加密。UDP的socket打开后不再关闭，尽量保持。
   2) ImeEnvCallback删除了getJsonBuff方法。该方法不再需要（不兼容）
   3) NetworkLogAdapter的logUdpResponse接口有参数修改，主工程需适配。删除不必要参数即可。（不兼容）
   4) kv和云cloud通道合并，kv通道删除。因此setCloudAddress接口中的kv相关的host与端口号，需要删除。（不兼容）
   5) 在imeservice中注册ICoreLifecycle对service的生命周期监听！（不兼容）
2) bugfix（兼容、无需测试）
   1) 修复内核可能会无限制写入轨迹文件，导致轨迹文件过大，甚至引发OOM的问题。修改后，如果内核检测到文件大小大于8M，则会删除轨迹文件
   2) 修复内核析构后，网络请求返回时可能导致的fault address问题

## 组件版本v6.0.x 内核版本：5.29 主工程版本：主线11.6.9
1) Feature OEMinput-6074 内核组件生僻字项目 （不兼容、修改API、需测试）
   1) OEMinput-6256 内核组件生僻字项目升级
      1) 增加 libocr.so ,  libBaiduOcrSDK.so，移除对应的 .a 文件
      2) 修改手写词库 hw6.bin 为 hw7.bin， 同时增加手写鉴权文件 hw_lic7.bin 
      3) 修改了 loadAiFontHwModel 接口的传参，增加 context 以及 手写鉴权文件路径参数（release_11_6_9_0, bc5d484e）
      4) 因与语音组的鉴权方案尚未确定，所以除主线外其他版本的手写都不可用！
      4) 更新鉴权SDK，解决https的安全问题
      5) 更新SDK，解决手写模型卸载加载问题
      6) 内核合入数字输入框手写模式优化
2) Feature MICInput_common-3793 【内核】符号面板补齐"符号大全"符号 （兼容、无API修改、无需测试）
   1) 符号面板增加更多符号。纯内核需求，无端上代码改动

## 组件版本v5.1.0_bugfix 内核版本：5.27.1.12 主工程版本：OPPO版本
1) OEMinput-6549【OPPO】全局手写 内核组件接口实现
   1) 需求地址：https://console.cloud.baidu-int.com/devops/icafe/issue/OEMinput-6549/show
   2) 端上告知内核笔迹区域和文字内容是否有交集：actTrackEnd方法新增isGesture参数，true代表有交集；
   3) 内核回调端上告知笔势类型：新增updateHwGesture(int hwGesture)方法告诉端上笔势类型。

## 组件版本v5.1.0_bugfix 内核版本：5.27.1.10 主工程版本：OPPO版本
1) 新增拼音九键分词修改为 1的开关，默认为关闭。
2) 英文 Enter 键优化，无论是否有输入码都执行换行操作 ：
   1) 需求地址：https://console.cloud.baidu-int.com/devops/icafe/issue/MICInput_common-3900/show
   2) 影响范围：所有项目，目前主线、荣耀、OPPO 均已支持该功能，该功能没有开关，端上必须修改代码。
   3) ime 工程修改：见提交 98b5e1d2。

## 组件版本v5.1.0_F.6844.3 内核版本：5.27.1.18 主工程版本：荣耀版本
1) [ MICInput_common-4069][分离键盘高斯误触补充外边框策略] 
2) [MICInput_common-4065][【内核】【荣耀】中文面板切到符号面板默认选中[最近]] 
3) [MICInput_common-4051]荣耀分离键盘误触模型新增GV按键数字ID映射适配] 
4) [MICInput_common-4055] OPPO 全局手写笔势识别优化

## 组件版本v5.1.0_F.6844.2 内核版本：5.27.1.17 主工程版本：荣耀版本
1) 修复26键分离键盘按键映射相关的bug

## 组件版本v5.1.0_F.6844.1 内核版本：5.27.1.16 主工程版本：荣耀版本
1) OEMinput-6846 [Story] 【荣耀】内核更新，支持分离键盘26键防误触功能（不兼容）
   - setPadLayout接口新增一个参数用来判断是否为26键分离键盘

## 组件版本v5.1.0_F.6462.3 内核版本：5.27.1.8 主工程版本：荣耀版本
1) OEMinput-6542 [Story] 【荣耀】部分输入框手写无法预上屏问题处理

## 组件版本v5.1.0_F.6462.2 内核版本：5.27.1.8 主工程版本：荣耀版本
1) BAIDUINPUTBUG-128989 [Bug] 【Android】【荣耀新内核】浏览器搜索框中有输入码，点击前往，上屏空格；游戏键盘，点击发送，上屏空格

## 组件版本v5.1.0_F.6462.1 内核版本：5.27.1.6 主工程版本：荣耀版本
1) OEMinput-6462 [Feature] 【荣耀】英文面板上，换行/确认键策略优化

## 组件版本v5.1.0_bugfix 内核版本：5.27.1.5 主工程版本：荣耀版本
1) 更新Emoji词库 
2) bugfix（兼容、需测试对应bug） 
   1) BAIDUINPUTBUG-127720 【内核】云联想问题修复
   2) cz5.bin词库更新，去掉【总理套餐】等问题

## 组件版本v5.1.0_bugfix 内核版本：5.27.1.1 主工程版本：vivo版本、小米版本
1) bugfix（兼容、需测试对应bug）
   1) B230227-1864 BAIDUINPUTBUG-126719 点击list符号上屏到了末尾问题
2) Feature OEMinput-6196 小米包大小优化（兼容、删除多余词库、小米版本）
   1) Story OEMinput-6276，包大小优化，人名词库在oem没有上线，删除多余词库，减少包体积
3) performance
   1) 组件化联调：显式声明内核 App 模块 flavor，便于组件化联调时切换 Build Variant。影响范围：对内核相关组件无影响，该模块不参与 aar 打包，仅影响本地编译。

## 组件版本v5.1.x 内核版本：5.27 主工程版本：主线11.6.8
1) bugfix（兼容、无需测试）
   1) 修复rank可能导致云广告不显示的问题（内核修改，端上无需关注）

## 组件版本v5.0.x 内核版本：5.26 主工程版本：主线11.6.7
1) Feature IMInput-340 云输入正式地址切换到https（不兼容、API新增、无需测试）
   1) Story IMInput-403，为解决切换为https后云输入成功率统计失效的问题，修改了getCustomData接口，新增了protoType参数（表示udp/https）。QA无需测试。
      1) 宿主工程修改可参考主线release_11_6_7_0，commit id: 0e5ae13b

## 组件版本v4.3.x 内核版本：5.26 主工程版本：主线11.6.7
1) Feature IMInput-340 云输入正式地址切换到https（兼容、API新增、无需测试）
   1) Story IMInput-371，为解决udp不安全的问题，临时将云输入从udp切换为https。此方案是主线临时性方案，OEM和商业化无需关注
2) bugfix
   1) BAIDUINPUTBUG-125238，修复了百度输入法先进入网页框，再进入原生框，第一次无Sug结果，后续才有的问题

## 组件版本v4.2.2_bugfix 内核版本：5.24 主工程版本：主线11.6.4
1) Feature IMInput-268 内核卡顿监测（兼容、API新增、无需测试）
    1) 增加内核卡顿的基本监测，默认关闭。该功能为临时性能力，除主线外版本请不要使用。
2) bugfix
   1) BAIDUINPUTBUG-123623，修复九键分离键盘下，切换左右手之后，数字面板能滑行输入的问题。
   2) Story IMInput-298，修复导出联想、误触数据时，没有在内核线程调用的问题
   3) BAIDUINPUTBUG-124877，修复候选词和input码对不上的问题

## 组件版本4.2.x 内核版本：5.24 主工程版本：主线11.6.2
1) Feature 5144 五笔三期（兼容、API新增、无需测试）
   1) 增加4个设置项接口，分别为自造词记忆开关、个性化短语开关、智能调频开关、Z键通配开关。若不设置，开关值与之前版本保持一致。
2) Feature 5860 OPPO软件搬家（兼容、API新增、无需测试）
   1) 新增6个内核词库文件操作接口：在特定场景下，使内核采用老内核词库文件生成新内核词库文件，舍弃原有新内核词库文件。宿主工程若无搬家场景，可直接合入
3) bugfix
   1) 修复因荣耀代码合入导致内核日志默认加密的问题。改为默认不加密。
   2) 内核升级bh4、ipt3sym_sys、black_white_dict2和cloud_black_dict2等文件。其中后两者为可写文件，内核会自动做词库升级。
   
## 组件版本4.1.x 内核版本：5.23 主工程版本：主线11.6
1) Feature 4910 在CLICK_DOWN中传入全部信息（兼容、API修改、无需测试）
   1) 给内核的按键事件中，增加坐标x、y的信息，用于云预取，增加首选率，用户无明显感知。API可向上兼容，但需要宿主配合才可以生效
2) Feature MICInput_common-3514 【词库】10月case例行更新（兼容、无API修改、无需测试）
   1) 词库更新：emoji（去除富士山、和服等联想）、笔画、cz5等
3) Story input-client-architecture-65 修改内核编译脚本CoreReplaceNew.py，适配服务端新的内核提测包（兼容、仅影响本地编译、无需测试）

## 组件版本v4.0.0_bugfix 内核版本：5.21 主工程版本：主线11.5.5
1) Bug fix（不兼容、API新增、无需测试）
    1) A85-5085，修复双拼布局下输入码回删的bug
    2) BAIDUINPUTBUG-121500，手写速度调节为最慢，写字后，立即主动发起光标移动，手写轨迹不消失
    3) 荣耀版本能力合入：内核方法耗时使用单独的接口字段控制、提升内核打印的日志级别至.d
    4) 荣耀版本能力合入：内核日志增加加密输出功能，且默认加密（后续会修复，改为不加密）。
    5) 荣耀版本能力合入：trace开关单独设置。宿主工程需根据打印日志的需求打开openTrace开关，否则会丢失部分内核日志。（不兼容、API变化、无需测试）
    6) 内核设置项异步模式支持问题修复
    7) input-client-architecture-63，修复因为预编译导致key的索引被替换为常量的问题
    8) BAIDUINPUTBUG-115924，添加五笔、拼音面板shift状态变化时是否刷新面板开关
    9) 【VIVO】拼音9键候选词“iQOO”位置调整
    10) 修复滑行输入时，布局切换没有及时更新导致的不准确问题
    11) 荣耀误触收集时的崩溃修复 
    12) 【vivo8.5】部分场景下屏蔽手写流式上屏, ConfigKey中增加DISABLE_HW_CAND_BEFORE_FINISH字段，为Boolean类型，默认false， 
        1) 含义：手写未结束的情况下会屏蔽REFL_CAND和REFL_CAND刷新标记，屏蔽手写预上屏
    13) BAIDUINPUTBUG-124345，英文候选开启，双击复制后会自动上屏非预期英文
    14) bughonor-980, 修复手写预上屏开关失效问题

## 组件版本4.0.x 内核版本：5.21 主工程版本：主线11.5
1) Feature 4655 注音键盘简体（兼容、API新增、无需测试）
    1) 增加判断当前的注音词库是简体还是繁体的API（getZhuyinCzInfo）。如果当前词库是繁体，可根据需求提示用户升级词库为简体。升级为简体后，注音词库支持简/繁切换。
    2) 华为和荣耀内置了注音词库。但在本次需求中暂不改动，仍然维持繁体词库，项目负责人可根据需要联系内核升级词库。升级后请注意兼容问题。
2) Feature 4574 新增云输入广告是否展示（不兼容，API新增，需宿主实现、需要测试）
    1) ImeEnvCallback中新增isAllowCloudAdShow接口，必须实现。实现逻辑可参考主线（Ifbdaa747ab972f28cacdb9c53020533f01477943）或返回默认值true。

## 组件版本：3.1.x 内核版本:5.20
1) 新增：网络请求 stream send 和 stream callback 日志，tag 为：『iptcore_net_log:』（兼容）
2) 新增：首屏显示2个关键词模块内容（对应卡片编号：MICInput_common-3474）（兼容）
3) AB test + 词库增量更新（兼容）
   1) 对外暴露内核 AB test 方法如下： virtual void cfg_set_cloud_version_2_0(bool enabled) = 0：设置内核云输入1.3协议和2.0协议 
   2) 新增设置项： 
      1) 手写是否请求云，主线端上需开启，（设置为true） virtual void cfg_set_is_hw_req_cloud(bool is_on) = 0; 
      2) 设置云输入发起等级，目标：根据情况限制云输入发起的次数，降低服务器压力，主线改为CloudReqMore。virtual void cfg_set_cloud_request_level(CloudReqLevel level) = 0; 
4) 新增 cz5down 增量更新接口，cz5down 词库加载状态接口(兼容)
5) 五笔半匹配模式 -- 在五笔超过4码时，可以匹配半匹配词(兼容) 

## 组件版本：3.0.4_F.5606 内核版本:5.19
### v3.0.4_F.5606
vivo 的输入码 inline 需求 [OEMinput-5650](https://console.cloud.baidu-int.com/devops/icafe/issue/OEMinput-5650/show?source=icode-commit-message-drawer)（不兼容，API新增） 
内核新增接口 cfg_set_enable_inline_show ，新增刷新标志位：REFL_INLINE_SHOW
cfg_set_enable_inline_show这个开关打开后，内核才会刷REFL_INLINE_SHOW，端上调用get_inline_show来获取inline输入码内容
端上新增 updateInlineShow 接口回调，如果没有inline需求，该接口可以空实现
内核修复bug：BAIDUINPUTBUG-117497

## 组件版本：3.0.4_release.11.4.1及以上 内核版本:5.18
1) 手写模型更新+手写速度调节（不兼容，API变更）。手写速度调节中需传入手写轨迹的时间参数，因此API变更，需要上层适配。适配可参考Feature 4586改动。

## 组件版本：3.0.4_release.11.4.0 内核版本:5.17
1) 适配工信部双清单需求，增加导出联系人数量和自造词数量导出的接口（兼容）
2) 通用次cand展示逻辑，提供端上展示通用次cand通知的能力，Feature 4404。方法名 actAiFontGeneratedNotify（名称需修改）（兼容）
3) 扩展设置项设置tab的接口（上线后出现问题，已回退，无需关注）

## 组件版本：3.0.2_release.11.3 内核版本:5.16
1) 增加设置符号26键是否需要自动返回普通26键的接口（兼容）

## 组件版本：2.0.8.7_release.11.2 内核版本:5.15
端上无显性改动，请参考内核的release note（兼容）

## 组件版本：2.0.8.5 -> 3.0.2 内核版本:5.14
1) 新世纪五笔，五笔98词库从wb398升级为wb498，86词库从wb3升级为wb486，新增wb4new的新世纪五笔词库。Feature 4366（不兼容，API兼容，但内核内部实现变更）
   1) 内核原逻辑中，只要存在自定义五笔方案的文件，就会使用自定义方案。但是在改版本内核中，宿主工程必须设置内核方案为自定义五笔（WB_DEF_SCHEMA），才可以切换为自定义五笔方案。
2) 新英文方案，Feature 4248。（不兼容，API兼容，但内部实现变更，建议宿主适配）
3) 加解密方案优化，上线后存在问题，回退至原方案。（兼容）

## 组件版本：2.0.8.3 -> 2.0.8.4 内核版本:5.13
1) 删除键逻辑变更，内核MRD：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/VuNIMoeyA2/F-8d4l2DtT/LGxVhHCuHl7flx（API兼容，但内核内部实现变化，需关注）

## 组件版本：2.0.8.0 -> 2.0.8.2 内核版本:5.12
1) 滑行输入模型升级，移除paddle.so的依赖（不兼容，API兼容，但内部实现变更，原模型无法使用，需下载新模型）。
2) 云输入成功率统计接口定义修改。（不兼容，接口变更，参考I88b10bdfbb747bb17f454e67202813ef46f5f0de、Id1362d8fb437bf1ca50074824292e2e7498c3aa6）
3) 移除无用的NPU相关类和接口（不兼容，API变更，若宿主使用了NPU相关的类和接口，移除即可）

## 组件版本：******* -> ******* 内核版本:5.11
1) AI助聊适配三期，Android仅接口适配，未接入。（兼容）

## 组件版本：******* -> ******* 内核版本:5.10
1) 计算器超长提示（兼容）

## 组件版本：******* -> ******* 内核版本:5.9
1) 无显性改动，请参考内核文档（兼容）


## v1.0.0(encrypt-chiper)
加解密（旧）module拆分。这个新的module会作为独立产物发布，提测的commit格式如下：
sdcime=[xxxxx][test][encrypt-chiper][xxxxxx][xx]
这个Module仅包含加解密的rar和so，是从imebase中拆分出来的，没有任何改动。宿主工程可根据需要自由选择使用旧的加解密库，或者是新（由内核实现的）加解密库。

## v1.0.0(encrypt-wrapper)
加解密（新）module拆分。这个新的module会作为独立产物发布，提测的commit格式如下：
sdcime=[xxxxx][test][encrypt-wrapper][xxxxxx][xx]
这个module的所有类名和方法都和旧的加解密库一致，这么做的目的是为了保证兼容性，外部不需要任何修改。方法内部，则是把加解密的具体实现委托给了内核。
注意：必须在内核open之后才可以使用。如果内核的签名校验不通过，加解密的相关方法也会出错。

## v*******
修复getEditSelection没有接收内核的len参数

## v2.0.2.9
修复md5计算错误

## v2.0.2.8
主线内核支持64位

## v2.0.2.0_Feature_3700_1.5
主线内核修复64位下输入崩溃问题

## v2.0.2.0_Feature_3700_1.4
主线内核修复找不到 native 方法导致的崩溃

## v2.0.2.0_Feature_3700_1.3
主线内核移除 Android.mk 中 hiai, nn_rank so 文件与依赖

## v2.0.2.0_Feature_3700_1.0
主线内核支持 64 位

## v1.7.2.5_F.3384.0.2
- aifont字体生成通知内核AI助聊显示

## v1.7.2.5_F.3384.0.1
- 内核重构
    - :MICInput_common-2502 [Story] 【5G内核】【重构】盲人辅助功能

- 新增需求
    - MICInput_common-2475 [Story] 邮箱查询以及邮箱联想优化

- 字体生成功能内核接口和处理

- 资源文件变化
    - cz5down.bin(词库更新), iec3dict.bin(新增数据), appmap.bin(文件更新)

- 接口变化：
    - 导出盲人辅助文件接口（cfg_cand_context_expor），已确认双端无调用,故删除
    - 增加TRACE_TYPE_WORD_INFO的trace_type

- bugfix
    - [*******][bug][92088][【iPhone】多音字“歘”字chuā的读音,拼音chuā没有歘字] BAIDUINPUTBUG-92088
    - [*******][bug][92064][【UFO】【iPhone】【用户反馈】纠正读音中「角色」的纠正读音不对+19839+Bug+词库/出词]

## V1.0.0.93 新内核组件，第一次稳定版本

- 对应内核版本*********(67436646)，for主线9.2.2 & 9.3.0

## V0.1.6 旧内核组件

- 对应内核版本********(67371060)，for主线9.2.0
