/*
 * Copyright (C) 2017 Baidu, Inc. All Rights Reserved.
 */

allprojects {
    repositories {
        flatDir {
            dirs 'libs' // 这里表示可以在libs中引用aar文件
        }
    }
}

apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply plugin: 'cocoservice'
apply plugin: 'kotlin-kapt'

apply plugin: 'com.baidu.pyramid.di'

android {
    compileSdkVersion rootProject.ext.compileSdkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion
    defaultConfig {
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode rootProject.ext.versionCode
        versionName rootProject.ext.versionName
        testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"

        ndk {
            abiFilters "armeabi-v7a", "arm64-v8a"
        }


        // flavor维度缺失时的策略：当遇到一个module中有个app中没有的'customizer'维度时，
        // 它应该按照下面这个顺序去匹配这个维度的flavors
        // 其中 build 是维度信息， 后面的是匹配列表
        missingDimensionStrategy 'product', ['productVIVO', 'productMainLine', 'productHuawei', 'productHonor',
                                             'productVIVO', 'productOPPO', 'productMi']

    }

    // 签名配置信息，
    signingConfigs {
        signed {
            keyAlias "imeurltest"
            keyPassword "baiduime"
            storeFile file("../imeurltest.keystore.jks")
            storePassword "baiduime"
            v1SigningEnabled true
            v2SigningEnabled true
        }
    }

    buildTypes {
        debug {
            debuggable true
            signingConfig signingConfigs.signed
        }
        release {
            minifyEnabled true
            signingConfig signingConfigs.signed
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        debug {
            jniDebuggable = true
            renderscriptDebuggable = true
        }
    }

    compileOptions {
        sourceCompatibility rootProject.ext.sourceCompatibilityVersion
        targetCompatibility rootProject.ext.targetCompatibilityVersion
    }

    packagingOptions {
        doNotStrip '**/*.so'
        pickFirst '**/libc++_shared.so'
    }

//    externalNativeBuild {
//        cmake {
//            path 'CMakeLists.txt'
//        }
//    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    androidTestImplementation('com.android.support.test.espresso:espresso-core:2.2.2', {
        exclude group: 'com.android.support', module: 'support-annotations'
    })

    implementation "com.android.support:appcompat-v7:${SUPPORT_V7_VERSION}"
    testImplementation 'junit:junit:4.12'
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$KOTLIN_VERSION"
    // 添加新内核依赖
    implementation project(path: ':iptcore')
    // 添加词库依赖
    implementation project(path: ':dict')
    implementation "com.google.code.gson:gson:${GSON_VERSION}"

    implementation("com.baidu.input:imebase:${IME_BASE_VERSION}") {
        exclude group: 'com.baidu.galaxy'
    }

    // 依赖注入相关
    implementation "com.baidu.pyramid:pyramid-annotation:0.1.13"

    kapt "com.baidu.coco:coco-apt:${COCO_APT_VERSION}"
    // Flywheel
    implementation "com.baidu.flywheel:fkernel:1.2.2"
    // 输入卡顿监控
    implementation("com.baidu.input:imetracecanary:0.1.5") {
        exclude group: 'androidx.appcompat', module: 'appcompat'
    }
    implementation "com.baidu.input:input-trace-api:1.0.1"
    implementation "com.baidu.input:input-trace-impl:1.0.1"
}

repositories {
    mavenCentral()
}
