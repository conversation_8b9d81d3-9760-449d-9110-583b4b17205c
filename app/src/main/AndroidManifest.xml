<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        package="com.baidu.iptandroiddemo">

    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.READ_PHONE_STATE"/>
    <uses-permission android:name="android.permission.INTERNET"/>

    <queries>
        <intent>
            <action android:name="android.intent.action.MAIN" />
            <category android:name="android.intent.category.LAUNCHER" />
        </intent>
    </queries>
    <application
            android:name=".ImeApplication"
            android:icon="@mipmap/ic_launcher"
            android:allowBackup="false"
            android:label="@string/app_name"
            tools:replace="android:allowBackup"
            android:theme="@style/AppTheme">
        <service
            android:exported="true"
                android:name=".ImeService"
                android:permission="android.permission.BIND_INPUT_METHOD">
            <intent-filter>
                <action android:name="android.view.InputMethod"/>
            </intent-filter>
            <meta-data
                    android:name="android.view.im"
                    android:resource="@xml/method"/>
        </service>

        <activity
            android:exported="true"
                android:name=".MainActivity"
                android:theme="@android:style/Theme.NoTitleBar"
                android:icon="@mipmap/ic_launcher"
                android:screenOrientation="portrait">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>

        <activity
                android:name=".ConfigActivity"
                android:exported="false"/>
    </application>

</manifest>
