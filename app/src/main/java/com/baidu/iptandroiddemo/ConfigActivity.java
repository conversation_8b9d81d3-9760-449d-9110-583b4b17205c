package com.baidu.iptandroiddemo;

import com.baidu.iptandroiddemo.fragment.CellConfigFragment;
import com.baidu.iptandroiddemo.fragment.PlainTextFragment;

import android.app.Activity;
import android.app.Fragment;
import android.app.FragmentManager;
import android.app.FragmentTransaction;
import android.os.Bundle;

/**
 * 设置页面
 * Created by <PERSON><PERSON><PERSON><PERSON> on 18/2/3.
 */
public class ConfigActivity extends Activity implements CellConfigFragment.IConfigView {

    /**
     * 主设置界面：词库设置界面
     */
    private static final int FRAGMENT_CELL_CONFIG = 0;

    public static final int PLAIN_TEXT_FRAGMENT = 1;

    /**
     * 主界面Fragment
     */
    private CellConfigFragment mMainFragment;
    /**
     * 当前fragment的id
     */
    private int mFragmentId;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_config);

        mMainFragment = new CellConfigFragment();
        mMainFragment.setConfigView(this);
        jumpToFragment(FRAGMENT_CELL_CONFIG, mMainFragment);
    }

    /**
     * 跳转到指定Fragment
     */
    @Override
    public void jumpToFragment(int id, Bundle args) {
        Fragment fragment = getFragmentById(id);
        if (fragment != null) {
            fragment.setArguments(args);
            jumpToFragment(id, fragment);
        }
    }

    /**
     * 跳转到指定Fragment
     */
    private void jumpToFragment(int id, Fragment fragment) {
        if (fragment == null) {
            return;
        }
        mFragmentId = id;
        FragmentManager fm = getFragmentManager();
        FragmentTransaction transaction = fm.beginTransaction();
        transaction.replace(R.id.content, fragment);
        transaction.commit();
    }

    /**
     * 根据指定id获取界面
     *
     * @param fragmentId 界面id
     *
     * @return fragment实例
     */
    private Fragment getFragmentById(int fragmentId) {
        switch (fragmentId) {
            case FRAGMENT_CELL_CONFIG:
                return mMainFragment;
            case PLAIN_TEXT_FRAGMENT:
                return new PlainTextFragment();
            default:
                return null;
        }
    }

    @Override
    public void onBackPressed() {
        if (mFragmentId != FRAGMENT_CELL_CONFIG) {
            jumpToFragment(FRAGMENT_CELL_CONFIG, mMainFragment);
        } else {
            super.onBackPressed();
        }
    }
}
