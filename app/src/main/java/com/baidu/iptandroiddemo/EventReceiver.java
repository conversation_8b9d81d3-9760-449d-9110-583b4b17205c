package com.baidu.iptandroiddemo;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.util.Log;

import com.baidu.iptcore.ImeCoreManager;


/**
 * 用于接收输入法注册的广播，并处理相应的事件，包括系统广播
 */
public final class EventReceiver extends BroadcastReceiver {
    /**
     * 接收到广播之后处理的行为
     *
     * @param context context
     * @param intent  广播的 intent
     */
    @Override
    public void onReceive(final Context context, Intent intent) {
        if (intent != null) {
            detectAppInstall(intent);
        }
    }

    private static void detectAppInstall(Intent intent) {
        String action = intent.getAction() == null ? "" : intent.getAction();
        if (action.equals(Intent.ACTION_PACKAGE_ADDED)) {
            Uri data = intent.getData();
            if (data == null) {
                return;
            }
            // 覆盖安装时会发送ADDED，REPLACED,REMOVED三个广播, 因此使用EXTRA_REPLACING来辅助判断覆盖安装
            boolean isNewInstall = !intent.getBooleanExtra(Intent.EXTRA_REPLACING, false);
            final String pkg = data.getSchemeSpecificPart();
            long ts = System.currentTimeMillis();
            Log.e("wentaoli", "action: " + action + ", dctSetNameByBuff: " + pkg + ":" + ts + "," + isNewInstall);
            ImeCoreManager.getPad().dctSetNameByBuff(pkg, ts, isNewInstall);
        }
    }

}
