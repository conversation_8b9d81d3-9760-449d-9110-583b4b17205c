package com.baidu.iptandroiddemo;

import com.baidu.iptandroiddemo.core.CoreHandler;
import com.baidu.iptandroiddemo.core.ICoreHandler;

import android.app.Application;
import android.content.Context;

/**
 * 输入法全局对象管理
 */
public class Global {

    /**
     * 输入法服务
     */
    private static ImeService serv;

    /**
     * 负责内核生命周期的操作对象
     */
    private static volatile ICoreHandler coreHandler;
    private static Application appContext;

    /**
     * 获取内核操作对象
     */
    public static ICoreHandler getCoreHandler() {
        if (coreHandler == null) {
            synchronized (ICoreHandler.LOCK) {
                if (coreHandler == null) {
                    coreHandler = new CoreHandler();
                }
            }
        }
        return coreHandler;
    }

    /**
     * 获得全局输入法服务
     */
    public static ImeService getImeService() {
        return serv;
    }

    /**
     * 设置全局输入法服务
     */
    public static void setImeService(ImeService service) {
        serv = service;
    }
    public static void setImeApp(Application application) {
        appContext = application;
    }

    public static Context getImeApp() {
        return appContext;
    }
}
