package com.baidu.iptandroiddemo;

import android.app.Application;
import android.os.Build;
import android.support.annotation.RequiresApi;

import com.baidu.input.common.network.retrofit.RetrofitUtils;

/**
 * 输入法的Application
 * Created by <PERSON><PERSON><PERSON><PERSON> on 18/3/6.
 */
public class ImeApplication extends Application {

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    @Override
    public void onCreate() {
        super.onCreate();

        // init application instance
        Global.setImeApp(this);

        // init ime core
        Global.getCoreHandler().initImeCore(this);

        InputTracerWrapper.installTraceWatcher(this);
        InputTracerWrapper.start();
        RetrofitUtils.Configuration.Builder builder = new RetrofitUtils.Configuration.Builder();
        RetrofitUtils.setConfiguration(builder.baseUrl(() -> "http://mime-sh.baidu.com").build());
    }
}
