/**
 *
 */
package com.baidu.iptandroiddemo;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.inputmethodservice.InputMethodService;
import android.os.Build;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.View;
import android.view.inputmethod.CompletionInfo;
import android.view.inputmethod.EditorInfo;

import com.baidu.input.support.precommit.IPreCommit;
import com.baidu.iptandroiddemo.cloud.SugManager;
import com.baidu.iptandroiddemo.core.ImeInputConnHelper;
import com.baidu.iptcore.ImeCoreConsts;
import com.baidu.iptcore.ImeCoreManager;
import com.baidu.keymap.switcher.InputStatConsts;
import com.baidu.keymap.switcher.KeymapSwitcherFactory;
import com.baidu.pad.ImeInputView;
import com.baidu.pad.ImePadView;
import com.baidu.util.CommUtil;
import com.baidu.util.PopServiceWrapper;

import static com.baidu.iptcore.ConfigKey.ANDROID_ID;
import static com.baidu.iptcore.ConfigKey.CHANNEL;
import static com.baidu.iptcore.ConfigKey.CITY;
import static com.baidu.iptcore.ConfigKey.CLOUD_INPUT_TYPE;
import static com.baidu.iptcore.ConfigKey.CUID;
import static com.baidu.iptcore.ConfigKey.ENV_APP;
import static com.baidu.iptcore.ConfigKey.ENV_EDIT_TYPE;
import static com.baidu.iptcore.ConfigKey.ENV_NET_TYPE;
import static com.baidu.iptcore.ConfigKey.INLINE_SHOW;
import static com.baidu.iptcore.ConfigKey.IPT_PLATFORM;
import static com.baidu.iptcore.ConfigKey.IPT_VER;
import static com.baidu.iptcore.ConfigKey.IS_EASYCLOUD;
import static com.baidu.iptcore.ConfigKey.IS_SUGOPEN;
import static com.baidu.iptcore.ConfigKey.MODEL;
import static com.baidu.iptcore.ConfigKey.OS_VERSION;
import static com.baidu.iptcore.ConfigKey.PHONE_HEIGHT;
import static com.baidu.iptcore.ConfigKey.PHONE_VENDOR;
import static com.baidu.iptcore.ConfigKey.PHONE_WIDTH;
import static com.baidu.iptcore.ConfigKey.THEME;
import static com.baidu.iptcore.ConfigKey.TRACE_MODE;

/**
 * 输入法service
 */
public class ImeService extends InputMethodService {
    
    /** Sug管理 */
    private final SugManager sugManager = new SugManager();

    private final EventReceiver receiver = new EventReceiver();
    @Override
    public void onCreate() {
        super.onCreate();
        registerReceiveAppInstall();

        Global.setImeService(this);
        Global.getCoreHandler().initImeCore(this);

        // 初始设置项，for test
        initImeCoreDeviceConfigs();
        initImeCorePrefConfigs();
        sugManager.installWhiteList();
    }

    /**
     * 监听安装
     */
    public void registerReceiveAppInstall() {
        IntentFilter filter = new IntentFilter();
        filter.addAction(Intent.ACTION_PACKAGE_ADDED);
        filter.addAction(Intent.ACTION_PACKAGE_CHANGED); // 插件全量包更新
        filter.addAction(Intent.ACTION_PACKAGE_REPLACED);
        filter.addAction(Intent.ACTION_PACKAGE_INSTALL);
        filter.addDataScheme("package");
        registerReceiver(receiver, filter);
    }

    /**
     * 获取已经安装的APP的PackageInfo数据结构
     *
     * @param pkg   APP包名
     * @param flags getPackageInfo用到的标志
     * @return 如果应用已安装，则返回PackageInfo，否则返回NULL
     */
    public final PackageInfo getInstalledPkgInfo(String pkg, int flags) {
        PackageInfo packageInfo = null;
        if (!TextUtils.isEmpty(pkg)) {
            try {
                packageInfo = this.getApplicationContext().getPackageManager().getPackageInfo(pkg, flags);
            } catch (PackageManager.NameNotFoundException e) {
                if (BuildConfig.DEBUG) {
                    e.printStackTrace();
                }
            }
        }

        return packageInfo;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();

        unregisterReceiver(receiver);
        CommUtil.mPadView = null;
        CommUtil.mInputView = null;

        ImeCoreManager.close();
        Global.setImeService(null);
        System.exit(0);
    }

    @Override
    public void onInitializeInterface() {
        super.onInitializeInterface();
    }

    @Override
    public View onCreateInputView() {

        CommUtil.updateScreenSize(this);
        CommUtil.mPadView = new ImePadView(this);

        return CommUtil.mPadView;
    }

    @Override
    public View onCreateCandidatesView() {

        CommUtil.updateScreenSize(this);
        CommUtil.mInputView = new ImeInputView(this);

        return CommUtil.mInputView;
    }

    @Override
    public void onStartInput(EditorInfo attribute, boolean restarting) {
        super.onStartInput(attribute, restarting);

        KeymapSwitcherFactory.Companion.getCn().switchLayout(InputStatConsts.InputType.PY,
                InputStatConsts.LayoutType.KEYBOARD_26);

    }

    @Override
    public void onFinishInput() {
        super.onFinishInput();

        if (CommUtil.mPadView != null) {
            CommUtil.mPadView.reset();
        }
    }

    @Override
    public void onStartInputView(EditorInfo attribute, boolean restarting) {
        super.onStartInputView(attribute, restarting);
        initImeCoreEnvConfigs(attribute);

        PopServiceWrapper.attatch(this, CommUtil.mPadView);

        KeymapSwitcherFactory.Companion.getCn()
                .switchLayout(InputStatConsts.InputType.PY, InputStatConsts.LayoutType.KEYBOARD_26);
    }

    @Override
    public void onFinishInputView(boolean finishingInput) {
        super.onFinishInputView(finishingInput);
        PopServiceWrapper.detatch();
    }

    @Override
    public void onUpdateSelection(int oldSelStart, int oldSelEnd,
                                  int newSelStart, int newSelEnd, int candidatesStart,
                                  int candidatesEnd) {
        super.onUpdateSelection(oldSelStart, oldSelEnd, newSelStart, newSelEnd,
                candidatesStart, candidatesEnd);
        ImeInputConnHelper.getInstance().onUpdateSelection(oldSelStart, oldSelEnd, newSelStart,
                newSelEnd, candidatesStart, candidatesEnd);
    }

    @Override
    public void onDisplayCompletions(CompletionInfo[] completions) {
    }

    @Override
    public void onWindowHidden() {
        CommUtil.mPadView.onHidden();

        if (CommUtil.mInputView != null) {
            CommUtil.mInputView.reset();
        }

        ImeCoreManager.getPad().sendPadEvent(ImeCoreConsts.PAD_EVENT_DOWN, false);
    }

    private void initImeCoreDeviceConfigs() {

        DisplayMetrics dm = getResources().getDisplayMetrics();
        int phoneWidth = dm.widthPixels;
        int phoneHeight = dm.heightPixels;

        ImeCoreManager.getConfig().setString(CUID, "FE4D09890934EA132EF37F66CF6EDBC2|V2OWIHDQT");
        ImeCoreManager.getConfig().setInt(PHONE_WIDTH, phoneWidth);
        ImeCoreManager.getConfig().setInt(PHONE_HEIGHT, phoneHeight);
        ImeCoreManager.getConfig().setString(MODEL, Build.MODEL);
//        ImeCoreManager.getConfig().setString(IPT_VER, "********");
//        ImeCoreManager.getConfig().setString(CHANNEL, "ffffffff");
//        ImeCoreManager.getConfig().setString(IPT_PLATFORM, "a1");
        ImeCoreManager.getConfig().setString(IPT_VER, "8.2.403.48");
        ImeCoreManager.getConfig().setString(CHANNEL, "1001192a");
        ImeCoreManager.getConfig().setString(IPT_PLATFORM, "p-a1-3-72");
        // android id
        ImeCoreManager.getConfig().setString(ANDROID_ID, getAndroidID());
        // 生产厂商
        ImeCoreManager.getConfig().setString(PHONE_VENDOR, Build.MANUFACTURER);
        // os_version
        ImeCoreManager.getConfig().setString(OS_VERSION, Build.VERSION.RELEASE);
        long[] sceneGroupIDs = new long[]{224};
        ImeCoreManager.getConfig().setSceneGroupIDs(sceneGroupIDs);

        if (BuildConfig.DEBUG) {
            Log.i("cdf", "cuid=" + ImeCoreManager.getConfig().getString(CUID));
            Log.i("cdf", "width=" + ImeCoreManager.getConfig().getInt(PHONE_WIDTH));
            Log.i("cdf", "height=" + ImeCoreManager.getConfig().getInt(PHONE_HEIGHT));
            Log.i("cdf", "model=" + ImeCoreManager.getConfig().getString(MODEL));
            Log.i("cdf", "iptversion=" + ImeCoreManager.getConfig().getString(IPT_VER));
            Log.i("cdf", "channel=" + ImeCoreManager.getConfig().getString(CHANNEL));
            Log.i("cdf", "platform=" + ImeCoreManager.getConfig().getString(IPT_PLATFORM));
        }
    }

    /**
     * Return the android id of device.
     *
     * @return the android id of device
     */
    public String getAndroidID() {
        @SuppressLint("HardwareIds")
        String id = Settings.Secure.getString(getContentResolver(), Settings.Secure.ANDROID_ID);
        return id == null ? "" : id;
    }


    /**
     * 设置云输入与sug的地址
     */
    private void setSwitchCloudAddress() {
        int httpPort = 80;
        int udpPort = 4040;

        boolean isCloudUdp = true;

        ImeCoreManager.getConfig().setCloudAddress("http://olime-sh2.baidu.com/py?", httpPort,
                "cloud-sh.baidu.com", udpPort,
                "https://olime-sh.baidu.com/newpy?", httpPort,
                "udpsug-sh.baidu.com", udpPort,
                "https://olime-sh2.baidu.com/py?", httpPort,
                isCloudUdp, false); // SUG全量使用http
        ImeCoreManager.getConfig().setUplDataAddress("https://mime-sh.baidu.com/v5/fb/rt?", httpPort);
    }

    private void initImeCoreEnvConfigs(EditorInfo attribute) {
        setSwitchCloudAddress();
        ImeCoreManager.getConfig().setInt(TRACE_MODE, 1);
        setSwitchCloudAddress();
        ImeCoreManager.getConfig().setBoolean(IS_EASYCLOUD, false);
        ImeCoreManager.getConfig().setBoolean(IS_SUGOPEN, true);
        ImeCoreManager.getConfig().setString(ENV_APP, attribute.packageName);
        ImeCoreManager.getConfig().setString(CITY, "上海");
        ImeCoreManager.getConfig().setInt(ENV_NET_TYPE, ImeCoreConsts.NET_TYPE_WIFI); // test::固定为wifi
        ImeCoreManager.getConfig().setInt(ENV_EDIT_TYPE, 1);
        ImeCoreManager.getConfig().setString(THEME, "default");
        ImeCoreManager.getConfig().setBoolean(INLINE_SHOW, true);

        if (BuildConfig.DEBUG) {
            Log.i("cdf", "envApp=" + ImeCoreManager.getConfig().getString(ENV_APP));
            Log.i("cdf", "envCity=" + ImeCoreManager.getConfig().getInt(CITY));
            Log.i("cdf", "envNetType=" + ImeCoreManager.getConfig().getInt(ENV_NET_TYPE));
            Log.i("cdf", "envEditType=" + ImeCoreManager.getConfig().getInt(ENV_EDIT_TYPE));
            Log.i("cdf", "envTheme=" + ImeCoreManager.getConfig().getString(THEME));
        }

        ImeCoreManager.getConfig().setPreCommitType(IPreCommit.TYPE_SET_COMPOSING_TEXT);

        Log.e("wentaoli", "sugDataMatch:" + ImeCoreManager.getPad().isMatchSugWhiteData());
    }

    private void initImeCorePrefConfigs() {
        ImeCoreManager.getConfig().setInt(CLOUD_INPUT_TYPE, ImeCoreConsts.CLOUD_ALL);
    }
}
