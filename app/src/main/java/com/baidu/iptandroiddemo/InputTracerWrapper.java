package com.baidu.iptandroiddemo;

import android.annotation.SuppressLint;
import android.app.Application;
import android.content.Context;
import android.util.Log;

import com.baidu.flywheel.FlyWheel;
import com.baidu.flywheel.trace.TraceInfo;
import com.baidu.flywheel.util.MatrixLog;
import com.baidu.input.inputtrace.store.InputTracerContext;
import com.baidu.input.inputtrace.store.InputTracerWatcher;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class InputTracerWrapper {

    private static final String TAG = "wentaoli";

    /**
     * 是否启动
     */
    private static boolean isStarted = false;

    /**
     * 当前的Trace的插件
     */
    private static InputTracerWatcher traceWatcher;

    /**
     * Application启动时安装
     *
     * @param application Application
     */
    public static void installTraceWatcher(Application application) {
        traceWatcher = FlyWheel.INSTANCE.install(new ImeTraceWatcherContext(application), InputTracerWatcher.class);
    }

    /**
     * 启动监测
     */
    @SuppressLint("LongLogTag")
    public static void start() {
        if (isStarted) {
            return;
        }
        if (traceWatcher != null) {
            Log.i(TAG, "trace watcher start");
            traceWatcher.start();
        }
    }

    /**
     * 停止监测
     */
    @SuppressLint("LongLogTag")
    public static void stop() {
        if (!isStarted) {
            return;
        }
        if (traceWatcher != null) {
            Log.i(TAG, "trace watcher stop");
            traceWatcher.stop();
            traceWatcher = null;
            isStarted = false;
        }
    }

    private static class ImeTraceWatcherContext extends InputTracerContext {
        public ImeTraceWatcherContext(Application application) {
            super(application);
        }

        @Override
        public boolean isDevEnv() {
            return true;
        }

        @Nullable
        @Override
        public MatrixLog.MatrixLogImp traceLogImpl() {
            return new MatrixLog.MatrixLogImp() {
                @Override
                public void v(final String tag, final String format, final Object... params) {
                    String log = (params == null || params.length == 0) ? format : String.format(format, params);
                    android.util.Log.v(tag, log);
                }

                @Override
                public void i(final String tag, final String format, final Object... params) {
                    String log = (params == null || params.length == 0) ? format : String.format(format, params);
                    android.util.Log.i(tag, log);

                }

                @Override
                public void d(final String tag, final String format, final Object... params) {
                    String log = (params == null || params.length == 0) ? format : String.format(format, params);
                    android.util.Log.d(tag, log);
                }

                @Override
                public void w(final String tag, final String format, final Object... params) {
                    String log = (params == null || params.length == 0) ? format : String.format(format, params);
                    android.util.Log.w(tag, log);
                }

                @Override
                public void e(final String tag, final String format, final Object... params) {
                    String log = (params == null || params.length == 0) ? format : String.format(format, params);
                    android.util.Log.e(tag, log);
                }

                @Override
                public void printErrStackTrace(String tag, Throwable tr, String format, Object... params) {
                    String log = (params == null || params.length == 0) ? format : String.format(format, params);
                    if (log == null) {
                        log = "";
                    }
                    log += "  " + android.util.Log.getStackTraceString(tr);
                    android.util.Log.e(tag, log);
                }
            };
        }

        /**
         * 耗时阈值
         */
        @Override
        public int getThresholdMs() {
            return 100;
        }

        /**
         * provide app info
         * E.g. user id, skin info, app version, app flavor,
         */
        @NotNull
        @Override
        public String appInfo() {
            return "dddd";
        }

        @Override
        public void onTraceBlock(@NotNull Context context, @NotNull TraceInfo info) {
            try {
                Log.e("wentaoli inputtracer", "onTraceBlock: " + info.getMap());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}
