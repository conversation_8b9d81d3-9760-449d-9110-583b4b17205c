package com.baidu.iptandroiddemo;

import android.app.Activity;
import android.app.AlertDialog;
import android.app.AlertDialog.Builder;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.view.View;

import com.baidu.setting.SettingGuidLogic;

public class MainActivity extends Activity implements
        SettingGuidLogic.GuidNextStepListener, View.OnClickListener {

    private SettingGuidLogic mGuidLogic;
    private ImeChageReceiver mImeChgReceiver;

    @Override
    public void onClick(View v) {
        Intent intent = new Intent(this, ConfigActivity.class);
        startActivity(intent);
    }

    private class ImeChageReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {

            if (mGuidLogic != null) {
                mGuidLogic.updateCtrEnable(true);
            }
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        reset();

        setContentView(R.layout.guide_view);
        
        setupViews();

        mGuidLogic = new SettingGuidLogic(this);
        mGuidLogic.addNextStepListener(this);

        if (!mGuidLogic.mDefaultIme || !mGuidLogic.mEnabelIme) {
            if (mImeChgReceiver == null) {

                mImeChgReceiver = new ImeChageReceiver();
                IntentFilter filter = new IntentFilter();

                filter.addAction("android.intent.action.INPUT_METHOD_CHANGED");
                registerReceiver(mImeChgReceiver, filter);
            }
        } else {

        }
    }

    private void setupViews() {
        View view = findViewById(R.id.setting_icon);
        view.setOnClickListener(this);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        reset();
    }
    
    @Override
    public void onWindowFocusChanged(boolean hasFocus)
    {
        super.onWindowFocusChanged(hasFocus);
        
        if (hasFocus && mGuidLogic != null)
        {
            mGuidLogic.updateCtrEnable(true);
        }
    }

    @Override
    public void onNextStep() {
        Builder builder  = new Builder(MainActivity.this);
        builder.setTitle("提示" ) ;
        builder.setMessage("输入法演示已启用") ;
        builder.setPositiveButton("确认" ,  null );
        builder.show();

    }

    private void reset() {

        if (mImeChgReceiver != null) {
            unregisterReceiver(mImeChgReceiver);
        }

        mGuidLogic = null;
        mImeChgReceiver = null;
    }
}
