package com.baidu.iptandroiddemo.bean;

import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * created by yanglingyun<PERSON> on 8/6/21
 */
public class TestAiFont {

    @SerializedName("character")
    private String character;

    @SerializedName("points")
    private List<Integer> points;

    public String getCharacter() {
        return character;
    }

    public void setCharacter(String character) {
        this.character = character;
    }

    public List<Integer> getPoints() {
        return points;
    }

    public void setPoints(List<Integer> points) {
        this.points = points;
    }
}
