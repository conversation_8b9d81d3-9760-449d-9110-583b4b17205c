package com.baidu.iptandroiddemo.cloud;

import com.baidu.iptcore.ImeCoreManager;

import static com.baidu.iptcore.ConfigKey.SUG_WHITE_DATA;

/**
 * Sug管理对象
 * Created by cdf on 18/4/16.
 */
public class SugManager {

    /**
     * {"com.huawei.appmarket":[1,5],"com.liwentao.demo":[1,2,3,4,5,6,7,8,9,10]} 经过base64后的结果
     */
    private static final String TEST_JSON =
            "8RAGq9hemjN85Aq6oyFqYkyWxqITrVfx4asfiyhZeokTJXqUk5EUqf4jwINn4m6J6OLA8zaxs8zTas8tzasU8zP_stoNavhfZqqSB";
    

    public void installWhiteList() {
        ImeCoreManager.getConfig().setString(SUG_WHITE_DATA, TEST_JSON);
    }
}
