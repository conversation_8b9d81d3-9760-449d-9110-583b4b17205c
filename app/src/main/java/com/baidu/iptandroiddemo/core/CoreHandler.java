package com.baidu.iptandroiddemo.core;

import com.baidu.input.support.DefaultCoreDutyHandler;
import com.baidu.iptandroiddemo.BuildConfig;
import com.baidu.iptandroiddemo.Global;
import com.baidu.iptcore.ImeCoreConfiguration;
import com.baidu.iptcore.ImeCoreConsts;
import com.baidu.iptcore.ImeCoreManager;
import com.baidu.util.PathUtil;

import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.text.TextUtils;

/**
 * 内核生命周期的管理对象
 */
public class CoreHandler implements ICoreHandler {

    /**
     * 内核的callback
     */
    private final ImeCoreCallback imeCoreCallback = new ImeCoreCallback();

    /**
     * 内核是否已经打开
     */
    private volatile boolean imeCoreOpened = false;

    @Override
    public void initImeCore(Context context) {

        synchronized (this) {
            imeCoreCallback.setImeService(Global.getImeService());
            if (!imeCoreOpened) {
                imeCoreOpened = initImeCoreInternal(context);
            }
        }
    }

    private boolean initImeCoreInternal(Context context) {
        System.loadLibrary("iptcore");

        ImeCoreConfiguration configuration = new ImeCoreConfiguration.Builder()
                .context(context.getApplicationContext())
                .dictionaryPath(PathUtil.dictDirPath())
                .platformEnv(imeCoreCallback)
                .imeCoreCallback(new DefaultCoreDutyHandler(imeCoreCallback))
                .enableLog(true)
                .enableProfiler(true)
                .packageInfo(getInstalledPkgInfo(BuildConfig.APPLICATION_ID, PackageManager.GET_SIGNATURES))
                .flavor(ImeCoreConsts.Flavor.MAINLINE)
                .build();
        ImeCoreManager.open(configuration);

        return true;
    }

    public static final PackageInfo getInstalledPkgInfo(String pkg, int flags) {
        PackageInfo packageInfo = null;
        if (!TextUtils.isEmpty(pkg)) {
            try {
                packageInfo = Global.getImeApp().getPackageManager().getPackageInfo(pkg, flags);
            } catch (PackageManager.NameNotFoundException e) {
                packageInfo = null;
            }
        }

        return packageInfo;
    }
}
