package com.baidu.iptandroiddemo.core;

import android.graphics.Point;
import android.graphics.Rect;
import android.os.Handler;
import android.os.Looper;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.util.Log;
import android.view.KeyEvent;
import android.view.inputmethod.ExtractedText;
import android.view.inputmethod.ExtractedTextRequest;
import android.view.inputmethod.InputConnection;

import com.baidu.input.inputtrace.api.InputTracer;
import com.baidu.input.inputtrace.api.MethodIds;
import com.baidu.input.support.InputDutyCallback;
import com.baidu.input.support.state.AICandIconState;
import com.baidu.input.support.state.AICandState;
import com.baidu.input.support.state.AICorrectInlineState;
import com.baidu.input.support.state.AIPadDataState;
import com.baidu.input.support.state.AIPadLoadingState;
import com.baidu.input.support.state.AppAdTrigger;
import com.baidu.input.support.state.CandInfoState;
import com.baidu.input.support.state.ContactInfoState;
import com.baidu.input.support.state.CoreTraceState;
import com.baidu.input.support.state.EggInfoState;
import com.baidu.input.support.state.InlineShowState;
import com.baidu.input.support.state.InputState;
import com.baidu.input.support.state.IptCandState;
import com.baidu.input.support.state.IptListState;
import com.baidu.input.support.state.MapTriggerWordsInfoState;
import com.baidu.input.support.state.RareCandState;
import com.baidu.input.support.state.SugAdState;
import com.baidu.input.support.state.SugCardState;
import com.baidu.input.support.state.SugState;
import com.baidu.input.support.state.TriggerWordsInfoState;
import com.baidu.iptandroiddemo.ImeService;
import com.baidu.iptcore.ImeCoreManager;
import com.baidu.iptcore.ImePlatformEnv;
import com.baidu.iptcore.info.IptAppMsgInfo;
import com.baidu.iptcore.info.IptCoreCandInfo;
import com.baidu.iptcore.info.IptCoreDutyInfo;
import com.baidu.iptcore.info.IptCoreSugAdAction;
import com.baidu.iptcore.util.CoreMethodCostRecorder;
import com.baidu.util.CommUtil;

import java.util.Arrays;

/**
 * 输入法内核的回调对象
 */
public class ImeCoreCallback implements ImePlatformEnv, InputDutyCallback {

    private static final int ID_TYPE_STRING = 1;

    private ImeService imeService;

    public void setImeService(ImeService imeService) {
        this.imeService = imeService;
    }

    @Override
    public boolean interceptDuty(IptCoreDutyInfo dutyInfo) {
        if (imeService == null || !imeService.isInputViewShown()) {
            return true;
        }
        return false;
    }

    @Override
    public void updateCand(IptCandState iptCandState) {
        if (CommUtil.sSymFragment != null && CommUtil.sSymFragment.isShowing()) {
            CommUtil.sSymFragment.update(iptCandState);
        } else if (CommUtil.sMoreCandFragment != null && CommUtil.sMoreCandFragment.isShowing()) {
            CommUtil.sMoreCandFragment.update(iptCandState);
        } else if (CommUtil.mPadView != null) {
            CommUtil.mPadView.updateCand(iptCandState);
        }
    }

    @Override
    public void updateInputBar(InputState inputState) {
        if (CommUtil.mInputView != null) {
            CommUtil.mInputView.update(inputState);
        }
    }

    @Override
    public void updateList(IptListState iptListState) {
        if (CommUtil.sSymFragment != null && CommUtil.sSymFragment.isShowing()) {
            CommUtil.sSymFragment.updateList(iptListState);
        } else if (CommUtil.sMoreCandFragment != null && CommUtil.sMoreCandFragment.isShowing()) {
            CommUtil.sMoreCandFragment.updateList(iptListState);
        } else if (CommUtil.mPadView != null) {
            CommUtil.mPadView.updateList(iptListState);
        }
    }

    @Override
    public void switchKeyboardWithTip(int padId, boolean isExternalSwitch, int tipState) {

    }

    @Override
    public void switchKeyboard(int padId, boolean isExternalSwitch) {

    }

    @Override
    public void refreshTips(int tipState) {

    }

    @Override
    public void refreshTrack() {

    }

    @Override
    public void submitBackspaceKeyEvent() {
        imeService.sendDownUpKeyEvents(KeyEvent.KEYCODE_DEL);
    }

    @Override
    public void submitEnterKeyEvent() {
        imeService.sendDownUpKeyEvents(KeyEvent.KEYCODE_ENTER);
    }

    @Override
    public void startListDefine() {

    }

    @Override
    public void submitText(int insertType, String insertText) {
        InputConnection connection = imeService.getCurrentInputConnection();
        if (connection != null) {
            connection.commitText(insertText, 1);
        }

        if (!TextUtils.isEmpty(insertText)) {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < 100; i++) {
                sb.append(insertText).append(";");
            }
            // 测试
            ImeCoreManager.getPad().userVoiceInputLog(sb.toString());
        }
    }

    @Override
    public void submitComposingText(String text) {
        InputConnection connection = imeService.getCurrentInputConnection();
        if (connection != null) {
            connection.setComposingText(text, 1);
        }
    }

    @Override
    public void finishComposingText() {
        InputConnection connection = imeService.getCurrentInputConnection();
        if (connection != null) {
            connection.finishComposingText();
        }
    }

    @Override
    public void actOpenSchema(int schemaSource, String schemaType, String schemaInfo) {

    }

    @Override
    public void finishAndComposingText(String insertText) {

    }

    @Override
    public void updateAIPadLoadingState(AIPadLoadingState aiPadLoadingState) {

    }

    @Override
    public void updateAIPadDataState(AIPadDataState aiPadDataState) {

    }

    @Override
    public void updateAICandIconState(AICandIconState aiCandIconState) {

    }

    @Override
    public void updateAICandState(AICandState aiCandState) {

    }

    @Override
    public void updateAICorrectInlineState(AICorrectInlineState aiCorrectInlineState) {

    }

    @Override
    public void updateSug(SugState sugState) {
        int cnt = sugState.getCount();
        Log.e("wentaoli", "updateSug: Count: " + cnt
                + " SugState: " + sugState.getSugState()
                + " ActionType: " + sugState.getSugActionType()
                + " SourceId: " + sugState.getSugSourceId()
                + " SelectedPos: " + sugState.getSelectedPos());

        Log.e("wentaoli", "updateSug: timeout: " + ImeCoreManager.getPad().getSugAdTimeoutMs());
        if (cnt > 0) {
            StringBuilder stringBuilder = new StringBuilder();
            boolean hasCsj = false;
            for (int i = 0; i < cnt; i++) {
                IptCoreCandInfo sugResult = sugState.getSugItem(i);
                stringBuilder.append(sugResult.uni()).append("-").append(sugResult.serviceType()).append(";");
                if (sugResult.serviceType() == 11) {
                    Log.e("wentaoli", "sugAd:" + sugResult.getSugAdStyle());
                }
                if (sugResult.serviceType() == 12) {
                    Log.e("wentaoli", "sugWmAd:" + sugResult.getWmSugAdInfo());
                    if (sugResult.getWmSugAdInfo() != null) {
                        hasCsj |= sugResult.getWmSugAdInfo().isFromCsj;
                    }
                }
            }
            Log.e("wentaoli", "updateSug: " + stringBuilder);
            Log.e("wentaoli", "load core sug?: " + hasCsj);
            if (hasCsj) {
                ImeCoreManager.getPad().loadCoreSugAd(AppAdTrigger.Sug);
            }
        }
    }

    @Override
    public void updateCoreSugAd(SugAdState state) {
        AppAdTrigger trigger = state.getAdTrigger();
        Log.e("wentaoli", "updateCoreSugAd: " + trigger + "," + state);

        if (!state.isEmpty()) {
            new Handler(Looper.getMainLooper()).post(() -> {
                int count = state.getCount();
                for (int i = 0; i < count; i++) {
                    IptCoreSugAdAction action = ImeCoreManager.getPad().coreSugAdClick(i, trigger, new Rect(), new Point(), new Point());
                    Log.e("wentaoli", "updateCoreSugAd: action:" + action);
                }
            });
        }

    }

    @Override
    public void updateSugCard(SugCardState sugCardState) {

    }

    @Override
    public void updateCandInfo(CandInfoState candInfoState) {

    }

    @Override
    public void updateContactInfo(ContactInfoState contactInfoState) {

    }

    @Override
    public void updateSrvCloudWhiteVer(int cloudVer) {

    }

    @Override
    public void updateEggInfo(EggInfoState eggInfo) {

    }

    @Override
    public void updatePreSelection(int cursorBefore, int cursorAfter, boolean isInsert) {
        if (imeService == null) {
            return;
        }
        InputConnection connection = imeService.getCurrentInputConnection();
        if (connection == null) {
            return;
        }

        if (cursorBefore == 0 && cursorAfter == 0) {
            connection.finishComposingText();
        } else {
            ExtractedText exText = connection.getExtractedText(new ExtractedTextRequest(), 0);
            if (exText != null) {
                int pos = exText.selectionStart;
                connection.setComposingRegion(pos - cursorBefore, pos + cursorAfter);
            }
        }
    }

    @Override
    public void updateSugSelection(int sugSelection) {

    }

    @Override
    public void updateSugCardSelection(int sugCardSelection) {

    }

    @Override
    public void updateKeyboardUI() {

    }

    @Override
    public void updateKeyboardUI(IptCoreDutyInfo dutyInfo, @Nullable int[] actionKeyIds) {
        updateKeyboardUI();
        if (actionKeyIds != null) {
            for (int id : actionKeyIds) {
                int methodId = MethodIds.INPUT_REFRESH_UI_START + id;
                Log.e("wentaoli inputtracer", "refresh ui:" + methodId + ", " + id);
                if (methodId >= MethodIds.INPUT_REFRESH_UI_END) {
                    continue;
                }
                InputTracer.i(methodId);
            }
        }

        Log.e("wentaoli", "core cost: " + coreMethodCosts());
        Log.e("wentaoli", "core infos: " + Arrays.toString(CoreMethodCostRecorder.getCoreMethodInfos()));
    }


    private static String coreMethodCosts() {
        long[] data = CoreMethodCostRecorder.getCoreInnerMethodCosts();
        int length = data.length;
        if (length > 0) {
            StringBuilder sb = new StringBuilder();
            for (long datum : data) {
                sb.append((int) (datum >> 32)).append(":").append((int) datum).append(",");
            }
            return sb.toString();
        }
        return "";
    }

    @Override
    public void jumpUrl(String url, String word) {

    }

    @Override
    public void submitCloudPicture(String url, String word) {

    }

    @Override
    public void startDownload(String title, String data, String url) {

    }

    @Override
    public void insertCursorBackward(String insertText) {

    }

    private void submitNormalText(String insertText) {
        if (imeService == null) {
            return;
        }
        imeService.getCurrentInputConnection().commitText(insertText, 1);
    }

    @Override
    public void replaceCandWords(String insertText, int replaceBefore, int replaceAfter,
                                 boolean isReplaceKeepCursor) {
        InputConnection inputConnection = imeService.getCurrentInputConnection();
        if (inputConnection == null) {
            return;
        }
        if (replaceBefore == 0 && replaceAfter == 0) {
            if (!TextUtils.isEmpty(insertText)) {
                inputConnection.beginBatchEdit();
                submitNormalText(insertText);
                if (isReplaceKeepCursor) {
                    for (int i = 0; i < insertText.length(); i++) {
                        imeService.sendDownUpKeyEvents(KeyEvent.KEYCODE_DPAD_LEFT);
                    }
                }
                inputConnection.endBatchEdit();
            }
        } else {
            inputConnection.beginBatchEdit();
            int oldSelStart = ImeInputConnHelper.getInstance().getCursorPosStart();
            int oldSelEnd = ImeInputConnHelper.getInstance().getCursorPosEnd();
            int newSelStart = oldSelStart >= replaceBefore ? oldSelStart - replaceBefore : 0;
            int newCursorPos =
                    (TextUtils.isEmpty(insertText) ? 0 : insertText.length()) + newSelStart;

            CharSequence replaceBeforeStr = imeService.getCurrentInputConnection()
                    .getTextBeforeCursor(replaceBefore, 0);

            if (!TextUtils.isEmpty(replaceBeforeStr)
                    && replaceBeforeStr.length() == replaceBefore
                    && !TextUtils.isEmpty(insertText)) {
                int sameLen = 0;
                for (; sameLen < replaceBefore && sameLen < insertText.length(); sameLen++) {
                    if (replaceBeforeStr.charAt(sameLen) != insertText.charAt(sameLen)) {
                        break;
                    }
                }
                if (sameLen > 0) {
                    replaceBefore -= sameLen;
                    insertText = insertText.substring(sameLen);
                }
            }
            if (replaceBefore > 0 || replaceAfter > 0) {
                int start = oldSelStart - replaceBefore;
                // 部分应用或者场景中可能获取的光标位置oldSelStart不正确（如View未正常实现InputConnection的方法），
                // 导致setComposingRegion失效，上屏重复内容
                if (start < 0) {
                    inputConnection.deleteSurroundingText(replaceBefore, replaceAfter);
                } else {
                    inputConnection.setComposingRegion(oldSelStart - replaceBefore, oldSelEnd + replaceAfter);
                }
            }

            if (!TextUtils.isEmpty(insertText)) {
                submitNormalText(insertText);
            } else {
                inputConnection.commitText("", 1);
            }

            if (isReplaceKeepCursor) {
                if (oldSelStart == oldSelEnd && newCursorPos != oldSelStart) {
                    if (newCursorPos > oldSelStart) {
                        for (int i = 0; i < (newCursorPos - oldSelStart); i++) {
                            imeService.sendDownUpKeyEvents(KeyEvent.KEYCODE_DPAD_LEFT);
                        }
                    } else {
                        for (int i = 0; i < (oldSelStart - newCursorPos); i++) {
                            imeService.sendDownUpKeyEvents(KeyEvent.KEYCODE_DPAD_RIGHT);
                        }
                    }
                }
            }
            inputConnection.endBatchEdit();
        }
    }

    @Override
    public void actSugInsert(String word) {

    }

    @Override
    public void actSugCurrentAppLink(String url, String word) {

    }

    @Override
    public void actSugCurrentAppSearch(String url, String word) {

    }

    @Override
    public void actSugThirdPartyAppLink(String cmd, String app, String data, String word) {

    }

    @Override
    public void actSugThirdPartyAppSearch(String cmd, String app, String data, String word) {

    }

    @Override
    public void actSugCustomLink(String url, String word) {

    }

    @Override
    public void insertAndEnter(int insertType, String insertText) {

    }

    @Override
    public void refreshCoreTrace(CoreTraceState traceState) {

    }

    @Override
    public void updateInlineShow(InlineShowState state) {
        Log.e("wentaoli", "updateInlineShow: " + state.getInline());
    }

    @Override
    public void updateTriggerWordInfo(TriggerWordsInfoState triggerWordInfoState) {
        Log.e("TriggerWord", "updateTriggerWordInfo: " + triggerWordInfoState.toString());
    }

    @Override
    public void updateMapTriggerWordInfo(MapTriggerWordsInfoState triggerWordInfoState, boolean isRefreshMapData) {
        Log.e("TriggerWord", "updateMapTriggerWordInfo: " + triggerWordInfoState.toString());
    }

    @Override
    public void updateHwGesture(int hwGesture) {
        Log.e("wentaoli", "updateHwGesture: " + hwGesture);
    }

    @Override
    public void updateRareCandState(RareCandState state) {
        int count = state.getCandCount();
        Log.e("wentaoli", "updateRareCandState: " + count);
        for (int i = 0; i < count; i++) {
            Log.e("wentaoli", "RareCand: " + state.getCandAt(i));
        }
    }

    @Override
    public int findApp(IptAppMsgInfo[] appList) {
        return 0;
    }

    @Override
    public boolean requestUrlResource(String[] urlList, long callback, int tag) {
        Log.e("wentaoli iptcore", "requestUrlResource: " + Arrays.toString(urlList) );
        if (urlList == null || urlList.length == 0) {
            return true;
        }

        new Thread(() -> ImeCoreManager.getPad().execCallback(tag, callback)).start();
        return false;
    }

    @Override
    public boolean isAllowAiPadAutoOpen(int tabType) {
        return true;
    }

    @Override
    public boolean isAllowCloudCampaignShow() {
        return false;
    }

    @Override
    public boolean isAllowCloudAdShow() {
        return false;
    }

    @Override
    public boolean isAllowMinorCandHighEQShow() {
        return false;
    }

    @Override
    public boolean isCloudCampaignFinalyShow(String schemaType, String schemaInfo, long userVipCondition, long vipExpireDay, long vipExpireDayTo) {
        return false;
    }

    @Override
    public boolean isCommonTriggerFinallyShow(String validPeople, int belongType) {
        return false;
    }

    @Override
    public String getEditBeforeCursor(int maxLength) {
        InputConnection conn = imeService.getCurrentInputConnection();
        if (conn != null) {
            CharSequence charSequence = conn.getTextBeforeCursor(maxLength, 0);
            if (charSequence != null) {
                return charSequence.toString();
            }
        }
        return null;
    }

    @Override
    public String getEditAfterCursor(int maxLength) {
        InputConnection conn = imeService.getCurrentInputConnection();
        if (conn != null) {
            CharSequence charSequence = conn.getTextAfterCursor(maxLength, 0);
            if (charSequence != null) {
                return charSequence.toString();
            }
        }
        return null;
    }

    @Override
    public String getEditSelection(int maxLength) {
        InputConnection conn = imeService.getCurrentInputConnection();
        if (conn != null) {
            CharSequence charSequence = conn.getSelectedText(0);
            if (charSequence != null) {
                return charSequence.toString().substring(0, maxLength);
            }
        }
        return null;
    }

    @Override
    public String getEditTextDialogue(int maxLength) {
        return null;
    }

    @Override
    public int getEnergyValue() {
        return -1;
    }


    @Override
    public void willEnterPad(int fromPadId, int toPadId) {

    }

    @Override
    public void didEnterPad(int fromPadId, int toPadId) {

    }

    @Override
    public boolean hasInstallApp(String packageName) {
        return false;
    }
}
