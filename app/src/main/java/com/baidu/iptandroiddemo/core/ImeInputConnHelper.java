package com.baidu.iptandroiddemo.core;

import com.baidu.iptcore.ImeCoreManager;

public class ImeInputConnHelper {

    /**
     * 记录的光标位置信息。用于向外提供当前光标位置，而不用通过InputConnection获取
     */
    private final CursorPosInfo cursorPosInfo = new CursorPosInfo();

    /**
     * 单例
     */
    private static volatile ImeInputConnHelper sInstance;

    /**
     * 获取单例
     */
    public static ImeInputConnHelper getInstance() {
        if (sInstance == null) {
            synchronized (ImeInputConnHelper.class) {
                if (sInstance == null) {
                    sInstance = new ImeInputConnHelper();
                }
            }
        }
        return sInstance;
    }

    private ImeInputConnHelper() {
        // make constructor private
    }

    /**
     * 获得当前记录的光标起始位置
     * 当有选中内容时，起始位置和终止位置不相等，其余相等
     */
    public int getCursorPosStart() {
        return cursorPosInfo.selStart;
    }

    /**
     * 获得当前记录的光标终止位置
     */
    public int getCursorPosEnd() {
        return cursorPosInfo.selEnd;
    }

    /**
     * 是否有选中
     */
    public boolean isCursorInSelection() {
        return cursorPosInfo.selEnd != cursorPosInfo.selStart;
    }

    /**
     * 光标移动的通知
     */
    public void onUpdateSelection(int oldSelStart, int oldSelEnd,
                                  int newSelStart, int newSelEnd,
                                  int candidatesStart, int candidatesEnd) {
        recordCursorPos(oldSelStart, oldSelEnd, newSelStart, newSelEnd, candidatesStart, candidatesEnd);

        // 有时候在有预上屏内容的时候，连续调用 finishComposing 和 commitText, 这时候会触发两次 IME 的 onUpdateSelection 回调。
        // 但是这两次 onUpdateSelection 回调都是在 commitText 调用之后，
        // 而 SmartPredictorBase 类的 updateByCommitText 变量是在 commitText 立马就被赋值了的。
        // 这样就会导致在处理 finishComposing 触发的 onUpdateSelection 回调时，可能会被误认为是 commitText 触发的 onUpdateSelection
        // 从而错误的调用了 reduceCommitText。
        // 为了处理这种情况，应该先判断是否是 finishComposing 触发的 onUpdateSelection 回调，如果是，无需产生预测。
        // 见 BAIDUINPUTBUG-127471
        if (oldSelStart == oldSelEnd && oldSelStart == newSelStart
                && newSelStart == newSelEnd && candidatesStart == candidatesEnd) {
            return;
        }

        // finishComposingText时不产生输入预测
        if (oldSelEnd == oldSelStart && oldSelStart == newSelStart) {
            return;
        }

        // 告诉内核光标移动了
        ImeCoreManager.getPad().actEditCursorChange();
    }

    /**
     * 记录当前的光标位置
     */
    private void recordCursorPos(int oldSelStart, int oldSelEnd, int newSelStart, int newSelEnd,
                                 int candidatesStart, int candidatesEnd) {
        cursorPosInfo.updateSelection(newSelStart, newSelEnd, candidatesStart, candidatesEnd);
    }

    /**
     * 光标位置信息
     */
    private static class CursorPosInfo {
        private int selStart;
        private int selEnd;
        private int candidatesStart;
        private int candidatesEnd;

        private void updateSelection(int newSelStart, int newSelEnd,
                                     int candidatesStart, int candidatesEnd) {
            this.selStart = newSelStart;
            this.selEnd = newSelEnd;
            this.candidatesStart = candidatesStart;
            this.candidatesEnd = candidatesEnd;
        }
    }
}
