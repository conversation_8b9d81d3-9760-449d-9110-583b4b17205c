package com.baidu.iptandroiddemo.fragment;

import static com.baidu.iptandroiddemo.ConfigActivity.PLAIN_TEXT_FRAGMENT;

import android.app.Fragment;
import android.os.Bundle;
import android.support.annotation.Nullable;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;

import com.baidu.input.manager.AssetsManager;
import com.baidu.iptandroiddemo.R;
import com.baidu.iptandroiddemo.bean.TestAiFont;
import com.baidu.iptcore.ImeCoreConsts;
import com.baidu.iptcore.ImeCoreManager;
import com.baidu.iptcore.info.IptPhraseGroup;
import com.baidu.iptcore.info.IptPhraseItem;
import com.baidu.iptcore.util.Logger;
import com.google.gson.Gson;

import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.Arrays;
import java.util.List;

/**
 * 词库设置界面
 * Created by chendanfneg on 18/2/3.
 */
public class CellConfigFragment extends Fragment implements View.OnClickListener {

    private IConfigView mConfigView;

    public interface IConfigView {
        void jumpToFragment(int id, Bundle args);
    }

    public CellConfigFragment() {
        super();
    }

    public void setConfigView(IConfigView configView) {
        mConfigView = configView;
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, Bundle savedInstanceState) {
        final ViewGroup content = (ViewGroup) inflater.inflate(R.layout.cell_config, container, false);
        ViewGroup contentChild = (ViewGroup) content.getChildAt(0);
        for (int i = 0; i < contentChild.getChildCount(); i++) {
            contentChild.getChildAt(i).setOnClickListener(this);
        }
        return content;
    }

    @Override
    public void onClick(View v) {
        String newGroupInfo;
        String newItemInfo;
        byte[] encryption = new byte[1024 * 32];
        switch (v.getId()) {
            case R.id.btn_get_phrase_group_count:
                int cnt = ImeCoreManager.getCellConfig().getPhraseGroupCount();
                showResult("个性短语分组：" + cnt);
                break;
            case R.id.btn_get_phrase_group:
                String phraseGroupInfo = getPhraseGroupInfo();
                showResult(phraseGroupInfo);
                break;
            case R.id.btn_add_phrase_group:
                ImeCoreManager.getCellConfig().addPhraseGroup("TestGroup");
                newGroupInfo = getPhraseGroupInfo();
                showResult(newGroupInfo);
                break;
            case R.id.btn_edit_phrase_group:
                ImeCoreManager.getCellConfig().editPhraseGroup(1, "TestGroupNewName");
                newGroupInfo = getPhraseGroupInfo();
                showResult(newGroupInfo);
                break;
            case R.id.btn_delete_phrase_group:
                ImeCoreManager.getCellConfig().deletePhraseGroup(1);
                newGroupInfo = getPhraseGroupInfo();
                showResult(newGroupInfo);
                break;
            
            case R.id.btn_enable_phrase_group:
                IptPhraseGroup group = ImeCoreManager.getCellConfig().getPhraseGroup(0);
                if (group != null) {
                    ImeCoreManager.getCellConfig().enablePhraseGroup(0, !group.isEnabled());
                    newGroupInfo = ImeCoreManager.getCellConfig().getPhraseGroup(0).toString();
                    showResult(newGroupInfo);
                }
                break;
            case R.id.btn_get_phrase_item_count:
                int count = ImeCoreManager.getCellConfig().getPhraseItemCount(1, null);
                showResult("个性短语数量：" + count);
                break;
            case R.id.btn_get_phrase_item:
                newItemInfo = getPhraseItemInfo(1);
                showResult(newItemInfo);
                break;
            case R.id.btn_add_phrase_item:
                ImeCoreManager.getCellConfig().addPhraseItem("hhh", "华华哈", 3, 1);
                newItemInfo = getPhraseItemInfo(1);
                showResult(newItemInfo);
                break;
            case R.id.btn_edit_phrase_item:
                ImeCoreManager.getCellConfig().editPhraseItem(1, "hhh", "华哈华", 3);
                newItemInfo = getPhraseItemInfo(1);
                showResult(newItemInfo);
                break;
            case R.id.btn_delete_phrase_item:
                ImeCoreManager.getCellConfig().deletePhraseItem(1);
                newItemInfo = getPhraseItemInfo(1);
                showResult(newItemInfo);
                break;
            case R.id.btn_aifont_load_model:
                String path = getActivity().getApplication().getFilesDir().getParent() + "/filesdict/hw7.bin";
                String licPath = getActivity().getApplication().getFilesDir().getParent() + "/filesdict/hw_lic7.bin";
                File file = new File(path);
                if (file.exists()) {
                    boolean result = ImeCoreManager.getPad().loadAiFontHwModel(getActivity(), path, licPath);
                    Logger.i("result：" + result + "  path：" + path);
                }
                break;
            case R.id.btn_aifont_unload_model:
                ImeCoreManager.getPad().unLoadAiFontHwModel();
                break;
            case R.id.btn_aifont_model_ver:
                int version = ImeCoreManager.getPad().aiFontHwModelVersion();
                Logger.i("" + version);
                break;
            case R.id.btn_rare_load:
                ImeCoreManager.getCellConfig().coreRefresh(ImeCoreConsts.ECORE_FILE_TYPE_CF_HANDWRITE, null);
                // ImeCoreManager.getCellConfig().coreRefresh(ImeCoreConsts.CFT_RARE_CHAIZI, null);
                Log.e("wentaoli", "onClick:  btn_rare_load");
                break;
            case R.id.btn_rare_unload:
                ImeCoreManager.getCellConfig().coreUnload(ImeCoreConsts.ECORE_FILE_TYPE_CF_HANDWRITE);
                // ImeCoreManager.getCellConfig().coreUnload(ImeCoreConsts.CFT_RARE_CHAIZI);
                Log.e("wentaoli", "onClick:  btn_rare_unload");
                break;
            case R.id.btn_rare_version:
                Log.e("wentaoli", "btn_rare_version: " + ImeCoreManager.getCellConfig().getHwRareVersion());
                Log.e("wentaoli", "log: " + ImeCoreManager.getLib().getTraceLog());
                break;
            case R.id.btn_aifont_points_score:
                StringBuilder stringBuilder3 = new StringBuilder();
                StringBuilder stringBuilder4 = new StringBuilder();
                try {
                    InputStream test3 = getActivity().getApplication().getAssets().open("test3.json");
                    InputStream test4 = getActivity().getApplication().getAssets().open("test4.json");
                    BufferedReader bufferedReader3 = new BufferedReader(new InputStreamReader(test3, "utf-8"));
                    BufferedReader bufferedReader4 = new BufferedReader(new InputStreamReader(test4, "utf-8"));
                    String line;
                    while ((line = bufferedReader3.readLine()) != null) {
                        stringBuilder3.append(line);
                    }
                    while ((line = bufferedReader4.readLine()) != null) {
                        stringBuilder4.append(line);
                    }
                    bufferedReader3.close();
                    bufferedReader4.close();
                } catch (IOException e) {
                    e.printStackTrace();
                    break;
                }
                TestAiFont testAiFontPoint3 = new Gson().fromJson(stringBuilder3.toString(), TestAiFont.class);
                TestAiFont testAiFontPoint4 = new Gson().fromJson(stringBuilder4.toString(), TestAiFont.class);
                int count3 = testAiFontPoint3.getPoints().size();
                int count4 = testAiFontPoint4.getPoints().size();
                int[] s3 = new int[count3];
                int[] s4 = new int[count4];
                for (int i = 0; i < count3; i++) {
                    s3[i] = testAiFontPoint3.getPoints().get(i);
                }
                for (int i = 0; i < count4; i++) {
                    s4[i] = testAiFontPoint4.getPoints().get(i);
                }
                int score3 = ImeCoreManager.getPad().aiFontRecoPoint(testAiFontPoint3.getCharacter(), s3);
                int score4 = ImeCoreManager.getPad().aiFontRecoPoint(testAiFontPoint4.getCharacter(), s4);
                Logger.i("score3：" + score3);
                Logger.i("score4：" + score4);
                break;
            case R.id.btn_aifont_generate_notify:
                ImeCoreManager.getPad().actAiFontGeneratedNotify(0, "这是一个测试的"
                        , "jbcdcd", "jbcdcd", 0);
                break;
            case R.id.btn_ai_card_data_encryption:
                StringBuilder stringBuilder = new StringBuilder();
                byte[] bytes = null;
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                String line;
                try {
                    InputStream aiCardRequestIs = getActivity().getApplication().getAssets().open(
                            "aicard_request.json");
                    BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(
                            aiCardRequestIs, "utf-8"));
                    int ch;
                    while ((ch = aiCardRequestIs.read()) > -1) {
                        baos.write(ch);
                    }
                    while ((line = bufferedReader.readLine()) != null) {
                        stringBuilder.append(line);
                    }
                    bytes = baos.toByteArray();
                    baos.close();
                    aiCardRequestIs.close();
                    bufferedReader.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
                String aiCardRequest  = stringBuilder.toString();
                // 需要结合主工程代码编译，单独运行会崩溃，原因：内核那不到主工程注册的StreamInfo
                byte[] result = ImeCoreManager.getPad().encryptionAICardRequestData( 0, bytes);
                break;
            case R.id.btn_ai_card_data_decrypt:
                String decrypt = "";
                ImeCoreManager.getPad().decryptAICardResponseData(0, encryption);
                break;
            case R.id.huawei_test:
                byte[] data = AssetsManager.load(getActivity(), "test_rule");
                String rule;
                if (data == null) {
                    rule = "fwUYzIiYTH_FTW-rVc5gp3gk7AmB6SP79GY4aTtN" +
                            "5TJzWGqYRftWO49NRDZiweJRf9HD2jTdliFLmGracN8LADZVGlPv_qx8nNmaLcXoqNLnsOQZtebf" +
                            "vtBCG5p2sbZ9il-h0SfyZMOyJpUP8wSjqnyIoIX7IcccB";
                } else {
                    rule = new String(data);
                }
                int test = ImeCoreManager.getCellConfig().dctCellInstallByBuffer(rule);
                Log.e("wentaoli", "dctCellInstallByBuffer: " + test);
                List<String> list = Arrays.asList("", "abc");
                for (String s : list) {
                    test = ImeCoreManager.getCellConfig().dctCellWordInDict(s);
                    Log.e("wentaoli", "dctCellWordInDict: " + test);
                    String value = ImeCoreManager.getCellConfig().dctCellExportBuffer(-1);
                    Log.e("wentaoli", "dctCellExportBuffer: " + value);
                    test = ImeCoreManager.getCellConfig().dctCellResetBuffer(-1);
                    Log.e("wentaoli", "dctCellResetBuffer: " + test);
                }
                break;
            default:
                break;
        }
    }

    private String getPhraseItemInfo(int groupId) {
        StringBuilder sb = new StringBuilder();
        int count = ImeCoreManager.getCellConfig().getPhraseItemCount(groupId, null);
        for (int i = 0; i < count; i++) {
            sb.append("【").append(i).append("】");
            IptPhraseItem item = ImeCoreManager.getCellConfig().getPhraseItem(i);
            sb.append(item);
            sb.append("\n");
        }
        return sb.toString();
    }

    private String getPhraseGroupInfo() {
        StringBuilder sb = new StringBuilder();
        int count = ImeCoreManager.getCellConfig().getPhraseGroupCount();
        for (int i = 0; i < count; i++) {
            sb.append("【").append(i).append("】");
            IptPhraseGroup group = ImeCoreManager.getCellConfig().getPhraseGroup(i);
            sb.append(group);
            sb.append("\n");
        }
        return sb.toString();
    }

    private void showResult(String message) {
        if (mConfigView != null) {
            Bundle bundle = new Bundle();
            bundle.putString(PlainTextFragment.BUNDLE_KEY_MESSAGE, message);
            mConfigView.jumpToFragment(PLAIN_TEXT_FRAGMENT, bundle);
        } else {
            Toast.makeText(getActivity(), message, Toast.LENGTH_SHORT).show();
        }
    }
}
