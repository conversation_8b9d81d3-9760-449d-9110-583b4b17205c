package com.baidu.iptandroiddemo.fragment;

import android.app.Fragment;
import android.os.Bundle;
import android.support.annotation.Nullable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ScrollView;
import android.widget.TextView;

/**
 * 展示文本的页面
 * 
 * Created by <PERSON><PERSON><PERSON><PERSON> on 18/2/3.
 */
public class PlainTextFragment extends Fragment {

    public static final String BUNDLE_KEY_MESSAGE = "message";

    private String mMessage;

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, Bundle savedInstanceState) {
        ScrollView scrollView = new ScrollView(container.getContext());
        TextView contentView = new TextView(container.getContext());
        contentView.setText(mMessage);
        scrollView.addView(contentView);
        return scrollView;
    }

    @Override
    public void setArguments(Bundle args) {
        super.setArguments(args);
        mMessage = args.getString(BUNDLE_KEY_MESSAGE);
    }
}
