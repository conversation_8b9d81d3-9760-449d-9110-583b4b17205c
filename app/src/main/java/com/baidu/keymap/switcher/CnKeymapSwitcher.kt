package com.baidu.keymap.switcher

import com.baidu.util.CommUtil

/**
 * 模拟输入法的中文切换

 * Created by <PERSON><PERSON><PERSON><PERSON> on 17/8/22.
 */
class CnKeymapSwitcher {
    
    fun switchInputType(inputType: InputStatConsts.InputType) {
        switchInputType(inputType, true)
    }
    
    fun switchInputType(inputType: InputStatConsts.InputType, needSwitchCore: Boolean) {
        InputStatMachine.instance.switchInputType(inputType)
        val padId = InputStatMachine.instance.nowKeyboard2PadId()
        if (CommUtil.mPadView != null) {
            CommUtil.mPadView.switchPad(padId, needSwitchCore)
        }
    }
    
    fun switchLayout(inputType: InputStatConsts.InputType, layout : InputStatConsts.LayoutType) {
        InputStatMachine.instance.switchLayout(inputType, layout)
        val padId = InputStatMachine.instance.nowKeyboard2PadId()
        if (CommUtil.mPadView != null) {
            CommUtil.mPadView.switchPad(padId, true)
        }
    }
    
    fun returnKeyboard() {
        var success = InputStatMachine.instance.returnKeyboard()
        if (success) {
            val padId = InputStatMachine.instance.nowKeyboard2PadId()
            if (CommUtil.mPadView != null) {
                CommUtil.mPadView.switchPad(padId, false)
            }
        }
    }
}
