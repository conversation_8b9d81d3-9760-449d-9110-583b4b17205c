package com.baidu.keymap.switcher

import com.baidu.iptcore.ImeCoreConsts

/**
 * 输入方式流转控制类
 * 
 * Created by <PERSON><PERSON><PERSON><PERSON> on 17/8/22.
 */
class InputStatMachine {
    
    companion object {
        val instance: InputStatMachine by lazy { InputStatMachine() }
    }

    /**
     * 当前的输入方式
     */
    private var mInputType: InputStatConsts.InputType? = null

    /**
     * 当前的布局。如果输入方式不支持布局，这里为null
     */
    private var mLayoutType: InputStatConsts.LayoutType? = null

    private var mLastInputType: InputStatConsts.InputType? = null
    private var mLastLayout: InputStatConsts.LayoutType? = null


    /**
     * 输入方式转换成padId，只对没有布局的输入方式有效，无效返回PAD_NONE
     */
    fun inputType2PadId(inputType: InputStatConsts.InputType?): Int {
        when (inputType) {
            InputStatConsts.InputType.BH -> return ImeCoreConsts.PAD_BH
            InputStatConsts.InputType.HW -> return ImeCoreConsts.PAD_HW
            InputStatConsts.InputType.RARE_HW -> return ImeCoreConsts.PAD_RARE_HW
            InputStatConsts.InputType.SYM -> return ImeCoreConsts.PAD_SYM
            InputStatConsts.InputType.MORE -> return ImeCoreConsts.PAD_MORE
            InputStatConsts.InputType.ZY -> return ImeCoreConsts.PAD_ZY
            InputStatConsts.InputType.CJ -> return ImeCoreConsts.PAD_CJ
            else -> return ImeCoreConsts.PAD_NONE
        }
    }

    /**
     * 输入方式和布局方式转换成padId
     */
    fun layoutType2PadId(inputType: InputStatConsts.InputType?, layoutType: InputStatConsts.LayoutType?): Int {
        when (layoutType) {
            InputStatConsts.LayoutType.KEYBOARD_26 -> {
                when (inputType) {
                    InputStatConsts.InputType.PY -> return ImeCoreConsts.PAD_PY26
                    InputStatConsts.InputType.EN -> return ImeCoreConsts.PAD_EN26
                    InputStatConsts.InputType.WB -> return ImeCoreConsts.PAD_WB26
                    InputStatConsts.InputType.NUM -> return ImeCoreConsts.PAD_123_26
                    else -> return ImeCoreConsts.PAD_NONE
                }
            }
            InputStatConsts.LayoutType.KEYBOARD_9 -> {
                when (inputType) {
                    InputStatConsts.InputType.PY -> return ImeCoreConsts.PAD_PY9
                    InputStatConsts.InputType.EN -> return ImeCoreConsts.PAD_EN9
                    InputStatConsts.InputType.WB -> return ImeCoreConsts.PAD_WB9
                    InputStatConsts.InputType.NUM -> return ImeCoreConsts.PAD_123_T9
                    else -> return ImeCoreConsts.PAD_NONE
                }
            }
            InputStatConsts.LayoutType.HW_FULL, InputStatConsts.LayoutType.HW_HALF -> {
                when (inputType) {
                    InputStatConsts.InputType.HW -> return ImeCoreConsts.PAD_HW
                    else -> return ImeCoreConsts.PAD_NONE
                }
            }
            else -> return ImeCoreConsts.PAD_NONE
        }
    }

    /**
     * 切换布局
     */
    fun switchLayout(inputType: InputStatConsts.InputType?, layout: InputStatConsts.LayoutType?) {
        when (inputType) {
            InputStatConsts.InputType.MORE, InputStatConsts.InputType.NUM, InputStatConsts.InputType.SYM -> {
                mLastInputType = mInputType
                mLastLayout = mLayoutType
            }
            else -> { }
        }
        mInputType = inputType
        mLayoutType = layout
    }


    /**
     * 切换输入方式
     */
    fun switchInputType(inputType: InputStatConsts.InputType) {
        when (inputType) {
            InputStatConsts.InputType.MORE, InputStatConsts.InputType.NUM, InputStatConsts.InputType.SYM -> {
                mLastInputType = mInputType
                mLastLayout = mLayoutType
            }
            else -> { }
        }
        mInputType = inputType
        mLayoutType = null
    }
    
    fun returnKeyboard(): Boolean {
        if (mLastInputType == null) {
            return false
        }
        switchLayout(mLastInputType, mLastLayout)
        mLastInputType = null
        mLastLayout = null
        return true
    }

    /**
     * 当前面板映射到内核的padId
     */
    fun nowKeyboard2PadId(): Int {
        if (mLayoutType == null) {
            return inputType2PadId(mInputType)
        } else {
            return layoutType2PadId(mInputType, mLayoutType)
        }
    }
}