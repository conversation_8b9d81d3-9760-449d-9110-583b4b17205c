/*
 * Copyright (C) 2017 Baidu, Inc. All Rights Reserved.
 */
package com.baidu.pad;

import com.baidu.input.support.state.IptCandState;
import com.baidu.iptcore.info.IptCoreCandInfo;
import com.baidu.pad.bean.PadCand;
import com.baidu.pop.InputTypeSelectListener;
import com.baidu.pop.MoreCandFragment;
import com.baidu.util.CommUtil;
import com.baidu.util.DrawUtil;
import com.baidu.util.PopServiceWrapper;
import com.citrus.popinn.PopHandle;

import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Rect;

/**
 * 候选条的绘制处理器
 * Created by ch<PERSON><PERSON><PERSON> on 17/4/10.
 */
public class CandHandler {

    private static final int HIT_TYPE_NONE = -1;
    private static final int HIT_TYPE_CAND_WORD = 0;
    private static final int HIT_TYPE_CAND_MORE = 1;
    private static final int HIT_TYPE_CAND_INPUT_TYPE = 2;
    
    /**
     * cand候选字的数组
     */
    private PadCand[] mCandList = new PadCand[64];
    /**
     * 当前的cand数量
     */
    private int mCandNum = 0;
    
    /** cand区域的宽度 */
    private int mWidth = 0;
    /** 当前候选词可绘制的左边界 */
    private int mCandOff = 0;
    /** cand区域的高度 */
    private int mHeight = 0;
    /** 候选词直接的padding值 */
    private int mCandSpan = 0;
    /** 绘制的画笔 */
    private Paint mPaint = new Paint();
    /** 候选字大小 */
    private int mCandFontSize = 0;
    /** 有候选条时候的右边距 */
    private int mRightPadding;

    /**
     * 点击的index
     */
    private CandAction mCandAction = new CandAction();
    /**
     * 更多icon的区域
     */
    private Rect mRectIconMore = new Rect();
    /**
     * 选择输入方式icon
     */
    private Rect mRectIconInputType = new Rect();

    class CandAction {
        int mHitType = HIT_TYPE_NONE;
        int mHitIdx = -1;
        
        void set(int hitType, int hitIdx) {
            mHitType = hitType;
            mHitIdx = hitIdx;
        }

        void set(int hitType) {
            mHitType = hitType;
            mHitIdx = -1;
        }

        public void reset() {
            mHitType = HIT_TYPE_NONE;
            mHitIdx = -1;
        }

        public boolean matches(int hitType, int hitIdx) {
            return mHitType == hitType && mHitIdx == hitIdx;
        }

        public boolean matches(int hitType) {
            return matches(hitType, -1);
        }
    }


    public CandHandler() {
        mPaint.setColor(Color.BLACK);
        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setAntiAlias(true);
        mPaint.setFilterBitmap(true);
        mPaint.setStrokeWidth(2);
    }

    /**
     * 更新候选字列表的UI属性
     * @param candStore 封装内核出的cand候选词的数据模型
     */
    public void updateCand(IptCandState candStore) {

        int candWidth = 0;
        PadCand padCand;
        IptCoreCandInfo candInfo;
        mCandNum = 0;
        for (int i = 0; i < candStore.getCandCount(); i++) {
            candInfo = candStore.getCandAt(i);
            padCand = new PadCand(i, candInfo);

            padCand.measure(mWidth - mRightPadding, mHeight - mCandOff);
            int textWidth = padCand.getMeasuredWidth();
            textWidth += mCandSpan * 2;
            if (candWidth + textWidth < mWidth - mRightPadding) {
                padCand.layout(candWidth, mCandOff, candWidth + textWidth, mCandOff + mHeight);
                mCandList[mCandNum++] = padCand;
                candWidth += textWidth;
            } else {
                break;
            }
        }
        updateUI();
    }

    private void updateUI() {
        CommUtil.mPadView.invalidate();
    }

    /**
     * 更新Cand区域的大小
     * @param width 宽度
     * @param height 高度
     */
    public void updateSize(int width, int height) {
        mWidth = width;
        mHeight = height;
        mCandSpan = width / 36;
        mCandFontSize = mHeight * 17 / 40;
        mRightPadding = mWidth / 10;
        mRectIconMore.set(mWidth - mRightPadding, 0, mWidth, mHeight);

        int iconWidth = mWidth / 10;
        mRectIconInputType.set(0, 0, iconWidth, mHeight);
    }

    // ///////////////////////////////////////////////////////////////
    // ---------- touch事件处理 ----------
    // ///////////////////////////////////////////////////////////////
    
    /**
     * 检测点击事件
     * @param x 点击坐标
     * @param y 点击坐标
     * @return 点击的候选字索引
     */
    public int detectHit(int x, int y) {
        int hitIdx = -1;
        if (mCandNum > 0) {
            for (int i = 0; i < mCandNum; i++) {
                PadCand cand = mCandList[i];
                if (cand.contains(x, y)) {
                    cand.setPressed(true);
                    hitIdx = i;
                    break;
                }
            }
            if (hitIdx >= 0) {
                mCandAction.set(HIT_TYPE_CAND_WORD, hitIdx);
            } else {
                if (mRectIconMore.contains(x, y)) {
                    mCandAction.set(HIT_TYPE_CAND_MORE);
                } else {
                    mCandAction.reset();
                }
            }
        } else {
            if (mRectIconInputType.contains(x, y)) {
                mCandAction.set(HIT_TYPE_CAND_INPUT_TYPE);
            } else {
                mCandAction.reset();
            }
        }

        if (!mCandAction.matches(HIT_TYPE_NONE)) {
            if (CommUtil.mPadView != null) {
                CommUtil.mPadView.invalidate();
            }
        }
        return hitIdx;
    }

    /**
     * 执行cand上的touch事件
     */
    public void executeCandAction() {
        switch (mCandAction.mHitType) {
            case HIT_TYPE_CAND_WORD:
                PadCand padCand = getCandAt(mCandAction.mHitIdx);
                if (padCand != null) {
                    padCand.doAction();
                }
                break;
            case HIT_TYPE_CAND_MORE:

                if (CommUtil.sMoreCandFragment == null) {
                    CommUtil.sMoreCandFragment = new MoreCandFragment();
                }
                PopServiceWrapper.showMoreCandView(CommUtil.sMoreCandFragment);
                break;
            case HIT_TYPE_CAND_INPUT_TYPE:
                // 展示输入方式选择浮层
                InputTypeSelectListener listener = new InputTypeSelectListener();
                PopHandle handle = PopServiceWrapper.showInputTypeSelect(listener);
                listener.setHandle(handle);
                break;
        }
        mCandAction.reset();
    }

    /**
     * 根据索引获取点击的cand信息
     * @param index 索引
     * @return cand信息
     */
    public PadCand getCandAt(int index) {
        if (index < 0 || index >= mCandList.length) {
            return null;
        }
        return mCandList[index];
    }

    // ///////////////////////////////////////////////////////////////
    // ---------- 绘制相关 ----------
    // ///////////////////////////////////////////////////////////////

    /**
     * 绘制候选字
     * @param canvas 画布
     */
    protected void paintCand(Canvas canvas) {

        mPaint.setColor(Color.WHITE);
        mPaint.setStyle(Paint.Style.STROKE);

        canvas.drawRect(0, mCandOff + 1, mWidth, mCandOff + mHeight, mPaint);

        if (mCandNum > 0) {
            drawCandOnStatus(canvas);
        } else {
            drawCandOffStatus(canvas);
        }
    }

    /**
     * 绘制没有候选字时候的状态
     * @param canvas 画布
     */
    private void drawCandOffStatus(Canvas canvas) {
        if (mCandAction.matches(HIT_TYPE_CAND_INPUT_TYPE)) {
            drawPressedBack(canvas, mRectIconInputType);
        }
        DrawUtil.drawText(canvas, mPaint, "☆", mRectIconInputType, mCandFontSize);
        
    }

    /**
     * 绘制有候选字时候的状态
     *
     * @param canvas 画布
     */
    private void drawCandOnStatus(Canvas canvas) {
        for (int i = 0; i < mCandNum; i++) {
            PadCand cand = mCandList[i];
            cand.draw(canvas);
        }

        if (mCandAction.matches(HIT_TYPE_CAND_MORE)) {
            drawPressedBack(canvas, mRectIconMore);
        }
        DrawUtil.drawText(canvas, mPaint, "▽", mRectIconMore, mCandFontSize);
    }

    private void drawPressedBack(Canvas canvas, Rect rect) {
        drawPressedBack(canvas, rect.left, rect.top, rect.right, rect.bottom);
    }

    private void drawPressedBack(Canvas canvas, int l, int t, int r, int b) {
        mPaint.setColor(Color.rgb(188, 188, 255));
        mPaint.setStyle(Paint.Style.FILL);
        canvas.drawRect(l, t, r, b, mPaint);
        mPaint.setColor(Color.WHITE);
        mPaint.setStyle(Paint.Style.STROKE);
    }
    
    public void hide() {
        // do nothing
    }
}
