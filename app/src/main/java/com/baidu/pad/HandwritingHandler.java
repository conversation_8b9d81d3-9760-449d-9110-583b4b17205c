package com.baidu.pad;

import java.util.ArrayList;
import java.util.List;

import com.baidu.iptandroiddemo.R;
import com.baidu.pad.action.ImeAction;
import com.baidu.util.CommUtil;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Rect;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.util.Pair;
import android.view.MotionEvent;

/**
 * 处理手写的绘制器
 */
class HandwritingHandler {
    private static final int DELAY_WAIT = 800;
    private static final int MSG_CLEAR_POINT = 0;
    
    private final Paint mPaint;
    private Rect mRectBounds = new Rect();

    private List<HwTrack> mTrackLines = new ArrayList<>();

    private boolean mIsDownInBounds = false;

    private Handler mHandler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case MSG_CLEAR_POINT:
                    clearTrack();
                    break;
                default:
                    break;
            }
        }
    };

    /**
     * 构造器
     */
    public HandwritingHandler(Context context) {
        mPaint = new Paint();
        mPaint.setAntiAlias(true);
        mPaint.setColor(Color.RED);

        mPaint.setStrokeWidth(context.getResources().getDimensionPixelSize(R.dimen.handwriting_width));
    }

    /**
     * 更新大小
     */
    public void updateSize(int left, int top, int right, int bottom) {
        mRectBounds.set(left, top, right, bottom);
    }

    /**
     * 通知不展示
     */
    public void hide() {
        mRectBounds.setEmpty();
    }

    /**
     * 处理触摸事件
     */
    public boolean onTouchEvent(MotionEvent event) {

        int x = (int) event.getX();
        int y = (int) event.getY();

        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                if (mRectBounds.contains(x, y)) {
                    mIsDownInBounds = true;
                    if (mHandler.hasMessages(MSG_CLEAR_POINT)) {
                        mHandler.removeMessages(MSG_CLEAR_POINT);
                    }
                    startTrack((int) event.getX(), (int) event.getY());
                    updateUI();
                }

                return mIsDownInBounds;
            case MotionEvent.ACTION_MOVE:
                if (mIsDownInBounds) {
                    ImeAction.dispatch(ImeAction.HW_TRACK_MOVE, x, y);
                    appendPoint((int) event.getX(), (int) event.getY());
                    updateUI();
                    return true;
                }
                break;
            case MotionEvent.ACTION_UP:
                if (mIsDownInBounds) {
                    mIsDownInBounds = false;
                    endTrack((int) event.getX(), (int) event.getY());
                    mHandler.sendEmptyMessageDelayed(MSG_CLEAR_POINT, DELAY_WAIT);
                    updateUI();
                    return true;
                }
                break;
            case MotionEvent.ACTION_CANCEL:
                mIsDownInBounds = false;
            default:
                break;
        }

        return false;
    }

    /**
     * 绘制
     */
    public void draw(Canvas canvas) {

        for (HwTrack track : mTrackLines) {
            track.draw(canvas, mPaint);
        }
    }

    /**
     * 更新UI
     */
    private void updateUI() {
        if (CommUtil.mPadView != null) {
            CommUtil.mPadView.invalidate();
        }
    }

    /**
     * 开始一条轨迹线
     */
    private void startTrack(int x, int y) {
        ImeAction.dispatch(ImeAction.HW_START_TRACK, x, y);
        HwTrack track = new HwTrack();
        track.appendPoint(x, y);
        mTrackLines.add(track);
    }

    /**
     * 清除轨迹
     */
    private void clearTrack() {
        mTrackLines.clear();
        updateUI();
    }

    /**
     * 添加轨迹点
     */
    private void appendPoint(int x, int y) {
        if (mTrackLines.size() == 0) {
            return;
        }
        mTrackLines.get(mTrackLines.size() - 1).appendPoint(x, y);
    }

    /**
     * 结束一条轨迹
     */
    private void endTrack(int x, int y) {
        appendPoint(x, y);
        ImeAction.dispatch(ImeAction.HW_END_TRACK, x, y);
    }
    
    /**
     * 一条手写轨迹的封装
     */
    class HwTrack {
        private List<Pair<Integer, Integer>> mPoints = new ArrayList<>();

        public void appendPoint(int x, int y) {
            mPoints.add(new Pair<>(x, y));
        }

        public void draw(Canvas canvas, Paint paint) {
            int startX;
            int startY;
            int endX;
            int endY;
            for (int i = 0; i < mPoints.size() - 1; i++) {
                startX = mPoints.get(i).first;
                startY = mPoints.get(i).second;
                endX = mPoints.get(i + 1).first;
                endY = mPoints.get(i + 1).second;
                canvas.drawLine(startX, startY, endX, endY, paint);
            }
        }
    }
}
