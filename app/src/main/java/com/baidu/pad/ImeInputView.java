package com.baidu.pad;

import java.util.List;

import com.baidu.input.support.state.InputState;
import com.baidu.iptandroiddemo.Global;
import com.baidu.iptandroiddemo.R;
import com.baidu.pad.action.ImeAction;
import com.baidu.pad.bean.PadIcon;
import com.baidu.util.CommUtil;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Paint.Style;
import android.text.TextUtils;
import android.util.Pair;
import android.view.MotionEvent;
import android.widget.LinearLayout;

/**
 * 承载input区的绘制，和二次元区域的绘制
 */
public class ImeInputView extends LinearLayout implements PadIcon.IconClickListener {

    private static final int ICON_CURSOR_LEFT = 0;
    private static final int ICON_CURSOR_RIGHT = 1;
    private static final int ICON_CURSOR_POP = 2;
    
    private int mWidth = 0;
    private int mHeight = 0;
    private int mFontSize = 0;
    private Paint mPaint = new Paint();
    private boolean mIsInEditMode = false;
    
    private PadIcon mCursorLeftIcon;
    private PadIcon mCursorRightIcon;
    private PadIcon mPopIcon;
    
    private InputState mInputBarStore;

    public ImeInputView(Context context) {
        super(context);
        this.setBackgroundColor(0xff808080);
        
        mPaint.setColor(Color.WHITE);
        mPaint.setStyle(Style.STROKE);
        mPaint.setAntiAlias(true);
        mPaint.setFilterBitmap(true);
        mPaint.setStrokeWidth(1);
        
        updateSize(0);
    }
    
    public void reset() {
        mWidth = 0;
        mIsInEditMode = false;
        requestLayout();
    }

    /**
     * 更新Input区的内容
     */
    public void update(InputState inputBarStore) {
        mInputBarStore = inputBarStore;

        int width =
                mIsInEditMode ? CommUtil.mScreenWidth : measureInputBarInNonEditMode(mInputBarStore.getInputString());
        if (width != mWidth) {
            updateSize(width);
            requestLayout();
        } else {
            invalidate();
        }

        if (mWidth > 0) {
            Global.getImeService().setCandidatesViewShown(true);
        } else {
            Global.getImeService().setCandidatesViewShown(false);
        }
    }

    /**
     * 测量非编辑模式下input需要显示的宽度
     */
    private int measureInputBarInNonEditMode(String inputShow) {
        return inputShow == null ? 0 : (int) mPaint.measureText(inputShow);
    }

    private void updateEditBounds() {
        mWidth = CommUtil.mScreenWidth;
        mHeight = 25 * mWidth / 360;
        mFontSize = mWidth * 15 / 360;
        mPaint.setTextSize(mFontSize);
        
        // 初始化操作icon
        int width = mHeight;
        int left = mWidth - width * 3;
        if (mCursorLeftIcon == null) {
            mCursorLeftIcon = new PadIcon("<", ICON_CURSOR_LEFT);
            mCursorLeftIcon.layout(left, 0, left + width, mHeight);
            mCursorLeftIcon.setListener(this);
        }
        if (mCursorRightIcon == null) {
            mCursorRightIcon = new PadIcon(">", ICON_CURSOR_RIGHT);
            mCursorRightIcon.layout(left + width, 0, left + 2 * width, mHeight);
            mCursorRightIcon.setListener(this);
        }
        if (mPopIcon == null) {
            mPopIcon = new PadIcon("POP", ICON_CURSOR_POP);
            mPopIcon.layout(left + 2 * width, 0, left + 3 * width, mHeight);
            mPopIcon.setListener(this);
        }
        ImeAction.dispatch(ImeAction.INPUT_CURSOR, 0);
    }

    private void updateSize(int textWidth) {
        mWidth = 0;
        mHeight = 0;

        int width = 0;
        if (CommUtil.mScreenHeight > CommUtil.mScreenWidth) {
            width = CommUtil.mScreenWidth;
        } else {
            width = CommUtil.mScreenHeight;
        }

        mHeight = 25 * width / 360;
        mFontSize = width * 15 / 360;
        mPaint.setTextSize(mFontSize);

        if (textWidth > 0) {
            mWidth = textWidth + (width / 72);
        }
    }
    
    @Override
    protected void onDraw(Canvas canvas) {
        drawInputShow(canvas);
        if (mIsInEditMode) {
            if (mCursorLeftIcon != null) {
                mCursorLeftIcon.draw(canvas);
            }
            if (mCursorRightIcon != null) {
                mCursorRightIcon.draw(canvas);
            }
            if (mPopIcon != null) {
                mPopIcon.draw(canvas);
            }
        }
    }

    private void drawInputShow(Canvas canvas) {
        // 绘制边框
        canvas.drawLine(0, 0, mWidth, 0, mPaint);
        canvas.drawLine(0, 0, 0, mHeight, mPaint);
        canvas.drawLine(mWidth, 0, mWidth, mHeight, mPaint);

        List<Pair<Integer, String>> mSpannableStrs = InputState.splitInputInfoString(mInputBarStore);
        if (mSpannableStrs != null) {
            int startX = 0;
            for (Pair<Integer, String> span : mSpannableStrs) {
                startX += drawSpan(canvas, span.first, span.second, startX, 0);
            }
        }

        // 绘制光标
        if (mIsInEditMode) {
            mPaint.setColor(Color.WHITE);
            mPaint.setStrokeWidth(3);
            String inputBeforeCursor = mInputBarStore.getInputStringBeforeCursor();
            if (TextUtils.isEmpty(inputBeforeCursor)) {
                canvas.drawLine(0, 0, 0, mHeight, mPaint);
            } else {
                int offX = (int) mPaint.measureText(inputBeforeCursor);
                canvas.drawLine(offX, 0, offX, mHeight, mPaint);
            }
        }
    }

    private int drawSpan(Canvas canvas, Integer type, String str, int xOffset, int yOffset) {
        mPaint.setTextSize(mFontSize);
        int width = (int) mPaint.measureText(str);
        switch (type) {
            case InputState.SPAN_TYPE_COMMIT_HZ:
                mPaint.setColor(getResources().getColor(R.color.input_commit_hz));
                drawText(canvas, str, xOffset, yOffset, xOffset + width, mHeight, mFontSize);
                break;
            case InputState.SPAN_TYPE_POP_REGION:
                mPaint.setColor(getResources().getColor(R.color.input_pop_background));
                mPaint.setStyle(Style.FILL);
                canvas.drawRect(xOffset, yOffset, xOffset + width, mHeight, mPaint);
                mPaint.setColor(getResources().getColor(R.color.input_commit_hz));
                drawText(canvas, str, xOffset, yOffset, xOffset + width, mHeight, mFontSize);
                break;
            case InputState.SPAN_TYPE_COMMIT_LIST:
                mPaint.setColor(getResources().getColor(R.color.input_commit_list));
                drawText(canvas, str, xOffset, yOffset, xOffset + width, mHeight, mFontSize);
                break;
            case InputState.SPAN_TYPE_UNCOMMIT_INPUT:
                mPaint.setColor(Color.WHITE);
                drawText(canvas, str, xOffset, yOffset, xOffset + width, mHeight, mFontSize);
                break;
            default:
                break;
        }
        return width;
    }

    protected void drawText(Canvas canvas, String txt, int tlX, int tlY,
            int brX, int brY, int fontSize) {
        float x = (tlX + brX) / 2;
        float y = (tlY + brY) / 2;

        mPaint.setTextSize(fontSize);
        mPaint.setStyle(Style.FILL);
        float textWidth = mPaint.measureText(txt);
        x = x - textWidth / 2;
        y = y + fontSize / 2;

        canvas.drawText(txt, x, y, mPaint);
        mPaint.setStyle(Style.STROKE);
    }
    
    @Override
    protected void onMeasure(int width, int height) {
        setMeasuredDimension(mWidth, mHeight);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        
        if (mIsInEditMode) {
            if (mCursorLeftIcon != null) {
                if (mCursorLeftIcon.onTouchEvent(event)) {
                    invalidate();
                    return true;
                }
            }
            if (mCursorRightIcon != null) {
                if (mCursorRightIcon.onTouchEvent(event)) {
                    invalidate();
                    return true;
                }
            }
            if (mPopIcon != null) {
                if (mPopIcon.onTouchEvent(event)) {
                    invalidate();
                    return true;
                }
            }
        }
        
        int action = event.getAction();
        switch (action) {
            case MotionEvent.ACTION_DOWN:
                break;
            case MotionEvent.ACTION_MOVE:
                break;
            case MotionEvent.ACTION_UP:
                if (mIsInEditMode) {
                    mIsInEditMode = false;
                    updateSize((int) mPaint.measureText(mInputBarStore.getInputString()));
                    requestLayout();
                } else {
                    mIsInEditMode = true;
                    updateEditBounds();
                    requestLayout();
                }
                break;
            default:
                break;
        }
        
        return true;
    }

    @Override
    public void onIconClicked(PadIcon bean) {
        int cursorIdx;
        switch (bean.getId()) {
            case ICON_CURSOR_LEFT:
                cursorIdx = mInputBarStore.getCursorIdx();
                ImeAction.dispatch(ImeAction.INPUT_CURSOR, cursorIdx - 1);
                break;
            case ICON_CURSOR_RIGHT:
                cursorIdx = mInputBarStore.getCursorIdx();
                ImeAction.dispatch(ImeAction.INPUT_CURSOR, cursorIdx + 1);
                break;
            case ICON_CURSOR_POP:
                ImeAction.dispatch(ImeAction.INPUT_POP);
                break;
            default:
                break;
        }
    }
}
