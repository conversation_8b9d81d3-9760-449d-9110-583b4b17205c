package com.baidu.pad;

import com.baidu.input.support.state.IptCandState;
import com.baidu.input.support.state.IptListState;
import com.baidu.iptandroiddemo.R;
import com.baidu.iptcore.ImeCoreConsts;
import com.baidu.iptcore.ImeCoreManager;
import com.baidu.iptcore.info.IptCoreCandInfo;
import com.baidu.keymap.switcher.InputStatConsts;
import com.baidu.keymap.switcher.InputStatMachine;
import com.baidu.keymap.switcher.KeymapSwitcherFactory;
import com.baidu.pad.action.ImeAction;
import com.baidu.pad.bean.PadKey;
import com.baidu.pad.skin.KeymapArranger;
import com.baidu.pop.SymbolPanelFragment;
import com.baidu.util.CommUtil;
import com.baidu.util.Consts;
import com.baidu.util.PopServiceWrapper;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Canvas;
import android.support.annotation.NonNull;
import android.text.TextUtils;
import android.view.MotionEvent;
import android.view.View;
import android.widget.Toast;

/**
 * 对应输入法的SoftKeyboardView和CandidateView
 */
public class ImePadView extends View implements PadKey.KeyTouchListener {

    private int mWidth = 0;
    private int mHeight = 0;
    private int mCandOff = 0;
    private int mCandHei = 0;
    private int mPadOff = 0;
    private int mPadHei = 0;

    private PadKey[] mKeyList = new PadKey[64];
    private int mKeyNum = 0;
    
    private HitType mHitType = HitType.HitTypeNone;
    private int mHitIdx = -1;
    private int mDownX = -1;
    private int mDownY = -1;
    
    /**
     * 处理候选条的绘制
     */
    private CandHandler mCandHandler = new CandHandler();
    /**
     * 处理List的绘制
     */
    private ListHandler mListHandler = new ListHandler(getContext());
    /**
     * 处理手写的绘制器
     */
    private HandwritingHandler mHandwritingHandler = new HandwritingHandler(getContext());

    @Override
    public void onKeyTouched(@NonNull PadKey key, int dragWay) {
        int keyId;
        switch (dragWay) {
            case ImeCoreConsts.INPUTTYPE_CLICK:
                onKeyClicked(key);
                break;
            case ImeCoreConsts.INPUTTYPE_LEFT:
                keyId = key.getLeftKeyId();
                if (keyId != 0) {
                    ImeAction.dispatch(ImeAction.KEY_DRAG_L, keyId);
                }
                break;
            case ImeCoreConsts.INPUTTYPE_RIGHT:
                keyId = key.getRightKeyId();
                if (keyId != 0) {
                    ImeAction.dispatch(ImeAction.KEY_DRAG_R, keyId);
                }
                break;
            case ImeCoreConsts.INPUTTYPE_UP:
                keyId = key.getUpKeyId();
                if (keyId != 0) {
                    ImeAction.dispatch(ImeAction.KEY_DRAG_U, keyId);
                }
                break;
            case ImeCoreConsts.INPUTTYPE_DOWN:
                keyId = key.getDownKeyId();
                if (keyId != 0) {
                    ImeAction.dispatch(ImeAction.KEY_DRAG_U, keyId);
                }
                break;
            default:
                break;
        }
    }
    
    private void onKeyClicked(PadKey key) {
        if (key != null) {
            int hitKeyId = key.getKeyId();
            if (hitKeyId == ImeCoreConsts.FKEY_ABC) { // ABC 功能键
                KeymapSwitcherFactory.Companion.getCn().switchLayout(InputStatConsts.InputType.EN,
                        InputStatConsts.LayoutType.KEYBOARD_26);

            } else if (hitKeyId == ImeCoreConsts.FKEY_123) {
                KeymapSwitcherFactory.Companion.getCn().switchLayout(InputStatConsts.InputType.NUM,
                        InputStatConsts.LayoutType.KEYBOARD_9);
            } else if ((hitKeyId == ImeCoreConsts.FKEY_SYM)) {
                
                KeymapSwitcherFactory.Companion.getCn().switchInputType(InputStatConsts.InputType.SYM, false);
                ImeAction.dispatch(ImeAction.KEY_CLICK, ImeCoreConsts.FKEY_SYM);
            } else if ((hitKeyId == ImeCoreConsts.FKEY_CN)) {
                KeymapSwitcherFactory.Companion.getCn().switchLayout(InputStatConsts.InputType.PY,
                        InputStatConsts.LayoutType.KEYBOARD_26);

            } else if (hitKeyId == ImeCoreConsts.FKEY_RETURN) {
                KeymapSwitcherFactory.Companion.getCn().returnKeyboard();
                ImeAction.dispatch(ImeAction.KEY_CLICK, hitKeyId);
            } else if (hitKeyId == Consts.FKEY_QUICK_CJ) {
                ImeAction.dispatch(ImeAction.KEY_CLICK, ImeCoreConsts.FKEY_SHIFT_CAPLOCK); // 速成仓颉按键传给内核的是shift键
            } else {
                ImeAction.dispatch(ImeAction.KEY_CLICK, hitKeyId);
            }
        }
    }

    enum HitType {
        HitTypeNone, HitTypeCand, 
    }

    enum DirectType {
        DirectTypeNone, DirectTypeUp, DirectTypeDown, DirectTypeLeft, DirectTypeRight
    }

    public ImePadView(Context context) {
        super(context);
        this.setBackgroundColor(context.getResources().getColor(R.color.panel_background_color));

        updateSize();
    }

    public void reset() {
        // TODO UI是否需要reset?
    }

    /**
     * 切换面板，包括显示状态的切换和内核切换
     * @param padId 如{@link ImeCoreConsts#PAD_PY26}
     */
    public void switchPad(int padId, boolean toSwitchCorePad) {
        switchOut();
        
        switch(padId) {
            case ImeCoreConsts.PAD_PY26:
                switchPy26();
                break;
            case ImeCoreConsts.PAD_EN26:
                switchEn26();
                break;
            case ImeCoreConsts.PAD_PY9:
                switchPy9();
                break;
            case ImeCoreConsts.PAD_EN9:
                switchEn9();
                break;
            case ImeCoreConsts.PAD_RARE_HW:
            case ImeCoreConsts.PAD_HW:
                switchHw();
                break;
            case ImeCoreConsts.PAD_BH:
                switchBh();
                break;
            case ImeCoreConsts.PAD_WB9:
                switchWb9();
                break;
            case ImeCoreConsts.PAD_WB26:
                switchWb26();
                break;
            case ImeCoreConsts.PAD_123_T9:
                switchNum();
                break;
            case ImeCoreConsts.PAD_ZY:
                switchZhuyin();
                break;
            case ImeCoreConsts.PAD_CJ:
            case ImeCoreConsts.PAD_SC:
                switchCangjie();
                break;
            case ImeCoreConsts.PAD_SYM:
                switchSym();
                break;
            default:
                break;
        }
        if (toSwitchCorePad) {
            if (padId == ImeCoreConsts.PAD_SYM) {
                ImeAction.dispatch(ImeAction.KEY_CLICK, ImeCoreConsts.FKEY_SYM);
            } else if (padId == ImeCoreConsts.PAD_123_T9) {
                ImeAction.dispatch(ImeAction.KEY_CLICK, ImeCoreConsts.FKEY_123);
            } else {
                ImeAction.dispatch(ImeAction.SWITCH_KEYBOARD, padId);
            }
        }
        invalidate();
    }

    private void switchOut() {
        mListHandler.hide();
        mHandwritingHandler.hide();
    }

    @Override
    protected void onDraw(Canvas canvas) {
        mCandHandler.paintCand(canvas);
        paintAllkey(canvas);
    }

    @SuppressLint("ClickableViewAccessibility")
    @Override
    public boolean onTouchEvent(MotionEvent ev) {
        
        // 先派发给sublist
        if (mListHandler.onTouchEvent(ev)) {
            return true;
        }

        // 再派发给手写区域
        if (mHandwritingHandler.onTouchEvent(ev)) {
            return true;
        }

        if (onKeymapTouchEvent(ev)) {
            invalidate();
            return true;
        }
        
        int action = ev.getAction();
        HitType oldHitType = mHitType;
        int oldHitIdx = mHitIdx;

        boolean response = false;
        int x = (int) ev.getX();
        int y = (int) ev.getY();

        switch (action) {
            case MotionEvent.ACTION_DOWN: {
                response = true;
                hitTest(x, y);
                if (mHitType != HitType.HitTypeNone && mHitIdx != -1) {
                    invalidate();
                }

                mDownX = x;
                mDownY = y;

                break;
            }
            case MotionEvent.ACTION_MOVE: {
                if (oldHitIdx != -1) {
                    hitTest(x, y);
                    if (mHitType != oldHitType || mHitIdx != oldHitIdx) {
                        mHitType = oldHitType;
                        mHitIdx = -1;
                        invalidate();
                    }
                }

                response = true;
                break;
            }
            case MotionEvent.ACTION_UP: {
                response = true;

                if (oldHitType != HitType.HitTypeNone) {
                    hitTest(x, y);
                    if (mHitType == oldHitType && mHitIdx == oldHitIdx) {
                        if (mHitType == HitType.HitTypeCand) {
                            mCandHandler.executeCandAction();
                        } 
                    } else {
                        DirectType directType = directTest(oldHitType, x, y);
                        if (directType != DirectType.DirectTypeNone) {
                            directEntry(directType, oldHitType);
                        }
                    }
                }

                mDownX = -1;
                mDownY = -1;
                mHitType = HitType.HitTypeNone;
                mHitIdx = -1;

                if (oldHitType != HitType.HitTypeNone && oldHitIdx != -1) {
                    invalidate();
                }

                break;
            }
            case MotionEvent.ACTION_CANCEL: {
                response = true;

                mDownX = -1;
                mDownY = -1;
                mHitType = HitType.HitTypeNone;
                mHitIdx = -1;

                break;
            }
        }

        return response;
    }

    /**
     * 处理面板所有按键的touch事件
     * @param event touch事件
     * @return 是否处理过
     */
    private boolean onKeymapTouchEvent(MotionEvent event) {
        for (int i = 0; i < mKeyNum; i++) {
            PadKey key = mKeyList[i];
            if (key.onTouchEvent(event)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检测用户操作的区域，赋值给mHitType
     * @param x 触摸点x
     * @param y 触摸点y
     */
    protected void hitTest(int x, int y) {

        mHitType = HitType.HitTypeNone;
        mHitIdx = -1;

        if (y < mCandHei) {
            mHitType = HitType.HitTypeCand;
            mHitIdx = mCandHandler.detectHit(x, y);
        }
    }

    protected DirectType directTest(HitType hitType, int x, int y) {

        DirectType directType = DirectType.DirectTypeNone;
        int disX = x - mDownX;
        int disY = y - mDownY;

        int absDisX = Math.abs(disX);
        int absDisY = Math.abs(disY);

        switch (hitType) {
            case HitTypeCand: {

                if (absDisY < mCandHei * 2 && absDisX * 2 > mWidth) {
                    if (disX > 0) {
                        directType = DirectType.DirectTypeRight;
                    } else {
                        directType = DirectType.DirectTypeLeft;
                    }
                }

                break;
            }
            default: {
                break;
            }
        }

        return directType;
    }
    
    protected void directEntry(DirectType directType, HitType hitType) {

    }
    
    protected void paintAllkey(Canvas canvas) {
        for (int i = 0; i < mKeyNum; ++i) {
            PadKey tkey = mKeyList[i];
            tkey.draw(canvas);
            
            // 特殊区域处理
            if (tkey.getKeyId() == Consts.FKEY_SUBLIST) {
                mListHandler.draw(canvas);
            }

            if (tkey.getKeyId() == Consts.FKEY_HANDWRITING) {
                mHandwritingHandler.draw(canvas);
            }
        }
    }

    @Override
    protected void onLayout(boolean changed, int l, int t, int r, int b) {
        super.onLayout(changed, l, t, r, b);
    }

    @Override
    protected void onMeasure(int width, int height) {

        setMeasuredDimension(mWidth, mHeight);
    }

    protected void updateSize() {

        mWidth = CommUtil.mScreenWidth;
        int width = 0;

        if (CommUtil.mScreenHeight > CommUtil.mScreenWidth) {
            width = CommUtil.mScreenWidth;
            mHeight = mWidth * 280 / 360;

        } else {
            width = CommUtil.mScreenHeight;
            mHeight = CommUtil.mScreenHeight / 2 + CommUtil.mScreenHeight / 12;
        }

        mCandHei = 40 * width / 360;
        
        mCandHandler.updateSize(mWidth, mCandHei);

        int height = mHeight;

        mCandOff = 0;
        height -= mCandHei;
        mPadOff = mCandOff + mCandHei;
        mPadHei = height;
    }

    protected void switchPy26() {
        mKeyNum = KeymapArranger.arrangeKeyMapPy26(mKeyList, 0, mPadOff, mWidth, mHeight);

        ImeCoreManager.getPad().setPadLayout(new int[]{0, 0, CommUtil.mScreenWidth, mPadHei}, false);
        for (int i = 0; i < mKeyNum; i++) {
            byte coreKeyChar = (byte) (mKeyList[i].mKeyid - 'A' + 1);
            int[] rect = new int[] {mKeyList[i].getLeft(), mKeyList[i].getTop(),
                    mKeyList[i].getRight(), mKeyList[i].getBottom()};
            ImeCoreManager.getPad().setPadKeyPos(coreKeyChar, rect, rect);
        }
    }

    protected void switchEn26() {
        mKeyNum = KeymapArranger.arrangeKeyMapEn26(mKeyList, 0, mPadOff, mWidth, mHeight);
    }

    protected void switchNum() {
        mKeyNum = KeymapArranger.arrangeKeyMapNum9(mKeyList, 0, mPadOff, mWidth, mHeight);
        initListIfNeeded(mKeyList, mKeyNum);
    }

    /**
     * 切换到符号面板
     */
    protected void switchSym() {
        
        if (CommUtil.sSymFragment == null) {
            CommUtil.sSymFragment = new SymbolPanelFragment();
        }
        PopServiceWrapper.showSymView(CommUtil.sSymFragment);
        
    }

    /**
     * 切换到py9面板
     */
    protected void switchPy9() {
        mKeyNum = KeymapArranger.arrangeKeyMapPy9(mKeyList, 0, mPadOff, mWidth, mHeight);
        initListIfNeeded(mKeyList, mKeyNum);
    }

    /**
     * 切换到en9面板
     */
    protected void switchEn9() {
        mKeyNum = KeymapArranger.arrangeKeyMapEn9(mKeyList, 0, mPadOff, mWidth, mHeight);
        initListIfNeeded(mKeyList, mKeyNum);
    }

    /**
     * 切换到手写面板
     */
    private void switchHw() {
        mKeyNum = KeymapArranger.arrangeKeyMapHw(mKeyList, 0, mPadOff, mWidth, mHeight);
        initHandwritingIfNeeded(mKeyList, mKeyNum);
    }

    /**
     * 切换到笔画面板
     */
    private void switchBh() {
        mKeyNum = KeymapArranger.arrangeKeyMapBh9(mKeyList, 0, mPadOff, mWidth, mHeight);
    }

    /**
     * 切换到五笔九键面板
     */
    private void switchWb9() {
        mKeyNum = KeymapArranger.arrangeKeyMapWb9(mKeyList, 0, mPadOff, mWidth, mHeight);
    }

    /**
     * 切换到五笔26键面板
     */
    private void switchWb26() {
        mKeyNum = KeymapArranger.arrangeKeyMapWb26(mKeyList, 0, mPadOff, mWidth, mHeight);
    }

    /**
     * 切换到仓颉面板
     */
    private void switchCangjie() {
        mKeyNum = KeymapArranger.arrangeKeyMapCangjie(mKeyList, 0, mPadOff, mWidth, mHeight);
    }

    /**
     * 切换到注音面板
     */
    private void switchZhuyin() {
        mKeyNum = KeymapArranger.arrangeKeyMapZhuyin(mKeyList, 0, mPadOff, mWidth, mHeight);
    }
    
    /**
     * 初始化list区域
     */
    private void initListIfNeeded(PadKey[] keyList, int keyNum) {
        boolean hasList = false;
        for (int i = 0; i < keyNum; ++i) {
            PadKey tkey = keyList[i];
            if (tkey.getKeyId() == Consts.FKEY_SUBLIST) {
                String[] items = new String[] {"，", "。", "？", "！", "~", "……", "：", "；"};
                ImeCoreManager.getPad().importSymList(items, items);
                // 暂时把updateSize放在这个位置
                mListHandler.updateSize(tkey.getLeft(), tkey.getTop(), tkey.getRight(), tkey.getBottom());
                hasList = true;
                break;
            }
        }
        if (!hasList) {
            mListHandler.updateSize(0, 0, 0, 0);
            mListHandler.hide();
        }
    }

    /**
     * 初始化手写区域
     */
    private void initHandwritingIfNeeded(PadKey[] keyList, int keyNum) {
        for (int i = 0; i < keyNum; ++i) {
            PadKey tkey = keyList[i];
            if (tkey.getKeyId() == Consts.FKEY_HANDWRITING) {
                // 暂时把updateSize放在这个位置
                mHandwritingHandler.updateSize(tkey.getLeft(), tkey.getTop(), tkey.getRight(), tkey.getBottom());
                break;
            }
        }
    }
    
    /**
     * 通知面板隐藏
     */
    public void onHidden() {
        if (mListHandler != null) {
            mListHandler.hide();
        }
        if (mCandHandler != null) {
            mCandHandler.hide();
        }
    }

    /**
     * 更新cand的UI属性
     */
    public void updateCand(IptCandState candStore) {
        if (InputStatMachine.Companion.getInstance().nowKeyboard2PadId() == ImeCoreConsts.PAD_HW) {
            if (candStore.getCandCount() > 0) {
                IptCoreCandInfo candInfo = candStore.getCandAt(0);
                String value = candInfo.uni();
                if (!TextUtils.isEmpty(value)) {
                    String pinyin = ImeCoreManager.getPad().getHwPinyinStr(value.charAt(0), false);
                    if (!TextUtils.isEmpty(pinyin)) {
                        Toast.makeText(getContext(), pinyin, Toast.LENGTH_SHORT).show();
                    }
                }
            }
        }
        mCandHandler.updateCand(candStore);
    }

    /**
     * 更新list的UI属性
     * @param listStore list的状态
     */
    public void updateList(IptListState listStore) {
        mListHandler.updateList(listStore);
    }
}
