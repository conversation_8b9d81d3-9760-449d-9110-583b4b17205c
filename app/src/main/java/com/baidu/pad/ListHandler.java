/*
 * Copyright (C) 2017 Baidu, Inc. All Rights Reserved.
 */
package com.baidu.pad;

import java.util.ArrayList;
import java.util.List;

import com.baidu.input.support.state.IptListState;
import com.baidu.pad.action.ImeAction;
import com.baidu.pad.bean.PadListItem;
import com.baidu.pad.region.ScrollableRegion;
import com.baidu.util.CommUtil;

import android.content.Context;
import android.graphics.Color;
import android.graphics.Paint;

/**
 * List的绘制器
 * Created by <PERSON><PERSON><PERSON><PERSON> on 17/4/14.
 */
public class ListHandler extends ScrollableRegion<PadListItem> {

    private static final int CELL_NUM = 4;

    private Paint mPaint;

    public ListHandler(Context context) {
        super(context, ORIENTATION_VERTICAL);
        mPaint = new Paint();
        mPaint.setColor(Color.BLACK);
        mPaint.setAntiAlias(true);
    }

    public void updateSize(int left, int top, int right, int bottom) {
        super.updateBounds(left, top, right, bottom);
    }
    
    /**
     * 更新list的UI属性
     * @param listState
     */
    public void updateList(IptListState listState) {
        int listCnt = listState.getListCnt();
        List<PadListItem> result = new ArrayList<>();
        for (int i = 0; i < listCnt; i++) {
            result.add(new PadListItem(listState.getListAt(i).uni(), i));
        }
        super.updateContent(result);
    }

    @Override
    protected void updateUI() {
        CommUtil.mPadView.invalidate();
    }

    @Override
    protected void measureChild(PadListItem cell) {
        cell.setMeasuredDimension(mRectBounds.width(), mRectBounds.height() / CELL_NUM);
    }

    @Override
    protected void doAction(PadListItem cell) {
        ImeAction.dispatch(ImeAction.LIST_CLICK, cell.getIndex());
    }
}
