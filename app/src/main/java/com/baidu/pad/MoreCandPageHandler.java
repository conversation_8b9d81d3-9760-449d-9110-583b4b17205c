package com.baidu.pad;

import java.util.Arrays;

import com.baidu.input.support.state.IptCandState;
import com.baidu.iptcore.info.IptCoreCandInfo;
import com.baidu.pad.bean.PadCand;

import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Rect;
import android.view.MotionEvent;

/**
 * 更多候选面板的候选字绘制处理器
 * 
 * Created by ch<PERSON><PERSON><PERSON> on 17/8/15.
 */
public class MoreCandPageHandler {

    private static final int PAGE_NUM = 16;
    private static final int COL_COUNT = 4;
    private static final int ROW_COUNT = 4;

    private PadCand[] mCandWords = new PadCand[PAGE_NUM];
    private Paint mPaint;
    private int mPressedIdx = -1;
    private CandClickListener mCandClickListener;
    
    private Rect mBounds = new Rect();

    /**
     * 当有候选词点击时候的监听器
     */
    public interface CandClickListener {

        /**
         * 候选词被点击
         */
        void onCandClicked();
    }
    
    public MoreCandPageHandler() {
        mPaint = new Paint();
        mPaint.setAntiAlias(true);
    }

    /**
     * 加载指定页的候选字
     *
     * @param pageIdx   页索引
     * @param candStore 候选字的数据封装
     *
     * @return true: 加载成功
     */
    public boolean loadCandPage(int pageIdx, IptCandState candStore) {
        if (pageIdx < 0) {
            return false;
        }
        int candCount = candStore.getCandCount();
        int startIdx = PAGE_NUM * pageIdx;
        int pageCount = Math.min(candCount - startIdx, PAGE_NUM);
        if (pageCount == 0) {
            return false;
        }
        Arrays.fill(mCandWords, null);
        IptCoreCandInfo candInfo;
        for (int i = 0; i < pageCount; i++) {
            candInfo = candStore.getCandAt(startIdx + i);
            mCandWords[i] = candInfo == null ? null : new PadCand(startIdx + i, candInfo);
            mCandWords[i].setNormalColor(0x20000000);
            mCandWords[i].setHighlightColor(0x80000000);
        }

        return true;
    }

    public void layoutCandWords(int left, int top, int right, int bottom) {
        mBounds.set(left, top, right, bottom);
        int width = (right - left) / COL_COUNT;
        int height = (bottom - top) / ROW_COUNT;
        int candLeft;
        int candTop;
        int padding = 5;
        for (int i = 0; i < mCandWords.length; i++) {
            candLeft = (i % COL_COUNT) * width + left;
            candTop = (i / ROW_COUNT) * height + top;
            if (mCandWords[i] != null) {
                mCandWords[i].layout(candLeft + padding, candTop + padding, candLeft + width - padding, 
                        candTop + height - padding);
            }
        }
    }
    
    public void onDraw(Canvas canvas) {
        if (mBounds.isEmpty()) {
            return;
        }
        for (int i = 0; i < mCandWords.length; i++) {
            if (mCandWords[i] != null) {
                mCandWords[i].draw(canvas);
            }
        }
    }

    public boolean onTouchEvent(MotionEvent event) {
        if (mBounds.isEmpty()) {
            return false;
        }
        int x = (int) event.getX();
        int y = (int) event.getY();
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                for (int i = 0; i < mCandWords.length; i++) {
                    if (mCandWords[i] != null && mCandWords[i].contains(x, y)) {
                        mCandWords[i].setPressed(true);
                        mPressedIdx = i;
                        return true;
                    }
                }
                break;
            case MotionEvent.ACTION_UP:
                if (mPressedIdx >= 0 && mPressedIdx < mCandWords.length) {
                    if (mCandWords[mPressedIdx] != null) {
                        mCandWords[mPressedIdx].setPressed(false);
                        if (mCandWords[mPressedIdx].contains(x, y)) {
                            mCandWords[mPressedIdx].doAction();
                            if (mCandClickListener != null) {
                                mCandClickListener.onCandClicked();
                            }
                        }
                    }
                    mPressedIdx = -1;
                    return true;
                }
                break;
            default:
                if (mPressedIdx >= 0 && mPressedIdx < mCandWords.length) {
                    return true;
                }
                break;
        }
        return false;

    }

    public void hide() {
        mBounds.setEmpty();
        for (int i = 0; i < mCandWords.length; i++) {
            if (mCandWords[i] != null) {
                mCandWords[i].layout(0, 0, 0, 0);
            }
        }
        reset();
    }

    private void reset() {
        mPressedIdx = -1;
    }

    public void setCandClickListener(CandClickListener listener) {
        mCandClickListener = listener;
    }
}
