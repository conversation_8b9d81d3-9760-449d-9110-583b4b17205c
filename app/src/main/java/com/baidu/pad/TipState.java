package com.baidu.pad;

import com.baidu.iptcore.info.IptCoreDutyInfo;
import com.baidu.util.BDLog;

/**
 * 输入法的面板状态（TIP）的管理类
 * Created by <PERSON><PERSON><PERSON><PERSON> on 17/8/7.
 */
public class TipState {
    
    private static volatile TipState sInstance;

    /**
     * 当前是否是英文联想
     */
    private boolean mIsEnLx = true;
    private boolean mIsShiftLock = false;
    private boolean mIsShift = false;
    private boolean mIsWordRangeSingle = false;
    private boolean mIsSymLock = false;
    private boolean mIsQuickCangjie = false;
    private boolean mIsCnShift = false;
    private boolean mIsInputOn = false;
    private boolean mIsCnLx = false;

    private TipState() {
        // do nothing
    }

    /**
     * 获取单例
     */
    public static TipState getInstance() {
        if (sInstance == null) {
            synchronized (TipState.class) {
                if (sInstance == null) {
                    sInstance = new TipState();
                }
            }
        }
        return sInstance;
    }

    public void onStateChange(int stateId, boolean stateValue) {
        switch (stateId) {
            case IptCoreDutyInfo.TIP_EN_ABC:
                mIsEnLx = stateValue;
                break;
            case IptCoreDutyInfo.TIP_CAPITAL_FIRST:
                mIsShift = stateValue;
                break;
            case IptCoreDutyInfo.TIP_CAPITAL_ALL:
                mIsShiftLock = stateValue;
                break;
            case IptCoreDutyInfo.TIP_MORE_SINGLE:
                mIsWordRangeSingle = stateValue;
                break;
            case IptCoreDutyInfo.TIP_SYM_LOCK:
                mIsSymLock = stateValue;
                break;
            case IptCoreDutyInfo.TIP_CN_SHIFT:
                mIsCnShift = stateValue;
                break;
            case IptCoreDutyInfo.TIP_CANGJIE_SC:
                mIsQuickCangjie = stateValue;
                break;
            case IptCoreDutyInfo.TIP_HAS_INPUT:
                mIsInputOn = stateValue;
                break;
            case IptCoreDutyInfo.TIP_CN_LIAN:
                mIsCnLx = stateValue;
                break;
            default:
                break;
        }
    }

    /**
     * 是否是英文联想状态
     */
    public boolean isEnLx() {
        return mIsEnLx;
    }

    public boolean isShiftLock() {
        return mIsShiftLock;
    }

    public boolean isShift() {
        return mIsShift;
    }

    public boolean isWordRangeSingle() {
        return mIsWordRangeSingle;
    }

    public boolean isSymLock() {
        return mIsSymLock;
    }

    public boolean isQuickCangjie() {
        return mIsQuickCangjie;
    }

    public boolean isCnShift() {
        return mIsCnShift;
    }
}
