package com.baidu.pad.action;

import com.baidu.iptcore.ImeCoreConsts;
import com.baidu.iptcore.ImeCoreManager;

import android.graphics.Point;

/**
 * 封装相关操作。目前只封装输入相关的操作。
 * 
 * Created by <PERSON><PERSON><PERSON><PERSON> on 17/7/4.
 */
public class ImeAction {
    
    // ///////////////////////////////////////////////////
    // -----------------   动作类型常量 ---------------------
    // ///////////////////////////////////////////////////
    
    /** 按键点击 */
    public static final int KEY_CLICK = 0;

    /** 候选字点击 */
    public static final int CAND_CLICK = 1;
    /** list区域项点击 */
    public static final int LIST_CLICK = 2;
    /** 手写启动 */
    public static final int HW_START_TRACK = 3;
    /** 手写move */
    public static final int HW_TRACK_MOVE = 4;
    /** 手写结束 */
    public static final int HW_END_TRACK = 5;
    /** 切换面板（功能键） */
    public static final int SWITCH_KEYBOARD = 6;
    /** 设置光标 */
    public static final int INPUT_CURSOR = 7;
    /** input区域的rollback */
    public static final int INPUT_POP = 8;

    public static final int KEY_DRAG_L = 9;
    public static final int KEY_DRAG_R = 10;
    public static final int KEY_DRAG_U = 11;
    public static final int KEY_DRAG_D = 12;

    /**
     * 类型
     */
    private final int mType;
    /**
     * Action的数据
     */
    private final Object mData;

    /**
     * 构造器1
     */
    private ImeAction(int type, int id) {
        mType = type;
        mData = id;
    }

    /**
     * 构造器2
     */
    private ImeAction(int type, Point point) {
        mType = type;
        mData = point;
    }

    public int getType() {
        return mType;
    }

    public Object getData() {
        return mData;
    }

    public static void dispatch(int type) {
        new ImeAction(type, null).dispatch();
    }

    public static void dispatch(int type, int id) {
        new ImeAction(type, id).dispatch();
    }

    public static void dispatch(int type, int x, int y) {
        new ImeAction(type, new Point(x, y)).dispatch();
    }
    
    /**
     * 派发这个事件
     */
    public void dispatch() {
        switch (getType()) {
            case KEY_CLICK:
                int keyId = (Integer) getData();
                if (keyId >= ImeCoreConsts.FKEY_BACK) {
                    keyId += 0;
                } else if (keyId >= 1000) {
                    keyId = ImeCoreConsts.FKEY_BACK + (keyId - 1000); // F功能键映射
                }
                ImeCoreManager.getPad().actKeyClicked(keyId, ImeCoreConsts.INPUTTYPE_CLICK);
                break;
            case KEY_DRAG_L:
                ImeCoreManager.getPad().actKeyClicked((Integer) getData(), ImeCoreConsts.INPUTTYPE_LEFT);
                break;
            case KEY_DRAG_R:
                ImeCoreManager.getPad().actKeyClicked((Integer) getData(), ImeCoreConsts.INPUTTYPE_RIGHT);
                break;
            case KEY_DRAG_U:
                ImeCoreManager.getPad().actKeyClicked((Integer) getData(), ImeCoreConsts.INPUTTYPE_UP);
                break;
            case KEY_DRAG_D:
                ImeCoreManager.getPad().actKeyClicked((Integer) getData(), ImeCoreConsts.INPUTTYPE_DOWN);
                break;
            case CAND_CLICK:
                ImeCoreManager.getPad().actCandClicked((Integer) getData());
                break;
            case LIST_CLICK:
                ImeCoreManager.getPad().actListClicked((Integer) getData());
                break;
            case HW_START_TRACK:
                ImeCoreManager.getPad().actTrackStart(((Point) getData()).x, ((Point) getData()).y,
                        System.currentTimeMillis());
                break;
            case HW_TRACK_MOVE:
                ImeCoreManager.getPad().actTrackMove(((Point) getData()).x, ((Point) getData()).y,
                        System.currentTimeMillis());
                break;
            case HW_END_TRACK:
                ImeCoreManager.getPad().actTrackEnd(((Point) getData()).x, ((Point) getData()).y,
                        System.currentTimeMillis(), true);
                break;
            case SWITCH_KEYBOARD:
                ImeCoreManager.getPad().switchKeyboard((Integer) getData());
                break;
            case INPUT_CURSOR:
                ImeCoreManager.getPad().actInputCursor((Integer) getData());
                break;
            case INPUT_POP:
                ImeCoreManager.getPad().actInputPop();
                break;
            default:
                break;
        }
    }
}
