/*
 * Copyright (C) 2017 Baidu, Inc. All Rights Reserved.
 */
package com.baidu.pad.bean;

import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.text.TextUtils;

import com.baidu.iptcore.info.IptCoreCandInfo;
import com.baidu.pad.action.ImeAction;
import com.baidu.pad.region.IRegionCell;
import com.baidu.util.CommUtil;
import com.baidu.util.DrawUtil;

/**
 * 候选词的UI封装
 * Created by ch<PERSON><PERSON><PERSON> on 17/4/10.
 */
public class PadCand implements IRegionCell {

    private static final float FONT_SIZE_RATIO = 0.45f;
    private static final int MIN_FONT_SIZE = 10;

    /**
     * 画笔
     */
    private final Paint mPaint;

    private int mHightlightClr = 0x7fffffff;
    private int mNormalClr = Color.TRANSPARENT;
    
    /**
     * 内核的候选词信息
     */
    IptCoreCandInfo mCandInfo;

    /**
     * 候选词索引
     */
    private int mCandIdx = -1;

    /**
     * 展示的文本
     */
    private String mText = null;
    /** 区域坐标：左上x */
    private int mLeft = 0;
    /** 区域坐标：左上y */
    private int mTop = 0;
    /** 区域坐标：右下x */
    private int mRight = 0;
    /** 区域坐标：右下y */
    private int mBottom = 0;
    /** 是否按下 */
    private boolean mIsPressed = false;

    /**
     * 构造器
     * @param candInfo 内核的候选词信息
     */
    public PadCand(int index, IptCoreCandInfo candInfo) {
        mCandIdx = index;
        mCandInfo = candInfo;

        mPaint = new Paint();
        mPaint.setAntiAlias(true);
        
        // 更新文本
        switch (candInfo.candType()) {
            case IptCoreCandInfo.CANDTYPE_CLOUD_ARROW: {
                this.mText = "(云动画)";
                break;
            }
            case IptCoreCandInfo.CANDTYPE_SERVICE: {
                this.mText = candInfo.uni() + '(' + candInfo.serviceType() + ",云)";
                break;
            }
            default: {
                this.mText = candInfo.uni();
                break;
            }
        }
    }

    @Override
    public String toString() {
        return "{PadCand:" + mText + "}";
    }

    @Override
    public void draw(Canvas c) {
        c.save();
        c.clipRect(mLeft, mTop, mRight, mBottom);
        if (mIsPressed) {
            mPaint.setColor(mHightlightClr);
            mPaint.setStyle(Paint.Style.FILL);
            c.drawRect(mLeft, mTop, mRight, mBottom, mPaint);
        } else {
            if (mNormalClr != Color.TRANSPARENT) {
                mPaint.setColor(mNormalClr);
                mPaint.setStyle(Paint.Style.FILL);
                c.drawRect(mLeft, mTop, mRight, mBottom, mPaint);
            }
        }

        int candBottomLine = mTop + getMeasuredHeight() * 5 / 6;
        if (mText != null) {
            mPaint.setColor(Color.WHITE);
            int defFontSize = (int) ((candBottomLine - mTop) * FONT_SIZE_RATIO);
            mPaint.setTextSize(defFontSize);
            int w = (int) mPaint.measureText(mText);
            if (w > getMeasuredWidth()) {
                defFontSize = defFontSize * getMeasuredWidth() / w;
            }
            if (defFontSize < MIN_FONT_SIZE) {
                defFontSize = MIN_FONT_SIZE;
            }
            DrawUtil.drawText(c, mPaint, mText, mLeft, mTop, mRight, candBottomLine, defFontSize);
        }

        if (mCandInfo != null) {
            int candType = mCandInfo.candType();
            int candFlag = mCandInfo.flag();
            String type = CommUtil.candTypeToString(candType);
            String flag = CommUtil.candFlagToString(candFlag);
            if (!TextUtils.isEmpty(type) || !TextUtils.isEmpty(flag)) {
                DrawUtil.drawText(c, mPaint, type + "|" + flag, mLeft,
                        candBottomLine, mRight, mBottom, (int) ((mBottom - candBottomLine)));
            }
        }
        c.restore();
    }

    @Override
    public void setPressed(boolean pressed) {
        mIsPressed = pressed;
    }

    @Override
    public boolean contains(int x, int y) {
        return ((x >= mLeft && x <= mRight) && (y >= mTop && y <= mBottom));
    }

    @Override
    public int getLeft() {
        return mLeft;
    }

    @Override
    public int getTop() {
        return mTop;
    }

    @Override
    public int getRight() {
        return mRight;
    }

    @Override
    public int getBottom() {
        return mBottom;
    }

    @Override
    public int getMeasuredWidth() {
        return mRight - mLeft;
    }

    @Override
    public int getMeasuredHeight() {
        return mBottom - mTop;
    }

    @Override
    public void layout(int left, int top, int right, int bottom) {
        mLeft = left;
        mRight = right;
        mTop = top;
        mBottom = bottom;
    }

    @Override
    public void setMeasuredDimension(int width, int height) {
        mRight = mLeft + width;
        mBottom = mTop + height;
    }

    public void doAction() {
        ImeAction.dispatch(ImeAction.CAND_CLICK, mCandIdx);
    }

    public void measure(int parentWidth, int parentHeight) {
        int defFontSize = (int) (parentHeight * FONT_SIZE_RATIO);
        mPaint.setTextSize(defFontSize);
        int width = 0;
        if (!TextUtils.isEmpty(mText)) {
            width = (int) mPaint.measureText(mText);
        }
        if (width > parentWidth) {
            width = parentWidth;
        }
        setMeasuredDimension(width, parentHeight);
    }

    public void setNormalColor(int color) {
        mNormalClr = color;
    }

    public void setHighlightColor(int color) {
        mHightlightClr = color;
    }
}
