package com.baidu.pad.bean;

import com.baidu.pad.region.RegionCell;
import com.baidu.util.DrawUtil;

import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.view.MotionEvent;

/**
 * Created by ch<PERSON><PERSON><PERSON> on 17/7/23.
 */

public class PadIcon extends RegionCell {
    
    String mText;
    int id;

    private IconClickListener mListener;

    public PadIcon(String mText, int id) {
        this.mText = mText;
        this.id = id;
    }

    public int getId() {
        return id;
    }

    public void setListener(IconClickListener listener) {
        this.mListener = listener;
    }

    @Override
    public void draw(Canvas c) {
        if (mIsPressed) {
            mPaint.setColor(0x3fffffff);
            mPaint.setStyle(Paint.Style.FILL);
            c.drawRect(mLeft, mTop, mRight, mBottom, mPaint);
        }

        if (mText != null) {
            mPaint.setColor(Color.WHITE);
            DrawUtil.drawText(c, mPaint, mText, mLeft, mTop, mRight, mBottom, getMeasuredHeight() / 3);
        }
    }
    
    public boolean onTouchEvent(MotionEvent event) {
        int x = (int) event.getX();
        int y = (int) event.getY();
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                if (contains(x, y)) {
                    setPressed(true);
                    return true;
                }
            case MotionEvent.ACTION_UP:
                if (mIsPressed) {
                    mIsPressed = false;
                    if (mListener != null) {
                        mListener.onIconClicked(this);
                    }
                    return true;
                }
                break;
            default:
                break;
        }
        return false;
    }
    
    public interface IconClickListener {
        void onIconClicked(PadIcon bean);
    }
}
