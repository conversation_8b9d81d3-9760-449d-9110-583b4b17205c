/*
 * Copyright (C) 2017 Baidu, Inc. All Rights Reserved.
 */
package com.baidu.pad.bean;

import com.baidu.input.inputtrace.api.InputTracer;
import com.baidu.input.inputtrace.api.MethodIds;
import com.baidu.iptcore.ImeCoreConsts;
import com.baidu.keymap.switcher.InputStatMachine;
import com.baidu.pad.TipState;
import com.baidu.pad.region.RegionCell;
import com.baidu.util.CommUtil;
import com.baidu.util.Consts;
import com.baidu.util.DrawUtil;

import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.text.TextUtils;
import android.util.Log;
import android.view.MotionEvent;

/**
 * Created by chend<PERSON><PERSON> on 17/4/13.
 */

public class PadKey extends RegionCell {

    private static final int DRAGWAY_NONE = -1;
    public int mKeyid = 0;
    public String mTxt = null;

    private int mPressX;
    private int mPressY;
    private int mUpKeyId;
    private int mLeftKeyId;
    private int mRightKeyId;
    private int mDownKeyId;

    public void setBounds(int left, int top, int right, int bottom) {
        mLeft = left;
        mTop = top;
        mRight = right;
        mBottom = bottom;
    }

    public void offset(int left, int top) {
        mLeft += left;
        mRight += left;
        mTop += top;
        mBottom += top;
    }

    @Override
    public String toString() {
        // DEBUG模式数据打印
        String text = mTxt == null ? "" : mTxt;
        String center = mKeyid >= 1000 ? mKeyid + "" : String.valueOf((char) mKeyid);
        String bounds = String.format("[%d,%d-%d,%d]", mLeft, mTop, mRight, mBottom);
        String str = String.format("\"TEXT=%s;BOUNDS=%s;CENTER=%s;", text, bounds, center);
        str = str + "\"";
        return str;
    }

    public void setText(String text) {
        mTxt = text;
    }

    public void setCenter(int center) {
        mKeyid = center;
    }
    public void setUp(int upKeyId) {
        mUpKeyId = upKeyId;
    }
    public void setLeft(int keyId) {
        mLeftKeyId = keyId;
    }
    public void setRight(int keyId) {
        mRightKeyId = keyId;
    }
    public void setDown(int keyId) {
        mDownKeyId = keyId;
    }

    public int getUpKeyId() {
        return mUpKeyId;
    }
    public int getLeftKeyId() {
        return mLeftKeyId;
    }
    public int getRightKeyId() {
        return mRightKeyId;
    }
    public int getDownKeyId() {
        return mDownKeyId;
    }

    public void resize(float scaleW, float scaleH) {
        mLeft = (int) (mLeft * scaleW + 0.5f);
        mTop = (int) (mTop * scaleH + 0.5f);
        mRight = (int) (mRight * scaleW + 0.5f);
        mBottom = (int) (mBottom * scaleH + 0.5f);
    }
    
    @Override
    public void draw(Canvas canvas) {

        // 特殊按键处理: 联想按键
        if (mKeyid == ImeCoreConsts.FKEY_EN_LX) {
            drawEnLxKey(canvas);
        } else if (mKeyid == ImeCoreConsts.FKEY_SHIFT || mKeyid == ImeCoreConsts.FKEY_SHIFT_CAPLOCK) {
            drawShiftKey(canvas);
        } else if (mKeyid == ImeCoreConsts.FKEY_SINGLE) {
            drawWordRangeKey(canvas);
        } else if (mKeyid == Consts.FKEY_QUICK_CJ) {
            drawQuickCjKey(canvas);
        } else {
            drawBack(canvas);
            drawFore(canvas);
        }
    }

    private void drawWordRangeKey(Canvas canvas) {
        drawBack(canvas);

        if (TipState.getInstance().isWordRangeSingle()) {
            DrawUtil.drawText(canvas, mPaint, "单字", mLeft, mTop, mRight, mBottom, (mBottom - mTop) / 4);
        } else {
            DrawUtil.drawText(canvas, mPaint, "多字", mLeft, mTop, mRight, mBottom, (mBottom - mTop) / 4);
        }
    }

    private void drawQuickCjKey(Canvas canvas) {
        drawBack(canvas);

        if (TipState.getInstance().isQuickCangjie()) {
            DrawUtil.drawText(canvas, mPaint, "速仓", mLeft, mTop, mRight, mBottom, (mBottom - mTop) / 4);
        } else {
            DrawUtil.drawText(canvas, mPaint, "仓速", mLeft, mTop, mRight, mBottom, (mBottom - mTop) / 4);
        }
    }

    private void drawEnLxKey(Canvas canvas) {
        drawBack(canvas);

        if (TipState.getInstance().isEnLx()) {
            int previousFlags = mPaint.getFlags();
            mPaint.setFlags(Paint.UNDERLINE_TEXT_FLAG);
            if (!TextUtils.isEmpty(mTxt)) {
                DrawUtil.drawText(canvas, mPaint, mTxt, mLeft, mTop, mRight, mBottom, (mBottom - mTop) / 4);
            }
            mPaint.setFlags(previousFlags);
        } else {
            drawFore(canvas);
        }
    }

    private void drawShiftKey(Canvas canvas) {
        drawBack(canvas);

        int nowPadId = InputStatMachine.Companion.getInstance().nowKeyboard2PadId();
        boolean isEn = (nowPadId == ImeCoreConsts.PAD_EN9 || nowPadId == ImeCoreConsts.PAD_EN26
                                || nowPadId == ImeCoreConsts.PAD_EN26_S);

        if (isEn) {
            if (TipState.getInstance().isShiftLock()) {
                if (!TextUtils.isEmpty(mTxt)) {
                    DrawUtil.drawText(canvas, mPaint, "SHIFT", mLeft, mTop, mRight, mBottom, (mBottom - mTop) / 4);
                }
            } else if (TipState.getInstance().isShift()) {
                if (!TextUtils.isEmpty(mTxt)) {
                    DrawUtil.drawText(canvas, mPaint, "Shift", mLeft, mTop, mRight, mBottom, (mBottom - mTop) / 4);
                }
            } else {
                if (!TextUtils.isEmpty(mTxt)) {
                    DrawUtil.drawText(canvas, mPaint, "shift", mLeft, mTop, mRight, mBottom, (mBottom - mTop) / 4);
                }
            }
        } else {
            if (TipState.getInstance().isCnShift()) {
                if (!TextUtils.isEmpty(mTxt)) {
                    DrawUtil.drawText(canvas, mPaint, "SHIFT", mLeft, mTop, mRight, mBottom, (mBottom - mTop) / 4);
                }
            } else {
                if (!TextUtils.isEmpty(mTxt)) {
                    DrawUtil.drawText(canvas, mPaint, "shift", mLeft, mTop, mRight, mBottom, (mBottom - mTop) / 4);
                }
            }
        }
    }

    private void drawFore(Canvas canvas) {
        if (!TextUtils.isEmpty(mTxt)) {
            DrawUtil.drawText(canvas, mPaint, mTxt, mLeft, mTop, mRight, mBottom, (mBottom - mTop) / 4);
        }
    }

    private void drawBack(Canvas canvas) {
        mPaint.setColor(Color.WHITE);
        mPaint.setStrokeWidth(2);
        mPaint.setStyle(Paint.Style.STROKE);
        canvas.drawRect(mLeft, mTop, mRight, mBottom, mPaint);

        if (mIsPressed) {
            mPaint.setColor(Color.rgb(188, 188, 255));
            mPaint.setStyle(Paint.Style.FILL);
            canvas.drawRect(mLeft + 1, mTop + 1, mRight - 1, mBottom - 1, mPaint);
            mPaint.setColor(Color.WHITE);
            mPaint.setStyle(Paint.Style.STROKE);
        }
    }

    public void setPressed(int x, int y) {
        mIsPressed = true;
        mPressX = x;
        mPressY = y;
    }
    
    public void resetPressed() {
        mIsPressed = false;
        mPressX = -1;
        mPressY = -1;
    }

    public int getKeyId() {
        return mKeyid;
    }

    public boolean onTouchEvent(MotionEvent event) {
        int x = (int) event.getX();
        int y = (int) event.getY();
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                if (contains(x, y)) {
                    setPressed(x, y);
                    int methodId = MethodIds.INPUT_DOWN_START + mKeyid;
                    Log.e("wentaoli inputtracer", "onTouchEvent down :" + methodId + ", " + mKeyid);
                    if (methodId < MethodIds.INPUT_DOWN_END) {
                        InputTracer.i(methodId);
                    }
                    return true;
                }
            case MotionEvent.ACTION_UP:
                if (mIsPressed) {
                    int dragWay = checkDragWay(mPressX, mPressY, x, y);
                    if (dragWay != DRAGWAY_NONE) {
                        if (CommUtil.mPadView != null) {
                            CommUtil.mPadView.onKeyTouched(this, dragWay);
                        }
                    }
                    resetPressed();
                    int methodId = MethodIds.INPUT_UP_START + mKeyid;
                    Log.e("wentaoli inputtracer", "onTouchEvent up :" + methodId + ", " + mKeyid);
                    if (methodId < MethodIds.INPUT_UP_END) {
                        InputTracer.i(methodId);
                    }
                    return true;
                }
                break;
            default:
                break;
        }
        return false;
    }

    private int checkDragWay(int pressX, int pressY, int releaseX, int releaseY) {
        
        // click检测
        if (releaseX >= getLeft() && releaseX <= getRight() && releaseY >= getTop() && releaseY <= getBottom()) {
            return ImeCoreConsts.INPUTTYPE_CLICK;
        }

        // x坐标不变的处理
        if (releaseX == pressX || getLeft() == getRight()) {
            if (releaseY < pressY) {
                return ImeCoreConsts.INPUTTYPE_UP;
            } else {
                return ImeCoreConsts.INPUTTYPE_DOWN;
            }
        }
        
        // 滑动检测，用滑动的倾角和按键形状的倾角做检测
        float tanAngle = (float) (releaseY - pressY) / (releaseX - pressX);
        float tanShape = (float) (getBottom() - getTop()) / (getRight() - getLeft());

        if (releaseY < pressY) {
            if (tanAngle > tanShape || tanAngle < -tanShape) {
                return ImeCoreConsts.INPUTTYPE_UP;
            } else if (tanAngle >= 0) {
                return ImeCoreConsts.INPUTTYPE_LEFT;
            } else {
                return ImeCoreConsts.INPUTTYPE_RIGHT;
            }
        } else {
            if (tanAngle > tanShape || tanAngle < -tanShape) {
                return ImeCoreConsts.INPUTTYPE_DOWN;
            } else if (tanAngle >= 0) {
                return ImeCoreConsts.INPUTTYPE_RIGHT;
            } else {
                return ImeCoreConsts.INPUTTYPE_LEFT;
            }
        }
        
    }

    public interface KeyTouchListener {
        void onKeyTouched(PadKey key, int dragWay);
    }

}
