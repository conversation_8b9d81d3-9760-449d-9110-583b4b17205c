/*
 * Copyright (C) 2017 Baidu, Inc. All Rights Reserved.
 */
package com.baidu.pad.bean;

import com.baidu.pad.region.IRegionCell;
import com.baidu.util.DrawUtil;

import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;

/**
 * 封装一个list的单元的展示
 * 
 * Created by <PERSON><PERSON><PERSON><PERSON> on 17/4/14.
 */
public class PadListItem implements IRegionCell {
    private int mLeft;
    private int mTop;
    private int mRight;
    private int mBottom;
    private String mText;
    private boolean mIsPressed;
    
    private Paint mPaint;
    int mIndex;

    public PadListItem(String text, int index) {
        mText = text;
        mIndex = index;
        
        mPaint = new Paint();
        mPaint.setAntiAlias(true);
    }
    
    public void setText(String text) {
        mText = text;
    }

    @Override
    public String toString() {
        return "PadListItem{" 
                + "Bounds=[" + mLeft + ", " + mTop + "- " + mRight + ", " + mBottom + "], mText='" + mText + '\'' + '}';
    }

    public String getText() {
        return mText;
    }

    public int getIndex() {
        return mIndex;
    }

    @Override
    public void draw(Canvas c) {

        if (mIsPressed) {
            mPaint.setColor(0x4fffffff);
            mPaint.setStyle(Paint.Style.FILL);
            c.drawRect(mLeft, mTop, mRight, mBottom, mPaint);
        } else {
            mPaint.setColor(0x1f000000);
            mPaint.setStyle(Paint.Style.FILL);
            c.drawRect(mLeft, mTop, mRight, mBottom, mPaint);
        }

        if (mText != null) {
            mPaint.setColor(Color.WHITE);
            DrawUtil.drawText(c, mPaint, mText, mLeft, mTop, mRight, mBottom, getMeasuredHeight() / 3);
        }
    }

    @Override
    public void setPressed(boolean pressed) {
        mIsPressed = pressed;
    }

    @Override
    public boolean contains(int x, int y) {
        return ((x >= mLeft && x <= mRight) && (y >= mTop && y <= mBottom));
    }

    @Override
    public int getLeft() {
        return mLeft;
    }

    @Override
    public int getTop() {
        return mTop;
    }

    @Override
    public int getRight() {
        return mRight;
    }

    @Override
    public int getBottom() {
        return mBottom;
    }

    @Override
    public int getMeasuredWidth() {
        return mRight - mLeft;
    }

    @Override
    public int getMeasuredHeight() {
        return mBottom - mTop;
    }

    @Override
    public void layout(int left, int top, int right, int bottom) {
        mLeft = left;
        mRight = right;
        mTop = top;
        mBottom = bottom;
    }

    @Override
    public void setMeasuredDimension(int width, int height) {
        mRight = mLeft + width;
        mBottom = mTop + height;
    }
}
