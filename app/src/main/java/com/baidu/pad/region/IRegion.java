package com.baidu.pad.region;

import android.graphics.Canvas;
import android.view.MotionEvent;

/**
 * 一个自绘区域暴露给外部的接口。
 * Created by <PERSON><PERSON><PERSON><PERSON> on 17/6/21.
 */
public interface IRegion<T> {
    void hide();
    void updateContent(T content);
    void updateBounds(int left, int top, int right, int bottom);
    void draw(Canvas canvas);
    boolean onTouchEvent(MotionEvent event);
}
