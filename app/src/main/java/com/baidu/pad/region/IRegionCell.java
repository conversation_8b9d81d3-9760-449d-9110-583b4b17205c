package com.baidu.pad.region;

import android.graphics.Canvas;

/**
 * 封装一个自绘区域的单元。和{@link ScrollableRegion}结合使用，用来展示自绘的可滚动区域。
 * 
 * Created by <PERSON><PERSON><PERSON><PERSON> on 17/6/21.
 */
public interface IRegionCell {
    
    void draw(Canvas c);

    void setPressed(boolean pressed);

    boolean contains(int x, int y);

    int getLeft();

    int getTop();

    int getRight();

    int getBottom();

    int getMeasuredWidth();

    int getMeasuredHeight();

    void layout(int left, int top, int right, int bottom);

    void setMeasuredDimension(int width, int height);

}
