package com.baidu.pad.region;

import android.graphics.Paint;

/**
 * Created by ch<PERSON><PERSON><PERSON> on 17/7/23.
 */
public abstract class RegionCell implements IRegionCell {

    protected int mLeft;
    protected int mTop;
    protected int mRight;
    protected int mBottom;
    protected boolean mIsPressed;

    protected Paint mPaint;
    
    public RegionCell() {
        mPaint = new Paint();
        mPaint.setAntiAlias(true);
    }

    @Override
    public void setPressed(boolean pressed) {
        mIsPressed = pressed;
    }

    @Override
    public boolean contains(int x, int y) {
        return ((x >= mLeft && x <= mRight) && (y >= mTop && y <= mBottom));
    }

    @Override
    public int getLeft() {
        return mLeft;
    }

    @Override
    public int getTop() {
        return mTop;
    }

    @Override
    public int getRight() {
        return mRight;
    }

    @Override
    public int getBottom() {
        return mBottom;
    }

    @Override
    public int getMeasuredWidth() {
        return mRight - mLeft;
    }

    @Override
    public int getMeasuredHeight() {
        return mBottom - mTop;
    }

    @Override
    public void layout(int left, int top, int right, int bottom) {
        mLeft = left;
        mRight = right;
        mTop = top;
        mBottom = bottom;
    }

    @Override
    public void setMeasuredDimension(int width, int height) {
        mRight = mLeft + width;
        mBottom = mTop + height;
    }
}
