package com.baidu.pad.region;

import java.util.ArrayList;
import java.util.List;

import com.baidu.iptandroiddemo.R;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Rect;
import android.os.Handler;
import android.os.Looper;
import android.support.annotation.CallSuper;
import android.view.MotionEvent;
import android.view.VelocityTracker;
import android.view.ViewConfiguration;
import android.widget.Scroller;

/**
 * 封装一块可滑动的区域。目前主要给Cand和List使用
 * 
 * Created by ch<PERSON><PERSON><PERSON> on 17/6/21.
 */
public abstract class ScrollableRegion<T extends IRegionCell> implements Runnable, IRegion<List<T>> {
    private static final byte SCROLL_STATE_IDLE = 0;
    private static final byte SCROLL_STATE_DRAGGING = 1;
    private static final byte SCROLL_STATE_SETTLING = 2;
    private static final long CALLBACK_DELAY = 100;

    public static final int ORIENTATION_HORIZONTAL = 0;
    public static final int ORIENTATION_VERTICAL = 1;

    /**
     * 绘制的区域
     */
    protected Rect mRectBounds = new Rect();

    /**
     * 展示的内容
     */
    protected List<T> mCells;

    /**
     * Scroller对象
     */
    private Scroller mScroller;

    private VelocityTracker mVelocityTracker;
    private int mTouchSlop;
    private int mFlingDistance;
    private int mMinimumVelocity;
    private int mMaximumVelocity;

    /**
     * 绘制背景的画笔
     */
    private Paint mPaint;

    /**
     * 当前的候选词索引
     */
    private int mCurrentSelectIdx;

    /**
     * 选中的候选词索引
     */
    private int mSelectedIdx;
    /**
     * 候选词的总宽度
     */
    private int mContentWidth;
    private int mContentHeight;

    /**
     * 候选区相对初始位置的水平滚动位移
     */
    private int mScrollX;
    private int mScrollY;

    /**
     * 初始触摸区域的X坐标
     */
    private int mInitialMotionX;
    private int mInitialMotionY;
    /**
     * 上次触摸区域的X坐标
     */
    private int mLastMotionX;
    /**
     * 上次触摸区域的Y坐标
     */
    private int mLastMotionY;
    /**
     * 点击后需要执行的cell
     */
    private T mPendingCell;

    /**
     * 主线程handler
     */
    private Handler mHandler = new Handler(Looper.getMainLooper());

    /**
     * 滚动状态
     */
    private byte mScrollState = SCROLL_STATE_IDLE;

    /**
     * 是否判断为拖动事件
     */
    private boolean mIsBeingDragged;

    /**
     * 水平滚动/竖直滚动
     */
    private int mOrientation;

    /**
     * 当前是否在一轮触摸事件中。从down在区域内开始到up结束期间为true
     */
    private boolean mIsTouched = false;

    public ScrollableRegion(Context context, int orientation) {
        mOrientation = orientation;

        this.mPaint = new Paint();
        this.mPaint.setAntiAlias(true);
        mPaint.setTextAlign(Paint.Align.LEFT);
        mPaint.setStyle(Paint.Style.FILL);

        mCells = new ArrayList<>();

        final ViewConfiguration configuration = ViewConfiguration.get(context);
        mTouchSlop = (configuration.getScaledPagingTouchSlop() / 3);
        mMinimumVelocity = configuration.getScaledMinimumFlingVelocity();
        mMaximumVelocity = configuration.getScaledMaximumFlingVelocity();
        mFlingDistance = context.getResources().getDimensionPixelSize(R.dimen.fling_distance);

        mScroller = new Scroller(context);
    }

    @CallSuper
    @Override
    public void draw(Canvas canvas) {
        canvas.save();
        canvas.clipRect(mRectBounds);
        computeScroll();

        if (mOrientation == ORIENTATION_HORIZONTAL) {
            canvas.translate(0, mScrollX);
        } else {
            canvas.translate(0, mScrollY);
        }

        onDraw(canvas);
        canvas.restore();
    }

    private void computeScroll() {
        if (!mScroller.isFinished() && mScroller.computeScrollOffset()) {
            if (mOrientation == ORIENTATION_HORIZONTAL) {
                int x = mScroller.getCurrX();
                int deltaX = x - mScrollX;
                if (deltaX != 0) {
                    scrollBy(deltaX);
                }
            } else {
                int y = mScroller.getCurrY();
                int deltaY = y - mScrollY;
                if (deltaY != 0) {
                    scrollBy(deltaY);
                }
            }
            updateUI();
            return;
        }
        mScrollState = SCROLL_STATE_IDLE;
        executeCallbackWhileNotScroll();
    }

    /**
     * 在UI滚动结束以后才执行点击的回调
     */
    protected void executeCallbackWhileNotScroll() {
        if (mPendingCell != null) {
            if (mScroller.isFinished() || !mScroller.computeScrollOffset()) {
                mHandler.removeCallbacksAndMessages(null); // 先移除所有未执行的任务
                mHandler.postDelayed(this, CALLBACK_DELAY);
            }
        }
    }

    /**
     * 返回最小滚动值
     */
    private int getMinScrollX() {
        return Math.min(mRectBounds.width() - mContentWidth, 0);
    }

    /**
     * 返回最大滚动值
     */
    private int getMaxScrollX() {
        return 0;
    }

    /**
     * 返回最小滚动值
     */
    private int getMinScrollY() {
        return Math.min(mRectBounds.height() - mContentHeight, 0);
    }

    /**
     * 返回最大滚动值
     */
    private int getMaxScrollY() {
        return 0;
    }
    
    /**
     * 滚动deltaX
     *
     * @param deltaX 偏移量
     */
    private void scrollBy(int deltaX) {
        if (mOrientation == ORIENTATION_HORIZONTAL) {
            mScrollX += deltaX;
            int leftBound = getMinScrollX();
            int rightBound = getMaxScrollX();
            if (mScrollX < leftBound) {
                mScrollX = leftBound;
            } else if (mScrollX > rightBound) {
                mScrollX = rightBound;
            }
        } else {
            mScrollY += deltaX;
            int topBound = getMinScrollY();
            int bottomBound = getMaxScrollY();
            if (mScrollY < topBound) {
                mScrollY = topBound;
            } else if (mScrollY > bottomBound) {
                mScrollY = bottomBound;
            }
        }
    }

    /**
     * 绘制内容
     */
    private void onDraw(Canvas c) {
        int size = mCells.size();
        for (int i = 0; i < size; i++) {
            T element = mCells.get(i);
            if (element == null) {
                continue;
            }

            element.draw(c);
        }
    }

    /**
     * 处理Touch事件
     */
    @Override
    public final boolean onTouchEvent(MotionEvent event) {
        int x = (int) event.getX();
        int y = (int) event.getY();

        if (event.getAction() == MotionEvent.ACTION_DOWN) {
            if (mRectBounds.contains(x, y)) {
                mIsTouched = true;
            } else {
                return false; // down在区域外，不处理
            }
        } else {
            // 其他事件，只有down在区域内才向下传递
            if (!mIsTouched) {
                return false;
            }
        }

        if (mVelocityTracker == null) {
            mVelocityTracker = VelocityTracker.obtain();
        }
        mVelocityTracker.addMovement(event);

        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                mScrollState = SCROLL_STATE_IDLE;
                mIsBeingDragged = false;
                mInitialMotionX = x;
                mInitialMotionY = y;
                mScroller.forceFinished(true);
                mCurrentSelectIdx = findPressed(x, y);
                setPressed(mCurrentSelectIdx, true);
                updateUI();
                break;
            case MotionEvent.ACTION_UP:
                mIsTouched = false;
                if (mIsBeingDragged) {
                    final VelocityTracker velocityTracker = mVelocityTracker;
                    velocityTracker.computeCurrentVelocity(1000, mMaximumVelocity);
                    int velocityX = (int) velocityTracker.getXVelocity();
                    int velocityY = (int) velocityTracker.getYVelocity();
                    boolean needFling =
                            (mOrientation == ORIENTATION_HORIZONTAL && Math.abs(velocityX) > mMinimumVelocity
                                     && Math.abs(x - mInitialMotionX) > mFlingDistance)
                                    || (mOrientation == ORIENTATION_VERTICAL && Math.abs(velocityY) > mMinimumVelocity
                                                && Math.abs(x - mInitialMotionY) > mFlingDistance);
                    if (needFling) {
                        mScrollState = SCROLL_STATE_SETTLING;

                        final int startX = mScrollX;
                        final int startY = mScrollY;
                        final int minX = getMinScrollX();
                        final int maxX = getMaxScrollX();
                        final int minY = getMinScrollY();
                        final int maxY = getMaxScrollY();

                        if (mOrientation == ORIENTATION_HORIZONTAL) {
                            mScroller.fling(startX, 0, velocityX, 0, minX, maxX, 0, 0);
                        } else {
                            mScroller.fling(0, startY, 0, startY, 0, 0, minY, maxY);
                        }
                    } else {
                        mScroller.forceFinished(true);
                        mScrollState = SCROLL_STATE_IDLE;
                    }
                } else {
                    if (mCurrentSelectIdx >= 0 && mCurrentSelectIdx == findPressed(x, y)) {
                        T cell = getCell(mCurrentSelectIdx);
                        if (null != cell) {
                            // 只有当前选中的和之前选中的不一致时才取消之前的
                            if (mSelectedIdx != mCurrentSelectIdx) {
                                unlockCell(mSelectedIdx);
                                mSelectedIdx = -1;
                            }

                            mPendingCell = cell;

                            scrollToCell(cell);
                        }
                    }
                }

                unlockCell();
                endDrag();
                break;
            case MotionEvent.ACTION_CANCEL:
                mIsTouched = false;
                mScroller.forceFinished(true);
                mScrollState = SCROLL_STATE_IDLE;
                unlockCell();
                endDrag();
                break;
            case MotionEvent.ACTION_MOVE:
                if (!mIsBeingDragged) {
                    int xDiff = Math.abs(x - mLastMotionX);
                    int yDiff = Math.abs(y - mLastMotionY);

                    if (mOrientation == ORIENTATION_HORIZONTAL) {
                        if (xDiff > mTouchSlop && xDiff > yDiff) {
                            mIsBeingDragged = true;
                            mScrollState = SCROLL_STATE_DRAGGING;
                            unlockCell();
                        }
                    } else {
                        if (yDiff > mTouchSlop && yDiff > xDiff) {
                            mIsBeingDragged = true;
                            mScrollState = SCROLL_STATE_DRAGGING;
                            unlockCell();
                        }
                    }

                }

                if (mIsBeingDragged) {
                    if (mOrientation == ORIENTATION_HORIZONTAL) {
                        scrollBy(x - mLastMotionX);
                    } else {
                        scrollBy(y - mLastMotionY);
                    }
                    updateUI();
                }
                break;
            default:
                break;
        }
        mLastMotionX = x;
        mLastMotionY = y;
        return true;
    }

    /**
     * 锁住下按词语，返回它的offset
     *
     * @param x x坐标
     * @param y y坐标
     *
     * @return 下标
     */
    private int findPressed(int x, int y) {
        int selectWordIdx = -1;
        x -= mScrollX;
        y -= mScrollY;
        int size = mCells.size();
        for (int i = 0; i < size; i++) {
            T word = mCells.get(i);
            if (word == null) {
                continue;
            }

            if (word.contains(x, y)) {
                selectWordIdx = i;
                break;
            }
        }
        return selectWordIdx;
    }

    /**
     * 设置下按效果
     *
     * @param index   下标
     * @param pressed true:下按
     */
    private void setPressed(int index, boolean pressed) {
        T word = getCell(index);
        if (null != word) {
            word.setPressed(pressed);
        }
    }

    /**
     * 取消下按状态
     */
    private void unlockCell() {
        if (mSelectedIdx != mCurrentSelectIdx) {
            unlockCell(mCurrentSelectIdx);
        }
    }

    /**
     * 取消下按状态
     */
    private void unlockCell(int index) {
        T word = getCell(index);
        if (null != word) {
            word.setPressed(false);
        }
    }

    /**
     * 结束拖动
     */
    private void endDrag() {
        mIsBeingDragged = false;
        if (mVelocityTracker != null) {
            mVelocityTracker.recycle();
            mVelocityTracker = null;
        }

        updateUI();
    }

    /**
     * 获取指定索引的词
     */
    private T getCell(int index) {
        if (mCells == null) {
            return null;
        }
        if (index < 0 || index >= mCells.size()) {
            return null;
        }

        return mCells.get(index);
    }

    /**
     * 滚动到指定的词
     *
     * @param cell 指定词
     */
    private void scrollToCell(T cell) {
        if (null == cell) {
            return;
        }
        int dx = 0;
        if (mOrientation == ORIENTATION_HORIZONTAL) {
            if (mScrollX + cell.getLeft() < mRectBounds.left) {        // 往左偏，内容不可见
                dx = mRectBounds.left - mScrollX - cell.getLeft();
            } else if (mScrollX + cell.getRight() > mRectBounds.right) {        // 往右偏，内容不可见
                dx = mRectBounds.right - mScrollX - cell.getRight();
            }
        } else {
            if (mScrollY + cell.getTop() < mRectBounds.top) {        // 往左偏，内容不可见
                dx = mRectBounds.top - mScrollY - cell.getTop();
            } else if (mScrollY + cell.getBottom() > mRectBounds.bottom) {        // 往右偏，内容不可见
                dx = mRectBounds.bottom - mScrollY - cell.getBottom();
            }
        }

        if (dx != 0) {
            final int max = 250;
            int duration = Math.abs(dx);
            duration = duration > max ? max : duration;
            startScroll(dx, duration);
        }

    }

    /**
     * 开始滚动
     *
     * @param dx       x轴距离
     * @param duration 滚动时间
     */
    private void startScroll(int dx, int duration) {
        mScroller.forceFinished(true);
        if (mOrientation == ORIENTATION_HORIZONTAL) {
            mScroller.startScroll(mScrollX, 0, dx, 0, duration);
        } else {
            mScroller.startScroll(0, mScrollY, 0, dx, duration);
        }
    }

    @Override
    public final void run() {
        doAction(mPendingCell);
        mPendingCell = null;
    }

    /**
     * 重置
     */
    private void reset() {
        mCurrentSelectIdx = -1;
        mSelectedIdx = -1;
    }

    /**
     * 更新内容
     */
    @Override
    public void updateContent(List<T> result) {
        if (result == null) {
            throw new IllegalArgumentException("result can not be null");
        }
        if (result != mCells) {
            mCells.clear();

            final int size = result.size();
            for (int i = 0; i < size; i++) {
                mCells.add(result.get(i));

            }

        }
        final int size = mCells.size();
        int xos = mRectBounds.left;
        int yos = mRectBounds.top;
        reset();
        for (int i = 0; i < size; i++) {
            T str = mCells.get(i);
            if (str == null) {
                continue;
            }

            measureChild(str);
            str.layout(xos, yos, xos + str.getMeasuredWidth(), yos + str.getMeasuredHeight());
            if (mOrientation == ORIENTATION_HORIZONTAL) {
                xos += str.getMeasuredWidth();
            } else {
                yos += str.getMeasuredHeight();
            }

        }
        mContentWidth = xos;
        mContentHeight = yos;
        updateUI();
    }

    /**
     * 隐藏。界面不展示时调用此方法。
     */
    @Override
    public void hide() {
        mHandler.removeCallbacksAndMessages(null); // 移除所有的消息和runnable
        if (mScrollState != SCROLL_STATE_IDLE) {
            mScroller.forceFinished(true);
        }
        mScrollX = 0;
        reset();
        updateBounds(0, 0, 0, 0);
    }

    /**
     * 更新区域
     */
    @Override
    public void updateBounds(int left, int top, int right, int bottom) {
        mRectBounds.set(left, top, right, bottom);
        if (!mCells.isEmpty()) {
            updateContent(mCells);
        }
    }

    protected abstract void updateUI();

    protected abstract void doAction(T cell);

    protected abstract void measureChild(T cell);


}
