package com.baidu.pad.skin;

import static com.baidu.util.Consts.FKEY_SEMICOLON;

import com.baidu.pad.bean.PadKey;

import android.text.TextUtils;

/**
 * 简易的按键解析
 * Created by cdf on 17/8/8.
 */
class KeyParam {

    /**
     * 解析出一个按键的实例。示例："TEXT=Q;BOUNDS=[10,160-116,313];CENTER=Q;"
     *
     * @param keyInfo 按键的配置
     *
     * @return 按键实例
     */
    public static PadKey parse(String keyInfo) {

        String[] props = keyInfo.split(";");
        String[] keyValue;
        PadKey padKey = new PadKey();
        for (String prop : props) {
            if (!TextUtils.isEmpty(prop)) {
                keyValue = prop.split("=");
                if (keyValue.length == 2) {
                    if (!TextUtils.isEmpty(keyValue[0]) && !TextUtils.isEmpty(keyValue[1])) {
                        parseProp(padKey, keyValue[0], keyValue[1]);
                    }
                }
            }
        }
        return padKey;
    }

    private static void parseProp(PadKey padKey, String key, String value) {
        if ("TEXT".equals(key)) {
            padKey.setText(value);
        } else if ("BOUNDS".equals(key)) {
            value = value.substring(1, value.length() - 1);
            String[] bounds = value.split(",|-");
            if (bounds.length == 4) {
                int left = Integer.parseInt(bounds[0]);
                int top = Integer.parseInt(bounds[1]);
                int right = Integer.parseInt(bounds[2]);
                int bottom = Integer.parseInt(bounds[3]);
                padKey.setBounds(left, top, right, bottom);

            }
        } else if ("CENTER".equals(key)) {
            if (value.length() > 1) {
                int center = Integer.parseInt(value);
                if (center == FKEY_SEMICOLON) {
                    center = ';';
                }
                padKey.setCenter(center);
            } else {
                int center = value.charAt(0);
                padKey.setCenter(center);
            }
        } else if ("UP".equals(key)) {
            if (value.length() == 1) {
                int center = value.charAt(0);
                padKey.setUp(center);
            }
        } else if ("LEFT".equals(key)) {
            if (value.length() == 1) {
                int center = value.charAt(0);
                padKey.setLeft(center);
            }
        } else if ("DOWN".equals(key)) {
            if (value.length() == 1) {
                int center = value.charAt(0);
                padKey.setDown(center);
            }
        } else if ("RIGHT".equals(key)) {
            if (value.length() == 1) {
                int center = value.charAt(0);
                padKey.setRight(center);
            }
        }
    }
}
