/*
 * Copyright (C) 2017 Baidu, Inc. All Rights Reserved.
 */
package com.baidu.pad.skin;

import com.baidu.iptcore.ImeCoreConsts;
import com.baidu.pad.bean.PadKey;
import com.baidu.util.Consts;

import android.graphics.Rect;

/**
 * keymap布局用的工具类
 * Created by <PERSON><PERSON><PERSON><PERSON> on 17/4/13.
 */

public class KeymapArranger {

    /**
     * py9面板布局
     *
     * @return 按键总个数
     */
    public static int arrangeKeyMapPy9(PadKey[] keyList, int left, int top, int right, int bottom) {

        Rect skinPanelBounds = new Rect(0, 128, 1152, 896);
        String[] keyInfos = new String[] {
                "TEXT=;BOUNDS=[28,156-224,683];CENTER=2000;",
                "TEXT=分词;BOUNDS=[252,156-448,313];CENTER=1;UP=1;",
                "TEXT=ABC;BOUNDS=[476,156-672,313];CENTER=2;UP=2;LEFT=A;RIGHT=C;",
                "TEXT=DEF;BOUNDS=[700,156-896,313];CENTER=3;UP=3;LEFT=D;RIGHT=F;",
                "TEXT=<-;BOUNDS=[924,156-1120,313];CENTER=1000;",
                "TEXT=GHI;BOUNDS=[252,341-448,498];CENTER=4;UP=4;LEFT=G;RIGHT=I;",
                "TEXT=JKL;BOUNDS=[476,341-672,498];CENTER=5;UP=5;LEFT=J;RIGHT=L;",
                "TEXT=MNO;BOUNDS=[700,341-896,498];CENTER=6;UP=6;LEFT=M;RIGHT=O;",
                "TEXT=清空;BOUNDS=[924,341-1120,498];CENTER=1001;",
                "TEXT=PQRS;BOUNDS=[252,526-448,683];CENTER=7;UP=7;LEFT=P;RIGHT=R;DOWN=S;",
                "TEXT=TUV;BOUNDS=[476,526-672,683];CENTER=8;UP=8;LEFT=T;RIGHT=V;",
                "TEXT=WXYZ;BOUNDS=[700,526-896,683];CENTER=9;UP=9;LEFT=W;RIGHT=Y;DOWN=Z",
                "TEXT=回车;BOUNDS=[924,526-1120,868];CENTER=1003;",
                "TEXT=123;BOUNDS=[28,711-224,868];CENTER=1009;",
                "TEXT=切英;BOUNDS=[252,711-448,868];CENTER=1008;",
                "TEXT=|____|;BOUNDS=[476,711-672,868];CENTER=1002;UP=0;",
                "TEXT=符;BOUNDS=[700,711-896,868];CENTER=1010;"
        };

        int len = Math.min(keyList.length, keyInfos.length);
        float scaleW = (float) (right - left) / skinPanelBounds.width();
        float scaleH = (float) (bottom - top) / skinPanelBounds.height();
        int deltaX = left - skinPanelBounds.left;
        int deltaY = top - skinPanelBounds.top;
        for (int i = 0; i < len; i++) {
            keyList[i] = KeyParam.parse(keyInfos[i]);
            keyList[i].resize(scaleW, scaleH);
            keyList[i].offset(deltaX, deltaY);
        }

        return len;
    }

    /**
     * en9面板布局
     *
     * @return 按键总个数
     */
    public static int arrangeKeyMapEn9(PadKey[] keyList, int left, int top, int right, int bottom) {

        Rect skinPanelBounds = new Rect(0, 128, 1152, 896);
        String[] keyInfos = new String[] {
                "TEXT=;BOUNDS=[28,156-224,683];CENTER=2000;",
                "TEXT=@.../;BOUNDS=[252,156-448,313];CENTER=1;UP=1;",
                "TEXT=ABC;BOUNDS=[476,156-672,313];CENTER=2;UP=2;LEFT=A;RIGHT=C;",
                "TEXT=DEF;BOUNDS=[700,156-896,313];CENTER=3;UP=3;LEFT=D;RIGHT=F;",
                "TEXT=<-;BOUNDS=[924,156-1120,313];CENTER=1000;",
                "TEXT=GHI;BOUNDS=[252,341-448,498];CENTER=4;UP=4;LEFT=G;RIGHT=I;",
                "TEXT=JKL;BOUNDS=[476,341-672,498];CENTER=5;UP=5;LEFT=J;RIGHT=L;",
                "TEXT=MNO;BOUNDS=[700,341-896,498];CENTER=6;UP=6;LEFT=M;RIGHT=O;",
                "TEXT=清空;BOUNDS=[924,341-1120,498];CENTER=1001;",
                "TEXT=PQRS;BOUNDS=[252,526-448,683];CENTER=7;UP=7;LEFT=P;RIGHT=R;DOWN=S;",
                "TEXT=TUV;BOUNDS=[476,526-672,683];CENTER=8;UP=8;LEFT=T;RIGHT=V;",
                "TEXT=WXYZ;BOUNDS=[700,526-896,683];CENTER=9;UP=9;LEFT=W;RIGHT=Y;DOWN=Z",
                "TEXT=↑;BOUNDS=[924,526-1120,683];CENTER=1005;",
                "TEXT=切中;BOUNDS=[28,711-224,868];CENTER=1012;",
                "TEXT=abc;BOUNDS=[252,711-448,868];CENTER=1016;",
                "TEXT=|____|;BOUNDS=[476,711-672,868];CENTER=1002;",
                "TEXT=符;BOUNDS=[700,711-896,868];CENTER=1010;",
                "TEXT=回车;BOUNDS=[924,711-1120,868];CENTER=1003;",
        };

        int len = Math.min(keyList.length, keyInfos.length);
        float scaleW = (float) (right - left) / skinPanelBounds.width();
        float scaleH = (float) (bottom - top) / skinPanelBounds.height();
        int deltaX = left - skinPanelBounds.left;
        int deltaY = top - skinPanelBounds.top;
        for (int i = 0; i < len; i++) {
            keyList[i] = KeyParam.parse(keyInfos[i]);
            keyList[i].resize(scaleW, scaleH);
            keyList[i].offset(deltaX, deltaY);
        }

        return len;
    }

    /**
     * 笔画面板布局
     *
     * @return 按键总个数
     */
    public static int arrangeKeyMapBh9(PadKey[] keyList, int left, int top, int right, int bottom) {

        Rect skinPanelBounds = new Rect(0, 128, 1152, 896);
        String[] keyInfos = new String[] {
                "TEXT=;BOUNDS=[28,156-224,683];CENTER=2000;",
                "TEXT=-;BOUNDS=[252,156-448,313];CENTER=1193;UP=1;",
                "TEXT=|;BOUNDS=[476,156-672,313];CENTER=1194;UP=2;",
                "TEXT=丿;BOUNDS=[700,156-896,313];CENTER=1195;UP=3;",
                "TEXT=<-;BOUNDS=[924,156-1120,313];CENTER=1000;",
                "TEXT=、;BOUNDS=[252,341-448,498];CENTER=1196;UP=4;",
                "TEXT=ㄥ;BOUNDS=[476,341-672,498];CENTER=1197;UP=5;",
                "TEXT=通配;BOUNDS=[700,341-896,498];CENTER=1198;UP=6;",
                "TEXT=清空;BOUNDS=[924,341-1120,498];CENTER=1001;",
                "TEXT=分词;BOUNDS=[252,526-448,683];CENTER=';UP=7;",
                "TEXT=：;BOUNDS=[476,526-672,683];CENTER=：;UP=8;",
                "TEXT=；;BOUNDS=[700,526-896,683];CENTER=；;UP=9;",
                "TEXT=123;BOUNDS=[28,711-224,868];CENTER=1009;",
                "TEXT=切中;BOUNDS=[252,711-448,868];CENTER=1012;",
                "TEXT=|____|;BOUNDS=[476,711-672,868];CENTER=1002;UP=0;",
                "TEXT=符;BOUNDS=[700,711-896,868];CENTER=1010;",
                "TEXT=回车;BOUNDS=[924,526-1120,868];CENTER=1003;"
        };
        int len = Math.min(keyList.length, keyInfos.length);
        float scaleW = (float) (right - left) / skinPanelBounds.width();
        float scaleH = (float) (bottom - top) / skinPanelBounds.height();
        int deltaX = left - skinPanelBounds.left;
        int deltaY = top - skinPanelBounds.top;
        for (int i = 0; i < len; i++) {
            keyList[i] = KeyParam.parse(keyInfos[i]);
            keyList[i].resize(scaleW, scaleH);
            keyList[i].offset(deltaX, deltaY);
        }

        return len;

    }

    /**
     * Num9面板布局
     *
     * @return 按键总个数
     */
    public static int arrangeKeyMapNum9(PadKey[] keyList, int left, int top, int right, int bottom) {

        Rect skinPanelBounds = new Rect(0, 128, 1152, 896);
        String[] keyInfos = new String[] {
                "TEXT=;BOUNDS=[28,156-224,683];CENTER=2000;",
                "TEXT=1;BOUNDS=[252,156-448,313];CENTER=1;",
                "TEXT=2;BOUNDS=[476,156-672,313];CENTER=2;",
                "TEXT=3;BOUNDS=[700,156-896,313];CENTER=3;",
                "TEXT=<-;BOUNDS=[924,156-1120,313];CENTER=1000;",
                "TEXT=4;BOUNDS=[252,341-448,498];CENTER=4;",
                "TEXT=5;BOUNDS=[476,341-672,498];CENTER=5;",
                "TEXT=6;BOUNDS=[700,341-896,498];CENTER=6;",
                "TEXT=@;BOUNDS=[924,341-1120,498];CENTER=@;",
                "TEXT=7;BOUNDS=[252,526-448,683];CENTER=7;",
                "TEXT=8;BOUNDS=[476,526-672,683];CENTER=8;",
                "TEXT=9;BOUNDS=[700,526-896,683];CENTER=9;",
                "TEXT=回车;BOUNDS=[924,526-1120,868];CENTER=1003;",
                "TEXT=返回;BOUNDS=[28,711-224,868];CENTER=1004;",
                "TEXT=空格;BOUNDS=[252,711-448,868];CENTER=1002;",
                "TEXT=0;BOUNDS=[476,711-672,868];CENTER=0;",
                "TEXT=符;BOUNDS=[700,711-896,868];CENTER=1010;"
        };

        int len = Math.min(keyList.length, keyInfos.length);
        float scaleW = (float) (right - left) / skinPanelBounds.width();
        float scaleH = (float) (bottom - top) / skinPanelBounds.height();
        int deltaX = left - skinPanelBounds.left;
        int deltaY = top - skinPanelBounds.top;
        for (int i = 0; i < len; i++) {
            keyList[i] = KeyParam.parse(keyInfos[i]);
            keyList[i].resize(scaleW, scaleH);
            keyList[i].offset(deltaX, deltaY);
        }

        return len;
    }

    /**
     * 切换到手写面板
     */
    public static int arrangeKeyMapHw(PadKey[] keyList, int left, int top, int right, int bottom) {

        int width = right - left;
        int height = (bottom - top);
        int keyWidth = width / 6;
        int keyHeight = height / 5;
        int keyNum = 0;

        // 手写区域
        PadKey key = new PadKey();
        key.mKeyid = Consts.FKEY_HANDWRITING;
        key.setBounds(left, top, right, bottom - keyHeight);
        keyList[keyNum++] = key;

        // 底部按键
        String[] textList = {"，", "。", "|____|", "<-", "回车"};
        int[] keyIds = {'，', '。', 1002, 1000, 1003};
        int[] widths = new int[] {keyWidth, keyWidth, keyWidth * 2, keyWidth, keyWidth};
        int leftStart = left;
        int topStart = bottom - keyHeight;
        for (int i = 0; i < 5; i++) {
            key = new PadKey();
            key.mTxt = textList[i];
            key.mKeyid = keyIds[i];
            key.setBounds(leftStart, topStart, leftStart + widths[i], topStart + keyHeight);
            leftStart += widths[i];
            keyList[keyNum++] = key;
        }

        return keyNum;
    }

    /**
     * en26面板布局
     */
    public static int arrangeKeyMapEn26(PadKey[] keyList, int left, int top, int right, int bottom) {

        Rect skinPanelBounds = new Rect(0, 128, 1152, 896);
        String[] keyInfos = new String[] {
                "TEXT=Q;BOUNDS=[10,160-116,313];CENTER=Q;UP=1;",
                "TEXT=W;BOUNDS=[124,160-230,313];CENTER=W;UP=2;",
                "TEXT=E;BOUNDS=[238,160-344,313];CENTER=E;UP=3;",
                "TEXT=R;BOUNDS=[352,160-458,313];CENTER=R;UP=4;",
                "TEXT=T;BOUNDS=[466,160-572,313];CENTER=T;UP=5;",
                "TEXT=Y;BOUNDS=[580,160-686,313];CENTER=Y;UP=6;",
                "TEXT=U;BOUNDS=[694,160-800,313];CENTER=U;UP=7;",
                "TEXT=I;BOUNDS=[808,160-914,313];CENTER=I;UP=8;",
                "TEXT=O;BOUNDS=[922,160-1028,313];CENTER=O;UP=9;",
                "TEXT=P;BOUNDS=[1036,160-1142,313];CENTER=P;UP=0;",
                "TEXT=A;BOUNDS=[67,344-173,497];CENTER=A;UP=!;",
                "TEXT=S;BOUNDS=[181,344-287,497];CENTER=S;UP=@;",
                "TEXT=D;BOUNDS=[295,344-401,497];CENTER=D;UP=#;",
                "TEXT=F;BOUNDS=[409,344-515,497];CENTER=F;UP=$;",
                "TEXT=G;BOUNDS=[523,344-629,497];CENTER=G;UP=%;",
                "TEXT=H;BOUNDS=[637,344-743,497];CENTER=H;UP=&;",
                "TEXT=J;BOUNDS=[751,344-857,497];CENTER=J;UP=*;",
                "TEXT=K;BOUNDS=[865,344-971,497];CENTER=K;UP=(;",
                "TEXT=L;BOUNDS=[979,344-1085,497];CENTER=L;UP=);",
                "TEXT=Z;BOUNDS=[181,528-287,681];CENTER=Z;UP=';",
                "TEXT=X;BOUNDS=[295,528-401,681];CENTER=X;UP=/;",
                "TEXT=C;BOUNDS=[409,528-515,681];CENTER=C;UP=-;",
                "TEXT=V;BOUNDS=[523,528-629,681];CENTER=V;UP=_;",
                "TEXT=B;BOUNDS=[637,528-743,681];CENTER=B;UP=:;",
                "TEXT=N;BOUNDS=[751,528-857,681];CENTER=N;",
                "TEXT=M;BOUNDS=[865,528-971,681];CENTER=M;UP=?;",
                "TEXT=Shift;BOUNDS=[10,528-165,681];CENTER=1005;",
                "TEXT=Back;BOUNDS=[987,528-1142,681];CENTER=1000;",
                "TEXT=中;BOUNDS=[17,712-148,865];CENTER=1011;",
                "TEXT=abc;BOUNDS=[158,712-289,865];CENTER=1016;",
                "TEXT=123;BOUNDS=[299,712-430,865];CENTER=1009;",
                "TEXT=|_____|;BOUNDS=[440,712-732,865];CENTER=1002;",
                "TEXT=, .;BOUNDS=[742,712-873,865];CENTER=1010;",
                "TEXT=Enter;BOUNDS=[883,712-1145,865];CENTER=1003;",
        };
        int len = Math.min(keyList.length, keyInfos.length);
        float scaleW = (float) (right - left) / skinPanelBounds.width();
        float scaleH = (float) (bottom - top) / skinPanelBounds.height();
        int deltaX = left - skinPanelBounds.left;
        int deltaY = top - skinPanelBounds.top;
        for (int i = 0; i < len; i++) {
            keyList[i] = KeyParam.parse(keyInfos[i]);
            keyList[i].resize(scaleW, scaleH);
            keyList[i].offset(deltaX, deltaY);
        }

        return len;
    }

    /**
     * en26面板布局
     */
    public static int arrangeKeyMapPy26(PadKey[] keyList, int left, int top, int right, int bottom) {

        Rect skinPanelBounds = new Rect(0, 128, 1152, 896);
        String[] keyInfos = new String[] {
                "TEXT=q;BOUNDS=[10,160-116,313];CENTER=q;UP=1;",
                "TEXT=w;BOUNDS=[124,160-230,313];CENTER=w;UP=2;",
                "TEXT=e;BOUNDS=[238,160-344,313];CENTER=e;UP=3;",
                "TEXT=r;BOUNDS=[352,160-458,313];CENTER=r;UP=4;",
                "TEXT=t;BOUNDS=[466,160-572,313];CENTER=t;UP=5;",
                "TEXT=y;BOUNDS=[580,160-686,313];CENTER=y;UP=6;",
                "TEXT=u;BOUNDS=[694,160-800,313];CENTER=u;UP=7;",
                "TEXT=i;BOUNDS=[808,160-914,313];CENTER=i;UP=8;",
                "TEXT=o;BOUNDS=[922,160-1028,313];CENTER=o;UP=9;",
                "TEXT=p;BOUNDS=[1036,160-1142,313];CENTER=p;UP=0;",
                "TEXT=a;BOUNDS=[67,344-173,497];CENTER=a;UP=~;",
                "TEXT=s;BOUNDS=[181,344-287,497];CENTER=s;UP=@;",
                "TEXT=d;BOUNDS=[295,344-401,497];CENTER=d;UP=#;",
                "TEXT=f;BOUNDS=[409,344-515,497];CENTER=f;UP=$;",
                "TEXT=g;BOUNDS=[523,344-629,497];CENTER=g;UP=%;",
                "TEXT=h;BOUNDS=[637,344-743,497];CENTER=h;UP=&;",
                "TEXT=j;BOUNDS=[751,344-857,497];CENTER=j;UP=*;",
                "TEXT=k;BOUNDS=[865,344-971,497];CENTER=k;UP=(;",
                "TEXT=l;BOUNDS=[979,344-1085,497];CENTER=l;UP=);",
                "TEXT=z;BOUNDS=[181,528-287,681];CENTER=z;UP=';",
                "TEXT=x;BOUNDS=[295,528-401,681];CENTER=x;UP=/;",
                "TEXT=c;BOUNDS=[409,528-515,681];CENTER=c;UP=-;",
                "TEXT=v;BOUNDS=[523,528-629,681];CENTER=v;UP=_;",
                "TEXT=b;BOUNDS=[637,528-743,681];CENTER=b;UP=：;",
                "TEXT=n;BOUNDS=[751,528-857,681];CENTER=n;UP=；;",
                "TEXT=m;BOUNDS=[865,528-971,681];CENTER=m;UP=、;",
                "TEXT=Shift;BOUNDS=[10,528-165,681];CENTER=1006;",
                "TEXT=Back;BOUNDS=[987,528-1142,681];CENTER=1000;",
                "TEXT=ABC;BOUNDS=[17,712-148,865];CENTER=1008;",
                "TEXT=...;BOUNDS=[158,712-289,865];CENTER=1010;",
                "TEXT=123;BOUNDS=[299,712-430,865];CENTER=1009;",
                "TEXT=|_____|;BOUNDS=[440,712-732,865];CENTER=1002;",
                "TEXT=, .;BOUNDS=[742,712-873,865];CENTER=，;UP=。",
                "TEXT=Enter;BOUNDS=[883,712-1145,865];CENTER=1003;",
        };
        int len = Math.min(keyList.length, keyInfos.length);
        float scaleW = (float) (right - left) / skinPanelBounds.width();
        float scaleH = (float) (bottom - top) / skinPanelBounds.height();
        for (int i = 0; i < len; i++) {
            keyList[i] = KeyParam.parse(keyInfos[i]);
            keyList[i].offset(-skinPanelBounds.left, -skinPanelBounds.top);
            keyList[i].resize(scaleW, scaleH);
            keyList[i].offset(left, top);
        }

        return len;
    }

    /**
     * 符号面板布局
     */
    public static int arrangeKeyMapSym(PadKey[] keyList, int left, int top, int right, int bottom) {

        Rect skinPanelBounds = new Rect(0, 0, 1152, 728);
        String[] keyInfos = new String[] {
                "TEXT=;BOUNDS=[0,0-230,600];CENTER=2000;",
                "TEXT=锁定;BOUNDS=[2,600-230,728];CENTER=1012;",
                "TEXT=/\\;BOUNDS=[232,600-460,728];CENTER=1013;",
                "TEXT=\\/;BOUNDS=[462,600-690,728];CENTER=1014;",
                "TEXT=<-;BOUNDS=[692,600-920,728];CENTER=1000;",
                "TEXT=Return;BOUNDS=[922,600-1150,728];CENTER=1004;",
                "TEXT=;BOUNDS=[230,0-1152,300];CENTER=2002;",
        };
        int len = Math.min(keyList.length, keyInfos.length);
        float scaleW = (float) (right - left) / skinPanelBounds.width();
        float scaleH = (float) (bottom - top) / skinPanelBounds.height();
        for (int i = 0; i < len; i++) {
            keyList[i] = KeyParam.parse(keyInfos[i]);
            keyList[i].offset(-skinPanelBounds.left, -skinPanelBounds.top);
            keyList[i].resize(scaleW, scaleH);
            keyList[i].offset(left, top);
        }
        return len;
    }

    /**
     * 切换到五笔26键面板
     */
    public static int arrangeKeyMapWb26(PadKey[] keyList, int left, int top, int right, int bottom) {
        Rect skinPanelBounds = new Rect(0, 128, 1152, 896);
        String[] keyInfos = new String[] {
                "TEXT=Q;BOUNDS=[10,160-116,313];CENTER=Q;UP=1;",
                "TEXT=W;BOUNDS=[124,160-230,313];CENTER=W;UP=2;",
                "TEXT=E;BOUNDS=[238,160-344,313];CENTER=E;UP=3;",
                "TEXT=R;BOUNDS=[352,160-458,313];CENTER=R;UP=4;",
                "TEXT=T;BOUNDS=[466,160-572,313];CENTER=T;UP=5;",
                "TEXT=Y;BOUNDS=[580,160-686,313];CENTER=Y;UP=6;",
                "TEXT=U;BOUNDS=[694,160-800,313];CENTER=U;UP=7;",
                "TEXT=I;BOUNDS=[808,160-914,313];CENTER=I;UP=8;",
                "TEXT=O;BOUNDS=[922,160-1028,313];CENTER=O;UP=9;",
                "TEXT=P;BOUNDS=[1036,160-1142,313];CENTER=P;UP=0;",
                "TEXT=A;BOUNDS=[67,344-173,497];CENTER=A;UP=~;",
                "TEXT=S;BOUNDS=[181,344-287,497];CENTER=S;UP=@;",
                "TEXT=D;BOUNDS=[295,344-401,497];CENTER=D;UP=#;",
                "TEXT=F;BOUNDS=[409,344-515,497];CENTER=F;UP=$;",
                "TEXT=G;BOUNDS=[523,344-629,497];CENTER=G;UP=%;",
                "TEXT=H;BOUNDS=[637,344-743,497];CENTER=H;UP=&;",
                "TEXT=J;BOUNDS=[751,344-857,497];CENTER=J;UP=*;",
                "TEXT=K;BOUNDS=[865,344-971,497];CENTER=K;UP=(;",
                "TEXT=L;BOUNDS=[979,344-1085,497];CENTER=L;UP=);",
                "TEXT=Z;BOUNDS=[181,528-287,681];CENTER=Z;UP=';",
                "TEXT=X;BOUNDS=[295,528-401,681];CENTER=X;UP=/;",
                "TEXT=C;BOUNDS=[409,528-515,681];CENTER=C;UP=-;",
                "TEXT=V;BOUNDS=[523,528-629,681];CENTER=V;UP=_;",
                "TEXT=B;BOUNDS=[637,528-743,681];CENTER=B;UP=：;",
                "TEXT=N;BOUNDS=[751,528-857,681];CENTER=N;UP=；;",
                "TEXT=M;BOUNDS=[865,528-971,681];CENTER=M;UP=、;",
                "TEXT=shift;BOUNDS=[10,528-165,681];CENTER=1005;",
                "TEXT=Back;BOUNDS=[987,528-1142,681];CENTER=1000;",
                "TEXT=ABC;BOUNDS=[17,712-148,865];CENTER=1008;",
                "TEXT=123;BOUNDS=[158,712-289,865];CENTER=1009;",
                "TEXT=,!;BOUNDS=[299,712-430,865];CENTER=，;UP=！;",
                "TEXT=|_____|;BOUNDS=[440,712-732,865];CENTER=1002;",
                "TEXT=符;BOUNDS=[742,712-873,865];CENTER=1010;",
                "TEXT=Enter;BOUNDS=[883,712-1145,865];CENTER=1003;",
        };
        int len = Math.min(keyList.length, keyInfos.length);
        float scaleW = (float) (right - left) / skinPanelBounds.width();
        float scaleH = (float) (bottom - top) / skinPanelBounds.height();
        for (int i = 0; i < len; i++) {
            keyList[i] = KeyParam.parse(keyInfos[i]);
            keyList[i].offset(-skinPanelBounds.left, -skinPanelBounds.top);
            keyList[i].resize(scaleW, scaleH);
            keyList[i].offset(left, top);
        }

        return len;
    }

    /**
     * 切换到仓颉面板
     */
    public static int arrangeKeyMapCangjie(PadKey[] keyList, int left, int top, int right, int bottom) {
        Rect skinPanelBounds = new Rect(0, 128, 1152, 896);
        String[] keyInfos = new String[] {
                "TEXT=手;BOUNDS=[10,160-116,313];CENTER=Q;UP=1;",
                "TEXT=田;BOUNDS=[124,160-230,313];CENTER=W;UP=2;",
                "TEXT=水;BOUNDS=[238,160-344,313];CENTER=E;UP=3;",
                "TEXT=口;BOUNDS=[352,160-458,313];CENTER=R;UP=4;",
                "TEXT=廿;BOUNDS=[466,160-572,313];CENTER=T;UP=5;",
                "TEXT=卜;BOUNDS=[580,160-686,313];CENTER=Y;UP=6;",
                "TEXT=山;BOUNDS=[694,160-800,313];CENTER=U;UP=7;",
                "TEXT=戈;BOUNDS=[808,160-914,313];CENTER=I;UP=8;",
                "TEXT=人;BOUNDS=[922,160-1028,313];CENTER=O;UP=9;",
                "TEXT=心;BOUNDS=[1036,160-1142,313];CENTER=P;UP=0;",
                "TEXT=日;BOUNDS=[67,344-173,497];CENTER=A;UP=~;",
                "TEXT=尸;BOUNDS=[181,344-287,497];CENTER=S;UP=@;",
                "TEXT=木;BOUNDS=[295,344-401,497];CENTER=D;UP=#;",
                "TEXT=火;BOUNDS=[409,344-515,497];CENTER=F;UP=$;",
                "TEXT=土;BOUNDS=[523,344-629,497];CENTER=G;UP=%;",
                "TEXT=竹;BOUNDS=[637,344-743,497];CENTER=H;UP=&;",
                "TEXT=十;BOUNDS=[751,344-857,497];CENTER=J;UP=*;",
                "TEXT=大;BOUNDS=[865,344-971,497];CENTER=K;UP=(;",
                "TEXT=中;BOUNDS=[979,344-1085,497];CENTER=L;UP=);",
                "TEXT=？;BOUNDS=[181,528-287,681];CENTER=Z;UP=';",
                "TEXT=難;BOUNDS=[295,528-401,681];CENTER=X;UP=/;",
                "TEXT=金;BOUNDS=[409,528-515,681];CENTER=C;UP=-;",
                "TEXT=女;BOUNDS=[523,528-629,681];CENTER=V;UP=_;",
                "TEXT=月;BOUNDS=[637,528-743,681];CENTER=B;UP=：;",
                "TEXT=弓;BOUNDS=[751,528-857,681];CENTER=N;UP=；;",
                "TEXT=一;BOUNDS=[865,528-971,681];CENTER=M;UP=、;",
                "TEXT=仓/速;BOUNDS=[10,528-165,681];CENTER=2003;",
                "TEXT=<-;BOUNDS=[987,528-1142,681];CENTER=1000;",
                "TEXT=ABC;BOUNDS=[17,712-148,865];CENTER=1008;",
                "TEXT=123;BOUNDS=[158,712-289,865];CENTER=1009;",
                "TEXT=,!;BOUNDS=[299,712-430,865];CENTER=，;UP=！;",
                "TEXT=|_____|;BOUNDS=[440,712-732,865];CENTER=1002;",
                "TEXT=符;BOUNDS=[742,712-873,865];CENTER=1010;",
                "TEXT=Enter;BOUNDS=[883,712-1145,865];CENTER=1003;",
        };
        int len = Math.min(keyList.length, keyInfos.length);
        float scaleW = (float) (right - left) / skinPanelBounds.width();
        float scaleH = (float) (bottom - top) / skinPanelBounds.height();
        for (int i = 0; i < len; i++) {
            keyList[i] = KeyParam.parse(keyInfos[i]);
            keyList[i].offset(-skinPanelBounds.left, -skinPanelBounds.top);
            keyList[i].resize(scaleW, scaleH);
            keyList[i].offset(left, top);
        }

        return len;
    }

    /**
     * 切换到注音面板
     */
    public static int arrangeKeyMapZhuyin(PadKey[] keyList, int left, int top, int right, int bottom) {
        Rect skinPanelBounds = new Rect(0, 128, 1152, 1080);
        String[] keyInfos = new String[] {
                "TEXT=ㄅ;BOUNDS=[10,160-116,313];CENTER=1;",
                "TEXT=ㄉ;BOUNDS=[124,160-230,313];CENTER=2;",
                "TEXT=ˇ;BOUNDS=[238,160-344,313];CENTER=3;",
                "TEXT=ˋ;BOUNDS=[352,160-458,313];CENTER=4;",
                "TEXT=ㄓ;BOUNDS=[466,160-572,313];CENTER=5;",
                "TEXT=ˊ;BOUNDS=[580,160-686,313];CENTER=6;",
                "TEXT=˙;BOUNDS=[694,160-800,313];CENTER=7;",
                "TEXT=ㄚ;BOUNDS=[808,160-914,313];CENTER=8;",
                "TEXT=ㄞ;BOUNDS=[922,160-1028,313];CENTER=9;",
                "TEXT=ㄢ;BOUNDS=[1036,160-1142,313];CENTER=0;",
                // line 2
                "TEXT=ㄆ;BOUNDS=[10,344-116,497];CENTER=q;",
                "TEXT=ㄊ;BOUNDS=[124,344-230,497];CENTER=w;",
                "TEXT=ㄍ;BOUNDS=[238,344-344,497];CENTER=e;",
                "TEXT=ㄐ;BOUNDS=[352,344-458,497];CENTER=r;",
                "TEXT=ㄔ;BOUNDS=[466,344-572,497];CENTER=t;",
                "TEXT=ㄗ;BOUNDS=[580,344-686,497];CENTER=y;",
                "TEXT=ㄧ;BOUNDS=[694,344-800,497];CENTER=u;",
                "TEXT=ㄛ;BOUNDS=[808,344-914,497];CENTER=i;",
                "TEXT=ㄟ;BOUNDS=[922,344-1028,497];CENTER=o;",
                "TEXT=ㄣ;BOUNDS=[1036,344-1142,497];CENTER=p;",
                // line 3
                "TEXT=ㄇ;BOUNDS=[10,528-116,681];CENTER=a;",
                "TEXT=ㄋ;BOUNDS=[124,528-230,681];CENTER=s;",
                "TEXT=ㄎ;BOUNDS=[238,528-344,681];CENTER=d;",
                "TEXT=ㄑ;BOUNDS=[352,528-458,681];CENTER=f;",
                "TEXT=ㄕ;BOUNDS=[466,528-572,681];CENTER=g;",
                "TEXT=ㄘ;BOUNDS=[580,528-686,681];CENTER=h;",
                "TEXT=ㄨ;BOUNDS=[694,528-800,681];CENTER=j;",
                "TEXT=ㄜ;BOUNDS=[808,528-914,681];CENTER=k;",
                "TEXT=ㄠ;BOUNDS=[922,528-1028,681];CENTER=l;",
                "TEXT=ㄤ;BOUNDS=[1036,528-1142,681];CENTER=2002;",
                // line 4
                "TEXT=ㄈ;BOUNDS=[10,712-116,865];CENTER=z;",
                "TEXT=ㄌ;BOUNDS=[124,712-230,865];CENTER=x;",
                "TEXT=ㄏ;BOUNDS=[238,712-344,865];CENTER=c;",
                "TEXT=ㄒ;BOUNDS=[352,712-458,865];CENTER=v;",
                "TEXT=ㄖ;BOUNDS=[466,712-572,865];CENTER=b;",
                "TEXT=ㄙ;BOUNDS=[580,712-686,865];CENTER=n;",
                "TEXT=ㄩ;BOUNDS=[694,712-800,865];CENTER=m;",
                "TEXT=ㄝ;BOUNDS=[808,712-914,865];CENTER=,;",
                "TEXT=ㄡ;BOUNDS=[922,712-1028,865];CENTER=.;",
                "TEXT=ㄥ;BOUNDS=[1036,712-1142,865];CENTER=/;",
                // line 5
                "TEXT=123;BOUNDS=[10,896-116,1049];CENTER=1009;",
                "TEXT=ABC;BOUNDS=[124,896-230,1049];CENTER=1008;",
                "TEXT=符;BOUNDS=[238,896-344,1049];CENTER=1010;",
                "TEXT=，。;BOUNDS=[352,896-458,1049];CENTER=。;UP=，;",
                "TEXT=|____|;BOUNDS=[466,896-686,1049];CENTER=1002;",
                "TEXT=ㄦ;BOUNDS=[694,896-800,1049];CENTER=-;",
                "TEXT=<-;BOUNDS=[808,896-914,1049];CENTER=1000;",
                "TEXT=回车;BOUNDS=[922,896-1142,1049];CENTER=1003;",
        };
        int len = Math.min(keyList.length, keyInfos.length);
        float scaleW = (float) (right - left) / skinPanelBounds.width();
        float scaleH = (float) (bottom - top) / skinPanelBounds.height();
        for (int i = 0; i < len; i++) {
            keyList[i] = KeyParam.parse(keyInfos[i]);
            keyList[i].offset(-skinPanelBounds.left, -skinPanelBounds.top);
            keyList[i].resize(scaleW, scaleH);
            keyList[i].offset(left, top);
        }

        return len;
    }

    /**
     * 切换到五笔9键面板
     */
    public static int arrangeKeyMapWb9(PadKey[] keyList, int left, int top, int right, int bottom) {
        Rect skinPanelBounds = new Rect(0, 128, 1152, 896);
        String[] keyInfos = new String[] {
                "TEXT=;BOUNDS=[28,156-224,683];CENTER=2000;",
                "TEXT=通配;BOUNDS=[252,156-448,313];CENTER=1;UP=1;",
                "TEXT=ABC;BOUNDS=[476,156-672,313];CENTER=2;UP=2;LEFT=A;RIGHT=C;",
                "TEXT=DEF;BOUNDS=[700,156-896,313];CENTER=3;UP=3;LEFT=D;RIGHT=F;",
                "TEXT=<-;BOUNDS=[924,156-1120,313];CENTER=1000;",
                "TEXT=GHI;BOUNDS=[252,341-448,498];CENTER=4;UP=4;LEFT=G;RIGHT=I;",
                "TEXT=JKL;BOUNDS=[476,341-672,498];CENTER=5;UP=5;LEFT=J;RIGHT=L;",
                "TEXT=MNO;BOUNDS=[700,341-896,498];CENTER=6;UP=6;LEFT=M;RIGHT=O;",
                "TEXT=清空;BOUNDS=[924,341-1120,498];CENTER=1001;",
                "TEXT=PQRS;BOUNDS=[252,526-448,683];CENTER=7;UP=7;LEFT=P;RIGHT=R;DOWN=S;",
                "TEXT=TUV;BOUNDS=[476,526-672,683];CENTER=8;UP=8;LEFT=T;RIGHT=V;",
                "TEXT=WXYZ;BOUNDS=[700,526-896,683];CENTER=9;UP=9;LEFT=W;RIGHT=Y;DOWN=Z",
                "TEXT=回车;BOUNDS=[924,526-1120,868];CENTER=1003;",
                "TEXT=123;BOUNDS=[28,711-224,868];CENTER=1009;",
                "TEXT=切英;BOUNDS=[252,711-448,868];CENTER=1008;",
                "TEXT=|____|;BOUNDS=[476,711-672,868];CENTER=1002;UP=0;",
                "TEXT=符;BOUNDS=[700,711-896,868];CENTER=1010;"
        };

        int len = Math.min(keyList.length, keyInfos.length);
        float scaleW = (float) (right - left) / skinPanelBounds.width();
        float scaleH = (float) (bottom - top) / skinPanelBounds.height();
        int deltaX = left - skinPanelBounds.left;
        int deltaY = top - skinPanelBounds.top;
        for (int i = 0; i < len; i++) {
            keyList[i] = KeyParam.parse(keyInfos[i]);
            keyList[i].resize(scaleW, scaleH);
            keyList[i].offset(deltaX, deltaY);
        }

        return len;
    }
}