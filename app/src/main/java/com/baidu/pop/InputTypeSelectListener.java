/*
 * Copyright (C) 2017 Baidu, Inc. All Rights Reserved.
 */
package com.baidu.pop;

import com.baidu.iptandroiddemo.ConfigActivity;
import com.baidu.iptandroiddemo.R;
import com.baidu.keymap.switcher.InputStatConsts;
import com.baidu.keymap.switcher.KeymapSwitcherFactory;
import com.baidu.util.PopServiceWrapper;
import com.citrus.popinn.PopHandle;

import android.content.Intent;
import android.view.View;

/**
 * 输入方式选择浮层的处理逻辑
 * 
 * Created by <PERSON><PERSON><PERSON><PERSON> on 17/4/12.
 */
public class InputTypeSelectListener implements View.OnClickListener {
    
    private PopHandle mDismissHandle;

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.btn_py9:

                KeymapSwitcherFactory.Companion.getCn().switchLayout(InputStatConsts.InputType.PY,
                        InputStatConsts.LayoutType.KEYBOARD_9);
                if (mDismissHandle != null) {
                    mDismissHandle.dismiss();
                }
                break;
            case R.id.btn_py26:
                KeymapSwitcherFactory.Companion.getCn().switchLayout(InputStatConsts.InputType.PY,
                        InputStatConsts.LayoutType.KEYBOARD_26);
                if (mDismissHandle != null) {
                    mDismissHandle.dismiss();
                }
                break;
            case R.id.btn_en9:
                KeymapSwitcherFactory.Companion.getCn().switchLayout(InputStatConsts.InputType.EN,
                        InputStatConsts.LayoutType.KEYBOARD_9);
                if (mDismissHandle != null) {
                    mDismissHandle.dismiss();
                }
                break;
            case R.id.btn_en26:
                KeymapSwitcherFactory.Companion.getCn().switchLayout(InputStatConsts.InputType.EN,
                        InputStatConsts.LayoutType.KEYBOARD_26);
                if (mDismissHandle != null) {
                    mDismissHandle.dismiss();
                }
                break;
            case R.id.btn_hw:
                KeymapSwitcherFactory.Companion.getCn().switchInputType(InputStatConsts.InputType.HW);
                if (mDismissHandle != null) {
                    mDismissHandle.dismiss();
                }
                break;
            case R.id.btn_rare_hw:
                KeymapSwitcherFactory.Companion.getCn().switchInputType(InputStatConsts.InputType.RARE_HW);
                if (mDismissHandle != null) {
                    mDismissHandle.dismiss();
                }
                break;
            case R.id.btn_bh:
                KeymapSwitcherFactory.Companion.getCn().switchInputType(InputStatConsts.InputType.BH);
                if (mDismissHandle != null) {
                    mDismissHandle.dismiss();
                }
                break;
            case R.id.btn_wb9:
                KeymapSwitcherFactory.Companion.getCn().switchLayout(InputStatConsts.InputType.WB,
                        InputStatConsts.LayoutType.KEYBOARD_9);
                if (mDismissHandle != null) {
                    mDismissHandle.dismiss();
                }
                break;
            case R.id.btn_wb26:
                KeymapSwitcherFactory.Companion.getCn().switchLayout(InputStatConsts.InputType.WB,
                        InputStatConsts.LayoutType.KEYBOARD_26);
                if (mDismissHandle != null) {
                    mDismissHandle.dismiss();
                }
                break;
            case R.id.btn_zy:
                KeymapSwitcherFactory.Companion.getCn().switchInputType(InputStatConsts.InputType.ZY);
                if (mDismissHandle != null) {
                    mDismissHandle.dismiss();
                }
                break;
            case R.id.btn_cj:
                KeymapSwitcherFactory.Companion.getCn().switchInputType(InputStatConsts.InputType.CJ);
                if (mDismissHandle != null) {
                    mDismissHandle.dismiss();
                }
                break;
            case R.id.btn_setting:
                if (mDismissHandle != null) {
                    mDismissHandle.dismiss();
                }
                PopServiceWrapper.showSettingView();
                break;
            case R.id.btn_cell_setting:
                if (mDismissHandle != null) {
                    mDismissHandle.dismiss();
                }
                Intent intent = new Intent(v.getContext(), ConfigActivity.class);
                v.getContext().startActivity(intent);
                break;
            default:
                break;
        }
    }

    public void setHandle(PopHandle handle) {
        mDismissHandle = handle;
    }
}
