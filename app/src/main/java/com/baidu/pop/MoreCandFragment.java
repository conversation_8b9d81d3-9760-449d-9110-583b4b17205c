package com.baidu.pop;

import com.baidu.input.support.state.IptCandState;
import com.baidu.input.support.state.IptListState;
import com.baidu.iptandroiddemo.R;
import com.baidu.iptcore.ImeCoreConsts;
import com.baidu.keymap.switcher.InputStatConsts;
import com.baidu.keymap.switcher.KeymapSwitcherFactory;
import com.baidu.pad.MoreCandPageHandler;
import com.baidu.pad.TipState;
import com.baidu.pad.action.ImeAction;
import com.citrus.popinn.PopFragment;
import com.citrus.popinn.PopLayoutParams;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

/**
 * 更多面板的代理展示
 * Created by baidu on 17/6/21.
 */
public class MoreCandFragment extends PopFragment
        implements View.OnClickListener, MoreCandPageHandler.CandClickListener {

    private IptCandState mCandStore;
    private SubListView mSubListView;
    private MoreCandPageView mCandPageView;
    private int mPageIdx = -1;
    private Runnable mRequestCandRunnable = new Runnable() {
        @Override
        public void run() {
            ImeAction.dispatch(ImeAction.KEY_CLICK, ImeCoreConsts.FKEY_MORECAND);
            KeymapSwitcherFactory.Companion.getCn().switchInputType(InputStatConsts.InputType.MORE, false);
        }
    };

    public MoreCandFragment() {
    }

    @Override
    public View onCreateView(Context context, ViewGroup parentView) {
        View contentView = LayoutInflater.from(context).inflate(R.layout.more_cand, parentView);
        mSubListView = (SubListView) contentView.findViewById(R.id.list);
        mCandPageView = (MoreCandPageView) contentView.findViewById(R.id.cand);
        mCandPageView.setCandClickListener(this);
        
        View wordRangeView = contentView.findViewById(R.id.single);
        View arrowUpView = contentView.findViewById(R.id.arrow_up);
        View arrowDownView = contentView.findViewById(R.id.arrow_down);
        View closeView = contentView.findViewById(R.id.close);
        wordRangeView.setOnClickListener(this);
        arrowUpView.setOnClickListener(this);
        arrowDownView.setOnClickListener(this);
        closeView.setOnClickListener(this);
        
        mCandPageView.post(mRequestCandRunnable);
        return contentView;
    }

    public void update(IptCandState candStore) {
        mCandStore = candStore;
        mPageIdx = -1;
        loadNextCandPage();
    }

    public void updateList(IptListState listStore) {
        mSubListView.updateList(listStore);
    }

    /**
     * 加载cand的一页
     */
    private void loadNextCandPage() {
        int pageIdx = mPageIdx + 1;
        boolean success = mCandPageView.loadCandPage(pageIdx, mCandStore);
        if (success) {
            mPageIdx = pageIdx;
        }
    }

    /**
     * 加载cand的一页
     */
    private void loadPreviousCandPage() {
        int pageIdx = mPageIdx - 1;
        boolean success = mCandPageView.loadCandPage(pageIdx, mCandStore);
        if (success) {
            mPageIdx = pageIdx;
        }
    }

    @Override
    public PopLayoutParams onCreatePopLayoutParams() {
        return new PopLayoutParams.Builder()
                .setDimension(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
                .setLocation(0, 0, 0).build();
    }

    @Override
    public void onDestroyView() {
        mCandStore = null;
        mPageIdx = -1;
        if (mCandPageView != null) {
            mCandPageView.removeCallbacks(mRequestCandRunnable);
        }
        mCandPageView = null;
        mSubListView = null;
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.single:
                ImeAction.dispatch(ImeAction.KEY_CLICK, ImeCoreConsts.FKEY_SINGLE);
                ((TextView)v).setText(TipState.getInstance().isWordRangeSingle() ? "单字" : "多字");
                break;
            case R.id.arrow_up:
                loadPreviousCandPage();
                break;
            case R.id.arrow_down:
                loadNextCandPage();
                break;
            case R.id.close:
                mHost.dismiss();
                KeymapSwitcherFactory.Companion.getCn().returnKeyboard();
                ImeAction.dispatch(ImeAction.KEY_CLICK, ImeCoreConsts.FKEY_RETURN);
                break;
            default:
                break;
        }
    }

    @Override
    public void onCandClicked() {
        ImeAction.dispatch(ImeAction.KEY_CLICK, ImeCoreConsts.FKEY_RETURN);
        mHost.dismiss();
    }

    
}
