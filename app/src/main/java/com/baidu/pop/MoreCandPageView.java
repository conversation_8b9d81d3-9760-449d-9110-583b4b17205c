package com.baidu.pop;

import com.baidu.input.support.state.IptCandState;
import com.baidu.pad.MoreCandPageHandler;

import android.content.Context;
import android.graphics.Canvas;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;

/**
 * 更多候选字面板的候选字区域
 * 
 * Created by ch<PERSON><PERSON><PERSON> on 17/6/22.
 */
public class MoreCandPageView extends View {

    private MoreCandPageHandler mMoreCandPageHandler = new MoreCandPageHandler();

    public MoreCandPageView(Context context) {
        super(context);
    }

    public MoreCandPageView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public MoreCandPageView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }
    
    /**
     * 加载指定页的候选字
     *
     * @param pageIdx   页索引
     * @param candStore 候选字的数据封装
     *
     * @return true: 加载成功
     */
    public boolean loadCandPage(int pageIdx, IptCandState candStore) {
        mMoreCandPageHandler.loadCandPage(pageIdx, candStore);
        requestLayout();
        return true;
    }
    
    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        super.onLayout(changed, left, top, right, bottom);
        mMoreCandPageHandler.layoutCandWords(0, 0, getWidth(), getHeight());
    }

    @Override
    protected void onDraw(Canvas canvas) {
        mMoreCandPageHandler.onDraw(canvas);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (mMoreCandPageHandler.onTouchEvent(event)) {
            invalidate();
            return true;
        } else {
            return false;
        }
    }

    public void setCandClickListener(MoreCandPageHandler.CandClickListener listener) {
        mMoreCandPageHandler.setCandClickListener(listener);
    }
}
