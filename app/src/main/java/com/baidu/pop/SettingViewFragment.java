package com.baidu.pop;

import com.baidu.iptandroiddemo.R;
import com.baidu.iptcore.ImeCoreConsts;
import com.baidu.iptcore.ImeCoreManager;
import com.baidu.util.CommUtil;
import com.baidu.util.PopServiceWrapper;
import com.citrus.popinn.PopFragment;
import com.citrus.popinn.PopLayoutParams;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.SeekBar;

import static com.baidu.iptcore.ConfigKey.ACGN;
import static com.baidu.iptcore.ConfigKey.AUTOFIX;
import static com.baidu.iptcore.ConfigKey.AUTOSAVE;
import static com.baidu.iptcore.ConfigKey.BH_FIRST;
import static com.baidu.iptcore.ConfigKey.CLOUD_INPUT_TYPE;
import static com.baidu.iptcore.ConfigKey.CNEN;
import static com.baidu.iptcore.ConfigKey.EMOJI;
import static com.baidu.iptcore.ConfigKey.EMOJI_LIAN;
import static com.baidu.iptcore.ConfigKey.ENCASE;
import static com.baidu.iptcore.ConfigKey.ENSORT;
import static com.baidu.iptcore.ConfigKey.FANTI;
import static com.baidu.iptcore.ConfigKey.FASTINPUT;
import static com.baidu.iptcore.ConfigKey.HW_LEGEND_MODE;
import static com.baidu.iptcore.ConfigKey.HW_SPEED;
import static com.baidu.iptcore.ConfigKey.HW_TYPE;
import static com.baidu.iptcore.ConfigKey.LEGEND_MODE;
import static com.baidu.iptcore.ConfigKey.PHRASE;
import static com.baidu.iptcore.ConfigKey.SHUANGPIN;
import static com.baidu.iptcore.ConfigKey.SYLIAN;
import static com.baidu.iptcore.ConfigKey.WBPY;
import static com.baidu.iptcore.ConfigKey.WBTIP;
import static com.baidu.iptcore.ConfigKey.WB_SCHEMA;

/**
 * 设置项浮层的监听器
 * Created by chendanfeng on 17/6/28.
 */
public class SettingViewFragment extends PopFragment implements CompoundButton.OnCheckedChangeListener {
  
    @Override
    public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
        switch (buttonView.getId()) {
            case R.id.cb_setting_acg:
                ImeCoreManager.getConfig().setBoolean(ACGN, isChecked);
                break;
            case R.id.cb_setting_fanti:
                ImeCoreManager.getConfig().setBoolean(FANTI, isChecked);
                break;
            case R.id.cb_setting_emoji:
                ImeCoreManager.getConfig().setBoolean(EMOJI, isChecked);
                break;
            case R.id.cb_setting_emoji_lian:
                ImeCoreManager.getConfig().setBoolean(EMOJI_LIAN, isChecked);
                break;
            case R.id.cb_setting_autofix:
                ImeCoreManager.getConfig().setBoolean(AUTOFIX, isChecked);
                break;
            case R.id.cb_setting_cnen:
                ImeCoreManager.getConfig().setBoolean(CNEN, isChecked);
                break;
            case R.id.cb_setting_shuangpin:
                ImeCoreManager.getConfig().setBoolean(SHUANGPIN, isChecked);
                break;
            case R.id.cb_setting_phrase:
                ImeCoreManager.getConfig().setBoolean(PHRASE, isChecked);
                break;
            case R.id.cb_setting_bh_first:
                ImeCoreManager.getConfig().setBoolean(BH_FIRST, isChecked);
                break;
            case R.id.cb_setting_wbpy:
                ImeCoreManager.getConfig().setBoolean(WBPY, isChecked);
                break;
            default:
                break;
        }
    }

    @Override
    public View onCreateView(Context context, ViewGroup parentView) {
        View content = LayoutInflater.from(context).inflate(R.layout.setting_view, null);
        initViews(content);
        return content;
    }

    private void initViews(View content) {
        // load configuration
        boolean isShuangpinNocvt = false;
        boolean isAutoSave = ImeCoreManager.getConfig().getBoolean(AUTOSAVE);
        boolean isSylian = ImeCoreManager.getConfig().getBoolean(SYLIAN);
        boolean isFastInput = ImeCoreManager.getConfig().getBoolean(FASTINPUT);
        boolean isWbTip = ImeCoreManager.getConfig().getBoolean(WBTIP);
        int encase = ImeCoreManager.getConfig().getInt(ENCASE);
        int form = 0;
        boolean isFanti = ImeCoreManager.getConfig().getBoolean(FANTI);
        boolean isAcg = ImeCoreManager.getConfig().getBoolean(ACGN);
        boolean isEmoji = ImeCoreManager.getConfig().getBoolean(EMOJI);
        boolean isEmojiAsso = ImeCoreManager.getConfig().getBoolean(EMOJI_LIAN);
        boolean isAutofix = ImeCoreManager.getConfig().getBoolean(AUTOFIX);
        boolean isCnen = ImeCoreManager.getConfig().getBoolean(CNEN);
        boolean isShuangpin = ImeCoreManager.getConfig().getBoolean(SHUANGPIN);
        int ensort = ImeCoreManager.getConfig().getInt(ENSORT);
        boolean isPhrase = ImeCoreManager.getConfig().getBoolean(PHRASE);
        boolean isBhFirst = ImeCoreManager.getConfig().getBoolean(BH_FIRST);
        boolean isWbpy = ImeCoreManager.getConfig().getBoolean(WBPY);
        int hwType = ImeCoreManager.getConfig().getInt(HW_TYPE);
        int hwSpeed = ImeCoreManager.getConfig().getInt(HW_SPEED);
        int wbSchema = ImeCoreManager.getConfig().getInt(WB_SCHEMA);
        int legendMode = ImeCoreManager.getConfig().getInt(LEGEND_MODE);
        int hwLegendMode = ImeCoreManager.getConfig().getInt(HW_LEGEND_MODE);
        int cloudInputType = ImeCoreManager.getConfig().getInt(CLOUD_INPUT_TYPE);

        CheckBox checkBox = (CheckBox) content.findViewById(R.id.cb_setting_acg);
        checkBox.setChecked(isAcg);
        checkBox.setOnCheckedChangeListener(this);

        checkBox = (CheckBox) content.findViewById(R.id.cb_setting_shuangpinnocvt);
        checkBox.setChecked(isShuangpinNocvt);

        checkBox = (CheckBox) content.findViewById(R.id.cb_setting_autosave);
        checkBox.setChecked(isAutoSave);

        checkBox = (CheckBox) content.findViewById(R.id.cb_setting_sylian);
        checkBox.setChecked(isSylian);

        checkBox = (CheckBox) content.findViewById(R.id.cb_setting_fastinput);
        checkBox.setChecked(isFastInput);

        checkBox = (CheckBox) content.findViewById(R.id.cb_setting_wbtip);
        checkBox.setChecked(isWbTip);

        RadioGroup group = (RadioGroup) content.findViewById(R.id.encase);
        RadioButton normal = (RadioButton) content.findViewById(R.id.normal);
        RadioButton first = (RadioButton) content.findViewById(R.id.first);
        RadioButton all = (RadioButton) content.findViewById(R.id.all);
        switch (encase) {
            case ImeCoreConsts.CFG_ENCASE_NORMAL:
                normal.setChecked(true);
                first.setChecked(false);
                all.setChecked(false);
                break;
            case ImeCoreConsts.CFG_ENCASE_FIRST:
                first.setChecked(true);
                normal.setChecked(false);
                all.setChecked(false);
                break;
            case ImeCoreConsts.CFG_ENCASE_ALL:
                all.setChecked(true);
                normal.setChecked(false);
                first.setChecked(false);
                break;
            default:
                break;
        }

        group = (RadioGroup) content.findViewById(R.id.form);
        RadioButton off = (RadioButton) content.findViewById(R.id.off);
        RadioButton manual = (RadioButton) content.findViewById(R.id.manual);
        RadioButton auto = (RadioButton) content.findViewById(R.id.auto);
        switch (form) {
            case ImeCoreConsts.CFG_URLEMAIL_OFF:
                off.setChecked(true);
                manual.setChecked(false);
                auto.setChecked(false);
                break;
            case ImeCoreConsts.CFG_URLEMAIL_MANUAL:
                manual.setChecked(true);
                off.setChecked(false);
                auto.setChecked(false);
                break;
            case ImeCoreConsts.CFG_URLEMAIL_AUTO:
                auto.setChecked(true);
                off.setChecked(false);
                manual.setChecked(false);
                break;
            default:
                break;
        }

        checkBox = (CheckBox) content.findViewById(R.id.cb_setting_fanti);
        checkBox.setChecked(isFanti);
        checkBox.setOnCheckedChangeListener(this);
        
        checkBox = (CheckBox) content.findViewById(R.id.cb_setting_emoji);
        checkBox.setChecked(isEmoji);
        checkBox.setOnCheckedChangeListener(this);
        
        checkBox = (CheckBox) content.findViewById(R.id.cb_setting_emoji_lian);
        checkBox.setChecked(isEmojiAsso);
        checkBox.setOnCheckedChangeListener(this);

        checkBox = (CheckBox) content.findViewById(R.id.cb_setting_autofix);
        checkBox.setChecked(isAutofix);
        checkBox.setOnCheckedChangeListener(this);

        checkBox = (CheckBox) content.findViewById(R.id.cb_setting_cnen);
        checkBox.setChecked(isCnen);
        checkBox.setOnCheckedChangeListener(this);

        checkBox = (CheckBox) content.findViewById(R.id.cb_setting_shuangpin);
        checkBox.setChecked(isShuangpin);
        checkBox.setOnCheckedChangeListener(this);

        group = (RadioGroup) content.findViewById(R.id.ensort);
        RadioButton byfreq = (RadioButton) content.findViewById(R.id.byfreq);
        RadioButton bylen = (RadioButton) content.findViewById(R.id.bylen);
        RadioButton byabc = (RadioButton) content.findViewById(R.id.byabc);
        switch (ensort) {
            case ImeCoreConsts.CFG_ENSORT_BYFREQ:
                byfreq.setChecked(true);
                bylen.setChecked(false);
                byabc.setChecked(false);
                break;
            case ImeCoreConsts.CFG_ENSORT_BYLEN:
                bylen.setChecked(true);
                byfreq.setChecked(false);
                byabc.setChecked(false);
                break;
            case ImeCoreConsts.CFG_ENSORT_BYABC:
                byabc.setChecked(true);
                byabc.setChecked(false);
                byabc.setChecked(false);
                break;
            default:
                break;
        }
        group.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(RadioGroup radioGroup, int i) {
                int radioButtonId = radioGroup.getCheckedRadioButtonId();
                switch (radioButtonId) {
                    case R.id.byfreq:
                        ImeCoreManager.getConfig().setInt(ENSORT, ImeCoreConsts.CFG_ENSORT_BYFREQ);
                        break;
                    case R.id.bylen:
                        ImeCoreManager.getConfig().setInt(ENSORT, ImeCoreConsts.CFG_ENSORT_BYLEN);
                        break;
                    case R.id.byabc:
                        ImeCoreManager.getConfig().setInt(ENSORT, ImeCoreConsts.CFG_ENSORT_BYABC);
                        break;
                    default:
                        break;
                }

            }
        });

        checkBox = (CheckBox) content.findViewById(R.id.cb_setting_phrase);
        checkBox.setChecked(isPhrase);
        checkBox.setOnCheckedChangeListener(this);

        checkBox = (CheckBox) content.findViewById(R.id.cb_setting_bh_first);
        checkBox.setChecked(isBhFirst);
        checkBox.setOnCheckedChangeListener(this);

        checkBox = (CheckBox) content.findViewById(R.id.cb_setting_wbpy);
        checkBox.setChecked(isWbpy);
        checkBox.setOnCheckedChangeListener(this);
        
        // 手写模式
        initHwTypeRadioGroup(content, hwType);
        
        // 手写速度
        initHwSpeedSeekBar(content, hwSpeed);
        
        // 五笔方案
        initWbSchemaRadioGroup(content, wbSchema);
        
        // 联想模式
        initLegendRadioGroup(content, legendMode, hwLegendMode);
    }

    private void initLegendRadioGroup(View content, int legendMode, int hwLegendMode) {
        RadioGroup group = (RadioGroup) content.findViewById(R.id.legend_mode);
        switch (legendMode) {
            case ImeCoreConsts.NO_LEGEND:
                group.check(R.id.legend_mode_no);
                break;
            case ImeCoreConsts.LEGEND_ONCE:
                group.check(R.id.legend_mode_once);
                break;
            case ImeCoreConsts.LEGEND_MORE:
                group.check(R.id.legend_mode_more);
                break;
            default:
                break;
        }
        group.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(RadioGroup radioGroup, int checkedId) {
                switch (checkedId) {
                    case R.id.legend_mode_no:
                        ImeCoreManager.getConfig().setInt(LEGEND_MODE, ImeCoreConsts.NO_LEGEND);
                        break;
                    case R.id.legend_mode_once:
                        ImeCoreManager.getConfig().setInt(LEGEND_MODE, ImeCoreConsts.LEGEND_ONCE);
                        break;
                    case R.id.legend_mode_more:
                        ImeCoreManager.getConfig().setInt(LEGEND_MODE, ImeCoreConsts.LEGEND_MORE);
                        break;
                    default:
                        break;
                    
                }
            }
        });

        group = (RadioGroup) content.findViewById(R.id.hw_legend_mode);
        switch (hwLegendMode) {
            case ImeCoreConsts.NO_LEGEND:
                group.check(R.id.hw_legend_mode_no);
                break;
            case ImeCoreConsts.LEGEND_ONCE:
                group.check(R.id.hw_legend_mode_once);
                break;
            case ImeCoreConsts.LEGEND_MORE:
                group.check(R.id.hw_legend_mode_more);
                break;
            default:
                break;
        }
        group.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(RadioGroup radioGroup, int checkedId) {
                switch (checkedId) {
                    case R.id.hw_legend_mode_no:
                        ImeCoreManager.getConfig().setInt(HW_LEGEND_MODE, ImeCoreConsts.NO_LEGEND);
                        break;
                    case R.id.hw_legend_mode_once:
                        ImeCoreManager.getConfig().setInt(HW_LEGEND_MODE, ImeCoreConsts.LEGEND_ONCE);
                        break;
                    case R.id.hw_legend_mode_more:
                        ImeCoreManager.getConfig().setInt(HW_LEGEND_MODE, ImeCoreConsts.LEGEND_MORE);
                        break;
                    default:
                        break;

                }
            }
        });
    }

    private void initWbSchemaRadioGroup(View content, int wbSchema) {
        RadioGroup group;
        group = (RadioGroup) content.findViewById(R.id.wbshema);
        RadioButton wb86 = (RadioButton) content.findViewById(R.id.wbshema_86);
        RadioButton wb98 = (RadioButton) content.findViewById(R.id.wbshema_98);
        switch (wbSchema) {
            case ImeCoreConsts.WB_86_SCHEMA:
                wb86.setChecked(true);
                wb98.setChecked(false);
                break;
            case ImeCoreConsts.WB_98_SCHEMA:
                wb86.setChecked(false);
                wb98.setChecked(true);
                break;
            default:
                break;
        }
        group.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(RadioGroup radioGroup, int i) {
                int radioButtonId = radioGroup.getCheckedRadioButtonId();
                switch (radioButtonId) {
                    case R.id.wbshema_86:
                        ImeCoreManager.getConfig().setInt(WB_SCHEMA, ImeCoreConsts.WB_86_SCHEMA);
                        break;
                    case R.id.wbshema_98:
                        ImeCoreManager.getConfig().setInt(WB_SCHEMA, ImeCoreConsts.WB_98_SCHEMA);
                        break;
                    default:
                        break;
                }

            }
        });
    }

    private void initHwSpeedSeekBar(View content, int hwSpeed) {
        SeekBar seekBar = (SeekBar) content.findViewById(R.id.hwspeed);
        seekBar.setProgress(hwSpeed);
        seekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                ImeCoreManager.getConfig().setInt(HW_SPEED, progress);
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {

            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {

            }
        });
    }

    private void initHwTypeRadioGroup(View content, int hwType) {
        RadioGroup group;
        group = (RadioGroup) content.findViewById(R.id.hwtype);
        RadioButton hwTypeHz = (RadioButton) content.findViewById(R.id.hwtype_hz);
        RadioButton hwTypeContinuation = (RadioButton) content.findViewById(R.id.hwtype_continuation);
        RadioButton hwTypeRedup = (RadioButton) content.findViewById(R.id.hwtype_redup);
        switch (hwType) {
            case ImeCoreConsts.CFGHWTYPE_HZ:
                hwTypeHz.setChecked(true);
                hwTypeRedup.setChecked(false);
                hwTypeContinuation.setChecked(false);
                break;
            case ImeCoreConsts.CFGHWTYPE_REDUP:
                hwTypeHz.setChecked(false);
                hwTypeRedup.setChecked(true);
                hwTypeContinuation.setChecked(false);
                break;
            case ImeCoreConsts.CFGHWTYPE_CONTINUATION:
                hwTypeHz.setChecked(false);
                hwTypeRedup.setChecked(false);
                hwTypeContinuation.setChecked(true);
                break;
            default:
                break;
        }
        group.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(RadioGroup radioGroup, int i) {
                int radioButtonId = radioGroup.getCheckedRadioButtonId();
                switch (radioButtonId) {
                    case R.id.hwtype_hz:
                        ImeCoreManager.getConfig().setInt(HW_TYPE, ImeCoreConsts.CFGHWTYPE_HZ);
                        break;
                    case R.id.hwtype_continuation:
                        ImeCoreManager.getConfig().setInt(HW_TYPE, ImeCoreConsts.CFGHWTYPE_CONTINUATION);
                        break;
                    case R.id.hwtype_redup:
                        ImeCoreManager.getConfig().setInt(HW_TYPE, ImeCoreConsts.CFGHWTYPE_REDUP);
                        break;
                    default:
                        break;
                }

            }
        });
    }

    @Override
    public PopLayoutParams onCreatePopLayoutParams() {
        return new PopLayoutParams.Builder()
                .setDimension(CommUtil.mScreenWidth, CommUtil.mScreenHeight - CommUtil.statusHeight)
                .setLocation(0, 0, PopServiceWrapper.getOffsetInScreen() + CommUtil.statusHeight).build();
    }

    @Override
    public void onDestroyView() {

    }
}
