package com.baidu.pop;

import com.baidu.input.support.state.IptListState;
import com.baidu.pad.ListHandler;

import android.content.Context;
import android.graphics.Canvas;
import android.support.annotation.Nullable;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;

/**
 * 左侧list的视图
 * 
 * Created by <PERSON><PERSON><PERSON><PERSON> on 17/8/15.
 */
public class SubListView extends View {
    
    private ListHandler mListHandler = new SymListHandler(getContext());
    
    public SubListView(Context context) {
        super(context);
    }

    public SubListView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public SubListView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public void updateList(IptListState listStore) {
        mListHandler.updateList(listStore);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        mListHandler.draw(canvas);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        return mListHandler.onTouchEvent(event);
    }

    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        super.onLayout(changed, left, top, right, bottom);
        mListHandler.updateBounds(0, 0, right - left, bottom - top);
    }

    /**
     * 符号面板的list处理器
     */
    private class SymListHandler extends ListHandler {
        public SymListHandler(Context context) {
            super(context);
        }

        @Override
        protected void updateUI() {
            SubListView.this.invalidate();
        }
    }
}
