package com.baidu.pop;

import com.baidu.input.support.state.IptCandState;
import com.baidu.input.support.state.IptListState;
import com.baidu.iptandroiddemo.R;
import com.baidu.iptcore.ImeCoreConsts;
import com.baidu.keymap.switcher.KeymapSwitcherFactory;
import com.baidu.pad.MoreCandPageHandler;
import com.baidu.pad.TipState;
import com.baidu.pad.action.ImeAction;
import com.citrus.popinn.PopFragment;
import com.citrus.popinn.PopLayoutParams;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

/**
 * 符号面板
 * <p>
 * Created by chendan<PERSON> on 17/8/15.
 */
public class SymbolPanelFragment extends PopFragment
        implements View.OnClickListener, MoreCandPageHandler.CandClickListener {

    private IptCandState mCandStore;
    private SubListView mSubListView;
    private MoreCandPageView mCandPageView;
    private int mPageIdx = -1;

    @Override
    public View onCreateView(Context context, ViewGroup parentView) {
        View contentView = LayoutInflater.from(context).inflate(R.layout.sym_panel, parentView);
        mSubListView = (SubListView) contentView.findViewById(R.id.list);
        mCandPageView = (MoreCandPageView) contentView.findViewById(R.id.cand);
        mCandPageView.setCandClickListener(this);

        View wordRangeView = contentView.findViewById(R.id.lock);
        View arrowUpView = contentView.findViewById(R.id.arrow_up);
        View arrowDownView = contentView.findViewById(R.id.arrow_down);
        View backView = contentView.findViewById(R.id.backspace);
        View closeView = contentView.findViewById(R.id.close);
        wordRangeView.setOnClickListener(this);
        arrowUpView.setOnClickListener(this);
        arrowDownView.setOnClickListener(this);
        backView.setOnClickListener(this);
        closeView.setOnClickListener(this);

        return contentView;
    }

    public void update(IptCandState candStore) {
        mCandStore = candStore;
        mPageIdx = -1;
        loadNextCandPage();
    }

    public void updateList(IptListState listStore) {
        mSubListView.updateList(listStore);
    }

    /**
     * 加载cand的一页
     */
    private void loadNextCandPage() {
        int pageIdx = mPageIdx + 1;
        boolean success = mCandPageView.loadCandPage(pageIdx, mCandStore);
        if (success) {
            mPageIdx = pageIdx;
        }
    }

    /**
     * 加载cand的一页
     */
    private void loadPreviousCandPage() {
        int pageIdx = mPageIdx - 1;
        boolean success = mCandPageView.loadCandPage(pageIdx, mCandStore);
        if (success) {
            mPageIdx = pageIdx;
        }
    }

    @Override
    public PopLayoutParams onCreatePopLayoutParams() {
        return new PopLayoutParams.Builder()
                .setDimension(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
                .setLocation(0, 0, 0).build();
    }

    @Override
    public void onDestroyView() {
        mCandStore = null;
        mPageIdx = -1;
        mCandPageView = null;
        mSubListView = null;
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.lock:
                ImeAction.dispatch(ImeAction.KEY_CLICK, ImeCoreConsts.FKEY_LOCK);
                ((TextView) v).setText(TipState.getInstance().isSymLock() ? "锁定" : "非锁定");
                break;
            case R.id.arrow_up:
                loadPreviousCandPage();
                break;
            case R.id.arrow_down:
                loadNextCandPage();
                break;
            case R.id.backspace:
                ImeAction.dispatch(ImeAction.KEY_CLICK, ImeCoreConsts.FKEY_BACK);
                break;
            case R.id.close:
                ImeAction.dispatch(ImeAction.KEY_CLICK, ImeCoreConsts.FKEY_RETURN);
                KeymapSwitcherFactory.Companion.getCn().returnKeyboard();
                mHost.dismiss();
                break;
            default:
                break;
        }
    }

    @Override
    public void onCandClicked() {
        if (!TipState.getInstance().isSymLock()) {
            mHost.dismiss();
        }
    }
}
