package com.baidu.setting;

import java.util.List;

import com.baidu.iptandroiddemo.R;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.provider.Settings;
import android.util.Log;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.inputmethod.InputMethodInfo;
import android.view.inputmethod.InputMethodManager;

public class SettingGuidLogic implements OnClickListener {
    public interface GuidNextStepListener {
        public void onNextStep();
    }

    public boolean mEnabelIme;
    public boolean mDefaultIme;

    private Activity mActi;
    private GuidNextStepListener mListener;

    public SettingGuidLogic(Activity acti) {
        mActi = acti;
        mListener = null;

        mActi.findViewById(R.id.guid_btn_next_step).setOnClickListener(this);
        mActi.findViewById(R.id.guid_btn_use_input).setOnClickListener(this);
        mActi.findViewById(R.id.guid_btn_select_input).setOnClickListener(this);
        mActi.findViewById(R.id.guid_btn_select_input).setOnClickListener(this);

        updateCtrEnable(false);
    }

    public void addNextStepListener(GuidNextStepListener listener) {
        mListener = listener;
    }

    public void updateCtrEnable(boolean ischeck) {
        
        boolean oldIsEnableIme = mEnabelIme;
        boolean oldIsDefaultIme = mDefaultIme;

        UpdateImeState(true, true);

        if (ischeck && oldIsEnableIme == mEnabelIme
                && oldIsDefaultIme == mDefaultIme) {
            return;
        }

        if (mEnabelIme) {
            mActi.findViewById(R.id.guid_btn_select_input).setEnabled(false);
        }
        else {
            mActi.findViewById(R.id.guid_btn_select_input).setEnabled(true);
        }

        if (mDefaultIme || !mEnabelIme) {
            mActi.findViewById(R.id.guid_btn_use_input).setEnabled(false);
        }
        else {
            mActi.findViewById(R.id.guid_btn_use_input).setEnabled(true);
        }

        if (mDefaultIme && mEnabelIme) {
            mActi.findViewById(R.id.guid_btn_next_step).setEnabled(true);
        }
        else {
            mActi.findViewById(R.id.guid_btn_next_step).setEnabled(false);
        }
    }
    
    private void UpdateImeState(boolean defaultstate, boolean enablestate)
    {
        if (defaultstate)
        {
            mDefaultIme = IsDefaultIme();
        }
        if (enablestate)
        {
            mEnabelIme = IsEnableIme();
        }
    }

    private boolean IsEnableIme() {
        boolean enable = false;

        InputMethodManager imm = (InputMethodManager) mActi
                .getSystemService(Context.INPUT_METHOD_SERVICE);
        List<InputMethodInfo> ime_list = imm.getEnabledInputMethodList();
        String pack_name = mActi.getPackageName();

        for (int i = 0; i < ime_list.size(); i++) {
            String ime_pack_name = ime_list.get(i).getPackageName();
            if (ime_pack_name != null && ime_pack_name.equals(pack_name)) {
                enable = true;
                break;
            }
        }

        return enable;
    }

    private boolean IsDefaultIme() {
        String lastInputMethodId = Settings.Secure.getString(
                mActi.getContentResolver(),
                Settings.Secure.DEFAULT_INPUT_METHOD);
        return (lastInputMethodId != null)
                && lastInputMethodId.equals(mActi.getPackageName()
                        + "/.ImeService");
    }

    @Override
    public void onClick(View v) {
        long[] time_data = new long[5];
        Log.i("time", "" + time_data[0] + "," + time_data[1] + "," + time_data[2] + "," + time_data[3] + "," + time_data[4]);
        try {
            Thread.sleep(1);
        }
        catch (Exception e) {
            // TODO: handle exception
        }
        Log.i("time", "" + time_data[0] + "," + time_data[1] + "," + time_data[2] + "," + time_data[3] + "," + time_data[4]);
        switch (v.getId()) {
        case R.id.guid_btn_select_input: {
            Intent intent = new Intent();
            intent.setAction(Settings.ACTION_INPUT_METHOD_SETTINGS);
            mActi.startActivity(intent);
            break;
        }
        case R.id.guid_btn_use_input: {
            InputMethodManager im = (InputMethodManager) mActi
                    .getSystemService(Context.INPUT_METHOD_SERVICE);
            im.showInputMethodPicker();
            break;
        }
//        case R.id.guid_btn_user_agree: {
//            break;
//        }
//        case R.id.guid_btn_next_step: {
//            if (mListener != null) {
//                mListener.onNextStep();
//            }
//            break;
//        }
        default: {
            break;
        }
        }
    }
}
