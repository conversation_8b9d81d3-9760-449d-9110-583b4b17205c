/*
 * Copyright (C) 2017 Baidu, Inc. All Rights Reserved.
 */
package com.baidu.util;

import android.util.Log;

/**
 * 打印Log
 * 
 * Created by <PERSON><PERSON><PERSON><PERSON> on 17/4/12.
 */

public class BDLog {

    private static final String DEFAULT_TAG = "cdf";

    public static void v(String msg) {
        v(DEFAULT_TAG, msg);
    }

    public static void v(String tag, String msg) {
        Log.v(tag, msg);
    }

    public static void i(String msg) {
        i(DEFAULT_TAG, msg);
    }

    public static void i(String tag, String msg) {
        Log.i(tag, msg);
    }

    /**
     * 使用默认tag和默认level打印，便于调试
     * @param format 格式
     * @param args 参数
     */
    public static void log(String format, Object... args) {
        i(String.format(format, args));
    }

    /**
     * 使用默认tag和默认level打印，便于调试
     */
    public static void log(String msg) {
        i(msg);
    }

    /**
     * 将数组打印出来的工具方法
     */
    public static <T> void logArray(String name, T[] result) {
        StringBuilder sb = new StringBuilder(name);
        if (result == null) {
            sb.append(":null");
            log(sb.toString());
            return;
        }
        int size = result.length;
        sb.append("(" + size + ")");
        if (size == 0) {
            sb.append("[]");
            log(sb.toString());
        } else {
            sb.append("[");
            for (T o : result) {
                if (o == null) {
                    sb.append("null,");
                } else {
                    sb.append(o.toString() + ",");
                }
            }
            sb.append("]");
            log(sb.toString());
        }
    }
}
