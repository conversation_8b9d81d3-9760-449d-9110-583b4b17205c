package com.baidu.util;

import android.content.Context;
import android.content.res.Resources;
import android.util.DisplayMetrics;

import com.baidu.iptandroiddemo.ImeService;
import com.baidu.iptandroiddemo.R;
import com.baidu.pad.ImeInputView;
import com.baidu.pad.ImePadView;
import com.baidu.pop.MoreCandFragment;
import com.baidu.pop.SymbolPanelFragment;

public class CommUtil {

    /** 屏幕宽度 */
    public static int mScreenWidth = 0;
    /** 屏幕高度 */
    public static int mScreenHeight = 0;
    /** 状态栏高度 */
    public static int statusHeight = 0;

    public static ImePadView mPadView = null;
    public static ImeInputView mInputView = null;
    public static MoreCandFragment sMoreCandFragment;
    public static SymbolPanelFragment sSymFragment;

    /** 符号面板没有语言概念 */
    public static int langType;

    public static final byte LANG_EN = 0x10;
    public static final byte LANG_CH = 0x20;
    public static final byte LANG_ETC = 0x30;

    public static byte inputWay = CommUtil.IFROME_SKEY;

    public static final byte IFROME_NULL = 0;

    public static final byte IFROME_SKEY = 1;

    public static final byte IFROME_HKEY = 2;

    public static final byte IFROME_WRITE = 3;

    private static String[] CAND_FLAG_STRINGS = new String[] {
            "",
            "Contact",
            "CloudCache",
            "Cloud",
            "Triangle"
    };

    private static String[] CAND_TYPE_STRINGS = new String[] {
            "","",
            "CloudArrow", "Service", "FastInput",
            "Media", "Zhidahao", "Op", "Xiehouyu", "Emoji",
            "Emotion", "EmojiLian", "EmotionLian",
    };

    public static void updateScreenSize(Context context) {
        
        DisplayMetrics dm = context.getResources().getDisplayMetrics();
        mScreenWidth = dm.widthPixels;
        mScreenHeight = dm.heightPixels;      
        
        // 手机状态栏的高度
        Resources res = context.getResources();
        try {
            int resourceId = res.getIdentifier("status_bar_height", "dimen", "android");
            statusHeight = res.getDimensionPixelSize(resourceId);
            if (statusHeight == 0) {
                statusHeight = res.getDimensionPixelSize(R.dimen.default_status_bar_height);
            }
        } catch (Exception e) {
            statusHeight = res.getDimensionPixelSize(R.dimen.default_status_bar_height);
            e.printStackTrace();
        }
    }

    public static String candTypeToString(int candType) {
        if (candType >= 0 && candType < CAND_TYPE_STRINGS.length) {
            return CAND_TYPE_STRINGS[candType];
        }
        return "";
    }

    public static String candFlagToString(int candFlag) {
        if (candFlag >= 0 && candFlag < CAND_FLAG_STRINGS.length) {
            return CAND_FLAG_STRINGS[candFlag];
        }
        return "";
    }
}
