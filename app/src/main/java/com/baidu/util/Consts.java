/*
 * Copyright (C) 2017 Baidu, Inc. All Rights Reserved.
 */
package com.baidu.util;

/**
 * 存放一些全局常量
 * Created by <PERSON><PERSON><PERSON><PERSON> on 17/4/14.
 */

public class Consts {

    /**
     * 和内核无关的功能键，这个地方需要考虑
     */
    public static final int FKEY_SUBLIST = 2000; // sublist区域
    public static final int FKEY_HANDWRITING = 2001; // 手写区域
    public static final int FKEY_SEMICOLON = 2002; // 分号
    public static final int FKEY_QUICK_CJ = 2003; // 速成仓颉

}
