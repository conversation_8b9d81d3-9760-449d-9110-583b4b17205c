/*
 * Copyright (C) 2017 Baidu, Inc. All Rights Reserved.
 */
package com.baidu.util;

import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Rect;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 17/4/10.
 */

public class DrawUtil {

    /**
     * 在指定区域绘制文字
     */
    public static void drawText(Canvas canvas, Paint paint, String txt, int tlX, int tlY,
                            int brX, int brY, int fontSize) {
        float x = (tlX + brX) / 2;
        float y = (tlY + brY) / 2;

        paint.setTextSize(fontSize);
        paint.setStyle(Paint.Style.FILL);
        float textWidth = paint.measureText(txt);
        int fontHeight = fontSize;
        x = x - textWidth / 2;
        y = y + fontHeight / 2;

        canvas.drawText(txt, x, y, paint);
        paint.setStyle(Paint.Style.STROKE);
    }

    public static void drawText(Canvas canvas, Paint paint, String txt, Rect bounds, int fontSize) {
        drawText(canvas, paint, txt, bounds.left, bounds.top, bounds.right, bounds.bottom, fontSize);
    }
}
