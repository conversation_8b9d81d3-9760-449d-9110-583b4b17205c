package com.baidu.util;

import java.io.File;

import com.baidu.iptandroiddemo.Global;

public class PathUtil {

    public static final String DICT_DIR_NAME = "dict";

    public static String K_ROOT_DIR_PATH;
    
    public static String dictDirPath() {
        if (K_ROOT_DIR_PATH == null) {
            K_ROOT_DIR_PATH =
                    Global.getImeApp().getFilesDir().getPath();
        }
        return K_ROOT_DIR_PATH + DICT_DIR_NAME + File.separator;
    }
}
