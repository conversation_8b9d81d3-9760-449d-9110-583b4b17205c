/*
 * Copyright (C) 2017 Baidu, Inc. All Rights Reserved.
 */
package com.baidu.util;

import com.baidu.iptandroiddemo.R;
import com.baidu.pop.MoreCandFragment;
import com.baidu.pop.SymbolPanelFragment;
import com.citrus.popinn.PopHandle;
import com.citrus.popinn.anno.AutoDismiss;
import com.citrus.popinn.anno.BindingClick;
import com.citrus.popinn.anno.Layout;
import com.citrus.popinn.anno.PopDelegate;

import android.view.View;
import android.widget.CompoundButton;

/**
 * DEMO工程里面的各种浮层的展示
 * 
 * Created by ch<PERSON><PERSON><PERSON> on 17/4/12.
 */
public interface PopService {
    
    @Layout(R.layout.input_type_select)
    @AutoDismiss(dismissId = R.id.btn_close)
    PopHandle showInputTypeSelector(@BindingClick View.OnClickListener listener);
    
    void showMoreCandView(@PopDelegate MoreCandFragment fragment);

    @AutoDismiss(dismissId = R.id.close)
    PopHandle showSettingView(@PopDelegate CompoundButton.OnCheckedChangeListener listener);

    void showSymPanel(@PopDelegate SymbolPanelFragment fragment);
}
