/*
 * Copyright (C) 2017 Baidu, Inc. All Rights Reserved.
 */
package com.baidu.util;

import com.baidu.pop.MoreCandFragment;
import com.baidu.pop.SettingViewFragment;
import com.baidu.pop.SymbolPanelFragment;
import com.citrus.popinn.PopHandle;
import com.citrus.popinn.PopInn;

import android.content.Context;
import android.support.v4.view.ViewCompat;
import android.view.View;

/**
 * 浮层展示的封装类
 * Created by ch<PERSON><PERSON><PERSON> on 17/4/12.
 */
public class PopServiceWrapper {

    private static PopService sPopService;
    private static PopInn sPopInn;

    /**
     * 创建代理service
     */
    private static PopService getService() {
        if (sPopService == null) {
            synchronized(PopServiceWrapper.class) {
                if (sPopService == null) {
                    sPopService = getPopInn().create(PopService.class);
                }
            }
        }
        return sPopService;
    }

    /**
     * 浮层管理类
     */
    private static PopInn getPopInn() {
        if (sPopInn == null) {
            synchronized(PopServiceWrapper.class) {
                if (sPopInn == null) {
                    sPopInn = new PopInn.Builder().build();
                }
            }
        }
        return sPopInn;
    }

    /**
     * 关闭
     */
    public static void closePopupWindow() {
        if (sPopInn != null) {
            sPopInn.closePopupWindow();
        }
    }

    /**
     * 关联context和anchorView
     */
    public static void attatch(Context context, View anchorView) {
        getPopInn().attach(context).with(anchorView);
    }

    /**
     * 断开关联
     */
    public static void detatch() {
        if (sPopInn != null) {
            sPopInn.closePopupWindow();
            sPopInn.detatch();
        }
    }
    
    // --------------------- 业务逻辑 ---------------------------------

    /**
     * 输入方式选择浮层
     */
    public static PopHandle showInputTypeSelect(View.OnClickListener listener) {
        return getService().showInputTypeSelector(listener);
    }

    /**
     * 更多候选字浮层
     */
    public static void showMoreCandView(MoreCandFragment fragment) {
        getService().showMoreCandView(fragment);
    }

    /**
     * 符号浮层
     */
    public static void showSymView(SymbolPanelFragment fragment) {
        getService().showSymPanel(fragment);
    }

    /**
     * 设置浮层
     */
    public static void showSettingView() {
        SettingViewFragment delegate = new SettingViewFragment();
        getService().showSettingView(delegate);
    }

    //  ------------------- 工具方法：坐标相关 -----------------------------
    
    public static int getOffsetInScreen() {
        View refView = null;
        if (CommUtil.mInputView != null && ViewCompat.isAttachedToWindow(CommUtil.mInputView)) {
            refView = CommUtil.mInputView;
        } else if (CommUtil.mPadView != null && ViewCompat.isAttachedToWindow(CommUtil.mPadView)) {
            refView = CommUtil.mPadView;
        }
        if (refView != null) {
            int[] location = new int[2];
            refView.getLocationOnScreen(location);
            return -location[1];
        }
        return 0;
    }

}
