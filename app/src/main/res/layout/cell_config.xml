<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
              android:id="@+id/cell_config_container"
              android:orientation="vertical"
              android:layout_width="match_parent"
              android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <Button
            android:id="@+id/btn_get_phrase_group_count"
            android:text="获取个性短语分组数量"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>
        <Button
            android:id="@+id/btn_get_phrase_group"
            android:text="查看个性短语分组"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>

        <Button
            android:id="@+id/btn_add_phrase_group"
            android:text="添加个性短语分组"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>

        <Button
            android:id="@+id/btn_edit_phrase_group"
            android:text="编辑个性短语分组"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>

        <Button
            android:id="@+id/btn_delete_phrase_group"
            android:text="删除个性短语分组"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>

        <Button
            android:id="@+id/btn_enable_phrase_group"
            android:text="启用/关闭个性短语分组"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>

        <Button
            android:id="@+id/btn_get_phrase_item_count"
            android:text="获得个性短语item的数量"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>

        <Button
            android:id="@+id/btn_get_phrase_item"
            android:text="获得个性短语item"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>

        <Button
            android:id="@+id/btn_add_phrase_item"
            android:text="添加个性短语item"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>

        <Button
            android:id="@+id/btn_edit_phrase_item"
            android:text="编辑个性短语item"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>

        <Button
            android:id="@+id/btn_delete_phrase_item"
            android:text="删除个性短语item"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>

        <Button
            android:id="@+id/btn_aifont_load_model"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="加载aifont模型"/>

        <Button
            android:id="@+id/btn_aifont_unload_model"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="卸载aifont模型"/>

        <Button
            android:id="@+id/btn_aifont_model_ver"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="aifont模型版本号"/>

        <Button
            android:id="@+id/btn_aifont_points_score"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="aifont手写评分"/>

        <Button
            android:id="@+id/btn_aifont_generate_notify"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="aifont手写字体生成通知"/>

        <Button
            android:id="@+id/btn_ai_card_data_encryption"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="AI卡片数据加密"/>

        <Button
            android:id="@+id/btn_ai_card_data_decrypt"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="AI卡片数据解密"/>

        <Button
            android:id="@+id/btn_rare_load"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="加载生僻字"/>

        <Button
            android:id="@+id/btn_rare_unload"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="卸载生僻字"/>

        <Button
            android:id="@+id/btn_rare_version"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="生僻字版本"/>

        <Button
            android:id="@+id/huawei_test"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="华为测试"/>

    </LinearLayout>

</ScrollView>