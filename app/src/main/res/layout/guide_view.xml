<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
            android:layout_width="fill_parent"
            android:layout_height="fill_parent"
            android:scrollbars="none"
            android:background="@android:color/background_light">

    <LinearLayout
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="#00000000">

        <EditText
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
            android:inputType="textCapCharacters"/>

        <LinearLayout
                android:layout_height="wrap_content"
                android:id="@+id/main_page_title"
                android:layout_width="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="17dip"
                android:orientation="horizontal"
                android:background="#00000000">

            <ImageView
                    android:id="@+id/setting_icon"
                    android:src="@mipmap/ic_launcher"
                    android:contentDescription="@string/app_name"
                    android:layout_height="72dip"
                    android:layout_width="72dip"/>

            <TextView
                    android:text="@string/guid_open_input"
                    android:textSize="25sp"
                    android:layout_gravity="center"
                    android:textColor="@android:color/black"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="8dip"
                    android:layout_width="wrap_content"/>
        </LinearLayout>

        <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dip"
                android:layout_marginLeft="20dip"
                android:layout_marginRight="20dip"
                android:background="#DFDFDF"
                android:orientation="vertical">

            <TextView
                    android:text="@string/guid_step_1"
                    android:layout_marginTop="5dip"
                    android:layout_marginLeft="5dip"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="17sp"
                    android:textColor="@android:color/black"
                    />

            <Button
                    android:id="@+id/guid_btn_select_input"
                    android:layout_marginTop="50dip"
                    android:layout_width="85dip"
                    android:layout_height="40dip"
                    android:text="@string/guid_select_input"
                    android:textSize="16sp"
                    android:gravity="center"
                    android:layout_gravity="right"
                    android:layout_marginRight="5dip"
                    android:layout_marginBottom="5dip"/>
        </LinearLayout>

        <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="18dip"
                android:layout_marginLeft="20dip"
                android:layout_marginRight="20dip"
                android:background="#DFDFDF"
                android:orientation="vertical">

            <TextView
                    android:text="@string/guid_step_2"
                    android:layout_marginTop="5dip"
                    android:layout_marginLeft="5dip"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="17sp"
                    android:textColor="@android:color/black"/>

            <Button
                    android:id="@+id/guid_btn_use_input"
                    android:layout_marginTop="50dip"
                    android:layout_width="85dip"
                    android:layout_height="40dip"
                    android:text="@string/guid_use_input"
                    android:textSize="16sp"
                    android:gravity="center"
                    android:layout_gravity="right"
                    android:layout_marginRight="5dip"
                    android:layout_marginBottom="5dip"/>
        </LinearLayout>

        <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="5dip"
                android:layout_marginLeft="22dip">

            <TextView
                    android:text="@string/guid_accept_user_agree"

                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="14sp"
                    android:textColor="@android:color/black"/>

            <Button
                    android:id="@+id/guid_btn_user_agree"
                    android:text="@string/guid_user_agree"
                    android:textSize="14sp"
                    android:textColor="@color/blue"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@color/clear"
                    android:gravity="center"/>
        </LinearLayout>

        <Button
                android:layout_width="180dip"
                android:layout_height="45dip"
                android:id="@+id/guid_btn_next_step"
                android:text="@string/guid_next_step"
                android:textSize="17sp"
                android:gravity="center"
                android:layout_marginTop="50dip"
                android:layout_gravity="center"/>
    </LinearLayout>
</ScrollView>
