<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2017 Baidu, Inc. All Rights Reserved.
  -->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
             android:background="#80000000"
             android:layout_width="match_parent"
             android:layout_height="match_parent">

    <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

        <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

            <Button
                    android:id="@+id/btn_py26"
                    android:layout_weight="1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="py26"/>
            <Button
                    android:id="@+id/btn_py9"
                    android:layout_weight="1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="py9"/>
            <Button
                    android:id="@+id/btn_en26"
                    android:layout_weight="1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="en26"/>
            <Button
                    android:id="@+id/btn_en9"
                    android:layout_weight="1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="en9"/>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                    android:id="@+id/btn_hw"
                    android:layout_weight="1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="hw"/>

            <Button
                    android:id="@+id/btn_rare_hw"
                    android:layout_weight="1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="rare hw"/>

            <Button
                    android:id="@+id/btn_bh"
                    android:layout_weight="1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="bh"/>
            <Button
                    android:id="@+id/btn_wb9"
                    android:layout_weight="1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="wb9"/>
            <Button
                    android:id="@+id/btn_wb26"
                    android:layout_weight="1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="wb26"/>
        </LinearLayout>

        <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

            <Button
                    android:id="@+id/btn_zy"
                    android:layout_weight="1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="ZY"/>

            <Button
                    android:id="@+id/btn_cj"
                    android:layout_weight="1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="CJ"/>
        </LinearLayout>


        <Button
                android:id="@+id/btn_cell_setting"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="词库设置"/>

        <Button
                android:id="@+id/btn_setting"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="设置"/>
        <Button
                android:id="@+id/btn_close"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="关闭"/>

    </LinearLayout>

</FrameLayout>