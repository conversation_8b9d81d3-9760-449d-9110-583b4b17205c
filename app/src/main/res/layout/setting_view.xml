<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              android:orientation="vertical"
              android:layout_width="match_parent"
              android:layout_height="match_parent"
              android:background="@color/popup_window_background">
    <Button
            android:id="@+id/close"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="关闭"/>

    <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">

        <LinearLayout
                android:padding="20dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

            <CheckBox
                    android:id="@+id/cb_setting_sentence"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="整句输入"/>

            <CheckBox
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:id="@+id/cb_setting_shuangpinnocvt"
                    android:text="双拼输入转换"/>

            <CheckBox
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:id="@+id/cb_setting_jianpin_gram"
                    android:text="简拼二元整句"/>

            <RadioGroup
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:id="@+id/encase">

                <RadioButton
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:id="@+id/normal"
                        android:text="普通状态"/>

                <RadioButton
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:id="@+id/first"
                        android:text="首字母大写状态"/>

                <RadioButton
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:id="@+id/all"
                        android:text="全部字母大写状态"/>
            </RadioGroup>

            <CheckBox
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:id="@+id/cb_setting_autosave"
                    android:text="自动实时保存"/>

            <CheckBox
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:id="@+id/cb_setting_sylian"
                    android:text="符号联想"/>

            <CheckBox
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:id="@+id/cb_setting_predict"
                    android:text="逐词优化"/>

            <RadioGroup
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:id="@+id/form">

                <RadioButton
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:id="@+id/off"
                        android:text="完全关闭网址邮箱符号输入"/>

                <RadioButton
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:id="@+id/manual"
                        android:text="手动输入模式"/>

                <RadioButton
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:id="@+id/auto"
                        android:text="自动输入模式"/>
            </RadioGroup>

            <CheckBox
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:id="@+id/cb_setting_xiehouyu"
                    android:text="歇后语标记"/>

            <CheckBox
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:id="@+id/cb_setting_fastinput"
                    android:text="快速输入"/>

            <CheckBox
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:id="@+id/cb_setting_wbtip"
                    android:text="五笔提示"/>

            <CheckBox
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:id="@+id/cb_setting_op"
                    android:text="运营词"/>

            <View
                    android:layout_width="match_parent"
                    android:layout_height="1px"
                    android:background="#ffd6d6d6">
            </View>

            <CheckBox
                    android:id="@+id/cb_setting_cnen"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="中英混输"/>

            <CheckBox
                    android:id="@+id/cb_setting_fanti"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="繁体输入"/>

            <CheckBox
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="笔画下字形优先"
                    android:id="@+id/cb_setting_bh_first"/>

            <CheckBox
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:id="@+id/cb_setting_wbpy"
                    android:text="五笔拼音"/>

            <CheckBox
                    android:id="@+id/cb_setting_shuangpin"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="双拼"/>

            <RadioGroup
                    android:id="@+id/ensort"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                <RadioButton
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:id="@+id/byfreq"
                        android:text="按英文词的频率排序"/>

                <RadioButton
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:id="@+id/bylen"
                        android:text="按英文词的长度排序"/>

                <RadioButton
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:id="@+id/byabc"
                        android:text="按英文词的字母顺序排序"/>
            </RadioGroup>

            <CheckBox
                    android:id="@+id/cb_setting_autofix"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="智能纠错"/>

            <CheckBox
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:id="@+id/cb_setting_phrase"
                    android:text="个性短语"/>

            <CheckBox
                    android:id="@+id/cb_setting_emoji"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="emoji"/>

            <CheckBox
                    android:id="@+id/cb_setting_emoji_lian"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="emoji联想"/>

            <CheckBox
                    android:id="@+id/cb_setting_acg"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="二次元模式"/>

            <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">
                
                <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="手写模式"/>

                <RadioGroup
                        android:id="@+id/hwtype"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                    <RadioButton
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:id="@+id/hwtype_hz"
                            android:text="单字手写"/>

                    <RadioButton
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:id="@+id/hwtype_redup"
                            android:text="多字叠写"/>

                    <RadioButton
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:id="@+id/hwtype_continuation"
                            android:text="多字连写"/>
                </RadioGroup>

            </LinearLayout>
            <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="手写速度"/>
            <SeekBar
                    android:id="@+id/hwspeed"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:max="4"/>
            
            <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="五笔方案"/>

                <RadioGroup
                        android:id="@+id/wbshema"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                    <RadioButton
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:id="@+id/wbshema_86"
                            android:text="五笔86方案"/>

                    <RadioButton
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:id="@+id/wbshema_98"
                            android:text="五笔98方案"/>
                </RadioGroup>

            </LinearLayout>

            <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="联想方案"/>

                <RadioGroup
                        android:id="@+id/legend_mode"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                    <RadioButton
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:id="@+id/legend_mode_no"
                            android:text="关闭联想"/>

                    <RadioButton
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:id="@+id/legend_mode_once"
                            android:text="单次联想"/>
                    <RadioButton
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:id="@+id/legend_mode_more"
                            android:text="多次联想"/>
                </RadioGroup>

            </LinearLayout>

            <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="手写联想方案"/>

                <RadioGroup
                        android:id="@+id/hw_legend_mode"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                    <RadioButton
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:id="@+id/hw_legend_mode_no"
                            android:text="关闭联想"/>

                    <RadioButton
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:id="@+id/hw_legend_mode_once"
                            android:text="单次联想"/>
                    <RadioButton
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:id="@+id/hw_legend_mode_more"
                            android:text="多次联想"/>
                </RadioGroup>

            </LinearLayout>

            <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="云输入网络条件"/>

                <RadioGroup
                        android:id="@+id/cloud_input_type"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                    <RadioButton
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:id="@+id/cloud_input_type_no"
                            android:text="不开启"/>

                    <RadioButton
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:id="@+id/cloud_input_type_wifi"
                            android:text="仅wifi开启"/>
                    <RadioButton
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:id="@+id/cloud_input_type_all"
                            android:text="所有网络开启"/>
                </RadioGroup>

            </LinearLayout>
        </LinearLayout>
    </ScrollView>

</LinearLayout>