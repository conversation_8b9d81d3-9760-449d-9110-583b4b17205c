<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
                android:orientation="vertical"
                android:background="@color/popup_window_background"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

    <LinearLayout
            android:id="@+id/footer"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:orientation="horizontal"
            android:layout_alignParentBottom="true"
            >

        <TextView
                android:id="@+id/lock"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:text="锁定"
                android:textColor="@color/key_fore_text"
                android:background="@drawable/button_background"
                android:layout_margin="2dp"
                />
        <TextView
                android:id="@+id/arrow_up"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:text="/\\"
                android:textColor="@color/key_fore_text"
                android:background="@drawable/button_background"
                android:layout_margin="2dp"
                />
        <TextView
                android:id="@+id/arrow_down"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:text="\\/"
                android:textColor="@color/key_fore_text"
                android:background="@drawable/button_background"
                android:layout_margin="2dp"
                />

        <TextView
                android:id="@+id/backspace"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:text="退格"
                android:textColor="@color/key_fore_text"
                android:background="@drawable/button_background"
                android:layout_margin="2dp"
                />
        <TextView
                android:id="@+id/close"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:text="返回"
                android:textColor="@color/key_fore_text"
                android:background="@drawable/button_background"
                android:layout_margin="2dp"
                />
    </LinearLayout>

    <com.baidu.pop.SubListView
            android:id="@+id/list"
            android:layout_height="match_parent"
            android:layout_width="100dp"
            android:layout_above="@+id/footer"
            />
    <com.baidu.pop.MoreCandPageView
            android:id="@+id/cand"
            android:layout_height="match_parent"
            android:layout_width="match_parent"
            android:layout_above="@+id/footer"
            android:layout_toRightOf="@+id/list"
            />

</RelativeLayout>