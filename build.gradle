buildscript {

    Properties properties = new Properties()
    def localFile = project.rootProject.file('local.properties')
    def rootLocalFile = project.rootProject.file('../../local.properties')
    if (localFile.exists()) {
        properties.load(localFile.newDataInputStream())
    } else if (rootLocalFile.exists()){
        properties.load(rootLocalFile.newDataInputStream())
    }

    def detect_root = "../../detect_root.gradle"
    def isRepoRoot = file(detect_root).exists()
    if (isRepoRoot) {
        apply from: detect_root
    } else {
        ext {
            localMavenDir = "${repositories.mavenLocal().url}"
        }
    }

    repositories {
        mavenLocal()
        google()
        maven {
            url "http://maven.applib.baidu-int.com:8681/repository/applib-public/"
            allowInsecureProtocol = true
        }
        maven {
            url "http://matrix.baidu-int.com/maven/repository/proxy-baidu/"
            allowInsecureProtocol = true
        }
        maven {
            url 'http://maven.aliyun.com/nexus/content/groups/public/'
            allowInsecureProtocol = true
        }
        maven { url 'https://matrix.baidu-int.com/nexus/repository/maven-public/'}
        // 手百cuid sdk
        maven {
            url 'http://maven.mbd.baidu.com:8666/repository/searchbox-public/'
            allowInsecureProtocol = true
        }
        maven {
            url "${MAVEN_URL}/maven-public/"
            credentials {
                username properties.getProperty('NEXUS_READ_USERNAME')
                password properties.getProperty('NEXUS_READ_PASSWORD')
            }
            allowInsecureProtocol = true
        }
        maven {
            url "http://matrix.baidu-int.com/maven/repository/proxy-baidu/"
            allowInsecureProtocol = true
        }
        //  根据条件动态添加本地仓库
        if (rootProject.hasProperty("localMavenDir")) {
            maven { url localMavenDir }
        }
    }
    dependencies {
        classpath "com.android.tools.build:gradle:${GRADLE_VERSION}"

        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$KOTLIN_VERSION"

        // 编译注入，仅仅在application中使用（apply plugin: 'com.baidu.pyramid.di'）
        classpath "com.baidu.pyramid:di-gradle-plugin:0.8.127"

        classpath "com.baidu.coco:coco-bpi:1.3.26"

    }
}

allprojects {
    Properties properties = new Properties()
    def localFile = project.rootProject.file('local.properties')
    def rootLocalFile = project.rootProject.file('../../local.properties')
    if (localFile.exists()) {
        properties.load(localFile.newDataInputStream())
    } else if (rootLocalFile.exists()){
        properties.load(rootLocalFile.newDataInputStream())
    }

    repositories {
        mavenLocal()
        google()
        maven {
            url 'http://maven.aliyun.com/nexus/content/groups/public/'
            allowInsecureProtocol = true
        }
        maven { url 'https://matrix.baidu-int.com/nexus/repository/maven-public/'}
        // 手百cuid sdk
        maven {
            url 'http://maven.mbd.baidu.com:8666/repository/searchbox-public/'
            allowInsecureProtocol = true
        }
        maven {
            url "${MAVEN_URL}/maven-public/"
            credentials {
                username properties.getProperty('NEXUS_READ_USERNAME')
                password properties.getProperty('NEXUS_READ_PASSWORD')
            }
            allowInsecureProtocol = true
        }
        maven {
            url "http://matrix.baidu-int.com/maven/repository/proxy-baidu/"
            allowInsecureProtocol = true
        }
        // 根据条件动态添加本地仓库
        if (rootProject.hasProperty("localMavenDir")) {
            maven { url localMavenDir }
        }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}

ext {
    minSdkVersion = MIN_SDK_VERSION as int
    targetSdkVersion = TARGET_SDK_VERSION as int
    compileSdkVersion = COMPILE_SDK_VERSION as int
    buildToolsVersion = BUILD_TOOLS_VERSION
    versionName = POM_VERSION_NAME
    versionCode = POM_VERSION_CODE as int
    sourceCompatibilityVersion = JavaVersion.VERSION_1_8
    targetCompatibilityVersion = JavaVersion.VERSION_1_8

    // native trace开关
    enableNativeTrace = false

    localMavenDir = "${repositories.mavenLocal().url}"
}

ext.deps = [
        // Test dependencies
        junit       : 'junit:junit:4.12',
        robolectric : 'org.robolectric:robolectric:3.1.1',
        mockito     : 'org.mockito:mockito-core:1.10.19',
        assertj     : 'com.squareup.assertj:assertj-android:1.0.0'
]

