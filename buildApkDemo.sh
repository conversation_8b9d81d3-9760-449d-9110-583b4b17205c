#!/bin/bash

###### ######################################################## #####
###### ######################################################## #####
# 脚本用途：编译debug版本的.a和so，打包android demo包

###### 环境配置
###### 1) android ndk版本: ndk-r17c

###### 使用方法和脚本参数
###### 使用举例：sh buildApkDemo.sh [无参数|apk|so|so-only]
###### 1) 无参数: 执行所有步骤
###### 2) apk: 不编译，只打包demoApk
###### 3) so: 重新编译内核的.a和so
###### 4) so-only: 只重新编译so
###### 5) addr2line: 崩溃栈retrace（崩溃栈信息需要配在下文中）

###### 修改本地参数
# iptcore的路径
IPTCORE_PATH=/Users/<USER>/BaiduCode/baidu/baiduinput/iptcore
# 当前工程路径
PROJECT_PATH=/Users/<USER>/BaiduCode/baidu/inputmethod/iptandroiddemo
# ndk路径
NDK_PATH=/Users/<USER>/Library/Android/android-ndk-r17c
# 基础路径（如果当前工程下不存在该目录，新建一个）
BUILD_DIR="buildDemoDir"
# base-apk名称（联系客户端RD找到匹配的那个，放在前面的BUILD_DIR里面）
BASE_APK_NAME="mainLineDebug-signed.apk"
# 输出文件的名字，最终apk的文件名是：OUT_APK_NAME-signed.apk
OUT_APK_NAME="mydebug"

###### 如果使用addr2line，需要将崩溃栈信息写在这里，用于retrace
PC_ADDRS="000e6d30 001e067f 000ebc09 0012b3a3"
#00 pc 000e6d30  /data/app/com.baidu.input-1/lib/arm/libiptcore.so (_Z4BN03PPv+5)
#01 pc 001e067f  /data/app/com.baidu.input-1/lib/arm/libiptcore.so (_Z4HP02P14s_wt_dnn_model+10)
#02 pc 000ebc09  /data/app/com.baidu.input-1/lib/arm/libiptcore.so (_Z4HH05P10s_wt_recorP9s_iptcore+38)
#03 pc 0012b3a3  /data/app/com.baidu.input-1/lib/arm/libiptcore.so (_Z16ipt_core_refreshP9s_sessionP13s_ipt_libfile+38)
#04 pc 000beee3  /data/app/com.baidu.input-1/lib/arm/libiptcore.so (_ZN7iptcore13ConfigPadImpl16cfg_core_refreshENS_9ConfigPad13ECoreFileTypeEPKc+90)
#05 pc 000a9499  /data/app/com.baidu.input-1/lib/arm/libiptcore.so (Java_com_baidu_iptcore_IptCoreInterface_coreRefresh+84)
#06 pc 0248b425  /data/dalvik-cache/arm/data@<EMAIL><EMAIL>@classes.dex
###### ######################################################## #####
###### ######################################################## #####

# addr2line工具路径
ADDR_PATH="${NDK_PATH}"/toolchains/arm-linux-androideabi-4.9/prebuilt/darwin-x86_64/bin
# 生成的未strip的so的路径
SO_PATH="${PROJECT_PATH}"/iptcore/obj/local/armeabi-v7a

FLAG_BUILD_A=true
FLAG_BUILD_SO=true
FLAG_BUILD_APK=true
FLAG_ADDR2LINE=false

if [ $# == 1 ]; then
  if [ $1 == "apk" ]; then
    FLAG_BUILD_A=false
    FLAG_BUILD_SO=false
  elif [ $1 == "so" ]; then
    FLAG_BUILD_APK=false
  elif [ $1 == "so-only" ]; then
    FLAG_BUILD_A=false
    FLAG_BUILD_APK=false
  elif [ $1 == "addr2line" ]; then
    FLAG_BUILD_A=false
    FLAG_BUILD_APK=false
    FLAG_ADDR2LINE=true
  fi
fi

echo "shell cmd= $FLAG_BUILD_A, $FLAG_BUILD_SO, $FLAG_BUILD_APK, $FLAG_ADDR2LINE"

if [ $FLAG_BUILD_A == true ]; then
    echo "===> STEP Build .A start"
    cd $IPTCORE_PATH/project/NDK/jni/
    $NDK_PATH/ndk-build -j40
    cd $PROJECT_PATH/

    cp $IPTCORE_PATH/project/NDK/obj/local/armeabi-v7a/libiptcore.a ./iptcore/jni/lib/libiptcore.a
    cp $IPTCORE_PATH/project/NDK/jni/lib/libpaddle_light_api_shared.so ./iptcore/jni/lib/libpaddle_light_api_shared.so
    cp $IPTCORE_PATH/project/NDK/jni/lib/libnn-rank.so ./iptcore/jni/lib/libnn-rank.so

    cp $IPTCORE_PATH/source/_pub/_pub_iptcore.h ./iptcore/jni/_pub_iptcore.h
    cp $IPTCORE_PATH/source/_pub/_pub_iptpad.h ./iptcore/jni/_pub_iptpad.h
    cp $IPTCORE_PATH/source/_pub/_pub_util.h ./iptcore/jni/_pub_util.h
    cp $IPTCORE_PATH/source/_pub/_pub_version.h ./iptcore/jni/_pub_version.h
fi

if [ $FLAG_BUILD_SO == true ]; then
  echo "===> STEP Build .SO start"
  cd $PROJECT_PATH/iptcore/jni/
  $NDK_PATH/ndk-build
  cd ../../
  cp ./iptcore/libs/armeabi-v7a/libiptcore.so ./iptcore/src/release/libs/armeabi-v7a/libiptcore.so
  cp ./iptcore/libs/armeabi-v7a/libpaddle_light_api_shared.so ./iptcore/src/release/libs/armeabi-v7a/libpaddle_light_api_shared.so
  cp ./iptcore/libs/armeabi-v7a/libnn-rank.so ./iptcore/src/release/libs/armeabi-v7a/libnn-rank.so
fi

if [ $FLAG_BUILD_APK == true ]; then
  echo "===> STEP Build .APK start"
  # base-apk解压之后所放置的位置
  UNZIP_DIR="${BUILD_DIR}/unzip_demo_dir"

  cd $PROJECT_PATH/
  echo "$(pwd)"

  # 清除原有的编译路径
  rm -rf "$PROJECT_PATH/${UNZIP_DIR}"

  # 解压apk文件
  unzip "$PROJECT_PATH/${BUILD_DIR}/${BASE_APK_NAME}" -d "$PROJECT_PATH/${UNZIP_DIR}/"

  # 清除原有的签名信息
  rm -rf "./${UNZIP_DIR}/META-INF"

  # 拷贝so文件
  SRC_LIB_DIR="./iptcore/src/release/libs/armeabi-v7a"
  DEST_LIB_DIR="./${UNZIP_DIR}/lib/armeabi-v7a"
  echo "cp ${SRC_LIB_DIR} -> ${DEST_LIB_DIR}"
  cp -fr "${SRC_LIB_DIR}/*" "${DEST_LIB_DIR}/"

  # 拷贝dict文件
  SRC_ASSET_DIR="./dict/product/MainLine/assets/dict"
  DEST_ASSET_DIR="./${UNZIP_DIR}/assets/dict"
  echo "cp ${SRC_ASSET_DIR} -> ${DEST_ASSET_DIR}"
  cp -fr "${SRC_ASSET_DIR}/*" "${DEST_ASSET_DIR}/"

  # zip + 签名
  SIGNED_APK="${OUT_APK_NAME}-signed.apk"
  UNSIGNED_APK="${OUT_APK_NAME}-unsigned.apk"
  cd "$PROJECT_PATH/${UNZIP_DIR}"
  zip -r $UNSIGNED_APK .
  mv $UNSIGNED_APK ..
  cd ..
  jarsigner -verbose -keystore ../imeurltest.keystore.jks -storepass baiduime -signedjar \
  $SIGNED_APK $UNSIGNED_APK "imeurltest"
fi

if [ $FLAG_ADDR2LINE == true ]; then
  echo "===> STEP ADDR 2 LINE START"
  $ADDR_PATH/arm-linux-androideabi-addr2line -e ${SO_PATH}/libiptcore.so ${PC_ADDRS}
fi