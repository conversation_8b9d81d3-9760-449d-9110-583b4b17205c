#!/bin/bash
#echo "off"
#这里设置编译需要的变量,需要改成linux环境下的路径
export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64/
export JDKBIN=/usr/lib/jvm/java-8-openjdk-amd64/jre/bin/
echo $JDKBIN
export ANDROIDTOOL=/home/<USER>/android-sdk-linux/build-tools/25.0.2
COMPILE_PATH=$(cd `dirname $0`; pwd)
#COMPILE_PATH=$(pwd)
echo $COMPILE_PATH
OUTPUT_DIR="${COMPILE_PATH}"/ime/build/bakApk
echo $OUTPUT_DIR
OUTPUT_SIGN="${COMPILE_PATH}"/ime/build/outputs/apk_sign
OUTPUT_RELEASE="${COMPILE_PATH}"/output

NDK_BUILD_PATH="${COMPILE_PATH}"/inputcore/jni/ImeCore_NDK_Armeabi
NDK_BUILD_SCRIPT="${NDK_BUILD_PATH}"/BINdkBuild.py
NDK_BUILD_OUTPUT="${NDK_BUILD_PATH}"/output

NDK_NEW_BUILD_PATH="${COMPILE_PATH}"/iptcore
NDK_NEW_BUILD_TOOLS=/home/<USER>/buildkit/android-ndk-r17c/
NDK_NEW_BUILD_OUTPUT="${COMPILE_PATH}"/iptcore/libs/
NDK_NEW_BUILD_UNSTRIP_OUTPUT="${COMPILE_PATH}"/iptcore/obj/local/

echo $NDK_BUILD_PATH
echo $NDK_BUILD_SCRIPT
echo $NDK_NEW_BUILD_PATH
echo $NDK_NEW_BUILD_OUTPUT
echo NDK_NEW_BUILD_UNSTRIP_OUTPUT

LOCAL_PROPATH="${COMPILE_PATH}"/local.properties
if [ ! -f $LOCAL_PROPATH ]; then
    echo cp /home/<USER>/localEnv/local.properties $LOCAL_PROPATH
    cp /home/<USER>/localEnv/local.properties $LOCAL_PROPATH
fi

if [ ! -d $OUTPUT_RELEASE ]; then
    mkdir $OUTPUT_RELEASE
fi
MAPPING_DIR="${COMPILE_PATH}"/ime/build/bakApk
if [ ! -d $MAPPING_DIR ]; then
    mkdir $MAPPING_DIR
fi

flag_value=$1
type=$2
old_commit_id=$3
new_commit_id=$4
compile_branch=$5
#flag_value=test
#type=QQ

# python3=/usr/bin/python3
# RELEASE_CHECK_SCRIPT=/home/<USER>/hudson/BaiduImeBuild/agile/agile_check_release.py
# check_release() {
#     echo "check_release $1"
#     if [ ! -f $RELEASE_CHECK_SCRIPT ];then
#         echo "RELEASE_CHECK_SCRIPT文件不存在"
#     else
#         echo $python3 $RELEASE_CHECK_SCRIPT --path="$COMPILE_PATH" --compile_type="$1" --compile_branch="$compile_branch" --bash_build_type="$type"
#         $python3 $RELEASE_CHECK_SCRIPT --path="$COMPILE_PATH" --compile_type="$1" --compile_branch="$compile_branch" --bash_build_type="$type"
#     fi
# }

copy_product() {
    echo "copy_product name=$1 if_need_sign=$2"
    if [ $2 == "true" ]; then
        echo "step 1 sign v1"
        echo java -jar $COMPILE_PATH/.builds/server_signed_apk.jar $OUTPUT_DIR/$1-unsigned.apk $OUTPUT_DIR/$1-signed-v1.apk
        java -jar $COMPILE_PATH/.builds/server_signed_apk.jar $OUTPUT_DIR/$1-unsigned.apk $OUTPUT_DIR/$1-signed-v1.apk
        
        echo "step 2 Zipalign apk"
        $ANDROIDTOOL/zipalign -f -v 4 $OUTPUT_DIR/$1-signed-v1.apk $OUTPUT_DIR/$1-signed-v1-aligned.apk
        
        echo "step 3 sign v2"
        ./gradlew signApk -PapkDir="$OUTPUT_DIR/$1-signed-v1-aligned.apk" -PoutApk="$OUTPUT_RELEASE/$1-signed.apk"
    else
        cp $OUTPUT_DIR/$1-signed.apk $OUTPUT_RELEASE/$1-signed.apk
    fi
    cp $MAPPING_DIR/$1-mapping.txt $OUTPUT_RELEASE/$1-mapping.txt
    cp $MAPPING_DIR/$1-R.txt $OUTPUT_RELEASE/$1-R.txt
}

check_compile() {
    echo "check compile result: $1 [0 ok 1 failed]"
    if [ $1 == 1 ]; then
      echo "check build failed!!!!!!"
      exit 1
    else
      echo "check build success!!!!!!"
    fi
}

echo $@
#这里设置当前APK输出前缀
APKNAME=$(basename ${COMPILE_PATH})

echo *****All Param********
echo JDKBIN              :[$JDKBIN]
echo ANDROIDTOOL         :[$ANDROIDTOOL]
echo COMPILE_PATH        :[$COMPILE_PATH]
echo OUTPUT_DIR          :[$OUTPUT_DIR]
echo OUTPUT_SIGN         :[$OUTPUT_SIGN]
echo OUTPUT_RELEASE      :[$OUTPUT_RELEASE]
echo MAPPING_DIR         :[$MAPPING_DIR]
echo APKNAME             :[$APKNAME]
echo flag_value          :[$flag_value] [test no_test ndk_test]
echo type                :[$type] [RELEASE QQ AS 91 OEM OTHER BETA PREVIEW EXP]
echo **********************
#read -n 1
#这里写入渠道号和搜索渠道号
echo "ffffffff" > ime/assets/channel
echo "ffffffff" > ime/assets/schannel

if [ $flag_value == "ndk_test" ]; then
#     echo "Step 1 build old Ndk for inputcore"
#     cd $NDK_BUILD_PATH
#     echo python3 $NDK_BUILD_SCRIPT "all"
#     python3 $NDK_BUILD_SCRIPT "all"
#     echo "Step 2 copy inputcore so to output"
#     cp $NDK_BUILD_OUTPUT/libinputcore-new-hw.so $OUTPUT_RELEASE/libinputcore-new-hw.so
#     cp $NDK_BUILD_OUTPUT/libinputcore-new-hw-debug.so $OUTPUT_RELEASE/libinputcore-new-hw-debug.so

    echo "Step 3 build new Ndk for iptcore"
    echo $NDK_NEW_BUILD_TOOLS/ndk-build NDK_PROJECT_PATH=$NDK_NEW_BUILD_PATH
    $NDK_NEW_BUILD_TOOLS/ndk-build NDK_PROJECT_PATH=$NDK_NEW_BUILD_PATH
    echo "Step 4 copy iptcore so to output"
    NDK_ABIS="armeabi-v7a arm64-v8a"
    for abi in $NDK_ABIS; do
     echo " output path "  $OUTPUT_RELEASE/$abi
     mkdir -p $OUTPUT_RELEASE/$abi
     cp $NDK_NEW_BUILD_OUTPUT$abi/libiptcore.so $OUTPUT_RELEASE/$abi/libiptcore.so
     cp $NDK_NEW_BUILD_OUTPUT$abi/libc++_shared.so $OUTPUT_RELEASE/$abi/libc++_shared.so
     cp $NDK_NEW_BUILD_OUTPUT$abi/libpaddle_light_api_shared.so $OUTPUT_RELEASE/$abi/libpaddle_light_api_shared.so
#     cp $NDK_NEW_BUILD_OUTPUT$abi/libmlm-lib.so $OUTPUT_RELEASE/$abi/libmlm-lib.so
     cp $NDK_NEW_BUILD_UNSTRIP_OUTPUT$abi/libiptcore.so $OUTPUT_RELEASE/$abi/libiptcore-unstrip.so
     cp $NDK_NEW_BUILD_UNSTRIP_OUTPUT$abi/libocr.so $OUTPUT_RELEASE/$abi/libocr.so
     cp $NDK_NEW_BUILD_UNSTRIP_OUTPUT$abi/libBaiduOcrSDK.so $OUTPUT_RELEASE/$abi/libBaiduOcrSDK.so
    done
else
    echo "not ndk test flag_value"
    
fi
echo "endendend!!!!!!!!!!!!!"

if [ $flag_value == "ndk_test" ]; then
    echo cd $COMPILE_PATH
    cd $COMPILE_PATH
    echo git log --no-merges -1 --shortstat --pretty=format:"%an|%s"
    git log --no-merges -1 --shortstat --pretty=format:"%an|%s" > $OUTPUT_RELEASE/commit_diff_log.txt
    
    cd $OUTPUT_RELEASE
    echo $OUTPUT_RELEASE
    echo strings *.so |grep GCC
    strings *.so |grep GCC
fi 
exit 0


