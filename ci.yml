Global:
  version: 2.0

Default:
  profile: [NO_TEST]

Profiles:
  - profile:
    name: NO_TEST
    mode: AGENT
    environment:
      cluster: UBUNTU
    build:
      command: sh ./build_gradle.sh no_test no_test ${old_commit_id} ${new_commit_id} ${AGILE_COMPILE_BRANCH};
    artifacts:
      release: true

  - profile:
    name: TEST_NDK
    mode: AGENT
    environment:
      cluster: UBUNTU
    build:
      command: sh ./build_gradle.sh ndk_test ndk ${old_commit_id} ${new_commit_id} ${AGILE_COMPILE_BRANCH};
    artifacts:
      release: true


