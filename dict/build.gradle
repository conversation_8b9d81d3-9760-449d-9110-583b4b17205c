apply plugin: 'com.android.library'

android {

    defaultPublishConfig "productMainLineRelease"

    compileSdkVersion rootProject.ext.compileSdkVersion

    defaultConfig {
        minSdkVersion 16
        targetSdkVersion rootProject.ext.targetSdkVersion

        testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    sourceSets {
        main {
            java.srcDirs = ['src/main/java']
            jni.srcDirs = [] // disable automatic ndk-build call
        }

        productMainLine {
            assets.srcDirs = ['product/MainLine/assets']
            java.srcDirs = ['product/MainLine/src/main/java']
        }

        productHuawei {
            assets.srcDirs = ['product/Huawei/assets']
            java.srcDirs = ['product/Huawei/src/main/java']
        }

        productHonor {
            assets.srcDirs = ['product/Honor/assets']
            java.srcDirs = ['product/Honor/src/main/java']
        }
        productVIVO {
            assets.srcDirs = ['product/VIVO/assets']
            java.srcDirs = ['product/VIVO/src/main/java']
        }
        productOPPO {
            assets.srcDirs = ['product/OPPO/assets']
            java.srcDirs = ['product/OPPO/src/main/java']
        }
        productMi {
            assets.srcDirs = ['product/Mi/assets']
            java.srcDirs = ['product/Mi/src/main/java']
        }

    }
    flavorDimensions 'product'
    productFlavors {
        productMainLine {
            dimension 'product'
        }
        productHuawei {
            dimension 'product'
        }
        productHonor {
            dimension 'product'
        }
        productVIVO {
            dimension 'product'
        }
        productOPPO {
            dimension 'product'
        }
        productMi {
            dimension 'product'
        }
    }
}

dependencies {
    implementation 'com.android.support:appcompat-v7:28.0.0'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'com.android.support.test:runner:1.0.2'
    androidTestImplementation 'com.android.support.test.espresso:espresso-core:3.0.2'

    // 词库依赖于iptcore
    compileOnly project(':iptcore')

    // pyramid 组件化框架
    implementation "com.baidu.pyramid:pyramid-annotation:0.1.9"
}

apply from: 'maven_publishing.gradle'