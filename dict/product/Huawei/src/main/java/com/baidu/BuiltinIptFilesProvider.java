package com.baidu;

import com.baidu.iptcore.ImeCoreConsts;
import com.baidu.iptcore.dependency.AbsBuiltinIptFilesProvider;
import com.baidu.pyramid.annotation.Service;
import com.baidu.pyramid.annotation.Singleton;

import java.util.ArrayList;
import java.util.List;

@Service
@Singleton
public class BuiltinIptFilesProvider extends AbsBuiltinIptFilesProvider {
    @Override
    public List<String> getFlavorBuiltinIptFiles() {
        return new ArrayList<String>() {
            {
                add(ImeCoreConsts.IPTCORE_DICT_ZY); // py3zhuyin.bin 华为荣耀内置
                add(ImeCoreConsts.IPTCORE_DICT_CANGJIE); // cj2dict.bin 华为荣耀内置
                add(ImeCoreConsts.IPTCORE_DICT_CANGJIE_QUICK);  // cj2dict_quick.bin 华为荣耀内置
            }
        };
    }
}
