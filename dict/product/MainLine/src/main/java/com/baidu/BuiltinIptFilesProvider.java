package com.baidu;

import com.baidu.iptcore.dependency.AbsBuiltinIptFilesProvider;
import com.baidu.pyramid.annotation.Service;
import com.baidu.pyramid.annotation.Singleton;

import java.util.Collections;
import java.util.List;

@Service
@Singleton
public class BuiltinIptFilesProvider extends AbsBuiltinIptFilesProvider {
    @Override
    public List<String> getFlavorBuiltinIptFiles() {
        return Collections.emptyList();
    }
}
