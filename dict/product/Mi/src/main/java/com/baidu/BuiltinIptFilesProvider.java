package com.baidu;

import com.baidu.iptcore.ImeCoreConsts;
import com.baidu.iptcore.dependency.AbsBuiltinIptFilesProvider;
import com.baidu.pyramid.annotation.Service;
import com.baidu.pyramid.annotation.Singleton;

import java.util.ArrayList;
import java.util.List;

@Service
@Singleton
public class BuiltinIptFilesProvider extends AbsBuiltinIptFilesProvider {
    @Override
    public List<String> getFlavorBuiltinIptFiles() {
        return new ArrayList<String>() {
            {
                add(ImeCoreConsts.INSTALL_FILE_FASTINPUT_MI); // 专门用于小米13以上，显示小米logo的快速输入词典
                add(ImeCoreConsts.IPTCORE_DICT_HW_GESTURE); // 增加内置手写笔势识别
            }
        };
    }
}
