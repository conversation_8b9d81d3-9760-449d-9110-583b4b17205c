package com.baidu;

import com.baidu.iptcore.ImeCoreConsts;
import com.baidu.iptcore.dependency.AbsBuiltinIptFilesProvider;
import com.baidu.pyramid.annotation.Service;
import com.baidu.pyramid.annotation.Singleton;

import java.util.Collections;
import java.util.List;

@Service
@Singleton
public class BuiltinIptFilesProvider extends AbsBuiltinIptFilesProvider {
    @Override
    public List<String> getFlavorBuiltinIptFiles() {
        // OPPO 增加内置手写笔势识别
        return Collections.singletonList(ImeCoreConsts.IPTCORE_DICT_HW_GESTURE);
    }
}
