package com.baidu;

public interface ImeCoreDictConsts {

    // 词库大小：手写模板大小
    int IPTFILE_WT_LENGTH = 3002182;

    // 词库大小：cz5.bin的大小
    int IPTFILE_CZ_LENGTH = 9605988;

    // 词库大小：ipt3sym_sys.bin 的大小 
    int IPTFILE_SYS_LENGTH = 23608;

    // cz5.bin
    String czMd5 = "5b321c7ab8ffb7569121e77defbec59c";

    // fanti3.bin
    String ftMD5 = "bfa8176df7055e73e6140e3c16bdb7fc";

    // bh4.bin
    String bhMD5 = "17e35bc6d40c8790faf48b3b5b410cc3";

    // wb486.bin, 五笔86的输入方案
    String wbMD5 = "70b8bc74400d6122015c25f50e9db4e3";

    // wb498.bin, 五笔98的输入方案
    String wb98MD5 = "8fa50508e25e2216f14c290e5b383d4d";

    // en5.bin
    String enMD5 = "140fa886889f7a11ffc92a66159105b3";

    // hw7.bin
    String iptwtMD5 = "645c4b3c6f2a1c07c87436239369512c";

    // ipt3sym_sys.bin
    String symMD5 = "4a83f308073a2b9ed956d14cb3fad509";

    // iec3dict.bin
    String correctMD5 = "0f5caa644b706d534d7916ecb9e2179b";

    // py3zhuyin.bin
    String zhuyinMD5 = null;

    // blind_assistance.bin
    String blindAssistanceMD5 = "6a7ad3028be48b7f67293ca3cbc34c9b";

    // py3corr2_tone.bin
    String pyToneMD5 = "3714a537094a5b8f8df7aa79eaebae4e";

    // py3transprob.bin
    String py3transprobMD5 = "37820685a457a60ba6943cf525dd15f5";

    // ttffilter.ini
    String ttffilterMD5 = "b5ce9153b4cb4bdc1926a800da4cdf74";

    // sens.bin
    String sensMD5 = "72371f43fd9c308e33b3013487042f43";

    // app_map2.bin
    String appMap2MD5 = "ed432c38f270430f367eb86a75e8eaaf";

    // hw_lic7.bin
    String hwLic7MD5 = "945a08596def08d458e196c744973879";

}