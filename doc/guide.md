[TOC]

# 组件概述

## 组件用途和输出方式

输入法的输入组件，为输入法提供了基础输入相关的接口和对内核的封装。仓库地址为：http://icode.baidu.com/repos/baidu/inputmethod/iptandroiddemo/tree/master

解耦为组件的初衷，是为了实现两个目的：
1. 输入模块的代码解耦
2. 为新旧内核的平稳过渡提供支持
因此，我们组件的工程结构也体现了上述两个目的。

针对第一点，输入组件输出aar包，包里面包含内核.a、词库、jni代码、内核so、java层的接口层，ime工程不再关心输入模块的细节，通过gradle依赖引入输入组件。

针对第二点，输入组件目前包含两个Android Module，分别是**inputcore模块**(旧内核模块)和**iptcore模块**(新内核模块)。这两个模块的名字没有区分新旧内核，所以需要记一下，所有含inputcore的都表示旧内核，所有含iptcore的都表示新内核。新旧内核模块各有两种打包方式：release和mock，这可以组合出新内核接入方式和旧内核接入方式，Android客户端即可切换为新内核或旧内核。

输入组件在ime中的定位，是『平台库』，这表示它可以理解为独立的SDK，不存在和其他组件的互相调用，也不涉及业务相关的逻辑，是一个可以完全独立出去的库。

## 发布和客户端集成方式

输入组件通过gradle脚本发布到Android端的maven仓库中，和Android端其他组件一样。inputcore和iptcore模块分别发布。发布脚本可以参考`maven_push.gradle`。

发布产物的名字定义在`gradle.properties`中，截取如下：

```
POM_NAME_INPUT=ime-inputcore
POM_ARTIFACT_ID_INPUT=ime-inputcore
POM_PACKAGING_INPUT=aar
POM_DESCRIPTION_INPUT=旧内核组件
POM_VERSION_NAME_INPUT=0.1.8
POM_VERSION_CODE_INPUT=1

POM_NAME_IPTCORE=ime-iptcore
POM_ARTIFACT_ID_IPTCORE=ime-iptcore
POM_PACKAGING_IPTCORE=aar
POM_DESCRIPTION_IPTCORE=新内核组件
POM_VERSION_NAME_IPTCORE=*******
POM_VERSION_CODE_IPTCORE=1
```
目前，aar的发布，已经和QA团队的Agile集成完毕。在组件中提测时，会自动编译模块的aar包并上传到maven仓库，完成后会自动发提测邮件。
```
// 提测msg示例：包含test和newcore关键字，则发布新内核aar
sdcime=[*******][test][newcore][【新内核】提测(for dev_9_0_1_50)][提测]

// 提测msg示例：仅包含test关键字，则发布旧内核aar
sdcime=[0.1.10][test][【旧内核】内核更新至********(67371051)，修复部分出词的问题][内核更新]
```

**inputcore模块**(旧内核模块)和**iptcore模块**(新内核模块)均有两种打包方式，release和mock。主要区别是release会包含完整的代码和资源文件，mock只包含能使编译通过的必要源码。

- 接入旧内核的集成方式：旧内核的release+新内核的mock
- 接入新内核的集成方式：旧内核的mock+新内核的release

修改集成方式，通过build.gradle中的`defaultPublishConfig 'release'`来修改。通常，我们拉好分支后，就修改两个模块的发布方式，后续就不需要再改了。

客户端集成时，只需要修改对应模块的版本号：

```
implementation "com.baidu.input:ime-iptcore:*******"
implementation "com.baidu.input:ime-inputcore:0.1.10"
```

# 工程结构

```
iptandroid.demo
├── README.md
├── app  #输入模块的demo工程
├── inputcore  #旧内核组件
    ├── jni  #旧内核的jni目录
    ├── src  #旧内核的java源码目录
	    ├── main
	    ├── release  #release包中需要的内容
	    	├── assets  #资源目录，放词库
		    └── libs  #lib目录，放so
	    ├── build.gradle  #模块编译脚本
	    └── maven_push.gradle  #模块发布脚本
    ├── build.gradle  #模块编译脚本
    └── maven_push.gradle  #模块发布脚本
├── iptcore  #新内核组件
    ├── jni  #新内核的jni目录
    ├── src  #新内核的java源码目录
	    ├── main
	    ├── release  #release包中需要的内容
	    	├── assets  #资源目录，放词库
		    └── libs  #lib目录，放so
	    ├── build.gradle  #模块编译脚本
	    └── maven_push.gradle  #模块发布脚本
    ├── build.gradle  #模块编译脚本
    └── maven_push.gradle  #模块发布脚本
├── build.gradle  #工程编译的gradle脚本
├── build_gradle.sh  #持续集成的agile编译脚本
├── ci.yml  #CI配置文件
├── CoreReplace.py  #旧内核替换脚本工具
├── CoreReplaceNew.py  #新内核替换脚本工具
├── gradle.properties  #gradle脚本的配置文件
└── local.properties  #本地配置
```

# 内核替换

工程目录下有CoreReplace.py和CoreReplaceNew.py脚本，用于内核提测邮件出来之后，替换组件内核。

使用方法如下（3步）：
```
使用方法：
1. 将内核产物直接放在工程目录下（可以直接在AndroidStudio的terminal中执行内核提测邮件中的wget命令）
2. 设置本机的NDKPATH
3. python CoreReplace.py
4. 执行后，根据terminal的输出提示，将so文件大小和词库md5值替换到代码指定位置
5. jni目录下的内容commit， msg含[corecompile]，线上编译so
6. 编译产物出来后，替换libs下的so文件，和其他内容一起提交
脚本会执行如下几步：
1. copy file: 将内核的.h和.a文件拷贝到工程目录下的正确位置
2. unzip Android.zip & rename: 解压内核词库文件，进行适当重命名操作
3. copy Android/* -> asset/ipt/* : 词库文件拷贝到asset的对应位置
4. NDK : ndk build编译so，并拷贝到libs目录
5. print so size and md5: 打印so的文件大小和词库的md5值，用于手动拷贝到代码中的对应位置
```


# 接口设计

这里只讨论新内核的接口设计。

## 接口使用说明
新内核的所有native方法全部集成在`IptCoreInterface.java`中。这个java文件相当于将内核接口以裸接口的形式提供给java层。
新内核组件在`IptCoreInterface`之上，进一步做了封装，用于更进一步屏蔽内核的细节。具体来说，包括如下几个方面：

- 内核初始化和关闭

```
// 初始化内核的Callback和Configuration
Context appContext = context.getApplicationContext();
ImeCoreConfiguration configuration = new ImeCoreConfiguration.Builder()
        .context(appContext)
        .enableLog(true)
        .dictionaryInstaller(new DefaultDictInstaller())
        .dictionaryPath(getDictionaryDir())
        .platformEnv(imeCoreCallback)
        .imeCoreCallback(new DefaultCoreDutyHandler(imeCoreCallback))
        .packageInfo(pi)
        .build();

// 初始化内核
ImeCoreManager.open(configuration);

// 关闭内核
ImeCoreManager.close();
```

- 输入接口

```
ImeCoreManager.getPad().switchKeyboard(ImeCoreConsts.PAD_PY9);
```

- Config接口

```
ImeCoreManager.getConfig().setIptVersion(Global.verName);
```

- 词库接口

```
ImeCoreManager.getCellConfig().installKwdCell(filePath);
```

## 异步输入模型

新内核组件集成了异步输入能力。主要实现逻辑在`CoreThreadEngine`中，使用HandlerThread驱动异步输入。
异步输入有两个核心要素：

- 快速点击时，多个排队的按键输入可以并做一次查询，只刷新一次UI
- 内核onDutyInfo时，将需要的数据直接在异步线程查询封装完毕，再刷给主线程

而针对其他的内核接口，如果需要抛到异步线程，则使用如下两种方式：

```
// 执行一个无返回值的任务
mCoreThreadEngine.executeTask(() -> super.actCandRefresh(candIdx, url));

// 执行一个有返回值的任务
String msg = mCoreThreadEngine.executeFutureTask(() -> super.getSugSourceMsg());
```

另一方面，上述几个操作对象的状态如下：

- getPad()：接口全部内核线程执行
- getConfig(): 接口当前线程直接执行
- getCellConfig(): 接口全部内核线程执行，当前线程等待

> 注：目前IptCoreInterface所有接口均是synchronize的，是否需要还未细考量

## 调试

Java层筛选iptcore的logcat即可。