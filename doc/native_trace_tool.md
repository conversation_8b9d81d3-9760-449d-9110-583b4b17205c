[TOC]

# 使用NativeTrace工具评估内核接口连续输入性能

## 为什么需要评估内核接口耗时性能
由于各厂商对输入的性能要求越来越高，加上内核现在承接的业务越来越多，我们想要：1) 更精细化地评估内核接口随着版本迭代的耗时变化(防劣化); 2) 对一些新的功能进行性能比较（如大词库）。因此我们希望模拟实际的连续输入场景，有更自动化的方法去总体地评估内核接口的耗时。
这里要解释下为什么不让内核去做: 内核有自己的评测，但是由于运行在PC中，和在移动设备上运行有量级的差异。我们这里主要是关注集成到版本之中的变化，和内核自己的不冲突。

## 工具和使用

1) 连续输入评测（不包含native_trace工具）

- 内核接口耗时的打印在组件中自动就有，宿主的Performance包自动打印内核接口耗时信息到文件。
- 自动化测试工具中集成了continuousinput测试case，对输入法进行连续输入测试。（测试数据的灌入：目前是用户轨迹中获取的高频词语，py9和py26）。
- 自动化测试结束后，使用配套脚本工具进行分析（参考tool_continuousinput_parser工具）。
    - tool_continuousinput_parser/main.py 主脚本，负责将测试后，真机生成的数据拉取到PC后，再进行统计分析和结果输出
    - tool_continuousinput_parser/input_data/ 灌入数据
    - tool_continuousinput_parser/res/ 示例数据和生成报告

2) 连续输入评测（包含native_trace工具）
native_trace工具是基于instrument方案的native耗时监控工具，原理是对我们自己的源码进行插桩，通过进入和退出的耗时信息，记录耗时堆栈分布并输出。
由于需要插桩，因此该工具侵入到编译过程，需要手动打perf包进行评测。

打包步骤：

- 准备好libiptcore-hooked.a
- 运行工程路径下的NativeTraceEnable.py脚本，负责打包hook后的so
- 修改工程路径下的build.gradle文件，enableNativeTrace改成true
- 修改gradle.properties，版本号后添加-perf，用于区分普通版本和perf版本
- 提测，集成到宿主工程中

分析步骤：

- 自动化测试结束后，分析脚本仍然在tool_continuousinput_parser文件夹下
    - tool_continuousinput_parser/NativeTraceParser.py nativeTrace解析脚本，自动集成到main.py的，不用额外调用
    - tool_continuousinput_parser/addr2line 用于解析时retrace symbol的，会自动拷贝未strip的so到这个工作路径
    - tool_continuousinput_parser/flamegraph 火焰图工具，外部的，可以自己从git上download下来


## TODO
1) 接口耗时打印目前还是代码写的，需要做AOP的处理
2) 现在是instrument方案，后续会换成sample方案吗（待调研）