apply plugin: 'maven-publish'
apply plugin: 'signing'

// 本地仓库VersionName
def localVersion = "${LOCAL_REV_BASE}.LOCAL-SNAPSHOT"

// 声明变量记录maven库地址
def mavenRepositoryUrl

// 本地仓库name
def localMavenName = "LocalMaven"

// 远端仓库name
def remoteMavenName = "RemoteMaven"

// 判断是发布到正式库,还是snapshots库
if (isReleaseBuild()) {
    println 'RELEASE BUILD'
    // 下面的库地址指向的是我们私有仓库的Releases 仓库
    mavenRepositoryUrl = hasProperty('RELEASE_REPOSITORY_URL') ? RELEASE_REPOSITORY_URL
            : "${MAVEN_URL}/maven-releases/"
} else {
    println 'SNAPSHOTS BUILD'
    // 下面的库地址指向的是我们私有仓库的snapshots 仓库
    mavenRepositoryUrl = hasProperty('SNAPSHOT_REPOSITORY_URL') ? SNAPSHOT_REPOSITORY_URL
            : "${MAVEN_URL}/maven-snapshots/"
}
// NEXUS_USERNAME等变量在我们主项目的gradle.properties中可以找到
def getRepositoryUsername() {
    return hasProperty('NEXUS_USERNAME') ? NEXUS_USERNAME : ""
}

def getRepositoryPassword() {
    return hasProperty('NEXUS_PASSWORD') ? NEXUS_PASSWORD : ""
}
// 根据我们在gradle.properties中声明的版本名称,来分辨是Release版本还是 snapshots版本
def isReleaseBuild() {
    return !POM_VERSION_NAME.contains("SNAPSHOT");
}

publishing {
    publications {
        if (project.plugins.hasPlugin('com.android.library')) {
            project.tasks.create(name: 'AndroidSourcesJar', type: Jar) {
                from android.sourceSets.main.java.source
                classifier 'sources'
            }

            SnapshotEncrypeChiper(MavenPublication) {
                // publish to local maven，上传源码
                artifact AndroidSourcesJar

                artifactId = POM_ARTIFACT_ID_ENCRYPT_CHIPER
                groupId = POM_GROUP_ID
                version = localVersion
                pom {
                    name = POM_NAME_ENCRYPT_CHIPER
                    packaging = POM_PACKAGING_ENCRYPT_CHIPER
                    description = POM_DESCRIPTION_ENCRYPT_CHIPER
                    url = POM_URL
                }
            }

            ReleaseEncrypeChiper(MavenPublication) {
                // publish to local maven，上传源码
                artifact AndroidSourcesJar

                artifactId = POM_ARTIFACT_ID_ENCRYPT_CHIPER
                groupId = POM_GROUP_ID
                version = POM_VERSION_NAME_ENCRYPT_CHIPER
                pom {
                    name = POM_NAME_ENCRYPT_CHIPER
                    packaging = POM_PACKAGING_ENCRYPT_CHIPER
                    description = POM_DESCRIPTION_ENCRYPT_CHIPER
                    url = POM_URL

                    licenses {
                        license {
                            name = POM_LICENCE_NAME
                            url = POM_LICENCE_URL
                            distribution = POM_LICENCE_DIST
                        }
                    }

                    developers {
                        developer {
                            id = POM_DEVELOPER_ID
                            name = POM_DEVELOPER_NAME
                        }
                    }

                    scm {
                        url = POM_SCM_URL
                        connection = POM_SCM_CONNECTION
                        developerConnection = POM_SCM_DEV_CONNECTION
                    }
                }
            }
        }
    }
    repositories {
        maven {
            name localMavenName
            url uri(localMavenDir)
        }
        maven {
            name remoteMavenName
            url mavenRepositoryUrl
            credentials {
                username getRepositoryUsername()
                password getRepositoryPassword()
            }
            allowInsecureProtocol = true
        }
    }
}

afterEvaluate {
    publishing.publications.each {
        def bundle = tasks.findByName('bundleReleaseAar')
        if (bundle != null) {
            def pub = publishing.publications."$it.name" as MavenPublication
            pub.artifact(bundle) {
                extension 'aar'
            }
            pub.pom.withXml {
                def node = it.asNode()
                def dependencies = node.appendNode('dependencies')
                configurations.debugRuntimeClasspath.incoming.dependencies.findAll {
                    it instanceof ModuleDependency
                }.each {
                    def dependency = dependencies.appendNode('dependency')
                    dependency.appendNode('groupId', it.group)
                    dependency.appendNode('artifactId', it.name)
                    dependency.appendNode('version', it.version)
                    if (it instanceof ExternalModuleDependency) {
                        if (!it.artifacts.isEmpty()) {
                            it.artifacts.first().with {
                                dependency.appendNode('classifier', it.classifier)
                                dependency.appendNode('type', it.type)
                            }
                        }
                        if (!it.excludeRules.isEmpty()) {
                            def exclusions = dependency.appendNode('exclusions')
                            it.excludeRules.each {
                                def exclusion = exclusions.appendNode('exclusion')
                                exclusion.appendNode('groupId', it.group ?: "*")
                                exclusion.appendNode('artifactId', it.module ?: "*")
                            }
                        }
                    }
                    dependency.appendNode('scope', 'compile')
                }
            }
        }
    }
}

// 兼容之前maven插件的uploadArchives
task uploadArchives(group: "baiduime-publishing", dependsOn: "publishReleaseEncrypeChiperPublicationToRemoteMavenRepository")

task publishingSnapshotToLocalMaven(group: "baiduime-publishing", dependsOn: "publishSnapshotEncrypeChiperPublicationToLocalMavenRepository")

task publishingReleaseToLocalMaven(group: "baiduime-publishing", dependsOn: "publishReleaseEncrypeChiperPublicationToLocalMavenRepository")


signing {
    required {
        // 之前老版本maven_push.gradle中，gradle.taskGraph.hasTask("uploadArchives")返回false，string的行参应该是path
        // 返回true，会报it has no configured signatory错误
        // isReleaseBuild() && (gradle.taskGraph.hasTask(uploadArchives) || gradle.taskGraph.hasTask(publishReleaseImeSharePublicationToRemoteMavenRepository))
        false
    }
    sign configurations.archives
}

tasks.withType(Sign) {
    onlyIf { isReleaseBuild() }
}

tasks.withType(PublishToMavenLocal) {
    onlyIf { false } // skip publish to local
}