package com.baidu.util;

import com.baidu.iptcore.ImeCoreManager;

/**
 * 参考encrypt-chipper中的Base64Encoder，类名和方法名不可改变
 */
public class Base64Encoder {

    public Base64Encoder() {
    }

    public static final byte[] B64Encode(byte[] data) {
        byte[] encodes;
        try {
            encodes = ImeCoreManager.getPad().b64Encode(data);
        } catch (Exception var3) {
            encodes = data;
        } catch (Error var4) {
            encodes = data;
        }

        return encodes;
    }

    public static final String B64Encode(String str, String charsetName) {
        String decode = null;
        if (str != null && str.length() > 0 && charsetName != null) {
            try {
                byte[] bytes = str.getBytes(charsetName);
                bytes = B64Encode(bytes);
                if (bytes != null) {
                    decode = new String(bytes, charsetName);
                }

                Object var5 = null;
            } catch (Exception var4) {
                decode = null;
            }
        }

        return decode;
    }

    public static final byte[] B64Decode(byte[] data) {
        byte[] decodes;
        try {
            decodes = ImeCoreManager.getPad().b64Decode(data);
        } catch (Exception var3) {
            decodes = data;
        } catch (Error var4) {
            decodes = data;
        }

        return decodes;
    }

    public static final String B64Decode(String str, String charsetName) {
        String decode = null;
        if (str != null && str.length() > 0 && charsetName != null) {
            try {
                byte[] bytes = str.getBytes(charsetName);
                bytes = B64Decode(bytes);
                if (bytes != null) {
                    decode = new String(bytes, charsetName);
                }

                Object var5 = null;
            } catch (Exception var4) {
                decode = null;
            }
        }

        return decode;
    }

    public static final int B64GetVersion() {
        int ver;
        try {
            ver = ImeCoreManager.getPad().getEncryptB64Version();
        } catch (Exception var2) {
            ver = 0;
        } catch (Error var3) {
            ver = 0;
        }

        return ver;
    }

}
