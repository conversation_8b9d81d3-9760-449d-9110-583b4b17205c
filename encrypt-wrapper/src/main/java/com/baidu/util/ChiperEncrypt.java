package com.baidu.util;

import com.baidu.iptcore.ImeCoreManager;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * 参考encrypt-chipper中的ChiperEncrypt，类名和方法名不可改变
 */
public class ChiperEncrypt {
    private static final String HASH_SHA1_ALGLRITHM = "SHA-1";
    private MessageDigest sha1Util;

    public ChiperEncrypt() {
        this.initSha1Param();
    }

    private void initSha1Param() {
        try {
            this.sha1Util = MessageDigest.getInstance("SHA-1");
        } catch (NoSuchAlgorithmException var2) {
            this.sha1Util = null;
        }

    }

    public final synchronized boolean ChiperInit() {
        // do nothing
        return true;
    }

    public final synchronized void ChiperDestroy() {
        // do noting

    }

    public final int ChiperGetVersion() {
        int ver;
        try {
            ver = ImeCoreManager.getPad().getEncryptVersion();
        } catch (Exception var3) {
            ver = 0;
        } catch (Error var4) {
            ver = 0;
        }

        return ver;
    }

    public final byte[] AESEncrypt(byte[] data) {
        byte[] encode;
        try {
            encode = ImeCoreManager.getPad().aesEncrypt(data);
        } catch (Exception var4) {
            encode = null;
        } catch (Error var5) {
            encode = null;
        }

        return encode;
    }

    public final byte[] AESEncryptV2(byte[] data) {
        byte[] encode;
        try {
            encode = ImeCoreManager.getPad().aesEncryptV2(data);
        } catch (Exception var4) {
            encode = null;
        } catch (Error var5) {
            encode = null;
        }

        return encode;
    }

    public final byte[] AESDecrypt(byte[] data) {
        byte[] decode;
        try {
            decode = ImeCoreManager.getPad().aesDecrypt(data);
        } catch (Exception var4) {
            decode = null;
        } catch (Error var5) {
            decode = null;
        }

        return decode;
    }

    public final byte[] AESB64Encrypt(byte[] data) {
        byte[] encode;
        try {
            encode = ImeCoreManager.getPad().aesB64Encrypt(data);
        } catch (Exception var4) {
            encode = null;
        } catch (Error var5) {
            encode = null;
        }

        return encode;
    }

    public final String AESB64Encrypt(String str, String charsetName) {
        String encrypt = null;
        if (str != null && str.length() > 0 && charsetName != null) {
            try {
                byte[] bytes = str.getBytes(charsetName);
                bytes = this.AESB64Encrypt(bytes);
                if (bytes != null) {
                    encrypt = new String(bytes, charsetName);
                }

                Object var6 = null;
            } catch (Exception var5) {
                encrypt = null;
            }
        }

        return encrypt;
    }

    public final byte[] AESB64EncryptV2(byte[] data) {
        Object var2 = null;

        byte[] encode;
        try {
            encode = ImeCoreManager.getPad().aesB64EncryptV2(data);
        } catch (Exception var4) {
            encode = null;
        } catch (Error var5) {
            encode = null;
        }

        return encode;
    }

    public final String AESB64EncryptV2(String str, String charsetName) {
        String encrypt = null;
        if (str != null && str.length() > 0 && charsetName != null) {
            try {
                byte[] bytes = str.getBytes(charsetName);
                bytes = this.AESB64EncryptV2(bytes);
                if (bytes != null) {
                    encrypt = new String(bytes, charsetName);
                }

                Object var6 = null;
            } catch (Exception var5) {
                encrypt = null;
            }
        }

        return encrypt;
    }

    public final byte[] AESB64Decrypt(byte[] data) {
        Object var2 = null;

        byte[] decode;
        try {
            decode = ImeCoreManager.getPad().aesB64Decrypt(data);
        } catch (Exception var4) {
            decode = null;
        } catch (Error var5) {
            decode = null;
        }

        return decode;
    }

    public final String AESB64Decrypt(String str, String charsetName) {
        String decrypt = null;
        if (str != null && str.length() > 0 && charsetName != null) {
            try {
                byte[] bytes = str.getBytes(charsetName);
                bytes = this.AESB64Decrypt(bytes);
                if (bytes != null) {
                    decrypt = new String(bytes, charsetName);
                }

                Object var6 = null;
            } catch (Exception var5) {
                decrypt = null;
            }
        }

        return decrypt;
    }

    public final byte[] RSAEncrypt(byte[] data) {
        byte[] encode;
        try {
            encode = ImeCoreManager.getPad().rsaEncrypt(data);
        } catch (Exception var4) {
            encode = null;
        } catch (Error var5) {
            encode = null;
        }

        return encode;
    }

    public final byte[] encryptBySHA1(byte[] originalBytes) {
        if (this.sha1Util != null) {
            this.sha1Util.update(originalBytes);
            return this.sha1Util.digest();
        } else {
            return null;
        }
    }

    public final boolean isSha1ValueEqual(byte[] digesta, byte[] digestb) {
        return MessageDigest.isEqual(digesta, digestb);
    }

    public final byte[] decryptByRSAPublic(byte[] data) {
        byte[] decode;
        try {
            decode = ImeCoreManager.getPad().rsaDecrypt(data);
        } catch (Exception var4) {
            decode = null;
        } catch (Error var5) {
            decode = null;
        }

        return decode;
    }
}
