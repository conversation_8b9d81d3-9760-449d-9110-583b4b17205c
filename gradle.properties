# Project-wide Gradle settings.

# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.

# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html

# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
org.gradle.jvmargs=-Xmx1536m

# When configured, <PERSON>rad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true

##############################################################################
## Library Version
##############################################################################

MIN_SDK_VERSION = 18
TARGET_SDK_VERSION = 31
COMPILE_SDK_VERSION = 31
BUILD_TOOLS_VERSION = 28.0.3

KOTLIN_VERSION=1.6.21
GRADLE_VERSION=7.2.2
APT_VERSION=1.8

MOCKITO_VERSION=2.28.2

SUPPORT_V4_VERSION=27.1.1
SUPPORT_V7_VERSION=27.1.1

IME_BASE_VERSION=0.2.33

APPCOMPAT_VERSION=1.0.0
GSON_VERSION=2.8.0

COCO_APT_VERSION=1.1.1

##############################################################################
## Maven Related
##############################################################################
# Mavenä»åºå°å
MAVEN_URL=http://szzj-tbag-psscholar-table1109.szzj.baidu.com:8081/repository
# properties for maven2 repository
# å¨POMæä»¶ä¸­ä½¿ç¨çgroup ID
POM_GROUP_ID=com.baidu.input
# POMæä»¶ä¸­æåä½ ç½ç«çå°å
POM_URL=https://www.baidu.com
# SCMæ¯æçæ¬ç®¡çå·¥å·,ä¸ä¸è¯´ä»çç¸å³ä¿¡æ¯
POM_SCM_URL=https://www.baidu.com
POM_SCM_CONNECTION=https://www.baidu.com
POM_SCM_DEV_CONNECTION=https://www.baidu.com
# ä½ çå¼æ¾åè®®ç¸å³ä¿¡æ¯
POM_LICENCE_NAME= Apache License Version 2.0
POM_LICENCE_URL= https://www.baidu.com
POM_LICENCE_DIST=Apache License Version 2.0
# å¼åèçç¸å³ä¿¡æ¯
POM_DEVELOPER_ID=baiduime
POM_DEVELOPER_NAME=baiduime

POM_VERSION_NAME=0.1.1
POM_VERSION_CODE=1

# åºåç§°-æ§åæ ¸ï¼éæ¸åºå¼
POM_NAME_INPUT=ime-inputcore
# artifactId
POM_ARTIFACT_ID_INPUT=ime-inputcore
# åºçæåæ ¼å¼ä¸ºaar, å¸¸è§çè¿æjar
POM_PACKAGING_INPUT=aar
# åºçæè¿°,è¯´æä»æ¯å¹²å¥ç
POM_DESCRIPTION_INPUT=æ§åæ ¸ç»ä»¶
# è¦åå¸ççæ¬å·ï¼snapshotsçæ¬ä¸ç¨æå¨æ¹ï¼æäº¤ä»£ç æ¶ä¼ç±èæ¬èªå¨å¤ç
POM_VERSION_NAME_INPUT=1.0.1
# åå¸çæ¬version codeï¼å¯¹äºaarèè¨è¯¥å¼æ æä¹ï¼ä¿æä¸åå³å¯
POM_VERSION_CODE_INPUT=1

# åºåç§°-æ°åæ ¸ï¼åæ ¸æ ¸å¿
POM_NAME_IPTCORE=ime-iptcore
# artifactId
POM_ARTIFACT_ID_IPTCORE=ime-iptcore
# åºçæåæ ¼å¼ä¸ºaar, å¸¸è§çè¿æjar
POM_PACKAGING_IPTCORE=aar
# åºçæè¿°,è¯´æä»æ¯å¹²å¥ç
POM_DESCRIPTION_IPTCORE=æ°åæ ¸ç»ä»¶
# è¦åå¸ççæ¬å·ï¼snapshotsçæ¬ä¸ç¨æå¨æ¹ï¼æäº¤ä»£ç æ¶ä¼ç±èæ¬èªå¨å¤ç
POM_VERSION_NAME_IPTCORE=8.16.3_F.9098.13
# åå¸çæ¬version codeï¼å¯¹äºaarèè¨è¯¥å¼æ æä¹ï¼ä¿æä¸åå³å¯
POM_VERSION_CODE_IPTCORE=1

# åºåç§°-æ°åæ ¸è¯åº
POM_NAME_IPTCORE_DICT=ime-iptcore-dict
# artifactId
POM_ARTIFACT_ID_IPTCORE_DICT=ime-iptcore-dict
# åºçæåæ ¼å¼ä¸ºaar, å¸¸è§çè¿æjar
POM_PACKAGING_IPTCORE_DICT=aar
# åºçæè¿°,è¯´æä»æ¯å¹²å¥ç
POM_DESCRIPTION_IPTCORE_DICT=æ°åæ ¸è¯åºç»ä»¶

# åºåç§°-æå¿å¤§æ¨¡å
POM_NAME_IPTCORE_WENXIN=ime-iptcore-wenxin
# artifactId
POM_ARTIFACT_ID_IPTCORE_WENXIN=ime-iptcore-wenxin
# åºçæåæ ¼å¼ä¸ºaar, å¸¸è§çè¿æjar
POM_PACKAGING_IPTCORE_WENXIN=aar
# åºçæè¿°,è¯´æä»æ¯å¹²å¥ç
POM_DESCRIPTION_IPTCORE_WENXIN=æ°åæ ¸æå¿å¤§æ¨¡å

# åºåç§° å è§£å¯åºï¼æ§ï¼
POM_NAME_ENCRYPT_CHIPER=encrypt-chiper
# artifactId
POM_ARTIFACT_ID_ENCRYPT_CHIPER=encrypt-chiper
# åºçæåæ ¼å¼ä¸ºaar, å¸¸è§çè¿æjar
POM_PACKAGING_ENCRYPT_CHIPER=aar
# åºçæè¿°,è¯´æä»æ¯å¹²å¥ç
POM_DESCRIPTION_ENCRYPT_CHIPER=æ§å è§£å¯åº
# è¦åå¸ççæ¬å·ï¼snapshotsçæ¬ä¸ç¨æå¨æ¹ï¼æäº¤ä»£ç æ¶ä¼ç±èæ¬èªå¨å¤ç
POM_VERSION_NAME_ENCRYPT_CHIPER=1.0.0
# åå¸çæ¬version codeï¼å¯¹äºaarèè¨è¯¥å¼æ æä¹ï¼ä¿æä¸åå³å¯
POM_VERSION_CODE_ENCRYPT_CHIPER=1


# åºåç§° å è§£å¯åºï¼æ°ï¼
POM_NAME_ENCRYPT_WRAPPER=encrypt-wrapper
# artifactId
POM_ARTIFACT_ID_ENCRYPT_WRAPPER=encrypt-wrapper
# åºçæåæ ¼å¼ä¸ºaar, å¸¸è§çè¿æjar
POM_PACKAGING_ENCRYPT_WRAPPER=aar
# åºçæè¿°,è¯´æä»æ¯å¹²å¥ç
POM_DESCRIPTION_ENCRYPT_WRAPPER=æ°å è§£å¯åº
# è¦åå¸ççæ¬å·ï¼snapshotsçæ¬ä¸ç¨æå¨æ¹ï¼æäº¤ä»£ç æ¶ä¼ç±èæ¬èªå¨å¤ç
POM_VERSION_NAME_ENCRYPT_WRAPPER=1.0.1
# åå¸çæ¬version codeï¼å¯¹äºaarèè¨è¯¥å¼æ æä¹ï¼ä¿æä¸åå³å¯
POM_VERSION_CODE_ENCRYPT_WRAPPER=1

# æ¬å°çæ¬
LOCAL_REV_BASE = *******
