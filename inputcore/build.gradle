apply plugin: 'com.android.library'
apply from: 'maven_publishing.gradle'

android {
    compileSdkVersion rootProject.ext.compileSdkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion

    defaultConfig {
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode rootProject.ext.versionCode
        versionName rootProject.ext.versionName

        testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"

    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        mock {
            debuggable true
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        debug {
            debuggable true
            renderscriptDebuggable = true
            jniDebuggable = true
        }
    }

    sourceSets {
        main {
            java.srcDirs = ['src/main/java']
        }

        release {
            root 'src/release'
            java.srcDirs = ['src/release/java']
            jniLibs.srcDir 'src/release/libs'
            assets.srcDirs = ['src/release/assets']
        }

        mock {
            root 'src/mock'
            java.srcDirs = ['src/mock/java']
            jniLibs.srcDir 'src/mock/libs'
            assets.srcDirs = ['src/mock/assets']
        }

        debug {
            root 'src/release'
            java.srcDirs = ['src/release/java']
            jniLibs.srcDir 'src/release/libs'
            assets.srcDirs = ['src/release/assets']
        }
    }

    defaultPublishConfig 'mock'

}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])

    // 基础工程
    implementation("com.baidu.input:imebase:${IME_BASE_VERSION}") {
        exclude group: 'com.baidu.galaxy'
    }

    implementation "com.android.support:appcompat-v7:${SUPPORT_V7_VERSION}"
    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'com.android.support.test:runner:1.0.2'
    androidTestImplementation 'com.android.support.test.espresso:espresso-core:3.0.2'
}
