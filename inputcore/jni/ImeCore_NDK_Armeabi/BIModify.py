# -*- encoding: utf-8 -*-
"""
This used to modify ndk file.

Authors: <AUTHORS>
Date:    2016/09/01 13:41:06
"""
import os
import sys
import subprocess
import re
import shutil

class BIModify(object):
    """ use for ndk file modify
    """
    def __init__(self, buildType):
        """ init
        """
        self.ifDebug = False
        self.buildType = buildType
        
    def initAll(self):
        """ init path and file
        """
        self.initPath()
        self.initFiles()
     
    def initPath(self):
        """init path
        """
        print('  --initPath')
        self.workspace = sys.path[0].replace('\\', '/') + '/'
        
        #内核编译需要的a文件有两个 dnnneon.a和libiptcore-new.a
        self.dnnneon_a_src = ""
        self.libipt_a_src = ""
        self.dnnneon_a_dst = ""
        self.libipt_a_dst =""
        
        #最终产物拷贝的路径
        self.outputPath = self.workspace + "output/"
        
        #需要拷贝到jni目录下的a文件存放位置，分成debug还有released
        self.corePath = self.workspace + "core/"
        self.coreReleasePath = self.corePath + "release/"
        self.coreDebugPath = self.corePath + "debug/"
        
        #最终产物so编译完成后的位置
        self.coreBuildPath = self.workspace + "libs/armeabi/"
        self.coreCopyPath = self.workspace + "jni/"
        self.build_final_so = self.coreBuildPath + "libinputcore-new-hw.so" 
        
        if self.buildType.lower() == "debug":
            self.dnnneon_a_src = self.coreDebugPath + 'dnnneon_debug.a'
            self.libipt_a_src = self.coreDebugPath + 'libiptcore_debug.a'

        elif self.buildType.lower() == "release":
            self.dnnneon_a_src = self.coreReleasePath + 'dnnneon.a'
            self.libipt_a_src = self.coreReleasePath + 'libiptcore.a'
        else:
            print("buildType wrong [%s]!!! [ only debug|release ]" % self.buildType)
            return
        
        self.dnnneon_a_dst = self.coreCopyPath + 'dnnneon.a'
        self.libipt_a_dst = self.coreCopyPath + 'libiptcore-new.a'
        
        #需要修改的pub函数
        self.modify_pub_file = self.workspace + "jni/platform/jni_pub_def.h"
        
    def biFolder(self, path):
        """set folder
        """
        if not os.path.exists(path):
            os.mkdir(path)
            print ("    --Make Dir : %s" % path)
            sys.stdout.flush()
        else:
            print ("    --make dir : %s" % path)
            
    def biFile(self, filepath):
        """set file
        """
        print('  --Check File:[%s]' % filepath)
        if  os.path.exists(filepath):
            try:
                os.remove(filepath)
                print('  --Remove File Sucessed!')
            except Exception as err:
                print('  --Remove File Failed!')
                print(err)
        else:
            print('  --File Not Exist No Need Del!')
         
    def initFiles(self):
        """init file
        """
        print('  --initFiles')
        #初始化产物路径
        self.biFolder(self.outputPath)
        
        #删除jnis遗留的a文件
        #dnneon文件无需删除
        #self.biFile(self.dnnneon_a_dst)
        self.biFile(self.libipt_a_dst)
        self.biFile(self.build_final_so)
    
    def printAll(self):
        """print param
        """
        print('\n  *******************All Param**********************')
        print('  --buildType:            [' + self.buildType + ']')
        print('  --workspace:            [' + self.workspace + ']')
        print('  --outputPath :          [' + str(self.outputPath) + ']')
        print('  --corePath:             [' + self.corePath + ']')
        print('  --coreReleasePath:      [' + self.coreReleasePath + ']')
        print('  --coreDebugPath:        [' + str(self.coreDebugPath) + ']')
        print('  --coreBuildPath:        [' + str(self.coreBuildPath) + ']')
        print('  --coreCopyPath:         [' + str(self.coreCopyPath) + ']')
        print('  --build_final_so:       [' + str(self.build_final_so) + ']')
        print('\n  --编译需要a文件路径，release和debug不同，请注意')
        #print('  --dnnneon_a_src:        [' + str(self.dnnneon_a_src) + ']')
        print('  --libipt_a_src:         [' + str(self.libipt_a_src) + ']')
        #print('  --dnnneon_a_dst:        [' + str(self.dnnneon_a_dst) + ']')
        print('  --libipt_a_dst:         [' + str(self.libipt_a_dst) + ']')
        print('\n  --需要修改的头文件')
        print('  --modify_pub_file:      [' + str(self.modify_pub_file) + ']')
        print('  ************************ End **********************\n')
        
    def copyA(self):
        """ copy a file
        """
        #dnnneon无正式测试区别暂时不用
        #print('  --copy dnnneon')
        #self.copyFile(self.dnnneon_a_src, self.dnnneon_a_dst)
        print('  --copy libiptcore')
        self.copyFile(self.libipt_a_src, self.libipt_a_dst)
    
    def copyFile(self, source_file, target_file):
        """copy file
        """
        if not os.path.exists(source_file):
            print("  --Src File Not Exist! [" + source_file + "]")
        else:
            shutil.copyfile(source_file, target_file)
            print("  --Copy [%s] To [%s] Successful" % (source_file, target_file))
    
    def modifyPub(self):
        """modify pub file
        """
        #modify_pub_file
        lines = [] 
        print('  --modifyPub:[%s]' % self.modify_pub_file)
        try:
            lines = open(self.modify_pub_file, 'r').readlines()
            flen = len(lines)-1
            for i in range(flen):
                if "CRASH_VERSION" in lines[i]:
                    if self.buildType == "debug": #测试需要注释CRASH_VERSION
                        m = re.search(r"^(#define\s+CRASH_VERSION.*)", lines[i])
                        if(m):
                            lines[i] = "//" + m.group(1) + '\n'
                            print("Debug Find Line[%s] After:[%s]" % (str(i), str(lines[i])))
                    elif self.buildType == "release": #正式需要打开CRASH_VERSION
                        m = re.search(r"^//(#define\s+CRASH_VERSION.*)", lines[i])
                        if(m):
                            lines[i] = m.group(1) + '\n'
                            print('  m:[%s]' % m.group(1))
                            print("Release Find Line[%s] After:[%s]" % (str(i), str(lines[i])))
        except Exception as err:
            print('  --open modify_pub_file fail')
            print(err)
        if lines:
            try:
                open(self.modify_pub_file, 'w').writelines(lines)
            except Exception as err:
                print('  --write modify_pub_file fail')
                print(err)
            
    def run(self):
        """main run
        """
        print('1. Init All')
        self.initAll()
        self.printAll()
        
        print('2. Copy A File')
        self.copyA()
            
        print('3. Modify Pub')
        self.modifyPub()

if __name__ == '__main__':
    #bIModify = BIModify('debug')
    bIModify = BIModify('release')
    bIModify.run()
    