# -*- encoding: utf-8 -*-
"""
This used to build ndk file.

Authors: <AUTHORS>
Date:    2016/09/01 13:41:06
"""
import os
import sys

import subprocess
import re
import shutil
from BIModify import BIModify

class BINdkBuild(object):
    """ndk build
    """
    def __init__(self):
        """init
        """
        self.ifDebug = False
        self.isv7a = False
        if not self.ifDebug:
            self.buildType = sys.argv[1]
            
        else:
            self.buildType = 'all'
        self.ndkPath = "/home/<USER>/ndkr10e/android-ndk-r10e/"

    def initAll(self):
        """init path and file
        """
        self.initPath()
        self.initFiles()
        self.printAll()

    def initPath(self):
        """init path
        """
        print('  --initPath')
        self.workspace = sys.path[0].replace('\\', '/') + '/'
        #最终产物拷贝的路径
        self.outputPath = self.workspace + "output/"
        self.coreBuildPath = self.workspace + "libs/armeabi/"
        self.build_final_so = self.coreBuildPath + "libinputcore-new-hw.so"
        
        self.coreBuildPathv7a = self.workspace + "libs/armeabi-v7a/"
        self.build_final_so_v7a = self.coreBuildPathv7a + "libinputcore-new-hw.so"
        
        self.build_release_so = self.outputPath + "libinputcore-new-hw.so"
        self.build_debug_so = self.outputPath + "libinputcore-new-hw-debug.so"

    def biFolder(self, path):
        """set folder
        """
        if not os.path.exists(path):
            os.mkdir(path)
            print ("    --Make Dir : %s" % path)
            sys.stdout.flush()
        else:
            print ("    --make dir : %s" % path)

    def biFile(self, filepath):
        """set file
        """
        print('  --Check File:[%s]' % filepath)
        if  os.path.exists(filepath):
            try:
                os.remove(filepath)
                print('  --Remove File Sucessed!')
            except Exception as err:
                print('  --Remove File Failed!')
                print(err)
        else:
            print('  --File Not Exist No Need Del!')

    def initFiles(self):
        """init files
        """
        print('  --initFiles')
        #初始化产物路径
        self.biFolder(self.outputPath)
        #删除遗留so文件
        self.biFile(self.build_release_so)
        self.biFile(self.build_debug_so)
        self.biFile(self.build_final_so)
        self.biFile(self.build_final_so_v7a)

    def printAll(self):
        """print param 
        """ 
        print('\n  *******************All Param**********************')
        print('  --buildType:             [' + str(self.buildType) + ']')
        print('  --workspace:             [' + self.workspace + ']')
        print('  --outputPath :           [' + str(self.outputPath) + ']')
        print('  --coreBuildPath:         [' + self.coreBuildPath + ']')
        print('  --build_final_so:        [' + self.build_final_so + ']')
        print('  --build_release_so:      [' + str(self.build_release_so) + ']')
        print('  --build_debug_so:        [' + str(self.build_debug_so) + ']') 
        print('  ************************ End **********************\n')

    def run(self):
        """main run
        """
        print('1. Init All')
        self.initAll()
        
        print('2. Build')
        self.build()

    def runBuildCmd(self):
        """run build cmd
        """
        cmd = self.ndkPath + 'ndk-build' + ' NDK_PROJECT_PATH=' + self.workspace
        print('  --cmd:[%s]' % cmd)
        try:
            lines = os.popen(cmd).readlines()
            for line in lines:
                if '[armeabi-v7a]' in line:
                    self.isv7a = True
                print('  --[%s]' % line)
        except Exception as err:
            print('  --write modify_pub_file fail')
            print(err)

    def copyOut(self, buildType):
        """copy build .so to output
        """
        if self.isv7a:
            self.coreBuildPath = self.workspace + "libs/armeabi-v7a/"
            self.build_final_so = self.coreBuildPath + "libinputcore-new-hw.so"
        if not os.path.exists(self.build_final_so):
            print('Build Failed!')
            return
        else:
            if buildType == 'release':
                self.copyFile(self.build_final_so, self.build_release_so)
            elif  buildType == 'debug':
                self.copyFile(self.build_final_so, self.build_debug_so)
            else:
                print('Error buildType[%s]' % str(buildType))

    def copyFile(self, source_file, target_file):
        """copy file
        """
        if not os.path.exists(source_file):
            print("  --Src File Not Exist! [" + source_file + "]")
        else:
            shutil.copyfile(source_file, target_file)
            print("  --Copy [%s] To [%s] Successful" % (source_file, target_file))

    def runBuild(self, type):
        """run build
        """
        biModify = BIModify(type)
        biModify.run()
        self.runBuildCmd()
        self.copyOut(type)

    def build(self):
        """build
        """
        if self.buildType.lower() == 'release' or self.buildType.lower() == 'debug':
            type = self.buildType.lower()
            self.runBuild(type)
            
        elif self.buildType.lower() == 'all':
            type = 'release'
            self.runBuild(type)
            type = 'debug'
            self.runBuild(type)
        else:
            print('wrong build type:[' + self.buildType + ']') 

if __name__ == '__main__':
    biNdkBuild = BINdkBuild()
    #bIBuild.initAll()
    biNdkBuild.run()
    