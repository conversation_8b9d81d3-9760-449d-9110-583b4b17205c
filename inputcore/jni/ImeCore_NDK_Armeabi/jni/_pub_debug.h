/*
 * _pub_debug.h
 *
 *  Created on: 2012-8-27
 *      Author: heliang
 *  调试模式(log)
 */

#ifndef PUB_DEBUG_H_
#define PUB_DEBUG_H_
#include "def_base.h"


#ifdef __cplusplus_no
extern "C"
{
#endif

#if (defined IPT_CONFIG_DEBUG_MODE) || (defined IPT_CONFIG_MEM_INFO) || defined(IPT_LOG36_ZID_TERMID)

IPT_IMPORT PLINT32 ipt_set_debug_log_path(const char* path,
        PLINT32 is_append); ////设置debug log 路径 0成功，-1失败
IPT_IMPORT PLINT32
ipt_close_debug_log(); /////关闭debug log 文件 0成功，-1失败，-100当前没有打开的debug log 文件
#endif
#ifdef IPT_CONFIG_MEM_INFO
#ifndef s_memory_cell_DEF_
#define s_memory_cell_DEF_
typedef struct s_memory_cell s_memory_cell;
#endif

#ifndef s_memory_info_DEF_
#define s_memory_info_DEF_
typedef struct s_memory_info s_memory_info;
#endif

struct s_memory_info
{
    PLUINT32 cell_cnt;//
    PLUINT32 cell_total_size;//记录用户空间
    PLUINT32 cell_max_size;//记录最大分配的内存
    PLUINT32 cell_alloc_cnt;//
    PLUINT32 cell_free_cnt;//
    PLUINT32 cell_realloc_cnt;//
    /////////////////////////////////////////
    s_memory_cell* cell_entry;//
};


struct s_memory_cell
{
    PLUINT32 cell_size;////记录用户空间
    PLUINT_32O64 cell_ptr;////记录校验地址.
    s_memory_cell* cell_prev;////
    s_memory_cell* cell_next;////
    ///////////////////////////////
    PLUINT32 buff[1];
};

IPT_IMPORT s_memory_info* ipt_memory_global_info();///内核分配内存总体概况
IPT_IMPORT void
ipt_memory_details_info();///内核分配内存详细信息
IPT_IMPORT void ipt_memory_graph_info();///内核分配内存图表
#endif
#ifdef IPT_CONFIG_MEM_INFO

#ifndef s_ipt_mem_info_DEF_
#define s_ipt_mem_info_DEF_
typedef struct s_ipt_mem_info s_ipt_mem_info;
#endif

struct s_ipt_mem_info
{
    PLUINT32 max_size;//内存峰值
    PLUINT32 cur_size;//当前内存值
    PLUINT32 cell_cnt;//当前未释放内存块数量

    PLUINT32 free_cnt;//释放内存块数量
    PLUINT32 alloc_cnt;//申请内存数量
    PLUINT32 realloc_cnt;//重新申请内存数量
};

IPT_IMPORT s_ipt_mem_info* ipt_get_mem_info();
IPT_IMPORT void ipt_reset_mem_max_size();

#endif
#ifdef __cplusplus_no
}
#endif



#endif /* PUB_DEBUG_H_ */
