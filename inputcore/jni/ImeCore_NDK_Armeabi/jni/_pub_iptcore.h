/*
 * pub_iptcore.h
 *
 *  Created on: 2011-11-9
 *      Author: <PERSON><PERSON><PERSON><PERSON>
 *      函数名编号:DA(01 - 99)
 */

#ifndef PUB_IPTCORE_H_
#define PUB_IPTCORE_H_
#include "_pub_version.h"
#if (defined IPT_PLATFORM_WIN32) || (defined IPT_PLATFORM_IPHONE) || (defined IPT_PLATFORM_LINUX)
#ifdef __cplusplus
extern "C"
{
#endif
#endif
#ifndef IPT_BASIC_TYPE_DEF
#define IPT_BASIC_TYPE_DEF
typedef unsigned int PLUINT32;
typedef unsigned short PLUINT16;
typedef unsigned char PLBYTE;
typedef unsigned char PLUINT8;
typedef signed int PLINT32;
typedef signed short PLINT16;
typedef signed char PLINT8;
typedef long long PLINT64;
typedef unsigned long long PLUINT64;
typedef const char PLFNAME;
typedef float PLREAL32;
typedef double PLREAL64;
#ifdef IPT_CONFIG_X64_CPU
typedef unsigned long long PLUINT_32O64;
typedef signed long long PLINT_32O64;
#else
typedef unsigned long int PLUINT_32O64;
typedef signed long int PLINT_32O64;
#endif
#define PLTRUE 1
#define PLFALSE 0
#define PLNULL 0
#endif

#ifndef IPT_IMPORT
#define IPT_IMPORT
#endif

#ifndef IPT_EXPORT
#define IPT_EXPORT
#endif

#ifndef IPT_APP_MAP
#define IPT_APP_MAP
#endif

////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////通用类型属性定义，使用与操作(&)做类型判断///////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////外部属性定义////////////////////////////////////////////////
////////////////////////////////////////////////通用类型属性定义//////////////////////////////////////////
#define EX_CAND_FLAG_CH                         0x1 ///<中文候选词属性定义
#define EX_CAND_FLAG_EN                         0x2 ///<英文候选词属性定义
#define EX_CAND_FLAG_CJ                         0x4 ///<长句候选项属性定义
#define EX_CAND_FLAG_AUTOWORD                   (0x8) /**<自动造词属性定义 */

#define EX_CAND_FLAG_US                         0x10 ///用户词属性定义（不包含细胞词, 即可以删除的词）
#define EX_CAND_FLAG_CELL                       0x20 ///细胞词属性定义
#define EX_CAND_FLAG_BW                         0x40 ///<中文笔画或五笔输入词属性定义
#define EX_CAND_FLAG_LIAN                       0x80 /**<中文联想词属性定义 */


#define EX_CAND_FLAG_EN_INPUT                   0x100 ///< 输入码候选词属性定义
#define EX_CAND_FLAG_CJ_CNEN                    0x200 ///<长句候选项属性定义(带有英文单词结果)
#define EX_CAND_FLAG_PRED                       0x400 ///< 逐词输入词属性定义
#define EX_CAND_FLAG_TOP_PRED                   0x800 /**<最高逐词输入词属性定义 */

#define EX_CAND_FLAG_GRAM                       0x1000 ///<三维词库词属性定义(执行联想查询时或者长句带三维词库结果)
#define EX_CAND_FLAG_IEC                        0x2000 ///<是否纠错候选标记
#define EX_CAND_FLAG_CONTACT                    0x4000 ///<联系人词
#define EX_CAND_FLAG_EN_PRECISE                 0x8000 /**<上划输入造的特殊词 */


#define EX_CAND_FLAG_PHRASE                     0x10000 ///<个性短语属性定义
#define EX_CAND_FLAG_CONTEXT                    0x20000 ///<场景化标记
#define EX_CAND_FLAG_OTHERWORD                  0x40000 ///<卖萌词定义
#define EX_CAND_FLAG_CHAIZI                     0x80000 /**<拆字组合属性定义  */

#define EX_CAND_FLAG_CORRECT                    (0x100000) ///<读音纠错属性定义
#define EX_CAND_FLAG_MEDIA                      0x200000 /**<多媒体词属性定义 -- 中文、英文、长句、短语类通用  */
#define EX_CAND_FLAG_HW                         0x400000 /**<手写词  */
#define EX_CAND_FLAG_TOP_IEC                    0x800000 /**<排在第一个的纠错词  */
/////////////////////////////////////特殊类型属性定义////////////////////////////////////////////////////////
////////为了保证以后可以扩展，低24位每一个bit都是独立的含义，可以直接与操作判断，高八位用于做附加的属性定义，用等号做判断在做高八位判断时，记得对低24位清零////////////////////
/////////////////////////////////////特殊类型属性定义，当低24位都是0的时候，使用等号做判断(==)/////////////////////////////////////////////////////

#define EX_CAND_FLAG_SYM                        (2<<24) ///<符号属性定义
#define EX_CAND_FLAG_OLDDEF                     (3<<24) ///<旧版自定义输入法属性定义
#define EX_CAND_FLAG_SYM_LIAN                   (4<<24) /**<符号联想属性定义  */

#define EX_CAND_FLAG_FORM                       (5<<24) ///< 网址和邮箱符号属性定义
#define EX_CAND_FLAG_FIRST                      (6<<24) /**< 固定首位词属性定义 */
#define EX_CAND_FLAG_EMOJI_LIAN                 (7<<24) /**<表情联想属性定义  */
#define EX_CAND_FLAG_EMOJI                      (8<<24) /**<表情属性定义  */

#define EX_CAND_FLAG_ZY                         (9<<24) /**<注音属性定义  */
#define EX_CAND_FLAG_CANGJIE                    (10<<24) /**<仓颉输入法属性定义  */
#define EX_CAND_FLAG_QUANTIFIER                 (11<<24) ///<数量词属性定义
#define EX_CAND_FLAG_DATE_WORD                  (12<<24) /**< 日期词属性定义 */

#define EX_CAND_FLAG_XIEHOUYU                   (13<<24) /**< 歇后语词属性定义 */
#define EX_CAND_FLAG_FAST_INPUT                 (14<<24) /**< 快速输入属性定义 */
#define EX_CAND_FLAG_ZHIDAHAO                   (15<<24) /**< 直达号属性定义 */
#define EX_CAND_FLAG_OP                         (16<<24) /**< 运营词属性定义 */

#define EX_CAND_FLAG_URL                        (17<<24) /**< url属性定义 */
#define EX_CAND_FLAG_PHONE_NUM                  (18<<24) /**< 电话号码属性定义 */
#define EX_CAND_FLAG_EN_LIAN                    (19<<24) /**< 英文联想属性定义  */
#define EX_CAND_FLAG_EMOTICON_LIAN              (20<<24) /**<颜文字联想属性定义  */

#define EX_CAND_FLAG_NIJIGEN                    (21<<24) /**<二次元属性定义  */
#define EX_CAND_FLAG_EMOTICON                   (22<<24) /**<颜文字属性定义  */
//#define EX_CAND_FLAG_MUSIC_LIAN                 (23<<24) /**<音乐彩蛋属性定义  */
//#define EX_CAND_FLAG_FALL_LIAN                  (24<<24) /**<掉落彩蛋属性定义  */

#define EX_CAND_FLAG_CLOUD_CACHE                (25<<24) /**< 云输入预测结果 */
#define EX_CAND_FLAG_VKWORD                     (26<<24) /**< V-K WORD结果 */
#define EX_CAND_FLAG_ZY_LIAN                    (27<<24) /**<注音联想属性定义 */
#define EX_CAND_FLAG_CONTACT_LIAN               (28<<24) /**< 联系人联想结果 */
#define EX_CAND_FLAG_DEFAULT_LIAN               (29<<24) /**< 默认联想词 */
#define EX_CAND_FLAG_CLOUD_LIAN                 (30<<24) /**< 云输入联想 */
#define EX_CAND_FLAG_CLOUD_FORCAST_LIAN         (31<<24)  /**< 云输入通过预测得到的联想 */
#define EX_CAND_FLAG_SPECIAL_LIAN         (32<<24)  /**< 云输入通过预测得到的联想 */
/////////////////////////////////////多媒体类型子属性定义，当该类型是EX_CAND_FLAG_MEDIA的时候，使用等号做判断(==)///////////////////////////////////
#define EX_CAND_FLAG_POS                        (2<<24) ///<定一下位吧
#define EX_CAND_FLAG_PIC                        (3<<24) ///<秀一张图片吧
#define EX_CAND_FLAG_SOUND                      (4<<24) ///<录一段声音吧
#define EX_CAND_FLAG_WRITE                      (5<<24) ///<用笔记书写心情吧
#define EX_CAND_FLAG_PEN                        (6<<24) /**<涂几笔吧 */
//例如，通过(type&EX_CAND_FLAG_MEDIA)结果为真确认是多媒体类型，再确认是否是EX_CAND_FLAG_POS类型时，应该使用((type&0xFF000000)==EX_CAND_FLAG_POS)做判断

///////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////候选词类型定义结束////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////扩展外部属性定义////////////////////////////////////////////////////
/////////////////////////////扩展通用类型属性定义////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////////////////////////////

#define EX_EX_CAND_FLAG_WHITE                   (0x1)   //云输入触发词
#define EX_EX_CAND_FLAG_WHITE_LIAN              (0x2)   //云输入触发词，只能够触发联想,没有该标记位代表有候选
#define EX_EX_CAND_FLAG_WHITE_TEXT              (0x4)   //云输入触发词，纯文本类型，没有该标记位代表非纯文本类型

///////////////////////////////////////////////////////////////////////////////////////////
#define EX_EX_CAND_FLAG_CLOUD_DUPLICATE         (0x1000)   //云缓存词，且与本地重复
///////////////////////////////////////////////////////////////////////////////////////////
#define EX_EX_CAND_FLAG_COMMIT_CAND                  (0x100000) //需要重新获取上屏词
////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////扩展候选词类型定义结束/////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////候选词埋点标记定义/////////////////////////////////////////
////////////////////////////低12位每一个bit都是独立的含义，高4位用于枚举定义///////////////////
#define BURY_POINT_CAND_FLAG_SYS            (0x1) //系统词
#define BURY_POINT_CAND_FLAG_US             (0x2) //用户词
#define BURY_POINT_CAND_FLAG_CELL           (0x4) //细胞词
#define BURY_POINT_CAND_FLAG_SEN            (0x8) //整句
#define BURY_POINT_CAND_FLAG_PRED           (0x10) //逐词
#define BURY_POINT_CAND_FLAG_IEC            (0x20) //纠错
#define BURY_POINT_CAND_FLAFG_CURSOR_LIAN   (0x40) //移动光标触发联想
/////////////////////////////////////////////////////////////////
#define BURY_POINT_CAND_FLAG_PHRASE         (1<<12) //个性短语
#define BURY_POINT_CAND_FLAG_CLOUD_CACHE    (2<<12) //云缓存
#define BURY_POINT_CAND_FLAG_CLOUD_FORCAST  (3<<12) //云预测
#define BURY_POINT_CAND_FLAG_VKWORD         (4<<12) //vkword
#define BURY_POINT_CAND_FLAG_NIJIGEN        (5<<12) //二次元
#define BURY_POINT_CAND_FLAG_EMOJI          (6<<12) //表情
#define BURY_POINT_CAND_FLAG_EMOTICON       (7<<12) //颜文字
#define BURY_POINT_CAND_FLAG_DEFAULT_LIAN   (8<<12) //默认联想词
#define BURY_POINT_CAND_FLAG_CLOUD          (14<<12) // 云输入词
#define BURY_POINT_CAND_FLAG_SUG            (15<<12) // sug词
////////////////////////////候选词埋点标记定义结束/////////////////////////////////////////
///////////////////////////////////输入码操作标记INPUT_FLAG////////////////////////////
////////////////////用于ipt_query_set_encase的参数和ipt_kp_append_point的input_case参数///////////
#define INPUT_FLAG_UPPER_CASE                   (0x1) ///<强制大写，或是shift键被按下
#define INPUT_FLAG_LOWER_CASE                   (0x2) ///<强制小写
#define INPUT_FLAG_PRECISE                      (0x4) ///<上划输入
#define INPUT_FLAG_NUM                          (0x8) /**<数字 内部使用标记，上层勿使用*/
#define INPUT_FLAG_INPUT_EDIT                   (0x10) /**<在input编辑条上的输入 */
///////////////////////////////////输入码操作标记INPUT_FLAG结束////////////////////////////

///////////////////////////////////输入码错误标记////////////////////////////
////////////////////////////////用于ipt_query_get_iec_tip的参数iec_info////////////////////////////
#define PY_IEC_FLAG_TE                  (0x1) ///<输错了
#define PY_IEC_FLAG_LE                  (0x2) ///<少输了，当前位置之前少输了一个字符
#define PY_IEC_FLAG_ME                  (0x4) ///<多输了
#define PY_IEC_FLAG_SE                  (0x8) /**<交换输入 */
#define PY_IEC_FLAG_POS                 (0x10) /**<少输了，当前位置之后少输了一个字符 */
///////////////////////////////////输入码操作标记INPUT_FLAG结束////////////////////////////


//////////////////////////////////手写查询范围HW_FIND_RANGE/////////////////////////
//////////////////////////////////用于手写查询范围设置
#define HW_FIND_RANGE_CH_COMMON                 (0x01) ///<常用字
#define HW_FIND_RANGE_CH_RARE                   (0x02) /**<生僻字  */
#define HW_FIND_RANGE_CH_RADICAL                (0x04) /**<偏旁部首  */

#define HW_FIND_RANGE_NUM                       (0x08) ///<数字
#define HW_FIND_RANGE_EN_LOWER                  (0x10) /**<英文小写  */
#define HW_FIND_RANGE_EN_UPPER                  (0x20) /**<英文大写  */

#define HW_FIND_RANGE_PUN_COMMON                (0x40) ///<常用标点
#define HW_FIND_RANGE_PUN_EXT                   (0x80) /**<扩展标点  */

#define HW_FIND_RANGE_SYM_COMMON                (0x0100) ///<常用符号
#define HW_FIND_RANGE_SYM_EXT                   (0x0200) /**<扩展符号  */
#define HW_FIND_RANGE_SYM_RARE_G1               (0x0400) /**<特殊符号1  */
#define HW_FIND_RANGE_SYM_RARE_G2               (0x0800) /**<特殊符号2  */
#define HW_FIND_RANGE_SYM_RARE_G3               (0x1000) /**<特殊符号3  */

#define HW_FIND_RANGE_CH_NM                     (HW_FIND_RANGE_CH_COMMON | HW_FIND_RANGE_CH_RARE) ///<不包括偏旁部首的汉字
#define HW_FIND_RANGE_EN                        (HW_FIND_RANGE_EN_LOWER | HW_FIND_RANGE_EN_UPPER) /**<英文  */
#define HW_FIND_RANGE_PUN                       (HW_FIND_RANGE_PUN_COMMON | HW_FIND_RANGE_PUN_EXT) /**<标点  */
#define HW_FIND_RANGE_SYM                       (HW_FIND_RANGE_SYM_COMMON \
                                                  | HW_FIND_RANGE_SYM_EXT \
                                                  | HW_FIND_RANGE_SYM_RARE_G1 \
                                                  | HW_FIND_RANGE_SYM_RARE_G2 \
                                                  | HW_FIND_RANGE_SYM_RARE_G3) /**<符号  */

#define HW_FIND_RANGE_CH_ALL                    (HW_FIND_RANGE_CH_NM | HW_FIND_RANGE_CH_RADICAL) /**<所有汉字  */
#define HW_FIND_RANGE_CH_RARE_RADICAL           (HW_FIND_RANGE_CH_RARE | HW_FIND_RANGE_CH_RADICAL) /**<生僻字+偏旁部首  */
#define HW_FIND_RANGE_EN_NUM                    (HW_FIND_RANGE_EN | HW_FIND_RANGE_NUM) /**<英文+数字  */
#define HW_FIND_RANGE_PUN_SYM                   (HW_FIND_RANGE_PUN | HW_FIND_RANGE_SYM) /**<标点+符号  */

#define HW_FIND_RANGE_ALL                       (HW_FIND_RANGE_CH_ALL | HW_FIND_RANGE_EN_NUM | HW_FIND_RANGE_PUN_SYM) /**<所有范围  */

//////////////////////////////////手写查询范围HW_FIND_RANGE结束/////////////////////////

/////////////////框属性////////////////
#define ATTRIBUTE_OTHER     (0x1) //未知属性//
#define ATTRIBUTE_SEARCH    (0x2) //搜索框属性//
#define ATTRIBUTE_WEB       (0x4) //网址框属性//

#ifndef s_kp_Layout_DEF_
#define s_kp_Layout_DEF_
typedef struct s_kp_Layout s_kp_Layout;
#endif

enum ot_idp_type
{
    OT_IDP_TYPE_EMOTICON = 0x1000000, //id映射中的颜文字类型(unicode字符)
    OT_IDP_TYPE_EMOJI = 0x2000000 //id映射中的颜文字类型(二进制流)
};

///英文查询结果的排序方式, 用于s_ipt_config结构体中的en_ensort变量
enum EN_SORT
{
    ENSORT_BYFREQ = 0, ///<按英文词的频率排序
    ENSORT_BYLEN, ///<按英文词的长度排序
    ENSORT_BYABC, ///<按英文词的字母顺序排序
};

///中英混排方式, 用于s_ipt_config结构体中的ch_cnen变量
enum CNEN_TOGGLE
{
    CNEN_OFF = 0, ///<关闭中英混排
    CNEN_ON = 1, ///<开启中英混排
    CNEN_ONLY_MUTI_CHAR = 2, ///<开启中英混排, 但屏蔽单个英文字母
};

///英文查询结果中大小写状态, 用于s_ipt_config结构体中的en_encase变量
enum EN_CASE
{
    ENCASE_NORMAL = 0, ///<普通状态
    ENCASE_FIRST, ///<首字母大写状态
    ENCASE_ALL ///<全部字母大写状态
};

///输入法查询选项, 用于ipt_query_find等函数
enum FIND_TYPE
{
    FINDTYPE_PY = 1, ///<中文查询
    FINDTYPE_EN, ///<英文查询
    FINDTYPE_BH, ///<笔画查询
    FINDTYPE_WB, ///<五笔查询
    FINDTYPE_DEF, ///<自定义查询
    FINDTYPE_SYM, ///<符号查询
    FINDTYPE_LIAN, ///<联想查询
    FINDTYPE_FORM, ///<邮箱网址查询
    FINDTYPE_HW,  ///<手写查询
    FINDTYPE_ZY, ///<注音输入法查询
    FINDTYPE_CANGJIE, ///<仓颉输入法查询
    FINDTYPE_PY_EDIT, ///<在编辑区上进行的中文查询
    FINDTYPE_Count ///<查询类型枚举值个数
};

//  FINDTYPE_PHRASE,//个性短语查询

///自造词查询选项, 用于ipt_usword_count等函数
enum USERWORD_TYPE
{
    USERWORD_CH1 = 1, ///<汉字
    USERWORD_CH2 = 2, ///<二字词
    USERWORD_CH3 = 3, ///<三字词
    USERWORD_CH4 = 4, ///<四字词
    USERWORD_CH5 = 5, ///<五字词
    USERWORD_CH6 = 6, ///<六字词
    USERWORD_CH7 = 7, ///<七字词
    USERWORD_CH8 = 8, /**< 八字词 */
    USERWORD_MORE = 9, /**< 八字以上词 */
    ///////////////////////////
    USERWORD_EN = 20, ///<英文词
    USERWORD_ALL = 30, /**< 所有中文词 */
    ///////////////////////////
    USERWORD_LAST_SIGNIGIE ///<自造词查询选项枚举值个数
};

///查询候选项目的类型, 用于ipt_query_get_count, ipt_query_get_str函数
enum GET_TYPE
{
    /////////////////////////////////////////////////////////////
    GETTYPE_CAND = 0, ///<普通候选词
    GETTYPE_LIST = 1, ///<拼音列表(或笔画筛选等)
    GETTYPE_HOTSYM = 2, ///<常用符号(目前不支持)
    GETTYPE_CAND_ORG = 3, ///<普通候选词原始形态(无繁简转换或英文大小写转换)
    GETTYPE_CAND_WB_TIP = 4, /**<中文候选词(带五笔编码提示) */
    GETTYPE_PY_LIST = 5, ///<拼音列表
    GETTYPE_BH_LIST = 6, /**笔画筛选列表 */
    GETTYPE_COMMIT_CAND = 7, ///<上屏候选词
    ///////////////////////////////////////////////////////////
    GETTYPE_enum_count, ///<查询结果类型枚举值个数
};

///网址邮箱符号编辑选项
enum FORM_EDIT_OPTION
{
    /////////////////////////////////////////////////////////////
    FORM_EDIT = 1, ///<编辑
    FORM_DEL = 2, ///<删除
    FORM_ADD = 3, /**< 添加 */
    ///////////////////////////////////////////////////////////
    FORM_EDIT_cnt, /**< 网址邮箱符号编辑选项枚举值个数 */
/////////////////////////////
};///////////////////////////////////////////////////////////


///个性短语编辑选项, 用于ipt_phrase_group_edit, ipt_phrase_item_edit等函数
enum PHRASE_EDIT_OPTION
{
    /////////////////////////////////////////////////////////////
    PHRASE_EDIT = 1,///<编辑
    PHRASE_DEL = 2,///<删除
    PHRASE_ADD = 3, ///<添加
    PHRASE_REMOVE = 4, ///<移除(批量删除)
    ///////////////////////////////////////////////////////////
    PHRASE_EDIT_cnt, /**< 个性短语编辑选项个数 */
/////////////////////////////
};///////////////////////////////////////////////////////////

///查询命令类型, 用于ipt_query_cmd函数, 下文所指的idx参数和返回值均指ipt_query_cmd函数的参数和返回值
enum QUERY_CMD_TYPE
{
    ////////////////////////////////////////////////////
    CMD_ADJUST_PUSH = 1, ///<将需要操作(调频、联想、逐词输入优化)的词压栈, idx参数表示要进行压栈候选词的索引, 返回值为0表示压栈成功, 返回值<0表示压栈失败
    CMD_ADJUST_POP = 2, ///<将已经压入栈的词以词为单位弹出(一般在退格的时候使用), idx参数表示弹出的词的个数, idx=0表示弹出所有词(即清空栈), 返回值为0表示弹出成功, 返回值<0表示弹出失败
    CMD_ADJUST_COMMIT = 3, /**<对栈内元素执行调频操作(操作完成后,自动清除栈), idx应设为0, 返回值>=0表示调频成功, 返回值<0表示调频失败*/
    ////////////////////////////////////////////////////////////////////////////////////////
    CMD_ADJUST_DELETE = 4, /**<删除候选词, idx表示要进行删除的候选词索引, 返回值为0表示删除成功, 返回值<0表示删除失败*/
    ////////////////////////////////////////////////////////////////////////////////////////
    CMD_ADJUST_BACKSPACE = 5, ///<将已经压入栈的词以字为单位弹出（一般用于候选词上屏后, 对已经压栈但还没有commit的词执行退格操作）, idx表示退格的字的个数, idx=0表示弹出所有词(即清空栈), 返回值为0表示删除成功, 返回值<0表示删除失败
    CMD_PREDICT_CLEAN = 6, ///<清除逐词输入优化状态(一般用于面板弹起, 输入标点等句子输入中断行为), idx参数在此命令中不起作用, 返回值为0表示清除成功, 返回值<0表示清除失败
    CMD_PREDICT_PREWORD_LEN = 7, /**<查询当前逐词输入前一个词的长度, idx参数在此命令中不起作用, 返回值为逐词输入前一个词的长度, 返回值为0时表示逐词输入功能未起作用(如逐词输入功能未打开或前一个词无法用来做逐词输入等情况)*/
    ////////////////////////////////////////////////////////////////////////////////////////
    CMD_STACK_ITEM_CNT = 8, ///<查询当前调频栈的PUSH次数, idx参数在此命令中不起作用, 返回值为调频栈的PUSH次数
    CMD_STACK_ITEM_LEN = 9, /**<查询当前栈里面某个元素的长度, idx表示待查询元素的下标, 返回值为该元素的长度, 如果没有这个元素则返回0 */
    ////////////////////////////////////////////////////////////////////////////////////////
    CMD_INPUT_CLEAN_UP = 10, /**<一次完整的查询结束，输入码已经被无法复原的清除掉的时候，使用该命令做一次清理,无法复原的意思是，候选词已经上屏可以调用联想查询的时候，或者左划清除所有输入 */
    ////////////////////////////////////////////////////////////////////////////////////////
    CMD_IS_DELABLE = 20, /**<判断某个词是否可以删除, idx参数表示该词的索引, 返回值>0时, 表示可以删除, 其他返回值则表示不能删除*/
    CMD_IS_SYSWORD = 21, /**<判断某个词是否在系统词库或已经打开的细胞词库中， idx参数表示该词的索引, 返回值>0时,才表示该词在系统词库中, 返回值=0表示该词不在系统词库中, 返回值<0表示出现错误 */
    ////////////////////////////////////////////////////////////////////////////////////////
    CMD_LIST_CLEAN = 30, ///<清除list的选中状态//请勿使用该接口，应当使用CMD_INPUT_CLEAN_UP
    CMD_PY_LIST_PUSH = 31, ///<选中当前拼音list中的第idx个拼音压栈,当选中的是“英文”时，返回1，其他返回0；
    CMD_PY_LIST_POP = 32, ///<将当前栈顶的拼音弹出
    CMD_BH_LIST_PUSH = 33, ///<选中当前笔画list中的第idx个笔画压栈
    CMD_BH_LIST_POP = 34,  ///<将当前栈顶的笔画弹出
    CMD_PY_LIST_CACHE_LEN = 35,  ///<返回当前拼音list缓存长度
    CMD_LIST_FIND_MODE = 36, ///<选择当前的list查询模式，中文或者英文，idx = 1时代表英文，idx = 0时代表中文(废弃)
    ////////////////////////////////////////////////////////////////////////////////////////
    CMD_CONTACT_CNT = 40, ///<获取和某个候选词相应联系人的属性个数, idx参数表示该词的索引, 返回值表示与该词对应的联系人的属性个数
    CMD_FIND_LIAN = 41, ///<执行联想查询, idx参数在此命令中不起作用(仅根据栈顶的词以及栈里已有的所有词分别进行联想), 返回值为0表示执行成功, 返回值<0代表执行失败

    CMD_FIND_REFRESH = 42, /**< 重新查询最后一次的查询结果, idx参数在此命令中不起作用, 返回值为0表示执行成功, 返回值<0代表执行失败*/
    ////////////////////////////////////////////////////////////////////////////////////////
    CMD_HW_CLEAN = 43,     /**< 清空手写状态 */
    CMD_CONTACT_DEL = 44, ///<仅删除某个词对应联系人的信息, idx参数表示词的索引, 返回值0表示操作成功，返回-1表示删除失败，返回-2表示未找到要删的词
    CMD_CONTACT_RESTORE_FREQ = 45, ///<删除某个词对应联系人的信息并恢复默认词频, idx参数表示词的索引, 返回值0表示操作成功，返回-1表示删除失败，返回-2表示未找到要删的词
    CMD_CONTACT_DEL_ALL = 46, ///<删除某个词对应联系人以及对应的自造词, idx参数表示词的索引, 返回值0表示操作成功，返回-1表示删除失败，返回-2表示未找到要删的词
    CMD_HW_BS_FILTER_CLEAN = 47, ///<清空部首过滤
    ////////////////////////////////////////////////////////////////////////////////////////
    CMD_SENTENCE_LIAN_CLEAN = 50, // /**< 清除整句出表情的状态*/
    CMD_SENTENCE_XIEHOUYU_CLEAN = 52, // /**< 清除整句歇后语状态*/
    ///////////////////////////////////////////////////////////////////////////////////////
    //CMD_EN_LIST_CLEAN = 60,    ///<该接口根本没实现~囧注释掉~如有需要使用CMD_INPUT_CLEAN_UP
    CMD_EN_LIST_PUSH = 61,  // 选中当前英文list中的第idx个英文压栈,当选中的是“数字”时，返回1，其他返回0；
    CMD_EN_LIST_POP = 62,  /// 将当前栈顶的英文弹出
    CMD_EN_LIST_CACHE_LEN = 63,  /**< 返回当前英文list缓存长度*/
    //////////////////////////////////////////////////////////////////////////////////////
    CMD_HW_BACKSPACE = 64, ///<手写后退
    //////////////////////////////////////////////////////////////////////////////////////
    CMD_ZY_LIST_PUSH = 71,  // 选中当前注音list中的第idx个注音压栈
    CMD_ZY_LIST_POP = 72,  /// 将当前栈顶的注音弹出
    CMD_ZY_LIST_CACHE_LEN = 73,  /**< 返回当前注音list缓存长度*/
    CMD_END
////////////////////////////////////////////////////////////////////////////////////////
};////////////////////////////////////////////////////


////CMD_PUSH////CMD_POP////CMD_COMMIT////CMD_DELETE////CMD_IS_DELABLE

///内核参数配置类型, 用于ipt_core_config函数中
enum CONFIG_TYPE
{
    CONFIGTYPE_SET_CONFIG = 1, ///<配置内核功能选项, 具体内容请参照s_ipt_config结构体的说明
    CONFIGTYPE_GET_CONFIG, ///<读取内核功能选项, 具体内容请参照s_ipt_config结构体的说明
    CONFIGTYPE_SET_MOHU, ///<配置模糊音, 具体内容请参照s_ipt_mohu结构体的说明
    CONFIGTYPE_GET_MOHU, ///<读取模糊音配置, 具体内容请参照s_ipt_mohu结构体的说明
    CONFIGTYPE_SET_SHUANGPIN, ///<配置双拼方案, 具体内容请参照s_ipt_shuangpin结构体的说明
    CONFIGTYPE_GET_SHUANGPIN, /**< 读取双拼方案, 具体内容请参照s_ipt_shuangpin结构体的说明 */
    CONFIGTYPE_SET_HW,  ///<配置手写, 具体内容请参照s_ipt_hw结构体的说明
    CONFIGTYPE_GET_HW,  ///<读取手写, 具体内容请参照s_ipt_hw结构体的说明
    ///////////////////////////////////////////////////////////
    CONFIGTYPE_Count ///<内核参数配置枚举类型的个数
};

///同步类型(词库同步操作时使用)
enum SYNC_FLAG
{
    SYNC_FLAG_CH = 1, ///<中文词
    SYNC_FLAG_EN = 2, ///<英文词
    SYNC_FLAG_UN = 3  ///<其他符号
};

///同步选项(词库同步操作时使用)
enum SYNC_OPTION
{
    SYNC_OPTION_PULL = 1, ///<增加词频
    SYNC_OPTION_DEL = 2,  ///<删除词
    SYNC_OPTION_REPULL = 3  ///<删除词后重新添加
};

///手写输入选项(手写输入时使用)
enum HW_INPUT_TYPE
{
    HW_INPUT_TYPE_HZ = 0x1, //<单字输入
    HW_INPUT_TYPE_REDUP = 0x2, //<叠写输入
    HW_INPUT_TYPE_NM = 0x4, //<连写输入
    HW_INPUT_TYPE_EN_NUM = 0x8  //<英文数字输入
};

///场景化列表
enum CONTEXT_ID
{
    NO_CONTEXT_ID = 0,  ///没有场景化
    CONTEXT_ID_MERCH = 0x1, ///电商场景
    CONTEXT_ID_APP = 0x2, ///应用市场场景
    CONTEXT_ID_LBS = 0x4,  ///地理位置场景
    CONTEXT_ID_MEDIA = 0x8, ///多媒体场景，视频
    CONTEXT_ID_GROUPON = 0x10, ///点评团购场景
    CONTEXT_ID_BROWSER = 0x20, ///浏览器场景
    CONTEXT_ID_SNS = 0x40, ///聊天软件场景
    CONTEXT_ID_MUSIC = 0x80, ///音乐类(QQ音乐、酷狗等)
    CONTEXT_ID_BILI = 0x100, ///B站类（bilibili）
    CONTEXT_ID_TIEBA = 0x200, ///贴吧论坛类(百度贴吧)
    CONTEXT_ID_SOCIAL = 0x400, ///交友类(陌陌)
    CONTEXT_ID_WZRY = 0x800, ///王者荣耀//
    CONTEXT_ID_JDQS = 0x1000, ///绝地求生//
    CONTEXT_ID_QQFC = 0x2000, ///qq飞车//
    CONTEXT_ID_MNSJ = 0x4000, ///迷你世界//
    CONTEXT_ID_LIVE = 0x8000, ///直播//
    CONTEXT_ID_XXQG = 0x10000, ///学习强国//
    CONTEXT_ID_XS = 0x20000, ///小说//
    CONTEXT_ID_YL = 0x40000, ///医疗//
    CONTEXT_ID_WB = 0x80000, ///微博//
    CONTEXT_ID_ES = 0x100000, ///二手//
};

enum AUTOREPLY_INTENT
{
    AUTOREPLY_INTENT_NAME = 1,  ///名字识别
    AUTOREPLY_INTENT_MOBILE = 2, ///手机号
    AUTOREPLY_INTENT_ADDRESS = 3, ///地址
    AUTOREPLY_INTENT_QQ = 4,  ///QQ
    AUTOREPLY_INTENT_WEIXIN = 5, ///微信
    AUTOREPLY_INTENT_BIRTH = 6, ///生日
    AUTOREPLY_INTENT_IDNUMBER = 7, ///身份证
    AUTOREPLY_INTENT_EMAIL = 8, ///电子邮箱
    AUTOREPLY_INTENT_TAX_ID = 9, ///税号
    AUTOREPLY_INTENT_STRICT_ADDRESS = 10, ///严格的地址
};

enum KEY_RECT_CN26_NAME
{
    KEY_RECT_A = 1,
    KEY_RECT_B = 2,
    KEY_RECT_C = 3,
    KEY_RECT_D = 4,
    KEY_RECT_E = 5,
    KEY_RECT_F = 6,
    KEY_RECT_G = 7,
    KEY_RECT_H = 8,
    KEY_RECT_I = 9,
    KEY_RECT_J = 10,
    KEY_RECT_K = 11,
    KEY_RECT_L = 12,
    KEY_RECT_M = 13,
    KEY_RECT_N = 14,
    KEY_RECT_O = 15,
    KEY_RECT_P = 16,
    KEY_RECT_Q = 17,
    KEY_RECT_R = 18,
    KEY_RECT_S = 19,
    KEY_RECT_T = 20,
    KEY_RECT_U = 21,
    KEY_RECT_V = 22,
    KEY_RECT_W = 23,
    KEY_RECT_X = 24,
    KEY_RECT_Y = 25,
    KEY_RECT_Z = 26,
    KEY_RECT_SHIFT = 27, //shift键
    KEY_RECT_DEL = 28, ///delete键
    KEY_RECT_NUM = 29, ///数字面板切换键
    KEY_RECT_CHEN = 30, ///中英文切换键
    KEY_RECT_COMMA = 31, //逗号
    KEY_RECT_SPACE = 32, //空格
    KEY_RECT_STOP = 33, ///句号
    KEY_RECT_SYM = 34, ///符号面板切换键
    KEY_RECT_ENTER = 35 ///回车
};

enum PHONE_STATE_LIST
{
    PHONE_STATE_PORTRAIT = 0, //竖屏
    PHONE_STATE_LANDSCAPE = 1 //横屏
};

enum SYM_SORT_ADJUST
{
    SYM_SORT_ADJ_NORMAL = 0,   //无调整符号排序
    SYM_SORT_ADJ_26KEYS_CN = 1, //从符号中文26键切入到更多符号面板
    SYM_SORT_ADJ_26KEYS_EN = 2, //从符号英文26键切入到更多符号面板
};

enum PHONE_TYPE
{
    ALP_MATE10 = 1, //Mate10
    HMA_MATE20 = 2, //Mate20
    ELE_P30 = 3, //P30
};
///////////////////////////////////////////////////////////////////////////////////////////////////
#ifndef s_Point_v2_DEF_
#define s_Point_v2_DEF_
typedef struct s_Point_v2 s_Point_v2;

#pragma pack (4)
struct s_Point_v2
{
    ////笔迹点,结构体(16位)
    PLUINT16 x;
    PLUINT16 y;
};
#pragma pack ()
#endif

#ifndef s_Rect_v2_DEF_
#define s_Rect_v2_DEF_
typedef struct s_Rect_v2 s_Rect_v2;
#endif

#pragma pack (4)
struct s_Rect_v2
{
    ////笔迹点,结构体(8位)
    s_Point_v2 TL;  //矩形左上角坐标
    s_Point_v2 BM;  //矩形右下角坐标
};
#pragma pack ()

#ifndef s_Point_v1_DEF_
#define s_Point_v1_DEF_
typedef struct s_Point_v1 s_Point_v1;
#endif

#pragma pack (2)
struct s_Point_v1
{
    ////笔迹点,结构体(8位)
    PLBYTE x;
    PLBYTE y;
};
#pragma pack ()

#ifndef s_Rect_v1_DEF_
#define s_Rect_v1_DEF_
typedef struct s_Rect_v1 s_Rect_v1;
#endif

#pragma pack (4)
struct s_Rect_v1
{
    ////笔迹点,结构体(8位)
    s_Point_v1 TL;
    s_Point_v1 BM;
};
#pragma pack ()
///////////////////////////////////////////////////////////////////////////////////////////////////
#pragma pack (4)

#ifndef s_ipt_libfile_DEF_
#define s_ipt_libfile_DEF_
typedef struct s_ipt_libfile s_ipt_libfile;
#endif
///内核数据文件对象，在ipt_core_load，ipt_core_create_emptylib等函数中使用（除了gram_file 和cz_file ,其他文件没什么必要尽量不要传文件内容指针）
#define IPT_LIBFILE_NUM (60)//内核数据文件个数，增加文件注意修改这个!!!

struct s_ipt_libfile
{
    const char* ch_hz2_file; ///<中文汉字数据文件
    const char* ch_usr3user_file; ///usr3用户词库///
    const char* ch_uz2_file; ///<中文用户词数据文件
    const char* ch_cz3_file; ///<中文词组数据文件(cz3.bin)
    //////////////////////////////////////////4///
    const char* ch_ft2_file;///<中文繁体数据文件
    const char* ch_bh4_file;///<中文笔画数据文
    const char* ch_wb2_file;///<中文五笔数据文件
    const char* ch_usr3cell_file;///usr3细胞词库///
    //////////////////////////////////////////8///
    const char* en_en2_file; ///<英文系统词数据文件
    const char* en_ue2_file;///<英文用户词数据文件
    const char* ot_sym2_file; ///<符号数据文件
    const char* ot_phrase_file; ///**< 个性短语数据文件 */
    //////////////////////////////////////////12///
    const char* ot_contact_file; ///<联系人数据文件
    const char* ot_def_file; ///<旧版自定义输入(五笔, 自然码, 郑码等）文件
    const char* ot_sylian_file; ///<符号联想数据文件
    const char* ch_cz3down_file; ///<下载词组数据文件
    //////////////////////////////////////////16///
    const char* ot_filter_file; ///<生僻字过滤文件(防止显示小方块)
    const char* ot_sync_file; ///<自造词同步文件，功能未上线
    const char* ot_form_file; ///<网址邮箱符号文件，mac版本专用
    const char* ot_first_file; ///**< 固定首位数据文件，mac版本专用*/
    //////////////////////////////////////////20///
    const char* ch_uzw_file;///<中文手写联想用户词数据文件
    const char* ot_keyword_file; ///关键词文件
    const char* ot_mixword_file; ///个性化输入文件（已废弃）
    const char* ot_huoxingwen_file;  ///火星文文件，暂未使用
    //////////////////////////////////////////24///
    const char* ot_otherword_file;///个性化输入文件(卖萌, 拆字等预装文件)
    const char* ot_cangjie_file; ///仓颉词库文件
    const char* ch_zy_hz_file; /**< 注音汉字文件 */
    const char* ch_zy_cz_file; /**< 注音词组文件 */
    //////////////////////////////////////////////28///
    const char* ch_pro_cor2_file; ///**<拼音纠正文件 */
    const char* kp_file; ///**<盲打拼音模板，已废弃 */
    const char* wt_hz_file;///手写汉字模板文件
    const char* wt_phrase_file; ///**<手写个性短语模板文件，已废弃  */
    //////////////////////////////////////////////32///
    const char* ot_xiehouyu_file; ///歇后语联想文件*/
    const char* wt_bs_file;       //部首过滤文件，已废弃
    const char* hz_label_file;    ///汉字标签文件，cz3下已废弃 ///
    const char* ch_trigram_file;    //三元库，暂未使用
    /////////////////////////////////////////////36///
    const char* ot_enlian_file; //英文联想文件，已废弃 */
    const char* ot_idmap_file; //id映射文件
    const char* ch_fj_file;  ///繁简转换文件
    const char* cloud_keyword_file;    //云输入关键字文件
    /////////////////////////////////////////////40////
    const char* ot_cand_con_file; //盲人辅助输入文件 */
    const char* ot_vkword_file; //v-k文件
    const char* ot_search_file;  //ASC搜索词文件
    const char* hz_tone_file;    //汉字音调文件
    /////////////////////////////////////////////44///
    const char* auto_reply_file;    //智能回复文件
    const char* prov_city_file;     //省市文件
    const char* ch_zy_usr_file; //注音自造词文件*/
    const char* config_file;    //存储客户端的设置项
    //////////////////////////////////////////////48///
    const char* us_bak_file;    //所有用户数据备份
    const char* ot_list_file;    //预制的左侧list，已废弃
    const char* ltp_dict_file;    //动态热区条件概率文件
    const char* usr_touch_file;   //用户个性化数据误触文件
    //////////////////////////////////////////////52///
    const char* app_map_file;      //app名字和框属性对应场景ID和词库文件
    const char* nnlm_file;//nnlm模型文件
    const char* thp_file;//thp模型文件
    const char* usr_voice_correct_file; //用户的语音纠错文件
    ///////////////////////////////////////////////56///
    const char* bayesian_sylian_file;//贝叶斯符号联想文件
    const char* ot_special_file; //运营联想词文件//
    const char* track_nn_file; //滑行输入模型文件//
    const char* reserved1; //下个新增文件就改这个名字//
    ///////////////////////////////////////////////60///
    PLUINT32 len_cz;///<中文词组镜像长度(此参数为0, 表示ch_cz_file参数传的是文件路径; 此参数为正数时, 表示ch_cz_file参数传的是文件内容指针)
    PLUINT32 len_gram;///<中文二元镜像长度(此参数为0, 表示ch_gram_file参数传的是文件路径; 此参数为正数时, 表示ch_gram_file参数传的是文件内容指针)
    PLUINT32 len_wb;///<中文五笔镜像长度(此参数为0, 表示ch_wb_file参数传的是文件路径; 此参数为正数时, 表示ch_wb_file参数传的是文件内容指针)
    PLUINT32 len_en2;/**< 英文系统词镜像长度(此参数为0, 表示en_en2_file参数传的是文件路径; 此参数为正数时, 表示en_en2_file参数传的是文件内容指针) */
    //////////////////////////////////////////
    PLUINT32 len_def;///<旧版自定义(五笔, 自然码, 郑码等）镜像长度(此参数为0, 表示ot_def_file参数传的是文件路径; 此参数为正数时, 表示ot_def_file参数传的是文件内容指针)
    //////////////////////////////////////////
    PLUINT32 len_cz3;///<中文词组镜像长度(此参数为0, 表示ch_cz3_file参数传的是文件路径; 此参数为正数时, 表示ch_cz3_file参数传的是文件内容指针)
    PLUINT32 len_cz3down;///<中文二元镜像长度(此参数为0, 表示ch_cz3down_file参数传的是文件路径; 此参数为正数时, 表示ch_cz3down_file参数传的是文件内容指针)
    /////////////////////////////////////////
    PLUINT32 len_resever0;///<镜像长度保留字段
    PLUINT32 len_resever1;///<镜像长度保留字段
    PLUINT32 len_resever2;/**< 镜像长度保留字段 */
    //////////////////////////////////////////
#ifdef IPT_MMAP
    PLUINT64 mmap_cz3_start;
    PLUINT64 mmap_cz3_len;
    PLUINT64 mmap_cz3_fd;
#endif
#ifdef CRASH_VERSION
    void* void_env;
    void* void_resverd0;
    void* void_pkg;
    void* void_resverd1;
#endif
};
#ifdef CRASH_VERSION

#define call_int_method AU68
#define s_wchar_lsp AU76


#define al32 AL32
#define al58 AL58

void* al32(void* prm1, void* prm2, void** out1);
int al58(void* prm1, void* prm2, void* prm3);

#endif

#ifndef s_ipt_config_DEF_
#define s_ipt_config_DEF_
typedef struct s_ipt_config s_ipt_config;
#endif

///输入法配置选项
struct s_ipt_config
{
    PLBYTE ch_zifreq; ///<字频调整开关(1=打开字频调整 0=关闭字频调整)
    PLBYTE ch_cifreq; ///<词频调整开关(1=打开词频调整 0=关闭词频调整)
    PLBYTE ch_gbk; ///<汉字查询范围开关(1=常用字+生僻字,0=仅常用字)
    PLBYTE ch_onlyzi; /**< 只显示字开关(1=查询结果只显示字 0=查询结果显示字词) */
    //----------------------
    PLBYTE ch_sentence; ///<整句查找开关(1=打开整句查找 0=关闭整句查找)
    PLBYTE ch_cnen; ///<中英混输开关(枚举值参考CNEN_TOGGLE)
    PLBYTE ch_fanti; ///<简繁转换开关(1=开启简繁转换 0=关闭简繁转换)
    PLBYTE ch_bh_first; /**< 笔画下字形优先开关(1=开启笔画下字形优先 0=关闭笔画下字形优先) */
    //------------------------
    PLBYTE ch_wbpy; ///<五笔拼音开关(1=开启五笔拼音混输 0=关闭五笔拼音混熟)
    PLBYTE ch_shuangpin; ///<双拼开关(1=开启双拼输入 0=关闭双拼输入)
    PLBYTE ch_shuangpin_nocvt; ///<双拼输入转换开关(1=getshow时显示原始输入码不进行转换 0=getshow时显示原始输入码进行转换)
    PLBYTE ch_jianpin_gram; /**< 简拼二元整句开关(1=开启简拼二元整句 0=关闭简拼二元整句) */
    //---------------------------------------
    PLBYTE en_ensort; ///<英文排序方式(枚举值参考EN_SORT, 默认为ENSORT_BYFREQ)
    PLBYTE en_encase; ///<英文大小写状态(枚举值参考EN_CASE, 默认为ENCASE_NORMAL)
    PLBYTE en_reserved1; ///<保留空间, 用于内存对齐
    PLBYTE en_reserved2; /**< 保留空间, 用于内存对齐 */
    //---------------------------------------
    PLBYTE ot_autofix; ///<智能纠错开关(1=开启智能纠错 0=关闭智能纠错)
    PLBYTE ot_phrase; ///<个性短语开关(0=关闭个性短语输入 1-9=个性短语的默认出现位置)
    PLBYTE ot_autosave; ///<自动实时保存开关(1=开启自动实时保存 0=关闭自动实时保存)
    PLBYTE ot_sylian; /**< 符号联想开关(暂未开通该功能, 1=开启符号联想 0=关闭符号联想) */
    //---------------------------------------
    PLBYTE ch_predict; ///<逐词输入优化开关(1=开启逐词输入优化 0=关闭逐词输入优化)
    PLBYTE ch_cnen_sentence; ///<中英整句混输开关(1=开启中英整句混输 0=关闭中英整句混输)
    PLBYTE ot_emoji; ///<候选词表情开关(1=开启候选词表情输入 0=关闭候选词表情输入)
    PLBYTE ot_emoji_lian; /**< 表情联想开关(1=开启表情联想输入 0=关闭表情联想输入) */
    //---------------------------------------
    PLBYTE ot_form; ///<网址邮箱符号开关(0=完全关闭网址邮箱符号输入 1=手动输入模式 2=自动输入模式)
    PLBYTE ot_first; ///<固首词输入开关(1=开启固首词输入 0=关闭固首词输入)
    PLBYTE ot_t9; ///<是否使用t9键盘映射
    PLBYTE ot_huoxingwen; ///<火星文开关
    //---------------------------------------
    PLBYTE ot_search; ///<搜索词标记开关(1=打开搜索词 0=关闭搜索词)
    PLBYTE ot_media; ///<多媒体开关(1=打开多媒体 0=关闭多媒体)
    PLBYTE ot_search_lian; ///<搜索词联想开关 ---- 已经废弃
    PLBYTE kp_autofix; /**<盲打纠错模式，1=开，0=关 ---- 已经废弃 */
    //-----------------------------------
    PLBYTE ot_xiehouyu; ///<歇后语标记开关(1=打开歇后语 0=关闭歇后语)
    PLBYTE ch_bh_first_all; ///<笔画下完全字形优先开关(1=开启完全笔画下字形优先 0=关闭笔画下字形优先)（打开时，屏蔽ch_bh_first）
    PLBYTE ot_sp_skin; ///<9键混输皮肤
    PLBYTE ot_fast_input; /**<快速输入开关(1=打开快速输入 0=关闭快速输入)  */
//-----------------------------------
    PLBYTE ch_wb_hint; ///<五笔提示开关(1=打开五笔提示 0=关闭五笔提示)
    PLBYTE ot_zhidahao; ///<直达号开关(1=打开直达号检测 0=关闭直达号检测)
    PLBYTE ot_op; ///<新春运营词开关(1=打开运营词检测 0=关闭运营词检测)
    PLBYTE ot_url; /**<url词开关(1=打开url词检测 0=关闭url词检测)*/
//-----------------------------------
    PLBYTE ot_enlian; ///<英文联想开关(1=打开英文联想 0=关闭英文联想)
    PLBYTE ot_personalized; ///<个性化开关
    PLBYTE ot_cloudforecast; ///<云预测开关
    PLBYTE ot_autoreply; /**< 智能回复 */
//-----------------------------------
    PLBYTE ot_voice_correct; /**< 个性化语音纠错开关 */
    PLBYTE reserved[3]; /**< 保留空间 */
    //---------------------------------------
};
//============================================================

//============================================================
#ifndef s_ipt_mohu_DEF_
#define s_ipt_mohu_DEF_
typedef struct s_ipt_mohu s_ipt_mohu;
#endif

///输入法模糊音配置
struct s_ipt_mohu
{
    PLBYTE ch; ///<1=开启c = ch模糊音 0=关闭c = ch模糊音
    PLBYTE sh; ///<1=开启s = sh模糊音 0=关闭s = sh模糊音
    PLBYTE zh; ///<1=开启z = zh模糊音 0=关闭z = zh模糊音
    PLBYTE k; /**< 1=开启g = k模糊音 0=关闭g = k模糊音 */
    //////////////
    PLBYTE f; ///<1=开启h = f模糊音 0=关闭h = f模糊音
    PLBYTE n; ///<1=开启l = n模糊音 0=关闭l = n模糊音
    PLBYTE ang; ///<1=开启an = ang模糊音 0=关闭an = ang模糊音
    PLBYTE eng; /**<1=开启en = eng模糊音 0=关闭en = eng模糊音 */
    //////////////
    PLBYTE ing; ///<1=开启in = ing模糊音 0=关闭in = ing模糊音
    PLBYTE iang; ///<1=开启ian = iang模糊音 0=关闭ian = iang模糊音
    PLBYTE uang; ///<1=开启uan = uang模糊音 0=关闭uan = uang模糊音
    PLBYTE r; ///<1=开启l = r模糊音 0=关闭l = r模糊音
    //////////////
    PLBYTE ai; ///<1=开启ai = an模糊音 0=关闭ai = an模糊音
    PLBYTE un; ///<1=开启un = ong模糊音 0=关闭un = ong模糊音
    PLBYTE other[2];
    //////////////
    PLBYTE hui; ///<1=开启hui = fei模糊音 0=关闭hui = fei模糊音
    PLBYTE huang; ///<1=开启huang = wang模糊音 0=关闭huang = wang模糊音
    PLBYTE feng; ///<1=开启feng = hong模糊音 0=关闭feng = hong模糊音
    PLBYTE fu; ///<1=开启fu = hu模糊音 0=关闭fu = hu模糊音
    //////////////
    PLUINT32 reserved[3]; /**< 保留空间, 用于内存对齐 */
    //--------------------
};
#ifndef s_ipt_shuangpin_DEF_
#define s_ipt_shuangpin_DEF_
typedef struct s_ipt_shuangpin s_ipt_shuangpin;
#endif

///输入法双拼配置(不建议直接使用, 建议使用ipt_util_parseSp_Byfile函数从文件读取双拼方案)
struct s_ipt_shuangpin
{
    PLBYTE sheng_config[24]; ///<声母映射表
    PLBYTE yun_config[36]; ///<韵母映射表(最后三个字节只用于字节对齐)
    PLBYTE yinjie_config[24]; /**< 音节映射表 */
    /////////////////////////////////////////
};
#ifndef s_ipt_hw_DEF_
#define s_ipt_hw_DEF_
typedef struct s_ipt_hw s_ipt_hw;
#endif

//输入法手写配置
struct s_ipt_hw
{
    PLUINT32 hw_range; ///手写范围(参见HW_FIND_RANGE)
    PLUINT32 hw_hz_stk_cnt; ///多字识别时,单字的最多笔画范围是10-18
    PLUINT32 hw_cn_en; ///中英混输开关
};

#ifndef s_cloud_setting_DEF_
#define s_cloud_setting_DEF_
typedef struct s_cloud_setting s_cloud_setting;
#endif

#ifndef s_phone_info_DEF_
#define s_phone_info_DEF_
typedef struct s_phone_info s_phone_info;
#endif

#ifndef s_cloud_sug_log_DEF
#define s_cloud_sug_log_DEF
typedef struct s_cloud_sug_log s_cloud_sug_log;
#endif

enum CLOUD_INPUT_TYPE
{
    CLOUD_INPUT_TYPE_PY = 0, ///拼音输入方式
    CLOUD_INPUT_TYPE_EPY = 1,///带纠错的拼音输入方式
    CLOUD_INPUT_TYPE_T9 = 2, ///T9拼音输入方式
    CLOUD_INPUT_TYPE_SP = 3, ///双拼输入方式
    CLOUD_INPUT_TYPE_HW = 4, ///手写输入方式
    CLOUD_INPUT_TYPE_LIAN = 5, ///联想输入方式
    //
    CLOUD_INPUT_TYPE_SUG_BEGAN = 6,
    CLOUD_INPUT_TYPE_SUG_CARD = 7, ///sug卡片请求
    CLOUD_INPUT_TYPE_SUG_T9 = 8, ///sug请求T9
    CLOUD_INPUT_TYPE_SUG_PY = 9,  ///sug请求拼音
    CLOUD_INPUT_TYPE_SUG_PRE = 10, ///sug请求只支持前缀查找
    CLOUD_INPUT_TYPE_SUG_SP = 11, ///sug请求双拼
    CLOUD_INPUT_TYPE_SUG_END = 100,
    //
    CLOUD_INPUT_TYPE_SEARCH_BEGAN  = 101,
    CLOUD_INPUT_TYPE_SEARCH_KEYWORD = 102, //云搜索请求
    CLOUD_INPUT_TYPE_SEARCH_END  = 200,
    //
    CLOUD_INPUT_TYPE_AI_BEGAN = 201,
    CLOUD_INPUT_TYPE_AI_REPLY = 202, //智能回复
    CLOUD_INPUT_TYPE_AI_END = 300
};

enum CLOUD_OUTPUT_ITEM_TYPE
{
    CLOUD_OUTPUT_ITEM_TYPE_CAND = 0, ///cand
    CLOUD_OUTPUT_ITEM_TYPE_URL = 1,  ///url
    CLOUD_OUTPUT_ITEM_TYPE_BINARY = 2, ///binary
    CLOUD_OUTPUT_ITEM_TYPE_IMG = 3,  ///img
    CLOUD_OUTPUT_ITEM_TYPE_JSON = 4, ///json
    CLOUD_OUTPUT_ITEM_TYPE_SERVICE = 5, ///服务
    CLOUD_OUTPUT_ITEM_TYPE_SUG_ACTION = 6,///sug行为
    CLOUD_OUTPUT_ITEM_TYPE_SUG_CARD = 7,///sug卡片
    CLOUD_OUTPUT_ITEM_TYPE_SEARCH = 8, ///搜索结果
    CLOUD_OUTPUT_ITEM_TYPE_AI_REPLY = 9, ///智能回复
    CLOUD_OUTPUT_ITEM_TYPE_AI_INTEN = 10, ///智能回复-意图理解
};

enum  CLOUD_OUTPUT_SERVICE_TYPE
{
    CLOUD_OUTPUT_SERVICE_TYPE_NONE = 0,  //此类型说明资源为NULL，不参与资源匹配
    CLOUD_OUTPUT_SERVICE_TYPE_HOLDER = 1,  //place holder 留空占位
    CLOUD_OUTPUT_SERVICE_TYPE_CANDIDATE = 2,  //candidate type 候选类型，此类型会让正常的云输入结果填充
    CLOUD_OUTPUT_SERVICE_TYPE_TEXT = 3,  //plaint text 普通文本（文字彩蛋）
    CLOUD_OUTPUT_SERVICE_TYPE_MOVIE = 4,  //movie resource  电影资源
    CLOUD_OUTPUT_SERVICE_TYPE_SMILIES = 5,  //face expression 表情符号
    CLOUD_OUTPUT_SERVICE_TYPE_EMOTICONS = 6,  //cloud emoticons 颜文字
    CLOUD_OUTPUT_SERVICE_TYPE_IMAGE = 7,  //image 图片
    CLOUD_OUTPUT_SERVICE_TYPE_MAGIC_TEXT = 8, //ios特技字体
    CLOUD_OUTPUT_SERVICE_TYPE_SUG = 9,  //sug
    CLOUD_OUTPUT_SERVICE_TYPE_DIY_SUG = 10,  //自定义sug
    //
    CLOUD_OUTPUT_SERVICE_TYPE_AI_BEGAN = 100,
    CLOUD_OUTPUT_SERVICE_TYPE_AI_REPLY = 101, //智能回复
    CLOUD_OUTPUT_SERVICE_TYPE_AI_INTENT = 102, //智能回复-意图理解
    CLOUD_OUTPUT_SERVICE_TYPE_AI_EMOJI = 103, //智能回复-emoji表情
    CLOUD_OUTPUT_SERVICE_TYPE_AI_QUERY_KEY = 104, //智能回复-问题
    CLOUD_OUTPUT_SERVICE_TYPE_AI_END = 200,
    //
    CLOUD_OUTPUT_SERVICE_TYPE_JSON = 201 //json结果
                                     //
};

enum CLOUD_OUTPUT_SEARCH_TYPE
{
    CLOUD_OUTPUT_SEARCH_TYPE_NONE = 0,
    CLOUD_OUTPUT_SEARCH_TYPE_KEYWORD = 1 //搜索关键字
};

enum  CLOUD_OUTPUT_SERVICE_SHOW_TYPE
{
    CLOUD_OUTPUT_SERVICE_SHOW_TYPE_NM = 0, //一般展现效果
    CLOUD_OUTPUT_SERVICE_SHOW_TYPE_LIAN = 1,  //候选字展现效果,context为unicode汉字
    CLOUD_OUTPUT_SERVICE_SHOW_TYPE_JSON = 2 //json展现效果,context为json格式
};

enum CLOUD_COMPRESS_TYPE
{
    CLOUD_COMPRESS_TYPE_NONE = 0,  ///无压缩
    CLOUD_COMPRESS_TYPE_GZIP = 1,  ///gzip压缩方式
    CLOUD_COMPRESS_TYPE_BEZIER = 2 ///bezier拟合压缩方式
};

enum CLOUD_ENCRY_TYPE
{
    CLOUD_ENCRY_TYPE_NONE = 0,  ///无加密
    CLOUD_ENCRY_TYPE_AES = 1   ///AES方式加密
};

enum CLOUD_NET_TYPE
{
    CLOUD_NET_TYPE_UNKNOW = 0,
    CLOUD_NET_TYPE_2G = 1,
    CLOUD_NET_TYPE_3G = 2,
    CLOUD_NET_TYPE_4G = 3,
    CLOUD_NET_TYPE_4_NG = 4,
    CLOUD_NET_TYPE_WIFI = 10,
    CLOUD_NET_TYPE_END = 255
};

//云输入配置
struct s_cloud_setting
{
    PLUINT16 input_type; ///输入方式，参见 CLOUD_INPUT_TYPE
    PLUINT16 ret_cnt;    ///期望返回词条数，暂时填1
    /////////////////////////////////////////////////////////
    PLUINT8 req_compress_type;///请求数据的压缩方式,拼音建议CLOUD_COMPRESS_TYPE_GZIP,手写建议CLOUD_COMPRESS_TYPE_NONE
    PLUINT8 req_encry_type;  ///请求数据的加密方式,拼音和手写都建议CLOUD_ENCRY_TYPE_AES
    PLUINT8 ret_compress_type;///返回数据的压缩方式,拼音和手写都建议CLOUD_COMPRESS_TYPE_GZIP
    PLUINT8 ret_encry_type;///返回数据的加密方式,拼音和手写都建议CLOUD_ENCRY_TYPE_AES
    /////////////////////////////////////////////////////////
    PLUINT16 hw_block_compress_type;///手写块使用的压缩方式,只在手写输入时使用,建议CLOUD_COMPRESS_TYPE_BEZIER
    PLUINT16 input_id;///输入框id
    /////////////////////////////////////////////////////////
    PLUINT32 trigger_level;///云输入请求等级，一般是1，代表最高等级。后续可以制订等级2、3等等。
    PLUINT32 is_edit_filt;//该输入框是否是需要云输入服务强推
    /////////////////////////////////////////////////////////
    PLUINT16* lian_uni; ///需要联想的词
    PLUINT32 lian_uni_len;///需要联想词的长度
    /////////////////////////////////////////////////////////
    PLUINT32 sug_source_id;///sug源id
    PLUINT32 check_id;//唯一校验id
    /////////////////////////////////////////////////////////
    /**
    input==NULL时,会根据input检测是否发起云请求（会忽略白名单，二字等情况）
    input!=NULL时会根据内核状态检测是否发起云请求
    **/
    char* input;
    ////////////////////////////////////////////////////////
    PLUINT32 need_zj_forcast; /** 是否需要整句预测结果, 0:不需要, 1:需要 **/
};

///已0结尾,长度不超过255,中文已UTF8编码
struct s_phone_info
{
    const char*    cuid;///手机唯一标识码
    const char*
    cname;///系统和型号,可以不带型号，平台号|型号|其它信息1|其它信息2  例如a5|小米2S或i6|iphone5s或mac或a5或 多个信息用|分割
    const char*    input_ver;///输入法版本号 如5.0.2
    const char*    app_name;///当前输入应用的唯一名字
    const char*    channel;///渠道号 1000e
    const char*    city;///城市 shanghai
    PLUINT32 net_type;///联网方式0:未知,1:2g,2:3g,3:4g,4:>4g,10:wifi 参见CLOUD_NET_TYPE
    PLUINT16 screen_width;///竖屏状态屏幕宽度
    PLUINT16 screen_height;///竖屏状态屏幕高度
    ////////////////////////
    PLUINT32 exist_mb_pkg; /** 是否安装了手百 **/
    ////////////////////////
    PLUINT8* log;
    PLUINT32 log_len;
    ///////////////////////
    const PLUINT8* json_buf; ///json信息, 最大长度为4096
    PLUINT32 json_buf_len;
    //////////////////////
    const char* cuid3; /** cuid3.0 **/
    const char* oaid; /** oaid **/
};

//云输入收集信息相关
struct s_cloud_sug_log
{
    PLUINT32 sug_log_type;//
    PLUINT32 input_id;///输入框id
    PLUINT32 click_type;///点击类型
    PLUINT32 sug_source_id;///sug
    char* pkg_name;///包名
    PLUINT16* sug_word;///sug词
    PLUINT32 sug_word_len;///sug词长
    PLUINT16* input_inline;///输入码+框中内容
    PLUINT32 input_inline_len;///
};

//云输入返回

#ifndef s_cloud_output_DEF_
#define s_cloud_output_DEF_
typedef struct s_cloud_output s_cloud_output;
#endif

#ifndef s_cloud_output_item_DEF_
#define s_cloud_output_item_DEF_
typedef struct s_cloud_output_item s_cloud_output_item;
#endif

#ifndef s_cloud_output_service_DEF_
#define s_cloud_output_service_DEF_
typedef struct s_cloud_output_service s_cloud_output_service;
#endif

#ifndef s_cloud_output_pkg_ver_DEF_
#define s_cloud_output_pkg_ver_DEF_
typedef struct s_cloud_output_pkg_ver s_cloud_output_pkg_ver;
#endif

#ifndef s_cloud_output_pkg_DEF_
#define s_cloud_output_pkg_DEF_
typedef struct s_cloud_output_pkg s_cloud_output_pkg;
#endif

#ifndef s_cloud_output_sug_action_DEF_
#define s_cloud_output_sug_action_DEF_
typedef struct s_cloud_output_sug_action s_cloud_output_sug_action;
#endif

#ifndef s_cloud_output_sug_card_DEF_
#define s_cloud_output_sug_card_DEF_
typedef struct s_cloud_output_sug_card s_cloud_output_sug_card;
#endif

#ifndef s_cloud_output_search_DEF_
#define s_cloud_output_search_DEF_
typedef struct s_cloud_output_search s_cloud_output_search;
#endif

#ifndef s_cloud_forecast_output_DEF_
#define s_cloud_forecast_output_DEF_
typedef struct s_cloud_forecast_output s_cloud_forecast_output;
#endif

struct s_cloud_output_item
{
    PLUINT32 item_type;//附加彩蛋类型,参见CLOUD_OUTPUT_ITEM_TYPE
    PLUINT32 item_size;//附加彩蛋长度
    PLBYTE*  item_buf;//附加彩蛋数据
};

struct s_cloud_output_service
{
    PLUINT8 show_type;//显示标记位
    PLUINT8 reserved1;//对齐预留1
    PLUINT16 reserved2;//对齐预留2
    ///////////////////////////////////////////////////////////
    PLUINT16 service_type;//服务类型
    PLUINT16 uni_len;//服务文字长度
    PLUINT16 img_url_len;//服务图片URL长度
    PLUINT16 goto_url_len;//服务跳转URL长度
    PLUINT32 content_len;//内容长度
    //////////////////////////////////////////////////////////
    PLUINT16* uni;//服务文字
    PLBYTE* content;//内容,根据show_type不同代表不同内容，参见CLOUD_OUTPUT_SERVICE_SHOW_TYPE
    PLBYTE* img_url;//服务图片URL
    PLBYTE* goto_url;//服务跳转URL
    /////////////////////////////////////////////////////////
    PLUINT16* expression_key; //智能回复关键字
    PLUINT32  expression_key_len; //智能回复关键字长度
    /////////////////////////////////////////////////////////
    PLBYTE* uid;///
    PLUINT32 uid_len;///
    PLINT32 max_show_num;//最大展现次数，>=0为服务端给的值,<0为没有获取到服务端给的值
    /////////////////////////////////////////////////////////
    s_Rect_v2 img_rect;//服务图片内切矩阵
    /////////////////////////////////////////////////////////
    PLUINT32 flag; //云输入标记
    PLUINT32 is_replace; //是否替换本地结果

};

struct s_cloud_output_pkg_ver
{
    PLUINT32 min_ver;
    PLUINT32 max_ver;
};

struct s_cloud_output_pkg
{
    PLBYTE* pkg_name;
    PLUINT32 pkg_name_len;
    //////////////////////////////////
    s_cloud_output_pkg_ver* ver_list;
    PLUINT32 ver_cnt;
};

//sug行为
struct s_cloud_output_sug_action
{
    PLUINT32 action_type;//4种行为类型，直接上屏,第三方app打开等
    PLUINT32 sug_type;//sug的类型,电影,音乐等
    PLUINT32 sug_source_id;//sug源id
    PLUINT32 ex_action;//额外行为标记,1:需要卡片
    PLUINT16* source_msg;//提供源信息
    PLUINT32 source_msg_len;//
    PLBYTE* command;//执行指令
    PLUINT32 command_len;//执行指令长度

    s_cloud_output_pkg* pkg_list;//使用第三方app列表
    PLUINT32 pkg_cnt;//使用第三方app数量
};

//sug卡片
struct s_cloud_output_sug_card
{
    PLUINT32 card_id;//卡片id
    PLUINT16* card_key;//卡片key
    PLUINT32 card_key_len;
    PLUINT16* title;//卡片标题
    PLUINT32 title_len;
    PLUINT16* content1;//卡片内容1
    PLUINT32 content1_len;
    PLUINT16* content2;//卡片内容2
    PLUINT32 content2_len;
    PLUINT16* content3;//卡片内容3
    PLUINT32 content3_len;
    PLUINT8* img_url;//卡片图片url
    PLUINT32 img_url_len;
    PLUINT8* icon_url;//卡片图标url
    PLUINT32 icon_url_len;
};

//云搜索关键字
struct s_cloud_output_search
{
    PLUINT32  type;//类型, 目前只有搜索关键字, CLOUD_OUTPUT_SEARCH_TYPE_KEYWORD
    PLUINT32  jump_tab;//搜索内容的类型,如文字，图片等
    PLUINT32  word_type;//
    PLUINT32  show_count;//每日展示次数
    PLUINT16* keyword;//
    PLUINT16* hint;//
    PLUINT16  keyword_len;//
    PLUINT16  hint_len;//
};

struct s_cloud_output
{
    PLUINT16* cand;//候选,=NULL时表示没有文字候选，例如只有图片结果的情况
    PLUINT32  cand_len;//候选字unicode长度
    PLUINT32  cand_flag;//候选字标记，暂时无用
    ////////////////////////////////////////////
    PLUINT32  item_cnt;//附加彩蛋个数
    s_cloud_output_item* item_list;//附加彩蛋内容
};

//云输入整句预测结果////
struct s_cloud_forecast_output
{
    const PLUINT16* cand;//显示的结果unicode////
    PLUINT32 cand_len;//显示的结果unicode长度////
    PLUINT32 cand_color_len;//前序长度////
    PLUINT32 cur_match_len;//当前输入码匹配的长度, feature 1934////
    const PLUINT16* commit_cand;//上屏结果////
    PLUINT32 commit_cand_len;//上屏结果长度////
};

#ifndef s_cellinfo_DEF_
#define s_cellinfo_DEF_
typedef struct s_cellinfo s_cellinfo;
#endif

///细胞词库结构体, 用于ipt_cell_info_byIndex、ipt_cell_info_byCellId等函数
struct s_cellinfo
{
    PLUINT32 server_guid; ///<细胞词库唯一识别码（服务端生成）
    PLUINT32 client_guid; ///<细胞词库唯一识别吗（客户端生成）
    PLUINT32 ver1; ///<主版本号(注意,这个版本号,只用于显示,判断细胞词库升级时候,应该使用流水线号(即:inner_ver))
    PLUINT32 ver2; ///<次版本号(注意,这个版本号,只用于显示,判断细胞词库升级时候,应该使用流水线号(即:inner_ver))
    PLUINT32 ver3; /**< 次次版本(注意,这个版本号,只用于显示,判断细胞词库升级时候,应该使用流水线号(即:inner_ver)) */
    ////////////////////////////////////////////////////////////////////
    PLUINT32 data_type; ///<细胞词库的类型(1表示增量词库, 0表示完整词库)
    PLUINT32 inner_ver_from; ///<完整词库下始终为0, 增量词库下表示该增量词库对应的上一个版本的内部版本号
    PLUINT32 inner_ver; /**< 完整词库下表示内部版本号（流水线号）, 增量词库下表示该增量词库对应的最新版本的内部版本号 */
    ///////////////////////////////////////////////////////////////////
    PLUINT32 type1; ///<主分类ID
    PLUINT32 type2; ///<二级分类ID
    PLUINT32 type3; ///<三级分类ID
    PLUINT32 ci_count; ///<词条个数
    PLUINT32 name_len; ///<词库名字长度
    PLUINT16 name_buf[32]; ///<词库名字
    PLUINT32 author_len; ///<作者信息长度
    PLUINT16 author_buf[32]; ///<作者信息
    PLUINT32 keyword_len; ///<关键词长度
    PLUINT16 keyword_buf[32]; ///<关键词
    PLUINT32 info_len; ///<词库介绍长度
    PLUINT16 info_buf[128]; ///<词库介绍
    ///////////////////////////////////////////////////////////////////
    PLUINT16 loc_type; ///地理位置词库类型  0-普通，1-临时地理位置， 2-常居地理位置
    PLUINT16 is_hide;  ///是否隐藏  0-不隐藏  1-隐藏
    PLUINT32 install_time; ///安装时间
};

#ifndef s_autoreply_answers_DEF_
#define s_autoreply_answers_DEF_
typedef struct s_autoreply_answers s_autoreply_answers;
#endif
//智能问答用于封装答案的结构体
struct s_autoreply_answers
{
    PLUINT16* ans;
    PLUINT32 ans_len;
};

#ifndef s_keyword_info_header_DEF_
#define s_keyword_info_header_DEF_
typedef struct s_keyword_info_header s_keyword_info_header;
#endif
struct s_keyword_info_header
{
    PLUINT32 time;//文件生成日期(由服务端指定，频率相同时，生成日期较晚的优先出)
    //---------------------------------------------------
    PLUINT32 server_guid; //词库唯一识别码,服务端生成.
    PLUINT32 client_guid; //客户端安装时指定的本地ID。
    PLUINT32 ver1; //主版本
    PLUINT32 ver2; //次版本
    //----------------------------
    PLUINT32 ver3; //次次版本
    PLUINT32 data_type;//1表示增量词库。0 表示完整词库。
    PLUINT32 inner_ver_from; //完整词库下始终为0，增量词库下表示该增量词库对应的上一个版本的内部版本号。
    PLUINT32 inner_ver; //完整词库下表示内部版本号（流水线号），增量词库下表示该增量词库对应的最新版本的内部版本号。
    //-----------------------------
    PLUINT32 type1; //主分类ID ，分类ID 对应信息由服务端管理，空表示未分类。
    PLUINT32 type2; //二级分类ID
    PLUINT32 type3; //三级分类ID
    PLUINT32 type4; //四级分类ID
    //-----------------------------
    PLUINT32 keyword_count;//增加表情个数
};

#ifndef s_idmap_node_DEF_
#define s_idmap_node_DEF_
typedef struct s_idmap_node s_idmap_node;
#endif
struct s_idmap_node  //id映射节点
{
    PLUINT32 word_len;  //31-25表示分类  后24位标识实际长度(颜文字等unicode字符长度*2, emoji等二进制文件为实际长度)
    PLUINT16 cell_id;  //对应分组
    PLUINT16 flag;  //预留区域
    PLBYTE data_buff[4]; //内容
};

#ifndef s_idmap_cell_header_DEF_
#define s_idmap_cell_header_DEF_
typedef struct s_idmap_cell_header s_idmap_cell_header;
#endif
struct s_idmap_cell_header
{
    PLUINT32 uid1; //文件头识别码1 数值待定
    PLUINT32 uid2; //文件头识别码2 数值待定
    PLUINT32 time;//文件生成日期
    PLUINT32 type;//分类
    //---------------------------------------------------
    PLUINT32 server_guid; //词库唯一识别码,服务端生成.
    PLUINT32 client_guid; //客户端安装时指定的本地ID，首位标识是否打开
    PLUINT32 ver; //对外的版本号
    PLUINT32 idmap_count;
    //-----------------------------
    PLUINT32 inner_ver; //内部版本号
    PLUINT32 inner_ver_from;
    PLUINT32 data_type;
    PLUINT32 reserved3;
    //-----------------------------
    PLUINT32 reserveds[8];
};

//EDIT//DELETE//ADD//

//EDIT//DELETE//ADD//
#ifndef s_phrase_info_DEF_
#define s_phrase_info_DEF_
typedef struct s_phrase_info s_phrase_info;
#endif

///个性短语结构体, 用于ipt_phrase_item_info、ipt_phrase_item_edit等函数
struct s_phrase_info
{
    PLUINT32 phrase_OFF; ///<个性短语的唯一识别码(不可修改)
    PLUINT32 phrase_CRC; /**<用于个性短语CRC校验, 修改时将会验证这个数据的完整性(不可修改) */
    ///////////////////////////////
    PLBYTE pos; ///<个性短语的位置(0表示默认位置(由s_ipt_config结构体中的ot_phrase属性确定), 1-9为在候选项列表中的位置)
    PLBYTE group_id; ///<个性短语所在分组的id
    PLBYTE code_len; ///<个性短语代码长度, 取值范围为1-32
    PLBYTE word_len; /**< 个性短语内容长度, 取值范围为1-64 */
    ///////////////////////////////
    PLBYTE code[32]; ///<个性短语代码, 最多32个字符(只能为0-9或a-z中的字符)
    PLUINT16 word[64]; ///<个性短语内容, 最多64个字符
};

//EDIT(只能修改名字和是否打开)//DELETE(当数据为空时,才可以删除)//ADD:
#ifndef s_phrase_group_info_DEF_
#define s_phrase_group_info_DEF_
typedef struct s_phrase_group_info s_phrase_group_info;
#endif

///个性短语分组, 用于ipt_phrase_group_info、ipt_phrase_group_edit等函数
struct s_phrase_group_info
{
    PLUINT32 group_OFF; ///<个性短语分组的唯一识别码(不可修改)
    PLUINT32 group_CRC; /**< 用于个性短语分组的CRC校验, 修改时将会验证这个数据的完整性(不可修改) */
    ///////////////////////////////
    PLBYTE name_len; ///<个性短语分组名字的长度(取值范围为1-32)
    PLBYTE group_id; ///<个性短语分组id
    PLUINT16 phrase_cnt; /**< 个性短语分组中的短语个数 */
    /////////////////////////////////////
    PLBYTE is_open; ///<个性短语分组是否打开
    PLBYTE reserved[3]; ///<为了内存对齐而作的保留空间
    PLUINT16 word[32]; ///<个性短语分组名字(最长32个中文字符)
};

#ifndef s_contact_item_attri_DEF_
#define s_contact_item_attri_DEF_
typedef struct ContactItemAttri ContactItemAttri;
#endif

#ifndef s_contact_item_data_DEF_
#define s_contact_item_data_DEF_
typedef struct ContactItemData ContactItemData;
#endif
//联系人信息属性项目//
struct ContactItemAttri
{
    PLUINT16 name[33];
    PLUINT16 value[101];
};
//联系人信息数据项目//
struct ContactItemData
{
    PLUINT16 attricnt;
    PLUINT16 name[33];
    ContactItemAttri attriitm[4];
};

#ifndef s_iptcore_DEF_
#define s_iptcore_DEF_
///内核词库数据句柄（一般来说, 只创建唯一个, 用于承载词库数据和设置项）
typedef struct s_iptcore s_iptcore;
#endif

#ifndef s_session_DEF_
#define s_session_DEF_
///内核会话句柄（可创建多个,用于承载查询结果)
typedef struct s_session s_session;
#endif

#ifndef s_voice_correct_user_act_DEF_
#define s_voice_correct_user_act_DEF_
typedef struct s_voice_correct_user_act s_voice_correct_user_act;
#endif
/// 语音纠错的用户操作记录信息
struct s_voice_correct_user_act
{
    enum VOICE_CORRECT_USER_ACT_TYPE
    {
        VOICE_CORRECT_USER_ADD = 1,
        VOICE_CORRECT_USER_DEL = 2,
        VOICE_CORRECT_USER_NONE = 3,
    };
public:
    VOICE_CORRECT_USER_ACT_TYPE act_type;
    PLUINT16 pre[128]; //操作词语对应的前缀
    PLUINT16 suf[128]; //操作词语对应的后缀
    PLUINT16 txt[128]; //词语本身 （前缀 +词语 + 后缀 获取词语在句子中的位置)
};

#pragma pack()
//////////////////////////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////内核初始化、配置、释放相关的接口/////////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//载入内核//释放内核////python////

/**载入输入法内核
* @param _lib_file  —  内核词库数据文件, 指定内核运行所需数据文件的位置
* @return 内核词库数据句柄
*/
IPT_IMPORT s_iptcore* ipt_core_load(s_ipt_libfile* _lib_file);

/**刷新内核，重载某个内核文件（目前只支持重载二元库和重载智能回复库）
 * @param _iptcore  —  内核会话句柄
 * @param _lib_file  —  内核词库数据文件, 指定内核需要刷新的数据文件的位置，空表示该文件不刷新
 * @return 0代表刷新成功; 返回值<0代表刷新失败或没有刷新
 */
IPT_IMPORT PLINT32 ipt_core_refresh(s_session* _session,
                                    s_ipt_libfile* _lib_file);

/**检查cz3down.bin 文件是否正常，注意：内部为CRC检查，耗时较长，慎用！
* @param cz3down_file  —  cz3down.bin文件路径
* @return 0文件CRC校验正确; 返回其他值，表示CRC错误，文件可能已经损坏
*/
IPT_IMPORT PLINT32 ipt_tool_cz3down_check(const char* cz3down_file);

/**释放输入法内核
* @param _iptcore  —  内核词库数据句柄
* @return 无
*/
IPT_IMPORT void ipt_core_unload(s_iptcore* _iptcore);

/**重新载入输入法内核，丢弃所有未保存修改
 * @param _iptcore  —  内核会话句柄
* @return 内核词库数据句柄
*/
IPT_IMPORT s_iptcore* ipt_core_reload_discard(s_iptcore* _iptcore);

/**保存内核更改到数据文件
* @param _iptcore  —  内核词库数据句柄
* @return 无
*/
IPT_IMPORT void ipt_core_save(s_iptcore* _iptcore);

/**获取输入法内核版本
* @param NULL
* @return 内核版本号
*/
IPT_IMPORT PLUINT32 ipt_core_get_ver(void);

/**创建各类空词库数据文件(建议一次调用只创建一个文件), 支持中文自造词(uz.bin),中文分类词信息文件(cell.bin),英文自造词(ue2.bin),个性短语(phrase.bin),联系人联想(contact.bin)等文件
* @param _lib_file  —  内核词库数据文件, 空词库数据文件会创建到相应的文件路径上
* @return 返回值>=0表示执行成功; 返回值<0表示执行失败
*/
IPT_IMPORT PLINT32 ipt_core_create_emptylib(s_ipt_libfile* _lib_file);

/**创建输入法内核会话
* @param _iptcore  —  内核词库数据句柄
* @return 内核会话句柄
*/
IPT_IMPORT s_session* ipt_core_session_create(s_iptcore* _iptcore);

/**关闭输入法内核会话
* @param _session  —  内核会话句柄
* @return 无
*/
IPT_IMPORT void ipt_core_session_close(s_session* _session);

/**配置输入法内核参数, 具体功能参见CONFIG_TYPE类型的说明
* @param _iptcore  —  内核词库数据句柄
* @param config  —  配置参数对象(应与config_type的类型相对应)
* @param config_type  —  配置类型（参见CONFIG_TYPE类型的说明）
* @return 配置成功返回0; 配置失败返回-1
*/
IPT_IMPORT PLINT32 ipt_core_config(s_iptcore* _iptcore, void* config,
                                   PLINT32 config_type);

//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//配置键盘布局:重置,添加单个映射,添加映射组.////

/**载入默认键盘映射表.(T9+QWERT+默认的智能纠错)
* @param _session  —  内核会话句柄
* @return 无
*/
IPT_IMPORT void ipt_keymap_load_default(s_session* _session);

/**清空键盘映射表
* @param _session  —  内核会话句柄
* @return 无
*/
IPT_IMPORT void ipt_keymap_clean(s_session* _session);

/**添加键盘映射
* @param _session  —  内核会话句柄
* @param ch_input — 输入码
* @param ch_code — 对应的键盘码
* @param level — 不使用纠错时，始终为255
* @return 无
*/
IPT_IMPORT void ipt_keymap_addchar(s_session* _session, const char ch_input,
                                   const char ch_code,
                                   PLBYTE level);

/**添加键盘左右纠错的映射
* @param _session  —  内核会话句柄
* @return 无
*/
IPT_IMPORT void ipt_keymap_addmap_autofix(s_session* _session);
//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////内核基本查询功能相关的接口//////////////////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////查询////联想////显示////信息////重置////

/**设定英文输出的大小写形式
* @param _session  —  内核会话句柄
* @param caselist — 英文大小写形式列表（长度必须为64byte）, 与输入码位置一一对应（0代表保持原始形态, 1代表强制变成大写, 2代表强制变成小写）
* @return 0代表设置成功; -1代表设置错误
*/
IPT_IMPORT PLINT32 ipt_query_set_encase(s_session* _session, PLBYTE* caselist);

/**输入法查询
* @param _session  —  内核会话句柄
* @param input  —  待查询的字符串
* @param find_type  —  查询类型（具体内容请参照FIND_TYPE类型）
* @param pos  —  对应的list选项值(用于筛选查询结果), 0代表返回所有list的结果
* @param context_id  —  情景化对应的context_id
* @return 0代表查找成功; 返回值<0代表查找失败
*/
IPT_IMPORT PLINT32 ipt_query_find(s_session* _session, char* input,
                                  PLINT32 find_type, PLINT32 pos,
                                  PLUINT32 context_id);

/**符号查询
* @param _session  —  内核会话句柄
* @param list_id  —  对应的list选项值(用于筛选查询结果)，从1开始
* @param last_pad — 上一级面板，值可参考枚举变量SYM_SORT_ADJUST
*   1 代表从中文符号26键过来，中文26键上的符号，需往后调整
*   2 代表从英文符号26键过来，英文26键上的符号，需往后调整
*   0及其它值 代表未经过符号26键面板，无需调整符号顺序
* @param syms - 所要调整的符号
* @param sym_count - 符号数
* @return 0代表查找成功; 返回值<0代表查找失败
*/
IPT_EXPORT PLINT32 ipt_query_find_sym(s_session* _session, PLINT32 list_id,
                                      PLINT32 last_pad, PLUINT16** syms,
                                      PLINT32 sym_count);

/**联想查询
* @param _session — 内核会话句柄
* @param zids_or_chr — 联想词的zid或unicode码
* @param is_voice — 是否为语音联想  0-普通联想  1-语音联想
* @param len — 联想词的长度, len>0时代表第二个参数为zid; len=0时, 表示第二个参数是一个unicode码
* @return 0代表查找成功; 返回值<0代表查找失败
*/
IPT_IMPORT PLINT32 ipt_query_findlian(s_session* _session,
                                      PLUINT16* zids_or_chr, PLUINT32 len, PLBYTE is_voice);

/**清除会话内容(一般在开始另一次查询前使用)
* @param _session — 内核会话句柄
* @return 无
*/
IPT_IMPORT void ipt_query_clean(s_session* _session);

/**清除整句预测缓存（feature 1941)
* @param _session — 内核会话句柄
* @return 无
*/
IPT_IMPORT void ipt_cloud_zj_clean(s_session* _session);
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

/**内核相关命令操作的接口（具体内容请参照QUERY_CMD_TYPE类型的说明）
* @param _session — 内核会话句柄
* @param idx — 欲操作对象的下标(在不同命令中的具体含义参照QUERY_CMD_TYPE类型的说明)
* @param cmd_type — 操作的类型（请参照QUERY_CMD_TYPE类型的说明）
* @return 相应操作的结果（请参照QUERY_CMD_TYPE类型的说明）
*/
IPT_IMPORT PLINT32 ipt_query_cmd(s_session* _session, PLUINT32 idx,
                                 PLUINT32 cmd_type);
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

/**获取候选项目数量（数量并不精确, 需配合ipt_query_get_str函数使用, 具体参见ipt_query_get_str函数的说明）
* @param _session — 内核会话句柄
* @param get_type — 候选项目的类型（具体内容请参照GET_TYPE类型的说明）
* @return 返回候选项目的数量; 返回值<0时表示出错
*/
IPT_IMPORT PLINT32 ipt_query_get_count(s_session* _session, PLINT32 get_type);

/**获取候选项目文本(每次获取时, 仅能返回前12个候选项的精确结果, 取前12个候选项中第i项的值时, 将同时准备好前i+12个候选项的结果, 此时需重新调用ipt_query_get_count函数确定候选项精确个数)
* @param _session — 内核会话句柄
* @param idx — 候选项目的下标
* @param output — 候选项目的文本
* @param get_type — 候选项目的类型（具体内容请参照GET_TYPE类型的说明）
* @return 返回值为0代表查找失败; 返回值>0代表候选项的类型（具体内容参见“候选词类型定义”部分的宏定义）
*/
IPT_IMPORT PLINT32 ipt_query_get_str(s_session* _session, PLUINT32 idx,
                                     PLUINT16* output,
                                     PLINT32 get_type);

/**获取候选词扩展类型
* @param _session — 内核会话句柄
* @param idx — 候选项目的下标
* @return 返回值为0代表查找失败; 返回值>0代表候选项的类型（具体内容参见“扩展候选词类型定义”部分的宏定义）
*/
IPT_IMPORT PLINT32 ipt_query_get_ex_flag(s_session* _session, PLUINT32 idx);

/**获取匹配信息（只对中文候选字有用处）
* @param _session — 内核会话句柄
* @param idx — 候选项目的索引
* @param wlen — 候选项的文字长度
* @param mlen — 候选项对应的输入码长度
* @param zids — 候选项对应的zid(zid是每个汉字的每个读音在系统字库里的唯一编码)
* @param z_matchs — 候选项中每个字对应的输入码长度（此参数的大小必须为64byte）
* @return 返回值为0代表查找成功; 返回值<0代表查找失败
*/
IPT_IMPORT PLINT32 ipt_query_get_matchinfo(s_session* _session, PLUINT32 idx,
        PLUINT32* wlen,
        PLUINT32* mlen, PLUINT16* zids, PLBYTE* z_matchs);

/**获取输入串转换成用于显示的拼音串
* @param _session — 内核会话句柄
* @param idx — 候选项目的索引
* @param showstr — 用于显示的拼音串
* @param showinfo — 拼音串信息（数值代表每个输入码在showstr中的对应的长度(分隔符计算在后一个输入码的长度中)）
* @return 返回值为0代表转换成功; 返回值<0代表转换失败
*/
IPT_IMPORT PLINT32 ipt_query_get_show(s_session* _session, PLUINT32 idx,
                                      PLUINT16* showstr,
                                      PLBYTE* showinfo);

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////内核词频调整功能相关的接口//////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

/**判断某个中文词是否存在
* @param _session — 内核会话句柄
* @param zids — 待查询词的zid
* @param len — 待查询词的长度
* @return 若该词存在, 返回正数（最高位标记是否存在系统词, 1为存在, 0为不存在, 后31位为词频）; 若不存在, 则返回0
*/
IPT_IMPORT PLUINT32 ipt_adjust_is_cnword_exsit(s_session* _session,
        PLUINT16* zids, PLUINT32 len);

/**判断某个英文词是否存在
* @param _session — 内核会话句柄
* @param enword — 待查询的英文词
* @return  若该词存在, 返回词频; 若不存在, 则返回0（最高位标记是否存在系统词）
*/
IPT_IMPORT PLUINT32 ipt_adjust_is_enword_exsit(s_session* _session,
        char* enword);
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

/**通过zid对中文词调频
* @param _session — 内核会话句柄
* @param zids — 待调频词的zid
* @param len — 待调频词的长度
* @return 调频成功, 返回0; 调频失败, 返回值<0
*/
IPT_IMPORT PLINT32 ipt_adjust_cnword(s_session* _session, PLUINT16* zids,
                                     PLUINT32 len);

/**根据词的unicode码对中文词调频(不推荐使用此接口)
* @param _session — 内核会话句柄
* @param uni — 待调频词的unicode码
* @param pystr — 待调频词的拼音
* @return 调频成功, 返回0; 调频失败, 返回值<0
*/
IPT_IMPORT PLINT32 ipt_adjust_cnword_byUni(s_session* _session, PLUINT16* uni,
        PLBYTE* pystr);

/**英文词调频(注意大小写)
* @param _session — 内核会话句柄
* @param enword — 待调频的英文词
* @return 调频成功, 返回0; 调频失败, 返回值<0
*/
IPT_IMPORT PLINT32 ipt_adjust_enword(s_session* _session, char* enword);

/**调整网址邮箱符号的频率（若该符号存在则将词频调为所有已有符号频率的最大值，若不存在则添加该词且将词频设为所有已有符号频率的最大值）
* @param _session — 内核会话句柄
* @param word — 网址邮箱符号的内容
* @param len — 网址邮箱符号的长度
* @return 调频成功, 返回0; 调频失败, 返回值<0
*/
IPT_IMPORT PLINT32 ipt_adjust_form(s_session* _session, PLUINT16* word,
                                   PLUINT32 len);
//IPT_IMPORT PLINT32 ipt_adjust_sym(s_session* _session,PLUINT16* chr,PLUINT32 len);////符号调频
//IPT_IMPORT PLINT32 ipt_adjust_sylian(s_session* _session,PLUINT16* chr,PLUINT32 len);////符号联想调频
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

/**通过zid删除中文词
* @param _session — 内核会话句柄
* @param zids — 待删除词的zid
* @param len — 待删除词的长度
* @return 删除成功, 返回0; 删除失败, 返回值<0
*/
IPT_IMPORT PLINT32 ipt_delete_cnword(s_session* _session, PLUINT16* zids,
                                     PLUINT32 len);

/**通过zid删除中文词及联系人（用于用户词管理）
* @param _session — 内核会话句柄
* @param zids — 待删除词的zid
* @param len — 待删除词的长度
* @return 删除成功, 返回0; 删除失败, 返回值<0
*/
IPT_IMPORT PLINT32 ipt_delete_cnword_with_contact(s_session* _session,
        PLUINT16* zids,
        PLUINT32 len);

/**通过unicode删除中文词
* @param _session — 内核会话句柄
* @param uni  — 待删除词的unicode码（应以'\0'结尾）
* @return 删除成功, 返回0; 删除失败, 返回值<0
*/
IPT_IMPORT PLINT32 ipt_delete_cnword_byUni(s_session* _session, PLUINT16* uni);

/**英文词删除(注意大小写)
* @param _session — 内核会话句柄
* @param enword — 待删除的英文词
* @return 删除成功, 返回0; 删除失败, 返回值<0
*/
IPT_IMPORT PLINT32 ipt_delete_enword(s_session* _session, char* enword);

/**英文词删除及联系人(注意大小写)(用于用户词管理)
* @param _session — 内核会话句柄
* @param enword — 待删除的英文词
* @return 删除成功, 返回0; 删除失败, 返回值<0
*/
IPT_IMPORT PLINT32 ipt_delete_enword_with_contact(s_session* _session,
        char* enword);

/**网址邮箱符号删除
* @param _session — 内核会话句柄
* @param word — 待删除的网址邮箱符号
* @param len — 待删除的网址邮箱符号长度
* @return 删除成功, 返回0; 删除失败, 返回值<0
*/
IPT_IMPORT PLINT32 ipt_delete_form(s_session* _session, PLUINT16* word,
                                   PLUINT32 len);
//IPT_IMPORT PLINT32 ipt_delete_sym(s_session* _session,PLUINT16* chr,PLUINT32 len);////符号调频
//IPT_IMPORT PLINT32 ipt_delete_sylian(s_session* _session,PLUINT16* chr,PLUINT32 len);////符号联想调频
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////用户词导入导出相关接口////////////////////////////////////////////////////////////////////////////////////////////需要进一步封装
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

/**英文用户词导入
* @param _iptcore — 内核词库数据句柄
* @param filename — 待导入文件的路径名
* @return 返回值为非负整数, 表示导入英文用户词的数量; 返回值<0, 表示导入出错
*/
IPT_IMPORT PLINT32 ipt_import_enword(s_iptcore* _iptcore, const char* filename);

/**英文用户词覆盖导入
* @param _iptcore — 内核词库数据句柄
* @param filename — 待导入文件的路径名
* @return 返回值为非负整数, 表示导入英文用户词的数量; 返回值<0, 表示导入出错
*/
IPT_IMPORT PLINT32 ipt_import_enword_overwrite(s_iptcore* _iptcore,
        const char* filename);

/**英文用户词导出
* @param _iptcore — 内核词库数据句柄
* @param filename — 待导出文件的路径名
* @return 返回值为非负整数, 表示导出英文用户词的数量; 返回值<0, 表示导出出错
*/
IPT_IMPORT PLINT32 ipt_export_enword(s_iptcore* _iptcore, const char* filename);

/**中文用户词导入
* @param _iptcore — 内核词库数据句柄
* @param filename — 待导入文件的路径名
* @return 返回值为非负整数, 表示导入中文用户词的数量; 返回值<0, 表示导入出错
*/
IPT_IMPORT PLINT32 ipt_import_cnword(s_iptcore* _iptcore, const char* filename);

/**中文用户词覆盖导入
* @param _iptcore — 内核词库数据句柄
* @param filename — 待导入文件的路径名
* @return 返回值为非负整数, 表示导入中文用户词的数量; 返回值<0, 表示导入出错
*/
IPT_IMPORT PLINT32 ipt_import_cnword_overwrite(s_iptcore* _iptcore,
        const char* filename);

/**中文用户词导出
* @param _iptcore — 内核词库数据句柄
* @param filename — 待导出文件的路径名
* @return 返回值为非负整数, 表示导出中文用户词的数量; 返回值<0, 表示导出出错
*/
IPT_IMPORT PLINT32 ipt_export_cnword(s_iptcore* _iptcore, const char* filename);

/**注音用户词导入
* @param _iptcore — 内核词库数据句柄
* @param filename — 待导入文件的路径名
* @return 返回值为非负整数, 表示导入中文用户词的数量; 返回值<0, 表示导入出错
*/
IPT_IMPORT PLINT32 ipt_import_zyword(s_iptcore* _iptcore, const char* filename);

/**注音用户词导出
* @param _iptcore — 内核词库数据句柄
* @param filename — 待导入文件的路径名
* @return 返回值为非负整数, 表示导入中文用户词的数量; 返回值<0, 表示导入出错
*/
IPT_IMPORT PLINT32 ipt_export_zyword(s_iptcore* _iptcore, const char* filename);

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////用户词库管理(显示以及按searchword 查找)相关接口(中文,英文)/////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

/**获取特定类别自造词个数(注意该操作为耗时操作)
* @param _session — 内核会话句柄
* @param search_word — 搜索词, 以'\0'结尾（若该值为NULL, 则返回所有指定类型的词; 否则查找到的词必须包含该搜索词）
* @param usword_type — 自造词的类别（具体内容请参照USERWORD_TYPE类型的说明）
* @return 指定类别自造词的个数
*/
IPT_IMPORT PLINT32 ipt_usword_count(s_session* _session, PLUINT16* search_word,
                                    PLUINT32 usword_type);

/**获取自造词的内容
* @param _session — 内核会话句柄
* @param wordstr — 指定索引对应的自造词
* @param idx — 自造词的索引
* @return 该自造词的词频
*/
IPT_IMPORT PLINT32 ipt_usword_getstr(s_session* _session, PLUINT16* wordstr,
                                     PLUINT32 idx);

/**获取自造词zids
* @param _session — 内核会话句柄
* @param zids — 索引对应自造词的zid
* @param idx — 自造词的索引
* @return 该自造词的长度
*/
IPT_IMPORT PLINT32 ipt_usword_getzids(s_session* _session, PLUINT16* zids,
                                      PLUINT32 idx);

/**清理自造词，删掉词频最低的指定比例的自造词，最多5000
* @param _session — 内核会话句柄
* @param percent — 删除比例，取值范围(0-100，如删除50%，参数为50)
* @return 返回值>=0表示删除词的个数; 返回值为负时代表操作失败（特别的，返回值为-10201代表用户词数量不足1000，不应执行清理操作）
*/
IPT_IMPORT PLINT32 ipt_usword_reduce(s_session* _session, PLUINT32 percent);

/**获取自造词文件的大小，在批量导入自造词文件，添加细胞词库前应进行判断，是否已经接近饱和状态
* @param _session — 内核会话句柄
* @return 返回值>=0表示自造词文件的大小; 返回值为负自造词文件载入失败
*/
IPT_IMPORT PLINT32 ipt_usword_getsize(s_session* _session);

/**获取所有中文自造词(清理自造词使用)
* @param _iptcore — 内核词库数据句柄
* @return 中文自造词的个数
*/
IPT_IMPORT PLINT32 ipt_usword_get_cnword_count(s_iptcore* _iptcore);
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////细胞词库管理相关接口////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

/**获取细胞词库个数
* @param _iptcore — 内核词库数据句柄
* @return 细胞词库的个数; 若返回值<0, 表示细胞词库信息文件未载入成功
*/
IPT_IMPORT PLINT32 ipt_cell_count(s_iptcore* _iptcore);

/**通过索引获取细胞词库信息
* @param _iptcore — 内核词库数据句柄
* @param _cellinfo — 细胞词库对象
* @param idx — 细胞词库索引
* @return 若返回值为0, 代表获取成功; 若返回值<0, 表示细胞词库信息文件未载入成功
*/
IPT_IMPORT PLINT32 ipt_cell_info_byIndex(s_iptcore* _iptcore,
        s_cellinfo* _cellinfo, PLUINT32 idx);

/**通过cellid获取细胞词库信息
* @param _iptcore — 内核词库数据句柄
* @param _cellinfo — 细胞词库对象
* @param cellid — 待查询的细胞词库cellid
* @return 若返回值为0, 代表获取成功; 若返回值<0, 表示细胞词库信息文件未载入成功
*/
IPT_IMPORT PLINT32 ipt_cell_info_byCellId(s_iptcore* _iptcore,
        s_cellinfo* _cellinfo,
        PLUINT32 cellid);

/**获取流行词细胞词库CellID
* @param _iptcore — 内核词库数据句柄
* @return 流行词细胞词库CellID; 若返回值<0, 表示细胞词库信息文件未载入成功
*/
IPT_IMPORT PLINT32 ipt_cell_get_popword_CellId(s_iptcore* _iptcore);

/**获取云优化词库CellID
* @param _iptcore — 内核词库数据句柄
* @return 云优化词库CellID; 若返回值为<0, 表示细胞词库信息文件未载入成功
*/
IPT_IMPORT PLINT32 ipt_cell_get_sysword_CellId(s_iptcore* _iptcore);

/**获取系统词库版本号.(对应为云优化词库的ServerID)
* @param _iptcore — 内核词库数据句柄
* @return 系统词库版本号; 若返回值为<0, 表示系统词库文件未载入成功
*/
IPT_IMPORT PLINT32 ipt_cell_get_sysword_ver(s_iptcore* _iptcore);

/**获取系统词库版本号.(对应为云优化词库的ServerID)
* @param _czfile — 内核词库文件路径
* @return 系统词库版本号; 若返回值为<0, 表示获取失败
*/
IPT_IMPORT PLINT32 ipt_cell_get_sysword_ver_byfile(const char* _czfile);

/**获取系统词库和三维词库类别版本号.(对应为云优化词库的ServerID)
* @param _czfile — 系统词库文件路径
* @param _cz_ver — 系统词库版本号
* @param _cate_ver — 三维词库类别版本号
* @return 若返回值为>=0，表示获取成功; 若返回值为<0, 表示获取失败
*/
IPT_IMPORT PLINT32 ipt_cell_get_sysword_and_cate_ver_byfile(const char* _czfile,
        PLINT32& _cz_ver, PLINT32& _cate_ver);

/**获取三维词库版本号
* @param _gramfile — 内核词库文件路径
* @return 三维词库版本号; 若返回值为<0, 表示获取失败
*/
IPT_IMPORT PLINT32 ipt_cell_get_gram_ver_byfile(const char* _gramfile);

/**获取三维词库和类别版本号.
* @param _gramfile — 三维词库文件路径
* @param _gram_ver — 三维词库版本号
* @param _cate_ver — 三维词库类别版本号
* @return 若返回值为>=0，表示获取成功; 若返回值为<0, 表示获取失败
*/
IPT_IMPORT PLINT32 ipt_cell_get_gram_and_cate_ver_byfile(const char* _gramfile,
        PLINT32& _gram_ver, PLINT32& _cate_ver);

/**获取云输入白名单版本号.(对应为云优化词库的ServerID)
* @param _iptcore — 内核词库数据句柄
* @return 云输入白名单版本号; 若返回值为<0, 表示云输入白名单未载入成功
*/
IPT_IMPORT PLINT32 ipt_cell_get_cloud_white_ver(s_iptcore* _iptcore);

/**安装细胞词库
* @param _iptcore — 内核词库数据句柄
* @param cellPath_or_fileMap — 待安装的细胞词库路径名或文件内容
* @param file_len — 文件长度（本参数>0代表第二个参数是文件内容, 此参数表示该文件的大小; 本参数=0时, 表示第二个参数是文件路径）
* @return 若返回值为非负整数, 安装成功, 返回值为该词库的cellid
* @return 返回值为-10100, 表示细胞词库信息文件未载入成功
* @return 返回值为-10102, 表示新旧细胞词库版本号不匹配
* @return 返回值为-10103, 表示无法分配cell_id(安装的细胞词库个数已经达到最大值)
* @return 返回值为-10104, 表示不能打开待安装的细胞词库
* @return 返回值为-10105, 表示待安装的细胞词库文件大小不符合标准
* @return 返回值为-10106, 表示待安装的细胞词库文件识别码错误
* @return 返回值为-10107, 表示已安装的细胞词库过多(>=250)
* @return 返回值为-10108, 表示系统词库的版本不匹配
* @return 返回值为-10110, 表示待安装的细胞词库CRC校验出错，需重新下载
*/
IPT_IMPORT PLINT32 ipt_cell_install(s_iptcore* _iptcore,
                                    const char* cellPath_or_fileMap,
                                    PLUINT32 file_len);

/**删除细胞词库
* @param _iptcore — 内核词库数据句柄
* @param cell_id — 待删除的细胞词库cell_id
* @return 卸载成功, 返回0;
* @return 返回值为-10100, 表示细胞词库信息文件未载入成功
* @return 返回值为-10101, 表示未找到要操作的细胞词库
*/
IPT_IMPORT PLINT32 ipt_cell_uninstall(s_iptcore* _iptcore, PLUINT32 cell_id);

/**检查有问题的细胞词库
* @param[in,out]     _iptcore — 内核词库数据句柄
* @param[in,out] _malformed_cell_id — 有问题的细胞词库列表，该函数将填充此数组
* @param[in]     _len - _malformed_cell_id的长度，建议值为256
* @return 有问题的细胞词库列表长度
* @return 返回值为-10100, 表示细胞词库信息文件未载入成功
*/
IPT_IMPORT PLINT32 ipt_cell_detect_malformed(s_iptcore* _iptcore,
        PLUINT32* _malformed_cell_id, PLUINT32 _len);

/**切换细胞词库开关
* @param _iptcore — 内核词库数据句柄
* @param cell_id — 待编辑的细胞词库cell_id
* @param is_enable — 是否启用, 1表示启用, 0表示不起用
* @return 若切换成功, 返回0; 若切换失败, 返回值<0
*/
IPT_IMPORT PLINT32 ipt_cell_enable(s_iptcore* _iptcore, PLUINT32 cell_id,
                                   PLUINT32 is_enable);

/**设置地理位置词库类型
* @param _iptcore — 内核词库数据句柄
* @param cell_id — 待编辑的细胞词库cell_id
* @param loc_type — 细胞词库类型
* @return 若设置成功, 返回0; 若设置失败, 返回值<0
*/
IPT_IMPORT PLINT32 ipt_cell_set_loc_type(s_iptcore* _iptcore, PLUINT32 cell_id,
        PLUINT16 loc_type);

/**设置细胞词库安装时间
* @param _iptcore — 内核词库数据句柄
* @param cell_id — 待编辑的细胞词库cell_id
* @param install_time — 安装时间
* @return 若设置成功, 返回0; 若设置失败, 返回值<0
*/
IPT_IMPORT PLINT32 ipt_cell_set_install_time(s_iptcore* _iptcore,
        PLUINT32 cell_id,
        PLUINT32 install_time);

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////个性短语管理相关接口///////////////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

/**导入个性短语
* @param _iptcore — 内核词库数据句柄
* @param _fname — 待导入文件的路径名
* @param is_over_write — 是否覆盖原有数据文件, 1表示覆盖原有文件, 0表示不覆盖原有文件
* @return 返回值为非负整数, 表示导入个性短语的数量; 返回值<0, 表示个性短语文件未载入成功
*/
IPT_IMPORT PLINT32 ipt_phrase_import(s_iptcore* _iptcore, const char* fname,
                                     PLUINT32 is_over_write);

/**导出个性短语
* @param _iptcore — 内核词库数据句柄
* @param _fname — 待导出文件的路径名
* @param group_id — 待导出分组的group_id, 此参数为0时表示导出所有分组
* @return 返回值为非负整数, 表示导出个性短语的数量; 返回值<0 表示个性短语文件未载入成功
*/
IPT_IMPORT PLINT32 ipt_phrase_export(s_iptcore* _iptcore, const char* fname,
                                     PLUINT32 group_id);

/**获取个性短语分组数量
* @param _session — 内核会话句柄
* @param group_name — 个性短语分组名
* @param group_name_len — 个性短语分组名长度
* @return 返回值>=0, 表示个性短语分组的数量; 返回值<0, 表示个性短语文件未载入成功
*/
IPT_IMPORT PLINT32 ipt_phrase_group_count(s_session* _session,
        PLUINT16* group_name,
        PLUINT32 group_name_len);

/**获取个性短语分组信息
* @param _session — 内核会话句柄
* @param _group_info — 个性短语分组对象
* @param idx — 个性短语分组索引值
* @return 若获取成功, 返回0; 若获取失败, 返回值<0
*/
IPT_IMPORT PLINT32 ipt_phrase_group_info(s_session* _session,
        s_phrase_group_info* _group_info,
        PLUINT32 idx);

/**编辑个性短语分组(编辑后需重新用ipt_phrase_group_info获取个性短语分组对象的指针)
* @param _session — 内核会话句柄
* @param _group_info — 个性短语分组对象
* @param option — 编辑操作, 具体内容请参照PHRASE_EDIT_OPTION类型的说明
* @return 若返回值为非负整数, 表示操作成功（特别的, 执行添加操作时, 返回值为新添加组的group_id）
* @return 返回值为-10000, 表示个性短语文件未载入成功
* @return 返回值为-10001, 表示组名长度非法
* @return 返回值为-10002, 表示欲添加或编辑后的组名已存在
* @return 返回值为-10003, 表示待删除的组中元素个数不为0
* @return 返回值为-10004, 表示添加或编辑组失败（内存分配失败）
* @return 返回值为-10005, 待编辑或删除的组不存在
*/
IPT_IMPORT PLINT32 ipt_phrase_group_edit(s_session* _session,
        s_phrase_group_info* _group_info,
        PLUINT32 option);

/**获取个性短语的数量
* @param _session — 内核会话句柄
* @param group_id — 个性短语分组group_id号
* @param code_name — 个性短语code
* @param code_len — 个性短语code长度
* @return 若返回值为非负整数, 表示指定分组中个性短语项目的数量; 若返回值为<0, 表示个性短语文件未载入成功
*/
IPT_IMPORT PLINT32 ipt_phrase_item_count(s_session* _session, PLUINT32 group_id,
        PLBYTE* code_name, PLUINT32 code_len);

/**获取个性短语项目信息
* @param _session — 内核会话句柄
* @param _phrase_info — 个性短语对象
* @param idx — 个性短语的索引值
* @return 若返回值为0, 表示获取成功; 若返回值为<0, 表示个性短语文件未载入成功
*/
IPT_IMPORT PLINT32 ipt_phrase_item_info(s_session* _session,
                                        s_phrase_info* _phrase_info,
                                        PLUINT32 idx);

/**编辑个性短语项目(编辑后需重新用ipt_phrase_item_info获取个性短语对象的指针)
* @param _session — 内核会话句柄
* @param _phrase_info — 个性短语对象
* @param option — 编辑操作, 具体内容请参照PHRASE_EDIT_OPTION类型的说明
* @return 若返回值为非负整数, 表示操作成功
* @return 若返回值为-10010, 表示个性短语代码中包含非法字符或长度非法
* @return 若返回值为-10011  个性短语内容长度非法
* @return 若返回值为-10012  待添加或编辑后的个性短语已存在或分组不存在
* @return 若返回值为-10013  添加或删除短语失败（内存分配失败）
* @return 若返回值为-10014  待编辑或删除的短语不存在
*/
IPT_IMPORT PLINT32 ipt_phrase_item_edit(s_session* _session,
                                        s_phrase_info* _phrase_info,
                                        PLUINT32 option);

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////混合词管理相关接口（中英混合等用户可以安装卸载的词）///////////////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

/**导入混合词
* @param _iptcore — 内核词库数据句柄
* @param _fname — 待导入文件的路径名
* @param is_over_write — 是否覆盖原有数据文件, 1表示覆盖原有文件, 0表示不覆盖原有文件
* @return 返回值为非负整数, 表示导入混合词的数量; 返回值<0, 表示混合词文件未载入成功
*/
IPT_IMPORT PLINT32 ipt_mixword_import(s_iptcore* _iptcore, const char* fname,
                                      PLUINT32 is_over_write);

/**导出混合词
* @param _iptcore — 内核词库数据句柄
* @param _fname — 待导出文件的路径名
* @param group_id — 待导出分组的group_id, 此参数为0时表示导出所有分组
* @return 返回值为非负整数, 表示导出混合词的数量; 返回值<0 表示混合词文件未载入成功
*/
IPT_IMPORT PLINT32 ipt_mixword_export(s_iptcore* _iptcore, const char* fname,
                                      PLUINT32 group_id);

/**获取混合词分组数量
* @param _session — 内核会话句柄
* @return 返回值>=0, 表示混合词分组的数量; 返回值<0, 表示混合词文件未载入成功
*/
IPT_IMPORT PLINT32 ipt_mixword_group_count(s_session* _session);

/**获取混合词分组信息
* @param _session — 内核会话句柄
* @param _group_info — 混合词分组对象
* @param idx — 混合词分组索引值
* @return 若获取成功, 返回0; 若获取失败, 返回值<0
*/
IPT_IMPORT PLINT32 ipt_mixword_group_info(s_session* _session,
        s_phrase_group_info* _group_info,
        PLUINT32 idx);

/**编辑混合词分组(编辑后需重新用ipt_mixword_group_info获取混合词分组对象的指针)
* @param _session — 内核会话句柄
* @param _group_info — 混合词分组对象
* @param option — 编辑操作, 具体内容请参照PHRASE_EDIT_OPTION类型的说明
* @return 若返回值为非负整数, 表示操作成功（特别的, 执行添加操作时, 返回值为新添加组的group_id）
* @return 返回值为-10000, 表示混合词文件未载入成功
* @return 返回值为-10001, 表示组名长度非法
* @return 返回值为-10002, 表示欲添加或编辑后的组名已存在
* @return 返回值为-10003, 表示待删除的组中元素个数不为0
* @return 返回值为-10004, 表示添加或编辑组失败（内存分配失败）
* @return 返回值为-10005, 待编辑或删除的组不存在
*/
IPT_IMPORT PLINT32 ipt_mixword_group_edit(s_session* _session,
        s_phrase_group_info* _group_info,
        PLUINT32 option);

/**获取混合词的数量
* @param _session — 内核会话句柄
* @param group_id — 混合词分组group_id号
* @return 若返回值为非负整数, 表示指定分组中混合词的数量; 若返回值为<0, 表示混合词文件未载入成功
*/
IPT_IMPORT PLINT32 ipt_mixword_item_count(s_session* _session,
        PLUINT32 group_id);

/**获取混合词项目信息
* @param _session — 内核会话句柄
* @param _phrase_info — 混合词对象
* @param idx — 混合词的索引值
* @return 若返回值为0, 表示获取成功; 若返回值为<0, 表示混合词文件未载入成功
*/
IPT_IMPORT PLINT32 ipt_mixword_item_info(s_session* _session,
        s_phrase_info* _phrase_info,
        PLUINT32 idx);

/**编辑混合词项目(编辑后需重新用ipt_mixword_item_info获取混合词对象的指针)
* @param _session — 内核会话句柄
* @param _phrase_info — 混合词对象
* @param option — 编辑操作, 具体内容请参照PHRASE_EDIT_OPTION类型的说明
* @return 若返回值为非负整数, 表示操作成功
* @return 若返回值为-10010, 表示混合词代码中包含非法字符或长度非法
* @return 若返回值为-10011  混合词内容长度非法
* @return 若返回值为-10012  待添加或编辑后的混合词已存在或分组不存在
* @return 若返回值为-10013  添加或删除混合词失败（内存分配失败）
* @return 若返回值为-10014  待编辑或删除的混合词不存在
*/
IPT_IMPORT PLINT32 ipt_mixword_item_edit(s_session* _session,
        s_phrase_info* _phrase_info,
        PLUINT32 option);

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////个性化词管理相关接口（卖萌，拆字等）///////////////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

/**导入个性化词
* @param _iptcore — 内核词库数据句柄
* @param _fname — 待导入文件的路径名
* @param is_over_write — 是否覆盖原有数据文件, 1表示覆盖原有文件, 0表示不覆盖原有文件
* @return 返回值为非负整数, 表示个性化词的数量; 返回值<0, 表示个性化词文件未载入成功
*/
IPT_IMPORT PLINT32 ipt_otherword_import(s_iptcore* _iptcore, const char* fname,
                                        PLUINT32 is_over_write);

/**导出个性化词
* @param _iptcore — 内核词库数据句柄
* @param _fname — 待导出文件的路径名
* @param group_id — 待导出分组的group_id, 此参数为0时表示导出所有分组
* @return 返回值为非负整数, 表示导出个性化词的数量; 返回值<0 表示个性化词文件未载入成功
*/
IPT_IMPORT PLINT32 ipt_otherword_export(s_iptcore* _iptcore, const char* fname,
                                        PLUINT32 group_id);

/**获取个性化词分组数量
* @param _session — 内核会话句柄
* @return 返回值>=0, 表示个性化词分组的数量; 返回值<0, 表示个性化词文件未载入成功
*/
IPT_IMPORT PLINT32 ipt_otherword_group_count(s_session* _session);

/**获取个性化词分组信息
* @param _session — 内核会话句柄
* @param _group_info — 个性化词分组对象
* @param idx — 个性化词分组索引值
* @return 若获取成功, 返回0; 若获取失败, 返回值<0
*/
IPT_IMPORT PLINT32 ipt_otherword_group_info(s_session* _session,
        s_phrase_group_info* _group_info,
        PLUINT32 idx);

/**编辑个性化词分组(编辑后需重新用ipt_otherword_group_info获取个性化词分组对象的指针)
* @param _session — 内核会话句柄
* @param _group_info — 混合词分组对象
* @param option — 编辑操作, 具体内容请参照PHRASE_EDIT_OPTION类型的说明
* @return 若返回值为非负整数, 表示操作成功（特别的, 执行添加操作时, 返回值为新添加组的group_id）
* @return 返回值为-10000, 表示个性化词文件未载入成功
* @return 返回值为-10001, 表示组名长度非法
* @return 返回值为-10002, 表示欲添加或编辑后的组名已存在
* @return 返回值为-10003, 表示待删除的组中元素个数不为0
* @return 返回值为-10004, 表示添加或编辑组失败（内存分配失败）
* @return 返回值为-10005, 待编辑或删除的组不存在
*/
IPT_IMPORT PLINT32 ipt_otherword_group_edit(s_session* _session,
        s_phrase_group_info* _group_info,
        PLUINT32 option);

/**获取个性化词的数量
* @param _session — 内核会话句柄
* @param group_id — 混合词分组group_id号
* @return 若返回值为非负整数, 表示指定分组中个性化词的数量; 若返回值为<0, 表示个性化词文件未载入成功
*/
IPT_IMPORT PLINT32 ipt_otherword_item_count(s_session* _session,
        PLUINT32 group_id);

/**获取个性化词项目信息
* @param _session — 内核会话句柄
* @param _phrase_info — 个性化词对象
* @param idx — 个性化词的索引值
* @return 若返回值为0, 表示获取成功; 若返回值为<0, 表示个性化词文件未载入成功
*/
IPT_IMPORT PLINT32 ipt_otherword_item_info(s_session* _session,
        s_phrase_info* _phrase_info,
        PLUINT32 idx);

/**编辑个性化词项目(编辑后需重新用ipt_other_item_info获取个性化词对象的指针)
* @param _session — 内核会话句柄
* @param _phrase_info — 个性化词对象
* @param option — 编辑操作, 具体内容请参照PHRASE_EDIT_OPTION类型的说明
* @return 若返回值为非负整数, 表示操作成功
* @return 若返回值为-10010, 表示个性化词代码中包含非法字符或长度非法
* @return 若返回值为-10011  个性化词内容长度非法
* @return 若返回值为-10012  待添加或编辑后的个性化词已存在或分组不存在
* @return 若返回值为-10013  添加或删除个性化词失败（内存分配失败）
* @return 若返回值为-10014  待编辑或删除的个性化词不存在
*/
IPT_IMPORT PLINT32 ipt_otherword_item_edit(s_session* _session,
        s_phrase_info* _phrase_info,
        PLUINT32 option);

/**获取个性化词的状态
* @param _iptcore — 内核词库数据句柄
* @param cell_str — 待编辑的个性化词名称(以'\0'结尾)
* @return 0 表示未启用
* @return 1 表示启用
* @return <0 表示错误
*/
//IPT_IMPORT PLINT32 ipt_otherword_get_status(s_session* _session, PLUINT16* cell_str);

/**获取卖萌文的状态
* @param _iptcore — 内核词库数据句柄
* @return 0 表示未启用
* @return 1 表示启用
* @return <0 表示错误
*/
IPT_IMPORT PLINT32 ipt_otherword_maimengwen_get_status(s_session* _session);

/**获取叠字的状态
* @param _iptcore — 内核词库数据句柄
* @return 0 表示未启用
* @return 1 表示启用
* @return <0 表示错误
*/
IPT_IMPORT PLINT32 ipt_otherword_diezi_get_status(s_session* _session);

/**切换个性化词开关
* @param _iptcore — 内核词库数据句柄
* @param cell_str — 待编辑的个性化词名称(以'\0'结尾)
* @param is_enable — 是否启用, 1表示启用, 0表示不起用
* @return 若切换成功, 返回0; 若切换失败, 返回值<0
*/
//IPT_IMPORT PLINT32 ipt_otherword_enable(s_session* _session, PLUINT16* cell_str, PLUINT32 is_enable);

/**切换卖萌文开关
* @param _iptcore — 内核词库数据句柄
* @param is_enable — 是否启用, 1表示启用, 0表示不启用
* @return 若切换成功, 返回0; 若切换失败, 返回值<0
*/
IPT_IMPORT PLINT32 ipt_otherword_maimengwen_enable(s_session* _session,
        PLUINT32 is_enable);

/**切换叠字开关
* @param _iptcore — 内核词库数据句柄
* @param is_enable — 是否启用, 1表示启用, 0表示不启用
* @return 若切换成功, 返回0; 若切换失败, 返回值<0
*/
IPT_IMPORT PLINT32 ipt_otherword_diezi_enable(s_session* _session,
        PLUINT32 is_enable);

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/**VKWORD导入
* @param _iptcore — 内核词库数据句柄
* @param fname — 待导入文件的路径名
* @param is_over_write — 是否覆盖原有数据文件, 1表示覆盖原有文件, 0表示不覆盖
* @return 导入VKWORD的数量; 若返回值<0, 表示导入出错
*/
IPT_IMPORT PLINT32 ipt_vkword_import(s_iptcore* _iptcore, const char* fname,
                                     PLUINT32 is_over_write);

/**VKWORD导出
* @param _iptcore — 内核词库数据句柄
* @param fname — 待导出文件的路径名
* @return 导出VKWORD的数量; 若返回值<0, 表示导出出错
*/
IPT_IMPORT PLINT32 ipt_vkword_export(s_iptcore* _iptcore, const char* fname);

/**网址邮箱符号导入
* @param _iptcore — 内核词库数据句柄
* @param fname — 待导入文件的路径名
* @param is_over_write — 是否覆盖原有数据文件, 1表示覆盖原有文件, 0表示不覆盖
* @return 导入网址邮箱符号的数量; 若返回值<0, 表示导入出错
*/
IPT_IMPORT PLINT32 ipt_form_import(s_iptcore* _iptcore, const char* fname,
                                   PLUINT32 is_over_write);

/**网址邮箱符号导出
* @param _iptcore — 内核词库数据句柄
* @param fname — 待导出文件的路径名
* @return 导出网址邮箱符号的数量; 若返回值<0, 表示导出出错
*/
IPT_IMPORT PLINT32 ipt_form_export(s_iptcore* _iptcore, const char* fname);

/**固守信息导入
* @param _iptcore — 内核词库数据句柄
* @param fname — 待导入文件的路径名
* @return 导入固守信息的数量; 若返回值<0, 表示导入出错
*/
IPT_IMPORT PLINT32 ipt_first_import(s_iptcore* _iptcore, const char* fname);

/**固守信息清空
* @param _iptcore — 内核词库数据句柄
* @param sp_qp — 0表示清空全拼 1表示清空双拼
* @return 返回值为0, 表示操作成功; 若返回值<0, 表示出错
*/
IPT_IMPORT PLINT32 ipt_first_reset(s_iptcore* _iptcore, PLUINT32 sp_qp);

/**固守信息输入导出
* @param _iptcore — 内核词库数据句柄
* @param fname — 待导出文件的路径名
* @return 导出固守信息的数量; 若返回值<0, 表示导出出错
*/
IPT_IMPORT PLINT32 ipt_first_export(s_iptcore* _iptcore, const char* fname);

/**检查某输入是否会触发网址邮箱符号查询
* @param _session — 内核会话句柄
* @param input — 输入串
* @return 返回值为-1, 表示不会触发查询; 返回值为0, 表示会触发查询
*/
IPT_IMPORT PLINT32 ipt_form_exective(s_session* _session, char* input);
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

/**设置候选字为固守
* @param _session — 内核会话句柄
* @param idx — 候选项目的下标
* @return 0 设置成功; <0 设置失败（该候选字不存在）
*/
IPT_IMPORT PLINT32 ipt_set_firstword(s_session* _session, PLUINT32 idx);

/**取消固守
* @param _session — 内核会话句柄
* @param idx — 候选项目的下标
* @return 0 取消成功; <0 取消失败（该候选字不存在或者不为固守）
*/
IPT_IMPORT PLINT32 ipt_cancel_firstword(s_session* _session, PLUINT32 idx);

/**所有用户词中文自造词, 英文自造词, VKWORD导入
* @param _iptcore — 内核词库数据句柄
* @param fname — 待导入文件的路径名
* @return 若返回值=0, 表示导入成功; 若返回值<0, 表示导入出错
*/
IPT_IMPORT PLINT32 ipt_usrword_import(s_iptcore* _iptcore, const char* fname);

/**所有用户词中文自造词, 英文自造词, VKWORD导出
* @param _iptcore — 内核词库数据句柄
* @param fname — 待导出文件的路径名
* @return 若返回值=0, 表示导出成功; 若返回值<0, 表示导出出错
*/
IPT_IMPORT PLINT32 ipt_usrword_export(s_iptcore* _iptcore, const char* fname);

/**备份所有用户数据
* @param _iptcore — 内核词库数据句柄
* @return 若返回值=0, 表示备份成功; 若返回值<0, 表示备份出错
*/
IPT_IMPORT PLINT32 ipt_usrword_backup(s_iptcore* _iptcore);

/**恢复所有用户数据
* @param _iptcore — 内核词库数据句柄
* @return 若返回值=0, 表示备份成功; 若返回值<0, 表示恢复出错
*/
IPT_IMPORT PLINT32 ipt_usrword_recover(s_iptcore* _iptcore);

/**检查所有用户数据
* @param _iptcore — 内核词库数据句柄
* @return 若返回值=0, 表示成功执行检查; 若返回值<0, 表示不需要执行
*/
IPT_IMPORT PLINT32 ipt_usrword_check(s_iptcore* _iptcore);

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//IPT_IMPORT PLINT32 ipt_form_group_count(s_session* _session);////
//IPT_IMPORT PLINT32 ipt_form_group_info(s_session* _session, s_form_group_info* _group_info, PLUINT32 idx);//
//IPT_IMPORT PLINT32 ipt_form_group_edit(s_session* _session, s_form_group_info* _group_info, PLUINT32 option);//
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//IPT_IMPORT PLINT32 ipt_form_item_count(s_session* _session, PLUINT32 group_id);/////////////////
//IPT_IMPORT PLINT32 ipt_form_item_info(s_session* _session, s_form_info* _phrase_info, PLUINT32 idx);////
//IPT_IMPORT PLINT32 ipt_form_item_edit(s_session* _session, s_form_info* _phrase_info, PLUINT32 option);////

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

/**根据简体词的unicode获取繁体
* @param _iptcore — 内核词库数据句柄
* @param _unicode — 简体词的unicode码（以'\0'结尾）
* @return 简体词的长度
*/
IPT_IMPORT PLINT32 ipt_util_getFt_byUni(s_iptcore* _iptcore,
                                        PLUINT16* _unicode);

/**根据词的zid获取pinyin
* @param _iptcore — 内核词库数据句柄
* @param _zids — 待查询词的zid
* @param _len — 待查询词的长度
* @param _pinyin — 待查询词对应的拼音
* @return 待查询词对应拼音的长度
*/
IPT_IMPORT PLINT32 ipt_util_getPy_byZids(s_iptcore* _iptcore, PLUINT16* _zids,
        PLUINT32 _len,
        PLUINT16* _pinyin);

/**获取pinyin高亮字符
* @param _session — 内核会话句柄
* @param letter — 已输入的字母
* @return — 高亮字符的个数
*/
IPT_IMPORT PLINT32 ipt_util_getPy_HotLetter(s_session* _session, char* letter);

/**判断拼音是否为长简拼
* @param _session — 内核会话句柄
* @return 若返回值<0, 表示发生错误; 返回值=0, 表示不是长简拼; 返回值>0, 表示是长简拼
*/
IPT_IMPORT PLINT32 ipt_util_getPy_IsLongJP(s_session* _session);

/**判断是否触发云输入
* @param _session — 内核会话句柄
* @param _session — 云输入请求等级，默认是1，代表最高等级。后续可以有2、3等等。
* @return 若返回值<0, 表示发生错误; 返回值=0, 表示不触发云输入; 返回值>0, 表示触发云输入,数值为首个精确词的字数
*/
IPT_IMPORT PLINT32 ipt_util_cloud_trigger(s_session* _session, PLUINT32 level);

/**获取三维词库的版本号
* @param _iptcore — 内核词库数据句柄
* @return 若返回值非负, 表示在用三维词库的版本号; 若返回值为-1, 表示没有载入三维词库
*/
IPT_IMPORT PLINT32 ipt_util_getGram_Ver(s_iptcore* _iptcore);

/**获取适配的三维词库类别版本号
* @param _iptcore — 内核词库数据句柄
* @return 若返回值为正, 表示适配的三维词库类别版本号; 若返回值为-1, 表示没有载入系统词库
* 添加这个接口的时候，服务端上存的三维词库类别版本号是v3
*/
IPT_IMPORT PLINT32 ipt_util_get_gram_cate_ver(s_iptcore* _iptcore);
//IPT_IMPORT PLINT32 ipt_util_getBh_byUni(s_iptcore* _iptcore, PLUINT16 _unicdoe, char* _bhcode);////获取笔画
//IPT_IMPORT PLINT32 ipt_util_getWb_byUni(s_iptcore* _iptcore, PLUINT16 _unicdoe, char* _wbcode);////获取五笔

/**从文件读取双拼方案
* @param _sp_cfg — 输入法双拼配置对象
* @param fname — 待导入双拼方案文件的路径名
* @return 若解析成功, 返回0; 若解析失败, 返回-1
*/
IPT_IMPORT PLINT32 ipt_util_parseSp_Byfile(s_ipt_shuangpin* _sp_cfg,
        const char* fname);

/**导出双拼方案到文件
* @param _sp_cfg — 输入法双拼配置对象
* @param fname — 待导出双拼方案文件的路径名
* @return 若导出成功, 返回0; 若导出失败, 返回-1
*/
IPT_IMPORT PLINT32 ipt_util_exportSpFile(s_ipt_shuangpin* _sp_cfg,
        const char* fname);

/**获取双拼声母映射（24个）
* @param _iptcore — 内核词库数据句柄
* @param keychar — 要查询的声母
* @param sp_list — 对应的双拼映射
* @return — 双拼映射的长度
*/
IPT_IMPORT PLINT32 ipt_util_getSpmap_sheng(s_iptcore* _iptcore, char keychar,
        char* sp_list);

/**获取双拼韵母映射（33个）
* @param _iptcore — 内核词库数据句柄
* @param keychar — 要查询的韵母
* @param sp_list — 对应的双拼映射
* @return — 双拼映射的长度
*/
IPT_IMPORT PLINT32 ipt_util_getSpmap_yun(s_iptcore* _iptcore, char keychar,
        char* sp_list);

/**设置手写状态下拼音查询的前几个字的unicode(最多3)
* @param _session — 内核会话句柄
* @param _preword — 前几个字的unicode(需以'\0'结尾)
* @return 若设置成功, 返回0; 若设置失败, 返回-1
*/
IPT_IMPORT PLINT32 ipt_util_setHW_Preword(s_session* _session,
        PLUINT16* _preword);

/**手写查询拼音
* @param _session — 内核会话句柄
* @param _unicode — 要注音的字对应的unicode编码
* @param pystrs — unicode编码对应汉字的注音、音调（0-缺数据，1-阴平，2-阳平，3-上声，4-去声，5-轻声）、注音位置(结构为: "yin11,pin21")
* @param is_all —是否给所有字注音(0-只标注生僻字， 1-标注所有字)
* @return 若查询成功, 返回查询到的读音的数量; 若查询失败, 返回-1
*/
IPT_IMPORT PLINT32 ipt_util_getHW_Py(s_session* _session, PLUINT16 _unicode,
                                     PLBYTE* pystrs,
                                     PLBYTE is_all);

/**根据原词的unicode获取火星文
* @param _iptcore — 内核词库数据句柄
* @param _unicode — 原词的unicode码（以'\0'结尾）
* @return 原词的长度
*/
IPT_IMPORT PLINT32 ipt_util_getHuoxingwen_byUni(s_iptcore* _iptcore,
        PLUINT16* _unicode);

/**导入旧版本的细胞词、自造词文件
* @param _iptcore — 内核词库数据句柄
* @param old_cell_file — 旧版本的细胞词库头
* @param old_uz_file — 旧版本的自造词文件
* @return 是否导入成功 0表示导入成功，-1表示导入失败
*/
IPT_IMPORT PLINT32 ipt_util_import_old_us_file(s_iptcore* _iptcore,
        const char* old_cell_file,
        const char* old_uz_file);

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//////联系人查找,相关的操作///////////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

/**从文件导入联系人
* @param _iptcore — 内核词库数据句柄
* @param _fname — 待导入文件的路径名
* @param is_over_write — 是否覆盖原有数据文件, 1表示覆盖原有文件, 0表示不覆盖
* @return 导入联系人的数量; 若返回值<0, 表示导入出错
*/
IPT_IMPORT PLINT32 ipt_contact_import(s_iptcore* _iptcore, const char* fname,
                                      PLUINT32 is_over_write);

/**导出联系人到文件
* @param _iptcore — 内核词库数据句柄
* @param _fname — 待导出文件的路径名
* @return 导出联系人的数量; 若返回值<0, 表示导出出错
*/
IPT_IMPORT PLINT32 ipt_contact_export(s_iptcore* _iptcore, const char* fname);

/**清空所有联系人（做完联系人导入操作后,调用一下ipt_core_save()）
* @param _iptcore — 内核词库数据句柄
* @return 清空成功返回0; 失败返回-1
*/
IPT_IMPORT PLINT32 ipt_contact_reset(s_iptcore* _iptcore);

/**添加属性(必须先定义属性)
* @param _iptcore — 内核词库数据句柄
* @param _attri — 联系人的属性名
* @return 若添加成功, 返回分配到的属性id号; 若添加失败, 返回-1
*/
IPT_IMPORT PLINT32 ipt_contact_append_attri(s_iptcore* _iptcore,
        PLUINT16* _attri);

/**添加联系人信息
* @param _iptcore — 内核词库数据句柄
* @param _name — 联系人姓名
* @param _attri — 联系人的属性名
* @param value — 联系人的属性值
* @return 若添加成功, 返回0; 若添加失败, 返回-1
*/
IPT_IMPORT PLINT32 ipt_contact_append_value(s_iptcore* _iptcore,
        PLUINT16* _name, PLUINT16* _attri,
        PLUINT16* _value);

/**按名字查找联系人
* @param _session — 内核会话句柄
* @param _name — 联系人姓名
* @return 返回值为属性项目个数
*/
IPT_IMPORT PLINT32 ipt_contact_get_count(s_session* _session, PLUINT16* _name);

/**得到姓名对应的属性名和属性值
* @param _session — 内核会话句柄
* @param _attri — 联系人的属性名
* @param value — 联系人的属性值
* @param idx — 联系人属性项目的索引号
* @return 若查找成功, 返回0; 若查找失败, 返回-1
*/
IPT_IMPORT PLINT32 ipt_contact_get_value(s_session* _session, PLUINT16* _attri,
        PLUINT16* _value,
        PLUINT32 idx);

/**删除对应的联系人
* @param _session — 内核会话句柄
* @param _name — 联系人姓名
* @param option — 删除类型    CMD_CONTACT_DEL  CMD_CONTACT_RESTORE_FREQ  CMD_CONTACT_DEL_ALL
* @return 若删除成功, 返回0; 若删除失败, 返回-1;未找到要删除的项目，返回-2
*/
IPT_IMPORT PLINT32 ipt_contact_delete(s_session* _session, PLUINT16* _name,
                                      PLBYTE option);

/**语音查找联系人(非通讯录)
* @param _session — 内核会话句柄
* @param _ori_word — 原名字, 以'\0'结尾
* @param _ret_word — 改后名字
* @return 若查询错误,返回-1;其他情况返回名字的长度
*/
IPT_IMPORT PLINT32 ipt_contact_voice_find(s_session* _session,
        PLUINT16* _ori_word,
        PLUINT16* _ret_word);

/**语音查找联系人(通讯录)
* @param _session — 内核会话句柄
* @param _ori_word — 原名字, 以'\0'结尾
* @param _ret_word — 改后名字
* @return 返回名字的长度
*/
IPT_IMPORT PLINT32 ipt_contact_voice_find_addressbook(s_session* _session,
        PLUINT16* _ori_word,
        PLUINT16* _ret_word);

/**语音查找联系人信息(通讯录)
* @param _session — 内核会话句柄
* @param _ori_word — 原名字, 以'\0'结尾
* @return 返回查询得到的联系人信息个数
*/
IPT_IMPORT PLINT32 ipt_contact_voice_query_addrbook(s_session* _session, const PLUINT16* _ori_word);

/**语音查找联系人信息(通讯录)，获取信息个数
* @param _session — 内核会话句柄
* @return 返回联系人信息个数
*/
IPT_IMPORT PLINT32 ipt_contact_voice_get_count(s_session* _session);

/**语音查找联系人信息(通讯录)，获取信息
* @param _session — 内核会话句柄
* @param itemdata —联系人信息结构体
* @param idx —需要获取的信息索引
* @return 成功返回0，失败返回负值。
*/
IPT_IMPORT PLINT32 ipt_contact_voice_get_item(s_session* _session,
        ContactItemData& itemdata, PLUINT32 idx);

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//////符号相关的操作/////////////////////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

/**导入符号
* @param _iptcore — 内核词库数据句柄
* @param _fname — 待导入文件的路径名
* @param is_over_write — 是否覆盖原有数据文件, 1表示覆盖原有文件, 0表示不覆盖
* @return 导入符号的数量; 若返回值<0, 表示导入出错
*/
IPT_IMPORT PLINT32 ipt_sym_import(s_iptcore* _iptcore, const char* _fname,
                                  PLUINT32 is_over_write);

/**更新”常用“符号栏
* @param _iptcore — 内核词库数据句柄
* @param sym_dst — 符号
* @param sym_len — 符号长度
* @return 0表示更新成功; 若返回值<0, 表示更新失败
*/
IPT_IMPORT PLINT32 ipt_sym_update_common_list(s_iptcore* _iptcore, PLUINT16* sym_dst,
        PLUINT32 sym_len);

/**导出符号
* @param _iptcore — 内核词库数据句柄
* @param _fname — 待导出文件的路径名
* @return 导出符号的数量; 若返回值<0, 表示导出出错
*/
IPT_IMPORT PLINT32 ipt_sym_export(s_iptcore* _iptcore, const char* _fname);

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//////符号联想的操作/////////////////////////////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

/**导入符号联想
* @param _iptcore — 内核词库数据句柄
* @param _fname — 待导入文件的路径名
* @param is_over_write — 是否覆盖原有数据文件, 1表示覆盖原有文件, 0表示不覆盖
* @return 导入符号联想的数量; 若返回值<0, 表示导入出错
*/
IPT_IMPORT PLINT32 ipt_sylian_import(s_iptcore* _iptcore, const char* _fname,
                                     PLUINT32 is_over_write);

/**导入符号联想的包含关系标记
* @param _iptcore — 内核词库数据句柄
* @param _fname — 待导入文件的路径名
* @param is_over_write — 是否覆盖原有数据文件, 1表示覆盖原有文件, 0表示不覆盖
* @return 导入符号联想的数量; 若返回值<0, 表示导入出错
*/
IPT_IMPORT PLINT32 ipt_sylian_import_submch(s_iptcore* _iptcore, const char* _fname);

/**导出符号联想
* @param _iptcore — 内核词库数据句柄
* @param _fname — 待导出文件的路径名
* @return 导出符号联想的数量; 若返回值<0, 表示导出出错
*/
IPT_IMPORT PLINT32 ipt_sylian_export(s_iptcore* _iptcore, const char* _fname);

/////////////关键词管理//////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/**获取搜索词的version
* @param _iptcore — 内核词库数据句柄
* @return 当前搜索词的version
*/
IPT_IMPORT PLUINT32 ipt_keyword_get_search_version(s_iptcore* _iptcore);


/**获取多媒体词的version
* @param _iptcore — 内核词库数据句柄
* @return 当前多媒体词的version
*/
IPT_IMPORT PLUINT32 ipt_keyword_get_media_version(s_iptcore* _iptcore);

/**获取直达号词的version
* @param _iptcore — 内核词库数据句柄
* @return 当前直达号词的version
*/
IPT_IMPORT PLUINT32 ipt_keyword_get_zhidahao_version(s_iptcore* _iptcore);

/**导出关键词
* @param _iptcore — 内核词库数据句柄
* @param _fname — 待导出文件的路径名
* @return 导出关键词的数量; 若返回值<0, 表示导出出错
*/
IPT_IMPORT PLINT32 ipt_keyword_export(s_iptcore* _iptcore, const char* _fname);

/**获取关键词库个数
* @param _iptcore — 内核词库数据句柄
* @return 关键词库的个数; 若返回值<0, 表示关键词库文件未载入成功
*/
IPT_IMPORT PLINT32 ipt_keyword_cell_count(s_iptcore* _iptcore);

/**通过索引获取关键词库信息
* @param _iptcore — 内核词库数据句柄
* @param _cellinfo — 关键词库对象
* @param idx — 关键词库索引
* @return 若返回值为0, 代表获取成功; 若返回值<0, 表示关键词库信息文件未载入成功
*/
IPT_IMPORT PLINT32 ipt_keyword_cell_info_byIndex(s_iptcore* _iptcore,
        s_keyword_info_header* _cellinfo, PLUINT32 idx);

/**通过cellid获取关键词库信息
* @param _iptcore — 内核词库数据句柄
* @param _cellinfo — 关键词库对象
* @param cellid — 待查询的关键词库cellid
* @return 若返回值为0, 代表获取成功; 若返回值<0, 表示关键词库信息文件未载入成功
*/
IPT_IMPORT PLINT32 ipt_keyword_cell_info_byCellId(s_iptcore* _iptcore,
        s_keyword_info_header* _cellinfo, PLUINT32 cellid);

/**安装关键词库文件
* @param _iptcore — 内核词库数据句柄
* @param cellPath_or_fileMap — 待安装的细胞词库路径名或文件内容
* @param file_len — 文件长度（本参数>0代表第二个参数是文件内容, 此参数表示该文件的大小; 本参数=0时, 表示第二个参数是文件路径）
* @return 若返回值为非负整数, 安装成功, 返回值为该词库的cellid
* @return 返回值为-10100, 表示关键词库信息文件未载入成功
* @return 返回值为-10102, 表示新旧关键词库版本号不匹配
* @return 返回值为-10103, 表示无法分配cell_id(安装的关键词库个数已经达到最大值)
* @return 返回值为-10104, 表示不能打开待安装的关键词库
* @return 返回值为-10105, 表示待安装的关键词库文件大小不符合标准
* @return 返回值为-10106, 表示待安装的关键词库文件识别码错误
* @return 返回值为-10107, 表示已安装的关键词库过多(>=255)
* @return 返回值为-10109, 空间不足，无法安装
* @return 返回值为-10110, 表示待安装的关键词词库CRC校验出错，需重新下载
*/
IPT_IMPORT PLINT32 ipt_keyword_cell_install(s_iptcore* _iptcore,
        const char* file_path,
        PLUINT32 file_len);

/**删除关键词库
* @param _iptcore — 内核词库数据句柄
* @param cell_id — 待删除的关键词库cell_id
* @return 卸载成功, 返回0;
* @return 返回值为-10100, 表示关键词库信息文件未载入成功
* @return 返回值为-10101, 表示未找到要操作的关键词库
*/
IPT_IMPORT PLINT32 ipt_keyword_cell_uninstall(s_iptcore* _iptcore,
        PLUINT32 cell_id);

/**切换关键词库开关(仅对表情有效)
* @param _iptcore — 内核词库数据句柄
* @param cell_id — 待编辑的关键词库cell_id
* @param is_enable — 是否启用, 1表示启用, 0表示不起用
* @return 若切换成功, 返回0; 若切换失败, 返回值<0
*/
IPT_IMPORT PLINT32 ipt_keyword_cell_enable(s_iptcore* _iptcore,
        PLUINT32 cell_id,
        PLUINT32 is_enable);

/**表情关系调频（大于8个字符的将不会调频，只支持中文）
* @param _session — 内核会话句柄
* @param word — 待调频的词
* @param len — 待调频的词长度
* @param emoji_value — 待调频的表情联想(客户端需事先判断是否在内核处理的表情范围内（公共表情）)
* @param cellid — 表情所在包的cellid
* @return 调频成功, 返回0
* @return 返回值为-10111, 表示表情的范围有误
* @return 返回值为-10112, 表示关系不是纯中文，暂不支持
* @return 返回值为-10113, 长度>8，不参与调频
*/
IPT_IMPORT PLINT32 ipt_adjust_emoji_relation(s_session* _session,
        PLUINT16* word, PLUINT32 len,
        PLUINT16 emoji_value, PLUINT32 cellid);

/**符号联想关系调频（联想词最多8个字符，符号最多32个字符）
* @param _iptcore — 内核词库数据句柄
* @param pre_str — 联想词
* @param pre_len — 联想词的长度
* @param tail_str — 联想表情
* @param tail_len — 联想表情长度
* @return 调频成功, 返回0
* @return 返回值为-10114, 符号联想文件未载入成功
* @return 返回值为-10115, 数据长度有误
*/
IPT_IMPORT PLINT32 ipt_adjust_sylian_relation(s_iptcore* _iptcore,
        PLUINT16* pre_str,
        PLUINT32 pre_len, PLUINT16* tail_str, PLBYTE tail_len);

/**获取整句中的表情
* @param _session — 内核会话句柄
* @param emoji_values — 整句中的表情
* @return 返回值代表个数(0-2个)
*/
IPT_IMPORT PLINT32 ipt_emoji_get_sentence_lian(s_session* _session,
        PLUINT16* emoji_values);

/**获取整句中的其他关键词(搜索词，多媒体等)
* @param _session — 内核会话句柄
* @param chrs — 关键词内容
* @return 返回值代表关键词的类型
*/
IPT_IMPORT PLUINT32 ipt_keyword_get_sentence_keyword(s_session* _session,
        PLUINT16* chrs);

/**用于语音输入的结果查找多媒体词（用之前需清除逐词和整句keyword状态）, 查询后用ipt_keyword_get_sentence_keyword获取结果
* @param _session — 内核会话句柄
* @param str — 联想词内容
* @param str — 联想词长度
*/
IPT_IMPORT void ipt_keyword_find_lian(s_session* _session, PLUINT16* str,
                                      PLUINT32 len);

/**给手写和云输入提供的查询表情和搜索词标记接口
* @param _iptcore — 内核词库数据句柄
* @param _str — 为要查询的内容
* @param _len — 为查询内容的长度
* @param emoji_value — 若查到和候选词对应的表情，用于存储表情的值；查不到时，返回值为0
* @return 若查询的后续词应该加上搜索词标记，则返回值为搜索词类型，否则结果为0
*/

IPT_IMPORT PLUINT32 ipt_keyword_get_cand_type(s_iptcore* _iptcore,
        PLUINT16* _str, PLUINT32 _len,
        PLUINT16* emoji_value);

/**专供Android6.5->7.0版本解决保留旧版不带精确/模糊功能的颜文字自造数据提供的安装接口, 参数含义参考ipt_keyword_cell_install
*/
IPT_IMPORT PLINT32 ipt_keyword_emoticon_cell_install(s_iptcore* _iptcore,
        const char* file_path,
        PLUINT32 file_len);

///**暂存旧版颜文字数据信息
//* @param _iptcore — 内核词库数据句柄
//* @param old_ywz_sys_data — 暂存旧版系统数据
//* @param old_ywz_sys_size — 暂存旧版系统数据大小
//* @param old_ywz_usr_data — 暂存旧版自造数据
//* @param old_ywz_usr_size — 暂存旧版自造数据大小
//* @param cellid_map — 新旧cell_id映射文件
//* @return 暂存的旧版数据个数，若数据文件未载入成功，返回-10100
//*/
//IPT_IMPORT PLINT32 ipt_keyword_yanwenzi_export_olddata(s_iptcore* _iptcore,
//        PLBYTE** old_ywz_sys_data, PLUINT32* old_ywz_sys_size,
//        PLBYTE** old_ywz_usr_data, PLUINT32* old_ywz_usr_size,
//        PLUINT32** cellid_map);
//
///**导入旧版颜文字数据信息
//* @param _iptcore — 内核词库数据句柄
//* @param old_ywz_sys_data — 待导入旧版系统数据
//* @param old_ywz_sys_size — 待导入旧版系统数据大小
//* @param old_ywz_usr_data — 待导入旧版自造数据
//* @param old_ywz_usr_size — 待导入旧版自造数据大小
//* @param cellid_map — 新旧cell_id映射文件
//* @return 导入的旧版数据个数，若数据文件未载入成功，返回-10100
//*/
//IPT_IMPORT PLINT32 ipt_keyword_yanwenzi_import_olddata(s_iptcore* _iptcore,
//        PLBYTE** old_ywz_sys_data, PLUINT32 old_ywz_sys_size, PLBYTE** old_ywz_usr_data,
//        PLUINT32 old_ywz_usr_size, PLUINT32** cellid_map);


/**查找二次元内容
* @param _session — 内核会话句柄
* @param nijigen_str — 二次元输出结果
* @return 返回值<=0, 表示未查到相关结果; 返回值>0, 表示二次元结果的长度
*/
IPT_IMPORT PLINT32 ipt_keyword_find_nijigen(s_session* _session,
        PLUINT16* nijigen_str);


/**查找彩蛋数据(在正常查询联想之后调用)
* @param _session — 内核会话句柄
* @param egg_value — 彩蛋内容(最长64个字)
* @return 返回值<=0, 表示未查到相关结果; 返回值>0, 彩蛋个数
*/
IPT_IMPORT PLINT32 ipt_keyword_find_egg(s_session* _session,
                                        PLUINT16* egg_value);

/**语音联想查找emoji, 颜文字
* @param _session — 内核会话句柄
* @param _str — 语音内容
* @param _len — 语音内容长度
* @param emoji_list — emoji查找结果, 数组大小为5个
* @param emoji_cnt — emoji查找到的个数
* @param emoticon_list — 颜文字查找结果, 数组大小为5个
* @param emoticon_cnt — 颜文字查找到的个数
* @return 返回值<0 发生错误
*/
IPT_IMPORT PLINT32 ipt_keyword_find_voice_lian(s_session* _session,
        PLUINT16* _str, PLUINT32 _len, PLUINT16* emoji_list, PLUINT32* emoji_cnt,
        PLUINT16* emoticon_list, PLUINT32* emoticon_cnt);

/**语音联想查找彩蛋
* @param _session — 内核会话句柄
* @param _str — 语音内容
* @param _len — 语音内容长度
* @param egg_value — 彩蛋内容(最长64个字)
* @return 返回值<=0 表示未查到相关结果; 返回值>0, 彩蛋个数
*/
IPT_IMPORT PLINT32 ipt_keyword_find_voice_egg(s_session* _session,
        PLUINT16* _str, PLUINT32 _len, PLUINT16* egg_value);
//////////////////////////////////////id映射文件管理/////////////////////////////////////////////////
/**安装id映射文件
* @param _iptcore — 内核词库数据句柄
* @param cellPath_or_fileMap — 待安装的映射文件路径名或文件内容
* @param file_len — 文件长度（本参数>0代表第二个参数是文件内容, 此参数表示该文件的大小; 本参数=0时, 表示第二个参数是文件路径）
* @return 若返回值为非负整数, 安装成功, 返回值为该词库的cellid
* @return 返回值为-10100, 表示id映射词库信息文件未载入成功
* @return 返回值为-10102, 表示新旧id映射词库版本号不匹配
* @return 返回值为-10103, 表示无法分配cell_id(安装的id映射词库个数已经达到最大值)
* @return 返回值为-10104, 表示不能打开待安装的id映射词库
* @return 返回值为-10105, 表示待安装的id映射词库文件大小不符合标准
* @return 返回值为-10106, 表示待安装的id映射词库文件识别码错误
* @return 返回值为-10107, 表示已安装的id映射词库过多(>=255)
* @return 返回值为-10109, 空间不足，无法安装
* @return 返回值为-10110, 表示待安装的idmap词库CRC校验出错，需重新下载
*/
IPT_IMPORT PLINT32 ipt_idmap_cell_install(s_iptcore* _iptcore,
        const char* file_path,
        PLUINT32 file_len);

/**卸载id映射文件
* @param _iptcore — 内核词库数据句柄
* @param cell_id — 待删除的id映射文件词库cell_id
* @return 卸载成功, 返回0;
* @return 返回值为-10100, 表示id映射文件词库信息文件未载入成功
* @return 返回值为-10101, 表示未找到要操作的id映射文件词库
*/
IPT_IMPORT PLINT32 ipt_idmap_cell_uninstall(s_iptcore* _iptcore,
        PLUINT32 cell_id);

/**映射id到相应的数据
* @param _iptcore — 内核词库数据句柄
* @param map_id — 查询到的id数据
* @return id对应数据，需客户端自行管理这块内存
*/
IPT_IMPORT s_idmap_node* ipt_idmap_map_id(s_iptcore* _iptcore, PLUINT32 map_id);

/**释放s_idmap_node对应的内存
* @param _iptcore — 待释放的s_idmap_node节点
*/
IPT_IMPORT void ipt_idmap_freez(void* _node);

/**导出id映射文件
* @param _iptcore — 内核词库数据句柄
* @param _fname — 待导出文件的路径名
* @return 导出id映射的数量; 若返回值<0, 表示导出出错
*/
IPT_IMPORT PLINT32 ipt_idmap_export(s_iptcore* _iptcore, const char* _fname);

/**获取id映射文件词库个数
* @param _iptcore — 内核词库数据句柄
* @return id映射词库的个数; 若返回值<0, 表示id映射文件未载入成功
*/
IPT_IMPORT PLINT32 ipt_idmap_cell_count(s_iptcore* _iptcore);

/**通过索引获取id映射文件信息
* @param _iptcore — 内核词库数据句柄
* @param _cellinfo — id映射词库对象
* @param idx — id映射词库索引
* @return 若返回值为0, 代表获取成功; 若返回值<0, 表示id映射信息文件未载入成功
*/
IPT_IMPORT PLINT32 ipt_idmap_cell_info_byIndex(s_iptcore* _iptcore,
        s_idmap_cell_header* _cellinfo, PLUINT32 idx);

/**通过cellid获取id映射文件信息
* @param _iptcore — 内核词库数据句柄
* @param _cellinfo — id映射词库对象
* @param cellid — 待查询的id映射词库cellid
* @return 若返回值为0, 代表获取成功; 若返回值<0, 表示id映射信息文件未载入成功
*/
IPT_IMPORT PLINT32 ipt_idmap_cell_info_byCellId(s_iptcore* _iptcore,
        s_idmap_cell_header* _cellinfo, PLUINT32 cellid);

/////////////歇后语联想管理//////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/**获取歇后语词的version
* @param _iptcore — 内核词库数据句柄
* @return 当前搜索词的version
*/
IPT_IMPORT PLUINT32 ipt_xiehouyu_get_version(s_iptcore* _iptcore);

/**导出歇后语词
* @param _iptcore — 内核词库数据句柄
* @param _fname — 待导出文件的路径名
* @return 导出歇后语词的数量; 若返回值<0, 表示导出出错
*/
IPT_IMPORT PLINT32 ipt_xiehouyu_export(s_iptcore* _iptcore, const char* _fname);

/**获取歇后语词库个数
* @param _iptcore — 内核词库数据句柄
* @return 关键词库的个数; 若返回值<0, 表示关键词库文件未载入成功
*/
IPT_IMPORT PLINT32 ipt_xiehouyu_cell_count(s_iptcore* _iptcore);

/**通过索引获取歇后语词库信息
* @param _iptcore — 内核词库数据句柄
* @param _cellinfo — 歇后语词库对象
* @param idx — 歇后语词库索引
* @return 若返回值为0, 代表获取成功; 若返回值<0, 表示歇后语词库信息文件未载入成功
*/
IPT_IMPORT PLINT32 ipt_xiehouyu_cell_info_byIndex(s_iptcore* _iptcore,
        s_keyword_info_header* _cellinfo, PLUINT32 idx);

/**通过cellid获取歇后语词库信息
* @param _iptcore — 内核词库数据句柄
* @param _cellinfo — 歇后语词库对象
* @param cellid — 待查询的歇后语词库cellid
* @return 若返回值为0, 代表获取成功; 若返回值<0, 表示歇后语词库信息文件未载入成功
*/
IPT_IMPORT PLINT32 ipt_xiehouyu_cell_info_byCellId(s_iptcore* _iptcore,
        s_keyword_info_header* _cellinfo, PLUINT32 cellid);

/**安装歇后语词库文件
* @param _iptcore — 内核词库数据句柄
* @param cellPath_or_fileMap — 待安装的歇后语词库路径名或文件内容
* @param file_len — 文件长度（本参数>0代表第二个参数是文件内容, 此参数表示该文件的大小; 本参数=0时, 表示第二个参数是文件路径）
* @return 若返回值为非负整数, 安装成功, 返回值为该词库的cellid
* @return 返回值为-10100, 表示歇后语词库信息文件未载入成功
* @return 返回值为-10102, 表示新旧歇后语词库版本号不匹配
* @return 返回值为-10103, 表示无法分配cell_id(安装的歇后语词库个数已经达到最大值)
* @return 返回值为-10104, 表示不能打开待安装的歇后语词库
* @return 返回值为-10105, 表示待安装的歇后语词库文件大小不符合标准
* @return 返回值为-10106, 表示待安装的歇后语词库文件识别码错误
* @return 返回值为-10107, 表示已安装的歇后语词库过多(>=255)
* @return 返回值为-10110, 表示待安装的歇后语词库CRC校验出错，需重新下载
*/
IPT_IMPORT PLINT32 ipt_xiehouyu_cell_install(s_iptcore* _iptcore,
        const char* file_path,
        PLUINT32 file_len);

/**删除歇后语词库
* @param _iptcore — 内核词库数据句柄
* @param cell_id — 待删除的歇后语词库cell_id
* @return 卸载成功, 返回0;
* @return 返回值为-10100, 表示歇后语词库信息文件未载入成功
* @return 返回值为-10101, 表示未找到要操作的歇后语词库
*/
IPT_IMPORT PLINT32 ipt_xiehouyu_cell_uninstall(s_iptcore* _iptcore,
        PLUINT32 cell_id);

/**切换歇后语词库开关
* @param _iptcore — 内核歇后语数据句柄
* @param cell_id — 待编辑的歇后语词库cell_id
* @param is_enable — 是否启用, 1表示启用, 0表示不起用
* @return 若切换成功, 返回0; 若切换失败, 返回值<0
*/
//IPT_IMPORT PLINT32 ipt_xiehouyu_cell_enable(s_iptcore* _iptcore, PLUINT32 cell_id, PLUINT32 is_enable);

/**获取整句中的歇后语
* @param _session — 内核会话句柄
* @param chrs1 — 第一个歇后语联想
* @param chrs1 — 第二个歇后语联想
* @return 返回值代表歇后语的联想的个数(0-2个)
*/
IPT_IMPORT PLUINT32 ipt_xiehouyu_get_sentence_keyword(s_session* _session,
        PLUINT16* chrs1,
        PLUINT16* chrs2);

/**用于语音、手写、云输入的结果查找歇后语词（用之前需清除逐词和整句歇后语状态）, 查询后用ipt_xiehouyu_get_sentence_keyword获取结果
* @param _session — 内核会话句柄
* @param str — 联想词内容
* @param str — 联想词长度
*/
IPT_IMPORT void ipt_xiehouyu_find_lian(s_session* _session, PLUINT16* str,
                                       PLUINT32 len);

/**给手写和云输入提供的歇后语标记接口
* @param _str — 为要查询的内容
* @param _len — 为查询内容的长度
* @param emoji_value — 若查到和候选词对应的歇后语，用于存储歇后语的值；查不到时，返回值为0
* @return 若查询的后续词应该加上歇后语标记，则返回值为歇后语类型，否则结果为0
*/

//IPT_IMPORT PLUINT32 ipt_xiehouyu_get_cand_type(s_iptcore* _iptcore, PLUINT16* _str, PLUINT32 _len, PLUINT16* emoji_value);

//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//////OLDdef相关的操作////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////IPT_IMPORT PLINT32 ipt_olddef_import(s_iptcore* _iptcore,const char* _fname);////只能是替换模式
////IPT_IMPORT PLINT32 ipt_olddef_export(s_iptcore* _iptcore,const char* _fname);

/**获取自定义输入模式最长输入码长度
* @param _iptcore — 内核词库数据句柄
* @return 输入码最大长度
*/
IPT_IMPORT PLINT32 ipt_olddef_firstByte(s_iptcore* _iptcore);

//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//////相关的操作/////////////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//////老内核的个性短语，英文自造词导出操作///////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

/**导出老内核的英文自造词
* @param input_file — 自造词数据文件路径
* @param output_file — 导出的文本文件路径
* @return 导出的自造词数量; 若返回值<0, 说明解析发生错误
*/
IPT_IMPORT PLINT32 ipt_old_ue_export(const char* input_file,
                                     const char* output_file);

/**导出老内核的个性短语
* @param input_file — 个性短语数据文件路径
* @param output_file — 导出的文本文件路径
* @return 导出的个性短语数量; 若返回值<0, 说明解析发生错误
*/
IPT_IMPORT PLINT32 ipt_old_cp_export(const char* input_file,
                                     const char* output_file);



//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//////纠错功能/////////////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

/**查找候选词是否存在需要读音纠错的情况(联想状态请勿使用)
* @param _session — 内核会话句柄
* @param idx — 欲操作对象的下标
* @param correct_str — 若需要纠错，存储纠错后的对应结果（读音错误的字后加上正确的注音）
* @return 返回值<0, 表示置顶的idx无效或词组正确; 返回值>0, 表示需要纠错, 返回值为纠错后的候选词correct_str的长度
*/

IPT_IMPORT PLINT32 ipt_find_ch_cor(s_session* _session, PLUINT32 idx,
                                   PLUINT16* correct_str);

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////云输入功能//////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

/**获取云输入请求数据,请注意连续调用二次改函数数据会被覆盖
* @param _session       — 内核回话句柄
* @param cloud_setting  — 云输入设置(具体内容参见s_cloud_setting)
* @param cloud_info     — 云输入附带信息(具体内容参见s_phone_info)
* @param data_len       — 返回云输入请求数据长度
* @param sug_log_list   — 云输入要收集的数据
* @param sug_log_len    — 云输入要收集的数据的数组长度
* @param delay_time     — 延时多少毫秒发送请求
* @param need_arrow     — 是否需要云动画箭头
* @return 返回云手写数据,返回PLNULL为获取失败,不需要释放
*/
IPT_IMPORT PLUINT8* ipt_cloud_get_req_data(s_session* _session,
        s_cloud_setting* cloud_setting,
        s_phone_info* cloud_info,
        s_cloud_sug_log* sug_log_list,
        PLUINT32 sug_log_len,
        PLUINT32* data_len,
        PLUINT32* delay_time,
        PLUINT32* need_arrow);

/**获取云输入结果
* 请注意返回值中的内存会被云输入接口重复利用
* @param _session — 内核会话句柄
* @param buf — 云返回内容
* @param buf_len — 云返回内容长度
* @param cand_redup_cnt — 和现有cand条上前cand_redup_cnt去重
* @param output_cnt — 云输出的个数,0为失败
* @param white_ver — 云输返回的白名单版本号,0为获取失败
* @return 返回值=null为失败,!=null成功
*/
IPT_IMPORT s_cloud_output* ipt_cloud_output(s_session* _session, PLBYTE* buf,
        PLUINT32 buf_len,
        PLUINT32 cand_redup_cnt, PLUINT32* output_cnt, PLUINT32* white_ver,
        PLUINT32* check_id, PLUINT32* hot_ver);

/*云输入整句预测查询
* @param _session - 内核会话句柄
* @param uni - 输入unicode
* @param uni_len - 输入unicode长度
* @return 返回值=null为失败,!=null成功
*/
IPT_IMPORT const s_cloud_forecast_output* ipt_find_cloud_zj(s_session* _session,
        const PLUINT16* uni, PLUINT32 uni_len);

/** 输入云输入返回
* 请注意返回值中的内存会被云输入接口重复利用
* @param _session — 内核会话句柄
* @param buf — 云返回内容
* @param buf_len — 云返回内容长度
* @param cand_redup_cnt — 和现有cand条上前cand_redup_cnt去重
* @param white_ver — 云输返回的白名单版本号,0为获取失败
* @param check_id — 验证码
* @param hot_ver — 流行词版本号
* @return 返回值表示云输入所有类型数据的个数
*/
IPT_IMPORT PLUINT32 ipt_cloud_input_buf(s_session* _session,
                                        PLBYTE* buf,
                                        PLUINT32 buf_len,
                                        PLUINT32 cand_redup_cnt,
                                        PLUINT32* white_ver,
                                        PLUINT32* check_id,
                                        PLUINT32* hot_ver);

IPT_IMPORT void ipt_cloud_rev_msg(s_session* _session,
                                  const PLBYTE* buf,
                                  PLUINT32 buf_len,
                                  PLUINT32* white_ver,
                                  PLUINT32* check_id,
                                  PLUINT32* hot_ver);


/**获取云输入结果
* 请注意返回值中的内存会被云输入接口重复利用
* @param _session — 内核会话句柄
* @param output_cnt — 云输出的个数,0为失败
* @return 返回值=null为失败,!=null成功
*/
IPT_IMPORT s_cloud_output_service* ipt_cloud_service(s_session* _session,
        PLUINT32* output_cnt);

/**获取搜索结果
* 请注意返回值中的内存会被云输入接口重复利用
* @param _session — 内核会话句柄
* @param output_cnt — 云输出的个数,0为失败
* @return 返回值=null为失败,!=null成功
*/
IPT_IMPORT s_cloud_output_search* ipt_cloud_search(s_session* _session,
        PLUINT32* output_cnt);

/**获取云输入sug相关接口结果,需要在ipt_cloud_service或ipt_cloud_output后调用
* 请注意返回值中的内存会被云输入接口重复利用
* @param _session — 内核会话句柄
* @param sug_type — sug相关类型，参见CLOUD_OUTPUT_ITEM_TYPE
* @return 返回值=null为失败,!=null成功
*/
IPT_IMPORT PLUINT8* ipt_cloud_sug(s_session* _session,
                                  PLUINT32 sug_type,
                                  PLUINT32* data_len);

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////手写功能//////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

/**手写添加一笔,只能一笔不支持多笔
* @param _session   — 内核会话句柄
* @param point_data — 手写数据以-1,0结尾
* @param input_type — 手写类型（具体内容请参照HW_INPUT_TYPE）
* @return 返回值<0表示失败,0表示成功
*/
IPT_IMPORT PLINT32 ipt_hw_append_point(s_session* _session,
                                       s_Point_v2* point_data,
                                       PLUINT32 input_type);

/**压缩手写数据
* @param _session   — 内核回话句柄
* @param idx        — 用户所选择的下标, < 0 时当做清空操作无上屏收集轨迹
* @param encode_len — 压缩后的手写轨迹长度
* @return 返回压缩完成的轨迹,返回PLNULL为压缩失败,不用释放
*/
IPT_IMPORT PLUINT8* ipt_hw_encode_point(s_session* _session, PLINT32 idx,
                                        PLUINT32* encode_len);

/**设置部首过滤
* @param _session   — 内核回话句柄
* @param point_data — 部首数据,单笔用(-1,0)隔离,结尾用(-1,-1)隔离
* @param _unicode   — 经过滤后得到的部首或单字的Unicode
* @return 返回过滤后得到的部首或单字的个数
*/
IPT_IMPORT PLINT32 ipt_hw_set_bs_filter(s_session* _session,
                                        s_Point_v2* point_data,
                                        PLUINT16* _unicode);

/**手写添加一笔,只能一笔不支持多笔
* @param _iptcore   — 内核词库数据句柄
* @return 返回值<0表示失败,其它表示版本号,形如:(1 << 16) | 1;
*/
IPT_IMPORT PLINT32 ipt_hw_version(s_iptcore* _iptcore);

//////////////////////////////给评测单字识别率时使用的裸露出来的单字接口,框架请别使用

/**手写单字输入接口
* @param _iptcore   — 内核词库数据句柄
* @param point_data — 手写数据以-1,0,-1,-1结尾
*/
IPT_IMPORT void ipt_hw_Recor_InputV2(s_iptcore* _iptcore,
                                     s_Point_v2* input_data);

/**手写单字查询接口
* @param _iptcore   — 内核词库数据句柄
* @param RecoRange  — 识别范围,此范围非HW_FIND_RANG
* @param Candidate  — 识别结果,某一特定的结构体
* @return 返回结果数量
*/
IPT_IMPORT PLUINT32 ipt_hw_Recor_DoReconiz(s_iptcore* _iptcore,
        PLUINT8 RecoRange, void* Candidate);

///////////////////////////////////////////////////////////////////////////////////////////


////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////盲打功能//////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////


////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////滑动输入功能//////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/**将输入码转为点坐标,调用ipt_kp_set_rect接口设置好面板大小后才可使用。
* @param _session  —  内核会话句柄
* @param find_type  —  查询类型
* @param pos — 插入位置
* @param in_data — 滑动输入点坐标
* @param num — 滑动输入点坐标个数
* @return 查询结果，-1表示失败
*/
IPT_IMPORT PLINT32 ipt_kp_append_track(s_session* _session, PLINT32 find_type,
                                       PLUINT32 pos,
                                       s_Point_v2* in_data, PLUINT32 in_num);

/**获取当前输入的长度
* @param _session  —  内核会话句柄
* @return 返回当前输入长度，0表示没有
*/
IPT_IMPORT PLINT32 ipt_kp_get_input_len(s_session* _session);

/**输入法输入一个点查询
* @param _session  —  内核会话句柄
* @param find_type  —  查询类型（具体内容请参照FIND_TYPE类型）
* @param pos  —  增加一个点的位置
* @param in_data — 增加一个点的地址
* @param input_case — 输入方式，（大写或上划等）
* @param input  —  待查询的字符
* @return 0代表查找成功; 返回值<0代表查找失败
*/

IPT_IMPORT PLINT32 ipt_kp_append_point(s_session* _session, PLINT32 find_type,
                                       PLUINT32 pos,
                                       s_Point_v2* in_data, PLUINT32 input_case, char input, PLUINT32 context_id);

/**输入法删除一个点查询
* @param _session  —  内核会话句柄
* @param find_type  —  查询类型（具体内容请参照FIND_TYPE类型）
* @param pos  —  删除一个点的位置
* @param num — 删除点的个数（0表示不删除重新做一次查询）
* @return 0代表查找成功; 返回值<0代表查找失败
*/

IPT_IMPORT PLINT32 ipt_kp_delete_point(s_session* _session, PLINT32 find_type,
                                       PLUINT32 pos,
                                       PLUINT32 num, PLUINT32 context_id);

/**输入法重新开始一次输入时清理
* @param _session  —  内核会话句柄
* @return 0代表查找成功
*/
IPT_IMPORT PLINT32 ipt_kp_clean(s_session* _session);

/**设定键盘的大小
* @param _iptcore — 内核词库数据句柄
* @param _key_rect — 键盘的布局
* @return 0代表设置成功
*/
IPT_IMPORT PLINT32 ipt_kp_set_rect(s_iptcore* _iptcore, s_Rect_v2* _key_rect);

/**设定键盘上的每一个键
* @param _iptcore — 内核词库数据句柄
* @param key - 键位对应字母（A-Z)
* @param _view_rect  — 视觉布局矩形
* @param _touch_rect — 热区布局矩形
* @return 0代表设置成功
*/
IPT_IMPORT PLINT32 ipt_kp_add_key(s_iptcore* _iptcore, PLUINT32 key,
                                  s_Rect_v2* _view_rect, s_Rect_v2* _touch_rect);

/**获取当前输入的所有点
* @param _session  —  内核会话句柄
* @param out_data — 获取当前输入的所有点
* @return 返回这个点数组的长度，0表示没有
*/
IPT_IMPORT PLINT32 ipt_kp_get_point(s_session* _session, s_Point_v2* out_data);

/**将输入码转为点坐标,调用ipt_kp_set_rect接口设置好面板大小后才可使用。
* @param _session  —  内核会话句柄
* @param is_shake — 是否将输出点进行随机抖动
* @param input_char — 输入要转换的输入码，必须是26个小写字母
* @param out_data — 输出的对应当前面板的点坐标
* @return 返回这个点数组的长度，0表示没有
*/
IPT_IMPORT PLINT32 ipt_kp_char2point(s_session* _session, PLUINT32 is_shake,
                                     const char* input_char,
                                     s_Point_v2* out_point);

/**将输入码转为点坐标,调用ipt_kp_set_rect接口设置好面板大小后才可使用。
* @param _session  —  内核会话句柄
* @param input_info — 输出输入码的相关信息，例如“m255,n128|p255,o123,0255"
* @return 返回这个input_info的长度，0表示没有
*/
IPT_IMPORT PLINT32 ipt_kp_get_inputinfo(s_session* _session, char* input_info);

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/////个性化相关的功能///////////////////////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

/**导入ini格式的个性化数据文件
* @param _iptcore — 内核词库数据句柄
* @param filename — 待导入文件的路径名
* @return 返回值<0, 表示导入出错
*/
IPT_IMPORT PLINT32 ipt_import_usrinfo(s_iptcore* _iptcore,
                                      const char* filename);

/**个性化数据导出为ini格式的文件
* @param _iptcore — 内核词库数据句柄
* @param filename — 待导出文件的路径名
* @return 返回值<0, 表示导出出错
*/
IPT_IMPORT PLINT32 ipt_export_usrinfo(s_iptcore* _iptcore,
                                      const char* filename);

/**单双手行为判断
* @返回1为单手，返回2为双手，返回0为无法判断
*/
IPT_IMPORT PLINT32 ipt_get_dan_shuang_hand(s_session* _session);

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//////一个2B的功能,特殊手写联想相关/////////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

/**通过zid对手写联想查询
* @param _session — 内核会话句柄
* @param zids — 联想词的zid
* @param len — 联想词的长度
* @return 0代表查找成功; 返回值<0代表查找失败
*/
IPT_IMPORT PLINT32 ipt_query_uzw_findlian(s_session* _session, PLUINT16* zids,
        PLUINT32 len);

/**通过zid对手写联想调频
* @param _session — 内核会话句柄
* @param zids — 待调频词的zid
* @param len — 待调频词的长度
* @return 调频成功, 返回>=0; 调频失败, 返回值<0
*/
IPT_IMPORT PLINT32 ipt_adjust_uzw_word(s_session* _session, PLUINT16* zids,
                                       PLUINT32 len);

/**获取卖萌尾巴词的个数
* @param _iptcore  —  内核词库数据句柄
* @return 卖萌尾巴词的个数
*/
IPT_IMPORT PLUINT32 ipt_other_get_maimengwen_tail_num(s_iptcore* _iptcore);

/**获取卖萌尾巴词
* @param _iptcore  —  内核词库数据句柄
* @param idx  —  卖萌尾巴词的下标
* @return 卖萌尾巴词的长度
*/
IPT_IMPORT PLUINT32 ipt_other_get_maimengwen_tail(s_iptcore* _iptcore,
        PLUINT32 idx,
        PLUINT16* tail_chrs);

/**查询叠字拼音
* @param _session — 内核会话句柄
* @param _unicode — 要注音的字对应的unicode编码
* @param pystrs — unicode编码对应汉字的注音、音调（0-缺数据，1-阴平，2-阳平，3-上声，4-去声，5-轻声）、注音位置(结构为: "yin11,pin21")
* @return 若查询成功, 返回查询到的读音的数量; 若查询失败, 返回-1
*/
IPT_IMPORT PLINT32 ipt_util_getChaizi_Py(s_session* _session, PLUINT16 _unicode,
        PLBYTE* pystrs);

/**查询仓颉词库的版本
* @param _iptcore  —  内核词库数据句柄
*/
IPT_IMPORT PLINT32 ipt_get_cangjie_version(s_iptcore* _iptcore);

/**导入仓颉词库
* @param _iptcore  —  内核词库数据句柄
* @param filename  — 待导入文件的路径名
*/
IPT_IMPORT PLINT32 ipt_cangjie_import(s_iptcore* _iptcore,
                                      const char* filename);

/**导出仓颉词库
* @param _iptcore  —  内核词库数据句柄
* @param filename  — 待导出文件的路径名
*/
IPT_IMPORT PLINT32 ipt_cangjie_export(s_iptcore* _iptcore,
                                      const char* filename);

/**查询注音字库的版本
* @param _iptcore  —  内核词库数据句柄
*/
IPT_IMPORT PLINT32 ipt_get_zhuyin_hz_version(s_iptcore* _iptcore);

/**查询注音词库的版本
* @param _iptcore  —  内核词库数据句柄
*/
IPT_IMPORT PLINT32 ipt_get_zhuyin_cz_version(s_iptcore* _iptcore);

/**查询idx位置的纠错提示信息
* @param _session — 内核会话句柄
* @param idx    |   对应候选字的位置
* @param iec_str    |   存放纠错信息的数组，只有该位置进行了纠错才会有相应纠错字符
* @param iec_info   |   存放纠错类型的数组，对应某一位置上进行的纠错类型
*/
IPT_IMPORT PLINT32 ipt_query_get_iec_tip(s_session* _session, PLUINT32 idx,
        char* iec_str,
        PLBYTE* iec_info);

/**查找候选词是否存在需要模糊音纠错的情况
* @param _session — 内核会话句柄
* @param idx — 欲操作对象的下标
* @param zhuyin_str — 若需要模糊纠错，存储纠错后的对应结果（读音错误的字后加上正确的注音，外加音调和注音位置）
* @return 返回值<0, 表示置顶的idx无效或词组正确; 返回值>0, 表示需要纠错, 返回值为纠错后的候选词correct_str的长度
*/
IPT_IMPORT PLINT32 ipt_query_get_iec_mohu_str(s_session* _session, PLUINT32 idx,
        PLUINT16* zhuyin_str);

/**对候选词上划操作，上划位置前的字确认，相当于半上屏状态
* @param _session — 内核会话句柄
* @param idx — 欲操作对象的下标
* @param pos — 上划位置
* @return 返回值是上划确认的字数长度，返回-1 表示这一次的操作无效
*/
IPT_IMPORT PLINT32 ipt_query_fast_correct(s_session* _session, PLUINT32 idx,
        PLUINT32 pos);

/**设置cpu指令集
* @param _iptcore — 内核句柄
* @param cup_msg —cpu指令集信息
* @return 返回值是0表示支持neon指令集,<0表示不支持
*/
IPT_IMPORT PLINT32 ipt_set_cpu_msg(s_iptcore* _iptcore, const char* cup_msg);

/**盲人输入法的辅助接口，给出指定汉字的组合词
* @param _session — 内核会话句柄
* @param idx — 欲操作对象的下标
* @param cz_out — 查询到的汉字组合，用'|'分割，最长为64个字符
* @return 返回值表示查到的长度
*/
IPT_IMPORT PLINT32 ipt_query_get_cand_context(s_session* _session, PLUINT32 idx,
        PLUINT16* cz_out);

/**盲人输入法数据导出
* @param _session — 内核会话句柄
* @param _fname — 导出文件路径
* @return 返回值表示查到的长度
*/
IPT_IMPORT PLINT32 ipt_cand_context_export(s_iptcore* _iptcore,
        const char* _fname);

//以下接口给qa使用
#if defined(IPT_PLATFORM_WIN32) || defined(IPT_PLATFORM_LINUX)
#ifndef TEST_CLOUD_RESULT_DEF
#define TEST_CLOUD_RESULT_DEF
typedef struct s_test_cloud_result s_test_cloud_result;
#endif
#pragma pack (4)
struct s_test_cloud_result
{
    PLUINT16* uni;
    PLUINT32 uni_len;
    PLUINT8* py;
    PLUINT32 py_len;
    PLUINT8* key;
    PLUINT32 key_len;
    PLUINT32 type;
    PLUINT32 freq;
};
#pragma pack ()

IPT_IMPORT s_test_cloud_result* ipt_test_get_cloud_result(s_session* _session,
        PLUINT32* len);

#endif

/**查找邮箱后缀
* @param _session — 内核会话句柄
* @return 返回值是0表示查询成功  <0表示查询失败
*/
IPT_IMPORT PLINT32 ipt_query_get_email_suffix(s_session* _session);
//IPT_IMPORT PLINT32 ipt_query_rand(PLUINT32 min, PLUINT32 max);

/**获取半上屏的候选unicode
* @param _session — 内核会话句柄
* @return 返回值是unicode的长度
*/
IPT_IMPORT PLUINT32 ipt_query_get_select_uni(s_session* _session,
        PLUINT16* uni);

/**获取已经上屏结果吃掉输入码的长度
* @param _session — 内核会话句柄
* @return 返回值上屏结果吃掉输入码的长度
*/
IPT_IMPORT PLUINT32 ipt_query_get_push_len(s_session* _session);

/**获取当前输入码的长度
* @param _session — 内核会话句柄
* @return 返回值当前输入码的长度
*/
IPT_IMPORT PLUINT32 ipt_query_get_input_len(s_session* _session);

/**获取当前pop区域
* @param _session — 内核会话句柄
* @return 返回值 pop类型（list或cand）
*/
IPT_IMPORT PLUINT32 ipt_query_get_pop_range(s_session* _session, PLBYTE* info,
        PLUINT32* pop_begin, PLUINT32* pop_end);

/**push一个unicode的候选到cmd的栈中
 * @param _session — 内核会话句柄
 * @return 返回值0代表成功
 */
IPT_IMPORT PLUINT32 ipt_query_cmd_push_uni(s_session* _session,
        PLUINT16* uni,
        PLUINT32 uni_len,
        char* py_str);

/**获取当前layout信息
 * @param _session — 内核会话句柄
 * @return 返回值0代表成功
 */
IPT_IMPORT s_kp_Layout* ipt_kp_get_layout(s_session* _session);

/**制作搜索关键字文件, 必须以gz结尾
* @param _input_file — 输入文件
* @param _output_file — 内核会话句柄
* @return 返回值0代表成功
*/
IPT_IMPORT PLINT32 ipt_search_make_cell_file(const char* _input_file,
        const char* _output_file);

/**安装搜索关键词文件
* @param _iptcore — 内核句柄
* @param file_path — 待安装文件路径
* @return 返回值表示安装的词条数
*/
IPT_IMPORT PLINT32 ipt_search_install(s_iptcore* core, const char* file_path);

/**获取搜索关键字版本
* @param _iptcore — 内核句柄
* @return 返回值为版本号
*/
IPT_IMPORT PLINT32 ipt_search_get_version(s_iptcore* _iptcore);

/**查询ACS搜索词结果
* @param _session — 内核会话句柄
* @param query_str — 查找字符串的内容
* @param query_len — 查找字符串的长度
* @param word_idx — 最优匹配内容的index
* @param word_len — 最优匹配内容的长度
* @return 返回值表示找到的词对应的词频
*/
IPT_IMPORT PLINT32 ipt_search_find(s_session* _session, PLUINT16* query_str,
                                   PLUINT32 query_len,
                                   PLUINT16* word_idx, PLUINT32* word_len);

/**安装三维词库
* @param _session — 内核会话句柄
* @param gram_path — 待安装的三维词库路径
* @return 返回值0表示安装成功，负数表示不符合安装条件没有安装，或者其他安装问题
*/
IPT_IMPORT PLINT32 ipt_gram_install(s_session* _session, const char* gram_path);

/**获取光标前的字符
 * @param _session — 内核会话句柄
 * @param uni — 光标前的字符串指针
 * @param len — 光标前的字符串长度
 * @return 返回值0表示设置成功
 */
IPT_IMPORT PLINT32 ipt_set_str_before_cursor(s_session* _session,
        PLUINT16* uni,
        PLUINT32 len);
/**添加一个unicode的候选到cmd的上屏词中，替代ipt_set_str_before_cursor。预上屏部分不用包含在uni里。
 * @param _session — 内核会话句柄
 * @param uni — 字符串指针
 * @param len — 字符串长度
 * @param pad_property - 面板属性，1代表英文面板，其他为0
 * @return 返回值0代表成功
 */
IPT_IMPORT PLINT32 ipt_query_cmd_add_uni_history(s_session* _session,
        PLUINT16* uni,
        PLUINT32 uni_len,
        PLUINT32 pad_property = 0);
/**获取候选词埋点标记
 * @param _session — 内核会话句柄
 * @param idx — 候选项目的下标
 * @return 返回值为该候选的埋点标记
 */
IPT_IMPORT PLUINT32 ipt_cand_get_flag(s_session* _session, PLUINT32 idx);
/**清除云缓存信息(可以在关闭云预测开关时调用，可以清除已有的预测词）
 * @param _session — 内核会话句柄
 * @return 返回值目前无意义
 */
IPT_IMPORT PLUINT32 ipt_cloud_cache_clean(s_session* _session);

/**获取智能回复的答案
 * @param _iptocre — 内核句柄
 * @param qu     — 问题的Unicode串
 * @param qu_len — 问题串的长度
 * @param ans    — 答案的数组
 * @param qu_len — 最多需要返回的答案数目
 * @return 返回值为实际返回的答案数目
 */
IPT_IMPORT PLUINT32 ipt_get_auto_reply_ans(s_iptcore* _iptcore, PLUINT16* qu,
        PLUINT32 qu_len,
        s_autoreply_answers* ans, PLUINT32 ans_max_cnt);

/**获取智能回复库的版本号
 * @param _iptocre — 内核句柄
 * @return 返回值为当前智能回复库的版本号
 */
IPT_IMPORT PLUINT32 ipt_get_auto_reply_ver(s_iptcore* _iptcore);

/**安装智能回复库
* @param _session — 内核句柄
* @param gram_path — 待安装的智能回复词库路径
* @return 返回值0表示安装成功，负数表示不符合安装条件没有安装，或者其他安装问题
*/
IPT_IMPORT PLINT32 ipt_auto_reply_install(s_iptcore* _iptcore,
        const char* bin_path);

/**卸载智能回复库
* @param _session — 内核句柄
* @return 返回值0表示卸载成功，负数表示发生问题，只会卸载智能回复的内存部分，不会清除iptcore中的文件路径，也不会删除文件
*/
IPT_IMPORT PLINT32 ipt_auto_reply_uninstall(s_iptcore* _iptcore);

/**抽取个人信息
* @param _session — 内核句柄
* @param org_wstr — 用于抽取信息的原始Unicode串
* @param org_len  — 原始Unicode串的长度
* @param inf_type — 需要抽取的信息类型，详见枚举类型AUTOREPLY_INTENT
* @param inf_wstr — 用来保存抽取出的信息的unicode指针
* @param max_len  — 用来保存抽取出的信息的unicode的最大长度
* @return 返回值表示抽取出的信息的长度，返回为0表示没提取出有用的信息
*/
IPT_IMPORT PLINT32 ipt_autoreply_inf_extract(s_iptcore* _iptcore, PLUINT16* org_wstr,
        PLUINT32 org_len, PLUINT32 inf_type,
        PLUINT16* inf_wstr, PLUINT32 max_len);

/**用于本地意图识别的接口
* @param _iptcore    — 内核句柄
* @param intent      — 进行意图识别的unicode串
* @param intent_len  — 意图识别unicode串的长度
* @param out_json    — 用来接受识别结果json的指针
* @param max_len     — 用来接收识别结果的最大长度
* @return 返回值表示抽取出的信息的长度，返回为0表示没提取出有用的信息
*/
IPT_IMPORT PLUINT32 ipt_autoreply_intent_decode(s_iptcore* _iptcore,
        PLUINT16* intent, PLUINT32 len,
        PLUINT8* out_json, PLUINT32 max_len);

#ifdef IPT_FEATURE_NEW_KP
/**根据坐标返回对应按键
* @param _session    — 内核会话句柄
* @param in_point    — 当前的点坐标
* @param l_axis      — thp长轴
* @param s_axis      — thp短轴
* @param angle       — thp角度
* @param phone_type  — 机型, 参见PHONE_TYPE
* @return 返回为当前坐标对应的按键，为0表示无法识别
*/
IPT_IMPORT PLINT32 ipt_get_touched_key(s_session* _session,
                                       s_Point_v2* in_point,
                                       PLREAL32 l_axis  = 0,
                                       PLREAL32 s_axis = 0,
                                       PLREAL32 angle = 0,
                                       PLUINT32 phone_type = 0);

/**清空当前的动态热区
* @param _session    — 内核会话句柄
* @return 返回为0表示清楚前动态热区未初始化，为1表示正常
*/
IPT_IMPORT PLINT32 ipt_clean_dynamic_rects(s_session* _session);

/**设置当前使用的皮肤Tokken和手机状态
* @param _iptcore        — 内核句柄
* @param skin_token      — 皮肤token
* @param skin_token_len  — 皮肤token长度
* @param phone_stat      — 手机当前的状态，参见PHONE_STATE_LIST列表
* @return 返回为0表示正确，返回-1表示设置失败
*/
IPT_IMPORT PLINT32 ipt_set_skin_token(s_iptcore* _iptcore, char* skin_token,
                                      PLUINT16 skin_token_len,
                                      PLUINT16 phone_state);

/**给iOS客户端提供设置精细化模型文件名的接口，并且加载模型
 * @param _iptcore  — 内核词库数据句柄
 * @param filename  - 客户端指定的ltp.bin路径
 * @return -1失败，0成功
 */
IPT_IMPORT PLINT32 ipt_set_ltp_file(s_iptcore* _iptcore, const char* filename);

/**导出上次上传至今的误触情况
* @param _iptcore        — 内核句柄
* @param out_json        — 用来保存返回json的指针
* @param out_json_len    — 最大返回的json长度
* @return 返回值表示实际返回的json长度，返回-1表示获取失败
*/
IPT_IMPORT PLINT32 ipt_usr_touch_export_fresh_data(s_iptcore* _iptcore,
        PLUINT16* out_json, PLUINT16 out_json_len);

/**导出这次commit的误触情况用于trace
* @param _iptcore        — 内核会话句柄
* @param out_buf         — 用来保存返回结果的指针
* @param out_max_len     — 最大返回的长度
* @return 返回值表示实际返回的json长度，返回-1表示获取失败
*/
IPT_IMPORT PLINT32 ipt_export_mis_key_for_trace(s_session* _session,
        PLUINT16* out_buf,
        PLUINT16 out_max_len);
#endif

/**导出用户本地统计的误触信息，信息保存的数据为从上次上传之后统计的数据
* @param _iptcore        — 内核句柄
* @param out_json        — 用来保存返回json的指针
* @param out_json_len    — 最大返回的json长度
* @return 返回值表示实际返回的json长度，返回-1表示获取失败
*         如果返回成功则会将本地统计的数据清空，返回-1则本地数据不会被清空，返回-1可尝试将out_json的分配空间加大
*/
IPT_IMPORT PLINT32 ipt_usr_touch_export_stats_data(s_iptcore* _iptcore,
        PLUINT16* out_json, PLUINT16 out_json_len);

/**设置符号面板的锁定开关
 * @param _session — 内核会话句柄
 * @param idx — 待设置的列表项
 * @param idx — 是否锁定
 * @return 返回值为0表示设置成果, 返回值为负时表示出错
 */
IPT_IMPORT PLUINT32 ipt_sym_set_lock_status(s_session* _session, PLUINT32 idx,
        PLBYTE is_hold);

/**重新载入五笔词库(用户86、98等编码方案的切换)
 * @param _iptocre — 内核句柄
 * @return 返回值为0表示重载成功
 */
IPT_IMPORT PLUINT32 ipt_wubi_reload(s_iptcore* _iptcore);

/**重新载入仓颉词库(仓颉、速成词库的切换)
 * @param _iptocre — 内核句柄
 * @return 返回值为0表示重载成功
 */
IPT_IMPORT PLUINT32 ipt_cangjie_reload(s_iptcore* _iptcore);

/**获取当前是否通过mmap的方式载入词库
 * @return 返回值转为二进制后，最低位表示dnn的载入情况，倒数第二位表示gram的载入情况，倒数第三位表示dic_cizu的载入情况
 */
IPT_IMPORT PLINT32 ipt_get_is_mmap_load(s_iptcore* _iptcore);

/**根据输入码code，能匹配到一句话sen的几个字
 * @param _session — 内核会话句柄
 * @param sen — 待匹配的unicode码
 * @param code — 待调频词的拼音
 * @return 返回匹配到的字数
 */
IPT_IMPORT PLUINT32 ipt_get_sentence_match_len(s_session* _session, const PLUINT16* sen, const char* code);

/**根据剪切板的内容，判断是否需要保存
* @param _session 
* @param unis
* @param uni_len
* @return 返回是否需要存储剪切板的内容,0:不需要,1:需要
*/
IPT_IMPORT PLUINT32 ipt_check_clip(s_session* _session, const PLUINT16* unis, PLUINT32 uni_len);

#ifdef IPT_APP_MAP
/**场景映射表导入
 * @param _iptcore — 内核词库数据句柄
 * @param filename — 待导入文件的路径名
 * @return 返回值为非负整数, 表示导入成功; 返回值<0, 表示导入出错
 */
IPT_IMPORT PLINT32 ipt_import_app_map(s_iptcore* _iptcore,
                                      const char* filename);

/**场景映射表导出
 * @param _iptcore — 内核词库数据句柄
 * @param filename — 待导出文件的路径名
 * @return 返回值为非负整数, 表示导出成功; 返回值<0, 表示导出出错
 */
IPT_IMPORT PLINT32 ipt_export_app_map(s_iptcore* _iptcore,
                                      const char* filename);

/**输入法查询
 * @param _session  —  内核会话句柄
 * @param input  —  待查询的字符串
 * @param find_type  —  查询类型（具体内容请参照FIND_TYPE类型）
 * @param pos  —  对应的list选项值(用于筛选查询结果), 0代表返回所有list的结果
 * @param app_name  —  对应包名
 * @param attribute - 输入框属性
 * @return 0代表查找成功; 返回值<0代表查找失败
 */
IPT_IMPORT PLINT32 ipt_query_find_app(s_session* _session,
                                      char* input,
                                      PLINT32 find_type,
                                      PLINT32 pos,
                                      char* app_name,
                                      PLUINT32 attribute);

/**输入法输入一个点查询
 * @param _session  —  内核会话句柄
 * @param find_type  —  查询类型（具体内容请参照FIND_TYPE类型）
 * @param pos  —  增加一个点的位置
 * @param in_data — 增加一个点的地址
 * @param input_case — 输入方式，（大写或上划等）
 * @param input  —  待查询的字符
 * @param app_name  —  对应包名
 * @param attribute - 输入框属性
 * @return 0代表查找成功; 返回值<0代表查找失败
 */

IPT_IMPORT PLINT32 ipt_kp_append_point_app(s_session* _session,
        PLINT32 find_type,
        PLUINT32 pos,
        s_Point_v2* in_data,
        PLUINT32 input_case,
        char input,
        char* app_name,
        PLUINT32 attribute);

/**输入法删除一个点查询
 * @param _session  —  内核会话句柄
 * @param find_type  —  查询类型（具体内容请参照FIND_TYPE类型）
 * @param pos  —  删除一个点的位置
 * @param num — 删除点的个数（0表示不删除重新做一次查询）
 * @param app_name  —  对应包名
 * @param attribute - 输入框属性
 * @return 0代表查找成功; 返回值<0代表查找失败
 */
IPT_IMPORT PLINT32 ipt_kp_delete_point_app(s_session* _session,
        PLINT32 find_type,
        PLUINT32 pos,
        PLUINT32 num,
        char* app_name,
        PLUINT32 attribute);

/**获取场景映射表版本号
 * @param _iptcore — 内核词库数据句柄
 * @return 返回值为0表示文件不存在，其他正整数为版本号
 */
IPT_IMPORT PLUINT32 ipt_app_get_map_version(s_iptcore* _iptcore);

/**获取当前场景需要启用哪些场景词库
 * @param _session  —  内核会话句柄
 * @param app_name  —  对应包名
 * @param attribute - 输入框属性
 * @return 返回值为对应细胞词个数
 */
IPT_IMPORT PLUINT32 ipt_app_get_cell_id(s_session* _session,
                                        char* app_name,
                                        PLUINT32 attribute,
                                        PLUINT32* cell_id);
/**获取当前场景需要额外安装哪些场景词库
 * @param _session  —  内核会话句柄
 * @param app_name  —  对应包名
 * @param attribute - 输入框属性
 * @return 返回值为对应细胞词个数
 */
IPT_IMPORT PLUINT32 ipt_app_get_lack_cell_id(s_session* _session,
        char* app_name,
        PLUINT32 attribute,
        PLUINT32* cell_id);

/**语音整句纠错
* @param _session — 内核会话句柄
* @param _ori_word — 原语句, 以'\0'结尾
* @param _ret_word — 改后语句
* @param _correct_words_cnt 纠错的单词个数(>=0)
* @return -1:表示纠错失败 0:纠错正常进行
*/
IPT_IMPORT PLINT32 ipt_usr_voice_correct(s_session* _session,
        PLUINT16* _ori_sent, PLUINT32 _ori_sent_len,
        PLUINT16* _ret_sent, PLUINT32* _correct_words_cnt = 0);
/**语音整句纠错的词典数据更新
 * @param _session — 内核会话句柄
 * @param _ori_word — 原语句, 以'\0'结尾
 * @return
 */
IPT_IMPORT PLINT32 ipt_usr_voice_update_user_dict(s_session* _session,
        PLUINT16* _ori_sent, PLUINT32 _ori_sent_len,
        s_voice_correct_user_act* acts, PLUINT32 act_len);

#endif

#ifdef IPT_NNLM
/**获取nnlm的version
* @param _iptcore — 内核词库数据句柄
* @return 当前nnlm的version,获取不到则返回-1
*/
IPT_IMPORT PLINT32 ipt_get_nnlm_version(s_iptcore* _iptcore);
#endif

#ifdef IPT_THP
/**获取thp的version
* @param _iptcore — 内核词库数据句柄
* @return 当前nnlm的version,获取不到则返回-1
*/
IPT_IMPORT PLINT32 ipt_get_thp_version(s_iptcore* _iptcore);
#endif

#if (defined IPT_PLATFORM_WIN32) || (defined IPT_PLATFORM_IPHONE) || (defined IPT_PLATFORM_LINUX)
#ifdef __cplusplus
}
#endif
#endif
#endif /* PUB_IPTCORE_H_ */

/****************************Android5.5新增功能******************************
* 1.#256 五笔输入法逐渐提示
* 2.#271 五笔输入法支持个性短语
* 3.#273 笔画输入支持纠错
* 4.#305 场景化功能
* 5.#273 笔画纠错功能
* 6.#308 支持混输格式流行词
*/

/****************************Android5.6新增功能******************************
* 1.#323 支持多种场景类型
* 2.#306 多颜文字按概率联想展示
* 3.#303 联想候选支持图片及URL
* 4.#312 云输入优化——触发策略调整
* 5.#324 输入码部分匹配时出词策略
* 6.#209 纠错状态input编辑优化（这部分比较客户端有对应改动，请留意, 新增input flag----INPUT_FLAG_INPUT_EDIT）
* 7.#299 重构云输入协议,云手写
* 8.#336 增加场景化词语标记（EX_CAND_FLAG_CONTEXT）
* 9.#340 按键纠错策略优化
* 10.#337 个性短语查询、修改功能
    接口更改如下，获取个性短语分组和个性短语本身的数量时，可以指定查询的内容
    IPT_IMPORT PLINT32 ipt_phrase_item_count(s_session* _session, PLUINT32 group_id,PLBYTE* code_name, PLUINT32 code_len);
    IPT_IMPORT PLINT32 ipt_phrase_group_count(s_session* _session, PLUINT16* group_name, PLUINT32 group_name_len);

*/
/****************************Android5.7新增功能******************************
* 1.#343 云输入控制添加白名单
* 2.#344 云输入返回支持服务
* 3.#345 判断输入框是否为云输入服务强推框
*
*新增数据相关
*新增字段s_cloud_setting->is_edit_filt//云输入服务是否在该输入框中强推荐
*新增枚举CLOUD_OUTPUT_ITEM_TYPE->CLOUD_OUTPUT_ITEM_TYPE_SERVICE//代表云输入返回的彩蛋为服务
*即s_cloud_output_item->item_type == CLOUD_OUTPUT_ITEM_TYPE_SERVICE时（s_cloud_output_service*）s_cloud_output_item->item_buf
*新增结构s_cloud_output_service//用于存储云输入返回的服务
*
*
*修改数据相关
*修改字段s_phone_info->name//系统|手机型号|其他信息->平台号|手机型号|其他信息 其中只有平台号必填
*
*
*新增接口相关
*ipt_cell_get_cloud_white_ver//获取云输入白名单版本
*
*
*修改接口相关
*ipt_cloud_output//新增参数white_ver，用于云输入返回时得到白名单的版本号
*
*/

/*****************************Android5.8新增功能***********************************
* 1.#354 简拼输入结果支持二元整句内容
*/

/*****************************Android6.0新增功能***********************************
* 1.#352 联想结果支持多条颜文字内容显示
* 2.#356 细胞词库、直达号词条计数规则
* 3.#363 颜文字联想关系优化（内核保存对应关系）
* 4.#369 颜文字联想支持自造功能
* 5.#000 云输入
* 6.#373 双拼下开启模糊音筛选时也应是模糊音(其实是修复一个遗留bug）
*
*新增接口相关
*ipt_adjust_emoji_relation//表情关系调频,大于8个字符的将不会调频，只支持中文
*ipt_cloud_service//获取云输入返回
*ipt_query_get_ex_flag//获取候选词扩展类型,目前只有白名单
*
*新增数据相关
*s_cloud_output_service//云输入返回数据，该ipt_cloud_output接口暂时弃用
*
*新增数据相关
*s_phone_info->city//城市
*s_phone_info->channel//渠道号
*
*/

/*****************************Android6.5新增功能***********************************
 * 1.#368 联想功能支持模糊/精确2种模式
 * 2.#371 支持繁体输入联想关系自造
 * 3.#000 DNN手写
 * 4.#355 整句模块支持词类模型 （控制宏：IPT_CONFIG_NPOS_OPEN）
 * 5.#377 内核增加接口ipt_util_get_gram_cate_ver，返回三维词库版本信息
 *
 *
 * 新增接口相关
 * IPT_IMPORT PLINT32 ipt_util_get_gram_cate_ver(s_iptcore* _iptcore);返回适配的gram.bin的类号，用于向服务端请求更新
 *
 *新增数据文件 ch_fj_file, 用于繁体数据转为简体
 */


/*****************************Android7.0新增功能***********************************
 * 1.#368 联想功能支持模糊/精确2种模式，且支持旧版本数据转为新版本(本版本客户端才使用)
 * 2.#371 支持繁体输入联想关系自造(本版本客户端才使用)
 * 3.#000 支持自造词添加时加上时间戳信息
 * 4.#360 符号联想策略优化
 * 5.#385 三维词库联想结果支持黑名单过滤
 * 6.#381 二次元emoji转换功能
 * 7.#387 纠错优化-无精确结果时给出更多候选词
 * 8.#404 上屏词语彩蛋功能
 * 9.#405 emoji转换文字部分转换为火星文
 * 10.#407 【盲人输入】候选词解释数据整理
 *

*新增接口相关
*ipt_adjust_sylian_relation //自造符号联想关系
*ipt_keyword_emoticon_cell_install  //6.5->7.0版本导入旧版颜文字数据
*ipt_keyword_find_nijigen  //查找二次元内容
*ipt_keyword_find_egg  //获取彩蛋联想
*ipt_query_get_cand_context //获取盲人输入法解释数据

*新增数据相关
*ch_fj_file, 用于繁体数据转为简体(本版本客户端才使用)
*ot_huoxingwen_file，火星文文件
*ot_cand_con_file; //盲人辅助输入文件
*/

/*****************************Android7.1新增功能***********************************
 * 1.#395 联想结果应根据之前多次上屏内容进行统计计算
 * 2.#367 个性化功能
 * 3.#415 半匹配结果展示策略优化
 * 4.#438 英文面板输入英文，不调用三维词库联想数据
 * 5.#452 半匹配结果中纠错结果调整
 * 6.#453 二元整句插入候选词策略调整
 * 7.#454 简拼输入过程，除自造外，同时保存用户“输入码-上屏词”信息，并参与候选排序
 * 8.#462 邮箱后缀联想功能

 *新增接口相关
 *ipt_query_get_email_suffix  //获取邮箱联想
 *ipt_vkword_import  //VK数据导入
 *ipt_vkword_export  //VK数据导出

 *新增数据相关
 *email.txt  //导入邮箱联想数据
 *ot_vkword.bin  //保存用户“输入码-上屏词”信息
 */

/*****************************Android7.2新增功能***********************************
* 1.#456 系统词库增加标记，不雅词汇（降频）不参与整句输入
* 2.#457 自动造词及单字结果支持恢复默认词频
* 3.#448 系统词库增加标记，标示不参与整句三维词库数据
* 4.#474 ACS搜索模块
* 4.#482 内核提供三维词库安装更新接口
*新增接口相关
* ipt_search_make_cell_file 制作搜索关键字文件
* ipt_search_install 安装搜索关键词文件
* ipt_search_get_version 获取搜索关键字版本
* ipt_search_find  查询ACS搜索词结果
* ipt_gram_install  安装三维词库
*新增文件相关
*ot_search.bin文件  //保存ACS搜索关键词模块
*/

/*****************************Android7.3新增功能***********************************
* 1.[513][增加更多的模糊音]
* 2.[516][【自造】单个阿拉伯数字不进行自造]
* 3.[518][内核增加接口获取当前候选结果属性标记及光标前内容]
* 4.[517][【双拼】单个字母的韵母，双拼模式下应可匹配到自己]
* 5.[511] 语音识别通讯录优化
* 6.[520]【逐词&联想】三维词库中的词类关系不参与逐词、联想计算
* 7.[512]【纠错】根据输入速度对纠错策略进行优化(控制宏：IPT_FEATURE_AUTOFIX_GRADE)
* 新增接口列表
* ipt_set_str_before_cursor 把光标前字符串传入内核
* ipt_cand_get_flag 获取埋点标记
* ipt_contact_voice_find  非通讯录场景中查找联系人中相同读音的词
* ipt_contact_voice_find_addressbook  通讯录场景中查找联系人中相同读音的词
* s_ipt_mohu结构体修改
*/

/*****************************Android7.4新增功能***********************************
* 1.#1561 输入法自造调频策略优化
* 2.#0 地理位置词库推送
* 3.#1566 语音支持表情联想功能
* 4.#1571 语音支持彩蛋联想功能
* 5.#1557 二元整句插入候选显示策略优化
* 新增接口列表
* ipt_cell_set_loc_type 设置细胞词库类型
* ipt_cell_set_install_time 设置细胞词库安装时间
* s_cellinfo结构体修改
* ipt_keyword_find_voice_lian  找语音符号、表情联想
* ipt_keyword_find_voice_egg   找语音彩蛋
*/

/*****************************Android7.5新增功能***********************************
* 1.#1574 简拼及纠错权重优化
* 2.#1580 多个云结果缓存及展示策略优化
* 3.#1545 多音字注音优化——支持相同音节不同声调
* 4.#1564 识别用户单/双手输入模式
* 5.#1591 智能回复
* 6.#1593 支持根据光标前内容给出联想及逐词结果
* 7.#1596 输入预测结果增加属性标签 BURY_POINT_CAND_FLAFG_CURSOR_LIAN ipt_cand_get_flag获取
* 8.#1598 移动光标预测联想不展示emoji和颜文字
* 9.#1599 智能预测功能，不查询自造词及KV数据
* 新增接口列表
* ipt_get_dan_shuang_hand     单双手模式的判断
* ipt_cloud_cache_clean     清除云缓存信息(可以在关闭云预测开关时调用，可以清除已有的预测词）
* ipt_get_auto_reply_ans      智能问答调用接口
* ipt_get_auto_reply_ver      智能问答库版本号的获取接口
* ipt_auto_reply_install      智能问答库的安装接口
* ipt_autoreply_inf_extract   本地信息提取接口
* 新增数据文件 hz_tone_file
* 新增数据文件 autoreply.bin
*/
/*****************************Android7.6新增功能***********************************
* 1.#0 九键支持双音节映射纠错
* 2.1612 模糊音功能优化
* 3.1618 内核支持查询自造词数量
* 4.1615 支持用户词备份恢复
* 5.1620 iOS自造词损坏用户KV数据处理
* 6.1622 内核数据修复log文件记录信息采用追加方式
* 7.1619 自动清理不常用自造词功能优化
* 8.1621 用户自造数据本地备份功能
* 新增接口列表
* ipt_usrword_backup  备份用户词
* ipt_usrword_recover 恢复用户词(客户端暂时不用)
* ipt_usword_get_cnword_count 获取所有中文自造词个数
* ipt_usrword_check  覆盖安装时检查用户词的一致性
* 新增数据文件 us_bak_file
*/
/*****************************Android7.7新增功能***********************************
 * 1.1628 【云输入】已缓存状态下云输入逻辑优化
 * 2.1629 【模糊音】模糊音结果降权策略优化调整
 */
/*****************************Android7.8新增功能***********************************
 *****************************Android8.0新增功能***********************************
 * 1.1636 内核词频策略调优
 * 2.1640 取消简拼和预测命中不发起云输入请求限制策略
 * 3.1642 半匹配结果分词筛选后，应具备逐词功能
 * 4.1644 用户输入筛选过程触发云输入请求
 * 5.1648 云结果已缓存情况下本地展示策略优化
 * 新增接口：
 * ipt_get_is_mmap_load 获取是否通过mmap方式载入词库
 * ipt_get_touched_key 根据坐标获取当前按键接口
 * ipt_clean_dynamic_rects 清除动态热区接口
 * ipt_set_skin_token 设置当前皮肤token的接口
 * ipt_usr_touch_export_fresh_data 导出上次上传以来的误触情况
 * ipt_export_mis_key_for_trace 导出这次Commit的误触情况用于trace
 * 新增数据文件
 * ltp_dict_file ; //对应ltp.bin 动态热区文件
 * usr_touch_file; //用户误触个性化文件
 * 貌似7.8就是8.0(确实是)
 */
/*****************************Android8.2新增功能***********************************
 * 1.1646 五笔输入候选容器优化
 * 2.1649 半匹配预测功能优化——考虑输入长度、纠错的影响
 * 3.1650 纠错词语与精确整句排序策略优化
 * 4.1651 联想结果上屏策略优化
 * 5.1654 自造词衰减策略优化（逐词策略优化）
 * 6.增加ipt_core_reload_discard接口，重新载入输入法内核，丢弃所有未保存修改
 * 7.1656 内核词库增加校验信息
 * 8.1658 用户词同步-本地导入导出策略优化
 * 9.1666 拼音支持查询扩展区汉字
 * 10.1667 场景化功能优化
 * 11.1689 连续输入策略优化
 * 12.1690 二字词发起云输入策略优化
 *
 * 新增接口列表
 * ipt_import_app_map  导入场景映射表
 * ipt_export_app_map  导出场景映射表
 * ipt_query_find_app  传包名和框属性的查询接口
 * ipt_kp_append_point_app  传包名和框属性的查询接口
 * ipt_kp_delete_point_app  传包名和框属性的查询接口
 * ipt_app_get_map_ver  获取app_map版本号
 # ipt_app_get_cell_id  获取当前操作框对应的场景词库id
 *
 */
/*****************************华为精简版新增功能(3.7.4.X)***********************************
 * 1.1708 增加默认联想候选
 * 2.1731 中英混排策略优化，中文完全匹配结果少的时候，放开英文结果
 * 3.1750 部分符号输入时，修改中文候选词为英文加符号
*/
/*****************************（AI输入法）新增功能***********************************
 * 1.1731 中英混排策略优化，中文完全匹配结果少的时候，放开英文结果
 * 2.1716 【云输入】云输入支持云联想功能
 * 3.1729 连续联想后续联想关闭表情和颜文字联想功能
 * 4.1710 优化云输入广告偶现无内容展示的问题
 * 5.0 用户自造词按照时间衰减策略优化（小孟针对评测做的优化）
 * 6.1750 部分符号输入时，修改中文候选词为英文加符号
 * 7.1796 查询通讯录名称
 */
/*****************************安卓8.3新增功能***********************************
 * 1.1731 中英混排策略优化，中文完全匹配结果少的时候，放开英文结果
 * 2.1716 【云输入】云输入支持云联想功能
 * 3.1729 连续联想后续联想关闭表情和颜文字联想功能
 * 4.1710 优化云输入广告偶现无内容展示的问题
 * 5.0 用户自造词按照时间衰减策略优化（小孟针对评测做的优化）
 * 6.1750 部分符号输入时，修改中文候选词为英文加符号
 * 7.1796 查询通讯录名称
 * 8.1792 中文26键上划S键候选邮箱
 * 9.1794 26键进入符号面板调整
 * 10.#359 增加自造词删除标记
 */

/*****************************8.5新增功能***********************************
 * 1.1822 纠错比例下调策略
 * 2.1793 自造关系逐词效果
 * 3.1818 检查有问题的细胞词库
 * 4.1799 关键信息提取
 *
 * 新增接口列表
 * ipt_cell_detect_malformed   检查有问题的细胞词库
 */
/*****************************8.6新增功能***********************************
 * 1.1845 逐词联想策略优化
 * 2.1851 个性化语音纠错
 * 3.1859 云输入整句预测
 * 4.1935 预测结果与输入码是否匹配判断
 * 新增接口列表
 * ipt_usr_voice_correct 个性化语音纠错接口
 * ipt_usr_voice_update_user_dict 个性化语音纠错的词典更新接口
 * ipt_find_cloud_zj 云输入整句预测联想查询接口
 * ipt_set_ltp_file iOS客户端设置ltp.bin的路径，并且加载模型
 * ipt_get_sentence_match_len 判断一个句子的前序与输入码是否能匹配起来
 * 新增数据文件
 * usr_voice_correct_file; //用户的语音纠错文件
 *
 */
/*****************************未知版本新增功能***********************************
 * 2.#357 利用词类关系生成合成词 （控制宏：IPT_CONFIG_WDCLS）
 * 4.#365 支持英文词组联想
 * 7.注音支持自造词
 * 8.#1716 云输入支持古诗词联想功能

 * 新增接口列表
 * ipt_import_zyword  导入注音自造词
 * ipt_export_zyword  导出注音自造词
 * ipt_cangjie_export 导出仓颉词库
 * ipt_cangjie_import 导入仓颉词库
 * ipt_get_auto_reply_ans      智能问答调用接口
 * ipt_get_auto_reply_ver      智能问答库版本号的获取接口
 * ipt_auto_reply_install      智能问答库的安装接口
 * ipt_auto_reply_uninstall    智能问答库的卸载接口
 * ipt_autoreply_inf_extract   本地信息提取接口
 * ipt_autoreply_intent_decode 本地意图识别接口
 * 新增数据文件
 * ot_cangjie_file; ///仓颉词库文件
 * ch_zy_hz_file; ///< 注音汉字文件
 * ch_zy_cz_file; ///< 注音词组文件
 * ch_zy_usr_file; //注音自造词文件
 */
