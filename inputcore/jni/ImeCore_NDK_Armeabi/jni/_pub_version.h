/*
 *_pub_version.h
 *
 *  Created on: 2015-9-18
 *      Author: <PERSON><PERSON> Ye
 */

/*67371059 = 67371008 + 51*/
#ifndef APP_MOBILE_BAIDUINPUT_COREBD__PUB__PUB_VERSION_H_
#define APP_MOBILE_BAIDUINPUT_COREBD__PUB__PUB_VERSION_H_

#define CORE_VERSION_DEF(_a, _b, _c, _d)                                       \
  (((_a) << 24) | ((_b) << 16) | ((_c) << 8) | (_d))
#ifdef IPT_CONFIG_DEBUG_MODE
#define CORE_VERSION CORE_VERSION_DEF(0, 4, 0, 52)
#else
#define CORE_VERSION CORE_VERSION_DEF(4, 4, 0, 52)
#endif


//注：
// 1.由于两个平台对某些文件起名方式不同，为了便于管理，已统一按照内核中使用的名字来命名
// 2.由于Android文件数据大小的限制，拿到文件后，需把cz2.bin这个文件切分为相应的文件块
// 3.usr2.bin（中文自造词）ue2.bin（英文自造词） keyword.bin（关键词数据）
//此处存储的是空的数据文件.
//对已经有这几个数据的用户不应该替换，只给新用户
// 4.此文件夹下还有一些各平台自己管理的数据，不要忘记添加
// 5.gram2.bin（二元关系） hzmdl.bin（）文件应从服务器上下载
//
//--------------------------------------Android----------------------------------------------------
// bh4.bin b1f9655f6708e9eca328dc6c54a87948    2015.9.15
// ch_cor2.bin 627b3ba0b4cbdb0d02ccb967d0716cf6    2015.9.15
// cz2.bin eca28e96fcd753ef45ba5d0a1eb1158c    2015.9.15
// en2.bin 71be1f11ab06baa119a60c16f5a210ff    2015.9.15
// ft2.bin 8fc186ffbc7b876ef3ea93d35138b8f5    2015.9.15
// hz_label.bin    68d9aa3b031a8620a3f839c0f2b8ed75    2015.9.15
// hz2.bin ebd8eb255ecc40b36288a3b7455cb892    2015.9.15
// keyword.bin 03302fc0131bc1df1ec35620d7ea9d11    2015.9.15
// phrase.bin  a0afd0bc5347f425b97f040fed946cf9    2015.9.15
// sym2.bin    bfcb1d78ac916fec35a71554d7877ac5    2015.9.15
// sylian.bin  8ed6613ed47990da0230268236faeab4    2015.9.15
// ue2.bin c47d62bb44a2eec6c8c6dac19fbf6f5b    2015.9.15
// usr2.bin    dfcf591c31167f1a17490ffde45eb46a    2015.9.15
// wb2.bin:    3b67bf2d20c1934ab5dba546575ccb56    2015.9.15
// wb298.bin:  76bada960b76a4931fbcbb37880037b2    2015.9.15
// xhy.bin f604ffc8725452a183ebcc8d3ca4fffa    2015.9.15
// 0.bcd   34ffb5d644e5d406b255be5eb333cca6    2015.9.15
// 1.bcd   6e7029a469387cf29d2a7deab0463216    2015.9.15
// 2.bcd   77c8bfc5367e060b8722632b7eba14d7    2015.9.15
// emoji.kwd   6ce713c91a8f5a7d4905d23c6be80753    2015.9.15
// emojiextend.kwd f7b159d54fdaa4c0b3b4f7dd17b367e3    2015.9.15
// fast_input.kwd  7233d409069a872ee875b7aa60c58039    2015.9.15
// media.kwd   7d755db6ac8196703895979b5b9e37e8    2015.9.15
// zdh.kwd 8451bca624d39137c761872c1e7a0f7c    2015.9.15
// phrase_plus.ini 2c4b981462b6acd1381d735dd7e3084e    2015.9.15
//
//--------------------------------------
//-iOS------------------------------------------------------ -
// bh4.bin:
// ch_cor2.bin:
// cz2.bin:
// en2.bin:
// ft2.bin
// gram2.bin:
// hz2.bin
// hzmdl.bin:
// phrase.bin:
// sym.bin:
// syml.bin:
// wb(86)2.bin:
// wb(98)2.bin:
//*/

#endif
