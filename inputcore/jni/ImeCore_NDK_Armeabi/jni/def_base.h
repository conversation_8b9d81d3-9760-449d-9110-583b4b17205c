/*
 * def_base.h
 *
 *  Created on: 2012-12-27
 *      Author: men<PERSON><PERSON><PERSON>
 *      函数名编号:
 */

#ifndef DEF_BASE_H_
#define DEF_BASE_H_

#ifndef  IPT_BASIC_TYPE_DEF
#define  IPT_BASIC_TYPE_DEF
typedef unsigned int PLUINT32;
typedef unsigned short PLUINT16;
typedef unsigned char PLBYTE;
typedef unsigned char PLUINT8;
typedef signed int PLINT32;
typedef signed short PLINT16;
typedef signed char PLINT8;
typedef long long PLINT64;
typedef const char PLFNAME;
typedef float PLREAL32;
typedef double PLREAL64;
#define PLTRUE 1
#define PLFALSE 0
#define PLNULL 0
#endif

#ifndef IPT_IMPORT
#define IPT_IMPORT
#endif

#ifndef IPT_EXPORT
#define IPT_EXPORT
#endif

#define IPT_FEATURE_CH  ///<中文模块基础开关
#define IPT_FEATURE_CH_CZ   ///<中文词组开关
#define IPT_FEATURE_CH_SENTENCE  ///<整句输入开关
#define IPT_FEATURE_CH_BH  ///<笔画开关
#define IPT_FEATURE_CH_WB ///<五笔输入开关
#define IPT_FEATURE_CH_FT ///<繁简切换功能输入开关
#define IPT_FEATURE_CH_US ///<中文自造词开关
#define IPT_FEATURE_CH_CELL ///<中文细胞词库
///////////////////////////////////////////////////////
#define IPT_FEATURE_EN  ///< 英文模块基础开关
#define IPT_FEATURE_EN_US  ///<英文自造词开关
///////////////////////////////////////////////////////
#define IPT_FEATURE_PHRASE  ///<个性短语开关
#define IPT_FEATURE_SYMBOL  ///<符号查询开关
#define IPT_FEATURE_CONTACT  ///<联系人查找开关
#define IPT_FEATURE_OLDDEF   ///<1.x版DEF查询开关
#define IPT_FEATURE_OLDCH3 ///<1.x版CH3文件导入接口
///////////////////////////////////////////////////////
#define IPT_FEATURE_USE_ITN_LIBC ///<使用默认的LIB_C
#define IPT_FEATURE_OLD_EXPORT /**< 导出旧版数据 */
///////////////////////////////////////////////////////
//#define IPT_FEATURE_TTFFILTER ///<TTF字体过滤
//#define IPT_FEATURE_HOTLETTER ///<拼音字母高亮.
//#define IPT_FEATURE_LIBTOOL  ///<词库工具开关
//#define IPT_FEATURE_WDSPLIT  ///<分词工具开关
//#define IPT_FEATURE_FILTER_NEW ///<使用新的排序模式
//#define IPT_FEATURE_3HIT_ADJUST ///<三次连续调频,调到首位.
///////////////////////////////////////////////////////
#define IPT_FEATURE_HW					//手写功能开关
#define IPT_FEATURE_USE_UZW		        //使用手写联想词库文件
#define IPT_FEATURE_HW_USE_BEZIER_BUFF  //使用bezier点缓冲
///////////////////////////////////////////////////////
#define IPT_CONFIG_BLOCK_WRITE  16384  ///<分段写入大小
//#define IPT_CONFIG_CODE_OBFUSCATION  ///<代码混淆
//#define IPT_CONFIG_X64_CPU  ///
//#define IPT_CONFIG_USE_WIN32DLL
//#define IPT_CONFIG_DEBUG_MODE   ///<调试模式
//#define IPT_CONFIG_LOG_ALLOC_FREE ///
//#define IPT_LOG36_ZID_TERMID

//#define IPT_PLATFORM_SYMBIAN  ///<Nokia Symbian
//#define IPT_PLATFORM_ANDROID  ///<Google Android
//#define IPT_PLATFORM_MACOS  ///<Apple Mac Os
//#define IPT_PLATFORM_IPHONE ///< Apple iPhone
//#define IPT_PLATFORM_IPHONE_EMU ///<Apple iPhone Emulater
//#define IPT_PLATFORM_MTK  ///<MediaTek(MTK)
//#define IPT_PLATFORM_MTK_EMU ///<MediaTek(MTK Emulater)
//#define IPT_PLATFORM_WIN32 ///<Windows Desktop  Version
//#define IPT_PLATFORM_WINCE ///<Windows CE
//#define IPT_PLATFORM_WP8_WIN32 ///<Windows Phone 8 Win32
//#define IPT_PLATFORM_WP8_ARM ///<Windows Phone 8 ARM
//#define IPT_PLATFORM_BIGENDNISS ///<BigEndianess CPU Mode(Spreadtrum, Some MIPS Arch, etc.)


#ifdef IPT_CONFIG_X64_CPU
typedef unsigned long long PLUINT_32O64;
typedef signed long long PLINT_32O64;
#else
typedef unsigned long int PLUINT_32O64;
typedef signed long int PLINT_32O64;
#endif



////////////////////////////////////////////////////////////////////////////////////////////////////////
/////////各个平台宏适配(开始)///////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////////
#ifdef IPT_PLATFORM_WP8_WIN32   ////Windows Phone 8 Win32
////////////////////////////////////////////////////////
#undef IPT_IMPORT
#undef IPT_EXPORT
#define IPT_IMPORT __declspec(dllexport)
#define IPT_EXPORT __declspec(dllexport)
////////////////////////////////////////////////////////
#else
#ifdef IPT_PLATFORM_WP8_ARM   ////Windows Phone 8 ARM
////////////////////////////////////////////////////////
#undef IPT_IMPORT
#undef IPT_EXPORT
#define IPT_IMPORT __declspec(dllexport)
#define IPT_EXPORT __declspec(dllexport)
////////////////////////////////////////////////////////
#else
#ifdef IPT_PLATFORM_RVDS_ANDROID   ////RVDS_for_Android 编译
#define IPT_FEATURE_HOTLETTER ///<拼音字母高亮(默认只有android,下面开启)
#define IPT_FEATURE_TTFFILTER
#else/////////////////////////////////////////////////////////////////////////////
#ifdef IPT_PLATFORM_MACOS   ////Apple Mac Os
////////////////////////////////////////////////////////
#define IPT_FEATURE_3HIT_ADJUST
#else/////////////////////////////////////////////////////////////////////////////
#ifdef IPT_PLATFORM_IPHONE   ////Apple iPhone
////////////////////////////////////////////////////////
#else/////////////////////////////////////////////////////////////////////////////
#ifdef IPT_PLATFORM_IPHONE_EMU   ////Apple iPhone Emulater
////////////////////////////////////////////////////////
#else/////////////////////////////////////////////////////////////////////////////
#ifdef IPT_PLATFORM_ANDROID ////Google Android
#define IPT_FEATURE_HOTLETTER //拼音字母高亮(默认只有android,下面开启)
#define IPT_FEATURE_TTFFILTER
#else/////////////////////////////////////////////////////////////////////////////
#ifdef IPT_PLATFORM_WIN32   ////Windows Desktop  Version
#undef IPT_IMPORT
#undef IPT_EXPORT
#define IPT_IMPORT __declspec(dllexport)
#define IPT_EXPORT __declspec(dllexport)
#ifdef __WINDE__
#define IPT_FEATURE_LIBTOOL
#define IPT_FEATURE_WDSPLIT
#define IPT_CONFIG_NO_CODE_OBFU
#endif
#define IPT_FEATURE_TTFFILTER
#define IPT_FEATURE_HOTLETTER ///<拼音字母高亮(默认只有android,下面开启)
#define IPT_FEATURE_ENC_IDEA_V2_DEC
#else/////////////////////////////////////////////////////////////////////////////
#ifdef IPT_PLATFORM_SYMBIAN   ////Nokia Symbian
#undef IPT_IMPORT
#undef IPT_EXPORT
#undef IPT_FEATURE_USE_ITN_LIBC
#define IPT_IMPORT __declspec(dllexport)
#define IPT_EXPORT __declspec(dllexport)
#ifdef __WINSCW__
#define IPT_FEATURE_LIBTOOL
#define IPT_FEATURE_HOTLETTER
#define IPT_FEATURE_3HIT_ADJUST
#define IPT_FEATURE_TTFFILTER
#define IPT_FEATURE_WDSPLIT  ///<分词工具开关
#define IPT_CONFIG_NO_CODE_OBFU
#define IPT_CONFIG_DEBUG_MODE
#endif

#else/////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////
#endif  ////Nokia Symbian
#endif  ////Windows Desktop  Version
#endif  ////Google Android
#endif  ////Apple iPhone Emulater
#endif  ////Apple iPhone
#endif  ////Apple Mac Os
#endif  ////RVDS_for_Android
#endif  ////Windows Phone 8 ARM
#endif  ////Windows Phone 8 Win32
////////////////////////////////////////////////////////////////////////////////////////////////////////
/////////各个平台宏适配(结束)////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////////

#endif /* DEF_BASE_H_ */
