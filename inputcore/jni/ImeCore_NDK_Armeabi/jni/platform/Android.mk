LOCAL_PATH := $(call my-dir)

include $(CLEAR_VARS)
LOCAL_MODULE := ime_core
LOCAL_SRC_FILES := ../libiptcore-new.a
include $(PREBUILT_STATIC_LIBRARY)

include $(CLEAR_VARS)
LOCAL_MODULE := lib_neon
LOCAL_SRC_FILES := ../dnnneon.a
include $(PREBUILT_STATIC_LIBRARY)

include $(CLEAR_VARS)
LOCAL_MODULE_TAGS := optional

LOCAL_MODULE    := libinputcore-new-hw
LOCAL_SRC_FILES := 	jni_register.cpp \
					com_baidu_input_PlumCore.cpp \
					utility.cpp \
					md5.cpp

LOCAL_CFLAGS := -DIPT_PLATFORM_RVDS_ANDROID
LOCAL_CPPFLAGS := -fsigned-char

LOCAL_STATIC_LIBRARIES := ime_core lib_neon

LOCAL_LDLIBS    := -llog -ljnigraphics -landroid

LOCAL_C_INCLUDES := \
		    $(call include-path-for)

include $(BUILD_SHARED_LIBRARY)

