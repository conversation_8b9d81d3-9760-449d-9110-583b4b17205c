#include <stdlib.h>
#include <time.h>
#include <stdio.h>
#include <android/bitmap.h>

#include "utility.h"
#include "com_baidu_input_PlumCore.h"
#include "md5.h" //for check gram.bin

#include "../_pub_iptcore.h"

#ifdef OPEN_DEBUG_LOG
#include "../_pub_debug.h"
#endif

#ifdef SWITCH_ENCODE
#include "../_pub_encrypt.h"
#endif

s_iptcore* iptcore = NULL;
s_session* session = NULL;

// 用于内核字符串导出的buffer。目前用于上屏词的trace导出，因此直接初始化
#define STRING_BUFF_LEN 512
static PLUINT16 g_string_buff[STRING_BUFF_LEN];

#define CIKU_NUM 2
#define CIKU_TYPE_CZ 0
#define CIKU_TYPE_GRAM 1

typedef struct _cikuInfo
{
	int ciku_size;
	char *ciku_buffer;
} cikuInfo;

#ifdef OPEN_DEBUG_LOG
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlSetDebugLogPath
 * Signature: (Ljava/lang/String;I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlSetDebugLogPath
  (JNIEnv *env, jobject obj, jstring path, jint isAppend)
{
	jint ret = -1;
	int len;
	jboolean isCopy = JNI_FALSE;

	char fileName[64];
	memset(fileName, 0, 64);
	
	if (path && ((len = env->GetStringUTFLength(path)) > 0))
	{
		const char* szStr = env->GetStringUTFChars(path, &isCopy);
		memcpy(fileName, szStr, len);
		env->ReleaseStringUTFChars(path, szStr);
		
		ret = ipt_set_debug_log_path(fileName, isAppend);
	}

	return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCloseDebugLog
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlCloseDebugLog
  (JNIEnv *env, jobject obj)
{
	return ipt_close_debug_log();
}
#endif

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlInit
 * Signature: ([Ljava/lang/String;Landroid/content/pm/PackageInfo)Z
 */
JNIEXPORT jboolean JNICALL Java_com_baidu_input_PlumCore_PlInit
  (JNIEnv * env, jobject obj, jobjectArray names, jobjectArray assetNames, jobject packageInfo, jobject assetManager)
{
    #define LENGTH 44
    #define NAME_LENGTH 64
    jboolean isCopy = JNI_FALSE;
	jboolean ret = JNI_FALSE;

	char fileNames[LENGTH][NAME_LENGTH];
	memset(fileNames, 0, LENGTH * NAME_LENGTH);

#ifdef CRASH_VERSION
	int code = getCode(env,obj,packageInfo);
   	int decision = (code == number() ? 0 : 100); 
	if(decision != 0)
	{
		decision = (code == numbersandbox() ? 0 : 100);
	}
	checkState(decision);
#endif

	s_ipt_libfile* libFiles = (s_ipt_libfile*) malloc(sizeof(s_ipt_libfile));
	memset(libFiles, 0, sizeof(s_ipt_libfile));

	for (int i = 0; i < LENGTH; i++)
	{
		jstring str = (jstring) env->GetObjectArrayElement(names, i);
		int len = env->GetStringUTFLength(str);
		if (len == 0)
			continue;

		const char* szStr = env->GetStringUTFChars(str, &isCopy);
		strcpy(fileNames[i], szStr);
		LOGI("Java_com_baidu_input_PlumCore_PlInit fileName[%d]=%s",i,fileNames[i]);
		env->ReleaseStringUTFChars(str, szStr);
		env->DeleteLocalRef(str);
	}

	libFiles->ch_hz2_file	= fileNames[0];
	libFiles->ch_uz2_file 	= fileNames[1];
	// libFiles->ch_cell_file 	= fileNames[2];

	libFiles->ch_ft2_file 	= fileNames[3];
	libFiles->ch_bh4_file 	= fileNames[4];
	libFiles->ch_wb2_file 	= fileNames[11];

	libFiles->en_en2_file 	= fileNames[12];
	libFiles->en_ue2_file	= fileNames[5];
	libFiles->ot_sym2_file 	= fileNames[6];
	libFiles->ot_phrase_file= fileNames[8];
	
	libFiles->ot_contact_file = fileNames[9];
	libFiles->ot_def_file = fileNames[10];
	libFiles->ot_sylian_file = fileNames[7];
	libFiles->ot_filter_file = fileNames[15];

	libFiles->ch_uzw_file = fileNames[16];
	libFiles->ot_huoxingwen_file = fileNames[17];
	libFiles->ot_otherword_file = fileNames[18];
	libFiles->ot_keyword_file = fileNames[14];

	libFiles->ot_cangjie_file = fileNames[19];
	libFiles->ch_zy_hz_file = fileNames[20];
	libFiles->ch_zy_cz_file = fileNames[21];
	libFiles->ch_pro_cor2_file = fileNames[22];
	
	libFiles->wt_hz_file = fileNames[24];
	libFiles->ot_xiehouyu_file = fileNames[25];
	libFiles->wt_bs_file = fileNames[26];
	libFiles->hz_label_file = fileNames[27];
	libFiles->ot_idmap_file = fileNames[28];
	libFiles->cloud_keyword_file = fileNames[29];
	libFiles->ot_cand_con_file = fileNames[30];
	libFiles->ot_vkword_file = fileNames[31];
	libFiles->ot_search_file = fileNames[32];
	libFiles->hz_tone_file = fileNames[33];
	libFiles->auto_reply_file = fileNames[34];
	libFiles->ch_zy_usr_file = fileNames[35];
	libFiles->us_bak_file = fileNames[36];
	libFiles->ch_cz3_file = fileNames[37];

	libFiles->ltp_dict_file = fileNames[38];
	libFiles->usr_touch_file = fileNames[39];
	libFiles->app_map_file = fileNames[40];
	libFiles->nnlm_file = NULL;
	libFiles->prov_city_file = NULL;
	libFiles->usr_voice_correct_file = fileNames[42];
	libFiles->ot_special_file = fileNames[43];
#ifdef CRASH_VERSION
	libFiles->void_env = (void *)env;
	libFiles->void_pkg = (void *) packageInfo;
#endif

#ifdef IPT_MMAP
    // 读取cz3系统词库文件在asset下的路径，目前只支持cz3只读文件，固定读37位。若路径为空则不从asset下加载，需要保证在/data/data下可加载
	char czFileName[NAME_LENGTH];
    memset(czFileName, 0, NAME_LENGTH);
    int czFileNameLen = 0;
	if (assetNames != NULL)
    {
        jstring str = (jstring)env->GetObjectArrayElement(assetNames, 37);
        czFileNameLen = env->GetStringUTFLength(str);
        if (czFileNameLen > 0)
        {
            const char* szStr = env->GetStringUTFChars(str, &isCopy);
            strcpy(czFileName, szStr);
            LOGI("Java_com_baidu_input_PlumCore_PlInit czFileName=%s", czFileName);
            env->ReleaseStringUTFChars(str, szStr);
            env->DeleteLocalRef(str);
        }
    }
	if (czFileNameLen > 0)
	{
        off64_t outStart;
        off64_t outLength;
        int fd;
        int ret = read_fd_from_asset(env, assetManager, czFileName, &outStart, &outLength, &fd);
        if (ret >=0 && outStart >= 0 && outLength >= 0)
        {
            libFiles->mmap_cz3_start = outStart;
            libFiles->mmap_cz3_len = outLength;
            libFiles->mmap_cz3_fd = fd;
        }
	}
#endif

	LOGI("wangditest ot_idmap_file: session %s", libFiles->ot_idmap_file);
	iptcore = ipt_core_load(libFiles);
	free(libFiles);

	if (NULL != iptcore)
	{
		session = ipt_core_session_create(iptcore);
	}
	LOGI("wangditest init: session %x", session);

	return (NULL != session)? JNI_TRUE : JNI_FALSE;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlHandWritingRefresh
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlHandWritingRefresh
  (JNIEnv * env, jobject obj, jstring name)
{
	jint ret = -1;
	int len;
	jboolean isCopy = JNI_FALSE;
	s_ipt_libfile* libFiles = NULL;
	char fileName[NAME_LENGTH];
	memset(fileName, 0, NAME_LENGTH);
	
	if (name == NULL)
	{
		memcpy(fileName, "&unload", 7);
	}
	else
	{
		if ((len = env->GetStringUTFLength(name)) == 0)
		{
			return -1;
		}
		const char* szStr = env->GetStringUTFChars(name, &isCopy);
		memcpy(fileName, szStr, len);
		env->ReleaseStringUTFChars(name, szStr);
	}

	libFiles = (s_ipt_libfile*) malloc(sizeof(s_ipt_libfile));
	memset(libFiles, 0, sizeof(s_ipt_libfile));
	libFiles->wt_hz_file = fileName;
    
	if (NULL != session)
	{
		ret = (jint)ipt_core_refresh(session, libFiles);
	}
	free(libFiles);
	return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCzDownRefresh
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlCzDownRefresh
  (JNIEnv * env, jobject obj, jstring name)
{
	jint ret = -1;
	int len = 0;
	jboolean isCopy = JNI_FALSE;
	s_ipt_libfile* libFiles = NULL;
	char fileName[256];
	memset(fileName, 0, 256);

	if (name == NULL)
	{
		memcpy(fileName, "&unload", 7);
	}
	else
	{
		if ((len = env->GetStringUTFLength(name)) == 0)
		{
			return -1;
		}
		const char* szStr = env->GetStringUTFChars(name, &isCopy);
		memcpy(fileName, szStr, len);
		env->ReleaseStringUTFChars(name, szStr);
	}

	libFiles = (s_ipt_libfile*) malloc(sizeof(s_ipt_libfile));
	memset(libFiles, 0, sizeof(s_ipt_libfile));
	libFiles->ch_cz3down_file = fileName;
	if (NULL != session)
	{
		ret = (jint)ipt_core_refresh(session, libFiles);
	}
	free(libFiles);
	return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGramRefresh
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlGramRefresh
  (JNIEnv * env, jobject obj, jstring name)
{
    // add by 2019/3/25:内核的三维词库gram.bin文件已经被废弃，上层调用gramRefresh的地方还没删干净，此接口实际已经无用
	return 0;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlAutoReplyRefresh
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlAutoReplyRefresh
  (JNIEnv * env, jobject obj, jstring name)
{

	jint ret = -1;
	int len;
	jboolean isCopy = JNI_FALSE;
	s_ipt_libfile* libFiles = NULL;
	char fileName[NAME_LENGTH];
	memset(fileName, 0, NAME_LENGTH);
	
	if (name == NULL)
	{
		memcpy(fileName, "&unload", 7);
	}
	else
	{
		if ((len = env->GetStringUTFLength(name)) == 0)
		{
			return -1;
		}
		const char* szStr = env->GetStringUTFChars(name, &isCopy);
		memcpy(fileName, szStr, len);
		env->ReleaseStringUTFChars(name, szStr);
	}

	libFiles = (s_ipt_libfile*) malloc(sizeof(s_ipt_libfile));
	memset(libFiles, 0, sizeof(s_ipt_libfile));
	libFiles->auto_reply_file = fileName;
    
	if (NULL != session)
	{
		ret = (jint)ipt_core_refresh(session, libFiles);
	}
	free(libFiles);

	return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlClose
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_baidu_input_PlumCore_PlClose
  (JNIEnv *env, jobject obj)
{

    jboolean flag = JNI_FALSE;

	if(NULL != session)
	{
		ipt_core_session_close(session);
		if(NULL != iptcore)
		{
			LOGI("wangditest close: success");
			ipt_core_unload(iptcore);
			flag = JNI_TRUE;
		}
		else
		{
			flag = JNI_FALSE;
		}
	}
	else
	{
		flag = JNI_FALSE;
	}

    return flag;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlFlush
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_baidu_input_PlumCore_PlFlush
  (JNIEnv *env, jobject)
{
	ipt_core_save(iptcore);
}


/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetCoreVersion
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlGetCoreVersion
  (JNIEnv *env, jobject obj)
{
	int version = ipt_core_get_ver();
#ifndef CRASH_VERSION 
	version = 0 - version;
#endif
	return (jint) version;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlConfig
 * Signature: ([BI)Z
 */
JNIEXPORT jboolean JNICALL Java_com_baidu_input_PlumCore_PlConfig
(JNIEnv *env, jobject obj, jbyteArray configArray, jint configType)
{
    jboolean isCopy = JNI_FALSE;
    jint ret = -1;
    jbyte *config = env->GetByteArrayElements( configArray, &isCopy );

    ret = ipt_core_config( iptcore, (void*)config,configType );

    env->ReleaseByteArrayElements( configArray,config,0 );

    return (0 == ret)? JNI_TRUE : JNI_FALSE;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetSPMapSheng
 * Signature: (B[B)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlGetSPMapSheng
(JNIEnv *env, jobject obj, jbyte keyChar, jbyteArray shengArray)
{
	jboolean isCopy = JNI_FALSE;
	jint ret = -1;
	jbyte* shengList = env->GetByteArrayElements(shengArray, &isCopy);
    ret = ipt_util_getSpmap_sheng(iptcore,(char)keyChar, (char*)shengList);

	env->ReleaseByteArrayElements(shengArray, shengList, 0);

	return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetSPMapYun
 * Signature: (B[B)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlGetSPMapYun
(JNIEnv *env, jobject obj, jbyte keyChar, jbyteArray yunArray)
{
	jboolean isCopy = JNI_FALSE;
	jint ret = -1;
	jbyte* yunList = env->GetByteArrayElements(yunArray, &isCopy);
    ret = ipt_util_getSpmap_yun(iptcore,(char)keyChar, (char*)yunList);

	env->ReleaseByteArrayElements(yunArray, yunList, 0);

	return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlSetSP
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlSetSP
  (JNIEnv *env, jobject obj, jstring jfileName)
{
    jint ret = 0;
    jboolean isCopy = JNI_FALSE;
    const char* fileName = env->GetStringUTFChars(jfileName, &isCopy);
        
    s_ipt_shuangpin spconfig;
    memset(&spconfig,0,sizeof(s_ipt_shuangpin));
    ret = (jint)ipt_util_parseSp_Byfile(&spconfig,fileName);

    if(ret == 0)
	{
		ret = ipt_core_config(iptcore, &spconfig, CONFIGTYPE_SET_SHUANGPIN);
	}
    env->ReleaseStringUTFChars(jfileName, fileName);

    return ret;
}


/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlSetKeyMap
 * Signature: (ICI)V
 */
JNIEXPORT void JNICALL Java_com_baidu_input_PlumCore_PlSetKeyMap
  (JNIEnv *env, jobject obj, jint id, jchar character, jint level)
{
	ipt_keymap_addchar( session, (char)id, (char)character, level);
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlSetKeyMapAutofix
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_baidu_input_PlumCore_PlSetKeyMapAutofix
  (JNIEnv *env, jobject obj)
{
	if (session)
	{
		ipt_keymap_addmap_autofix(session);
	}
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCleanKeyMap
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_baidu_input_PlumCore_PlCleanKeyMap
  (JNIEnv *env, jobject)
{
	ipt_keymap_clean( session );
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlLoadDefaultKeyMap
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_baidu_input_PlumCore_PlLoadDefaultKeyMap
  (JNIEnv *env, jobject)
{
	ipt_keymap_load_default( session );
}


/*
 * Class:    com_baidu_input_PlumCore
 * Method:   PlSetEncase
 * Signature:([B)I
 */

JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlSetEncase
(JNIEnv *env, jobject obj, jbyteArray encaseArray)
{
	jboolean isCopy = JNI_FALSE;	
	jint ret = -1;

	if (NULL != encaseArray)
	{
		jbyte *encase = NULL;
		jint encaseLen = 0;
		encaseLen = env->GetArrayLength(encaseArray);
		if (encaseLen == ENCASE_LEN)
		{
			encase = env->GetByteArrayElements(encaseArray, &isCopy );
			ret = ipt_query_set_encase(session, (PLBYTE*)encase);
            env->ReleaseByteArrayElements(encaseArray, encase, JNI_ABORT);
		}
	}

	return ret;
}

#ifdef IPT_APP_MAP
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlFind
 * Signature: ([BIILjava/lang/String;I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlFind
  (JNIEnv *env, jobject obj, jbyteArray inputArray, jint pos, jint findType, jstring jappName, jint attribute)
{
	jboolean isCopy = JNI_FALSE;
	jbyte *input = env->GetByteArrayElements(inputArray, &isCopy);

	char* appName = (char*)env->GetStringUTFChars(jappName, &isCopy);

#ifdef TEST_CATCH_CRASH_SIGNAL
	int input_len = 0;
	while (input[input_len]) {
		input_len++;
	}
	if (input_len >= 8)
	{
		/** 寮哄埗宕╂簝 */
		int *p = 0;
		*p = 5;
	}
#endif

	ipt_query_clean(session);

    LOGI("PlFind input=%s, findType = %d, pos=%d, attribute=%d", input, findType, pos, attribute);

	jint ret = (jint) ipt_query_find_app(session, (char*) input, findType, pos, appName, attribute);

	env->ReleaseByteArrayElements(inputArray, input, JNI_ABORT);
	env->ReleaseStringUTFChars(jappName, appName);
	return ret;
}

#else
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlFind
 * Signature: ([BIIII)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlFind
  (JNIEnv *env, jobject obj, jbyteArray inputArray, jint pos, jint findType, jint context)
{
	jboolean isCopy = JNI_FALSE;
	jbyte *input = env->GetByteArrayElements(inputArray, &isCopy);

#ifdef TEST_CATCH_CRASH_SIGNAL
	int input_len = 0;
	while (input[input_len]) {
		input_len++;
	}
	if (input_len >= 8)
	{
		/** 寮哄埗宕╂簝 */
		int *p = 0;
		*p = 5;
	}
#endif

	ipt_query_clean(session);

    LOGI("PlFind input=%s, findType = %d, pos=%d, context=%d", input, findType, pos, context);

	jint ret = (jint) ipt_query_find(session, (char*) input, findType, pos, context);

	env->ReleaseByteArrayElements(inputArray, input, JNI_ABORT);
	return ret;
}

#endif

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlFindLian
 * Signature: ([CI)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlFindLian
  (JNIEnv *env, jobject obj, jcharArray zids_Or_unicode, jint len)//maybe can have no len parameter
{
	 jboolean isCopy = JNI_FALSE;
	 jchar *lianReference = env->GetCharArrayElements(zids_Or_unicode,&isCopy);//

	 ipt_query_clean(session);

	 jint ret = (jint)ipt_query_findlian(session, (PLUINT16*)lianReference, (PLINT32)len, 0);

	 env->ReleaseCharArrayElements(zids_Or_unicode,lianReference,JNI_ABORT);
	 return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlSymImportReorder
 * Signature: (II)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlSymImportReorder
  (JNIEnv *env, jobject obj, jint list_id, jint last_pad, jobjectArray jsyms, jint jlen)
{
     int length = jlen;
     if (length < 0)
     {
        length = 0;
     }
     PLUINT16** syms = new PLUINT16*[length];
     PLUINT16 arr[length][4];
     if (NULL != jsyms)
     {
        jint i,j;
        jarray array;
        int size = env->GetArrayLength(jsyms);

        for (i = 0; i < size && i<length ; i++)
        {
           array = (jarray) env->GetObjectArrayElement(jsyms, i);
           int len = env->GetArrayLength(array);

           jchar *data = env->GetCharArrayElements((jcharArray) array, 0);
           for (j=0; j <len && j<4; j++)
           {
               arr[i][j] = data[j];
           }
           syms[i] = arr[i];
           env->ReleaseCharArrayElements((jcharArray)array, data, 0 );
        }
     }
	 jint ret = -1;
     if (NULL != session && NULL != syms)
     {
        ret = ipt_query_find_sym(session, (PLINT32)list_id, (PLINT32)last_pad, syms, length);
     }
     return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlQueryCmd
 * Signature: (II)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlQueryCmd
  (JNIEnv *env, jobject obj, jint id, jint cmdType)
{
	return ipt_query_cmd(session, id, cmdType);
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlClean
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_baidu_input_PlumCore_PlClean
(JNIEnv *env, jobject obj)
{
	ipt_query_clean(session);
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCount
 * Signature: (I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlCount
  (JNIEnv *env, jobject obj, jint getType)
{
	return ipt_query_get_count(session, getType);
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetStr
 * Signature: (Lcom/baidu/input/pub/CoreString;II)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlGetStr
  (JNIEnv *env, jobject obj,jobject customObj,jint id,jint getType)
{
    PLUINT16 output[MAX_OUTPUT];
    memset(output, 0, 2*MAX_OUTPUT);
	jint ret = -1;

	if(getType == GETTYPE_USWORD)
	{
		ret = ipt_usword_getstr(session, output, (PLUINT32)id);
	}
    else
	{
    	ret = ipt_query_get_str(session, (PLUINT32)id, output,getType);
    }
	
    jstring str = convert_wchar_to_jstring(env, output);
    	
    jclass customCls = env->GetObjectClass(customObj);

    static jmethodID cs_set_method = env->GetMethodID(customCls, "set","(Ljava/lang/String;I)V");
    env->CallVoidMethod(customObj, cs_set_method, str, ret);

    env->DeleteLocalRef(customCls);
	env->DeleteLocalRef(str);
	return ret;

}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetStrRange
 * Signature: ([Lcom/baidu/input/pub/CoreString;III)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlGetStrRange
  (JNIEnv *env, jobject obj,jobjectArray customObjArray,jint startid, jint endid, jint getType)
{
    int count = env->GetArrayLength(customObjArray);

    if(count <= 0)
    	return -1;

    jobject customObj = env->GetObjectArrayElement(customObjArray, 0);
    jclass customCls = env->GetObjectClass(customObj);
    static jmethodID cs_set_method = env->GetMethodID(customCls, "set","(Ljava/lang/String;I)V");

    int id;
    jint ret = -1;
    for(id = startid; id <= endid; id++)
    {
        PLUINT16 output[MAX_OUTPUT];
        memset(output, 0, 2*MAX_OUTPUT);

        if(getType == GETTYPE_USWORD)
        {
        	ret = ipt_usword_getstr(session, output, (PLUINT32)id);
        }
        else
        {
            ret = ipt_query_get_str(session, (PLUINT32)id, output,getType);
        }

        jstring str = convert_wchar_to_jstring(env, output);

        jobject customObj = env->GetObjectArrayElement(customObjArray, id - startid);

        env->CallVoidMethod(customObj, cs_set_method, str, ret);

        env->DeleteLocalRef(str);

        if(-1 == ret)
        {
            break;
        }
    }

    env->DeleteLocalRef(customCls);

	return ret;

}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetExFlag
 * Signature: (I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlGetExFlag
  (JNIEnv *env, jobject obj, jint id)
{
	jint ret = -1;

	ret = ipt_query_get_ex_flag(session, (PLUINT32)id);

	return ret;

}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlMatchInfo
 * Signature: (I)[I
 */
JNIEXPORT jintArray JNICALL Java_com_baidu_input_PlumCore_PlMatchInfo//optimization
  (JNIEnv *env, jobject obj, jint id)
{
	PLUINT32 wLen = 0;
	PLUINT32 mLen = 0;
    PLUINT16 zids[MAX_CH_WORD];
	PLBYTE zmatchs[MAX_Z_MATCH_LEN];
    memset(zids,0,MAX_CH_WORD*sizeof(PLUINT16));
	memset(zmatchs,0,MAX_Z_MATCH_LEN*sizeof(PLBYTE));

    int ret = ipt_query_get_matchinfo(session, (PLUINT32)id, &wLen, &mLen, zids, zmatchs);

	LOGI("PlMatchInfo Len=%d:%d", (int)wLen, (int)mLen);

    jboolean isCopy = JNI_FALSE;
    jintArray result =  (jintArray)env->NewIntArray(2+wLen+wLen);
#ifdef SET_ARRAY_WITH_INTERFACE	
	setIntArrayElement(env, result, 0, (jint) wLen);
	setIntArrayElement(env, result, 1, (jint) mLen);
	for(PLUINT32 i = 0; i < wLen; i++)
    {
		setIntArrayElement(env, result, 2 + i, (jint) zids[i]);
		setIntArrayElement(env, result, 2 + wLen + i, (jint) zmatchs[i]);
    }
#else
    jint *element = env->GetIntArrayElements(result,&isCopy);

    element[0] = wLen;
    element[1] = mLen;

    for(PLUINT32 i = 0; i < wLen; i++)
    {
        element[2+i] = (jint) zids[i];
		element[2+wLen+i] = (jint) zmatchs[i];
		LOGI("PlMatchInfo array=%d:%d:%d", (int)i, (int)zids[i], (int)zmatchs[i]);
    }

    env->ReleaseIntArrayElements(result,element,JNI_ABORT);
#endif
    return result;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetShow
 * Signature: (I[B)Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_baidu_input_PlumCore_PlGetShow//optimization
  (JNIEnv *env, jobject obj, jint id, jbyteArray jshowInfo)
{
    PLUINT16 output[MAX_OUTPUT];
    jboolean isCopy = JNI_FALSE;
	int size = env->GetArrayLength(jshowInfo);
#ifdef SET_ARRAY_WITH_INTERFACE
	PLBYTE showInfo[MAX_SHOW_INFO];
	int ret = ipt_query_get_show(session, (PLUINT32)id, output, (PLBYTE*)showInfo);
	env->SetByteArrayRegion(jshowInfo, 0, size, (const jbyte*)showInfo);
#else
	jbyte * showInfo = NULL;
    if(size == MAX_SHOW_INFO)
	{
		showInfo = env->GetByteArrayElements(jshowInfo, &isCopy);
	}
    int ret = ipt_query_get_show(session, (PLUINT32)id, output, (PLBYTE*)showInfo);

	env->ReleaseByteArrayElements(jshowInfo,showInfo,JNI_ABORT);
#endif
    return convert_wchar_to_jstring(env,output);
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlAdjustCnWord
 * Signature: ([CII)Z
 */
JNIEXPORT jboolean JNICALL Java_com_baidu_input_PlumCore_PlAdjustCnWord//can have len parameter maybe
  (JNIEnv *env, jobject obj, jcharArray zidsArray, jint len, jint editType)
{
    jboolean isCopy = JNI_FALSE;
    jint ret = -1;

    if(len > MAX_CH_USER_WORD_LEN || len < MIN_CH_USER_WORD_LEN)
	{
    	return JNI_FALSE;
	}
    jchar *zids = env->GetCharArrayElements(zidsArray,&isCopy);

    switch(editType)
    {
    case ADJUST_TYPE:
    	ret = ipt_adjust_cnword(session, (PLUINT16*)zids, (PLUINT32)len);
    	break;
    case DELETE_TYPE:
    	ret = ipt_delete_cnword(session, (PLUINT16*)zids, (PLUINT32)len);
    	break;
    default:
    	break;
    }

    env->ReleaseCharArrayElements(zidsArray,zids,JNI_ABORT);

	ipt_core_save(iptcore);

    return (0 == ret)? JNI_TRUE : JNI_FALSE;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlAdjustCnWordbyUni

 * Signature: (Ljava/lang/String;Ljava/lang/String;I)Z
 */
JNIEXPORT jboolean JNICALL Java_com_baidu_input_PlumCore_PlAdjustCnWordbyUni
  (JNIEnv *env, jobject obj, jstring junicode, jstring jpinyin, jint editType)
{
    jboolean isCopy = JNI_FALSE;
    jint ret = -1;
    int size = env->GetStringLength(junicode);
    if (size > MAX_CH_USER_WORD_LEN || size < MIN_CH_USER_WORD_LEN)
	{
    	return JNI_FALSE;
	}
    const jchar *tmpUnicode = env->GetStringChars(junicode, &isCopy);
    PLUINT16 unicode[MAX_CH_USER_WORD_LEN + 1];
	memset(unicode, 0, (MAX_CH_USER_WORD_LEN + 1) * sizeof(PLUINT16));
	jchar_to_wchar(unicode, tmpUnicode, size);

    PLBYTE* pinyin = NULL;
    const char* tmpPinyin = env->GetStringUTFChars(jpinyin, &isCopy);

    if (env->GetStringLength(jpinyin) == 0)
	{
		pinyin = NULL;
	}
	else
	{
		pinyin = (PLBYTE*)tmpPinyin;
	}
	LOGI("PlAdjustCnWordbyUni pinyin=%d",pinyin);

    switch (editType)
    {
    case ADJUST_TYPE:
    	ret = ipt_adjust_cnword_byUni(session, unicode, pinyin);
    	break;
    case DELETE_TYPE:
    	ret = ipt_delete_cnword_byUni(session, unicode);
    	break;
    default:
    	break;
    }

	LOGI("PlAdjustCnWordbyUni after");

    env->ReleaseStringChars(junicode, tmpUnicode);
	env->ReleaseStringUTFChars(jpinyin, tmpPinyin);

	ipt_core_save(iptcore);

    return (0 == ret)? JNI_TRUE : JNI_FALSE;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlAdjustEnWord
 * Signature: (Ljava/lang/String;I)Z
 */
JNIEXPORT jboolean JNICALL Java_com_baidu_input_PlumCore_PlAdjustEnWord
  (JNIEnv *env, jobject obj, jstring jenword, jint editType)
{
    jboolean isCopy = JNI_FALSE;
    jint ret = -1;
    char* enword = (char*)env->GetStringUTFChars(jenword, &isCopy);

    switch(editType)
    {
    case ADJUST_TYPE:
    	ret = ipt_adjust_enword(session, enword);
    	break;
    case DELETE_TYPE:
    	ret = ipt_delete_enword(session, enword);
    	break;
    default:
    	break;
    }

    env->ReleaseStringUTFChars(jenword, enword);

	ipt_core_save(iptcore);

    return (0 == ret)? JNI_TRUE : JNI_FALSE;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlFindUsWord
 * Signature: (Ljava/lang/String;I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlFindUsWord
  (JNIEnv *env, jobject obj, jstring jsearchWord, jint type)
{
    jboolean isCopy = JNI_FALSE;
	jint ret = -1;
	jint size = env->GetStringLength(jsearchWord);
    	
  	PLUINT16 searchWord[size + 1];
	memset(searchWord, 0, (size + 1) * sizeof(PLUINT16));

	const jchar* tmpSearchWord = env->GetStringChars(jsearchWord, &isCopy);
	jchar_to_wchar(searchWord, tmpSearchWord, size);

	ret = ipt_usword_count(session, searchWord, type);

	env->ReleaseStringChars(jsearchWord,tmpSearchWord);

	return ret;

}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetCnWordCount
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlGetCnWordCount
  (JNIEnv *env, jobject obj)
{

	return ipt_usword_get_cnword_count(iptcore);

}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlImportWords
 * Signature: (Ljava/lang/String;I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlImportWords
  (JNIEnv *env, jobject obj, jstring jfileName, jint langType)
{
    jboolean isCopy = JNI_FALSE;
    jint ret = -1;
    const char* fileName = env->GetStringUTFChars(jfileName, &isCopy);

    switch(langType)
    {
    case CN_TYPE:
    	ret = ipt_import_cnword(iptcore, fileName);
    	break;
    case EN_TYPE:
    	ret = ipt_import_enword(iptcore, fileName);
    	break;
    default:
    	break;
    }

    env->ReleaseStringUTFChars(jfileName, fileName);

    return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlExportWords
 * Signature: (Ljava/lang/String;I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlExportWords
  (JNIEnv *env, jobject obj, jstring jfileName, jint langType)
{
	jboolean isCopy = JNI_FALSE;
	jint ret = -1;
	const char* fileName = env->GetStringUTFChars(jfileName, &isCopy);
    
	switch (langType) {
	case CN_TYPE:
		ret = ipt_export_cnword(iptcore, fileName);
		break;
	case EN_TYPE:
		ret = ipt_export_enword(iptcore, fileName);
		break;
	default:
		break;
	}

	env->ReleaseStringUTFChars(jfileName, fileName);

	return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlIdmapCellInstall
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlIdmapCellInstall
  (JNIEnv * env, jobject obj, jstring jfileName)
{
    jboolean isCopy = JNI_FALSE;
    const char* fileName = env->GetStringUTFChars(jfileName, &isCopy);
    jint idmapCellId = ipt_idmap_cell_install(iptcore, fileName, 0);

    env->ReleaseStringUTFChars(jfileName, fileName);
    return idmapCellId;
}

/*



 * Class:     com_baidu_input_PlumCore
 * Method:    PlIdmapCellInstallByBuff
 * Signature: ([B)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlIdmapCellInstallByBuff
  (JNIEnv * env, jobject obj, jbyteArray jbuff)
{
    jboolean isCopy = JNI_FALSE;
    jbyte * buff = env->GetByteArrayElements(jbuff, &isCopy);
    int len = env->GetArrayLength(jbuff);

    jint cellId = ipt_idmap_cell_install(iptcore, (const char*)buff, len);

    env->ReleaseByteArrayElements(jbuff, buff, JNI_ABORT);

    return cellId;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlIdmapCellUninstall
 * Signature: (I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlIdmapCellUninstall
  (JNIEnv *env, jobject obj, jint idmapCellId)
{
	return (jint)ipt_idmap_cell_uninstall(iptcore, (PLUINT32)idmapCellId);
}

/* Class:     com_baidu_input_PlumCore

 * Method:    PlIdmapCellGetinfoById
 * Signature: (Lcom/baidu/input/pub/IdmapCellInfo;I)V
 */
JNIEXPORT void JNICALL Java_com_baidu_input_PlumCore_PlIdmapCellGetinfoById
  (JNIEnv * env, jobject obj, jobject jidmapCellInfo, jint id)
{
	s_idmap_node* idmapCellInfo = ipt_idmap_map_id(iptcore, id);
    static jclass customCls = 0;
    static jmethodID set_str_method = 0;
    static jmethodID set_int_method = 0;
	static jmethodID set_byte_method = 0;
	
  	if(idmapCellInfo != NULL)
	{
		customCls = env->GetObjectClass(jidmapCellInfo);
		int type = idmapCellInfo->word_len & 0xf000000;
		int len = idmapCellInfo->word_len & 0x0ffffff;
		jstring data = NULL;
		jbyteArray datas = NULL;
		if(type == 0x1000000)
		{
			LOGI("PlIdmapCellGetinfoById sym: %d", len);
			data = env->NewString((jchar*)idmapCellInfo->data_buff, len / 2);
		}
		else
		{
			datas = (jbyteArray)env->NewByteArray(len);
			env->SetByteArrayRegion(datas, 0, len, (const jbyte*)idmapCellInfo->data_buff);
		}
		if(set_str_method == 0)
			set_str_method = env->GetMethodID(customCls, "set_str","(Ljava/lang/String;)V");
		if(set_int_method == 0)
			set_int_method = env->GetMethodID(customCls, "set_int","(III)V");
		if(set_byte_method == 0)
			set_byte_method = env->GetMethodID(customCls, "set_byte","([B)V");
		env->CallVoidMethod(jidmapCellInfo, set_str_method,data);
		env->CallVoidMethod(jidmapCellInfo, set_int_method,type,
												idmapCellInfo->cell_id,
												idmapCellInfo->flag);
		env->CallVoidMethod(jidmapCellInfo, set_byte_method, datas);
		env->DeleteLocalRef(data);
		env->DeleteLocalRef(datas);
		env->DeleteLocalRef(customCls);
		ipt_idmap_freez(idmapCellInfo);
	}
}


/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlIdmapExport
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlIdmapExport
  (JNIEnv * env, jobject obj, jstring jfileName)
{
	jint ret = -1;
	jboolean isCopy = JNI_FALSE;
    const char* fileName = env->GetStringUTFChars(jfileName, &isCopy);
    ret = ipt_idmap_export(iptcore, fileName);
    env->ReleaseStringUTFChars(jfileName, fileName);
    return ret;
}


//////////////////////////////////////////////////
////缁嗚優璇嶅簱绠＄悊鐩稿叧鎺ュ彛//////////////////////////////
//////////////////////////////////////////////////
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCellInstall
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlCellInstall
  (JNIEnv * env, jobject obj, jstring jfileName)
{
    jboolean isCopy = JNI_FALSE;
    const char* fileName = env->GetStringUTFChars(jfileName, &isCopy);
    jint cellId = ipt_cell_install(iptcore, fileName, 0);

    env->ReleaseStringUTFChars(jfileName, fileName);
    return cellId;
}

/*



 * Class:     com_baidu_input_PlumCore
 * Method:    PlCellInstallByBuff
 * Signature: ([B)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlCellInstallByBuff
  (JNIEnv * env, jobject obj, jbyteArray jbuff)
{
    jboolean isCopy = JNI_FALSE;
    jbyte * buff = env->GetByteArrayElements(jbuff, &isCopy);
    int len = env->GetArrayLength(jbuff);

    jint cellId = ipt_cell_install(iptcore, (const char*)buff, len);

    env->ReleaseByteArrayElements(jbuff, buff, JNI_ABORT);

    return cellId;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCellUninstall
 * Signature: (I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlCellUninstall
  (JNIEnv *env, jobject obj, jint cellId)
{
	return (jint)ipt_cell_uninstall(iptcore, (PLUINT32)cellId);
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCellEnable
 * Signature: (IZ)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlCellEnable
  (JNIEnv * env, jobject obj, jint cellId, jboolean isEnable)
{
	 return (jint)ipt_cell_enable(iptcore, (PLUINT32)cellId, (PLUINT32)isEnable);
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCellSetLocType
 * Signature: (II)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlCellSetLocType
  (JNIEnv * env, jobject obj, jint cellId, jint locType)
{
	 return (jint)ipt_cell_set_loc_type(iptcore, (PLUINT32)cellId, (PLUINT16)locType);
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCellSetInstallTime
 * Signature: (II)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlCellSetInstallTime
  (JNIEnv * env, jobject obj, jint cellId, jint installTime)
{
	 return (jint)ipt_cell_set_install_time(iptcore, (PLUINT32)cellId, (PLUINT32)installTime);
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCellCount
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlCellCount
  (JNIEnv *env, jobject)
{
	return ipt_cell_count(iptcore);
}


 /* Class:     com_baidu_input_PlumCore
 * Method:    PlCellGetinfo
 * Signature: ([Lcom/baidu/input/pub/CellInfo;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlCellGetinfo
  (JNIEnv * env, jobject _obj, jobjectArray jcellInfos)
{
	int count = env->GetArrayLength(jcellInfos);

    if(count < 0)
		return -1;

	s_cellinfo* cellInfos = (s_cellinfo*)malloc(sizeof(s_cellinfo) * count);
    memset(cellInfos,0,sizeof(s_cellinfo)*count);
    int index=0;

    static jmethodID set_str_method = 0;
    static jmethodID set_int_method = 0;

    for(index = 0; index < count; index++)
    {
        jobject element = env->GetObjectArrayElement(jcellInfos, index);
        jclass customCls = env->GetObjectClass(element);

        s_cellinfo* cellInfo = cellInfos+index;
        ipt_cell_info_byIndex(iptcore, cellInfo, index);
        if(cellInfo == NULL)
            break;

        jstring name = convert_wchar_to_jstring2(env, cellInfo->name_buf, cellInfo->name_len);
        jstring author = convert_wchar_to_jstring2(env, cellInfo->author_buf, cellInfo->author_len);
        jstring keyword = convert_wchar_to_jstring2(env, cellInfo->keyword_buf, cellInfo->keyword_len);
        jstring info = convert_wchar_to_jstring2(env, cellInfo->info_buf, cellInfo->info_len);

        if(set_str_method == 0)
            set_str_method = env->GetMethodID(customCls, "set_str","(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V");
        if(set_int_method == 0)
            set_int_method = env->GetMethodID(customCls, "set_int","(IIIIIIIIIIIIIII)V");

        env->CallVoidMethod(element, set_str_method,name,
												author,
												keyword,
												info);

        env->CallVoidMethod(element, set_int_method,cellInfo->server_guid,
												cellInfo->client_guid,
												cellInfo->ver1,
												cellInfo->ver2,
												cellInfo->ver3,
												cellInfo->data_type,
												cellInfo->inner_ver_from,
												cellInfo->inner_ver,
												cellInfo->type1,
												cellInfo->type2,
												cellInfo->type3,
												cellInfo->ci_count,
												cellInfo->loc_type,
												cellInfo->install_time,
												cellInfo->is_hide);
		env->DeleteLocalRef(info);
		env->DeleteLocalRef(keyword);
		env->DeleteLocalRef(author);
		env->DeleteLocalRef(name);
		env->DeleteLocalRef(customCls);
		env->DeleteLocalRef(element);
    }
	free(cellInfos);

    return (jint)index;
}


 /* Class:     com_baidu_input_PlumCore

 * Method:    PlCellGetinfoById
 * Signature: (Lcom/baidu/input/pub/CellInfo;I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlCellGetinfoById
  (JNIEnv * env, jobject obj, jobject jcellInfo, jint id)
{
	s_cellinfo* cellInfo = (s_cellinfo*) malloc(sizeof(s_cellinfo));
	if (cellInfo == NULL)
	{
		return -1;
	}

    memset(cellInfo, 0, sizeof(s_cellinfo));
    int ret = -1;
    static jclass customCls = 0;
    static jmethodID set_str_method = 0;
    static jmethodID set_int_method = 0;

	customCls = env->GetObjectClass(jcellInfo);

  	ret = ipt_cell_info_byCellId(iptcore, cellInfo, id);

  	jstring name = convert_wchar_to_jstring2(env, cellInfo->name_buf, cellInfo->name_len);
  	jstring author = convert_wchar_to_jstring2(env, cellInfo->author_buf, cellInfo->author_len);
  	jstring keyword = convert_wchar_to_jstring2(env, cellInfo->keyword_buf, cellInfo->keyword_len);
  	jstring info = convert_wchar_to_jstring2(env, cellInfo->info_buf, cellInfo->info_len);

 	if(set_str_method == 0)
      	set_str_method = env->GetMethodID(customCls, "set_str","(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V");
 	if(set_int_method == 0)
       	set_int_method = env->GetMethodID(customCls, "set_int","(IIIIIIIIIIIIIII)V");

 	env->CallVoidMethod(jcellInfo, set_str_method,name,
												author,
												keyword,
												info);

 	env->CallVoidMethod(jcellInfo, set_int_method,cellInfo->server_guid,
												cellInfo->client_guid,
												cellInfo->ver1,
												cellInfo->ver2,
												cellInfo->ver3,
												cellInfo->data_type,
												cellInfo->inner_ver_from,
												cellInfo->inner_ver,
												cellInfo->type1,
												cellInfo->type2,
												cellInfo->type3,
												cellInfo->ci_count,
												cellInfo->loc_type,
												cellInfo->install_time,
												cellInfo->is_hide);
	env->DeleteLocalRef(name);
	env->DeleteLocalRef(author);
	env->DeleteLocalRef(keyword);
	env->DeleteLocalRef(info);
	env->DeleteLocalRef(customCls);
    free(cellInfo);

    return ret;
}


 /* 
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCellGetVer
 * Signature: (I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlCellGetVer
  (JNIEnv *env, jobject obj, jint type)
{
	int cellId = 0;
    switch(type)
	{
		case POP:
			cellId = (jint)ipt_cell_get_popword_CellId(iptcore);
			break;
		case SYS:
			cellId = (jint)ipt_cell_get_sysword_CellId(iptcore);
			break;
		default:
			break;
	}

	s_cellinfo cellInfo;
	memset(&cellInfo, 0, sizeof(s_cellinfo));
	ipt_cell_info_byCellId(iptcore, &cellInfo, cellId);

    return cellInfo.inner_ver;
}


 /* 
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCellGetSysVER
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlCellGetSysVER
  (JNIEnv *env, jobject obj)
{
	return (jint)ipt_cell_get_sysword_ver(iptcore);
}

///////////////////////////////////////////////////////
////涓�х煭璇鐞嗙浉鍏虫帴鍙�///////////////////////////////////
///////////////////////////////////////////////////////
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlPhraseImport
 * Signature: (Ljava/lang/String;Z)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlPhraseImport
(JNIEnv *env, jobject obj, jstring jfileName, jboolean overwrite)
{
    jboolean isCopy = JNI_FALSE;
    PLUINT32 isOverwrite = overwrite ? 1 : 0;
    const char* fileName = env->GetStringUTFChars(jfileName, &isCopy);
    jint ret = (jint)ipt_phrase_import(iptcore, fileName, isOverwrite);
    env->ReleaseStringUTFChars(jfileName, fileName);
    return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlPhraseImport
 * Signature: (Ljava/lang/String;I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlPhraseExport
  (JNIEnv * env, jobject obj, jstring jfileName, jint groupId)
{
    jboolean isCopy = JNI_FALSE;

    const char* fileName = env->GetStringUTFChars(jfileName, &isCopy);

    jint ret = (jint)ipt_phrase_export(iptcore, fileName, groupId);

    env->ReleaseStringUTFChars(jfileName, fileName);

    return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlPhraseGetCount
 * Signature: (I[B)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlPhraseGetCount
  (JNIEnv * env, jobject obj, jint groupId, jbyteArray codenameArray)
{
	jboolean isCopy = JNI_FALSE;
	jint ret = -1;
	if(NULL != codenameArray)
	{
		jint codenameLen = env->GetArrayLength(codenameArray);
		jbyte* codeName = env->GetByteArrayElements(codenameArray, &isCopy);
		ret = ipt_phrase_item_count(session,groupId,(PLBYTE*)codeName, codenameLen);
		env->ReleaseByteArrayElements(codenameArray, codeName, JNI_ABORT);
	}
	else
	{
		ret = ipt_phrase_item_count(session,groupId,NULL, 0);
	}
	return ret;
}


/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlPhraseGetInfo
 * Signature: (Lcom/baidu/input/pub/PhraseInfo;I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlPhraseGetInfo
  (JNIEnv *env, jobject obj, jobject jphraseInfo, jint index)
{
	s_phrase_info phraseInfo;
    memset(&phraseInfo,0,sizeof(s_phrase_info));

    int ret = ipt_phrase_item_info(session, &phraseInfo, index);

    if(ret != 0)
        return 0;
	
    jclass customCls = 0;

    customCls = env->GetObjectClass(jphraseInfo);

    char tmpCode[MAX_PHRASE_CODE_LEN  + 1] = {'\0'};
    memcpy(tmpCode, phraseInfo.code, phraseInfo.code_len);
   	jstring code = env->NewStringUTF(tmpCode);
   	jstring word = convert_wchar_to_jstring2(env, phraseInfo.word, phraseInfo.word_len);

    static jmethodID set_str_method = env->GetMethodID(customCls, "set_str","(Ljava/lang/String;Ljava/lang/String;)V");
    static jmethodID set_int_method = env->GetMethodID(customCls, "set_int","(IIII)V");

  	env->CallVoidMethod(jphraseInfo, set_str_method,code,word);
   	env->CallVoidMethod(jphraseInfo, set_int_method,phraseInfo.phrase_OFF,
											phraseInfo.phrase_CRC,
											phraseInfo.pos,
											phraseInfo.group_id);

	env->DeleteLocalRef(code);
	env->DeleteLocalRef(word);
	env->DeleteLocalRef(customCls);

    return ret;
}

 /*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlPhraseEdit
 * Signature: (Lcom/baidu/input/pub/PhraseInfo;I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlPhraseEdit
  (JNIEnv *env, jobject obj, jobject jphraseInfo, jint option)
{
    jclass customCls = env->GetObjectClass(jphraseInfo);
    static jmethodID getPhrase_OFF = env->GetMethodID(customCls, "getPhrase_OFF","()I");
    static jmethodID getPhrase_CRC = env->GetMethodID(customCls, "getPhrase_CRC","()I");
    static jmethodID getPos = env->GetMethodID(customCls, "getPos","()I");
    static jmethodID getGroup_id = env->GetMethodID(customCls, "getGroup_id","()I");
    static jmethodID getCode = env->GetMethodID(customCls, "getCode","()Ljava/lang/String;");
    static jmethodID getWord = env->GetMethodID(customCls, "getWord","()Ljava/lang/String;");

	int phrase_OFF = env->CallIntMethod(jphraseInfo, getPhrase_OFF);;
	int phrase_CRC = env->CallIntMethod(jphraseInfo, getPhrase_CRC);;
	int pos = env->CallIntMethod(jphraseInfo, getPos);;
	int group_id = env->CallIntMethod(jphraseInfo, getGroup_id);;
	jstring jcode = (jstring)env->CallObjectMethod(jphraseInfo, getCode);
	jstring jword = (jstring)env->CallObjectMethod(jphraseInfo, getWord);

	env->DeleteLocalRef(customCls);

#define MAX_CODE_LEN 32+1
#define MAX_WORD_LEN 64+1
	jboolean isCopy = JNI_FALSE;
	int wordLen = (int) env->GetStringLength(jword);
	if(wordLen > MAX_WORD_LEN - 1 && wordLen < 1)
    		return -1;
    		
	const jchar* word = env->GetStringChars(jword, &isCopy);

	int codeLen = (int) env->GetStringUTFLength(jcode);
	if(codeLen > MAX_CODE_LEN - 1 && codeLen < 1)
    		return -1;
    		
	const char* code = env->GetStringUTFChars(jcode, &isCopy);

	PLUINT16 wcharContent[MAX_WORD_LEN];
	memset(wcharContent, 0, MAX_WORD_LEN * sizeof(PLUINT16));
	jchar_to_wchar(wcharContent, word,wordLen);
	wcharContent[wordLen] = (PLUINT16) '\0';

	s_phrase_info phraseInfo;
    memset(&phraseInfo,0,sizeof(s_phrase_info));
	phraseInfo.phrase_OFF = phrase_OFF;
	phraseInfo.phrase_CRC = phrase_CRC;
    phraseInfo.pos = pos;
    phraseInfo.group_id = group_id;
    phraseInfo.code_len = codeLen;
    memcpy(&phraseInfo.code[0], code, codeLen);
    phraseInfo.word_len = wordLen;
    memcpy(&phraseInfo.word[0], &wcharContent[0], wordLen*sizeof(PLUINT16));

	jint ret = (jint) ipt_phrase_item_edit(session, &phraseInfo, option);

	env->ReleaseStringChars(jword, word);
	env->ReleaseStringUTFChars(jcode, code);

	return ret;

}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlPhraseGPGetCount
 * Signature: ([CI)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlPhraseGPGetCount
  (JNIEnv * env, jobject obj, jcharArray groupnameArray, jint len)
{
	jboolean isCopy = JNI_FALSE;
	jint ret = -1;
	if(NULL != groupnameArray)
	{
		jchar* groupName = env->GetCharArrayElements(groupnameArray, &isCopy);
		ret = ipt_phrase_group_count(session, (PLUINT16*) groupName, len);
		env->ReleaseCharArrayElements(groupnameArray, groupName, JNI_ABORT);
	}
	else
	{
		ret = ipt_phrase_group_count(session, NULL, 0);
	}
	return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlPhraseGPGetInfo
 * Signature: (Lcom/baidu/input/pub/PhraseGPInfo;I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlPhraseGPGetInfo
  (JNIEnv *env, jobject obj, jobject jphraseGPInfo, jint index)
{
	s_phrase_group_info groupInfo;
    memset(&groupInfo,0,sizeof(s_phrase_group_info));

    int ret = ipt_phrase_group_info(session, &groupInfo, index);

    if(ret != 0)
        return 0;
	
    jclass customCls = 0;

    customCls = env->GetObjectClass(jphraseGPInfo);

   	jstring word = convert_wchar_to_jstring2(env, groupInfo.word, groupInfo.name_len);

    static jmethodID set_str_method = env->GetMethodID(customCls, "set_str","(Ljava/lang/String;)V");
    static jmethodID set_int_method = env->GetMethodID(customCls, "set_int","(IIIII)V");

  	env->CallVoidMethod(jphraseGPInfo, set_str_method,word);
   	env->CallVoidMethod(jphraseGPInfo, set_int_method,groupInfo.group_OFF,
											groupInfo.group_CRC,
											groupInfo.group_id,
											groupInfo.phrase_cnt,
											groupInfo.is_open);
	env->DeleteLocalRef(word);
	env->DeleteLocalRef(customCls);

    return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlPhraseGPEdit
 * Signature: (Lcom/baidu/input/pub/PhraseGPInfo;I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlPhraseGPEdit
  (JNIEnv *env, jobject obj,  jobject jphraseGPInfo, jint option)
{
    jclass customCls = env->GetObjectClass(jphraseGPInfo);
    static jmethodID getGroup_OFF = env->GetMethodID(customCls, "getGroup_OFF","()I");
    static jmethodID getGroup_CRC = env->GetMethodID(customCls, "getGroup_CRC","()I");
    static jmethodID getGroup_id = env->GetMethodID(customCls, "getGroup_id","()I");
    static jmethodID getPhrase_cnt = env->GetMethodID(customCls, "getPhrase_cnt","()I");
    static jmethodID getIs_open = env->GetMethodID(customCls, "getIs_open","()I");
    static jmethodID getWord = env->GetMethodID(customCls, "getWord","()Ljava/lang/String;");

	int group_OFF = env->CallIntMethod(jphraseGPInfo, getGroup_OFF);
	int group_CRC = env->CallIntMethod(jphraseGPInfo, getGroup_CRC);
	int group_id = env->CallIntMethod(jphraseGPInfo, getGroup_id);
	int phrase_cnt = env->CallIntMethod(jphraseGPInfo, getPhrase_cnt);
	int is_open = env->CallIntMethod(jphraseGPInfo, getIs_open);
	jstring jword = (jstring)env->CallObjectMethod(jphraseGPInfo, getWord);

	env->DeleteLocalRef(customCls);

	jboolean isCopy = JNI_FALSE;
	int wordLen = (int) env->GetStringLength(jword);
	if(wordLen > MAX_NAME_LEN - 1 && wordLen < 1)
    		return -1;
    		
	const jchar* word = env->GetStringChars(jword, &isCopy);

	PLUINT16 wcharContent[MAX_NAME_LEN];
	memset(wcharContent, 0, MAX_NAME_LEN * sizeof(PLUINT16));
	jchar_to_wchar(wcharContent, word,wordLen);
	wcharContent[wordLen] = (PLUINT16) '\0';

	s_phrase_group_info groupInfo;
    memset(&groupInfo,0,sizeof(s_phrase_group_info));
    groupInfo.group_OFF = group_OFF;
    groupInfo.group_CRC = group_CRC;
    groupInfo.phrase_cnt = phrase_cnt;
    groupInfo.group_id = group_id;
    groupInfo.name_len = wordLen;
    groupInfo.is_open = (PLBYTE)is_open;
    memcpy(&groupInfo.word[0], word, wordLen * sizeof(jchar));

    jint ret = (jint)ipt_phrase_group_edit(session,&groupInfo, option);

    env->ReleaseStringChars(jword, word);
    return ret;
}

///////////////////////////////////////////////////////
////鍔犲瘑鐩稿叧鎺ュ彛/////////////////////////////////////////
///////////////////////////////////////////////////////
#ifdef SWITCH_ENCODE
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlIdea
 * Signature: ([BZ)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlIdea
  (JNIEnv * env, jobject obj, jbyteArray jbuff,jboolean mode)
{
	jboolean isCopy = JNI_FALSE;
    jbyte *buffer = env->GetByteArrayElements(jbuff, &isCopy);
    jint buffLen = env->GetArrayLength(jbuff);
	int ret;
    if(mode == JNI_TRUE)
	{
        ret = ipt_enc_idea_encode((PLBYTE*)buffer,buffLen);
	}
    else
	{
        ret = ipt_enc_idea_decode((PLBYTE*)buffer,buffLen);
	}
    env->ReleaseByteArrayElements(jbuff,buffer,JNI_ABORT);
	return ret;
}


/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlRsaEncoder
 * Signature: ([B)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlRsaEncoder
  (JNIEnv * env, jobject obj, jbyteArray jbuff)
{
	jboolean isCopy = JNI_FALSE;
    jbyte *buffer = env->GetByteArrayElements(jbuff, &isCopy);

    int ret = ipt_enc_rsa_encode_byDK((PLBYTE*)buffer);
    env->ReleaseByteArrayElements(jbuff,buffer,JNI_ABORT);
	return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlRsaGetBufferNeed
 * Signature: (I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlRsaGetBufferNeed
  (JNIEnv * env, jobject obj, jint contentlength)
{
    return ipt_enc_rsa_buffer_need(contentlength);
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlMartine
 * Signature: ([Ljava/lang/String;)[B
 */
JNIEXPORT jbyteArray JNICALL Java_com_baidu_input_PlumCore_PlMartine
  (JNIEnv *env, jobject obj, jobjectArray contents)
{
	jboolean isCopy = JNI_FALSE;
	jint arrLen = env->GetArrayLength(contents);

	jint contentLen = 0;
	jint userKey[4];
	jbyte seperate = 0x87;
	jbyteArray encodedContent = NULL;

	//contentLen婢堆冪毈鐠侊紕鐣�
	int i=0;
	for(i=0;i<arrLen;i++)
	{
		jobject objs = env->GetObjectArrayElement(contents, i);
		jstring str = (jstring)objs;
		contentLen += env->GetStringUTFLength(str);
		if(arrLen-1 != i)
		{
			contentLen += 1;
		}
		env->DeleteLocalRef(objs);
	}

	const jint BUFFERLEN = ipt_enc_ideaB64_buffer_need(contentLen);

	char *buffer = (char*)malloc(sizeof(char)*BUFFERLEN); 
	memset(buffer,0,BUFFERLEN);
	
	//16鐎涙濡梹鍨閻ㄥ嫬鐦戦柦?
	srand((jint)time(0));
	userKey[0] = rand()%65535;
	srand((jint)time(0));
	userKey[1] = rand()%65535;
	srand((jint)time(0));
	userKey[2] = rand()%65535;
	srand((jint)time(0));
	userKey[3] = rand()%65535;
	memcpy(buffer+4,userKey,16);


	//鐟曚礁濮炵�靛棛娈戦崘鍛啇
	contentLen = 0;
	for(i=0;i<arrLen;i++)
	{
		jobject objs = env->GetObjectArrayElement(contents, i);
		jstring str = (jstring)objs;

		if(str == NULL)
		{
			return NULL;
		}
		const char* szStr = env->GetStringUTFChars(str, &isCopy);
		int len = strlen(szStr);

		if(len == 0)
		{
			return NULL;
		}
		memcpy(buffer+20+contentLen, szStr, len);
		contentLen += len;


		if(arrLen-1 != i)
		{
			memcpy(buffer+20+contentLen, &seperate, 1);
			contentLen += 1;
		}
		
		env->ReleaseStringUTFChars(str, szStr);
		env->DeleteLocalRef(objs);
	}

	//鐟曚礁濮炵�靛棛娈戦崘鍛啇闂�鍨閿�?2娴ｅ秵鏆ｉ弫甯礉閸楃姷鏁�4鐎涙濡敍?
	memcpy(buffer,&contentLen,4);

	ipt_enc_ideaB64_encode((PLBYTE*)buffer);
			
	encodedContent = env->NewByteArray(BUFFERLEN);
	env->SetByteArrayRegion(encodedContent, 0, BUFFERLEN,(const jbyte*)buffer);
	free(buffer);
	return encodedContent;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlIdeaBase64Decoder
 * Signature: ([B)I

 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlIdeaBase64Decoder
  (JNIEnv * env, jobject obj, jbyteArray jbuff)
{
	jboolean isCopy = JNI_FALSE;
    jbyte *buffer = env->GetByteArrayElements(jbuff, &isCopy);

    int ret = ipt_enc_ideaB64_decode((PLBYTE*)buffer);

    env->ReleaseByteArrayElements(jbuff,buffer,JNI_ABORT);

	return ret;
}


/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetProductVersions
 * Signature: ([Ljava/lang/String;Landroid/content/pm/PackageInfo;)[B
 */
JNIEXPORT jbyteArray JNICALL Java_com_baidu_input_PlumCore_PlGetProductVersions
  (JNIEnv * env, jobject obj, jobjectArray contents, jobject packageInfo)
{
	jboolean isCopy = JNI_FALSE;
	jint arrLen = env->GetArrayLength(contents);

	jint contentLen = 0;
	jint userKey[4];
	jbyte seperate = 0x87;
	jbyteArray encodedContent = NULL;

#ifdef CRASH_VERSION
   	int ret = (getCode(env,obj,packageInfo) == number() ? 0 : 100); 
	checkState(ret);
#endif

	//contentLen婢堆冪毈鐠侊紕鐣�
	int i=0;
	for(i=0;i<arrLen;i++)
	{
		jobject objs = env->GetObjectArrayElement(contents, i);
		jstring str = (jstring)objs;
		contentLen += env->GetStringUTFLength(str);
		if(arrLen-1 != i)
		{
			contentLen += 1;
		}
		env->DeleteLocalRef(objs);
	}

	const jint BUFFERLEN = ipt_enc_ideaB64_buffer_need_v2(contentLen);

	char *buffer = (char*)malloc(sizeof(char)*BUFFERLEN); 
	memset(buffer,0,BUFFERLEN);
	
	srand((jint)time(0));
	userKey[0] = rand()%65535;
	srand((jint)time(0));
	userKey[1] = rand()%65535;
	srand((jint)time(0));
	userKey[2] = rand()%65535;
	srand((jint)time(0));
	userKey[3] = rand()%65535;
	memcpy(buffer+4,userKey,16);


	contentLen = 0;
	for(i=0;i<arrLen;i++)
	{
		jobject objs = env->GetObjectArrayElement(contents, i);
		jstring str = (jstring)objs;

		if(str == NULL)
		{
			return NULL;
		}
		const char* szStr = env->GetStringUTFChars(str, &isCopy);
		int len = strlen(szStr);

		if(len == 0)
		{
			return NULL;
		}
		memcpy(buffer+20+contentLen, szStr, len);
		contentLen += len;

		if(arrLen-1 != i)
		{
			memcpy(buffer+20+contentLen, &seperate, 1);
			contentLen += 1;
		}
		
		env->ReleaseStringUTFChars(str, szStr);
		env->DeleteLocalRef(objs);
	}

	memcpy(buffer,&contentLen,4);

	ipt_enc_ideaB64_encode_v2((PLBYTE*)buffer);
			
	encodedContent = env->NewByteArray(BUFFERLEN);
	env->SetByteArrayRegion(encodedContent, 0, BUFFERLEN,(const jbyte*)buffer);
	free(buffer);
	return encodedContent;
}


#endif //#ifdef SWITCH_ENCODE

///////////////////////////////////////////////////////
////鑱旂郴浜虹浉鍏虫帴鍙�///////////////////////////////////////
///////////////////////////////////////////////////////
/*
 * Class:     com_baidu_input_PlumCore

 * Method:    PlCtClean
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_baidu_input_PlumCore_PlCtClean
  (JNIEnv * env, jobject obj)
{
    jint ret = ipt_contact_reset(iptcore);
    if(0 == ret)
	{
		return JNI_TRUE;
 	}
	else
	{
		return JNI_FALSE;
	}
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCtAddAttris
 * Signature: ([Ljava/lang/String;)Z
 */
JNIEXPORT jboolean JNICALL Java_com_baidu_input_PlumCore_PlCtAddAttris
  (JNIEnv *env, jobject obj, jobjectArray jattributes)
{
	jboolean isCopy = JNI_FALSE;
	int attrLen = env->GetArrayLength(jattributes);
    int i = 0;
	int ret = -1;

	if(attrLen > MAX_CT_ATTRI_NUM)
	{
		return JNI_FALSE;
	}

	PLUINT16 wAttribute[MAX_CT_ATTRI+1] = {0};
    jstring jattribute;

    for(i = 0; i < attrLen; i++)
	{
		//get attribute
		memset(wAttribute, 0, sizeof(PLUINT16) * (MAX_CT_ATTRI+1));
		jattribute = (jstring)env->GetObjectArrayElement(jattributes, i);
			
        int size = (int)env->GetStringLength(jattribute);

	    if(jattribute == NULL || 0 == size)
	    {
			env->DeleteLocalRef(jattribute); 
		    continue;
	    }

	    if(size > MAX_CT_ATTRI)
		{
	        size = MAX_CT_ATTRI;
		}
	    const jchar* attribute = env->GetStringChars(jattribute, &isCopy);
        jchar_to_wchar(wAttribute, attribute, size);
        wAttribute[size] = (PLUINT16)'\0';
	    env->ReleaseStringChars(jattribute, attribute);
		env->DeleteLocalRef(jattribute);            

		//append attribute
		ret = ipt_contact_append_attri(iptcore, wAttribute);

		if(ret < 0)
		{
			return JNI_FALSE;
		}
	}

    return JNI_TRUE;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCtAddContact
 * Signature: (Ljava/lang/String;[Ljava/lang/String;[Ljava/lang/String;)Z
 */
JNIEXPORT jboolean JNICALL Java_com_baidu_input_PlumCore_PlCtAddContact
  (JNIEnv * env, jobject obj, jstring jname, jobjectArray jattributes, jobjectArray jvalues)
{
	jboolean isCopy = JNI_FALSE;
	int attrLen = env->GetArrayLength(jattributes);
    int valLen = env->GetArrayLength(jvalues);
    int size = env->GetStringLength(jname);
    int i = 0;
	bool ret = true;

	if(attrLen != valLen || attrLen > MAX_CT_ATTRI_NUM)
	{
		return JNI_FALSE;
	}

    const jchar* name = env->GetStringChars(jname, &isCopy);

	if(size > MAX_CT_NAME)
	{
		size = MAX_CT_NAME;
	}

	PLUINT16 wName[MAX_CT_NAME+1] = {0};
	PLUINT16 wAttribute[MAX_CT_ATTRI+1] = {0};
	PLUINT16 wValue[MAX_CT_VALUE+1] = {0};
    jstring jattribute;
    jstring jvalue;
    jchar_to_wchar(wName,name,size);

    wName[size] = (PLUINT16)'\0';

	env->ReleaseStringChars(jname, name);

    for(i = 0; i < attrLen; i++)
	{
		//get attribute
		memset(wAttribute, 0, sizeof(PLUINT16) * (MAX_CT_ATTRI+1));
		jattribute = (jstring)env->GetObjectArrayElement(jattributes, i);
			
        int size = (int)env->GetStringLength(jattribute);

	    if(jattribute == NULL || 0 == size)
	    {
			env->DeleteLocalRef(jattribute); 
		    continue;
	    }

	    if(size > MAX_CT_ATTRI)
		{
	        size = MAX_CT_ATTRI;
		}
	    const jchar* attribute = env->GetStringChars(jattribute, &isCopy);
        jchar_to_wchar(wAttribute, attribute, size);
        wAttribute[size] = (PLUINT16)'\0';
	    env->ReleaseStringChars(jattribute, attribute);
		env->DeleteLocalRef(jattribute);     

		//get value
	    memset(wValue, 0, sizeof(PLUINT16) * (MAX_CT_VALUE+1));
		jvalue = (jstring)env->GetObjectArrayElement(jvalues, i);
			
        size = (int)env->GetStringLength(jvalue);

	    if(jattribute == NULL || 0 == size)
	    {
			env->DeleteLocalRef(jvalue); 
		    continue;
	    }

	    if(size > MAX_CT_VALUE)
		{
	        size = MAX_CT_VALUE;
		}
	    const jchar* value = env->GetStringChars(jvalue, &isCopy);
        jchar_to_wchar(wValue, value, size);
        wValue[size] = (PLUINT16)'\0';
	    env->ReleaseStringChars(jvalue, value);
		env->DeleteLocalRef(jvalue);  

		//append value
		ret &= ipt_contact_append_value(iptcore, wName, wAttribute, wValue);
	}

	return (jboolean) ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCtGetContact
 * Signature: (I)[Ljava/lang/String;
 */
JNIEXPORT jobjectArray JNICALL Java_com_baidu_input_PlumCore_PlCtGetContact
  (JNIEnv * env, jobject obj, jint count)
{
	jboolean isCopy = JNI_FALSE;

    if(count <= 0)
	{
        return NULL;
	}

	int i = 0;
    PLUINT16 wAttribute[MAX_CT_ATTRI+1] = {0};
	PLUINT16 wValue[MAX_CT_VALUE+1] = {0};
	jstring jattribute;
    jstring jvalue;

	jobjectArray contactInfo; 
    jclass objClass = (env)->FindClass("java/lang/String");
    contactInfo = (env)->NewObjectArray(count*2, objClass, 0);

	for(i = 0; i < count; i++)
	{
		memset(wAttribute, 0, sizeof(PLUINT16) * (MAX_CT_ATTRI+1));
		memset(wValue, 0, sizeof(PLUINT16) * (MAX_CT_VALUE+1));
		ipt_contact_get_value(session, wAttribute, wValue, i);
        jstring jattribute = convert_wchar_to_jstring(env,wAttribute);
		jstring jvalue = convert_wchar_to_jstring(env,wValue);
		//env->SetObjectArrayElement(contactInfo, 2*i, jattribute); //attr|val|attr|val|attr|val|attr|val
		//env->SetObjectArrayElement(contactInfo, 2*i + 1, jvalue);

		env->SetObjectArrayElement(contactInfo, i, jattribute); //attr|attr|attr|attr|val|val|val|val
		env->SetObjectArrayElement(contactInfo, i + count, jvalue);
		
		env->DeleteLocalRef(jattribute); 
		env->DeleteLocalRef(jvalue); 
	}

    env->DeleteLocalRef(objClass); 

    return contactInfo;

}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCtGetContact
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlCtGetCount
  (JNIEnv * env, jobject obj, jstring name)
{
	jboolean isCopy = JNI_FALSE;
    jint ret = -1;
	if (name && session)
	{
		int size = env->GetStringLength(name);
		if(size > 0 && size < MAX_CT_NAME)
		{
			const jchar *tmpName = env->GetStringChars(name,&isCopy);
			PLUINT16 unicode[MAX_CT_NAME + 1];
			memset(unicode, 0, (MAX_CT_NAME + 1) * sizeof(PLUINT16));
			jchar_to_wchar(unicode, tmpName, size);

			ret = ipt_contact_get_count(session, unicode);
			env->ReleaseStringChars(name, tmpName);
		}
	}
	return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCtDeleteContact
 * Signature: (Ljava/lang/String;I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlCtDeleteContact
  (JNIEnv * env, jobject obj, jstring name, jint cmd)
{
	jboolean isCopy = JNI_FALSE;
    jint ret = -1;
	if (name && session)
	{
		int size = env->GetStringLength(name);
		if(size > 0 && size < MAX_CT_NAME)
		{
			const jchar *tmpName = env->GetStringChars(name,&isCopy);
			PLUINT16 unicode[MAX_CT_NAME + 1];
			memset(unicode, 0, (MAX_CT_NAME + 1) * sizeof(PLUINT16));
			jchar_to_wchar(unicode, tmpName, size);

			ret = ipt_contact_delete(session, unicode, cmd);
			env->ReleaseStringChars(name, tmpName);
		}
	}
	return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCtVoiceFind
 * Signature: (Ljava/lang/String;)Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_baidu_input_PlumCore_PlCtVoiceFind
  (JNIEnv * env, jobject obj, jstring oriword)
{
	jboolean isCopy = JNI_FALSE;
    jint ret = -1;
	jstring str = NULL;
	if (oriword && session)
	{
		int size = env->GetStringLength(oriword);
		if(size > 0 && size < MAX_CT_NAME)
		{
			const jchar *tmpName = env->GetStringChars(oriword,&isCopy);
			PLUINT16 unicode[MAX_CT_NAME + 1];
			memset(unicode, 0, (MAX_CT_NAME + 1) * sizeof(PLUINT16));
			jchar_to_wchar(unicode, tmpName, size);
			PLUINT16 retunicode[MAX_CT_NAME + 1];
			memset(retunicode, 0, (MAX_CT_NAME + 1) * sizeof(PLUINT16));
			ret = ipt_contact_voice_find(session, unicode, retunicode);
			if (ret > 0)
			{
				str = convert_wchar_to_jstring(env, retunicode);
			}
			env->ReleaseStringChars(oriword, tmpName);
		}
	}
	return str;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCtVoiceFindAddressbook
 * Signature: (Ljava/lang/String;)Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_baidu_input_PlumCore_PlCtVoiceFindAddressbook
  (JNIEnv * env, jobject obj, jstring oriword)
{
	jboolean isCopy = JNI_FALSE;
    jint ret = -1;
	jstring str = NULL;
	if (oriword && session)
	{
		int size = env->GetStringLength(oriword);
		if (size > 0 && size < MAX_CT_NAME)
		{
			const jchar *tmpName = env->GetStringChars(oriword,&isCopy);
			PLUINT16 unicode[MAX_CT_NAME + 1];
			memset(unicode, 0, (MAX_CT_NAME + 1) * sizeof(PLUINT16));
			jchar_to_wchar(unicode, tmpName, size);
			PLUINT16 retunicode[MAX_CT_NAME + 1];
			memset(retunicode, 0, (MAX_CT_NAME + 1) * sizeof(PLUINT16));
			ret = ipt_contact_voice_find_addressbook(session, unicode, retunicode);
			if (ret > 0)
			{
				str = convert_wchar_to_jstring(env, retunicode);
			}
			env->ReleaseStringChars(oriword, tmpName);
		}
	}
	return str;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlUsrwordBackup
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_baidu_input_PlumCore_PlUsrwordBackup
  (JNIEnv *env, jobject obj)
{
		if(NULL != iptcore)
		{
			int ret = ipt_usrword_backup(iptcore);
			if(0 == ret)
			{
				return JNI_TRUE;
			}
		}
		return JNI_FALSE;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlUsrwordRecover
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_baidu_input_PlumCore_PlUsrwordRecover
  (JNIEnv *env, jobject obj)
{
		if(NULL != iptcore)
		{
			int ret = ipt_usrword_recover(iptcore);
			if(0 == ret)
			{
				return JNI_TRUE;
			}
		}
		return JNI_FALSE;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlUsrwordCheck
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_baidu_input_PlumCore_PlUsrwordCheck
  (JNIEnv *env, jobject obj)
{
		if(NULL != iptcore)
		{
			int ret = ipt_usrword_check(iptcore);
			if(0 == ret)
			{
				return JNI_TRUE;
			}
		}
		return JNI_FALSE;
}

///////////////////////////////////////////////////////
////鍏朵粬闇�瀹屽杽鐨勬帴鍙�///////////////////////////////////////
///////////////////////////////////////////////////////
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlPyFindFT
 * Signature: (Ljava/lang/String;)Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_baidu_input_PlumCore_PlFindFT
  (JNIEnv * env, jobject obj,jstring junicode)
{
    jboolean isCopy = JNI_FALSE;

    int size = (int)env->GetStringLength(junicode);
        
    if(size > (MAX_FT_LEN)  && size < 1)
	{
    	return NULL;
    }
	
    const jchar* tmp = env->GetStringChars(junicode,&isCopy);

    PLUINT16 unicode[MAX_FT_LEN + 1];
    memset(unicode,0,(MAX_FT_LEN + 1)*sizeof(PLUINT16));
    jchar_to_wchar(unicode,tmp,size);
    //unicode[size] = (PLUINT16)'\0';

    ipt_util_getFt_byUni(iptcore, (PLUINT16*)unicode);

    jstring ftstr = convert_wchar_to_jstring(env,unicode);

    env->ReleaseStringChars(junicode,tmp);

    return ftstr;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlDeleteUsWord
 * Signature: ([BIZ)V
 */
JNIEXPORT void JNICALL Java_com_baidu_input_PlumCore_PlDeleteUsWord
  (JNIEnv * env, jobject obj, jbyteArray inputArray,jint id, jboolean isCh)
{
    jboolean isCopy = JNI_FALSE;

    if(isCh == JNI_TRUE)
    {
        PLINT32 wLen = 0;
        PLUINT16 zids[MAX_CH_WORD];
        memset(zids,0,MAX_CH_WORD*sizeof(PLUINT16));

        wLen = ipt_usword_getzids(session, zids, (PLUINT32)id);

        ipt_delete_cnword_with_contact(session, (PLUINT16*)zids, wLen);
    }
    else
    {
        jbyte* input = env->GetByteArrayElements(inputArray, &isCopy);
        ipt_delete_enword_with_contact(session, (char*)input);

        env->ReleaseByteArrayElements(inputArray,input,JNI_ABORT);
    }
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlIsCNSysword
 * Signature: (I)Z
 */
JNIEXPORT jboolean JNICALL Java_com_baidu_input_PlumCore_PlIsCNSysword
  (JNIEnv * env, jobject obj, jint id)
{
    PLINT32 wLen = 0;
    PLUINT16 zids[MAX_CH_WORD];
    memset(zids,0,MAX_CH_WORD*sizeof(PLUINT16));

    wLen = ipt_usword_getzids(session, zids, (PLUINT32)id);

	int ret = ipt_adjust_is_cnword_exsit(session, zids, wLen);

	if(ret & 0x80000000)
	{
		return JNI_TRUE;
	}
	else
	{
		return JNI_FALSE;
	}
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlIsENSysword
 * Signature: ([B)Z
 */
JNIEXPORT jboolean JNICALL Java_com_baidu_input_PlumCore_PlIsENSysword
  (JNIEnv * env, jobject obj, jbyteArray enwordArray)
{
    jboolean isCopy = JNI_FALSE;
	jbyte* enword = env->GetByteArrayElements(enwordArray, &isCopy);
    int ret = ipt_adjust_is_enword_exsit(session, (char*)enword);   
	env->ReleaseByteArrayElements(enwordArray,enword,JNI_ABORT);
	if(ret & 0x80000000)
	{
		return JNI_TRUE;
	}
	else
	{
		return JNI_FALSE;
	}
}


/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetMatchLen
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlGetMatchLen
  (JNIEnv *env, jobject)
{
	return ipt_olddef_firstByte(iptcore);
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetHotLetter
 * Signature: ([B)V
 */
JNIEXPORT void JNICALL Java_com_baidu_input_PlumCore_PlGetHotLetter
  (JNIEnv *env, jobject obj,jbyteArray hotLetter)
{
	//hot letter includes 27 letters	
    jboolean isCopy = JNI_FALSE;
    jint length = (jint)env->GetArrayLength(hotLetter);

    if(length != 27)
	{
		return;
	}
    jbyte *letter = env->GetByteArrayElements(hotLetter, &isCopy);
    ipt_util_getPy_HotLetter(session, (char*)letter);

    env->ReleaseByteArrayElements(hotLetter,letter,0);
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    GlChangeColor
 * Signature: ([I[II)V
 */
JNIEXPORT void JNICALL Java_com_baidu_input_PlumCore_GlChangeColor
  (JNIEnv *env, jobject obj, jintArray old_rgb, jintArray rgb, jint color)
{
	jboolean isCopy = JNI_FALSE;
	//鑾峰緱鏁扮粍棣栧湴鍧�
	jint *old_element = env->GetIntArrayElements(old_rgb, &isCopy);
#ifndef SET_ARRAY_WITH_INTERFACE
	jint *new_element = env->GetIntArrayElements(rgb, &isCopy);
#endif
	//鑾峰緱鏁扮粍闀垮害
	jint length = (jint)env->GetArrayLength(old_rgb);
	
	//鏁版嵁澶勭悊
#ifndef SET_ARRAY_WITH_INTERFACE
	jint *tmp = new_element;
#endif
	jint *old_tmp = old_element;
	
	for(jint i = 0; i < length; i++){
#ifndef SET_ARRAY_WITH_INTERFACE
		*tmp++ = (*old_tmp++ & 0xFF000000) | color;
#else
		setIntArrayElement(env, rgb, i, (*old_tmp++ & 0xFF000000) | color);
#endif
    }

	//閲婃斁璧勬簮
	env->ReleaseIntArrayElements(old_rgb, old_element, JNI_ABORT);
#ifndef SET_ARRAY_WITH_INTERFACE
	env->ReleaseIntArrayElements(rgb, new_element, JNI_ABORT);
#endif
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    GlSetNight
 * Signature: ([II)V
 */
//JNIEXPORT void JNICALL Java_com_baidu_input_PlumCore_GlSetNight
//  (JNIEnv *env, jobject obj, jintArray rgb, jint total)
//{
//	jint alpha, r, g, b, tmp;
//	jboolean isCopy = JNI_FALSE;
	//鑾峰緱鏁扮粍棣栧湴鍧�
//	jint *rgb_element = env->GetIntArrayElements(rgb, &isCopy);
//	jint *rgb_tmp = rgb_element;
	
//	for (jint i = 0; i < total; i++)
//	{
//		tmp = *rgb_tmp;
//		alpha = tmp & 0xFF000000;

//		r = ((tmp >> 16) & 0xff);
//		g = ((tmp >> 8) & 0xff);
//		b = (tmp & 0xff);
				
//		r >>= 1;
//		g >>= 1;
//		b >>= 1;

//		tmp = alpha | (r << 16) | (g << 8) | b;
//#ifndef SET_ARRAY_WITH_INTERFACE
//		*rgb_tmp++ = tmp;
//#else
//		setIntArrayElement(env, rgb, i, tmp);
//		rgb_tmp++;
//#endif
//	}

	//閲婃斁璧勬簮
//	env->ReleaseIntArrayElements(rgb, rgb_element, JNI_ABORT);
//}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    GlSetNight
 * Signature: (Landroid/graphics/Bitmap;)V
 */
JNIEXPORT void JNICALL Java_com_baidu_input_PlumCore_GlSetNight
  (JNIEnv *env, jobject obj, jobject bitmapIn)
{
    changeDayNightModeInner(env, bitmapIn, JNI_TRUE);
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    GlSetNightColor
 * Signature: (I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_GlSetNightColor
  (JNIEnv *env, jobject obj, jint src)
{
    return changeToNightColor(src);
}

/*
 * Class:      com_baidu_input_PlumCore
 * Method:     GlSetDayColor
 * Signature:  (I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_GlSetDayColor
  (JNIEnv *env, jobject obj, jint src)
{
    return changeToDayColor(src);
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    GlSetToDayMode
 * Signature: (Landroid/graphics/Bitmap;)V
 */
JNIEXPORT void JNICALL Java_com_baidu_input_PlumCore_GlSetToDayMode
  (JNIEnv *env, jobject obj, jobject bitmapIn)
{
    changeDayNightModeInner(env, bitmapIn, JNI_FALSE);
}

void changeDayNightModeInner(JNIEnv *env, jobject bitmapIn, jboolean isNightMode)
{
    int tmp, ret, total, i;
    int prev = 0;
    int prevResult = 0;
    AndroidBitmapInfo infoIn;
    void *rgb_element;

    // Get image info
    if ((ret = AndroidBitmap_getInfo(env, bitmapIn, &infoIn)) != 0)
        return;
    // Check image
    if (infoIn.format != ANDROID_BITMAP_FORMAT_RGBA_8888)
        return;
    // Lock all images
    if ((ret = AndroidBitmap_lockPixels(env, bitmapIn, &rgb_element)) != 0) {
        //AndroidBitmap_lockPixels failed!
        return;
    }
    // height width
    int h = infoIn.height;
    int w = infoIn.width;

    total = w * h;

    jint *rgb_tmp = (int *)rgb_element;

    for (i = 0; i < total; i++)
    {
        tmp = *rgb_tmp;
        if (tmp == prev) {
            *rgb_tmp++ = prevResult;
            continue;
        }

        prev = tmp;

        if (isNightMode == JNI_TRUE)
        {
            tmp = changeToNightColor(tmp);
        }
        else
        {
            tmp = changeToDayColor(tmp);
        }

        prevResult = tmp;
        *rgb_tmp++ = tmp;
    }

    AndroidBitmap_unlockPixels(env, bitmapIn);
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    getProtCode
 * Signature: ()Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_baidu_input_PlumCore_getProtCode
  (JNIEnv *env, jobject obj)
{
    const char* tmpstr = "gp32fcd49e20190809194258";
    return env->NewStringUTF(tmpstr);
}
///////////////////////////////////////////////////////
////绗﹀彿鐩稿叧鐨勬搷浣�///////////////////////////////////////
///////////////////////////////////////////////////////

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlSymImport
 * Signature: (Ljava/lang/String;Z)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlSymImport
  (JNIEnv * env, jobject obj, jstring jfileName, jboolean isOverWrite)
{
	jboolean isCopy = JNI_FALSE;
    const char* fileName = env->GetStringUTFChars(jfileName, &isCopy);
	int ret = -1;

	if(JNI_TRUE == isOverWrite)
	{
		ret = ipt_sym_import(iptcore, fileName, 1);
    }
	else
	{
		ret = ipt_sym_import(iptcore, fileName, 0);
	}
    env->ReleaseStringUTFChars(jfileName, fileName);
	
	return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlSymExport
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlSymExport
  (JNIEnv * env, jobject obj, jstring jfileName)
{
	jboolean isCopy = JNI_FALSE;
    const char* fileName = env->GetStringUTFChars(jfileName, &isCopy);

	int ret = ipt_sym_export(iptcore, fileName);

    env->ReleaseStringUTFChars(jfileName, fileName);
	
	return ret;
}

/*
 * Class:     com_baidu_input_PlumCore

 * Method:    PlSylianImport
 * Signature: (Ljava/lang/String;Z)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlSylianImport
  (JNIEnv * env, jobject obj, jstring jfileName, jboolean isOverWrite)
{
	jboolean isCopy = JNI_FALSE;
    const char* fileName = env->GetStringUTFChars(jfileName, &isCopy);
	int ret = -1;

	if(JNI_TRUE == isOverWrite)
	{
		ret = ipt_sylian_import(iptcore, fileName, 1);
    }
	else
	{
		ret = ipt_sylian_import(iptcore, fileName, 0);
	}
    env->ReleaseStringUTFChars(jfileName, fileName);
	
	return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlSylianExport
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlSylianExport
  (JNIEnv * env, jobject obj, jstring jfileName)
{
	jboolean isCopy = JNI_FALSE;
    const char* fileName = env->GetStringUTFChars(jfileName, &isCopy);

	int ret = ipt_sylian_export(iptcore, fileName);

    env->ReleaseStringUTFChars(jfileName, fileName);
	
	return ret;
}


/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCreateEmptyLibFile
 * Signature: (Ljava/lang/String;I)Z
 */
/**
JNIEXPORT jboolean JNICALL Java_com_baidu_input_PlumCore_PlCreateEmptyLibFile
  (JNIEnv * env, jobject obj, jstring jfileName, jint type)
{
	jboolean isCopy = JNI_FALSE;
    const char* fileName = env->GetStringUTFChars(jfileName, &isCopy);
	int ret = -1;

    s_ipt_libfile* libFiles = (s_ipt_libfile*) malloc(sizeof(s_ipt_libfile));
	memset(libFiles, 0, sizeof(s_ipt_libfile));
    
    switch(type)
	{
		case UZ:
		{
            libFiles->ch_uz2_file = fileName;
			break;
		}
		case UE:
		{
			libFiles->en_ue2_file = fileName;
			break;
		}
		case CELL:
		{
			libFiles->ch_cell_file 	= fileName;
			break;
		}
		case PHRASE:
		{
			libFiles->ot_phrase_file= fileName;
			break;
		}
		case CONTACT:
		{
			libFiles->ot_contact_file= fileName;
			break;
		}
		default:
			break;

	}

	ret = ipt_core_create_emptylib(libFiles);

    env->ReleaseStringUTFChars(jfileName, fileName);

    if(ret > 0)
	{
		return JNI_TRUE;
	}
    else
	{
		return JNI_FALSE;
	}
}
*/

 /* 
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetGramVersion
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlGetGramVersion
  (JNIEnv * env, jobject obj)
{
	return ipt_util_getGram_Ver(iptcore);
}

 /* 
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetGramCateVersion
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlGetGramCateVersion
  (JNIEnv * env, jobject obj)
{
	return ipt_util_get_gram_cate_ver(iptcore);
}

 /* 
 * Class:     com_baidu_input_PlumCore
 * Method:    PlIsLongJP
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlIsLongJP
  (JNIEnv * env, jobject obj)
{
	return ipt_util_getPy_IsLongJP(session);
}

 /* 
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCheckFileMD5
 * Signature: (Ljava/lang/String;[B)V
 */
JNIEXPORT void JNICALL Java_com_baidu_input_PlumCore_PlCheckFileMD5
  (JNIEnv * env, jobject obj, jstring jfileName, jbyteArray jMD5Digest)
{
	jboolean isCopy = JNI_FALSE;
    const char* fileName = env->GetStringUTFChars(jfileName, &isCopy);

    if(NULL == fileName)
	{
		return;
	}
    int size = env->GetArrayLength(jMD5Digest);

    if(size < 32)
	{
		return;
	}
#ifndef SET_ARRAY_WITH_INTERFACE
    jbyte * MD5Digest = env->GetByteArrayElements(jMD5Digest, &isCopy);
#endif

	FILE *file;
	MD5_CTX context;
	int len, i;
	unsigned char buffer[1024], digest[16];

	LOGI("sizeof(unsigned char) = %d", sizeof(unsigned char));

	if ((file = fopen (fileName, "rb")) == NULL)
	{
		LOGI("%s can't be opened", fileName);
	}
	else 
	{
		MD5Init (&context);

		while ((len = fread (buffer, 1, 1024, file)))
		{
			MD5Update (&context, buffer, len);
		}

		MD5Final (digest, &context);

		for( i = 0; i < 16; i++) 
		{
#ifdef SET_ARRAY_WITH_INTERFACE
			setByteArrayElement(env, jMD5Digest, 2 * i, (jbyte) (digest[i] / 16));
			setByteArrayElement(env, jMD5Digest, (2 * i) + 1, (jbyte) (digest[i] % 16));
#else
			MD5Digest[2*i] = digest[i] / 16;
			MD5Digest[2*i + 1] = digest[i] % 16;
#endif
		}

		fclose (file);
	}

	env->ReleaseStringUTFChars(jfileName, fileName);
#ifndef SET_ARRAY_WITH_INTERFACE
    env->ReleaseByteArrayElements(jMD5Digest, MD5Digest, JNI_ABORT);
#endif
}


 /* 
 * Class:     com_baidu_input_PlumCore
 * Method:    PlOldUeExport
 * Signature: (Ljava/lang/String;Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlOldUeExport
  (JNIEnv * env, jobject obj, jstring jinputFile, jstring joutputFile)
{
	jboolean isCopy = JNI_FALSE;
	jint ret = -1;
	const char* inputFile = env->GetStringUTFChars(jinputFile, &isCopy);
	const char* outputFile = env->GetStringUTFChars(joutputFile, &isCopy);

	ret = ipt_old_ue_export(inputFile, outputFile);
	
	env->ReleaseStringUTFChars(jinputFile, inputFile);
	env->ReleaseStringUTFChars(joutputFile, outputFile);

	return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlOldCpExport
 * Signature: (Ljava/lang/String;Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlOldCpExport
  (JNIEnv * env, jobject obj, jstring jinputFile, jstring joutputFile)
{
	jboolean isCopy = JNI_FALSE;
	jint ret = -1;

	const char* inputFile = env->GetStringUTFChars(jinputFile, &isCopy);
	const char* outputFile = env->GetStringUTFChars(joutputFile, &isCopy);

	ret = ipt_old_cp_export(inputFile, outputFile);
	
	env->ReleaseStringUTFChars(jinputFile, inputFile);
	env->ReleaseStringUTFChars(joutputFile, outputFile);

	return ret;
}

/*

 * Class:     com_baidu_input_PlumCore

 * Method:    PlEmojiImport

 * Signature: (Ljava/lang/String;Z)I
 */
/*JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlEmojiImport
  (JNIEnv * env, jobject obj, jstring jfileName, jboolean isOverWrite)
{
#ifdef CATCH_CRASH_SIGNAL
	setEnv4SignalCatch(env);
#endif
	jboolean isCopy = JNI_FALSE;
    const char* fileName = env->GetStringUTFChars(jfileName, &isCopy);
	int ret = -1;

	if(JNI_TRUE == isOverWrite)
		ret = ipt_emoji_import(iptcore, fileName, 1);
    else
		ret = ipt_emoji_import(iptcore, fileName, 0);

    env->ReleaseStringUTFChars(jfileName, fileName);
	
	return ret;
}*/

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlEmojiExport
 * Signature: (Ljava/lang/String;)I
 */
/*JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlEmojiExport
  (JNIEnv * env, jobject obj, jstring jfileName)
{
#ifdef CATCH_CRASH_SIGNAL
	setEnv4SignalCatch(env);
#endif
	jboolean isCopy = JNI_FALSE;
    const char* fileName = env->GetStringUTFChars(jfileName, &isCopy);

	int ret = ipt_emoji_export(iptcore, fileName);

    env->ReleaseStringUTFChars(jfileName, fileName);
	
	return ret;
}*/

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlUsWordReduce
 * Signature: (I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlUsWordReduce
  (JNIEnv * env, jobject obj, jint percent)
{
	return (jint)ipt_usword_reduce(session, percent);
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlSetHWPreword
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlSetHWPreword
  (JNIEnv * env, jobject obj, jstring jperword)
{
	jboolean isCopy = JNI_FALSE;
    jint ret = -1;
	if (jperword && session)
	{
		int size = env->GetStringLength(jperword);
		if(size > 0 && size < 4)
		{
			const jchar *tmpPerword = env->GetStringChars(jperword,&isCopy);
			PLUINT16 unicode[10];
			memset(unicode, 0, 10 * sizeof(PLUINT16));
			jchar_to_wchar(unicode, tmpPerword, size);

			ret = ipt_util_setHW_Preword(session, unicode);

			env->ReleaseStringChars(jperword, tmpPerword);
		}
	}
	return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetHWPinyin
 * Signature: (CI)[B
 */
JNIEXPORT jbyteArray JNICALL Java_com_baidu_input_PlumCore_PlGetHWPinyin
  (JNIEnv * env, jobject obj, jchar unicode, jint isall)
{
	jboolean isCopy = JNI_FALSE;
    jbyteArray pinyin = NULL;
	if (session)
	{
		char pystrs[64];
		memset(pystrs, 0, sizeof(char) * 64);
		int ret = ipt_util_getHW_Py(session, (PLUINT16)unicode, (PLBYTE*)pystrs, isall);
		if (ret > 0)
		{
			int len = 0;
			while (len < 64 && pystrs[len] != '\0')
			{
				len++;
			}
			pinyin = env->NewByteArray(len);
			env->SetByteArrayRegion(pinyin, 0, len, (const jbyte*)pystrs);
		}
	}
	return pinyin;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlAdjustCnWordbyHW
 * Signature: ([CI)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlAdjustCnWordbyHW
  (JNIEnv * env, jobject obj, jcharArray zidsArray, jint len)
{
	jboolean isCopy = JNI_FALSE;

    if(len > MAX_CH_USER_WORD_LEN || len < MIN_CH_USER_WORD_LEN)
	{
    	return -1;
	}
    jchar *zids = env->GetCharArrayElements(zidsArray, &isCopy);

	jint ret = ipt_adjust_uzw_word(session, (PLUINT16*)zids, len);

    env->ReleaseCharArrayElements(zidsArray, zids, JNI_ABORT);

    return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlFindLianbyHW
 * Signature: ([CI)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlFindLianbyHW
  (JNIEnv * env, jobject obj, jcharArray zidsArray, jint len)
{
	jboolean isCopy = JNI_FALSE;

    jchar *zids = env->GetCharArrayElements(zidsArray, &isCopy);

	jint ret = ipt_query_uzw_findlian(session, (PLUINT16*)zids, len);

    env->ReleaseCharArrayElements(zidsArray, zids, JNI_ABORT);

    return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlOtherwordGetStatus
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlOtherwordGetStatus
  (JNIEnv * env, jobject obj, jstring jtype)
{
	jint ret = -1;
/*	if (jtype && session)
	{
		int len = env->GetStringLength(jtype);
		if (len > 0 && len < 10)
		{
			jboolean isCopy = JNI_FALSE;
			const jchar *tmpType = env->GetStringChars(jtype, &isCopy);
			PLUINT16 type[10];
			memset(type, 0, 10 * sizeof(PLUINT16));
			jchar_to_wchar(type, tmpType, len);
			
			jint ret = ipt_otherword_get_status(session, type);

			env->ReleaseStringChars(jtype, tmpType);
		}
	}
*/
    return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlOtherwordEnable
 * Signature: (Ljava/lang/String;I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlOtherwordEnable
  (JNIEnv * env, jobject obj, jstring jtype, jint is_enable)
{
	jint ret = -1;
/*	if (jtype && session)
	{
		int len = env->GetStringLength(jtype);
		if (len > 0 && len < 10)
		{
			jboolean isCopy = JNI_FALSE;
			const jchar *tmpType = env->GetStringChars(jtype, &isCopy);
			PLUINT16 type[10];
			memset(type, 0, 10 * sizeof(PLUINT16));
			jchar_to_wchar(type, tmpType, len);
			
			jint ret = ipt_otherword_enable(session, type, is_enable);

			env->ReleaseStringChars(jtype, tmpType);
		}
	}
*/
    return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKeywordGetSearchVersion
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlKeywordGetSearchVersion
  (JNIEnv *env, jobject obj)
{
	return ipt_keyword_get_search_version(iptcore);
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKeywordGetMediaVersion
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlKeywordGetMediaVersion
  (JNIEnv * env, jobject obj)
{
	return ipt_keyword_get_media_version(iptcore);
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKeywordExport
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlKeywordExport
  (JNIEnv * env, jobject obj, jstring jfileName)
{
	jint ret = -1;
	jboolean isCopy = JNI_FALSE;
    const char* fileName = env->GetStringUTFChars(jfileName, &isCopy);
    ret = ipt_keyword_export(iptcore, fileName);
    env->ReleaseStringUTFChars(jfileName, fileName);
    return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKeywordCellCount
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlKeywordCellCount
  (JNIEnv * env, jobject obj)
{
	return ipt_keyword_cell_count(iptcore);
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKeywordCellInfoByIndex
 * Signature: (Lcom/baidu/input/pub/KeywordInfo;I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlKeywordCellInfoByIndex
  (JNIEnv * env, jobject obj, jobject jkeywordInfo, jint index)
{
	s_keyword_info_header* keywordInfo = (s_keyword_info_header*) malloc(sizeof(s_keyword_info_header));
	if (keywordInfo == NULL)
	{
		return -1;
	}
    memset(keywordInfo, 0 ,sizeof(s_keyword_info_header));
    int ret = -1;
    static jclass customCls = 0;
    static jmethodID set_int_method = 0;

	customCls = env->GetObjectClass(jkeywordInfo);

  	ret = ipt_keyword_cell_info_byIndex(iptcore, keywordInfo, index);
	
	set_int_method = env->GetMethodID(customCls, "set_int","(IIIIIIIIIIIIII)V");

	env->CallVoidMethod(jkeywordInfo, set_int_method,keywordInfo->time,
											keywordInfo->server_guid,
											keywordInfo->client_guid,
											keywordInfo->ver1,
											keywordInfo->ver2,
											keywordInfo->ver3,
											keywordInfo->data_type,
											keywordInfo->inner_ver_from,
											keywordInfo->inner_ver,
											keywordInfo->type1,
											keywordInfo->type2,
											keywordInfo->type3,
											keywordInfo->type4,
											keywordInfo->keyword_count);
	env->DeleteLocalRef(customCls);
    free(keywordInfo);
    
    return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKeywordCellInfoByCellId
 * Signature: (Lcom/baidu/input/pub/KeywordInfo;I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlKeywordCellInfoByCellId
  (JNIEnv * env, jobject obj, jobject jkeywordInfo, jint cellId)
{
	s_keyword_info_header* keywordInfo = (s_keyword_info_header*)malloc(sizeof(s_keyword_info_header));
	if (keywordInfo == NULL)
	{
		return -1;
	}
    memset(keywordInfo,0,sizeof(s_keyword_info_header));
    int ret = -1;
    static jclass customCls = 0;
    static jmethodID set_int_method = 0;

	customCls = env->GetObjectClass(jkeywordInfo);

  	ret = ipt_keyword_cell_info_byCellId(iptcore, keywordInfo, cellId);

	set_int_method = env->GetMethodID(customCls, "set_int","(IIIIIIIIIIIIII)V");

	env->CallVoidMethod(jkeywordInfo, set_int_method, keywordInfo->time,
											keywordInfo->server_guid,
											keywordInfo->client_guid,
											keywordInfo->ver1,
											keywordInfo->ver2,
											keywordInfo->ver3,
											keywordInfo->data_type,
											keywordInfo->inner_ver_from,
											keywordInfo->inner_ver,
											keywordInfo->type1,
											keywordInfo->type2,
											keywordInfo->type3,
											keywordInfo->type4,
											keywordInfo->keyword_count);
	env->DeleteLocalRef(customCls);
    free(keywordInfo);
    
    return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKeywordCellInstall
 * Signature: (Ljava/lang/String;[BI)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlKeywordCellInstall
  (JNIEnv * env, jobject obj, jstring jfileName, jbyteArray jbuff, jint len)
{
	int ret = -1;
	jboolean isCopy = JNI_FALSE;
	
	if(len == 0)
	{
		const char* fileName = env->GetStringUTFChars(jfileName, &isCopy);
		ret = ipt_keyword_cell_install(iptcore, fileName, len);
		env->ReleaseStringUTFChars(jfileName,fileName);
	}
	else
	{
		jbyte * buff = env->GetByteArrayElements(jbuff, &isCopy);
		ret = ipt_keyword_cell_install(iptcore, (const char *)buff, len);
		env->ReleaseByteArrayElements(jbuff, buff, JNI_ABORT);
	}
	
	return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKeywordCellUninstall
 * Signature: (I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlKeywordCellUninstall
  (JNIEnv * env, jobject obj, jint cellId)
{
	int ret = -1;
	ret = ipt_keyword_cell_uninstall(iptcore, cellId);
	
	return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKeywordCellEnable
 * Signature: (IZ)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlKeywordCellEnable
  (JNIEnv * env, jobject obj, jint cellId, jboolean enabled)
{
	int ret = -1;
	int isEnable = 0;
	if(enabled == JNI_TRUE)
	{
		isEnable = 1;
	}
	else
	{
		isEnable = 0;
	}
		
	ret = ipt_keyword_cell_enable(iptcore, cellId, isEnable);
	
	return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKeywordGetCandType
 * Signature: (Ljava/lang/String;)[I
 */
JNIEXPORT jintArray JNICALL Java_com_baidu_input_PlumCore_PlKeywordGetCandType
  (JNIEnv * env, jobject obj, jstring str)
{
	jboolean isCopy = JNI_FALSE;
	jintArray ret = (jintArray)env->NewIntArray(2);
#ifndef SET_ARRAY_WITH_INTERFACE
    jint *element = env->GetIntArrayElements(ret, &isCopy);
    element[0] = 0;
    element[1] = 0;
#else
	setIntArrayElement(env, ret, 0, 0);
	setIntArrayElement(env, ret, 1, 0);
#endif

	if (str && iptcore)
	{
		const jchar *tmpStr = env->GetStringChars(str,&isCopy);
		int len = env->GetStringLength(str);
		PLUINT16 unicode[len + 1];
		memset(unicode, 0, (len + 1) * sizeof(PLUINT16));
		jchar_to_wchar(unicode, tmpStr, len);
		env->ReleaseStringChars(str, tmpStr);

		PLUINT16 emoji_value = 0;
		PLUINT32 r = ipt_keyword_get_cand_type(iptcore, unicode, len, &emoji_value);
#ifndef SET_ARRAY_WITH_INTERFACE
		element[0] = (jint) r;
		element[1] = (jint) emoji_value;
#else
		setIntArrayElement(env, ret, 0, (jint) r);
		setIntArrayElement(env, ret, 1, (jint) emoji_value);
#endif
	}
#ifndef SET_ARRAY_WITH_INTERFACE
	env->ReleaseIntArrayElements(ret,element,JNI_ABORT);
#endif
	return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKeywordYanwenziExportOlddata
 * Signature: ([BI[BI[I)I
 */
//JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlKeywordYanwenziExportOlddata
//  (JNIEnv * env, jobject obj, jbyteArray joldywzsysdata, jint oldywzsyssize, jbyteArray joldywzusrdata, jint oldywzusrsize, jintArray jcellidmap)
//{
//	jboolean isCopy = JNI_FALSE;
//	jbyte* oldywzsysdata = env->GetByteArrayElements(joldywzsysdata, &isCopy);
//	jbyte* oldywzusrdata = env->GetByteArrayElements(joldywzusrdata, &isCopy);
//	jint* cellidmap = env->GetIntArrayElements(jcellidmap, &isCopy);
//	jint ret = ipt_keyword_yanwenzi_export_olddata(iptcore, (PLBYTE**)&oldywzsysdata, (PLUINT32*)&oldywzsyssize, (PLBYTE**)&oldywzusrdata, (PLUINT32*)&oldywzusrsize, (PLUINT32**)&cellidmap);
//	env->ReleaseByteArrayElements(joldywzsysdata,oldywzsysdata,JNI_ABORT);
//	env->ReleaseByteArrayElements(joldywzusrdata, oldywzusrdata,JNI_ABORT);
//	env->ReleaseIntArrayElements(jcellidmap,cellidmap,JNI_ABORT);
//	return ret;
//}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKeywordYanwenziImportOlddata
 * Signature: ([BI[BI[I)I
 */
//JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlKeywordYanwenziImportOlddata
//  (JNIEnv * env, jobject obj, jbyteArray joldywzsysdata, jint oldywzsyssize, jbyteArray joldywzusrdata, jint oldywzusrsize, jintArray jcellidmap)
//{
//	jboolean isCopy = JNI_FALSE;
//	jbyte* oldywzsysdata = env->GetByteArrayElements(joldywzsysdata, &isCopy);
//	jbyte* oldywzusrdata = env->GetByteArrayElements(joldywzusrdata, &isCopy);
//	jint* cellidmap = env->GetIntArrayElements(jcellidmap, &isCopy);
//	jint ret = ipt_keyword_yanwenzi_import_olddata(iptcore, (PLBYTE**)&oldywzsysdata, oldywzsyssize, (PLBYTE**)&oldywzusrdata, oldywzusrsize, (PLUINT32**)&cellidmap);
//	env->ReleaseByteArrayElements(joldywzsysdata,oldywzsysdata,JNI_ABORT);
//	env->ReleaseByteArrayElements(joldywzusrdata, oldywzusrdata,JNI_ABORT);
//	env->ReleaseIntArrayElements(jcellidmap,cellidmap,JNI_ABORT);
//	return ret;
//}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKeywordEmoticonCellInstall
 * Signature: (Ljava/lang/String;[BI)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlKeywordEmoticonCellInstall
  (JNIEnv * env, jobject obj, jstring jfileName, jbyteArray jbuff, jint len)
{
	int ret = -1;
	jboolean isCopy = JNI_FALSE;
	
	if(len == 0)
	{
		const char* fileName = env->GetStringUTFChars(jfileName, &isCopy);
		ret = ipt_keyword_emoticon_cell_install(iptcore, fileName, len);
		env->ReleaseStringUTFChars(jfileName,fileName);
	}
	else
	{
		jbyte * buff = env->GetByteArrayElements(jbuff, &isCopy);
		ret = ipt_keyword_emoticon_cell_install(iptcore, (const char *)buff, len);
		env->ReleaseByteArrayElements(jbuff, buff, JNI_ABORT);
	}
	
	return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKeywordFindNijigen
 * Signature: ()[C
 */
JNIEXPORT jcharArray JNICALL Java_com_baidu_input_PlumCore_PlKeywordFindNijigen
  (JNIEnv * env, jobject obj)
{
	jcharArray nijigenstr = NULL;
	if (session)
	{
		PLUINT16 nijigen[64];
		memset(nijigen, 0, 64 * sizeof(PLUINT16));

		int ret = ipt_keyword_find_nijigen(session, (PLUINT16*)nijigen);
		LOGI("PlKeywordFindNijigen ret=%d",ret);
		if (ret > 0)
		{
			nijigenstr = (jcharArray) env->NewCharArray(ret);
			for(PLUINT32 i = 0; i < ret; i++)
			{
				setCharArrayElement(env, nijigenstr, i, (jchar) nijigen[i]);
			}
		}
	}
    return nijigenstr;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKeywordFindEgg
 * Signature: ()[C
 */
JNIEXPORT jcharArray JNICALL Java_com_baidu_input_PlumCore_PlKeywordFindEgg
  (JNIEnv * env, jobject obj)
{
	jcharArray eggstr = NULL;
	if (session)
	{
		PLUINT16 egg[64];
		memset(egg, 0, 64 * sizeof(PLUINT16));

		int ret = ipt_keyword_find_egg(session, (PLUINT16*)egg);
		if (ret > 0)
		{
			eggstr = (jcharArray) env->NewCharArray(ret);
			for(PLUINT32 i = 0; i < ret; i++)
			{
				setCharArrayElement(env, eggstr, i, (jchar) egg[i]);
			}
		}
	}
    return eggstr;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKeywordFindVoiceLian
 * Signature: (Ljava/lang/String;[I[I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlKeywordFindVoiceLian
  (JNIEnv * env, jobject obj, jstring str, jintArray emojiList, jintArray emoticonList)
{
	jboolean isCopy = JNI_FALSE;
    jint *emojis = env->GetIntArrayElements(emojiList, &isCopy);
	jint *emotions = env->GetIntArrayElements(emoticonList, &isCopy);
	
	
	PLUINT32 ret = -1;

	if (str)
	{
		const jchar *tmpStr = env->GetStringChars(str,&isCopy);
		int len = env->GetStringLength(str);
		PLUINT16 unicode[len + 1];
		memset(unicode, 0, (len + 1) * sizeof(PLUINT16));
		jchar_to_wchar(unicode, tmpStr, len);
		env->ReleaseStringChars(str, tmpStr);
		
		LOGI("PlKeywordFindVoiceLian content =%s",unicode);
		
		PLUINT16 tempemojis[5];
		memset(tempemojis, 0, 5 * sizeof(PLUINT16));
		PLUINT16 tempemotions[5];
		memset(tempemotions, 0, 5 * sizeof(PLUINT16));
		PLUINT32 emojiLen = 0;
		PLUINT32 emotionLen = 0;

		ret = ipt_keyword_find_voice_lian(session, unicode, len, (PLUINT16 *)tempemojis, (PLUINT32 *)&emojiLen, (PLUINT16 *)tempemotions, (PLUINT32 *)&emotionLen);
		int i;
		for(i = 0; i < 5; i++)
		{
			emojis[i] = tempemojis[i];
			LOGI("PlKeywordFindVoiceLian emoji index=%d",i);
			LOGI("PlKeywordFindVoiceLian emoji value=%d",emojis[i]);
		}
		for(i = 0; i < 5; i++)
		{
			emotions[i] = tempemotions[i];
			LOGI("PlKeywordFindVoiceLian emotion index=%d",i);
			LOGI("PlKeywordFindVoiceLian emotion value=%d",emotions[i]);
		}
		
		LOGI("PlKeywordFindVoiceLian ret =%d",ret);
		env->ReleaseIntArrayElements(emojiList,emojis,0);
		env->ReleaseIntArrayElements(emoticonList,emotions,0);
		
	}
		
	return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKeywordFindVoiceEgg
 * Signature: (Ljava/lang/String;)[C
 */
JNIEXPORT jcharArray JNICALL Java_com_baidu_input_PlumCore_PlKeywordFindVoiceEgg
  (JNIEnv * env, jobject obj, jstring str)
{
	jboolean isCopy = JNI_FALSE;
	jcharArray eggstr = NULL;
	if (session && str)
	{
		const jchar *tmpStr = env->GetStringChars(str,&isCopy);
		int len = env->GetStringLength(str);
		PLUINT16 unicode[len + 1];
		memset(unicode, 0, (len + 1) * sizeof(PLUINT16));
		jchar_to_wchar(unicode, tmpStr, len);
		env->ReleaseStringChars(str, tmpStr);
		
		PLUINT16 egg[64];
		memset(egg, 0, 64 * sizeof(PLUINT16));

		int ret = ipt_keyword_find_voice_egg(session, unicode, len, (PLUINT16*)egg);
		if (ret > 0)
		{
			eggstr = (jcharArray) env->NewCharArray(ret);
			for(PLUINT32 i = 0; i < ret; i++)
			{
				setCharArrayElement(env, eggstr, i, (jchar) egg[i]);
			}
		}
	}
    return eggstr;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKeywordGetSentenceKeyword
 * Signature: (Lcom/baidu/input/pub/CoreString;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlKeywordGetSentenceKeyword
  (JNIEnv * env, jobject obj, jobject customObj)
{  
    jboolean isCopy = JNI_FALSE;
    PLUINT16 output[MAX_OUTPUT];
    memset(output,0,2*MAX_OUTPUT);
	jint ret = -1;

	ret = ipt_keyword_get_sentence_keyword(session,(PLUINT16*)output);
    	
    jstring str = convert_wchar_to_jstring(env,output);
    	
    jclass customCls = env->GetObjectClass(customObj);

    static jmethodID cs_set_method = env->GetMethodID(customCls, "set","(Ljava/lang/String;I)V");
    env->CallVoidMethod(customObj, cs_set_method, str, ret);
    
    env->DeleteLocalRef(customCls);
	env->DeleteLocalRef(str);
    
    return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKeywordFindLian
 * Signature: (Ljava/lang/String;I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlKeywordFindLian
  (JNIEnv * env, jobject obj, jstring jword, jint len)
{
	int ret = 0;
	jboolean isCopy = JNI_FALSE;
	const jchar* word = env->GetStringChars(jword, &isCopy);
	//int len = env->GetStringLength(jword);
	
    ipt_keyword_find_lian(session, (PLUINT16*)word, len);
   	env->ReleaseStringChars(jword,word);
	return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlEmojiGetSentenceLian
 * Signature: ([S)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlEmojiGetSentenceLian
  (JNIEnv * env, jobject obj, jshortArray jemojiValues)
{
	int ret = -1;
	jboolean isCopy = JNI_FALSE;
    jshort *emojiValues = env->GetShortArrayElements(jemojiValues, &isCopy);

    ret = ipt_emoji_get_sentence_lian(session,(PLUINT16*)emojiValues);
    env->ReleaseShortArrayElements(jemojiValues,emojiValues,JNI_ABORT);

	return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlEmojiAdjustEmojiRelation
 * Signature: (Ljava/lang/String;ICI)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlEmojiAdjustEmojiRelation
  (JNIEnv * env, jobject obj, jstring jword, jint len, jchar emojiValue, jint cellId)
{
	int ret = -1;
	jboolean isCopy = JNI_FALSE;
	const jchar* word = env->GetStringChars(jword, &isCopy);
	ret = ipt_adjust_emoji_relation(session, (PLUINT16*)word, len, (PLUINT16)emojiValue, cellId);
	env->ReleaseStringChars(jword,word);
	return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlSymAdjustSylianRelation
 * Signature: (Ljava/lang/String;ILjava/lang/String;I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlSymAdjustSylianRelation
  (JNIEnv * env, jobject obj, jstring jprestr, jint prelen, jstring jtailstr, jint taillen)
{
	int ret = -1;
	jboolean isCopy = JNI_FALSE;
	const jchar* prestr = env->GetStringChars(jprestr, &isCopy);
	const jchar* tailstr = env->GetStringChars(jtailstr, &isCopy);
	ret = ipt_adjust_sylian_relation(iptcore, (PLUINT16*)prestr, prelen, (PLUINT16*)tailstr, taillen);
	env->ReleaseStringChars(jprestr,prestr);
	env->ReleaseStringChars(jtailstr,tailstr);
	return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetCangjieVer
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlGetCangjieVer
  (JNIEnv * env, jobject obj)
{
	int ret = -1;
	if (iptcore)
	{
		ret = ipt_get_cangjie_version(iptcore);
	}
	return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetZhiDaHaoVer
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlGetZhiDaHaoVer
  (JNIEnv * env, jobject obj)
{
	int ret = -1;
	if (iptcore)
	{
		ret = ipt_keyword_get_zhidahao_version(iptcore);
	}
	return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetZhuyinHzVer
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlGetZhuyinHzVer
  (JNIEnv * env, jobject obj)
{
	int ret = -1;
	if (iptcore)
	{
		ret = ipt_get_zhuyin_hz_version(iptcore);
	}
	return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetZhuyinCzVer
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlGetZhuyinCzVer
  (JNIEnv * env, jobject obj)
{
	int ret = -1;
	if (iptcore)
	{
		ret = ipt_get_zhuyin_cz_version(iptcore);
	}
	return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlHandWritingRecognizeAppand
 * Signature: ([SI)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlHandWritingRecognizeAppand
  (JNIEnv *env, jobject obj, jshortArray jpointData, jint inputType)
{
    int ret = -1;
	if(session && iptcore && jpointData)
	{
		jboolean isCopy = JNI_FALSE;
		jshort *pointData = env->GetShortArrayElements(jpointData, &isCopy);

		ret = ipt_hw_append_point(session, (s_Point_v2 *)pointData, inputType);

		env->ReleaseShortArrayElements(jpointData, pointData, JNI_ABORT);
	}
	return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlHandWritingVersion
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlHandWritingVersion
  (JNIEnv *env, jobject obj)
{
    int ret = -1;
	if(iptcore)
	{
		ret = ipt_hw_version(iptcore);
	}
	return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlHandWritingSetConfig
 * Signature: ([I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlHandWritingSetConfig
  (JNIEnv *env, jobject obj, jintArray configArray)
{
	
	jboolean isCopy = JNI_FALSE;
    jint ret = -1;
	if (iptcore && configArray)
	{
		jint *config = env->GetIntArrayElements(configArray, &isCopy );
		ret = ipt_core_config(iptcore, (void*)config, CONFIGTYPE_SET_HW);
		env->ReleaseIntArrayElements(configArray, config, JNI_ABORT);
	}
    return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlHandWritingGetConfig
 * Signature: ()[I
 */
JNIEXPORT jintArray JNICALL Java_com_baidu_input_PlumCore_PlHandWritingGetConfig
  (JNIEnv *env, jobject obj)
{
	jboolean isCopy = JNI_FALSE;
    jintArray configArray = NULL;
	if (iptcore)
	{
		configArray = (jintArray)env->NewIntArray(2);
		jint *config = env->GetIntArrayElements(configArray, &isCopy);
		ipt_core_config(iptcore, (void*)config, CONFIGTYPE_GET_HW);
		env->ReleaseIntArrayElements(configArray, config, JNI_ABORT);
	}
    return configArray;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlHandWritingEncodePoints
 * Signature: (I)[B
 */
JNIEXPORT jbyteArray JNICALL Java_com_baidu_input_HandWritingCore_PlHandWritingEncodePoints
  (JNIEnv *env, jobject obj, jint candIdx)
{
	jboolean isCopy = JNI_FALSE;
	jbyteArray encodesArray = NULL;
	PLUINT8* encodes = NULL;
	PLUINT32 encodesLen = 0;
	if (session)
	{
		encodes = ipt_hw_encode_point(session, candIdx, &encodesLen);

		if (encodes != NULL && encodesLen > 0)
		{
			encodesArray = env->NewByteArray(encodesLen);
			env->SetByteArrayRegion(encodesArray, 0, encodesLen, (const jbyte*)encodes);
		}
	}
	return encodesArray;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlHandWritingSetBsFilter
 * Signature: ([S)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlHandWritingSetBsFilter
  (JNIEnv *env, jobject obj, jshortArray jpointData)
{
    int ret = -1;
	if(session && jpointData)
	{
		jboolean isCopy = JNI_FALSE;
		jshort *pointData = env->GetShortArrayElements(jpointData, &isCopy);
		PLUINT16 unicode[128];
		memset(unicode, 0, 128 * sizeof(PLUINT16));
		ret = ipt_hw_set_bs_filter(session, (s_Point_v2 *)pointData, (PLUINT16*)unicode);

		env->ReleaseShortArrayElements(jpointData, pointData, JNI_ABORT);
	}
	return ret;
}

#ifdef KEYPAD
#ifdef IPT_APP_MAP
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKpAppendPoint
 * Signature: (IISSIBLjava/lang/String;I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlKpAppendPoint
  (JNIEnv * env, jobject obj, jint findType, jint pos, jshort px, jshort py, jint inputCase, jbyte input, jstring jappName, jint attribute)
{
	int ret = -1;
	if (session)
	{
		s_Point_v2 inPoint;
		memset(&inPoint, 0, sizeof(s_Point_v2));

		inPoint.x = px;
		inPoint.y = py;

		jboolean isCopy = JNI_FALSE;
		char* appName = (char*)env->GetStringUTFChars(jappName, &isCopy);

		ret = ipt_kp_append_point_app(session, findType, pos, &inPoint, inputCase, input, appName, attribute);

		env->ReleaseStringUTFChars(jappName, appName);
	}
	return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKpDeletePoint
 * Signature: (IIILjava/lang/String;I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlKpDeletePoint
  (JNIEnv * env, jobject obj, jint findType, jint pos, jint num, jstring jappName, jint attribute)
{
	int ret = -1;
	if (session)
	{
	    jboolean isCopy = JNI_FALSE;
        char* appName = (char*)env->GetStringUTFChars(jappName, &isCopy);

		ret = ipt_kp_delete_point_app(session, findType, pos, num, appName, attribute);

		env->ReleaseStringUTFChars(jappName, appName);
	}
	return ret;
}
#else
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKpAppendPoint
 * Signature: (IISSIBI)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlKpAppendPoint
  (JNIEnv * env, jobject obj, jint findType, jint pos, jshort px, jshort py, jint inputCase, jbyte input, jint context)
{
	int ret = -1;
	if (session)
	{
		s_Point_v2 inPoint;
		memset(&inPoint, 0, sizeof(s_Point_v2));

		inPoint.x = px;
		inPoint.y = py;
		ret = ipt_kp_append_point(session, findType, pos, &inPoint, inputCase, input, context);
	}
	return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKpDeletePoint
 * Signature: (IIII)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlKpDeletePoint
  (JNIEnv * env, jobject obj, jint findType, jint pos, jint num, jint context)
{
	int ret = -1;
	if (session)
	{
		ret = ipt_kp_delete_point(session, findType, pos, num, context);
	}
	return ret;
}

#endif

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKpClean
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlKpClean
  (JNIEnv * env, jobject obj)
{
	int ret = -1;
	if (session)
	{
		ret = ipt_kp_clean(session);
	}
	return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKpSetRect
 * Signature: ([I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlKpSetRect
  (JNIEnv * env, jobject obj, jintArray jrect)
{
	jboolean isCopy = JNI_FALSE;
	int ret = -1;
	if (iptcore && jrect)
	{
		jint * rect = env->GetIntArrayElements(jrect, &isCopy);
		s_Rect_v2 key_rect;
		s_Point_v2 TL;
		s_Point_v2 BR;
		memset(&key_rect, 0, sizeof(s_Rect_v2));
		
		memset(&TL, 0, sizeof(s_Point_v2));
		TL.x = rect[0];
		TL.y = rect[1];
		key_rect.TL = TL;
		
		memset(&BR, 0, sizeof(s_Point_v2));
		BR.x = rect[2];
		BR.y = rect[3];
		key_rect.BM = BR;
		
		ret = ipt_kp_set_rect(iptcore, &key_rect);
		
		env->ReleaseIntArrayElements(jrect, rect, JNI_ABORT);
	}
	return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKpGetPoint
 * Signature: ()[S
 */
JNIEXPORT jshortArray JNICALL Java_com_baidu_input_PlumCore_PlKpGetPoint
  (JNIEnv * env, jobject obj)
{
#define MAX_POINT_SIZE 64
	int size;
	int i = 0;
	jshortArray resultArray = NULL;

	s_Point_v2 *outPoint = (s_Point_v2*)malloc(sizeof(s_Point_v2) * MAX_POINT_SIZE);
	memset(outPoint, 0, sizeof(s_Point_v2)*MAX_POINT_SIZE);

	if (outPoint != NULL)
	{
		size =  ipt_kp_get_point(session, outPoint);
		if (size > 0)
		{
			LOGI("PlKpGetPoint size=%d", size);

			jshort result[MAX_POINT_SIZE*2+1] = {0};
			result[0] = size;
			for(i=0; i< size; i++)
			{
				result[2*i + 1] = outPoint[i].x;
				result[2*i + 2] = outPoint[i].y;
				LOGI("PlKpGetPoint outPoint[%d].x=%d",i, outPoint[i].x);
				LOGI("PlKpGetPoint outPoint[%d].y=%d",i, outPoint[i].y);
			}

			resultArray = env->NewShortArray(size*2 + 1);
			env->SetShortArrayRegion(resultArray, 0, size*2 + 1, (const jshort*)result);
		}

		free(outPoint);
	}
	return resultArray;

}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKpAddKey
 * Signature: (B[I[I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlKpAddKey
  (JNIEnv * env, jobject obj, jbyte key, jintArray jvrect, jintArray jtrect)
{
	jboolean isCopy = JNI_FALSE;
    int ret = -1;
    if (iptcore && jvrect && jtrect)
    {
        jint * v_rect = env->GetIntArrayElements(jvrect, &isCopy);
        jint * t_rect = env->GetIntArrayElements(jtrect, &isCopy);
        s_Rect_v2 key_rect;
        s_Point_v2 TL;
        s_Point_v2 BR;
        memset(&key_rect, 0, sizeof(s_Rect_v2));

        memset(&TL, 0, sizeof(s_Point_v2));
        TL.x = v_rect[0];
        TL.y = v_rect[1];
        key_rect.TL = TL;

        memset(&BR, 0, sizeof(s_Point_v2));
        BR.x = v_rect[2];
        BR.y = v_rect[3];
        key_rect.BM = BR;

        s_Rect_v2 touch_rect;
        memset(&touch_rect, 0, sizeof(s_Rect_v2));

        memset(&TL, 0, sizeof(s_Point_v2));
        TL.x = t_rect[0];
        TL.y = t_rect[1];
        touch_rect.TL = TL;

        memset(&BR, 0, sizeof(s_Point_v2));
        BR.x = t_rect[2];
        BR.y = t_rect[3];
        touch_rect.BM = BR;

        ret = ipt_kp_add_key(iptcore, key, &key_rect, &touch_rect);

        env->ReleaseIntArrayElements(jvrect, v_rect, JNI_ABORT);
        env->ReleaseIntArrayElements(jtrect, t_rect, JNI_ABORT);
    }
    return ret;
}


/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetTouchedKey
 * Signature: (II)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlGetTouchedKey
  (JNIEnv * env, jobject obj, jint px, jint py)
{
    jint ret = 0;
    if (iptcore)
    {
        s_Point_v2 inPoint;
        memset(&inPoint, 0, sizeof(s_Point_v2));

        inPoint.x = px;
        inPoint.y = py;
        // todo 待接入thp功能默认设置为0，接入后传入对应的值
        ret = (jint) ipt_get_touched_key(session, &inPoint, 0, 0, 0);
    }
    return ret;
}
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCleanDynamicRects
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlCleanDynamicRects
  (JNIEnv * env, jobject obj)
{
    jint ret = 0;
    if (iptcore)
    {
        ret = (jint) ipt_clean_dynamic_rects(session);
    }
    return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlSetSkinToken
 * Signature: ([CII)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlSetSkinToken
  (JNIEnv * env, jobject obj, jcharArray skin_token_array, jint len, jint phone_state)
{
    jint ret = -1;
    if (iptcore)
    {
        jboolean isCopy = JNI_FALSE;
        jchar *skin_token = env->GetCharArrayElements(skin_token_array, &isCopy);

        char skin_token2[len];
        for (int i = 0; i < len; i++)
        {
            skin_token2[i] = (char) skin_token[i];
        }

        ret = (jint) ipt_set_skin_token(iptcore, (char*)skin_token2, (PLUINT16) (len), (PLUINT16)phone_state);
        env->ReleaseCharArrayElements(skin_token_array, skin_token, JNI_ABORT);
	}
	return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlUsrTouchExportFreshData
 * Signature: ()Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_baidu_input_PlumCore_PlUsrTouchExportFreshData
  (JNIEnv * env, jobject obj)
{
    jstring result = NULL;
    if (iptcore)
    {
        PLUINT16 max_json_len = 2048;
        PLUINT16 out_buf[max_json_len];
        memset(out_buf, 0, max_json_len * sizeof(PLUINT16));
        jint ret = (jint) ipt_usr_touch_export_fresh_data(iptcore, out_buf, max_json_len);
        if (ret > 0)
        {
            // 首选获取字符串的长度
            int slen = 0;
            for (int i = 0; i < max_json_len; i++) {
                // 如果没有遇到终止符
                if (out_buf[i] != 0) {
                    slen++;
                }
            }

            jboolean isCopy = JNI_FALSE;
            jcharArray outputarr = env->NewCharArray(slen + 1);
            jchar* output = env->GetCharArrayElements(outputarr, &isCopy);
            for (int i = 0; i < slen; i++) {
                if ((out_buf[i] & 0x0000) != 0) {
                    output[i] = (jchar) ' ';
                    continue;
                }
                output[i] = (jchar) out_buf[i];
                // LOGI("PlUsrTouchExportFreshData: %d", out_buf[i]);
            }
            output[slen] = (jchar) '\0';

            result = env->NewString((jchar*) output, slen);
            env->ReleaseCharArrayElements(outputarr, output, JNI_ABORT);
            env->DeleteLocalRef(outputarr);
        } else {
            LOGI("PlUsrTouchExportFreshData ret : %d", ret);
        }
	}
	return result;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlUsrTouchExportFreshData
 * Signature: ()Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_baidu_input_PlumCore_PlExportMisKeyForTrace
  (JNIEnv * env, jobject obj)
{
    jstring result = NULL;
    if (iptcore)
    {
        memset(g_string_buff, 0, STRING_BUFF_LEN * sizeof(PLUINT16));
        jint ret = (jint) ipt_export_mis_key_for_trace(session, g_string_buff, STRING_BUFF_LEN);

        if (ret > 0)
        {
            result = convert_wchar_to_jstring(env, g_string_buff);
        }
	}
	return result;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKpQueryIecTip
 * Signature: (I[B[B)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlKpQueryIecTip
  (JNIEnv * env, jobject obj, jint idx, jbyteArray jiec_str, jbyteArray jiec_info)
{
	int ret = -1;
	if (session && jiec_str && jiec_info && idx >= 0)
	{
#ifdef SET_ARRAY_WITH_INTERFACE
		int size1 = env->GetArrayLength(jiec_str);
		int size2 = env->GetArrayLength(jiec_str);
		if (size1 == 64 && size2 == 64)
		{
			char iec_str[64] = {0};
			PLBYTE iec_info[64] = {0};

			ret = ipt_query_get_iec_tip(session, idx, (char*)iec_str, (PLBYTE*)iec_info);

			env->SetByteArrayRegion(jiec_str, 0, 64, (const jbyte*)iec_str);
			env->SetByteArrayRegion(jiec_info, 0, 64, (const jbyte*)iec_info);
		}
#else
		jboolean isCopy = JNI_FALSE;
		jbyte* iec_str = env->GetByteArrayElements(jiec_str, &isCopy);
		jbyte* iec_info = env->GetByteArrayElements(jiec_info, &isCopy);
		
		ret = ipt_query_get_iec_tip(session, idx, (char*)iec_str, (PLBYTE*)iec_info);
		
		env->ReleaseByteArrayElements(jiec_info, iec_info, JNI_ABORT);
		env->ReleaseByteArrayElements(jiec_str, iec_str, JNI_ABORT);
#endif
	}
	return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKpQueryIecMohuStr
 * Signature: (I)Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_baidu_input_PlumCore_PlKpQueryIecMohuStr
  (JNIEnv * env, jobject obj, jint idx)
{
    jstring str = NULL;
	if (session && idx >= 0)
	{
		PLUINT16 unicode[128];
		memset(unicode, 0, 128 * sizeof(PLUINT16));

		int ret = ipt_query_get_iec_mohu_str(session, idx, (PLUINT16*)unicode);
		if (ret > 0)
		{
			str = convert_wchar_to_jstring(env, unicode);
		}
	}
    return str;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKpFindChCor
 * Signature: (I)Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_baidu_input_PlumCore_PlKpFindChCor
  (JNIEnv * env, jobject obj, jint idx)
{
    jstring str = NULL;
	if (session && idx >= 0)
	{
		PLUINT16 unicode[128];
		memset(unicode, 0, 128 * sizeof(PLUINT16));

		int ret = ipt_find_ch_cor(session, idx, (PLUINT16*)unicode);
		if (ret > 0)
		{
			str = convert_wchar_to_jstring(env, unicode);
		}
	}
    return str;
}
#endif//KEYPAD

int tim_subtract(struct timeval *result, struct timeval *x, struct timeval *y)

{

    int nsec;

    if ( x->tv_sec > y->tv_sec )

        return   -1;

    if ((x->tv_sec==y->tv_sec) && (x->tv_usec>y->tv_usec))

        return   -1;

    result->tv_sec = ( y->tv_sec-x->tv_sec );

    result->tv_usec = ( y->tv_usec-x->tv_usec );

    if (result->tv_usec<0)

    {

        result->tv_sec--;

        result->tv_usec+=1000000;

    }

    return   0;

}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCloudGetReqData
 * Signature: (Lcom/baidu/input/ime/cloudinput/manage/ICloudRequestData;Lcom/baidu/input/ime/cloudinput/ICloudSetting;Lcom/baidu/input/ime/cloudinput/ICloudInfo;[Lcom/baidu/input/ime/cloudinput/CloudLog;)V
 */
JNIEXPORT void JNICALL Java_com_baidu_input_PlumCore_PlCloudGetReqData
  (JNIEnv * env, jobject obj, jobject reqdata, jobject objsettings, jobject objinfo, jobjectArray logArray)
{
    //struct   timeval   start2,stop2,diff2;

    //gettimeofday(&start2,0);
	if(reqdata == NULL)
	{
		return;
	}
	jboolean isCopy = JNI_FALSE;
	jbyteArray reqArray = NULL;
	PLUINT8* reqs = NULL;
	PLUINT32 dataLen = 0;
	PLUINT32 delay_time = 0;
	PLUINT32 need_arrow = 0;
	s_cloud_setting cloudsettings = {0};
	static jmethodID get_input_type_method = 0;
	static jmethodID get_ret_cnt_method = 0;
	static jmethodID get_req_compress_type_method = 0;
    static jmethodID get_req_encry_type_method = 0;
	static jmethodID get_ret_compress_type_method = 0;
	static jmethodID get_ret_encry_type_method = 0;
	static jmethodID get_hw_block_compress_type_method = 0;
	static jmethodID get_input_id_method = 0;
	static jmethodID get_trigger_level_method = 0;
	static jmethodID get_yun_input_id = 0;
	static jmethodID get_lian_uni_method = 0;
	static jmethodID get_sug_source_id_cloud_settings_method = 0;
	static jmethodID get_check_id_method = 0;
	static jmethodID get_zj_forecast_method = 0;
	if(objsettings == NULL)
	{
		LOGI("null object");
		return;
	}
	jclass settingsCls = env->GetObjectClass(objsettings);
	
	if(get_input_type_method == 0)
        get_input_type_method = env->GetMethodID(settingsCls, "get_input_type","()C");
	if(get_ret_cnt_method == 0)
		get_ret_cnt_method = env->GetMethodID(settingsCls, "get_ret_cnt", "()C");
	if(get_req_compress_type_method == 0)
		get_req_compress_type_method = env->GetMethodID(settingsCls, "get_req_compress_type", "()B");
    if(get_req_encry_type_method == 0)
        get_req_encry_type_method = env->GetMethodID(settingsCls, "get_req_encry_type","()B");
	if(get_ret_compress_type_method == 0)
        get_ret_compress_type_method = env->GetMethodID(settingsCls, "get_ret_compress_type","()B");
	if(get_ret_encry_type_method == 0)
        get_ret_encry_type_method = env->GetMethodID(settingsCls, "get_ret_encry_type","()B");
	if(get_hw_block_compress_type_method == 0)
        get_hw_block_compress_type_method = env->GetMethodID(settingsCls, "get_hw_block_compress_type","()B");
	if(get_input_id_method == 0)
        get_input_id_method = env->GetMethodID(settingsCls, "get_input_id","()C");
	if(get_trigger_level_method == 0)
        get_trigger_level_method = env->GetMethodID(settingsCls, "get_trigger_level","()I");
	if(get_yun_input_id == 0)
        get_yun_input_id = env->GetMethodID(settingsCls, "get_yun_input_id","()I");
	if(get_lian_uni_method == 0)
        get_lian_uni_method = env->GetMethodID(settingsCls, "get_lian_uni","()Ljava/lang/String;");
	if(get_sug_source_id_cloud_settings_method == 0)
		get_sug_source_id_cloud_settings_method = env->GetMethodID(settingsCls, "get_sug_source_id", "()I");
	if(get_check_id_method == 0)
		get_check_id_method = env->GetMethodID(settingsCls, "get_check_id", "()I");
    if(get_zj_forecast_method == 0)
        get_zj_forecast_method = env->GetMethodID(settingsCls, "getZjForecast", "()I");
	cloudsettings.input_type = (PLUINT16)env->CallCharMethod(objsettings, get_input_type_method);
	cloudsettings.ret_cnt = (PLUINT16)env->CallCharMethod(objsettings, get_ret_cnt_method);
	cloudsettings.req_compress_type = (PLUINT8)env->CallByteMethod(objsettings, get_req_compress_type_method);
	cloudsettings.req_encry_type = (PLUINT8)env->CallByteMethod(objsettings, get_req_encry_type_method);
	cloudsettings.ret_compress_type = (PLUINT8)env->CallByteMethod(objsettings, get_ret_compress_type_method);
	cloudsettings.ret_encry_type = (PLUINT8)env->CallByteMethod(objsettings, get_ret_encry_type_method);
	cloudsettings.hw_block_compress_type = (PLUINT16)env->CallByteMethod(objsettings, get_hw_block_compress_type_method);
	cloudsettings.input_id = (PLUINT16)env->CallCharMethod(objsettings, get_input_id_method);
	cloudsettings.trigger_level = (PLUINT32)env->CallIntMethod(objsettings, get_trigger_level_method);
	cloudsettings.is_edit_filt = (PLUINT32)env->CallIntMethod(objsettings, get_yun_input_id);
	jstring lian = (jstring)(env->CallObjectMethod(objsettings, get_lian_uni_method));
	LOGI("Java_com_baidu_input_PlumCore_PlCloudGetReqData 1");
	const jchar *tmpUnicode = NULL;
	if(lian == NULL)
	{
		LOGI("Java_com_baidu_input_PlumCore_PlCloudGetReqData 2");
		cloudsettings.lian_uni = NULL;
		cloudsettings.lian_uni_len = 0;
	}
	else
	{
		LOGI("Java_com_baidu_input_PlumCore_PlCloudGetReqData 3");
		int size = env->GetStringLength(lian);
		LOGI("Java_com_baidu_input_PlumCore_PlCloudGetReqData 4");
		if(size > 0)
		{
			LOGI("Java_com_baidu_input_PlumCore_PlCloudGetReqData 5");
			tmpUnicode = env->GetStringChars(lian,&isCopy);
			LOGI("Java_com_baidu_input_PlumCore_PlCloudGetReqData 6");
			cloudsettings.lian_uni = (PLUINT16*)tmpUnicode;
			LOGI("Java_com_baidu_input_PlumCore_PlCloudGetReqData 7");
			cloudsettings.lian_uni_len = size;
			LOGI("Java_com_baidu_input_PlumCore_PlCloudGetReqData 8");
		}
		else
		{
			cloudsettings.lian_uni = NULL;
			cloudsettings.lian_uni_len = 0;
		}
	}
	cloudsettings.sug_source_id = (PLUINT32)env->CallIntMethod(objsettings, get_sug_source_id_cloud_settings_method);
	cloudsettings.check_id = (PLUINT32)env->CallIntMethod(objsettings, get_check_id_method);
	cloudsettings.need_zj_forcast = (PLUINT32)env->CallIntMethod(objsettings, get_zj_forecast_method);
	//struct   timeval   stop3,diff3;
	//gettimeofday(&stop3,0);

    //tim_subtract(&diff3,&start2,&stop3);
    //LOGI("time setting: %d", diff3.tv_usec);
	//LOGI("Java_com_baidu_input_PlumCore_PlCloudGetReqData 9");
    s_phone_info phone_info = {0};
	static jmethodID get_cuid_method = 0;
	static jmethodID get_oid_method = 0;
	static jmethodID get_aid_method = 0;
	static jmethodID get_cname_method = 0;
	static jmethodID get_input_ver_method = 0;
    static jmethodID get_app_name_method = 0;
	static jmethodID get_channel_method = 0;
	static jmethodID get_city_method = 0;
	static jmethodID get_net_type_method = 0;
	static jmethodID get_screen_width_method = 0;
	static jmethodID get_screen_height_method = 0;
	static jmethodID get_log_method = 0;
	static jmethodID get_json_buf_method = 0;
	static jmethodID get_bd_exist_method = 0;
	if(objinfo == NULL)
	{
		LOGI("null object");
		return;
	}
	jclass infoCls = env->GetObjectClass(objinfo);
	if (get_oid_method == 0) {
	    get_oid_method = env->GetMethodID(infoCls, "get_oid","()Ljava/lang/String;");
	}
	if (get_aid_method == 0) {
	    get_aid_method = env->GetMethodID(infoCls, "get_aid","()Ljava/lang/String;");
    }
	if(get_cuid_method == 0)
        get_cuid_method = env->GetMethodID(infoCls, "get_cuid","()Ljava/lang/String;");
	if(get_cname_method == 0)
		get_cname_method = env->GetMethodID(infoCls, "get_cname", "()Ljava/lang/String;");
	if(get_input_ver_method == 0)
		get_input_ver_method = env->GetMethodID(infoCls, "get_input_ver", "()Ljava/lang/String;");
    if(get_app_name_method == 0)
        get_app_name_method = env->GetMethodID(infoCls, "get_app_name","()Ljava/lang/String;");
	if(get_channel_method == 0)
		get_channel_method = env->GetMethodID(infoCls, "get_channel","()Ljava/lang/String;");
	if(get_city_method == 0)
		get_city_method = env->GetMethodID(infoCls, "get_city","()Ljava/lang/String;");
	if(get_net_type_method == 0)
        get_net_type_method = env->GetMethodID(infoCls, "get_net_type","()I");
	if(get_screen_width_method == 0)
        get_screen_width_method = env->GetMethodID(infoCls, "get_screen_width","()C");
	if(get_screen_height_method == 0)
        get_screen_height_method = env->GetMethodID(infoCls, "get_screen_height","()C");
	if(get_log_method == 0)
		get_log_method = env->GetMethodID(infoCls, "get_log","()[B");
	if(get_json_buf_method == 0)
	    get_json_buf_method = env->GetMethodID(infoCls, "get_json_buf","()[B");
	if (get_bd_exist_method == 0)
        get_bd_exist_method = env->GetMethodID(infoCls, "get_bd_exist","()I");

    jstring oid = (jstring)(env->CallObjectMethod(objinfo, get_oid_method));
	if (oid == NULL) {
		phone_info.oaid = NULL;
	} else {
		phone_info.oaid = (char*)env->GetStringUTFChars(oid, &isCopy);
	}
	jstring aid = (jstring)(env->CallObjectMethod(objinfo, get_aid_method));
    if (aid == NULL) {
    	phone_info.cuid3 = NULL;
    } else {
    	phone_info.cuid3 = (char*)env->GetStringUTFChars(aid, &isCopy);
    }
	jstring cuid = (jstring)(env->CallObjectMethod(objinfo, get_cuid_method));
	if(cuid == NULL)
	{
		phone_info.cuid = NULL;
	}
	else
	{
		phone_info.cuid = (char*)env->GetStringUTFChars(cuid, &isCopy);
	}
	jstring cname = (jstring)(env->CallObjectMethod(objinfo, get_cname_method));
	if(cname == NULL)
	{
		phone_info.cname = NULL;
	}
	else
	{
		phone_info.cname = (char*)env->GetStringUTFChars(cname, &isCopy);
	}
	jstring input_ver = (jstring)(env->CallObjectMethod(objinfo, get_input_ver_method));
	if(input_ver == NULL)
	{
		phone_info.input_ver = NULL;
	}
	else
	{
		phone_info.input_ver = (char*)env->GetStringUTFChars(input_ver, &isCopy);
	}
	jstring app_name = (jstring)(env->CallObjectMethod(objinfo, get_app_name_method));
	if(app_name == NULL)
	{
		phone_info.app_name = NULL;
	}
	else
	{
		phone_info.app_name = (char*)env->GetStringUTFChars(app_name, &isCopy);
	}
	jstring channel = (jstring)(env->CallObjectMethod(objinfo, get_channel_method));
	if(channel == NULL)
	{
		phone_info.channel = NULL;
	}
	else
	{
		phone_info.channel = (char*)env->GetStringUTFChars(channel, &isCopy);
	}
	jstring city = (jstring)(env->CallObjectMethod(objinfo, get_city_method));
	if(city == NULL)
	{
		phone_info.city = NULL;
	}
	else
	{
		phone_info.city = (char*)env->GetStringUTFChars(city, &isCopy);
	}
	phone_info.net_type = (int)env->CallIntMethod(objinfo, get_net_type_method);
	phone_info.exist_mb_pkg = (int)env->CallIntMethod(objinfo, get_bd_exist_method);
	phone_info.screen_width = (PLUINT16)env->CallCharMethod(objinfo, get_screen_width_method);
	phone_info.screen_height = (PLUINT16)env->CallCharMethod(objinfo, get_screen_height_method);
	jbyteArray log = (jbyteArray)(env->CallObjectMethod(objinfo, get_log_method));
	if(log == NULL)
	{
		phone_info.log = NULL;
		phone_info.log_len = 0;
	}
	else
	{
	    phone_info.log = (PLUINT8*)env->GetByteArrayElements(log, &isCopy);
		phone_info.log_len = env->GetArrayLength(log);
	}

	jbyteArray json_buf = (jbyteArray)(env->CallObjectMethod(objinfo, get_json_buf_method));
	if(json_buf == NULL)
    {
    	phone_info.json_buf = NULL;
    	phone_info.json_buf_len = 0;
    }
    else
    {
        phone_info.json_buf = (PLUINT8*)env->GetByteArrayElements(json_buf, &isCopy);
    	phone_info.json_buf_len = env->GetArrayLength(json_buf);

    }

	//struct   timeval   stop4,diff4;
    //gettimeofday(&stop4,0);

    //tim_subtract(&diff4,&stop3,&stop4);
    //LOGI("time info: %d", diff4.tv_usec);
	s_cloud_sug_log* sug_log = NULL;
	int sug_log_len = 0;
	jobjectArray pkg_names = NULL;
	jobjectArray sug_words = NULL;
	jobjectArray input_inlines = NULL;
	if(logArray != NULL)
	{
		sug_log_len = env->GetArrayLength(logArray);
		sug_log = (s_cloud_sug_log*)malloc(sizeof(s_cloud_sug_log)*sug_log_len);
		static jmethodID get_sug_log_type_method = 0;
		static jmethodID get_sug_input_id_method = 0;
		static jmethodID get_click_type_method = 0;
		static jmethodID get_sug_source_id_method = 0;
		static jmethodID get_pkg_name_method = 0;
		static jmethodID get_sug_word_method = 0;
		static jmethodID get_input_inline_method = 0;
		int index = 0;
		jclass objClass = (env)->FindClass("java/lang/String");
		pkg_names = env->NewObjectArray(sug_log_len, objClass, NULL);
	    sug_words = env->NewObjectArray(sug_log_len, objClass, NULL);
		input_inlines = env->NewObjectArray(sug_log_len, objClass, NULL);
		for(index = 0; index < sug_log_len; index++)
		{
			jobject element = env->GetObjectArrayElement(logArray, index);
			if(element == NULL)
			{
				LOGI("null object");
				return;
			}
			jclass customCls = env->GetObjectClass(element);
			s_cloud_sug_log* sug = sug_log + index;
			if(get_sug_log_type_method == 0)
				get_sug_log_type_method = env->GetMethodID(customCls, "get_sug_log_type", "()I");
			if(get_sug_input_id_method == 0)
				get_sug_input_id_method = env->GetMethodID(customCls, "get_input_id", "()I");
			if(get_click_type_method == 0)
				get_click_type_method = env->GetMethodID(customCls, "get_click_type", "()I");
			if(get_sug_source_id_method == 0)
				get_sug_source_id_method = env->GetMethodID(customCls, "get_sug_source_id", "()I");
			if(get_pkg_name_method == 0)
				get_pkg_name_method = env->GetMethodID(customCls, "get_pkg_name", "()Ljava/lang/String;");
			if(get_sug_word_method == 0)
				get_sug_word_method = env->GetMethodID(customCls, "get_sug_word", "()Ljava/lang/String;");
			if(get_input_inline_method == 0)
				get_input_inline_method = env->GetMethodID(customCls, "get_input_inline", "()Ljava/lang/String;");
			sug->sug_log_type = env->CallIntMethod(element, get_sug_log_type_method);
			LOGI("get_sug_log_type: %d", sug->sug_log_type);
			sug->input_id = env->CallIntMethod(element, get_sug_input_id_method);
			LOGI("get_input_id: %d", sug->input_id);
			sug->click_type = env->CallIntMethod(element, get_click_type_method);
			LOGI("get_click_type: %d", sug->click_type);
			sug->sug_source_id = env->CallIntMethod(element, get_sug_source_id_method);
			LOGI("get_sug_source_id: %d", sug->sug_source_id);
			jstring pkg_name = (jstring)(env->CallObjectMethod(element, get_pkg_name_method));
			if(pkg_name == NULL)
			{
				sug->pkg_name = NULL;
				env->SetObjectArrayElement(pkg_names, index, NULL);
			}
			else
			{
				sug->pkg_name = (char*)env->GetStringUTFChars(pkg_name, &isCopy);
				env->SetObjectArrayElement(pkg_names, index, pkg_name);
			}
			LOGI("get_pkg_name: %s", sug->pkg_name);
			jstring sug_word = (jstring)(env->CallObjectMethod(element, get_sug_word_method));
			if(sug_word == NULL)
			{
				sug->sug_word = NULL;
				sug->sug_word_len = 0;
				env->SetObjectArrayElement(sug_words, index, NULL);
			}
			else
			{
				int size = env->GetStringLength(sug_word);
				if(size > 0)
				{
					const jchar *tmpSugword = env->GetStringChars(sug_word, &isCopy);
					sug->sug_word = (PLUINT16*)tmpSugword;
					sug->sug_word_len = size;
					env->SetObjectArrayElement(sug_words, index, sug_word);
				}
				else
				{
					sug->sug_word = NULL;
					sug->sug_word_len = 0;
					env->SetObjectArrayElement(sug_words, index, NULL);
				}
			}
			LOGI("get_sug_word: %s", sug->sug_word);
			jstring input_inline = (jstring)(env->CallObjectMethod(element, get_input_inline_method));
			if(input_inline == NULL)
			{
				sug->input_inline = NULL;
				sug->input_inline_len = 0;
				env->SetObjectArrayElement(input_inlines, index, NULL);
			}
			else
			{
				int size = env->GetStringLength(input_inline);
				if(size > 0)
				{
					const jchar *tmpInputline = env->GetStringChars(input_inline, &isCopy);
					sug->input_inline = (PLUINT16*)tmpInputline;
					sug->input_inline_len = size;
					env->SetObjectArrayElement(input_inlines, index, input_inline);
				}
				else
				{
					sug->input_inline = NULL;
					sug->input_inline_len = 0;
					env->SetObjectArrayElement(input_inlines, index, NULL);
				}
			}
			LOGI("get_input_inline: %s", sug->input_inline);
			env->DeleteLocalRef(pkg_name);
			env->DeleteLocalRef(sug_word);
			env->DeleteLocalRef(input_inline);
			env->DeleteLocalRef(element);
			env->DeleteLocalRef(customCls);
		}
	}

	//struct   timeval   stop5,diff5;
    //gettimeofday(&stop5,0);

    //tim_subtract(&diff5,&stop4,&stop5);
    //LOGI("time sug: %d", diff5.tv_usec);

	if (session)
	{
        //struct   timeval   start,stop,diff;

        LOGI("phone_info.oaid: %s, phone_info.cuid3: %s", phone_info.oaid, phone_info.cuid3);
        //gettimeofday(&start,0);
		reqs = ipt_cloud_get_req_data(session, &cloudsettings, &phone_info, sug_log, sug_log_len, &dataLen,
		&delay_time, &need_arrow);
		//gettimeofday(&stop,0);

        //tim_subtract(&diff,&start,&stop);
		//LOGI("time: %d", diff.tv_usec);
		//LOGI("Java_com_baidu_input_PlumCore_PlCloudGetReqData 11");
		if (reqs != NULL && dataLen > 0)
		{
			reqArray = env->NewByteArray(dataLen);
			env->SetByteArrayRegion(reqArray, 0, dataLen, (const jbyte*)reqs);
		}
		jclass objClass2 = (env)->GetObjectClass(reqdata);
		static jmethodID set_req_content = env->GetMethodID(objClass2, "setReqContent","([B)V");
		static jmethodID set_delay_time = env->GetMethodID(objClass2, "setDelayTime","(I)V");
		static jmethodID set_need_arrow = env->GetMethodID(objClass2, "setNeedArrow","(I)V");
		env->CallVoidMethod(reqdata, set_req_content, reqArray);
		env->CallVoidMethod(reqdata, set_delay_time, delay_time);
		env->CallVoidMethod(reqdata, set_need_arrow, need_arrow);
		env->DeleteLocalRef(objClass2);
	}
	int idx;
	for(idx = 0; idx < sug_log_len; idx++)
	{
		s_cloud_sug_log* sug = sug_log + idx;
	
		if(pkg_names != NULL)
		{
			jstring pkg = (jstring)env->GetObjectArrayElement(pkg_names, idx);
			if(pkg != NULL)
			{
				env->ReleaseStringUTFChars(pkg, sug->pkg_name);
			}
		}
		if(sug_words != NULL)
		{
			jstring sug_word = (jstring)env->GetObjectArrayElement(sug_words, idx);
			if(sug_word != NULL)
			{
				env->ReleaseStringChars(sug_word, sug->sug_word);
			}
		}
		if(input_inlines != NULL)
		{
			jstring input_inline = (jstring)env->GetObjectArrayElement(input_inlines, idx);
			if(input_inline != NULL)
			{
				env->ReleaseStringChars(input_inline, sug->input_inline);
			}
		}
	}
	if(lian && tmpUnicode)
	{
		env->ReleaseStringChars(lian,tmpUnicode);
	}
	if (oid) {
    	env->ReleaseStringUTFChars(oid, phone_info.oaid);
    }
    if (aid) {
        env->ReleaseStringUTFChars(aid, phone_info.cuid3);
    }
	if(cuid)
	{
		env->ReleaseStringUTFChars(cuid, phone_info.cuid);
	}
	if(cname)
	{
		env->ReleaseStringUTFChars(cname, phone_info.cname);
	}
	if(input_ver)
	{
		env->ReleaseStringUTFChars(input_ver, phone_info.input_ver);
	}
	if(app_name)
	{
		env->ReleaseStringUTFChars(app_name, phone_info.app_name);
	}
	if(channel)
	{
		env->ReleaseStringUTFChars(channel, phone_info.channel);
	}
	if(city)
	{
		env->ReleaseStringUTFChars(city, phone_info.city);
	}
	if(log)
	{
		env->ReleaseByteArrayElements(log, (jbyte*)phone_info.log, JNI_ABORT );
	}
	env->DeleteLocalRef(pkg_names);
	env->DeleteLocalRef(sug_words);
	env->DeleteLocalRef(input_inlines);
	env->DeleteLocalRef(lian);
	env->DeleteLocalRef(cuid);
	env->DeleteLocalRef(oid);
	env->DeleteLocalRef(aid);
	env->DeleteLocalRef(cname);
	env->DeleteLocalRef(input_ver);
	env->DeleteLocalRef(app_name);
	env->DeleteLocalRef(channel);
	env->DeleteLocalRef(city);
	env->DeleteLocalRef(settingsCls);
	env->DeleteLocalRef(infoCls);
	//gettimeofday(&stop2,0);

    //tim_subtract(&diff2,&start2,&stop2);
    //LOGI("time delta: %d", diff2.tv_usec);
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlFindCloudZj
 * Signature: (Ljava/lang/String;)Lcom/baidu/input/ime/cloudinput/CloudForecastOutput;
 */
JNIEXPORT jobject JNICALL Java_com_baidu_input_PlumCore_PlFindCloudZj
  (JNIEnv * env, jobject obj, jstring junicode)
{
    jboolean isCopy = JNI_FALSE;
    const PLUINT16* uni = NULL;
    int len = 0;
    if (junicode != NULL)
    {
        uni = (const PLUINT16*) env->GetStringChars(junicode, &isCopy);
        len = env->GetStringLength(junicode);
    }
    const s_cloud_forecast_output* cloud_forecast_output = ipt_find_cloud_zj(session, uni, len);
    if (cloud_forecast_output == NULL)
    {
    	return NULL;
    }
    jclass cls = env->FindClass("com/baidu/input/ime/cloudinput/CloudForecastOutput");
    jmethodID jconstructorId = env->GetMethodID(cls, "<init>","()V");
    jobject jforecast_output = env->NewObject(cls, jconstructorId);
    jmethodID jmethod = env->GetMethodID(cls, "set", "(Ljava/lang/String;IILjava/lang/String;)V");
    jstring cand = NULL;
    if (cloud_forecast_output->cand_len >= 0)
    {
    	cand = env->NewString(cloud_forecast_output->cand, cloud_forecast_output->cand_len);
    }
    jstring commit = NULL;
    if (cloud_forecast_output->commit_cand_len >= 0)
    {
    	commit = env->NewString(cloud_forecast_output->commit_cand, cloud_forecast_output->commit_cand_len);
    }
    jint cand_color_len = cloud_forecast_output->cand_color_len;
    jint cur_match_len = cloud_forecast_output->cur_match_len;
    env->CallVoidMethod(jforecast_output, jmethod, cand, cand_color_len, cur_match_len, commit);
    if (junicode != NULL)
    {
        env->ReleaseStringChars(junicode, uni);
    }
    return jforecast_output;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetSentenceMatchLen
 * Signature: (Ljava/lang/String;Ljava/lang/String;)I;
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlGetSentenceMatchLen
  (JNIEnv * env, jobject obj, jstring sen, jstring code)
{
	if (sen == NULL || code == NULL)
	{
		return -1;
	}
    jboolean is_copy = JNI_FALSE;
    const jchar* jchar_sen = env->GetStringChars(sen, &is_copy);
    const char* char_code = env->GetStringUTFChars(code, &is_copy);
    int len = env->GetStringLength(sen);
    PLUINT16 output[len + 1];
    jchar_to_wchar(output, jchar_sen, len);

    jint ret = -1;
    if (session)
    {
    	ret = ipt_get_sentence_match_len(session, output, char_code);
    }
    env->ReleaseStringChars(sen, jchar_sen);
    env->ReleaseStringUTFChars(code, char_code);
    return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCloudZjClean
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_baidu_input_PlumCore_PlCloudZjClean
  (JNIEnv * env, jobject obj)
{
    if (session)
    {
        ipt_cloud_zj_clean(session);
    }
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCloudInputBuf
 * Signature: ([BIILcom/baidu/input/ime/cloudinput/manage/ICloudDataManager;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlCloudInputBuf
  (JNIEnv * env, jobject obj, jbyteArray bufArray, jint bufLen, jint candRedupCnt, jobject customObj)
{
	jboolean isCopy = JNI_FALSE;
	PLUINT32 white_ver = 0;
	PLUINT32 check_id = 0;
	PLUINT32 hot_ver = 0;
	int ret = 0;
	if (session)
	{
		jbyte* receive_buf = env->GetByteArrayElements(bufArray, &isCopy);
		ret = ipt_cloud_input_buf(session, (PLBYTE*)receive_buf, bufLen, candRedupCnt, &white_ver, &check_id, &hot_ver);
		LOGI("check_id: %x", check_id);
		env->ReleaseByteArrayElements(bufArray, receive_buf, JNI_ABORT );
		jclass customCls = env->GetObjectClass(customObj);
		static jmethodID set_white_ver = env->GetMethodID(customCls, "set_white_ver","(I)V");
		static jmethodID set_check_id = env->GetMethodID(customCls, "set_check_id", "(I)V");
		env->CallVoidMethod(customObj, set_white_ver, white_ver);
		env->CallVoidMethod(customObj, set_check_id, check_id);
		env->DeleteLocalRef(customCls);
	}
	return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCloudOutput
 * Signature: ()[Lcom/baidu/input/ime/cloudinput/CloudOutputService;
 */
JNIEXPORT jobjectArray JNICALL Java_com_baidu_input_PlumCore_PlCloudOutput
  (JNIEnv * env, jobject obj)
{
	jboolean isCopy = JNI_FALSE;
	PLUINT32 output_cnt = 0;
	jobjectArray cloudOutputArray;
	if (session)
	{
		s_cloud_output_service* cloud_ouputs = ipt_cloud_service(session, &output_cnt);
		if(cloud_ouputs == NULL)
		{
			LOGI("array null");
		}
		jclass objClass = (env)->FindClass("com/baidu/input/ime/cloudinput/CloudOutputService");
		LOGI("output_cnt: %x", output_cnt);
		cloudOutputArray = (env)->NewObjectArray(output_cnt, objClass, 0);
		static jmethodID constructor_method = 0;
		static jmethodID set_service_type = 0;
		static jmethodID set_uni = 0;
		static jmethodID set_img = 0;
		static jmethodID set_img_url = 0;
		static jmethodID set_goto_url = 0;
		static jmethodID set_img_rect = 0;
		static jmethodID set_show_flag = 0;
		static jmethodID set_uid = 0;
		static jmethodID set_max_show_num = 0;
		if(constructor_method == 0)
			constructor_method = env->GetMethodID(objClass, "<init>","()V");
		if(set_service_type == 0)
			set_service_type = env->GetMethodID(objClass, "set_service_type","(I)V");
		if(set_uni == 0)
			set_uni = env->GetMethodID(objClass, "set_uni","(Ljava/lang/String;)V");
		if(set_img == 0)
			set_img = env->GetMethodID(objClass, "set_img","(Ljava/lang/String;)V");
		if(set_img_url == 0)
			set_img_url = env->GetMethodID(objClass, "set_img_url","([B)V");
		if(set_goto_url == 0)
			set_goto_url = env->GetMethodID(objClass, "set_goto_url","([B)V");
		if(set_img_rect == 0)
			set_img_rect = env->GetMethodID(objClass, "set_img_rect","([I)V");
		if(set_show_flag == 0)
			set_show_flag = env->GetMethodID(objClass, "set_show_flag","(B)V");
		if(set_uid == 0)
			set_uid = env->GetMethodID(objClass, "set_uid", "([B)V");
		if(set_max_show_num == 0)
			set_max_show_num = env->GetMethodID(objClass, "set_max_show_num", "(I)V");
		int index = 0;
		for(index = 0; index < output_cnt; index++)
		{
			jobject element = env->NewObject(objClass, constructor_method);
			s_cloud_output_service* cloud_output = cloud_ouputs + index;
			if(cloud_output == NULL)
			{
				LOGI("item null");
				break;
			}
			env->CallVoidMethod(element, set_service_type,cloud_output->service_type);
			jint uni_len = cloud_output->uni_len;
			LOGI("item uni_len: %x", uni_len);
			jstring uni = NULL;
			if(uni_len > 0)
			{
				uni = env->NewString((jchar*)cloud_output->uni, uni_len);
			}
			env->CallVoidMethod(element, set_uni, uni);
			jint img_len = cloud_output->content_len;
			LOGI("item img_len: %x", img_len);
			//jbyteArray img = NULL;
			jstring img = NULL;
			if(img_len > 0)
			{
				if(cloud_output->service_type == CLOUD_OUTPUT_SERVICE_TYPE_AI_INTENT
				            || cloud_output->service_type == CLOUD_OUTPUT_SERVICE_TYPE_JSON)
				{
					char temps[img_len + 1];
					int i;
					for(i = 0; i < img_len; i++)
					{
						temps[i] = cloud_output->content[i];
					}
					temps[i] = '\0';
					img = env->NewStringUTF((char*)temps);
				}
				else
				{
				    img = env->NewString((jchar*)cloud_output->content, img_len / 2);
				}
				//img = (jbyteArray)env->NewByteArray(img_len);
				//int i;
				//for(i = 0; i < img_len; i++)
				//{
				//	setByteArrayElement(env, img, 0, cloud_output->img[i]);
				//}
				//img = env->NewString((jchar*)cloud_output->img, img_len / 2);
			}
			env->CallVoidMethod(element, set_img, img);
			jint img_url_len = cloud_output->img_url_len;
			LOGI("item img_url_len: %x", img_url_len);
			//jstring img_url = NULL;
			jbyteArray img_url = NULL;
			if(img_url_len > 0)
			{
				//jboolean isCopy = JNI_FALSE;
				// temp = env->NewString((jchar*)cloud_output->img_url, img_url_len / 2);
				//const char* temps = env->GetStringUTFChars(temp, &isCopy);
				//img_url = env->NewStringUTF((char*)temps);
				//img_url = env->NewStringUTF((char*)cloud_output->img_url);
				img_url = (jbyteArray)env->NewByteArray(img_url_len);
				LOGI("init");
				env->SetByteArrayRegion(img_url, 0, img_url_len, (const jbyte*)cloud_output->img_url);
				LOGI("set");
			}
			env->CallVoidMethod(element, set_img_url, img_url);
			jint goto_url_len = cloud_output->goto_url_len;
			LOGI("item goto_url_len: %x", goto_url_len);
			//jstring goto_url = NULL;
			jbyteArray goto_url = NULL;
			if(goto_url_len > 0)
			{
				//goto_url = env->NewStringUTF((char*)cloud_output->goto_url);
				//jboolean isCopy = JNI_FALSE;
				//jstring temp = env->NewString((jchar*)cloud_output->goto_url, goto_url_len / 2);
				//const char* temps = env->GetStringUTFChars(temp, &isCopy);
				//goto_url = env->NewStringUTF((char*)temps);
				goto_url = (jbyteArray)env->NewByteArray(goto_url_len);
				LOGI("init");
				env->SetByteArrayRegion(goto_url, 0, goto_url_len, (const jbyte*)cloud_output->goto_url);
				LOGI("set");
			}
			env->CallVoidMethod(element, set_goto_url, goto_url);
			jint uid_len = cloud_output->uid_len;
			jbyteArray uid = NULL;
			if(uid_len > 0)
			{
				uid = (jbyteArray)env->NewByteArray(uid_len);
				env->SetByteArrayRegion(uid, 0, uid_len, (const jbyte*)cloud_output->uid);
			}
			env->CallVoidMethod(element, set_uid, uid);
			env->CallVoidMethod(element, set_max_show_num, cloud_output->max_show_num);
			LOGI("set to java");
			s_Rect_v2 rect = cloud_output->img_rect;
			s_Point_v2 TL = rect.TL;
			s_Point_v2 BM = rect.BM;
			LOGI("left: %x", cloud_output->img_rect.TL.x);
			LOGI("top: %x", cloud_output->img_rect.TL.y);
			LOGI("right: %x", cloud_output->img_rect.BM.x);
			LOGI("bottom: %x", cloud_output->img_rect.BM.y);
			LOGI("point left: %x", TL.x);
			LOGI("point top: %x", TL.y);
			LOGI("point right: %x", BM.x);
			LOGI("point bottom: %x", BM.y);
			//jint points[4] = {TL.x, TL.y, BM.x, BM.y};
			jintArray pointArray = (jintArray)env->NewIntArray(4);
			//env->SetIntArrayRegion(pointArray, 0, 4, points);
			setIntArrayElement(env, pointArray, 0, (jint) TL.x);
			setIntArrayElement(env, pointArray, 1, (jint) TL.y);
			setIntArrayElement(env, pointArray, 2, (jint) BM.x);
			setIntArrayElement(env, pointArray, 3, (jint) BM.y);
			//jint* points = env->GetIntArrayElements(pointArray, &isCopy);
			//LOGI("array left: %x", points[0]);
			//LOGI("array top: %x", points[1]);
			//LOGI("array right: %x", points[2]);
			//LOGI("array bottom: %x", points[3]);
			env->CallVoidMethod(element, set_img_rect, pointArray);
			env->CallVoidMethod(element, set_show_flag, cloud_output->show_type);
			env->SetObjectArrayElement(cloudOutputArray, index, element);
			env->DeleteLocalRef(pointArray);
			env->DeleteLocalRef(uni);
			env->DeleteLocalRef(img);
			env->DeleteLocalRef(img_url);
			env->DeleteLocalRef(goto_url);
			env->DeleteLocalRef(uid);
			env->DeleteLocalRef(element);
		}
		env->DeleteLocalRef(objClass);
	}
	return cloudOutputArray;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCloudSearch
 * Signature: ()[Lcom/baidu/input/ime/cloudinput/CloudOutputSearch;
 */
JNIEXPORT jobjectArray JNICALL Java_com_baidu_input_PlumCore_PlCloudSearch
  (JNIEnv * env, jobject obj)
{
	jboolean isCopy = JNI_FALSE;
	PLUINT32 output_cnt = 0;
	jobjectArray cloudOutputSearchArray;
	if (session)
	{
		s_cloud_output_search* cloud_ouput_searchs = ipt_cloud_search(session, &output_cnt);
		if(cloud_ouput_searchs == NULL)
		{
			LOGI("array null");
		}
		jclass objClass = (env)->FindClass("com/baidu/input/ime/cloudinput/CloudOutputSearch");
		LOGI("output_cnt: %x", output_cnt);
		cloudOutputSearchArray = (env)->NewObjectArray(output_cnt, objClass, 0);
		static jmethodID constructor_method = 0;
		static jmethodID set_int = 0;
		static jmethodID set_str = 0;
		if(constructor_method == 0)
			constructor_method = env->GetMethodID(objClass, "<init>","()V");
		if(set_int == 0)
			set_int = env->GetMethodID(objClass, "set_int","(IIII)V");
		if(set_str == 0)
			set_str = env->GetMethodID(objClass, "set_str","(Ljava/lang/String;Ljava/lang/String;)V");
		int index = 0;
		for(index = 0; index < output_cnt; index++)
		{
			jobject element = env->NewObject(objClass, constructor_method);
			s_cloud_output_search* cloud_output_search = cloud_ouput_searchs + index;
			if(cloud_output_search == NULL)
			{
				LOGI("item null");
				break;
			}
			env->CallVoidMethod(element, set_int,cloud_output_search->type, cloud_output_search->jump_tab, cloud_output_search->word_type, cloud_output_search->show_count);
			jint keyword_len = cloud_output_search->keyword_len;
			LOGI("item keyword_len: %x", keyword_len);
			jstring keyword = NULL;
			if(keyword_len > 0)
			{
				keyword = env->NewString((jchar*)cloud_output_search->keyword, keyword_len);
			}
			jint hint_len = cloud_output_search->hint_len;
			LOGI("item hint_len: %x", hint_len);
			jstring hint = NULL;
			if(hint_len > 0)
			{
				hint = env->NewString((jchar*)cloud_output_search->hint, hint_len);
			}
			env->CallVoidMethod(element, set_str, keyword, hint);
			env->SetObjectArrayElement(cloudOutputSearchArray, index, element);
			env->DeleteLocalRef(keyword);
			env->DeleteLocalRef(hint);
			env->DeleteLocalRef(element);
		}
		env->DeleteLocalRef(objClass);
	}
	return cloudOutputSearchArray;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlSugOutput
 * Signature: (I)Ljava/lang/Object;
 */
JNIEXPORT jobject JNICALL Java_com_baidu_input_PlumCore_PlSugOutput
  (JNIEnv *env, jobject obj, jint sugType)
{
	jboolean isCopy = JNI_FALSE;
	jobject sug;
	PLUINT32 data_len = 0;
	if (session)
	{
		if(sugType == CLOUD_OUTPUT_ITEM_TYPE_SUG_ACTION)
		{
			s_cloud_output_sug_action* sug_ac = (s_cloud_output_sug_action*)ipt_cloud_sug(session, sugType, &data_len);
			if(sug_ac == NULL)
			{
				LOGI("null object");
				return NULL;
			}
			jclass objClass = (env)->FindClass("com/baidu/input/ime/cloudinput/SugAction");
			static jmethodID constructor_method = 0;
			static jmethodID set_int_method = 0;
			static jmethodID set_str_method = 0;
			static jmethodID set_pkg_method = 0;
			if(constructor_method == 0)
				constructor_method = env->GetMethodID(objClass, "<init>","()V");
			LOGI("new object");
			if(set_int_method == 0)
				set_int_method = env->GetMethodID(objClass, "set_int","(IIIII)V");
			LOGI("set_int");
			if(set_str_method == 0)
				set_str_method = env->GetMethodID(objClass, "set_str","(Ljava/lang/String;Ljava/lang/String;)V");
			LOGI("set_command");
			if(set_pkg_method == 0)
				set_pkg_method = env->GetMethodID(objClass, "set_pkg","(ILjava/lang/String;[I)V");
			LOGI("set_pkg");
			sug = env->NewObject(objClass, constructor_method);
			env->CallVoidMethod(sug, set_int_method, sug_ac->action_type, sug_ac->sug_type, sug_ac->sug_source_id, sug_ac->ex_action, sug_ac->pkg_cnt);
			LOGI("call set_int");
			jint source_msg_len = sug_ac->source_msg_len;
			jstring source_msg = NULL;
			if(source_msg_len > 0)
			{
				source_msg = env->NewString((jchar*)sug_ac->source_msg, source_msg_len);
			}
			jint command_len = sug_ac->command_len;
			jstring command = NULL;
			if(command_len > 0)
			{
				//jstring temp = env->NewString((jchar*)sug_ac->command, command_len / 2);
				//const char* temps = env->GetStringUTFChars(temp, &isCopy);
				char temps[command_len + 1];
				LOGI("init success");
				int i;
				for(i = 0; i < command_len; i++)
				{
					temps[i] = sug_ac->command[i];
				}
				LOGI("assign success");
				temps[i] = '\0';
				LOGI("total success");
				command = env->NewStringUTF((char*)temps);
				LOGI("new string success");
				//env->ReleaseStringUTFChars(temp, temps);
				//env->DeleteLocalRef(temp);
				LOGI("command: %s", temps);
			}
			env->CallVoidMethod(sug, set_str_method, source_msg, command);
			LOGI("call set_command");
			jint pkg_cnt = sug_ac->pkg_cnt;
			s_cloud_output_pkg* pkgs = sug_ac->pkg_list;
			int index = 0;
			for(index = 0; index < pkg_cnt; index++)
			{
				s_cloud_output_pkg* pkg = pkgs + index;
				if(pkg == NULL)
				{
					break;
				}
				jint pkg_name_len = pkg->pkg_name_len;
				jstring pkg_name = NULL;
				if(pkg_name_len > 0)
				{
					//jstring temp = env->NewString((jchar*)pkg->pkg_name, pkg_name_len / 2);
					//const char* temps = env->GetStringUTFChars(temp, &isCopy);
					char temps[pkg_name_len + 1];
					LOGI("init success");
					int i;
					for(i = 0; i < pkg_name_len; i++)
					{
						temps[i] = pkg->pkg_name[i];
					}
					LOGI("assign success");
					temps[i] = '\0';
					LOGI("total success");
					pkg_name = env->NewStringUTF((char*)temps);
					//env->ReleaseStringUTFChars(temp, temps);
					//env->DeleteLocalRef(temp);
				}
				jint ver_cnt = pkg->ver_cnt;
				jintArray versionArray = (jintArray)env->NewIntArray(ver_cnt * 2);
				s_cloud_output_pkg_ver *ver_list = pkg->ver_list;
				if(ver_list != NULL)
				{
					int idx;
					for(idx = 0; idx < ver_cnt; idx++)
					{
						setIntArrayElement(env, versionArray, idx * 2, (jint) ver_list->min_ver);
						setIntArrayElement(env, versionArray, idx * 2 + 1, (jint) ver_list->max_ver);
					}
				}
				env->CallVoidMethod(sug, set_pkg_method, index, pkg_name, versionArray);
				LOGI("call set_pkg");
				env->DeleteLocalRef(pkg_name);
				env->DeleteLocalRef(versionArray);
			}
			LOGI("level 1");
			env->DeleteLocalRef(command);
			LOGI("level 2");
		}
		else if(sugType == CLOUD_OUTPUT_ITEM_TYPE_SUG_CARD)
		{
			s_cloud_output_sug_card* sug_card = (s_cloud_output_sug_card*)ipt_cloud_sug(session, sugType, &data_len);
			if(sug_card == NULL)
				return NULL;
			jclass objClass = (env)->FindClass("com/baidu/input/ime/cloudinput/CardInfo");
			static jmethodID constructor_method = 0;
			static jmethodID set_card_id_method = 0;
			static jmethodID set_str_method = 0;
			if(constructor_method == 0)
				constructor_method = env->GetMethodID(objClass, "<init>","()V");
			if(set_card_id_method == 0)
				set_card_id_method = env->GetMethodID(objClass, "set_card_id","(I)V");
			if(set_str_method == 0)
				set_str_method = env->GetMethodID(objClass, "set_str","(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V");
			sug = env->NewObject(objClass, constructor_method);
			env->CallVoidMethod(sug, set_card_id_method, sug_card->card_id);
			jint card_key_len = sug_card->card_key_len;
			jstring card_key = NULL;
			if(card_key_len > 0)
			{
				card_key = env->NewString((jchar*)sug_card->card_key, card_key_len);
			}
			jint title_len = sug_card->title_len;
			jstring title = NULL;
			if(title_len > 0)
			{
				title = env->NewString((jchar*)sug_card->title, title_len);
			}
			jint content1_len = sug_card->content1_len;
			jstring content1 = NULL;
			if(content1_len > 0)
			{
				content1 = env->NewString((jchar*)sug_card->content1, content1_len);
			}
			jint content2_len = sug_card->content2_len;
			jstring content2 = NULL;
			if(content2_len > 0)
			{
				content2 = env->NewString((jchar*)sug_card->content2, content2_len);
			}
			jint content3_len = sug_card->content3_len;
			jstring content3 = NULL;
			if(content3_len > 0)
			{
				content3 = env->NewString((jchar*)sug_card->content3, content3_len);
			}
			jint img_url_len = sug_card->img_url_len;
			jstring img_url = NULL;
			if(img_url_len > 0)
			{
				int len;
				for(len = 0; len < img_url_len; len++)
				{
					LOGI("img_url: %x", sug_card->img_url[len]);
				}
				//jstring temp = env->NewString((jchar*)sug_card->img_url, img_url_len / 2);
				//const char* temps = env->GetStringUTFChars(temp, &isCopy);
				char temps[img_url_len + 1];
				LOGI("init success");
				int i;
				for(i = 0; i < img_url_len; i++)
				{
					temps[i] = sug_card->img_url[i];
				}
				LOGI("assign success");
				temps[i] = '\0';
				img_url = env->NewStringUTF((char*)temps);
				//env->ReleaseStringUTFChars(temp, temps);
				//env->DeleteLocalRef(temp);
			}
			jint icon_url_len = sug_card->icon_url_len;
			jstring icon_url = NULL;
			if(icon_url_len > 0)
			{
				//jstring temp = env->NewString((jchar*)sug_card->icon_url, icon_url_len / 2);
				//const char* temps = env->GetStringUTFChars(temp, &isCopy);
				char temps[icon_url_len + 1];
				LOGI("init success");
				int i;
				for(i = 0; i < icon_url_len; i++)
				{
					temps[i] = sug_card->icon_url[i];
				}
				LOGI("assign success");
				temps[i] = '\0';
				icon_url = env->NewStringUTF((char*)temps);
				//env->ReleaseStringUTFChars(temp, temps);
				//env->DeleteLocalRef(temp);
			}
			env->CallVoidMethod(sug, set_str_method, card_key, title, content1, content2, content3, img_url, icon_url);
			env->DeleteLocalRef(card_key);
			env->DeleteLocalRef(title);
			env->DeleteLocalRef(content1);
			env->DeleteLocalRef(content2);
			env->DeleteLocalRef(content3);
			env->DeleteLocalRef(img_url);
			env->DeleteLocalRef(icon_url);
		}
		LOGI("level 3");
	}
	LOGI("level 4");
	return sug;
}

 /* 
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCellGetCloudWhiteVer
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlCellGetCloudWhiteVer
  (JNIEnv *env, jobject obj)
{
	return (jint)ipt_cell_get_cloud_white_ver(iptcore);
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlImportOldUzFile
 * Signature: (Ljava/lang/String;Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlImportOldUzFile
  (JNIEnv *env, jobject obj, jstring jCellFileName, jstring jUzFileName)
{
    jboolean isCopy = JNI_FALSE;
    jint ret = -1;
	if (iptcore && jCellFileName && jUzFileName)
	{
	    const char* cellFileName = env->GetStringUTFChars(jCellFileName, &isCopy);
		const char* uzFileName = env->GetStringUTFChars(jUzFileName, &isCopy);

		ret = ipt_util_import_old_us_file(iptcore, cellFileName, uzFileName);

		env->ReleaseStringUTFChars(jUzFileName, uzFileName);
	    env->ReleaseStringUTFChars(jCellFileName, cellFileName);
	}
    return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlSetCpuMsg
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlSetCpuMsg
  (JNIEnv *env, jobject obj, jstring jcpuMsg)
{
    jboolean isCopy = JNI_FALSE;
    jint ret = -1;
    const char* cpuMsg = env->GetStringUTFChars(jcpuMsg, &isCopy);
	
	ret = ipt_set_cpu_msg(iptcore, cpuMsg);

    env->ReleaseStringUTFChars(jcpuMsg, cpuMsg);

    return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlQueryGetCandContext
 * Signature: (I)Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_baidu_input_PlumCore_PlQueryGetCandContext
  (JNIEnv * env, jobject obj, jint idx)
{
    jstring str = NULL;
	if (session && idx >= 0)
	{
		PLUINT16 unicode[64];
		memset(unicode, 0, 64 * sizeof(PLUINT16));

		int ret = ipt_query_get_cand_context(session, idx, (PLUINT16*)unicode);
		if (ret > 0)
		{
			str = convert_wchar_to_jstring(env, unicode);
		}
	}
    return str;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCandContextExport
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlCandContextExport
  (JNIEnv *env, jobject obj, jstring jfname)
{
    jboolean isCopy = JNI_FALSE;
    jint ret = -1;
    const char* fname = env->GetStringUTFChars(jfname, &isCopy);
	
	ret = ipt_cand_context_export(iptcore, fname);

    env->ReleaseStringUTFChars(jfname, fname);

    return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlVkwordImport
 * Signature: (Ljava/lang/String;Z)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlVkwordImport
(JNIEnv *env, jobject obj, jstring jfileName, jboolean overwrite)
{
    jboolean isCopy = JNI_FALSE;
    PLUINT32 isOverwrite = overwrite ? 1 : 0;
    const char* fileName = env->GetStringUTFChars(jfileName, &isCopy);
    jint ret = (jint)ipt_vkword_import(iptcore, fileName, isOverwrite);
    env->ReleaseStringUTFChars(jfileName, fileName);
    return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlVkwordExport
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlVkwordExport
  (JNIEnv * env, jobject obj, jstring jfileName)
{
	jboolean isCopy = JNI_FALSE;
    const char* fileName = env->GetStringUTFChars(jfileName, &isCopy);

	int ret = ipt_vkword_export(iptcore, fileName);

    env->ReleaseStringUTFChars(jfileName, fileName);
	
	return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlImportUsrinfo
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlImportUsrinfo
(JNIEnv *env, jobject obj, jstring jfileName)
{
    jboolean isCopy = JNI_FALSE;
    const char* fileName = env->GetStringUTFChars(jfileName, &isCopy);
    jint ret = (jint)ipt_import_usrinfo(iptcore, fileName);
    env->ReleaseStringUTFChars(jfileName, fileName);
    return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlExportUsrinfo
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlExportUsrinfo
(JNIEnv *env, jobject obj, jstring jfileName)
{
    jboolean isCopy = JNI_FALSE;
    const char* fileName = env->GetStringUTFChars(jfileName, &isCopy);
    jint ret = (jint)ipt_export_usrinfo(iptcore, fileName);
    env->ReleaseStringUTFChars(jfileName, fileName);
    return ret;
}

 /*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlQueryGetEmailSuffix
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlQueryGetEmailSuffix
  (JNIEnv * env, jobject obj)
{
	return ipt_query_get_email_suffix(session);
}

 /*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlQueryCmdPushUni
 * Signature: (Ljava/lang/String;Ljava/lang/String;)Z
 */
JNIEXPORT jboolean JNICALL Java_com_baidu_input_PlumCore_PlQueryCmdPushUni(JNIEnv * env, jobject obj, jstring junicode, jstring jpinyin)
{

    jboolean isCopy = JNI_FALSE;

    int unicode_size = (int)env->GetStringLength(junicode);
    int pinyin_size = (int)env->GetStringLength(jpinyin);

    // TODO size大小限制怎么定
    if (unicode_size <= 0)
    {
        return JNI_FALSE;
    }

    if (pinyin_size <= 0)
    {
        return JNI_FALSE;
    }

    const jchar *tmpUnicode = env->GetStringChars(junicode, &isCopy);
    PLUINT16 unicode[unicode_size + 1];
	memset(unicode, 0, (unicode_size + 1) * sizeof(PLUINT16));
	jchar_to_wchar(unicode, tmpUnicode, unicode_size);

    const char* pinyin = env->GetStringUTFChars(jpinyin, &isCopy);
    char* cpinyin = (char*)malloc(pinyin_size * sizeof(char));
    memcpy(cpinyin, pinyin, pinyin_size);

    //int ret = ipt_query_cmd_push_uni(session, unicode, unicode_size, (char*) pinyin);
    int ret = ipt_query_cmd_push_uni(session, unicode, unicode_size, cpinyin);
    env->ReleaseStringChars(junicode, tmpUnicode);
	env->ReleaseStringUTFChars(jpinyin, pinyin);
	free(cpinyin);
    return (0 == ret)? JNI_TRUE : JNI_FALSE;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlSearchInstall
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlSearchInstall
  (JNIEnv * env, jobject obj, jstring jfileName)
{
	int ret = -1;
	jboolean isCopy = JNI_FALSE;
	
	const char* fileName = env->GetStringUTFChars(jfileName, &isCopy);
	ret = ipt_search_install(iptcore, fileName);
	env->ReleaseStringUTFChars(jfileName,fileName);
	
	return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetSearchVersion
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlGetSearchVersion
  (JNIEnv *env, jobject obj)
{
	return ipt_search_get_version(iptcore);
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlSearchFind
 * Signature: ([CI)[I
 */
JNIEXPORT jintArray JNICALL Java_com_baidu_input_PlumCore_PlSearchFind
  (JNIEnv *env, jobject obj, jcharArray queryArray, jint queryLen)
{
	jboolean isCopy = JNI_FALSE;
	jchar *query_str = env->GetCharArrayElements(queryArray, &isCopy);
    jintArray result =  (jintArray)env->NewIntArray(2);
	jint wordIdx = 0;
	jint wordLen = 0;
	jint ret = (jint) ipt_search_find(session, (PLUINT16*)query_str, (PLUINT32)queryLen, (PLUINT16*)&wordIdx, (PLUINT32*)&wordLen);
	setIntArrayElement(env, result, 0, wordIdx);
	setIntArrayElement(env, result, 1, wordLen);
	env->ReleaseCharArrayElements(queryArray, query_str, JNI_ABORT);
	
	return result;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCheckClip
 * Signature: ([CI)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlCheckClip
  (JNIEnv *env, jobject obj, jcharArray uniArray, jint uniLen)
{
	jboolean isCopy = JNI_FALSE;
	jchar *uni = env->GetCharArrayElements(uniArray, &isCopy);
	jint ret = (jint) ipt_check_clip(session, (PLUINT16*)uni, (PLUINT32)uniLen);
	env->ReleaseCharArrayElements(uniArray, uni, JNI_ABORT);
	
	return ret;
}


/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlSetStrBeforeCursor
 * Signature: ([CI)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlSetStrBeforeCursor
  (JNIEnv *env, jobject obj, jcharArray uniArray, jint uniLen)
{
	jboolean isCopy = JNI_FALSE;
	jchar *uni = env->GetCharArrayElements(uniArray, &isCopy);
	jint ret = (jint) ipt_set_str_before_cursor(session, (PLUINT16*)uni, (PLUINT32)uniLen);
	env->ReleaseCharArrayElements(uniArray, uni, JNI_ABORT);

	return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCandGetFlag
 * Signature: (I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlCandGetFlag
  (JNIEnv *env, jobject obj, jint idx)
{
	jint ret = (jint) ipt_cand_get_flag(session, (PLUINT32)idx);
	
	return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlZywordImport
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlZywordImport
(JNIEnv *env, jobject obj, jstring jfileName)
{
    jboolean isCopy = JNI_FALSE;
    const char* fileName = env->GetStringUTFChars(jfileName, &isCopy);
    jint ret = (jint)ipt_import_zyword(iptcore, fileName);
    env->ReleaseStringUTFChars(jfileName, fileName);
    return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlZywordExport
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlZywordExport
  (JNIEnv * env, jobject obj, jstring jfileName)
{
	jboolean isCopy = JNI_FALSE;
    const char* fileName = env->GetStringUTFChars(jfileName, &isCopy);

	int ret = ipt_export_zyword(iptcore, fileName);

    env->ReleaseStringUTFChars(jfileName, fileName);

	return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetAutoReplyAns
 * Signature: ([CI)[Ljava/lang/String;
 */
JNIEXPORT jobjectArray JNICALL Java_com_baidu_input_PlumCore_PlGetAutoReplyAns
  (JNIEnv *env, jobject obj, jcharArray quArray, jint quLen)
{
	jboolean isCopy = JNI_FALSE;
	jchar* qu = env->GetCharArrayElements(quArray, &isCopy);
	jint ansMaxCnt = 9;
	s_autoreply_answers* answers = (s_autoreply_answers*)malloc(sizeof(s_autoreply_answers) * ansMaxCnt);
    memset(answers,0,sizeof(s_autoreply_answers)*ansMaxCnt);
	int index=0;

    for(index = 0; index < ansMaxCnt; index++)
    {
		s_autoreply_answers* answer = answers+index;
		answer->ans = (PLUINT16*)malloc(sizeof(PLUINT16) * 64);
		memset(answer->ans, 0, 64 * sizeof(PLUINT16));
		answer->ans_len = 0;
	}
	
	jint ret = (jint) ipt_get_auto_reply_ans(iptcore, (PLUINT16*)qu, (PLUINT32)quLen, answers, ansMaxCnt);
	
	if(ret <= 0)
	{
		return NULL;
	}
	
	jobjectArray answerArray; 
    jclass objClass = (env)->FindClass("java/lang/String");
    answerArray = (env)->NewObjectArray(ret, objClass, 0);

	int i;
	for(i = 0; i < ret && i < ansMaxCnt; i++)
	{
		s_autoreply_answers* answer = answers + i;
        jstring temp = convert_wchar_to_jstring2(env, answer->ans, answer->ans_len);

		env->SetObjectArrayElement(answerArray, i, temp);
		
		env->DeleteLocalRef(temp); 
		free(answer->ans);
	}

    env->DeleteLocalRef(objClass); 
	free(answers);
	
	return answerArray;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetAutoReplyVersion
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlGetAutoReplyVersion
  (JNIEnv *env, jobject obj)
{
	int version = ipt_get_auto_reply_ver(iptcore);
	return (jint) version;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlAutoReplyInstall
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlAutoReplyInstall
  (JNIEnv * env, jobject obj, jstring jfileName)
{
	int ret = -1;
	jboolean isCopy = JNI_FALSE;
	
	const char* fileName = env->GetStringUTFChars(jfileName, &isCopy);
	ret = ipt_auto_reply_install(iptcore, fileName);
	env->ReleaseStringUTFChars(jfileName,fileName);
	
	return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlAutoReplyUninstall
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_baidu_input_PlumCore_PlAutoReplyUninstall
  (JNIEnv *env, jobject obj)
{
	if(NULL != iptcore)
	{
		int ret = ipt_auto_reply_uninstall(iptcore);
		if(0 == ret)
		{
			return JNI_TRUE;
		}
	}
	
	return JNI_FALSE;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlAutoreplyInfExtract
 * Signature: (Ljava/lang/String;II)[C
 */
JNIEXPORT jcharArray JNICALL Java_com_baidu_input_PlumCore_PlAutoreplyInfExtract
  (JNIEnv *env, jobject obj, jstring jorgWstr, jint orgLen, jint infType)
{
	int ret = -1;
	jboolean isCopy = JNI_FALSE;
	const jchar* orgWstr = env->GetStringChars(jorgWstr, &isCopy);
	PLUINT16 infWstr[orgLen];
	memset(infWstr, 0, orgLen * sizeof(PLUINT16));
	if (NULL != iptcore) {
	    ret = ipt_autoreply_inf_extract(iptcore, (PLUINT16*)orgWstr, orgLen, infType, (PLUINT16 *)infWstr, orgLen);
	}
	jcharArray jinfWstr = NULL;
	if(ret > 0)
	{
		jinfWstr = (jcharArray) env->NewCharArray(ret);
		for(PLUINT32 i = 0; i < ret; i++)
		{
			setCharArrayElement(env, jinfWstr, i, (jchar) infWstr[i]);
		}
	}
	env->ReleaseStringChars(jorgWstr,orgWstr);
	return jinfWstr;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlAutoreplyIntentDecode
 * Signature: (Ljava/lang/String;I)Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_baidu_input_PlumCore_PlAutoreplyIntentDecode
  (JNIEnv *env, jobject obj, jstring jintent, jint intentLen)
{
	int ret = -1;
	jboolean isCopy = JNI_FALSE;
	const jchar* intentArray = env->GetStringChars(jintent, &isCopy);
	int maxLen = 512;
	PLUINT8 outJson[maxLen];
	memset(outJson, 0, maxLen * sizeof(PLUINT8));
	ret = ipt_autoreply_intent_decode(iptcore, (PLUINT16*)intentArray, intentLen, (PLUINT8 *)outJson, maxLen);
	jstring decodeRes = NULL;
	if(ret > 0)
	{
		PLUINT16 outJson16[maxLen];
		memset(outJson16, 0, maxLen * sizeof(PLUINT16));
		for(PLUINT32 i = 0; i < ret; i++)
		{
			outJson16[i] = outJson[i];
		}
		decodeRes = convert_wchar_to_jstring2(env, outJson16, ret);
	}
	env->ReleaseStringChars(jintent, intentArray);
	return decodeRes;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetOptimizedVoiceResult
 * Signature: (Ljava/lang/String;)Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_baidu_input_PlumCore_PlGetOptimizedVoiceResult
  (JNIEnv *env, jobject obj, jstring joriResult)
{

    jboolean isCopy = JNI_FALSE;
    const jchar* ori_result = env->GetStringChars(joriResult, &isCopy);
    jint size = env->GetStringLength(joriResult);

    PLUINT16 ori_sent[size + 1];
    memset(ori_sent, 0, sizeof(PLUINT16) * (size + 1));
    jchar_to_wchar(ori_sent, ori_result, size);

    memset(g_string_buff, 0, STRING_BUFF_LEN * sizeof(PLUINT16));

	jint ret = ipt_usr_voice_correct(session, ori_sent, (PLUINT32) size, g_string_buff);

	env->ReleaseStringChars(joriResult, ori_result);

    jstring result = NULL;
    if (ret >= 0)
    {
        result = convert_wchar_to_jstring(env, g_string_buff);
    }

	return result;

}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlSendVoiceTrace
 * Signature: (Ljava/lang/String;[Lcom/baidu/input/ime/voicerecognize/voicetrace/TraceBean$$TraceEntity)I;
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlSendVoiceTrace
  (JNIEnv *env, jobject obj, jstring joriResult, jobjectArray traces)
{
    if (joriResult == NULL || traces == NULL)
    {
        return -1;
    }
    jboolean isCopy = JNI_FALSE;
    const jchar* ori_result = env->GetStringChars(joriResult, &isCopy);
    jint size = env->GetStringLength(joriResult);

    PLUINT16 ori_sent[size + 1];
    memset(ori_sent, 0, sizeof(PLUINT16) * (size + 1));
    jchar_to_wchar(ori_sent, ori_result, size);

    int length = env->GetArrayLength(traces);
    s_voice_correct_user_act* acts = (s_voice_correct_user_act*) malloc(sizeof(s_voice_correct_user_act) * length);
    static jmethodID get_act_method = 0;
    static jmethodID get_txt_method = 0;
    static jmethodID get_pre_method = 0;
    static jmethodID get_suf_method = 0;
    for (int index = 0; index < length; index++)
    {
        jobject element = env->GetObjectArrayElement(traces, index);
        if (element == NULL)
        {
            return -1;
        }
        jclass traceCls = env->GetObjectClass(element);

        if (get_act_method == 0)
        {
            get_act_method = env->GetMethodID(traceCls, "getAct", "()I");
        }
        if (get_txt_method == 0)
        {
            get_txt_method = env->GetMethodID(traceCls, "getTxt", "()Ljava/lang/String;");
        }
        if (get_pre_method == 0)
        {
            get_pre_method = env->GetMethodID(traceCls, "getPre", "()Ljava/lang/String;");
        }
        if (get_suf_method == 0)
        {
            get_suf_method = env->GetMethodID(traceCls, "getSuf", "()Ljava/lang/String;");
        }

        s_voice_correct_user_act* act = acts + index;
        act->act_type = (s_voice_correct_user_act::VOICE_CORRECT_USER_ACT_TYPE)
                                 env->CallIntMethod(element, get_act_method);

        int txt_array_len = (sizeof(act->txt) / sizeof(act->txt[0]));
        int pre_array_len = (sizeof(act->pre) / sizeof(act->pre[0]));
        int suf_array_len = (sizeof(act->suf) / sizeof(act->suf[0]));
        memset(act->txt, 0, txt_array_len * sizeof(PLUINT16));
        memset(act->pre, 0, pre_array_len * sizeof(PLUINT16));
        memset(act->suf, 0, suf_array_len * sizeof(PLUINT16));

        jstring jtxt = (jstring) (env->CallObjectMethod(element, get_txt_method));
        int len = jtxt == NULL ? 0 : env->GetStringLength(jtxt);
        if (len > 0)
        {
            PLUINT16* str = (PLUINT16*) env->GetStringChars(jtxt, &isCopy);
            len = len > (txt_array_len - 1) ? (txt_array_len - 1) : len;
            memcpy(act->txt, str, len * sizeof(PLUINT16));
            env->ReleaseStringChars(jtxt, str);
        }

        jstring jpre = (jstring) (env->CallObjectMethod(element, get_pre_method));
        len = jpre == NULL ? 0 : env->GetStringLength(jpre);
        if (len > 0)
        {
            PLUINT16* str = (PLUINT16*) env->GetStringChars(jpre, &isCopy);
            len = len > (pre_array_len - 1) ? (pre_array_len - 1) : len;
            memcpy(act->pre, str, len * sizeof(PLUINT16));
            env->ReleaseStringChars(jpre, str);
        }

        jstring jsuf = (jstring) (env->CallObjectMethod(element, get_suf_method));
        len = jsuf == NULL ? 0 : env->GetStringLength(jsuf);
        if (len > 0)
        {
            PLUINT16* str = (PLUINT16*) env->GetStringChars(jsuf, &isCopy);
            int len = env->GetStringLength(jsuf);
            len = len > (suf_array_len - 1) ? (suf_array_len - 1) : len;
            memcpy(act->suf, str, len * sizeof(PLUINT16));
            env->ReleaseStringChars(jsuf, str);
        }
    }
    int ret = ipt_usr_voice_update_user_dict(session, ori_sent, (PLINT32) size, acts, length);

    free(acts);
    env->ReleaseStringChars(joriResult, ori_result);
    return ret;
}

#ifdef IPT_APP_MAP
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlAppmapImport
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlAppmapImport
(JNIEnv *env, jobject obj, jstring jfileName)
{
    jboolean isCopy = JNI_FALSE;
    const char* fileName = env->GetStringUTFChars(jfileName, &isCopy);
    jint ret = (jint)ipt_import_app_map(iptcore, fileName);
    env->ReleaseStringUTFChars(jfileName, fileName);
    return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlAppmapExport
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlAppmapExport
  (JNIEnv * env, jobject obj, jstring jfileName)
{
	jboolean isCopy = JNI_FALSE;
    const char* fileName = env->GetStringUTFChars(jfileName, &isCopy);

	int ret = ipt_export_app_map(iptcore, fileName);

    env->ReleaseStringUTFChars(jfileName, fileName);

	return ret;
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetAppmapVersion
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlGetAppmapVersion
  (JNIEnv *env, jobject obj)
{
	return ipt_app_get_map_version(iptcore);
}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetAppCellId
 * Signature: (Ljava/lang/String;I)[I
 */
JNIEXPORT jintArray JNICALL Java_com_baidu_input_PlumCore_PlGetAppCellId
  (JNIEnv *env, jobject obj, jstring jappName, jint attribute)
{
    PLUINT32 cellid[MAX_OUTPUT];
    memset(cellid, 0, sizeof(PLUINT32)*MAX_OUTPUT);

    jboolean isCopy = JNI_FALSE;
    char* appName = (char*)env->GetStringUTFChars(jappName, &isCopy);

	jint ret = ipt_app_get_cell_id(session, appName, attribute, cellid);

    jintArray result = NULL;

    LOGI("PlGetAppCellId num=%d", ret);

	if(ret > 0)
	{
	    result =  (jintArray)env->NewIntArray(ret);
	    for(PLUINT32 i = 0; i < ret; i++)
        {
            LOGI("PlGetAppCellId i=%d cellid=%d", i, cellid[i]);
            setIntArrayElement(env, result, i, cellid[i]);
        }
	}

	env->ReleaseStringUTFChars(jappName, appName);

	return result;

}

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetAppLackCellId
 * Signature: (Ljava/lang/String;I)[I
 */
JNIEXPORT jintArray JNICALL Java_com_baidu_input_PlumCore_PlGetAppLackCellId
  (JNIEnv *env, jobject obj, jstring jappName, jint attribute)
{
    PLUINT32 cellid[MAX_OUTPUT];
    memset(cellid, 0, sizeof(PLUINT32)*MAX_OUTPUT);

    jboolean isCopy = JNI_FALSE;
    char* appName = (char*)env->GetStringUTFChars(jappName, &isCopy);

	jint ret = ipt_app_get_lack_cell_id(session, appName, attribute, cellid);

    jintArray result = NULL;

    LOGI("PlGetAppLackCellId num=%d", ret);

	if(ret > 0)
	{
	    result =  (jintArray)env->NewIntArray(ret);
	    for(PLUINT32 i = 0; i < ret; i++)
        {
            LOGI("PlGetAppLackCellId i=%d cellid=%d", i, cellid[i]);
            setIntArrayElement(env, result, i, cellid[i]);
        }
	}

	env->ReleaseStringUTFChars(jappName, appName);

	return result;

}

#endif