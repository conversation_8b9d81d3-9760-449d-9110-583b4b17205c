/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
#include "jni_pub_def.h"
/* Header for class com_baidu_input_PlumCore */

#ifndef _Included_com_baidu_input_PlumCore
#define _Included_com_baidu_input_PlumCore

#ifdef __cplusplus
extern "C" {
#endif

#ifdef OPEN_DEBUG_LOG
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlSetDebugLogPath
 * Signature: (Ljava/lang/String;I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlSetDebugLogPath
  (JNIEnv *, jobject, jstring, jint);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCloseDebugLog
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlCloseDebugLog
  (JNIEnv *, jobject);
#endif

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlInit
 * Signature: ([Ljava/lang/String;Landroid/content/pm/PackageInfo;)Z
 */
JNIEXPORT jboolean JNICALL Java_com_baidu_input_PlumCore_PlInit
  (JNIEnv *, jobject, jobjectArray, jobjectArray, jobject, jobject);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlHandWritingRefresh
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlHandWritingRefresh
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCzDownRefresh
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlCzDownRefresh
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGramRefresh
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlGramRefresh
  (JNIEnv *, jobject, jstring);
  
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlAutoReplyRefresh
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlAutoReplyRefresh
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlClose
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_baidu_input_PlumCore_PlClose
  (JNIEnv *, jobject);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlFlush
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_baidu_input_PlumCore_PlFlush
  (JNIEnv *, jobject);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetCoreVersion
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlGetCoreVersion
  (JNIEnv *, jobject);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlConfig
 * Signature: ([BI)Z
 */
JNIEXPORT jboolean JNICALL Java_com_baidu_input_PlumCore_PlConfig
(JNIEnv *, jobject, jbyteArray, jint);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetSPMapSheng
 * Signature: (B[B)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlGetSPMapSheng
(JNIEnv *, jobject, jbyte, jbyteArray);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetSPMapYun
 * Signature: (B[B)I

 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlGetSPMapYun
(JNIEnv *, jobject, jbyte, jbyteArray);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlSetSP
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlSetSP
  (JNIEnv *, jobject, jstring);


/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlSetKeyMap
 * Signature: (ICI)V
 */
JNIEXPORT void JNICALL Java_com_baidu_input_PlumCore_PlSetKeyMap
  (JNIEnv *, jobject, jint, jchar, jint);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlSetKeyMapAutofix
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_baidu_input_PlumCore_PlSetKeyMapAutofix
  (JNIEnv *, jobject);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCleanKeyMap
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_baidu_input_PlumCore_PlCleanKeyMap
  (JNIEnv *, jobject);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlLoadDefaultKeyMap
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_baidu_input_PlumCore_PlLoadDefaultKeyMap
  (JNIEnv *, jobject);

/*
 * Class:    com_baidu_input_PlumCore
 * Method:   PlSetEncase
 * Signature:([B)I
 */

JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlSetEncase
(JNIEnv *, jobject, jbyteArray);

#ifdef IPT_APP_MAP
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlFind
 * Signature: ([BIIILjava/lang/String;I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlFind
  (JNIEnv *, jobject, jbyteArray, jint, jint, jstring, jint);
#else
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlFind
 * Signature: ([BIII)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlFind
  (JNIEnv *, jobject, jbyteArray, jint, jint, jint);
#endif
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlFindLian
 * Signature: ([CI)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlFindLian
  (JNIEnv *, jobject, jcharArray, jint);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlQueryCmd
 * Signature: (II)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlQueryCmd
  (JNIEnv *, jobject, jint, jint);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlClean
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_baidu_input_PlumCore_PlClean
(JNIEnv * env, jobject obj);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCount
 * Signature: (I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlCount
  (JNIEnv *, jobject, jint);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetStr
 * Signature: (Lcom/baidu/input/pub/CoreString;II)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlGetStr
  (JNIEnv *, jobject,jobject,jint,jint);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetStrRange
 * Signature: ([Lcom/baidu/input/pub/CoreString;III)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlGetStrRange
  (JNIEnv *, jobject,jobjectArray,jint, jint, jint);
  
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetExFlag
 * Signature: (I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlGetExFlag
  (JNIEnv *, jobject,jint);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlMatchInfo
 * Signature: (I)[I
 */
JNIEXPORT jintArray JNICALL Java_com_baidu_input_PlumCore_PlMatchInfo
  (JNIEnv *, jobject, jint);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetShow
 * Signature: (I[B)Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_baidu_input_PlumCore_PlGetShow
  (JNIEnv *, jobject, jint, jbyteArray);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlAdjustCnWord
 * Signature: ([CII)Z
 */
JNIEXPORT jboolean JNICALL Java_com_baidu_input_PlumCore_PlAdjustCnWord
  (JNIEnv *, jobject, jcharArray, jint, jint);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlAdjustCnWordbyUni
 * Signature: (Ljava/lang/String;Ljava/lang/String;I)Z
 */
JNIEXPORT jboolean JNICALL Java_com_baidu_input_PlumCore_PlAdjustCnWordbyUni
  (JNIEnv *, jobject, jstring , jstring , jint );

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlAdjustEnWord
 * Signature: (Ljava/lang/String;I)Z
 */
JNIEXPORT jboolean JNICALL Java_com_baidu_input_PlumCore_PlAdjustEnWord
  (JNIEnv *, jobject, jstring, jint);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlFindUsWord
 * Signature: (Ljava/lang/String;I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlFindUsWord
  (JNIEnv * , jobject , jstring , jint);
  
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetCnWordCount
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlGetCnWordCount
  (JNIEnv *, jobject);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlImportWords
 * Signature: (Ljava/lang/String;I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlImportWords
  (JNIEnv *, jobject, jstring, jint);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlExportWords
 * Signature: (Ljava/lang/String;I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlExportWords
  (JNIEnv *, jobject, jstring, jint);
  
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlIdmapCellInstall
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlIdmapCellInstall
  (JNIEnv *, jobject, jstring);
  
/*



 * Class:     com_baidu_input_PlumCore
 * Method:    PlIdmapCellInstallByBuff
 * Signature: ([B)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlIdmapCellInstallByBuff
  (JNIEnv *, jobject, jbyteArray);
  
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlIdmapCellUninstall
 * Signature: (I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlIdmapCellUninstall
  (JNIEnv *, jobject, jint);
  
/* Class:     com_baidu_input_PlumCore

 * Method:    PlIdmapCellGetinfoById
 * Signature: (Lcom/baidu/input/pub/IdmapCellInfo;I)V
 */
JNIEXPORT void JNICALL Java_com_baidu_input_PlumCore_PlIdmapCellGetinfoById
  (JNIEnv *, jobject, jobject, jint);



//////////////////////////////////////////////////
////细胞词库管理相关接口//////////////////////////////
//////////////////////////////////////////////////
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCellInstall
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlCellInstall
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCellInstallByBuff
 * Signature: ([B)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlCellInstallByBuff
  (JNIEnv * , jobject, jbyteArray);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCellUninstall
 * Signature: (I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlCellUninstall
  (JNIEnv *, jobject, jint);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCellEnable
 * Signature: (IZ)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlCellEnable
  (JNIEnv *, jobject, jint, jboolean);
  
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCellSetLocType
 * Signature: (II)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlCellSetLocType
  (JNIEnv *, jobject, jint, jint);
  
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCellSetInstallTime
 * Signature: (II)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlCellSetInstallTime
  (JNIEnv *, jobject, jint, jint);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCellCount
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlCellCount
  (JNIEnv *, jobject);

 /* 
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCellGetinfo
 * Signature: ([Lcom/baidu/input/pub/CellInfo;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlCellGetinfo
  (JNIEnv *, jobject, jobjectArray);

 /* Class:     com_baidu_input_PlumCore
 * Method:    PlCellGetinfoById
 * Signature: (Lcom/baidu/input/pub/CellInfo;I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlCellGetinfoById
  (JNIEnv *, jobject, jobject, jint);

 /* 
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCellGetVer
 * Signature: (I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlCellGetVer
  (JNIEnv *, jobject, jint);


 /* 
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCellGetSysVER
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlCellGetSysVER
  (JNIEnv *, jobject);
///////////////////////////////////////////////////////
////个性短语管理相关接口///////////////////////////////////
///////////////////////////////////////////////////////
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlPhraseImport
 * Signature: (Ljava/lang/String;Z)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlPhraseImport
  (JNIEnv *, jobject, jstring, jboolean);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlPhraseImport
 * Signature: (Ljava/lang/String;I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlPhraseExport
  (JNIEnv *, jobject, jstring, jint);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlPhraseGetCount
 * Signature: (I[B)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlPhraseGetCount
  (JNIEnv *, jobject, jint, jbyteArray);


/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlPhraseGetInfo
 * Signature: (Lcom/baidu/input/pub/PhraseInfo;I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlPhraseGetInfo
  (JNIEnv *, jobject, jobject, jint);

  /*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlPhraseEdit
 * Signature: (Lcom/baidu/input/pub/PhraseInfo;I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlPhraseEdit
  (JNIEnv *, jobject, jobject, jint);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlPhraseGPGetCount
 * Signature: ([CI)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlPhraseGPGetCount
  (JNIEnv *, jobject, jcharArray, jint);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlPhraseGPGetInfo
 * Signature: (Lcom/baidu/input/pub/PhraseGPInfo;I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlPhraseGPGetInfo
  (JNIEnv *, jobject, jobject, jint);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlPhraseGPEdit
 * Signature: (Lcom/baidu/input/pub/PhraseGPInfo;I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlPhraseGPEdit
  (JNIEnv *, jobject,  jobject, jint);

///////////////////////////////////////////////////////
////加密相关接口/////////////////////////////////////////
///////////////////////////////////////////////////////
#ifdef SWITCH_ENCODE
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlIdea
 * Signature: ([BZ)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlIdea
  (JNIEnv *, jobject , jbyteArray, jboolean mode);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlRsaEncoder
 * Signature: ([B)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlRsaEncoder
  (JNIEnv * , jobject, jbyteArray);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlRsaGetBufferNeed
 * Signature: (I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlRsaGetBufferNeed
  (JNIEnv * , jobject, jint);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlMartine
 * Signature: ([Ljava/lang/String;)[B
 */
JNIEXPORT jbyteArray JNICALL Java_com_baidu_input_PlumCore_PlMartine
  (JNIEnv *, jobject, jobjectArray);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlIdeaBase64Decoder
 * Signature: ([B)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlIdeaBase64Decoder
  (JNIEnv * , jobject, jbyteArray);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetProductVersions
 * Signature: ([Ljava/lang/String;Landroid/content/pm/PackageInfo;)[B
 */
JNIEXPORT jbyteArray JNICALL Java_com_baidu_input_PlumCore_PlGetProductVersions
  (JNIEnv * , jobject, jobjectArray, jobject);


#endif// #ifdef SWITCH_ENCODE

///////////////////////////////////////////////////////
////联系人相关接口///////////////////////////////////////
///////////////////////////////////////////////////////
/*
 * Class:     com_baidu_input_PlumCore

 * Method:    PlCtClean
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_baidu_input_PlumCore_PlCtClean
  (JNIEnv *, jobject);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCtAddAttris

 * Signature: ([Ljava/lang/String;)Z
 */
JNIEXPORT jboolean JNICALL Java_com_baidu_input_PlumCore_PlCtAddAttris
  (JNIEnv *, jobject, jobjectArray);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCtAddContact
 * Signature: (Ljava/lang/String;[Ljava/lang/String;[Ljava/lang/String;)Z
 */
JNIEXPORT jboolean JNICALL Java_com_baidu_input_PlumCore_PlCtAddContact
  (JNIEnv *, jobject, jstring, jobjectArray, jobjectArray);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCtDeleteContact
 * Signature: (Ljava/lang/String;I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlCtDeleteContact
  (JNIEnv *, jobject, jstring, jint);
  
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCtVoiceFind
 * Signature: (Ljava/lang/String;)Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_baidu_input_PlumCore_PlCtVoiceFind
  (JNIEnv *, jobject, jstring);
  
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCtVoiceFindAddressbook
 * Signature: (Ljava/lang/String;)Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_baidu_input_PlumCore_PlCtVoiceFindAddressbook
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCtGetContact
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlCtGetCount
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCtGetContact
 * Signature: (I)[Ljava/lang/String;
 */
JNIEXPORT jobjectArray JNICALL Java_com_baidu_input_PlumCore_PlCtGetContact
  (JNIEnv *, jobject, jint);
  
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlUsrwordBackup
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_baidu_input_PlumCore_PlUsrwordBackup
  (JNIEnv *, jobject);
  
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlUsrwordRecover
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_baidu_input_PlumCore_PlUsrwordRecover
  (JNIEnv *, jobject);
  
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlUsrwordCheck
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_baidu_input_PlumCore_PlUsrwordCheck
  (JNIEnv *, jobject);

///////////////////////////////////////////////////////
////其他需完善的接口///////////////////////////////////////
///////////////////////////////////////////////////////
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlFindFT
 * Signature: (Ljava/lang/String;)Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_baidu_input_PlumCore_PlFindFT
  (JNIEnv * env, jobject obj,jstring str);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlDeleteUsWord
 * Signature: ([BIZ)V
 */
JNIEXPORT void JNICALL Java_com_baidu_input_PlumCore_PlDeleteUsWord
  (JNIEnv *, jobject, jbyteArray, jint, jboolean);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlIsCNSysword
 * Signature: (I)Z
 */
JNIEXPORT jboolean JNICALL Java_com_baidu_input_PlumCore_PlIsCNSysword
  (JNIEnv *, jobject, jint);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlIsENSysword
 * Signature: ([B)Z
 */
JNIEXPORT jboolean JNICALL Java_com_baidu_input_PlumCore_PlIsENSysword
  (JNIEnv *, jobject, jbyteArray);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetMatchLen
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlGetMatchLen
  (JNIEnv *, jobject);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetHotLetter
 * Signature: ([B)V
 */
JNIEXPORT void JNICALL Java_com_baidu_input_PlumCore_PlGetHotLetter
  (JNIEnv *, jobject, jbyteArray);  
  
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    GlChangeColor
 * Signature: ([I[II)V
 */
JNIEXPORT void JNICALL Java_com_baidu_input_PlumCore_GlChangeColor
  (JNIEnv *, jobject, jintArray, jintArray, jint);
  
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    GlSetNight
 * Signature: ([II)V
 */
//JNIEXPORT void JNICALL Java_com_baidu_input_PlumCore_GlSetNight
//  (JNIEnv *, jobject, jintArray, jint);
  
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    GlSetNight
 * Signature: (Landroid/graphics/Bitmap;)V
 */
JNIEXPORT void JNICALL Java_com_baidu_input_PlumCore_GlSetNight
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    GlSetNightColor
 * Signature: (I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_GlSetNightColor
  (JNIEnv *, jobject, jint);


/*
 * Class:     com_baidu_input_PlumCore
 * Method:    GlSetDayColor
 * Signature: (I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_GlSetDayColor
(JNIEnv *, jobject, jint);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    GlSetToDayMode
 * Signature: (Landroid/graphics/Bitmap;)V
 */
JNIEXPORT void JNICALL Java_com_baidu_input_PlumCore_GlSetToDayMode
  (JNIEnv *env, jobject obj, jobject bitmapIn);

void changeDayNightModeInner(JNIEnv *, jobject, jboolean);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    getProtCode
 * Signature: ()Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_baidu_input_PlumCore_getProtCode
  (JNIEnv *env, jobject obj);

///////////////////////////////////////////////////////
////符号相关的操作///////////////////////////////////////
///////////////////////////////////////////////////////

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlSymImport
 * Signature: (Ljava/lang/String;Z)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlSymImport
  (JNIEnv * , jobject, jstring, jboolean);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlSymExport
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlSymExport
  (JNIEnv * , jobject, jstring);


/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlSymImportReorder
 * Signature: (II[II)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlSymImportReorder
  (JNIEnv *, jobject, jint, jint, jobjectArray jsyms, jint jlen);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlSylianImport
 * Signature: (Ljava/lang/String;Z)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlSylianImport
  (JNIEnv * , jobject, jstring, jboolean);

/*
 * Class:     com_baidu_input_PlumCore

 * Method:    PlSylianExport
 * Signature: (Ljava/lang/String;)I

 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlSylianExport
  (JNIEnv * , jobject, jstring);


/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCreateEmptyLibFile
 * Signature: (Ljava/lang/String;I)Z
 */
 /**
JNIEXPORT jboolean JNICALL Java_com_baidu_input_PlumCore_PlCreateEmptyLibFile
  (JNIEnv * , jobject, jstring, jint);
*/

 /* 
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetGramVersion
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlGetGramVersion
  (JNIEnv *, jobject);
  
 /* 
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetGramCateVersion
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlGetGramCateVersion
  (JNIEnv *, jobject);

 /*
  * Class:	   com_baidu_input_PlumCore
  * Method:    PlGetZhiDaHaoVer
  * Signature: ()I
  */
 JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlGetZhiDaHaoVer
   (JNIEnv *, jobject);


 /* 
 * Class:     com_baidu_input_PlumCore
 * Method:    PlIsLongJP
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlIsLongJP
  (JNIEnv *, jobject);

 /* 
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCheckFileMD5
 * Signature: (Ljava/lang/String;[B)V
 */
JNIEXPORT void JNICALL Java_com_baidu_input_PlumCore_PlCheckFileMD5
  (JNIEnv *, jobject, jstring, jbyteArray);

 /* 
 * Class:     com_baidu_input_PlumCore
 * Method:    PlOldUeExport
 * Signature: (Ljava/lang/String;Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlOldUeExport
  (JNIEnv *, jobject, jstring, jstring);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlOldCpExport
 * Signature: (Ljava/lang/String;Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlOldCpExport
  (JNIEnv *, jobject, jstring, jstring);

/*

 * Class:     com_baidu_input_PlumCore
 * Method:    PlEmojiImport
 * Signature: (Ljava/lang/String;Z)I
 */
/*JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlEmojiImport
  (JNIEnv * , jobject, jstring, jboolean);*/

/*

 * Class:     com_baidu_input_PlumCore

 * Method:    PlEmojiExport
 * Signature: (Ljava/lang/String;)I

 */
/*JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlEmojiExport
  (JNIEnv * , jobject, jstring);*/

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlUsWordReduce
 * Signature: (I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlUsWordReduce
  (JNIEnv *, jobject, jint);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlSetHWPreword
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlSetHWPreword
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetHWPinyin
 * Signature: (CI)[B
 */
JNIEXPORT jbyteArray JNICALL Java_com_baidu_input_PlumCore_PlGetHWPinyin
  (JNIEnv *, jobject, jchar, jint);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlAdjustCnWordbyHW
 * Signature: ([CI)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlAdjustCnWordbyHW
  (JNIEnv *, jobject, jcharArray, jint);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlFindLianbyHW
 * Signature: ([CI)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlFindLianbyHW
  (JNIEnv *, jobject, jcharArray, jint);
  
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlOtherwordGetStatus
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlOtherwordGetStatus
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlOtherwordEnable
 * Signature: (Ljava/lang/String;I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlOtherwordEnable
  (JNIEnv *, jobject, jstring, jint);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKeywordGetSearchVersion
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlKeywordGetSearchVersion
  (JNIEnv *, jobject);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKeywordGetMediaVersion
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlKeywordGetMediaVersion
  (JNIEnv *, jobject);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKeywordExport
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlKeywordExport
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKeywordCellCount
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlKeywordCellCount
  (JNIEnv *, jobject);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKeywordCellInfoByIndex
 * Signature: (Lcom/baidu/input/pub/KeywordInfo;I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlKeywordCellInfoByIndex
  (JNIEnv *, jobject, jobject, jint);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKeywordCellInfoByCellId
 * Signature: (Lcom/baidu/input/pub/KeywordInfo;I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlKeywordCellInfoByCellId
  (JNIEnv *, jobject, jobject, jint);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKeywordCellInstall
 * Signature: (Ljava/lang/String;I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlKeywordCellInstall
  (JNIEnv *, jobject, jstring, jbyteArray, jint);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKeywordCellUninstall
 * Signature: (I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlKeywordCellUninstall
  (JNIEnv *, jobject, jint);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKeywordCellEnable
 * Signature: (IZ)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlKeywordCellEnable
  (JNIEnv *, jobject, jint, jboolean);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKeywordGetCandType
 * Signature: (Ljava/lang/String;)[I
 */
JNIEXPORT jintArray JNICALL Java_com_baidu_input_PlumCore_PlKeywordGetCandType
  (JNIEnv *, jobject, jstring);
  
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKeywordYanwenziExportOlddata
 * Signature: ([BI[BI[I)I
 */
//JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlKeywordYanwenziExportOlddata
//  (JNIEnv *, jobject, jbyteArray, jint, jbyteArray, jint, jintArray);
  
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKeywordYanwenziImportOlddata
 * Signature: ([BI[BI[I)I
 */
//JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlKeywordYanwenziImportOlddata
//  (JNIEnv *, jobject, jbyteArray, jint, jbyteArray, jint, jintArray);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKeywordEmoticonCellInstall
 * Signature: (Ljava/lang/String;[BI)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlKeywordEmoticonCellInstall
  (JNIEnv *, jobject, jstring, jbyteArray, jint);
  
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKeywordFindNijigen
 * Signature: ()[C
 */
JNIEXPORT jcharArray JNICALL Java_com_baidu_input_PlumCore_PlKeywordFindNijigen
  (JNIEnv *, jobject);
  
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKeywordFindEgg
 * Signature: ()[C
 */
JNIEXPORT jcharArray JNICALL Java_com_baidu_input_PlumCore_PlKeywordFindEgg
  (JNIEnv *, jobject);
  
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKeywordFindVoiceLian
 * Signature: (Ljava/lang/String;[I[I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlKeywordFindVoiceLian
  (JNIEnv *, jobject, jstring, jintArray, jintArray);
  
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKeywordFindVoiceEgg
 * Signature: (Ljava/lang/String;)[C
 */
JNIEXPORT jcharArray JNICALL Java_com_baidu_input_PlumCore_PlKeywordFindVoiceEgg
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKeywordGetSentenceKeyword
 * Signature: (Lcom/baidu/input/pub/CoreString;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlKeywordGetSentenceKeyword
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKeywordFindLian
 * Signature: (Ljava/lang/String;I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlKeywordFindLian
  (JNIEnv *, jobject, jstring, jint);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlEmojiGetSentenceLian
 * Signature: ([S)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlEmojiGetSentenceLian
  (JNIEnv *, jobject, jshortArray);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlEmojiAdjustEmojiRelation
 * Signature: (Ljava/lang/String;ICI)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlEmojiAdjustEmojiRelation
  (JNIEnv *, jobject, jstring, jint, jchar, jint);
  
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlSymAdjustSylianRelation
 * Signature: (Ljava/lang/String;ILjava/lang/String;I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlSymAdjustSylianRelation
  (JNIEnv *, jobject, jstring, jint, jstring, jint);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetCangjieVer
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlGetCangjieVer
  (JNIEnv *, jobject);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetZhuyinHzVer
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlGetZhuyinHzVer
  (JNIEnv *, jobject);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetZhuyinCzVer
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlGetZhuyinCzVer
  (JNIEnv *, jobject);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlHandWritingRecognizeAppand
 * Signature: ([SI)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlHandWritingRecognizeAppand
  (JNIEnv *, jobject, jshortArray, jint);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlHandWritingVersion
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlHandWritingVersion
  (JNIEnv *, jobject);
  
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlHandWritingSetConfig
 * Signature: ([I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlHandWritingSetConfig
  (JNIEnv *, jobject, jintArray);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlHandWritingSetConfig
 * Signature: ()[I
 */
JNIEXPORT jintArray JNICALL Java_com_baidu_input_PlumCore_PlHandWritingGetConfig
  (JNIEnv *, jobject);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlHandWritingEncodePoints
 * Signature: (I)[B
 */
JNIEXPORT jbyteArray JNICALL Java_com_baidu_input_HandWritingCore_PlHandWritingEncodePoints
  (JNIEnv *, jobject, jint);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlHandWritingSetBsFilter
 * Signature: ([S)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlHandWritingSetBsFilter
  (JNIEnv *, jobject, jshortArray);

#ifdef KEYPAD
#ifdef IPT_APP_MAP
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKpAppendPoint
 * Signature: (IISSIBLjava/lang/String;I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlKpAppendPoint
  (JNIEnv *, jobject, jint, jint, jshort, jshort, jint, jbyte, jstring, jint);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKpDeletePoint
 * Signature: (IIILjava/lang/String;I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlKpDeletePoint
  (JNIEnv *, jobject, jint, jint, jint, jstring, jint);
#else
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKpAppendPoint
 * Signature: (IISSIBI)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlKpAppendPoint
  (JNIEnv *, jobject, jint, jint, jshort, jshort, jint, jbyte, jint);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKpDeletePoint
 * Signature: (IIII)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlKpDeletePoint
  (JNIEnv *, jobject, jint, jint, jint, jint);
#endif

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKpClean
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlKpClean
  (JNIEnv *, jobject);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKpSetRect
 * Signature: ([I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlKpSetRect
  (JNIEnv *, jobject, jintArray);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKpGetPoint
 * Signature: ()[S
 */
JNIEXPORT jshortArray JNICALL Java_com_baidu_input_PlumCore_PlKpGetPoint
  (JNIEnv *, jobject);
  
  
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKpAddKey
 * Signature: (B[I[I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlKpAddKey
  (JNIEnv *, jobject, jbyte, jintArray, jintArray);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetTouchedKey
 * Signature: (II)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlGetTouchedKey
  (JNIEnv * env, jobject obj, jint x, jint y);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCleanDynamicRects
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlCleanDynamicRects
  (JNIEnv * env, jobject obj);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlSetSkinToken
 * Signature: ([CII)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlSetSkinToken
  (JNIEnv * env, jobject obj, jcharArray skin_token_array, jint len, jint phone_state);                                 

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlUsrTouchExportFreshData
 * Signature: ()Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_baidu_input_PlumCore_PlUsrTouchExportFreshData
  (JNIEnv * env, jobject obj);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlUsrTouchExportFreshData
 * Signature: ()Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_baidu_input_PlumCore_PlExportMisKeyForTrace
  (JNIEnv * env, jobject obj);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKpQueryIecTip
 * Signature: (I[B[B)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlKpQueryIecTip
  (JNIEnv *, jobject, jint, jbyteArray, jbyteArray);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKpQueryIecMohuStr
 * Signature: (I)Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_baidu_input_PlumCore_PlKpQueryIecMohuStr
  (JNIEnv *, jobject, jint);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlKpFindChCor
 * Signature: (I)Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_baidu_input_PlumCore_PlKpFindChCor
  (JNIEnv * env, jobject obj, jint idx);
#endif //KEYPAD

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCloudGetReqData
 * Signature: (Lcom/baidu/input/ime/cloudinput/manage/CloudRequestData;Lcom/baidu/input/ime/cloudinput/CloudSetting;Lcom/baidu/input/ime/cloudinput/CloudInfo;[Lcom/baidu/input/ime/cloudinput/CloudLog;)V
 */
JNIEXPORT void JNICALL Java_com_baidu_input_PlumCore_PlCloudGetReqData
  (JNIEnv *, jobject, jobject, jobject, jobject, jobjectArray);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlFindCloudZj
 * Signature: (Ljava/lang/String;)Lcom/baidu/input/ime/cloudinput/CloudForecastOutput;
 */
JNIEXPORT jobject JNICALL Java_com_baidu_input_PlumCore_PlFindCloudZj
  (JNIEnv * env, jobject obj, jstring junicode);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetSentenceMatchLen
 * Signature: (Ljava/lang/String;Ljava/lang/String;)I;
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlGetSentenceMatchLen
  (JNIEnv * env, jobject obj, jstring sen, jstring code);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCloudZjClean
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_baidu_input_PlumCore_PlCloudZjClean
  (JNIEnv * env, jobject obj);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCloudInputBuf
 * Signature: ([BIILcom/baidu/input/ime/cloudinput/manage/CloudDataManager;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlCloudInputBuf
  (JNIEnv *, jobject, jbyteArray, jint, jint, jobject);
  
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCloudOutput
 * Signature: ()[Lcom/baidu/input/ime/cloudinput/CloudOutputService;
 */
JNIEXPORT jobjectArray JNICALL Java_com_baidu_input_PlumCore_PlCloudOutput
  (JNIEnv *, jobject);
  
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCloudSearch
 * Signature: ()[Lcom/baidu/input/ime/cloudinput/CloudOutputSearch;
 */
JNIEXPORT jobjectArray JNICALL Java_com_baidu_input_PlumCore_PlCloudSearch
  (JNIEnv * env, jobject obj);
  
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlSugOutput
 * Signature: (I)Ljava/lang/Object;
 */
JNIEXPORT jobject JNICALL Java_com_baidu_input_PlumCore_PlSugOutput
  (JNIEnv *, jobject, jint);
  
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCellGetCloudWhiteVer
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlCellGetCloudWhiteVer
  (JNIEnv * , jobject);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlImportOldUzFile
 * Signature: (Ljava/lang/String;Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlImportOldUzFile
  (JNIEnv *, jobject, jstring, jstring);
  
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlSetCpuMsg
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlSetCpuMsg
  (JNIEnv *, jobject, jstring);
  
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlQueryGetCandContext
 * Signature: (I)Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_baidu_input_PlumCore_PlQueryGetCandContext
  (JNIEnv *, jobject, jint);
  
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCandContextExport
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlCandContextExport
  (JNIEnv *, jobject, jstring);
  
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlVkwordImport
 * Signature: (Ljava/lang/String;Z)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlVkwordImport
(JNIEnv *, jobject, jstring, jboolean);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlVkwordExport
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlVkwordExport
  (JNIEnv *, jobject, jstring);
  
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlImportUsrinfo
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlImportUsrinfo
(JNIEnv *, jobject, jstring);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlExportUsrinfo
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlExportUsrinfo
(JNIEnv *, jobject, jstring);


 /*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlQueryGetEmailSuffix
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlQueryGetEmailSuffix
  (JNIEnv *, jobject);

 /*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlQueryCmdPushUni
 * Signature: (Ljava/lang/String;Ljava/lang/String;)Z
 */
JNIEXPORT jboolean JNICALL Java_com_baidu_input_PlumCore_PlQueryCmdPushUni(JNIEnv *, jobject, jstring, jstring);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlSearchInstall
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlSearchInstall
  (JNIEnv *, jobject, jstring);
  
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetSearchVersion
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlGetSearchVersion
  (JNIEnv *, jobject);
  
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlSearchFind
 * Signature: ([CI)[I
 */
JNIEXPORT jintArray JNICALL Java_com_baidu_input_PlumCore_PlSearchFind
  (JNIEnv *, jobject, jcharArray, jint);


/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCheckClip
 * Signature: ([CI)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlCheckClip
  (JNIEnv *, jobject, jcharArray, jint);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlSetStrBeforeCursor
 * Signature: ([CI)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlSetStrBeforeCursor
  (JNIEnv *, jobject, jcharArray, jint);
  
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlCandGetFlag
 * Signature: (I)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlCandGetFlag
  (JNIEnv *, jobject, jint);

/*
* Class:     com_baidu_input_PlumCore
* Method:    PlZywordImport
* Signature: (Ljava/lang/String;)I
*/
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlZywordImport
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetAutoReplyAns
 * Signature: ([CI)[Ljava/lang/String;
 */
JNIEXPORT jobjectArray JNICALL Java_com_baidu_input_PlumCore_PlGetAutoReplyAns
  (JNIEnv *, jobject, jcharArray, jint);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetAutoReplyVersion
 * Signature: ()I
 */  
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlGetAutoReplyVersion
  (JNIEnv *, jobject);
  
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlAutoReplyInstall
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlAutoReplyInstall
  (JNIEnv *, jobject, jstring);
  
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlAutoReplyUninstall
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_baidu_input_PlumCore_PlAutoReplyUninstall
  (JNIEnv *, jobject);
  
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlAutoreplyInfExtract
 * Signature: (Ljava/lang/String;II)[C
 */
JNIEXPORT jcharArray JNICALL Java_com_baidu_input_PlumCore_PlAutoreplyInfExtract
  (JNIEnv *, jobject, jstring, jint, jint);
  
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlAutoreplyIntentDecode
 * Signature: (Ljava/lang/String;I)Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_baidu_input_PlumCore_PlAutoreplyIntentDecode
  (JNIEnv *, jobject, jstring, jint);

/*
* Class:     com_baidu_input_PlumCore
* Method:    PlZywordExport
* Signature: (Ljava/lang/String;)I
*/
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlZywordExport
  (JNIEnv *, jobject, jstring);

/*
* Class:     com_baidu_input_PlumCore
* Method:    PlZywordImport
* Signature: (Ljava/lang/String;)I
*/
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlZywordImport
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetAutoReplyAns
 * Signature: ([CI)[Ljava/lang/String;
 */
JNIEXPORT jobjectArray JNICALL Java_com_baidu_input_PlumCore_PlGetAutoReplyAns
  (JNIEnv *, jobject, jcharArray, jint);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetAutoReplyVersion
 * Signature: ()I
 */  
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlGetAutoReplyVersion
  (JNIEnv *, jobject);
  
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlAutoReplyInstall
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlAutoReplyInstall
  (JNIEnv *, jobject, jstring);
  
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlAutoReplyUninstall
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_baidu_input_PlumCore_PlAutoReplyUninstall
  (JNIEnv *, jobject);
  
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlAutoreplyInfExtract
 * Signature: (Ljava/lang/String;II)[C
 */
JNIEXPORT jcharArray JNICALL Java_com_baidu_input_PlumCore_PlAutoreplyInfExtract
  (JNIEnv *, jobject, jstring, jint, jint);

/*
* Class:     com_baidu_input_PlumCore
* Method:    PlZywordExport
* Signature: (Ljava/lang/String;)I
*/
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlZywordExport
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetOptimizedVoiceResult
 * Signature: (Ljava/lang/String;)Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_baidu_input_PlumCore_PlGetOptimizedVoiceResult
  (JNIEnv *env, jobject obj, jstring joriResult);


/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlSendVoiceTrace
 * Signature: (Ljava/lang/String;[Lcom/baidu/input/ime/voicerecognize/voicetrace/TraceBean$TraceEntity;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlSendVoiceTrace
  (JNIEnv *env, jobject obj, jstring joriResult, jobjectArray traces);

#ifdef IPT_APP_MAP
/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlAppmapImport
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlAppmapImport
(JNIEnv *, jobject, jstring);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlAppmapExport
 * Signature: (Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlAppmapExport
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetAppmapVersion
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_baidu_input_PlumCore_PlGetAppmapVersion
  (JNIEnv *, jobject);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetAppCellId
 * Signature: (Ljava/lang/String;I)[I
 */
JNIEXPORT jintArray JNICALL Java_com_baidu_input_PlumCore_PlGetAppCellId
  (JNIEnv *, jobject, jstring, jint);

/*
 * Class:     com_baidu_input_PlumCore
 * Method:    PlGetAppLackCellId
 * Signature: (Ljava/lang/String;I)[I
 */
JNIEXPORT jintArray JNICALL Java_com_baidu_input_PlumCore_PlGetAppLackCellId
  (JNIEnv *, jobject, jstring, jint);

#endif

#ifdef __cplusplus
}
#endif

#endif//_Included_com_baidu_input_PlumCore
