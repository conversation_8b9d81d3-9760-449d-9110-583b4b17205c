#ifndef _JNI_REGISTER_H_
#define _JNI_REGISTER_H_

/**************************/
/***** compile macro ******/
/**************************/
#define SET_ARRAY_WITH_INTERFACE // array的set重写
#define KEYPAD // 纠错相关
//#define CATCH_CRASH_SIGNAL // 内核crash log抓取
//#define IPT_CONFIG_CONTEXT_INPUT_OPEN
//#define TEST_CATCH_CRASH_SIGNAL
//#define OPEN_DEBUG_LOG
//#define RECORD_TIME
//#define SWITCH_ENCODE
//#define DEBUG_VERSION
#define CRASH_VERSION // 正式版本
#define IPT_FEATURE_NEW_KP // 新纠错策略的宏开关
#define IPT_APP_MAP     // 是否开启app场景化出词，该开关不控制内核源码编译，仅控制jni代码是否需要编入
#define IPT_MMAP

#ifdef CRASH_VERSION
#undef DEBUG_VERSION
#undef RECORD_TIME
#endif

#ifdef OPEN_DEBUG_LOG
#define IPT_CONFIG_DEBUG_MODE
#endif

#ifdef DEBUG_VERSION
#include "android/log.h"
#define  LOG_TAG    "IME_JNI"
#define  LOGI(...)     __android_log_print(ANDROID_LOG_INFO,LOG_TAG,__VA_ARGS__)
#else
#define  LOGI(...)
#endif

#ifndef  IPT_BASIC_TYPE_DEF
#define  IPT_BASIC_TYPE_DEF
typedef unsigned int PLUINT32;
typedef unsigned short PLUINT16;
typedef unsigned char PLBYTE;
typedef unsigned char PLUINT8;
typedef signed int PLINT32;
typedef signed short PLINT16;
typedef signed char PLINT8;
typedef long long PLINT64;
typedef unsigned long long PLUINT64;
typedef const char PLFNAME;
typedef float PLREAL32;
typedef double PLREAL64;
#define PLTRUE 1
#define PLFALSE 0
#define PLNULL 0
#endif

#endif
