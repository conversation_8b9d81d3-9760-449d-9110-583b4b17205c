#include <stdio.h>
#include "jni_pub_def.h"
#include "com_baidu_input_PlumCore.h"

static JNINativeMethod core_method_table[] = {
    //name,signature,fnPtr
#ifdef OPEN_DEBUG_LOG
	{ "PlSetDebugLogPath", "(Ljava/lang/String;I)I", (void*)Java_com_baidu_input_PlumCore_PlSetDebugLogPath },
	{ "PlCloseDebugLog", "()I", (void*)Java_com_baidu_input_PlumCore_PlCloseDebugLog },
#endif
    { "PlInit", "([Ljava/lang/String;[Ljava/lang/String;Landroid/content/pm/PackageInfo;Landroid/content/res/AssetManager;)Z", (void*)Java_com_baidu_input_PlumCore_PlInit },
	{ "PlHandWritingRefresh", "(Ljava/lang/String;)I", (void*)Java_com_baidu_input_PlumCore_PlHandWritingRefresh },
    { "PlCzDownRefresh", "(Ljava/lang/String;)I", (void*)Java_com_baidu_input_PlumCore_PlCzDownRefresh },
	{ "PlGramRefresh", "(Ljava/lang/String;)I", (void*)Java_com_baidu_input_PlumCore_PlGramRefresh },
	{ "PlAutoReplyRefresh", "(Ljava/lang/String;)I", (void*)Java_com_baidu_input_PlumCore_PlAutoReplyRefresh },
    { "PlClose", "()Z", (void*)Java_com_baidu_input_PlumCore_PlClose },
	{ "PlFlush", "()V", (void*)Java_com_baidu_input_PlumCore_PlFlush },
    { "PlGetCoreVersion","()I",(void*)Java_com_baidu_input_PlumCore_PlGetCoreVersion},
    { "PlConfig", "([BI)Z", (void*)Java_com_baidu_input_PlumCore_PlConfig },
	{ "PlGetSPMapSheng", "(B[B)I", (void*)Java_com_baidu_input_PlumCore_PlGetSPMapSheng },
	{ "PlGetSPMapYun", "(B[B)I", (void*)Java_com_baidu_input_PlumCore_PlGetSPMapYun },
	{ "PlSetSP","(Ljava/lang/String;)I",(void*)Java_com_baidu_input_PlumCore_PlSetSP },
    { "PlSetKeyMap", "(ICI)V", (void*)Java_com_baidu_input_PlumCore_PlSetKeyMap },
	{ "PlSetKeyMapAutofix", "()V", (void*)Java_com_baidu_input_PlumCore_PlSetKeyMapAutofix },
    { "PlCleanKeyMap", "()V", (void*)Java_com_baidu_input_PlumCore_PlCleanKeyMap },
    { "PlLoadDefaultKeyMap", "()V", (void*)Java_com_baidu_input_PlumCore_PlLoadDefaultKeyMap },
	{ "PlSetEncase", "([B)I", (void*)Java_com_baidu_input_PlumCore_PlSetEncase },
#ifdef IPT_APP_MAP
    { "PlFind","([BIILjava/lang/String;I)I",(void*)Java_com_baidu_input_PlumCore_PlFind},
#else
    { "PlFind", "([BIII)I", (void*)Java_com_baidu_input_PlumCore_PlFind },
#endif
 	{ "PlFindLian", "([CI)I", (void*)Java_com_baidu_input_PlumCore_PlFindLian },
    { "PlQueryCmd", "(II)I", (void*)Java_com_baidu_input_PlumCore_PlQueryCmd },
    { "PlClean", "()V", (void*)Java_com_baidu_input_PlumCore_PlClean },
    { "PlCount", "(I)I", (void*)Java_com_baidu_input_PlumCore_PlCount },
    { "PlGetStr", "(Lcom/baidu/input/pub/CoreStringInfo;II)I", (void*)Java_com_baidu_input_PlumCore_PlGetStr },
    { "PlGetStrRange", "([Lcom/baidu/input/pub/CoreStringInfo;III)I", (void*)Java_com_baidu_input_PlumCore_PlGetStrRange },
	{ "PlGetExFlag", "(I)I", (void*)Java_com_baidu_input_PlumCore_PlGetExFlag },
    { "PlMatchInfo", "(I)[I", (void*)Java_com_baidu_input_PlumCore_PlMatchInfo },
    { "PlGetShow", "(I[B)Ljava/lang/String;", (void*)Java_com_baidu_input_PlumCore_PlGetShow },
    { "PlAdjustCnWord", "([CII)Z", (void*)Java_com_baidu_input_PlumCore_PlAdjustCnWord },
	{ "PlAdjustCnWordbyUni", "(Ljava/lang/String;Ljava/lang/String;I)Z", (void*)Java_com_baidu_input_PlumCore_PlAdjustCnWordbyUni },
    { "PlAdjustEnWord", "(Ljava/lang/String;I)Z", (void*)Java_com_baidu_input_PlumCore_PlAdjustEnWord },
	{ "PlFindUsWord", "(Ljava/lang/String;I)I", (void*)Java_com_baidu_input_PlumCore_PlFindUsWord },
	{ "PlGetCnWordCount", "()I", (void*)Java_com_baidu_input_PlumCore_PlGetCnWordCount },
    { "PlImportWords", "(Ljava/lang/String;I)I", (void*)Java_com_baidu_input_PlumCore_PlImportWords },
    { "PlExportWords", "(Ljava/lang/String;I)I", (void*)Java_com_baidu_input_PlumCore_PlExportWords },
	{ "PlIdmapCellInstall","(Ljava/lang/String;)I",(void*)Java_com_baidu_input_PlumCore_PlIdmapCellInstall },
	{ "PlIdmapCellInstallByBuff","([B)I",(void*)Java_com_baidu_input_PlumCore_PlIdmapCellInstallByBuff },
	{ "PlIdmapCellUninstall","(I)I",(void*)Java_com_baidu_input_PlumCore_PlIdmapCellUninstall },
	{ "PlIdmapCellGetinfoById","(Lcom/baidu/input/pub/IdmapCellInfo;I)V",(void*)Java_com_baidu_input_PlumCore_PlIdmapCellGetinfoById },
    { "PlCellInstall","(Ljava/lang/String;)I",(void*)Java_com_baidu_input_PlumCore_PlCellInstall },
	{ "PlCellInstallByBuff","([B)I",(void*)Java_com_baidu_input_PlumCore_PlCellInstallByBuff },
    { "PlCellUninstall","(I)I",(void*)Java_com_baidu_input_PlumCore_PlCellUninstall },
    { "PlCellEnable","(IZ)I",(void*)Java_com_baidu_input_PlumCore_PlCellEnable },
	{ "PlCellSetLocType","(II)I",(void*)Java_com_baidu_input_PlumCore_PlCellSetLocType },
	{ "PlCellSetInstallTime","(II)I",(void*)Java_com_baidu_input_PlumCore_PlCellSetInstallTime },
    { "PlCellCount","()I",(void*)Java_com_baidu_input_PlumCore_PlCellCount },
    { "PlCellGetinfo","([Lcom/baidu/input/pub/CellInfo;)I",(void*)Java_com_baidu_input_PlumCore_PlCellGetinfo },
	{ "PlCellGetinfoById","(Lcom/baidu/input/pub/CellInfo;I)I",(void*)Java_com_baidu_input_PlumCore_PlCellGetinfoById },
	{ "PlCellGetVer","(I)I",(void*)Java_com_baidu_input_PlumCore_PlCellGetVer },
	{ "PlCellGetSysVER","()I",(void*)Java_com_baidu_input_PlumCore_PlCellGetSysVER },
//	{ "PlGetZhiDaHaoVer","()I",(void*)Java_com_baidu_input_PlumCore_PlGetZhiDaHaoVer },
    { "PlPhraseImport","(Ljava/lang/String;Z)I",(void*)Java_com_baidu_input_PlumCore_PlPhraseImport },
    { "PlPhraseExport","(Ljava/lang/String;I)I",(void*)Java_com_baidu_input_PlumCore_PlPhraseExport},
    { "PlPhraseGetCount","(I[B)I",(void*)Java_com_baidu_input_PlumCore_PlPhraseGetCount },
    { "PlPhraseGetInfo","(Lcom/baidu/input/pub/PhraseInfo;I)I",(void*)Java_com_baidu_input_PlumCore_PlPhraseGetInfo },
    { "PlPhraseEdit","(Lcom/baidu/input/pub/PhraseInfo;I)I",(void*)Java_com_baidu_input_PlumCore_PlPhraseEdit },
    { "PlPhraseGPGetCount","([CI)I",(void*)Java_com_baidu_input_PlumCore_PlPhraseGPGetCount },
	{ "PlPhraseGPGetInfo","(Lcom/baidu/input/pub/PhraseGPInfo;I)I",(void*)Java_com_baidu_input_PlumCore_PlPhraseGPGetInfo },
    { "PlPhraseGPEdit","(Lcom/baidu/input/pub/PhraseGPInfo;I)I",(void*)Java_com_baidu_input_PlumCore_PlPhraseGPEdit },
#ifdef SWITCH_ENCODE
 	{ "PlIdea", "([BZ)I", (void*)Java_com_baidu_input_PlumCore_PlIdea }, 
	{ "PlRsaEncoder", "([B)I", (void*)Java_com_baidu_input_PlumCore_PlRsaEncoder }, 
    { "PlRsaGetBufferNeed", "(I)I", (void*)Java_com_baidu_input_PlumCore_PlRsaGetBufferNeed }, 
	{ "PlMartine", "([Ljava/lang/String;)[B", (void*)Java_com_baidu_input_PlumCore_PlMartine }, 
	{ "PlIdeaBase64Decoder", "([B)I", (void*)Java_com_baidu_input_PlumCore_PlIdeaBase64Decoder }, 
	{ "PlGetProductVersions", "([Ljava/lang/String;Landroid/content/pm/PackageInfo;)[B", (void*)Java_com_baidu_input_PlumCore_PlGetProductVersions }, 
#endif //#ifdef SWITCH_ENCODE
    { "PlCtClean", "()Z", (void*)Java_com_baidu_input_PlumCore_PlCtClean }, 
	{ "PlCtAddAttris", "([Ljava/lang/String;)Z", (void*)Java_com_baidu_input_PlumCore_PlCtAddAttris }, 
    { "PlCtAddContact", "(Ljava/lang/String;[Ljava/lang/String;[Ljava/lang/String;)Z", (void*)Java_com_baidu_input_PlumCore_PlCtAddContact }, 
    { "PlCtGetContact", "(I)[Ljava/lang/String;", (void*)Java_com_baidu_input_PlumCore_PlCtGetContact }, 
	{ "PlCtGetCount", "(Ljava/lang/String;)I", (void*)Java_com_baidu_input_PlumCore_PlCtGetCount },
	{ "PlCtDeleteContact", "(Ljava/lang/String;I)I", (void*)Java_com_baidu_input_PlumCore_PlCtDeleteContact },
	{ "PlCtVoiceFind", "(Ljava/lang/String;)Ljava/lang/String;", (void*)Java_com_baidu_input_PlumCore_PlCtVoiceFind },
	{ "PlCtVoiceFindAddressbook", "(Ljava/lang/String;)Ljava/lang/String;", (void*)Java_com_baidu_input_PlumCore_PlCtVoiceFindAddressbook },
	{ "PlUsrwordBackup", "()Z", (void*)Java_com_baidu_input_PlumCore_PlUsrwordBackup },
	{ "PlUsrwordRecover", "()Z", (void*)Java_com_baidu_input_PlumCore_PlUsrwordRecover },
	{ "PlUsrwordCheck", "()Z", (void*)Java_com_baidu_input_PlumCore_PlUsrwordCheck },
	{ "PlFindFT", "(Ljava/lang/String;)Ljava/lang/String;", (void*)Java_com_baidu_input_PlumCore_PlFindFT }, 
	{ "PlDeleteUsWord", "([BIZ)V", (void*)Java_com_baidu_input_PlumCore_PlDeleteUsWord }, 
	{ "PlIsCNSysword", "(I)Z", (void*)Java_com_baidu_input_PlumCore_PlIsCNSysword }, 
	{ "PlIsENSysword", "([B)Z", (void*)Java_com_baidu_input_PlumCore_PlIsENSysword }, 
	{ "PlGetMatchLen", "()I", (void*)Java_com_baidu_input_PlumCore_PlGetMatchLen }, 
	{ "PlGetHotLetter", "([B)V", (void*)Java_com_baidu_input_PlumCore_PlGetHotLetter },
	{ "GlChangeColor", "([I[II)V", (void*)Java_com_baidu_input_PlumCore_GlChangeColor },
//	{ "GlSetNight", "([II)V", (void*)Java_com_baidu_input_PlumCore_GlSetNight },
	{ "GlSetNight", "(Landroid/graphics/Bitmap;)V", (void*)Java_com_baidu_input_PlumCore_GlSetNight },
	{ "GlSetNightColor", "(I)I", (void*)Java_com_baidu_input_PlumCore_GlSetNightColor },
	{ "GlSetDayColor", "(I)I", (void*)Java_com_baidu_input_PlumCore_GlSetDayColor },
	{ "GlSetToDayMode", "(Landroid/graphics/Bitmap;)V", (void*)Java_com_baidu_input_PlumCore_GlSetToDayMode },
	{ "getProtCode", "()Ljava/lang/String;", (void*)Java_com_baidu_input_PlumCore_getProtCode },
	{ "PlSymImport", "(Ljava/lang/String;Z)I", (void*)Java_com_baidu_input_PlumCore_PlSymImport },
	{ "PlSymExport", "(Ljava/lang/String;)I", (void*)Java_com_baidu_input_PlumCore_PlSymExport }, 
	{ "PlSylianImport", "(Ljava/lang/String;Z)I", (void*)Java_com_baidu_input_PlumCore_PlSylianImport },
	//{ "PlSylianExport", "(Ljava/lang/String;)I", (void*)Java_com_baidu_input_PlumCore_PlSylianExport }, 
	//{ "PlCreateEmptyLibFile", "(Ljava/lang/String;I)Z", (void*)Java_com_baidu_input_PlumCore_PlCreateEmptyLibFile }, 
	{ "PlGetGramVersion","()I",(void*)Java_com_baidu_input_PlumCore_PlGetGramVersion},
	{ "PlGetGramCateVersion","()I",(void*)Java_com_baidu_input_PlumCore_PlGetGramCateVersion},
	{ "PlIsLongJP","()I",(void*)Java_com_baidu_input_PlumCore_PlIsLongJP},
	{ "PlCheckFileMD5","(Ljava/lang/String;[B)V",(void*)Java_com_baidu_input_PlumCore_PlCheckFileMD5},
	{ "PlOldUeExport","(Ljava/lang/String;Ljava/lang/String;)I",(void*)Java_com_baidu_input_PlumCore_PlOldUeExport},
	{ "PlOldCpExport","(Ljava/lang/String;Ljava/lang/String;)I",(void*)Java_com_baidu_input_PlumCore_PlOldCpExport},
	//{ "PlEmojiImport", "(Ljava/lang/String;Z)I", (void*)Java_com_baidu_input_PlumCore_PlEmojiImport }, 
	//{ "PlEmojiExport", "(Ljava/lang/String;)I", (void*)Java_com_baidu_input_PlumCore_PlEmojiExport }, 
	{ "PlUsWordReduce", "(I)I", (void*)Java_com_baidu_input_PlumCore_PlUsWordReduce},
	{ "PlSetHWPreword", "(Ljava/lang/String;)I", (void*)Java_com_baidu_input_PlumCore_PlSetHWPreword }, 
	{ "PlGetHWPinyin", "(CI)[B", (void*)Java_com_baidu_input_PlumCore_PlGetHWPinyin},
	{ "PlAdjustCnWordbyHW", "([CI)I", (void*)Java_com_baidu_input_PlumCore_PlAdjustCnWordbyHW }, 
	{ "PlFindLianbyHW", "([CI)I", (void*)Java_com_baidu_input_PlumCore_PlFindLianbyHW},
	{ "PlOtherwordGetStatus", "(Ljava/lang/String;)I", (void*)Java_com_baidu_input_PlumCore_PlOtherwordGetStatus},
	{ "PlOtherwordEnable", "(Ljava/lang/String;I)I", (void*)Java_com_baidu_input_PlumCore_PlOtherwordEnable},
	{ "PlKeywordGetSearchVersion", "()I", (void*)Java_com_baidu_input_PlumCore_PlKeywordGetSearchVersion},
	{ "PlKeywordGetMediaVersion", "()I", (void*)Java_com_baidu_input_PlumCore_PlKeywordGetMediaVersion},
	{ "PlKeywordExport", "(Ljava/lang/String;)I", (void*)Java_com_baidu_input_PlumCore_PlKeywordExport},
	{ "PlKeywordCellCount", "()I", (void*)Java_com_baidu_input_PlumCore_PlKeywordCellCount},
	{ "PlKeywordCellInfoByIndex", "(Lcom/baidu/input/pub/KeywordInfo;I)I", (void*)Java_com_baidu_input_PlumCore_PlKeywordCellInfoByIndex},
	{ "PlKeywordCellInfoByCellId", "(Lcom/baidu/input/pub/KeywordInfo;I)I", (void*)Java_com_baidu_input_PlumCore_PlKeywordCellInfoByCellId},
	{ "PlKeywordCellInstall", "(Ljava/lang/String;[BI)I", (void*)Java_com_baidu_input_PlumCore_PlKeywordCellInstall},
	{ "PlKeywordCellUninstall", "(I)I", (void*)Java_com_baidu_input_PlumCore_PlKeywordCellUninstall},
	{ "PlKeywordCellEnable", "(IZ)I", (void*)Java_com_baidu_input_PlumCore_PlKeywordCellEnable},
	{ "PlKeywordGetCandType", "(Ljava/lang/String;)[I", (void*)Java_com_baidu_input_PlumCore_PlKeywordGetCandType},
	//{ "PlKeywordYanwenziExportOlddata", "([BI[BI[I)I", (void*)Java_com_baidu_input_PlumCore_PlKeywordYanwenziExportOlddata},
	//{ "PlKeywordYanwenziImportOlddata", "([BI[BI[I)I", (void*)Java_com_baidu_input_PlumCore_PlKeywordYanwenziImportOlddata},
	{ "PlKeywordEmoticonCellInstall", "(Ljava/lang/String;[BI)I", (void*)Java_com_baidu_input_PlumCore_PlKeywordEmoticonCellInstall},
	{ "PlKeywordFindNijigen", "()[C", (void*)Java_com_baidu_input_PlumCore_PlKeywordFindNijigen},
	{ "PlKeywordFindEgg", "()[C", (void*)Java_com_baidu_input_PlumCore_PlKeywordFindEgg},
	{ "PlKeywordFindVoiceLian", "(Ljava/lang/String;[I[I)I", (void*)Java_com_baidu_input_PlumCore_PlKeywordFindVoiceLian},
	{ "PlKeywordFindVoiceEgg", "(Ljava/lang/String;)[C", (void*)Java_com_baidu_input_PlumCore_PlKeywordFindVoiceEgg},
	{ "PlKeywordGetSentenceKeyword", "(Lcom/baidu/input/pub/CoreStringInfo;)I", (void*)Java_com_baidu_input_PlumCore_PlKeywordGetSentenceKeyword},
	{ "PlKeywordFindLian", "(Ljava/lang/String;I)I", (void*)Java_com_baidu_input_PlumCore_PlKeywordFindLian},
	{ "PlEmojiGetSentenceLian", "([S)I", (void*)Java_com_baidu_input_PlumCore_PlEmojiGetSentenceLian},
	{ "PlEmojiAdjustEmojiRelation", "(Ljava/lang/String;ICI)I", (void*)Java_com_baidu_input_PlumCore_PlEmojiAdjustEmojiRelation},
	{ "PlSymAdjustSylianRelation", "(Ljava/lang/String;ILjava/lang/String;I)I", (void*)Java_com_baidu_input_PlumCore_PlSymAdjustSylianRelation},
	{ "PlGetCangjieVer", "()I", (void*)Java_com_baidu_input_PlumCore_PlGetCangjieVer},
	{ "PlGetZhuyinHzVer", "()I", (void*)Java_com_baidu_input_PlumCore_PlGetZhuyinHzVer},
	{ "PlGetZhuyinCzVer", "()I", (void*)Java_com_baidu_input_PlumCore_PlGetZhuyinCzVer},
	{ "PlHandWritingRecognizeAppand", "([SI)I", (void*)Java_com_baidu_input_PlumCore_PlHandWritingRecognizeAppand},
	{ "PlHandWritingVersion", "()I", (void*)Java_com_baidu_input_PlumCore_PlHandWritingVersion},
	{ "PlHandWritingSetConfig", "([I)I", (void*)Java_com_baidu_input_PlumCore_PlHandWritingSetConfig},
	{ "PlHandWritingGetConfig", "()[I", (void*)Java_com_baidu_input_PlumCore_PlHandWritingGetConfig},
	{ "PlHandWritingEncodePoints", "(I)[B", (void*)Java_com_baidu_input_HandWritingCore_PlHandWritingEncodePoints},
	{ "PlHandWritingSetBsFilter", "([S)I", (void*)Java_com_baidu_input_PlumCore_PlHandWritingSetBsFilter},
#ifdef KEYPAD
#ifdef IPT_APP_MAP
    { "PlKpAppendPoint","(IISSIBLjava/lang/String;I)I",(void*)Java_com_baidu_input_PlumCore_PlKpAppendPoint},
    { "PlKpDeletePoint","(IIILjava/lang/String;I)I",(void*)Java_com_baidu_input_PlumCore_PlKpDeletePoint},
#else
	{ "PlKpAppendPoint", "(IISSIBI)I", (void*)Java_com_baidu_input_PlumCore_PlKpAppendPoint},
	{ "PlKpDeletePoint", "(IIII)I", (void*)Java_com_baidu_input_PlumCore_PlKpDeletePoint },
#endif
	{ "PlKpClean", "()I", (void*)Java_com_baidu_input_PlumCore_PlKpClean},
	{ "PlKpSetRect", "([I)I", (void*)Java_com_baidu_input_PlumCore_PlKpSetRect },
	{ "PlKpGetPoint", "()[S", (void*)Java_com_baidu_input_PlumCore_PlKpGetPoint},
	{ "PlKpAddKey", "(B[I[I)I", (void*)Java_com_baidu_input_PlumCore_PlKpAddKey},
    { "PlGetTouchedKey", "(II)I", (void*)Java_com_baidu_input_PlumCore_PlGetTouchedKey},
    { "PlCleanDynamicRects", "()I", (void*)Java_com_baidu_input_PlumCore_PlCleanDynamicRects},
    { "PlSetSkinToken", "([CII)I", (void*)Java_com_baidu_input_PlumCore_PlSetSkinToken},
    { "PlUsrTouchExportFreshData", "()Ljava/lang/String;", (void*)Java_com_baidu_input_PlumCore_PlUsrTouchExportFreshData},
    { "PlExportMisKeyForTrace", "()Ljava/lang/String;", (void*)Java_com_baidu_input_PlumCore_PlExportMisKeyForTrace},
	{ "PlKpQueryIecTip", "(I[B[B)I", (void*)Java_com_baidu_input_PlumCore_PlKpQueryIecTip},
	{ "PlKpQueryIecMohuStr", "(I)Ljava/lang/String;", (void*)Java_com_baidu_input_PlumCore_PlKpQueryIecMohuStr},
	{ "PlKpFindChCor", "(I)Ljava/lang/String;", (void*)Java_com_baidu_input_PlumCore_PlKpFindChCor},
#endif
	{ "PlCloudInputBuf", "([BIILcom/baidu/input/ime/cloudinput/manage/ICloudDataManager;)I", (void*)
	Java_com_baidu_input_PlumCore_PlCloudInputBuf},
	{ "PlCloudGetReqData", "(Lcom/baidu/input/ime/cloudinput/manage/ICloudRequestData;Lcom/baidu/input/ime/cloudinput/ICloudSetting;Lcom/baidu/input/ime/cloudinput/ICloudInfo;[Lcom/baidu/input/ime/cloudinput/CloudLog;)V", (void*)Java_com_baidu_input_PlumCore_PlCloudGetReqData},
	{ "PlFindCloudZj", "(Ljava/lang/String;)Lcom/baidu/input/ime/cloudinput/CloudForecastOutput;", (void*)Java_com_baidu_input_PlumCore_PlFindCloudZj},
	{ "PlGetSentenceMatchLen", "(Ljava/lang/String;Ljava/lang/String;)I", (void*)Java_com_baidu_input_PlumCore_PlGetSentenceMatchLen},
	{ "PlCloudZjClean", "()V", (void*)Java_com_baidu_input_PlumCore_PlCloudZjClean},
	{ "PlCloudOutput", "()[Lcom/baidu/input/ime/cloudinput/CloudOutputService;", (void*)Java_com_baidu_input_PlumCore_PlCloudOutput},
	{ "PlCloudSearch", "()[Lcom/baidu/input/ime/cloudinput/CloudOutputSearch;", (void*)Java_com_baidu_input_PlumCore_PlCloudSearch},
	{ "PlSugOutput", "(I)Ljava/lang/Object;", (void*)Java_com_baidu_input_PlumCore_PlSugOutput},
	{ "PlCellGetCloudWhiteVer", "()I", (void*)Java_com_baidu_input_PlumCore_PlCellGetCloudWhiteVer},
	{ "PlImportOldUzFile", "(Ljava/lang/String;Ljava/lang/String;)I", (void*)Java_com_baidu_input_PlumCore_PlImportOldUzFile},
	{ "PlSetCpuMsg", "(Ljava/lang/String;)I", (void*)Java_com_baidu_input_PlumCore_PlSetCpuMsg},
	{ "PlQueryGetCandContext", "(I)Ljava/lang/String;", (void*)Java_com_baidu_input_PlumCore_PlQueryGetCandContext},
	{ "PlCandContextExport", "(Ljava/lang/String;)I", (void*)Java_com_baidu_input_PlumCore_PlCandContextExport},
	{ "PlVkwordImport","(Ljava/lang/String;Z)I",(void*)Java_com_baidu_input_PlumCore_PlVkwordImport },
    { "PlVkwordExport","(Ljava/lang/String;)I",(void*)Java_com_baidu_input_PlumCore_PlVkwordExport},
	{ "PlImportUsrinfo","(Ljava/lang/String;)I",(void*)Java_com_baidu_input_PlumCore_PlImportUsrinfo},
	{ "PlExportUsrinfo","(Ljava/lang/String;)I",(void*)Java_com_baidu_input_PlumCore_PlExportUsrinfo},
	{ "PlQueryGetEmailSuffix","()I",(void*)Java_com_baidu_input_PlumCore_PlQueryGetEmailSuffix},
	{ "PlQueryCmdPushUni","(Ljava/lang/String;Ljava/lang/String;)Z",(void*)Java_com_baidu_input_PlumCore_PlQueryCmdPushUni},
	{ "PlSearchInstall","(Ljava/lang/String;)I",(void*)Java_com_baidu_input_PlumCore_PlSearchInstall},
	{ "PlGetSearchVersion","()I",(void*)Java_com_baidu_input_PlumCore_PlGetSearchVersion},
	{ "PlSearchFind","([CI)[I",(void*)Java_com_baidu_input_PlumCore_PlSearchFind},
	{ "PlSetStrBeforeCursor","([CI)I",(void*)Java_com_baidu_input_PlumCore_PlSetStrBeforeCursor},
	{ "PlCandGetFlag","(I)I",(void*)Java_com_baidu_input_PlumCore_PlCandGetFlag},
	{ "PlGetAutoReplyAns","([CI)[Ljava/lang/String;",(void*)Java_com_baidu_input_PlumCore_PlGetAutoReplyAns},
	{ "PlGetAutoReplyVersion","()I",(void*)Java_com_baidu_input_PlumCore_PlGetAutoReplyVersion},
	{ "PlAutoReplyInstall","(Ljava/lang/String;)I",(void*)Java_com_baidu_input_PlumCore_PlAutoReplyInstall},
	{ "PlAutoReplyUninstall","()Z",(void*)Java_com_baidu_input_PlumCore_PlAutoReplyUninstall},
	{ "PlZywordImport","(Ljava/lang/String;)I",(void*)Java_com_baidu_input_PlumCore_PlZywordImport},
    { "PlZywordExport","(Ljava/lang/String;)I",(void*)Java_com_baidu_input_PlumCore_PlZywordExport},
	{ "PlAutoreplyInfExtract","(Ljava/lang/String;II)[C",(void*)Java_com_baidu_input_PlumCore_PlAutoreplyInfExtract},
	{ "PlAutoreplyIntentDecode","(Ljava/lang/String;I)Ljava/lang/String;",(void*)Java_com_baidu_input_PlumCore_PlAutoreplyIntentDecode},
	{ "PlGetOptimizedVoiceResult","(Ljava/lang/String;)Ljava/lang/String;",(void*)Java_com_baidu_input_PlumCore_PlGetOptimizedVoiceResult},
	{ "PlSendVoiceTrace","(Ljava/lang/String;[Lcom/baidu/input/ime/voicerecognize/voicetrace/TraceBean$TraceEntity;)I",(void*)Java_com_baidu_input_PlumCore_PlSendVoiceTrace},
	{ "PlCheckClip","([CI)I",(void*)Java_com_baidu_input_PlumCore_PlCheckClip},


#ifdef IPT_APP_MAP
    { "PlAppmapImport","(Ljava/lang/String;)I",(void*)Java_com_baidu_input_PlumCore_PlAppmapImport},
    { "PlAppmapExport","(Ljava/lang/String;)I",(void*)Java_com_baidu_input_PlumCore_PlAppmapExport},
    { "PlGetAppmapVersion","()I",(void*)Java_com_baidu_input_PlumCore_PlGetAppmapVersion},
    { "PlGetAppCellId","(Ljava/lang/String;I)[I",(void*)Java_com_baidu_input_PlumCore_PlGetAppCellId},
    { "PlGetAppLackCellId","(Ljava/lang/String;I)[I",(void*)Java_com_baidu_input_PlumCore_PlGetAppLackCellId},
#endif
};

JNIEXPORT jint JNI_OnLoad(JavaVM *vm, void *reserved)
{
    JNIEnv *env;
	jclass clazz;
	jint result = -1;
	if (vm->GetEnv((void**) &env, JNI_VERSION_1_4) != JNI_OK)
	{
		LOGI("ERROR: GetEnv failed\n");
		goto bail;
	}

	if ((clazz = env->FindClass("com/baidu/input/PlumCore")) == NULL)
	{
		LOGI("ERROR: FindClass com/baidu/input/PlumCore failed\n");
		goto bail;
	}

	if (env->RegisterNatives(clazz, core_method_table, sizeof(core_method_table) / sizeof(JNINativeMethod)) < 0)
	{
        LOGI("RegisterNatives failed");
        goto bail;
    }

	result = JNI_VERSION_1_4;

bail:
	env->DeleteLocalRef(clazz);
	return result;
}

JNIEXPORT void JNI_OnUnload(JavaVM *vm, void *reserved)
{
	JNIEnv *env;
	jclass k;
	jint r;

	r = vm->GetEnv((void **) &env, JNI_VERSION_1_2);
	k = env->FindClass ("com/baidu/input/PlumCore");
	env->UnregisterNatives(k);
	env->DeleteLocalRef(k);
}