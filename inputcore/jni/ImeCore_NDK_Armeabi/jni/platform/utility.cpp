#include "stdlib.h"
#include "utility.h"
#include "../_pub_iptcore.h"

#include <android/asset_manager.h>
#include <android/asset_manager_jni.h>

#ifdef RECORD_TIME
#include <sys/time.h>
#endif

// 夜间模式变色参数：RGB的缩放系数 * 精度缩放系数（固定值1024）
const int FACTOR_RGB = 680;
// 夜间模式变色参数：RGB的偏移系数 * 精度缩放系数（固定值1024）
const int  OFFSET_RGB = -33792;
// 夜间模式变色参数：精度缩放系数的移位bit数
const int ZOOM_BIT = 10;

char* itoa(int value, char* string) {
	char tmp[33];
	char* tp = tmp;
	int i;
	unsigned v;
	int sign;
	char* sp;

	sign = (value < 0);

	if (sign)
		v = -value;
	else
		v = (unsigned) value;

	while (v || tp == tmp) {
		i = v % 10;
		v = v / 10;

		if (i < 10)
			*tp++ = i + '0';
		else
			*tp++ = i + 'a' - 10;
	}

	if (string == 0)
		string = (char*) malloc((tp - tmp) + sign + 1);

	sp = string;

	if (sign)
		*sp++ = '-';

	while (tp > tmp)
		*sp++ = *--tp;

	*sp = 0;

	return string;
}

int WCSLEN(const PLUINT16* wstr) {
	int len = 0;
	while (wstr[len]) {
		len++;
	}
	return len;
}

PLUINT16 * char_to_wchar(PLUINT16 *wstr, const char *str) {
	while (*str != '\0') {
		*wstr++ = (PLUINT16)(*str++);
	}
	*wstr = (PLUINT16) '\0';

	return wstr;
}

void jchar_to_wchar(PLUINT16*wstr, const jchar *jc, int size) 
{
	int len = 0;

	while (len < size && *jc != (jchar) '\0') 
	{
		*wstr++ = (PLUINT16)(*jc++);
		len++;
	}
	*wstr = '\0';
}

jstring convert_wchar_to_jstring2(JNIEnv * env, PLUINT16* wstr, jsize slen) {
	jboolean isCopy = JNI_FALSE;
	jcharArray outputarr = env->NewCharArray(MAX_OUTPUT);
	jchar* output = env->GetCharArrayElements(outputarr, &isCopy);

	for (int i = 0; i < slen; i++) {
		if ((wstr[i] & 0x0000) != 0) {
			output[i] = (jchar) ' ';
			continue;
		}
		output[i] = (jchar) wstr[i];
	}
	output[slen] = (jchar) '\0';

	jstring js = env->NewString((jchar*) output, slen);

	env->ReleaseCharArrayElements(outputarr, output, JNI_ABORT);
	env->DeleteLocalRef(outputarr);
	return js;
}

jstring convert_wchar_to_jstring(JNIEnv * env, PLUINT16* wstr) {
	jsize slen = (jsize)(WCSLEN(wstr));
	return convert_wchar_to_jstring2(env, wstr, slen);
}

PLUINT16 * itow(int integer, PLUINT16 *wstr) {
	char* tmp = itoa(integer, NULL);
	PLUINT16 *ret = char_to_wchar(wstr, tmp);
	free(tmp);
	return ret;
}

void set_int_filed(JNIEnv * env, jclass clazz, jobject obj, const char *name,
		int value) {
	jfieldID fieldID = env->GetFieldID(clazz, name, "I");
	if (fieldID == 0)
		return;
	env->SetIntField(obj, fieldID, value);
}

void set_string_filed(JNIEnv * env, jclass clazz, jobject obj, const char *name,
		jstring value) {
	jfieldID fieldID = env->GetFieldID(clazz, name, "Ljava/lang/String;");
	if (fieldID == 0)
		return;
	env->SetObjectField(obj, fieldID, value);
}

void *_aligned4_malloc(size_t size)
{
	void *tempPtr = malloc(size + 4);
	char offset = 4 - (((int)tempPtr) % 4);
	char *alignedPtr = ((char*)tempPtr) + offset;
	alignedPtr[-1] = offset;
	return (void*)alignedPtr;
}

void _aligned4_free(void *ptr)
{
	char offset = ((char*)ptr)[-1];
	free(((char*)ptr) - offset);
}

#ifdef RECORD_TIME
long getCurrentTimeMillis()
{    
   struct timeval tv;    
   gettimeofday(&tv, NULL);
   return ((tv.tv_sec * 1000) + (tv.tv_usec / 1000));
}
#endif

#ifdef CRASH_VERSION
void checkState(int ret)
{
    int state = 0;
    if(state == 0)
		state++;
	else
		state--;

	if(ret != 0)
	{
		int *p = 0;
		*p = 5;
	}
}

int number()
{
	PLUINT32 i = 0;
	PLUINT32 seed = 0;
	PLBYTE* seed_ptr = (PLBYTE*) (&seed);
	PLUINT32 seed2 = 0;
	PLBYTE* seed_ptr2 = (PLBYTE*) (&seed2);
	for (i = 0; i < 4; i++)
	{
		seed_ptr[i] = ((i * 1783) % 736) ^ (i + 37);
		seed_ptr2[1]  = 0xCF;
	}

	seed_ptr2[3]  = 0xC4;

	for (i = 0; i < 4; i++)
		seed_ptr2[i] =  (1+ i + seed_ptr2[i]);

	seed_ptr2[0]  += 0xA1;
	seed_ptr2[2]  += 0x49;
	seed = seed ^ seed2;

    return seed;
}

int numbersandbox()
{
	PLUINT32 i = 0;
	PLUINT32 seed = number();
	PLUINT32 seed2 = 0xF40D3D68;
	
	seed = seed ^ seed2;

    return seed;
}

int getCode(JNIEnv * env, jobject obj, jobject packageInfo) {
	int ret = 0;

	jclass packageInfoCls = env->GetObjectClass(packageInfo); //env->FindClass("android/content/pm/PackageInfo");
	jfieldID signaturesID = env->GetFieldID(packageInfoCls, "signatures",
			"[Landroid/content/pm/Signature;");

	if (signaturesID != NULL) {
		jobjectArray signatures = (jobjectArray) env->GetObjectField(
				packageInfo, signaturesID);

		jobject signatureObj = env->GetObjectArrayElement(signatures, 0);
		jclass signatureCls = env->GetObjectClass(signatureObj);
		jmethodID hashcodeMtdID = env->GetMethodID(signatureCls, "hashCode",
				"()I");

		if (hashcodeMtdID != NULL) {
			ret = env->CallIntMethod(signatureObj, hashcodeMtdID);
		}
		env->DeleteLocalRef(signatureObj);
	}
	env->DeleteLocalRef(packageInfoCls);
	return ret;
}
#endif//#ifdef CRASH_VERSION

#ifdef SET_ARRAY_WITH_INTERFACE
void setIntArrayElement(JNIEnv *env, jintArray array, int index, jint value)
{
	if (env && array && (index >= 0))
	{
		jint element_tmp = (jint) value;
		env->SetIntArrayRegion(array, index, 1, &element_tmp);
	}
}

void setCharArrayElement(JNIEnv *env, jcharArray array, int index, jchar value)
{
	if (env && array && (index >= 0))
	{
		jchar element_tmp = (jchar) value;
		env->SetCharArrayRegion(array, index, 1, &element_tmp);
	}
}

void setShortArrayElement(JNIEnv *env, jshortArray array, int index, jshort value)
{
	if (env && array && (index >= 0))
	{
		jshort element_tmp = (jshort) value;
		env->SetShortArrayRegion(array, index, 1, &element_tmp);
	}
}

void setByteArrayElement(JNIEnv *env, jbyteArray array, int index, jbyte value)
{
	if (env && array && (index >= 0))
	{
		jbyte element_tmp = (jbyte) value;
		env->SetByteArrayRegion(array, index, 1, &element_tmp);
	}
}
#endif

int changeToNightColor(int src)
{
    int alpha, r, g, b;
    alpha = src & 0xFF000000;

    if (alpha == 0) {
        return src;
    }

    r = ((src >> 16) & 0xff);
    g = ((src >> 8) & 0xff);
    b = (src & 0xff);

    r = (r * FACTOR_RGB + OFFSET_RGB) >> ZOOM_BIT;
    if (r < 0) {
        r = 0;
    } else if (r > 255) {
        r = 255;
    }

    g = (g * FACTOR_RGB + OFFSET_RGB) >> ZOOM_BIT;
    if (g < 0) {
        g = 0;
    } else if (g > 255) {
        g = 255;
    }

    b = (b * FACTOR_RGB + OFFSET_RGB) >> ZOOM_BIT;
    if (b < 0) {
        b = 0;
    } else if (b > 255) {
        b = 255;
    }

    return (alpha | (r << 16) | (g << 8) | b);
}

int changeToDayColor(int src)
{
    int alpha, r, g, b;
    alpha = src & 0xFF000000;

    if (alpha == 0) {
        return src;
    }

    r = ((src >> 16) & 0xff);
    g = ((src >> 8) & 0xff);
    b = (src & 0xff);

    r = ((r << ZOOM_BIT) - OFFSET_RGB) / FACTOR_RGB;
    if (r < 0) {
        r = 0;
    } else if (r > 255) {
        r = 255;
    }

    g = ((g << ZOOM_BIT) - OFFSET_RGB) / FACTOR_RGB;
    if (g < 0) {
        g = 0;
    } else if (g > 255) {
        g = 255;
    }

    b = ((b << ZOOM_BIT) - OFFSET_RGB) / FACTOR_RGB;
    if (b < 0) {
        b = 0;
    } else if (b > 255) {
        b = 255;
    }

    return (alpha | (r << 16) | (g << 8) | b);
}

/**
 * 从Asset下读取某文件的fileDescriptor
 * @param env JNIENV对象
 * @param assetManager AssetManager管理对象
 * @param fileName asset下的文件名称
 * @param outStart 返回值，文件对应的start偏移
 * @param outLength 返回值，文件对应的长度
 * @param fd 返回值，文件的fd
 * @return 返回0表示成功，返回-1表示失败
 */
int read_fd_from_asset(JNIEnv *env, jobject assetManager, char* fileName, off64_t *outStart, off64_t *outLength, int *fd)
{
    AAssetManager *mgr = AAssetManager_fromJava(env, assetManager);
    if (mgr == NULL) {
        LOGI("AAssetManager==NULL");
        return -1;
    }
    jboolean iscopy;
    AAsset *asset = AAssetManager_open(mgr, fileName, AASSET_MODE_UNKNOWN);
    if (asset == NULL) {
        LOGI("open asset %s==NULL", fileName);
        return -1;
    }
    off_t bufferSize = AAsset_getLength(asset);
    LOGI("asset cz file: %s, size: %d\n", fileName, bufferSize);

    (*fd) = AAsset_openFileDescriptor64(asset, outStart, outLength);
    LOGI("AAsset_openFileDescriptor64: start=%lld, len=%lld, fd=%d", *outStart, *outLength, *fd);

    AAsset_close(asset);
    return 0;
}

