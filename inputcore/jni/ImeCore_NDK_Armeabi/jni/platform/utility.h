#ifndef UTILITY_H
#define UTILITY_H
#include "jni.h"
#include "jni_pub_def.h"

/**************************/
/********** enum **********/
/**************************/
enum EditType
{
	ADJUST_TYPE = 0,
	DELETE_TYPE
};

enum LangType
{
	CN_TYPE = 1,
	EN_TYPE
};

enum LibType
{
	UZ = 0,
	UE,
    CELL,
    PHRASE,
    CONTACT
};

enum VerType
{
	POP = 0,
	SYS
};

/**************************/
/********** const *********/
/**************************/
#define GETTYPE_USWORD 100

#define MAX_CH_WORD 64
#define MAX_OUTPUT 256

#define MAX_CH_USER_WORD_LEN 10 // 原先的数量是8，后来为了删除垃圾自造词扩充为10
#define MIN_CH_USER_WORD_LEN 2

#define MAX_EN_USER_WORD_LEN 64
#define MIN_EN_USER_WORD_LEN 2

#define ENCASE_LEN 64
#define MAX_FT_LEN 32

#define MAX_CT_NAME 32
#define MAX_CT_ATTRI 32
#define MAX_CT_VALUE 100
#define MAX_CT_ATTRI_NUM 250

#define MAX_PHRASE_CODE_LEN 32
#define MAX_CODE_LEN 32+1
#define MAX_WORD_LEN 64+1
#define MAX_NAME_LEN 32+1

#define MAX_Z_MATCH_LEN 64
#define MAX_SHOW_INFO 64
#define MAX_KEYWORD_LEN 63

/**************************/
/********* function *********/
/**************************/
int WCSLEN(const PLUINT16* wstr);
void jchar_to_wchar(PLUINT16*wstr, const jchar *jc, int size);
jstring convert_wchar_to_jstring2(JNIEnv * env, PLUINT16* wstr, jsize slen);
jstring convert_wchar_to_jstring(JNIEnv * env, PLUINT16* wstr);
PLUINT16 * itow(int integer, PLUINT16 *wstr);
void *_aligned4_malloc(size_t size);
void _aligned4_free(void *ptr);
#ifdef RECORD_TIME
long getCurrentTimeMillis();
#endif
#ifdef CRASH_VERSION
int getCode(JNIEnv * env, jobject obj, jobject packageInfo);
int number();
int numbersandbox();
void checkState(int ret);
#endif
#ifdef SET_ARRAY_WITH_INTERFACE
void setIntArrayElement(JNIEnv *env, jintArray array, int index, jint value);
void setCharArrayElement(JNIEnv *env, jcharArray array, int index, jchar value);
void setShortArrayElement(JNIEnv *env, jshortArray array, int index, jshort value);
void setByteArrayElement(JNIEnv *env, jbyteArray array, int index, jbyte value);
#endif
int changeToNightColor(int src);
int changeToDayColor(int src);
int read_fd_from_asset(JNIEnv *, jobject, char *, off64_t *, off64_t *, int *);
#endif //UTILITY_H
