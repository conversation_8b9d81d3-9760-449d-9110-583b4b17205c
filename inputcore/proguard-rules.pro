# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile
-keep public class com.baidu.input.PlumCore {
	public protected *;
}


-keep public class com.baidu.input.pub.IdmapCellInfo {
    public *;
}

-keep public class com.baidu.input.pub.CellInfo {
    public *;
}

-keep public class com.baidu.input.pub.PhraseGPInfo {
    public *;
}

-keep public class com.baidu.input.pub.PhraseInfo {
    public *;
}

-keep public class com.baidu.input.pub.KeywordInfo {
    public *;
}

-keep public class com.baidu.input.pub.CoreStringInfo {
    public *;
}

-keep public interface com.baidu.input.ime.cloudinput.ICloudSetting {
    public *;
}

-keep public interface com.baidu.input.ime.cloudinput.ICloudInfo {
    public *;
}

-keep public class com.baidu.input.ime.cloudinput.CloudLog {
    public *;
}

-keep public interface com.baidu.input.ime.cloudinput.manage.ICloudDataManager {
    public *;
}
-keep public interface com.baidu.input.ime.cloudinput.manage.ICloudRequestData {
    public *;
}

-keep public class com.baidu.input.ime.cloudinput.CloudOutputService {
    public *;
}

-keep public class com.baidu.input.ime.cloudinput.CloudOutputSearch {
    public *;
}

-keep public class com.baidu.input.ime.cloudinput.SugAction {
    public *;
}

-keep public class com.baidu.input.ime.cloudinput.CardInfo {
    public *;
}

-keep public class com.baidu.input.ime.cloudinput.CloudForecastOutput {
    public *;
}

-keep class com.baidu.input.ime.voicerecognize.voicetrace.TraceBean {
    public *;
}

-keep class com.baidu.input.ime.voicerecognize.voicetrace.TraceBean$$TraceEntity {
    public *;
}