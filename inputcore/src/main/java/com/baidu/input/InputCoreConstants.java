package com.baidu.input;

/**
 * 常量
 */
public interface InputCoreConstants {

    /**
     * 内核文件的文件名,在libs下去除lib开头，去除so结尾的名字
     */
    String FILE_DATA_INPUTCORE_FILE_NAME = "inputcore";
    /**
     * 内核文件的版本号
     */
    String FILE_DATA_INPUTCORE_VERSION = "********";
    /**
     * 内核文件的大小，用于relinker采取备用方案本地缓存的文件校验
     */
    int FILE_DATA_INPUTCORE_FILE_SIZE = 3863188;

    /**
     * 词库大小：手写模板
     */
    int IPTFILE_WT_LENGTH = 3071564;


    // 输入法必须的文件
    byte IPTFILE_HZ = 0;
    byte IPTFILE_UZ = 1;
    byte IPTFILE_CELL = 2;
    byte IPTFILE_FT = 3;
    byte IPTFILE_BH = 4;
    byte IPTFILE_UE = 5;
    byte IPTFILE_SYMBIN = 6;
    byte IPTFILE_SYMLBIN = 7;
    /** 个性短语对应的配置文件 phrase.bin */
    byte IPTFILE_CP = 8;
    /** 联系人文件 */
    byte IPTFILE_CATE = 9;
    byte IPTFILE_DEF = 10;
    /** 五笔86模式 */
    byte IPTFILE_WB86 = 11;
    byte IPTFILE_EN = 12;
    byte IPTFILE_GRAM = 13;
    /** 输入时会触发多媒体搜索表情的关键字 */
    byte IPTFILE_KEYWORD = 14;
    /** 根据ttf过滤的文件 */
    byte IPTFILE_TTFFILTER = 15;
    /** 手写自造次 */
    byte IPTFILE_USERWORD_HW = 16;
    /** 火星文对应关系 */
    byte IPTFILE_MARSWORD = 17;
    /** 其它特殊的对应关系 */
    byte IPTFILE_OTHERWORD = 18;
    // added for zhuyin-cangjie
    byte IPTFILE_CANGJIE = 19;
    @Deprecated
    byte IPTFILE_ZHUYIN_HZ = 20;
    @Deprecated
    byte IPTFILE_ZHUYIN_CZ = 21;
    /** 拼音纠正文件 */
    byte IPTFILE_CH_COR = 22;
    /** 盲打拼音模板 */
    byte IPTFILE_KP = 23;
    /** 手写汉字模板文件 */
    byte IPTFILE_WT = 24;
    /** 歇后语模板文件 */
    byte IPTFILE_XHY = 25;
    /** 手写部首筛选模板文件 */
    byte IPTFILE_WT_BS = 26;
    /** 场景化模板文件 */
    @Deprecated
    byte IPTFILE_HZ_LABEL = 27;
    /** 颜文字id映射文件 */
    byte IPTFILE_OT_IDMAP = 28;
    /** 云输入关键字文件 */
    byte IPTFILE_CLOUD_KEYWORD = 29;
    /** 候选字上下文文件 */
    byte IPTFILE_CAND_CONTEXT = 30;
    /** vkword文件 */
    byte IPTFILE_VKWORD = 31;
    /** search文件 */
    byte IPTFILE_SEARCH = 32;
    /** 汉字音调文件 */
    byte IPTFILE_HZ_TONE = 33;
    /** 智能问答文件 */
    byte IPTFILE_AUTOREPLY = 34;
    /** 注音自造词**/
    byte IPTFILE_ZY_USER = 35;
    /** 所有用户数据备份 */
    byte IPTFILE_US_BAK = 36;
    /** 系统词库 */
    byte IPTFILE_CZ = 37;
    /** 动态热区条件概率文件 */
    byte IPTFILE_LTP = 38;
    /** 个性化动态热区文件 */
    byte IPTFILE_LTP_USR = 39;
    /** app名字和框属性对应场景ID和词库文件 */
    byte IPTFILE_APP_MAP = 40;
    /** 省份城市 */
    byte IPTFILE_PROV_CITY = 41;
    /** 用户的语音纠错文件 */
    byte IPTFILE_VOICE_CORRECT = 42;
    /** 本地联想运营词 */
    byte IPTFILE_SPECIAL = 43;
    /** 五笔98模式 */
    byte IPTFILE_WB98 = 44;
    /** 仓颉速成模式 */
    byte IPTFILE_CANGJIE_QUICK = 45;
    /** 中文自造词新 */
    byte IPTFILE_USR3USER = 46;
    /** 新的语音语调文件 */
    byte IPTFILE_HZCORRTONE = 47;
    /** 新的英文自造词文件 */
    byte IPTFILE_ENUSER = 48;
    /** 新的防误触模型 */
    byte IPTFILE_PY3TRANSPROB = 49;
    /** 用户符号文件 */
    byte IPTFILE_SYM_USER = 50;
    /** 用户细胞词文件 */
    byte IPTFILE_USR3CELL = 51;
    /** 注音词库文件 */
    byte IPTFILE_ZY = 52;
    /**
     * 英文系统词库文件
     */
    byte IPTFILE_EN_SYS = 53;

    String czMd5 = "a62a655ba539f10593b59314f9c2a9b3";
    String ftMD5 = "134d8b90cbb530f63f83fd34c705530a";
    String bhMD5 = "8d32cc83af48b2acb644f9f3f1cd8ab9";
    String wbMD5 = "626a71392497d0cd88141d0303c6d057";
    String enMD5 = "14a270371ccd96521b51cf10ae52e178";
    String xhyMD5 = "03748b10d94a747d80d44dc7723dcf45";
    String iptwtMD5 = "1fcdce6758e2885ece7ed4093abc32ff";

    String symMD5 = "8516b0c6993dceb07c88cd23931fff2e";
    String hzMd5 = "403de65cbd4f773dc64726e16427728d";
    String kpMD5 = "4a90d5d7c6d5802100794cb91293a4f4";
    String hzLabelMD5 = "ca10b647b0d8aa5b88ac66078cea57ad";
    String chCorMD5 = "0dece414ee5f4a8311a7f6706d2b4a4f";
    String hzCorrToneMD5 = "019d9e2d1d4d7d70b20be83072427b89";

}
