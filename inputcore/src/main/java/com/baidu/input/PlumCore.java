package com.baidu.input;

import com.baidu.input.ime.cloudinput.CloudForecastOutput;
import com.baidu.input.ime.cloudinput.CloudLog;
import com.baidu.input.ime.cloudinput.CloudOutputSearch;
import com.baidu.input.ime.cloudinput.ICloudInfo;
import com.baidu.input.ime.cloudinput.CloudOutputService;
import com.baidu.input.ime.cloudinput.ICloudSetting;
import com.baidu.input.ime.cloudinput.manage.ICloudDataManager;
import com.baidu.input.ime.cloudinput.manage.ICloudRequestData;
// import com.baidu.input.ime.voicerecognize.voicetrace.TraceBean;
import com.baidu.input.pub.CellInfo;
import com.baidu.input.pub.CoreStringInfo;
import com.baidu.input.pub.IdmapCellInfo;
import com.baidu.input.pub.KeywordInfo;
import com.baidu.input.pub.PhraseGPInfo;
import com.baidu.input.pub.PhraseInfo;

import android.content.pm.PackageInfo;
import android.content.res.AssetManager;
import android.graphics.Bitmap;

public abstract class PlumCore {
    // 内核输入定义
    public final static byte CORE_NONE = 0;
    public final static byte CORE_PY = 1;
    public final static byte CORE_EN = 2;
    public final static byte CORE_BH = 3;
    public final static byte CORE_WB = 4;
    public final static byte CORE_DEF = 5;
    public final static byte CORE_SYM = 6;
    public final static byte CORE_HW = 9;
    public final static byte CORE_ZHUYIN = 10;// modified for zhuyin-cangjie
    public final static byte CORE_CANGJIE = 11;// modified for zhuyin-cangjie
    public final static byte CORE_PY_EDIT = 12;

    public final static int SET_CONFIG = 1;
    public final static int GET_CONFIG = 2;
    public final static int SET_MOHU = 3;
    public final static int GET_MOHU = 4;
    public final static int SET_SHUANGPIN = 5;
    public final static int GET_SHUANGPIN = 6;
    public final static int SET_HW_CONFIG = 7;
    public final static int GET_HW_CONFIG = 8;

    public final static int USERWORDS_EDIT_ADJUST = 0;
    public final static int USERWORDS_EDIT_DELETE = 1;

    public final static int GETTYPE_NULL = -1;
    public final static int GETTYPE_CAND = 0;// //普通候选词///////////////
    public final static int GETTYPE_LIST = 1;// //拼音列表(或 笔画筛选 等)/////
    public final static int GETTYPE_HOTSYM = 2; // /<常用符号(目前不支持)
    public final static int GETTYPE_CAND_ORG = 3; // /<普通候选词原始形态(无繁简转换或英文大小写转换)
    public final static int GETTYPE_CAND_WB_TIP = 4; // <中文候选词(带五笔编码提示)
    public final static int GETTYPE_PY_LIST = 5; // /<拼音列表
    public final static int GETTYPE_BH_LIST = 6; // 笔画筛选列表
    public static final int GETTYPE_COMMIT_CAND = 7; // 上屏候选词（显示候选词可能与上屏候选词不同）
    public final static int GETTYPE_USWORD = 100; // //自造词.//////////////////

    public final static int SEARCHKEY_EN = 20;
    /**
     * 查询所有中文词
     */
    public static final int SEARCHKEY_ALL_CH = 30;

    public final static byte CELLVER_POP = 0;
    public final static byte CELLVER_SYS = 1;

    public final static byte PHRASE_EDIT = 1;
    public final static byte PHRASE_DEL = 2;
    public final static byte PHRASE_ADD = 3;
    public final static byte PHRASE_REMOVE = 4;

    public final static byte MATCHINFO_WORDCNT = 0;
    public final static byte MATCHINFO_CODECNT = 1;
    public final static byte MATCHINFO_ZI_ID = 2;

    /**
     * 需要调频的词压栈(idx 表示要进行压栈候选词索引)
     */
    public final static int CMD_ADJUST_PUSH = 1;
    /**
     * 需要调频的词弹出(退格的时候才用, idx 表示弹出的词的个数,idx=0表示弹出所有词(清空栈))
     */
    public final static int CMD_ADJUST_POP = 2;
    /**
     * 对栈内元素执行调调频操作 (此时:设定idx = 0就可以了)
     */
    public final static int CMD_ADJUST_COMMIT = 3;
    /**
     * 删除词(idx 表示要进行删除的候选词索引)，支持中英文，英文注意大小写
     */
    public final static int CMD_ADJUST_DELETE = 4;
    /**
     * 上屏后,对已经压栈的词(但还没有commit),以字为单位做退格操作,idx 表示退格的字的个数
     */
    public final static int CMD_ADJUST_BACKSPACE = 5;
    /**
     * 清除逐词输入优化状态(面板弹起,输入标点,等句子中断行为
     */
    public final static int CMD_PREDICT_CLEAN = 6;
    /**
     * 查询当前逐词前一个词的长度
     */
    public final static int CMD_PREDICT_PREWORD_LEN = 7;
    /**
     * 查询当前调频栈的PUSH次数
     */
    public final static int CMD_STACK_ITEM_CNT = 8;
    /**
     * 查询当前栈里面,某个第idx个元素的长度.(没有这个元素就返回0)
     */
    public final static int CMD_STACK_ITEM_LEN = 9;
    /**
     * 判断是否可以删除.(返回值>0 时,才表示可以删除)
     */
    public final static int CMD_IS_DELABLE = 20;
    /**
     * 判断是否存在系统词.(返回值>0 时,才表示存在系统词, = 0和<0 分别表示不存在和出现错误.)
     */
    public final static int CMD_IS_SYSWORD = 21;
    /**
     * 一次完整的查询结束，输入码已经被无法复原的清除掉的时候，使用该命令做一次清理,无法复原的意思是，候选词已经上屏可以调用联想查询的时候，或者左划清除所有输入
     */
    public final static int CMD_INPUT_CLEAN_UP = 10;
    /**
     * 清除list的选中状态
     */
    public final static int CMD_LIST_CLEAN = 30;
    /**
     * 选中当前拼音list中的第idx个拼音压栈
     */
    public final static int CMD_PY_LIST_PUSH = 31;
    /**
     * 将当前栈顶的拼音弹出
     */
    public final static int CMD_PY_LIST_POP = 32;
    /**
     * 选中当前笔画list中的第idx个笔画压栈
     */
    public final static int CMD_BH_LIST_PUSH = 33;
    /**
     * 将当前栈顶的笔画弹出
     */
    public final static int CMD_BH_LIST_POP = 34;
    /**
     * 返回当前拼音list缓存长度
     */
    public final static int CMD_PY_LIST_CACHE_LEN = 35;
    /**
     * 选择当前的list查询模式，中文或者英文，idx = 1时代表英文，idx = 0时代表中文
     */
    public final static int CMD_LIST_FIND_MODE = 36;
    /**
     * 获取相应联系人个数
     */
    public final static int CMD_CONTACT_CNT = 40;
    /**
     * 执行联想查询.
     */
    public final static int CMD_FIND_LIAN = 41;
    /**
     * 使用上次的条件重新查询一次
     */
    public final static int CMD_FIND_REFRESH = 42;
    /**
     * 清除手写状态
     */
    public final static int CMD_HW_CLEAN = 43;
    /**
     * 删除某个词对应联系人的信息
     */
    public final static int CMD_CONTACT_DEL = 44;
    /**
     * 删除某个词对应联系人的信息并恢复默认词频
     */
    public final static int CMD_CONTACT_RESTORE_FREQ = 45;
    /**
     * 删除某个词对应联系人以及对应的自造词
     */
    public final static int CMD_CONTACT_DEL_ALL = 46;
    /**
     * 清除整句出表情的状态
     */
    public final static int CMD_SENTENCE_LIAN_CLEAN = 50;
    /**
     * 清除英文list的选中状态 请勿使用该接口，应当使用CMD_INPUT_CLEAN_UP
     */
    public final static int CMD_EN_LIST_CLEAN = 60;
    /**
     * 选中当前英文list中的第idx个英文压栈,当选中的是“数字”时，返回1，其他返回0；
     */
    public final static int CMD_EN_LIST_PUSH = 61;
    /**
     * 将当前栈顶的英文弹出 // 暂不实现
     */
    public final static int CMD_EN_LIST_POP = 62;
    /**
     * 返回当前英文list缓存长度
     */
    public final static int CMD_EN_LIST_CACHE_LEN = 63;
    /**
     * 手写回退
     */
    public static final int CMD_HW_BACKSPACE = 64;
    // /////////////////////////////////输入码操作标记INPUT_FLAG////////////////////////////
    // //////////////////////////////用于ipt_query_set_encase的参数////////////////////////////
    /**
     * 正常状态
     */
    public final static int INPUT_FLAG_NORMAL = 0x00;
    /**
     * 强制大写，或是shift键被按下
     */
    public final static int INPUT_FLAG_UPPER_CASE = 0x01;
    /**
     * 强制小写
     */
    public final static int INPUT_FLAG_LOWER_CASE = 0x02;
    /**
     * 上划精确输入
     */
    public final static int INPUT_FLAG_PRECISE = 0x04;

    // ////////////////////////////////手写查询范围HW_FIND_RANGE/////////////////////////
    // ////////////////////////////////用于手写查询范围设置
    /**
     * 常用字
     */
    public final static int HW_FIND_RANGE_CH_COMMON = (0x01);
    /**
     * 生僻字
     */
    public final static int HW_FIND_RANGE_CH_RARE = (0x02);
    /**
     * 偏旁部首
     */
    public final static int HW_FIND_RANGE_CH_RADICAL = (0x04);
    /**
     * 数字
     */
    public final static int HW_FIND_RANGE_NUM = (0x08);
    /**
     * 英文小写
     */
    public final static int HW_FIND_RANGE_EN_LOWER = (0x10);
    /**
     * 英文大写
     */
    public final static int HW_FIND_RANGE_EN_UPPER = (0x20);
    /**
     * 常用标点
     */
    public final static int HW_FIND_RANGE_PUN_COMMON = (0x40);
    /**
     * 扩展标点
     */
    public final static int HW_FIND_RANGE_PUN_EXT = (0x80);
    /**
     * 常用符号
     */
    public final static int HW_FIND_RANGE_SYM_COMMON = (0x0100);
    /**
     * 扩展符号
     */
    public final static int HW_FIND_RANGE_SYM_EXT = (0x0200);
    /**
     * 特殊符号1
     */
    public final static int HW_FIND_RANGE_SYM_RARE_G1 = (0x0400);
    /**
     * 特殊符号2
     */
    public final static int HW_FIND_RANGE_SYM_RARE_G2 = (0x0800);
    /**
     * 特殊符号3
     */
    public final static int HW_FIND_RANGE_SYM_RARE_G3 = (0x1000);
    /**
     * 不包括偏旁部首的汉字
     */
    public final static int HW_FIND_RANGE_CH_NM = (HW_FIND_RANGE_CH_COMMON | HW_FIND_RANGE_CH_RARE);
    /**
     * 英文
     */
    public final static int HW_FIND_RANGE_EN = (HW_FIND_RANGE_EN_LOWER | HW_FIND_RANGE_EN_UPPER);
    /**
     * 标点
     */
    public final static int HW_FIND_RANGE_PUN = (HW_FIND_RANGE_PUN_COMMON | HW_FIND_RANGE_PUN_EXT);
    /**
     * 符号
     */
    public final static int HW_FIND_RANGE_SYM = (HW_FIND_RANGE_SYM_COMMON | HW_FIND_RANGE_SYM_EXT
            | HW_FIND_RANGE_SYM_RARE_G1 | HW_FIND_RANGE_SYM_RARE_G2 | HW_FIND_RANGE_SYM_RARE_G3);
    /**
     * 所有汉字
     */
    public final static int HW_FIND_RANGE_CH_ALL = (HW_FIND_RANGE_CH_NM | HW_FIND_RANGE_CH_RADICAL);
    /**
     * 生僻字+偏旁部首
     */
    public final static int HW_FIND_RANGE_CH_RARE_RADICAL = (HW_FIND_RANGE_CH_RARE | HW_FIND_RANGE_CH_RADICAL);
    /**
     * 英文+数字
     */
    public final static int HW_FIND_RANGE_EN_NUM = (HW_FIND_RANGE_EN | HW_FIND_RANGE_NUM);
    /**
     * 标点+符号
     */
    public final static int HW_FIND_RANGE_PUN_SYM = (HW_FIND_RANGE_PUN | HW_FIND_RANGE_SYM);

    /**
     * 虚框范围
     */
    public final static int HW_FIND_RANGE_DASH = (HW_FIND_RANGE_EN_NUM | HW_FIND_RANGE_PUN_SYM);
    /**
     * 所有范围
     */
    public final static int HW_FIND_RANGE_ALL = (HW_FIND_RANGE_CH_ALL | HW_FIND_RANGE_DASH);

    // ////////////////////////////////
    // /手写输入选项(手写输入时使用)
    /**
     * 单字输入
     */
    public final static int HW_INPUT_TYPE_HZ = 1;
    /**
     * 叠写输入
     */
    public final static int HW_INPUT_TYPE_REDUP = 2;
    /**
     * 连写输入
     */
    public final static int HW_INPUT_TYPE_NM = 4;
    /**
     * 英文数字输入
     */
    public final static int HW_INPUT_TYPE_EN_NUM = 8;

    /*********************************/
    /*********************************/
    /* 内核初始化、配置、释放相关的接口 */
    /*********************************/
    /*********************************/

    // /////////////////////////////////输入码错误标记////////////////////////////
    /**
     * 输错
     */
    public final static byte PY_IEC_FLAG_TE = 0x1;
    /**
     * 少输
     */
    public final static byte PY_IEC_FLAG_LE = 0x2;
    /**
     * 多输
     */
    public final static byte PY_IEC_FLAG_ME = 0x4;
    /**
     * 交换输入
     */
    public final static byte PY_IEC_FLAG_SE = 0x8;
    /**
     * 少输，在最前
     */
    public final static byte PY_IEC_FLAG_POS = 0x10;
    /**
     * 少输，在最前
     */
    public final static byte PY_IEC_FLAG_LE_PRE = PY_IEC_FLAG_LE | PY_IEC_FLAG_POS;

    public final static int CIKU_TYPE_CZ = 0;
    public final static int CIKU_TYPE_GRAM = 1;


    /**
     * 场景类型定义，没有场景
     **/
    public static final int NO_CONTEXT_ID = 0;
    /**
     * 场景类型定义，电商场景
     **/
    public static final int CONTEXT_ID_MERCH = 0x1;
    /**
     * 场景类型定义，应用市场场景
     **/
    public static final int CONTEXT_ID_APP = 0x2;
    /**
     * 场景类型定义，地理位置场景
     **/
    public static final int CONTEXT_ID_LBS = 0x4;
    /**
     * 场景类型定义，多媒体场景
     **/
    public static final int CONTEXT_ID_MEDIA = 0x8;
    /**
     * 场景类型定义，点评团购场景
     **/
    public static final int CONTEXT_ID_GROUPON = 0x10;
    /**
     * 场景类型定义，浏览器场景
     **/
    public static final int CONTEXT_ID_BROWSER = 0x20;
    /**
     * 场景类型定义，聊天软件场景
     **/
    public static final int CONTEXT_ID_SNS = 0x40;
    /**
     * 场景类型定义，音乐类场景
     **/
    public static final int CONTEXT_ID_MUSIC = 0x80;
    /**
     * 场景类型定义，B站类场景
     **/
    public static final int CONTEXT_ID_BILI = 0x100;
    /**
     * 场景类型定义，贴吧论坛场景
     **/
    public static final int CONTEXT_ID_TIEBA = 0x200;
    /**
     * 场景类型定义，交友类场景
     **/
    public static final int CONTEXT_ID_SOCIAL = 0x400;
    /**
     * 场景类型定义，王者荣耀场景
     **/
    public static final int CONTEXT_ID_WZRY = 0x800;

    /**
     * 当list中选中“英文”时传给内核的值,PlKpAppendPoint穿的pos参数常量,pos只能为0以及LIST_ENGLISH_SELECT的值
     */
    public static final int LIST_ENGLISH_SELECT = 127;

    /**
     * 云输入触发词
     */
    public static final int EX_EX_CAND_FLAG_WHITE = 0x01;
    /**
     * 云输入触发词，只能够触发联想，没有该标记位代表有候选
     */
    public static final int EX_EX_CAND_FLAG_WHITE_LIAN = 0x02;
    /**
     * 云输入触发词，纯文本类型，没有该标记位代表非纯文本类型
     */
    public static final int EX_EX_CAND_FLAG_WHITE_TEXT = 0x4;
    /**
     * 云缓存词，且与本地重复
     */
    public static final int EX_EX_CAND_FALG_CLOUD_DUPLICATE = 0x1000;

    /**
     * 支持CPU的neom指令,{@link #PlSetCpuMsg(String)}
     */
    public static final int CPU_NEON_SUPPORT = 0;
    public static final int CPU_NEON_NOT_SUPPORT = -1;
    public static final int CPU_NEON_NOT_INITIALIZE = 1;


    // ////////////////////////////候选词埋点标记定义/////////////////////////////////////////
    // //////////////////////////低12位每一个bit都是独立的含义，高4位用于枚举定义///////////////////
    // 系统词
    public static final int BURY_POINT_CAND_FLAG_SYS = (0x1);
    // 用户词
    public static final int BURY_POINT_CAND_FLAG_US = (0x2);
    // 细胞词
    public static final int BURY_POINT_CAND_FLAG_CELL = (0x4);
    // 整句
    public static final int BURY_POINT_CAND_FLAG_SEN = (0x8);
    // 逐词
    public static final int BURY_POINT_CAND_FLAG_PRED = (0x10);
    // 纠错
    public static final int BURY_POINT_CAND_FLAG_IEC = (0x20);
    //移动光标触发联想
    public static final int BURY_POINT_CAND_FLAFG_CURSOR_LIAN = (0x40);
    // ///////////////////////////////////////////////////////////////
    // 个性短语
    public static final int BURY_POINT_CAND_FLAG_PHRASE = (1 << 12);
    // 云缓存
    public static final int BURY_POINT_CAND_FLAG_CLOUD_CACHE = (2 << 12);
    // 云预测
    public static final int BURY_POINT_CAND_FLAG_CLOUD_FORCAST = (3 << 12);
    // vkword
    public static final int BURY_POINT_CAND_FLAG_VKWORD = (4 << 12);
    // 二次元
    public static final int BURY_POINT_CAND_FLAG_NIJIGEN = (5 << 12);
    // 表情
    public static final int BURY_POINT_CAND_FLAG_EMOJI = (6 << 12);
    // 颜文字
    public static final int BURY_POINT_CAND_FLAG_EMOTICON = (7 << 12);

    // 云输入词
    public static final int BURY_POINT_CAND_FLAG_CLOUD = (14 << 12);
    // sug词
    public static final int BURY_POINT_CAND_FLAG_SUG = (15 << 12);

    /** 名字识别 */
    public static final int AUTOREPLY_INTENT_NAME = 1;
    /** 手机号 */
    public static final int AUTOREPLY_INTENT_MOBILE = 2;
    /** 地址 */
    public static final int AUTOREPLY_INTENT_ADDRESS = 3;
    /** QQ */
    public static final int AUTOREPLY_INTENT_QQ = 4;
    /** 微信 */
    public static final int AUTOREPLY_INTENT_WEIXIN = 5;
    /** 生日 */
    public static final int AUTOREPLY_INTENT_BIRTH = 6;
    /** 身份证 */
    public static final int AUTOREPLY_INTENT_IDNUMBER = 7;
    /** 电子邮箱 */
    public static final int AUTOREPLY_INTENT_EMAIL = 8;

    // ///////////////////////////////////////////////////////////////////////////////////////////////////
    // ////////////////////////////////////// 防误触常量 //////////////////////////////////////
    /** 内核防误触功能定义的A键常量 */
    public static final byte TOUCHKP_KEY_RECT_A = 1;
    /** 内核防误触功能定义的Z键常量,a-z按序排列 */
    public static final byte TOUCHKP_KEY_RECT_Z = 26;
    /** shift键 */
    public static final byte TOUCHKP_KEY_RECT_SHIFT = 27;
    /** delete键 */
    public static final byte TOUCHKP_KEY_RECT_DEL = 28;
    /** 数字面板切换键 */
    public static final byte TOUCHKP_KEY_RECT_NUM = 29;
    /** 中英文切换键 */
    public static final byte TOUCHKP_KEY_RECT_CHEN = 30;
    /** 逗号 */
    public static final byte TOUCHKP_KEY_RECT_COMMA = 31;
    /** 空格 */
    public static final byte TOUCHKP_KEY_RECT_SPACE = 32;
    /** 句号 */
    public static final byte TOUCHKP_KEY_RECT_STOP = 33;
    /** 符号面板切换 */
    public static final byte TOUCHKP_KEY_RECT_SYM = 34;
    /** 回车 */
    public static final byte TOUCHKP_KEY_RECT_ENTER = 35;
    /** emoji键 */
    public static final byte TOUCHKP_KEY_RECT_EMOJI = 36;
    /** 分词符"'"，已被使用，目前和KEY_RECT_SHIFT相同 */
    public static final byte TOUCHKP_KEY_RECT_SPLIT = 39;

    /** 竖屏 */
    public static final byte PHONE_STATE_PORTRAIT = 0;
    /** 横屏 */
    public static final byte PHONE_STATE_LANDSCAPE = 1;

    /** 未知属性 */
    public static final byte ATTRIBUTE_OTHER = 0x1;
    /** 搜索框属性 */
    public static final byte ATTRIBUTE_SEARCH = 0x2;

    /**
     * 初始化 载入词库和字库
     *
     * @param iptFiles      待从/data/data下加载的内核文件列表
     * @param assetIptFiles 待从asset下加载的文件列表，和iptFiles数组长度相同，同索引位置表征同一个文件。对应位置如果指定了asset下的路径，则优先从asset下加载
     * @param pi            包信息
     * @param assetManager  AssetManager对象
     * @return 成功返回true，失败返回false
     */
    protected native boolean PlInit(String[] iptFiles, String[] assetIptFiles, PackageInfo pi, AssetManager assetManager);

    /**
     * 刷新内核，重载手写库
     *
     * @param fileName 内核词库数据文件, 指定内核需要刷新的数据文件的位置，若文件不存在不刷新；如果为null表示只卸载
     * @return 0代表刷新成功; 返回值<0代表刷新失败或没有刷新
     */
    public native int PlHandWritingRefresh(String fileName);

    /**
     * 刷新内核，重载二元库gram.bin
     *
     * @param fileName 内核词库数据文件, 指定内核需要刷新的数据文件的位置，若文件不存在不刷新；如果为null表示只卸载
     * @return 0代表刷新成功; 返回值<0代表刷新失败或没有刷新
     */
    public native int PlGramRefresh(String fileName);

    /**
     * 刷新内核，重载智能回复文件autoreply.bin
     *
     * @param fileName 内核词库数据文件, 指定内核需要刷新的数据文件的位置，若文件不存在不刷新；如果为null表示只卸载
     * @return 0代表刷新成功; 返回值<0代表刷新失败或没有刷新
     */
    public native int PlAutoReplyRefresh(String fileName);

    /**
     * 卸载词库和字库
     *
     * @return 成功返回true，失败返回false
     */
    public native boolean PlClose();

    // /**
    // * 初始化 载入词库和字库
    // *
    // * @param fileNames
    // * 文件名
    // * @param am
    // * assetmanager的实例
    // * @return 成功返回true，失败返回false
    // */
    // protected native boolean PlInit2(String[] fileNames, AssetManager am);
    //
    // /**
    // * 卸载词库和字库
    // *
    // * @return
    // */
    // protected native boolean PlClose2();

    /**
     * 将更改保存到文件
     *
     * @return
     */
    public native void PlFlush();

    /**
     * 获得内核版本号
     *
     * @return 内核词库版本号
     */
    public native int PlGetCoreVersion();

    /**
     * 设置输入状态
     *
     * @param params 输入状态拼接顺序：（48 byte） 字频；词频；常用字/生僻字；只显示字，默认； 整句；中英混输(0:关闭,1:开启,2:开启,但屏蔽单个字母)；简繁转换；笔画 下 字形优先；
     *               开启五笔拼音；开启双拼；不显示双拼原始输入码；开启简拼二元整句； 英文排序，默认为BYFREQ=0，BYLEN=1，BYABC=2；英文大小写，默认为NORMAL=0， FIRST=1，
     *               ALL=2；reserved；reserved； 智能纠错；个性短语(0=关闭, 1-9 = 个性短语的默认出现位置)；是否开启自动实时保存；符号联想开关(暂未开通该功能)；
     *               逐词输入优化;中英整句混输;reserved;reserved 6个四个字节整数； 模糊音拼接顺序：（16 byte） ch;sh;zh;k; f;n;ang;eng;
     *               ing;iang;uang;reserved; 四个字节整数 双拼方案：（64 byte） 声母[24];韵母[33];reserved[3]； 四个字节整数；
     * @param type   1：设置输入状态 2：读得输入状态 3：设置模糊音 4：读取模糊音 5：设置双拼方案 6：读取双拼方案
     * @return 成功返回true， 失败返回false
     */
    public native boolean PlConfig(byte[] params, int type);

    /**
     * 获取双拼声母映射
     *
     * @param keyChar
     * @param shengList [in/out] 可分配一个64字节的buffer
     * @return 返回shengList的实际个数
     */
    public native int PlGetSPMapSheng(byte keyChar, byte[] shengList);

    /**
     * 获取双拼韵母映射
     *
     * @param keyChar
     * @param yunList [in/out] 可分配一个128个字节的buffer
     * @return 返回yunList的实际个数
     */
    public native int PlGetSPMapYun(byte keyChar, byte[] yunList);

    /**
     * 设置载入双拼文件
     *
     * @param filename
     * @return 成功返回0, I/0错误返回-1, 配置文件错误返回-2
     */
    public native int PlSetSP(String filename);

    /**
     * 设置精确模糊对应关系
     *
     * @param id
     * @param ch
     */
    public native void PlSetKeyMap(int id, char ch, int level);

    /**
     * 设置精确模糊对应关系
     *
     * @param id
     * @param ch
     */
    public final void PlSetKeyMap(int id, char ch) {
        PlSetKeyMap(id, ch, 255);
    }

    /**
     * 添加键盘左右纠错的映射
     */
    public native void PlSetKeyMapAutofix();

    /**
     * 清除精确模糊关系
     */
    public native void PlCleanKeyMap();

    /**
     * 载入默认键盘映射表.(T9+QWERT+默认的智能纠错)
     * <p>
     * 目前上层代码中还没有使用场景，主要是内核自己使用；现在先开放出来，后续可能会用到
     */
    public native void PlLoadDefaultKeyMap();

    /*********************************/
    /*********************************/
    /* 基本查询功能相关的接口 */
    /*********************************/
    /*********************************/

    /**
     * 设定英文输出的大小写形式.长度为64的一个buffer
     *
     * @param shifts 英文大小写控制列表
     * @return 错误代码
     */
    public native int PlSetEncase(byte[] shifts);

    /**
     * 按照输入的搜索词库和字库
     *
     * @param input     输入
     * @param pos       0 -- 默认   64 -- 最末    127 -- 英文
     * @param type      选项 （1:拼音, 2：英文, 3：笔画, 4：五笔,5：自定义, 6：符号, 7:个性短语 , 8:联想 ）
     * @param contextId 场景类别Id
     * @return 错误代码
     */
    public native int PlFind(byte[] input, int pos, int type, int contextId);

    /**
     * 词语联想
     *
     * @param zids 需要联想的内容:每个字的ID组成的数组 或者unicode码组，以‘\0’结尾
     * @param len  需要联想的内容的长度：字长度 或者0
     * @return 错误代码
     */
    public native int PlFindLian(char[] zids, int len);

    /**
     * 通过zid对手写联想查询
     *
     * @param zids 需要联想的内容:每个字的ID组成的数组 或者unicode码组，以‘\0’结尾
     * @param len  需要联想的内容的长度：字长度 或者0
     * @return 0代表查找成功; 返回值<0代表查找失败
     */
    public native int PlFindLianbyHW(char[] zids, int len);

    /**
     * 清楚查询（Find和FindLian）的缓存
     */
    public native void PlClean();

    /**
     * 获得当次查询的候选字或列表或常用符号的个数
     *
     * @param type 0：普通候选词 1：拼音列表（或笔画筛选列表） 2：常用符号
     * @return 个数
     */
    public native int PlCount(int type);

    /**
     * 获取查询到的候选字或候选词
     *
     * @param cs   此处 将使用Java的反射机制，返回查询到的字或词
     * @param id   候选字或候选词的位置，一般 从0开始到PlCount
     * @param type 选项 （1:普通候选词, 2：拼音列表（或笔画筛选列表）, 3：常用符号）
     * @return 候选字或候选词的类型 （）
     */
    public native int PlGetStr(CoreStringInfo cs, int id, int type);

    /**
     * 根据候选词偏移范围获取查询到的候选字或候选词
     *
     * @param cs      此处 将使用Java的反射机制，返回查询到的字或词
     * @param startId 候选字或候选词的起始位置
     * @param endId   候选字或候选词的结束位置
     * @param type    选项 （1:普通候选词, 2：拼音列表（或笔画筛选列表）, 3：常用符号）
     * @return
     */
    public native int PlGetStrRange(CoreStringInfo[] cs, int startId, int endId, int type);

    /**
     * 获取候选词扩展类型
     *
     * @param id 候选字或候选词的位置，一般 从0开始到PlCount
     * @return 候选字或候选词的扩展类型 （0x1为云输入白名单）
     */
    public native int PlGetExFlag(int id);

    /**
     * 获取选中中文候选字字词的信息
     *
     * @param id 候选列表中的索引
     * @return 返回选中中文候选字字词的信息（0:返回字的长度, 1:返回匹配的输入码的长度,2:每个字的编码(多少个字就有多少个);3:每个字对应输入码的长度（多少个字就有多少个））
     */
    public native int[] PlMatchInfo(int id);

    /**
     * 得到输入显示
     *
     * @param id   选中的候选词的索引
     * @param info 选中的候选词的索引的长度
     * @return 返回输入显示
     */
    public native String PlGetShow(int id, byte[] info);

    /**
     * CMD_ADJUST_DELETE = 4,////删除词(idx 表示要进行删除的候选词索引)，支持中英文，英文注意大小写 CMD_IS_DELABLE = 20,////判断是否可以删除.(返回值>0 时,才表示可以删除)
     * CMD_IS_SYSWORD = 21,////判断是否存在系统词.(返回值>0 时,才表示存在系统词, = 0和<0 分别表示不存在和出现错误.) CMD_CONTACT_CNT = 40, ///获取相应联系人个数.
     * CMD_FIND_LIAN = 41,////执行联想查询.
     *
     * @param id      候选词的索引
     * @param cmdType 动作的类型
     * @return CMD_ADJUST_DELETE：//返回1表示存在//返回0表示不存在//返回负数,表示出错. CMD_IS_DELABLE：返回1表示可以删除，返回0表示无法删除 CMD_IS_SYSWORD：返回值>0
     * 时,才表示存在系统词, = 0和<0 分别表示不存在和出现错误. CMD_CONTACT_CNT：返回联系人个数 CMD_FIND_LIAN：返回错误代码
     */
    public native int PlQueryCmd(int id, int cmdType);

    /*********************************/
    /*********************************/
    /* 用户词导入导出相关接口 */
    /*********************************/
    /*********************************/

    /**
     * 通过 自造词的字的 id，调频中文自造词，添加自造词或删除中文自造词
     *
     * @param tab      里面保存的是自造词中每个字的id
     * @param editType 调整的方式（0：调频自造词，1：删除自造词）
     */
    public native boolean PlAdjustCnWord(char[] tab, int len, int editType);

    /**
     * 通过zid对手写联想调频
     *
     * @param zids 里面保存的是自造词中每个字的id
     * @param len  需要调频词的长度，当小于2，大于8时无效，直接返回失败
     * @return 调频成功, 返回0; 调频失败, 返回值<0
     */
    public native int PlAdjustCnWordbyHW(char[] zids, int len);

    /**
     * 通过 自造 词的 unicode码 ，调频中文自造词，添加自造词 或删除中文自造词
     *
     * @param str      里面保存的是自造词中每个字的id
     * @param pinyin   通过unicode码处理 自造词，存在多音字的情况，因此需要添加拼音
     * @param editType 调整的方式（0：调频自造词，1：删除自造词）
     */
    public native boolean PlAdjustCnWordbyUni(String str, String pinyin, int editType);

    /**
     * 调频英文自造词或删除英文自造词
     *
     * @param str      英文自造词
     * @param editType 调整的方式（0：调频自造词，1：删除自造词）
     */
    public native boolean PlAdjustEnWord(String str, int editType);

    /**
     * @param searchKey 用于搜索的关键字的unicode码串
     * @param type      用户自造词的类型 （2：二字词，3：三字词，... ，8：八字词，20：英文自造词）
     * @return
     */
    public native int PlFindUsWord(String searchKey, int type);// ////////////////////??????

    /**
     * 获取所有中文自造词(清理自造词使用)
     *
     * @return 中文自造词的个数
     */
    public native int PlGetCnWordCount();

    /**
     * 导入词
     *
     * @param filePath 导入词库的路径
     * @param type     1:拼音2:英文
     * @return 导入词的个数
     */
    public native int PlImportWords(String filePath, int type);

    /**
     * 导出词库
     *
     * @param filePath 导出词库的路径
     * @param type     1:拼音2:英文
     * @return 导出词的个数
     */
    public native int PlExportWords(String filePath, int type);

    /**
     * 安装颜文字
     *
     * @param 颜文字文件所在路径
     * @return 成功返回idmapcellid 失败返回负数错误码
     */
    public native int PlIdmapCellInstall(String path);

    /**
     * 安装颜文字
     *
     * @param 颜文字的buff
     * @return 成功返回idmapcellid 失败返回负数错误码
     */
    public native int PlIdmapCellInstallByBuff(byte[] buff);

    /**
     * 卸载颜文字
     *
     * @param 颜文字id
     * @return 成功返回0， 失败返回负数错误码
     */
    public native int PlIdmapCellUninstall(int id);

    /**
     * 根据id获取颜文字
     *
     * @param info 颜文字信息结构
     * @param id   map_id
     */
    public native void PlIdmapCellGetinfoById(IdmapCellInfo info, int id);

    /*********************************/
    /*********************************/
    /* 细胞词库管理相关接口 */
    /*********************************/
    /*********************************/
    /**
     * 安装细胞词库
     *
     * @param 细胞词库所在路径
     * @return 成功返回cellid 失败返回负数错误码
     */
    public native int PlCellInstall(String path);

    /**
     * 安装细胞词库
     *
     * @param 细胞词库的buff
     * @return 成功返回cellid 失败返回负数错误码
     */
    public native int PlCellInstallByBuff(byte[] buff);

    /**
     * 卸载细胞词库
     *
     * @param 细胞词库id
     * @return 成功返回0， 失败返回负数错误码
     */
    public native int PlCellUninstall(int id);

    /**
     * 启用/禁用指定id的细胞词库
     *
     * @param 细胞词库id
     * @param 是否启用
     * @return 返回0表示成功进行该启用/禁用的操作 失败返回负数错误码
     */
    public native int PlCellEnable(int id, boolean enable);

    /**
     * 设置地理位置词库类型
     *
     * @param 细胞词库id
     * @param 细胞词库类型
     * @return 若设置成功, 返回0; 若设置失败, 返回值<0
     */
    public native int PlCellSetLocType(int id, int locType);

    /**
     * 设置细胞词库安装时间
     *
     * @param 细胞词库id
     * @param 安装时间
     * @return 若设置成功, 返回0; 若设置失败, 返回值<0
     */
    public native int PlCellSetInstallTime(int id, int installTime);

    /**
     * 获得细胞词库的个数
     *
     * @return 返回细胞词库的个数
     */
    public native int PlCellCount();

    /**
     * 获取细胞词库信息
     *
     * @param 细胞词库信息结构 //先要new出数组，数组大小由PlCellCount获得
     * @return 一共有多少细胞词库
     */
    public native int PlCellGetinfo(CellInfo[] infos);

    /**
     * 根据index获取细胞词库信息
     *
     * @param 细胞词库信息结构
     * @param 细胞词库的id
     * @return 正确返回0，错误返回非0值
     */
    public native int PlCellGetinfoById(CellInfo info, int id);

    /**
     * 获得词库的版本
     *
     * @param type 0:流行词 1：系统词
     * @return inner_ver
     */
    public native int PlCellGetVer(int type);

    /**
     * 获得系统词库的SID
     *
     * @return
     */
    public native int PlCellGetSysVER();

    /*********************************/
    /*********************************/
    /* 个性短语管理相关接口 */
    /*********************************/
    /*********************************/
    /**
     * 导入编辑的自定义短语文件
     *
     * @param filename  文件路径
     * @param overWrite 是否覆盖文件，true覆盖，false追加
     * @return 0表示成功
     */
    public native int PlPhraseImport(String filename, boolean overWrite);

    /**
     * 导出自定义短语
     *
     * @param filename
     * @param groupId  0表示全部
     * @return 0表示成功
     */
    public native int PlPhraseExport(String filename, int groupId);

    /**
     * 获得自定义短语分组数量
     *
     * @param groupId  分组ID
     * @param codeName 个性短语code
     * @return groupId组中个性短语code的个性短语个数
     */
    public native int PlPhraseGetCount(int groupId, byte[] codeName);

    /**
     * 获得自定义短语信息
     *
     * @param phraseInfo 自定义短语信息
     * @param id         自定义短语所在位置
     * @return 错误码
     */
    public native int PlPhraseGetInfo(PhraseInfo phraseInfo, int id);

    /**
     * 自定义短语编辑
     *
     * @param phraseInfo 需要编辑的自定义短语的信息
     * @param option     编辑类型 1:编辑， 2：删除， 3：添加
     * @return 0表示成功
     */
    public native int PlPhraseEdit(PhraseInfo phraseInfo, int option);

    /**
     * 获得自定义短语分组数量
     *
     * @param groupName 个性短语分组名
     * @param len       个性短语分组名长度
     * @return 分组个数
     */
    public native int PlPhraseGPGetCount(char[] groupName, int len);

    /**
     * 获得分组的信息
     *
     * @param phraseGPInfo 一个分组的信息
     * @param id           分组的index
     * @return phraseCount|groupId|name|nameLen
     */
    public native int PlPhraseGPGetInfo(PhraseGPInfo phraseGPInfo, int id);

    /**
     * 分组编辑
     *
     * @param phraseGPInfo 一个分组的信息
     * @param option       编辑类型 1:编辑， 2：删除， 3：添加
     * @return 0表示成功
     */
    public native int PlPhraseGPEdit(PhraseGPInfo phraseGPInfo, int option);

    // /*********************************/
    // /*********************************/
    // /* 加密相关接口 */
    // /*********************************/
    // /*********************************/
    // /**
    // * 网络数据包加密
    // *
    // * @param data
    // * @param isEnc
    // * true加密 false解密
    // * @return 加密成功返回0，出错返回 <0 的数值
    // */
    // public native int PlIdea(byte[] data, boolean isEnc);
    //
    // /**
    // * 用户信息上穿时加密
    // *
    // * @param content
    // * 要加密的内容格式如下： 加密内容的长度+28个0+加密的字节数组+n个0（其中n是这样得出的，调用PlRsaGetBufferNeed（加密字节数组长度）-加密字节数组长度-32）
    // * @return 解密成功返回0，解密出错返回非零数值
    // */
    // public native int PlRsaEncoder(byte[] content);
    //
    // /**
    // * 获得RSA加密后所需的长度
    // *
    // * @param contentLength
    // * 要加密的内容长度
    // * @return RSA加密后所需的长度
    // */
    // public native int PlRsaGetBufferNeed(int contentLength);
    //
    // /**
    // * IdeaBase64加密 用于登陆密码；内部防刷；网络备份用户词前的扰码
    // *
    // * @param infos
    // * @return
    // */
    // public native byte[] PlMartine(String[] infos);
    //
    // /**
    // * 网络数据包解密（前四个字节（0~3），组成整数，注意是小端内存，这个整数为送去加密的内容的长度， 内容从第20个开始（注意从第0开始））
    // *
    // * @param data
    // * @return 错误代码
    // */
    // public native int PlIdeaBase64Decoder(byte[] data);
    //
    // /**
    // * IdeaBase64加密 用于登陆密码；内部防刷；网络备份用户词前的扰码
    // *
    // * @param infos
    // * @param packageInfo
    // * @return
    // */
    // public native byte[] PlGetProductVersions(String[] infos, PackageInfo packageInfo);

    /*********************************/
    /*********************************/
    /* 联系人相关接口 */
    /*********************************/
    /*********************************/
    // /**
    // * 按照给定路径开始创建通讯录数据文件
    // *
    // * @param path
    // * 分类信息文件的路径
    // * @param attributes
    // * 各个联系人条目的信息
    // * @param attrib_num
    // * 联系人条目信息的数目
    // * @param countname
    // * 人数
    // * @return
    // */
    // public native boolean PlCtBeginCreate(String path, String[] attributes, int attrib_num, int countname);
    //
    // /**
    // * 完成创建通讯录文件的操作
    // *
    // *
    // * @return
    // */
    // public native boolean PlCtEndCreate();

    /**
     * 添加一个通讯录条目
     *
     * @param name    记录名字
     * @param attribs 此记录的所有属性名称
     * @param values  此记录的所有属性的值
     * @return
     */
    public native boolean PlCtAddContact(String name, String[] attribs, String[] values);

    @Deprecated
    public native boolean PlCtAddAttris(String[] attribs);

    /**
     * 在通讯录中查找指定人名的联系方式
     *
     * @param count 属性项目个数
     * @return 返回对应人名的联系方式
     */
    public native String[] PlCtGetContact(int count);

    /**
     * 在通讯录中查找指定人名的联系人属性个数
     *
     * @param name 需要查询的人名
     * @return 返回属性个数，-1为调用失败
     */
    public native int PlCtGetCount(String name);

    /**
     * 删除对应的联系人
     *
     * @param name — 联系人姓名
     * @param cmd  — 删除类型 CMD_CONTACT_DEL CMD_CONTACT_RESTORE_FREQ CMD_CONTACT_DEL_ALL
     * @return 若删除成功, 返回0; 若删除失败, 返回-1;未找到要删除的项目，返回-2
     */
    public native int PlCtDeleteContact(String name, int cmd);

    /**
     * 语音查找联系人(非通讯录)
     *
     * @param oriname 原名字, 以'\0'结尾
     * @return 改后名字
     */
    public native String PlCtVoiceFind(String oriname);

    /**
     * 语音查找联系人(通讯录)
     *
     * @param oriname 原名字, 以'\0'结尾
     * @return 改后名字
     */
    public native String PlCtVoiceFindAddressbook(String oriname);

    /**
     * 备份所有用户数据
     *
     * @return 是否备份成功
     */
    public native boolean PlUsrwordBackup();

    /**
     * 恢复所有用户数据
     *
     * @return 是否恢复成功
     */
    public native boolean PlUsrwordRecover();

    /**
     * 检查所有用户数据
     *
     * @return 是否检查成功
     */
    public native boolean PlUsrwordCheck();

    /**
     * 清理通讯录联系人
     *
     * @return
     */
    public native boolean PlCtClean();

    /*********************************/
    /*********************************/
    /* 其他需完善的接口 */
    /*********************************/
    /*********************************/

    /**
     * 简体转繁体
     *
     * @param str
     * @return
     */
    public native String PlFindFT(String str);

    /**
     * 删除指定的自造词
     *
     * @param input 中文=null 英文=英文的字节数组
     * @param pos   获取时的位置
     * @param isCh  中文=true 英文=false
     */
    public native void PlDeleteUsWord(byte[] input, int pos, boolean isCh);

    /**
     * 清理自造词，删掉词频最低的指定比例的自造词，最多5000
     *
     * @param percent — 删除比例，取值范围(0-100，如删除50%，参数为50)
     * @return 返回值>=0表示删除词的个数; 返回值为负时代表操作失败（特别的，返回值为-10201代表用户词数量不足1000，不应执行清理操作）
     */
    public native int PlUsWordReduce(int percent);

    /**
     * 判断是否中文系统词
     *
     * @param input 中文字id
     * @param len   中文字id的长度
     * @return true：是系统词 false：不是系统词
     */
    public native boolean PlIsCNSysword(int index);

    /**
     * 判断是否英文系统词
     *
     * @param enword 英文单词， 以'\0'结尾
     * @return true：是系统词 false：不是系统词
     */
    public native boolean PlIsENSysword(byte[] enword);

    /**
     * 获得自定义码表中的自动上屏的匹配长度
     *
     * @return
     */
    public native int PlGetMatchLen();

    /**
     * 获得当前输入情况下各个字母的等级
     *
     * @param alphas
     */
    public native void PlGetHotLetter(byte[] alphas);

    /**
     * 把数组改为指定的颜色。
     * 已经废弃，使用{@link com.baidu.util.GraphicsLibrary#glChangeColor(int[], int[], int)}
     */
    @Deprecated
    public native void GlChangeColor(int[] src, int[] dst, int clr);

    /**
     * 图片转为夜间模式
     * 已经废弃，使用{@link com.baidu.util.GraphicsLibrary#glSetNight(Bitmap)}
     */
    @Deprecated
    public native void GlSetNight(Bitmap bmp);

    /**
     * 颜色转为夜间模式
     * 已经废弃，使用{@link com.baidu.util.GraphicsLibrary#changeToNightMode(int)}
     */
    @Deprecated
    public native int GlSetNightColor(int color);

    /**
     * 颜色由夜间转变为日间模式
     * 已经废弃，使用{@link com.baidu.util.GraphicsLibrary#changeToDayMode(int)}
     */
    @Deprecated
    public native int GlSetDayColor(int color);

    /**
     * 图片从夜间模式还原为日间模式
     * 已经废弃，使用{@link com.baidu.util.GraphicsLibrary#glSetToDayMode(Bitmap)}
     */
    @Deprecated
    public native void GlSetToDayMode(Bitmap bmp);

    /**
     * 获得保护的key。目前用于小游戏的scretkey的提供，复用内核的签名校验方法将scretKey保护起来
     */
    public native String getProtCode();

    /*********************************/
    /*********************************/
    /* 符号相关 的接口 */
    /*********************************/
    /*********************************/

    /**
     * 生成二进制格式的符号文件
     *
     * @param name        文本符号文件的路径
     * @param isOverWrite 是否覆盖
     * @return
     */
    public native int PlSymImport(String name, boolean isOverWrite);

    /**
     * 导出符号文件
     *
     * @param name 导出的符号文件路径
     * @return -1 失败。 >0 导出个数
     */
    public native int PlSymExport(String name);

    /**
     * 生成二进制格式的符号文件
     *
     * @param name        文本符号文件的路径
     * @param isOverWrite 是否覆盖
     * @return
     */
    public native int PlSylianImport(String name, boolean isOverWrite);

    /**
     * 导出符号文件
     *
     * @param name 导出的符号文件路径
     * @return
     */
    // public native int PlSylianExport(String name);

    /**
     * 生成二进制格式的符号文件
     *
     * @param name 文本符号文件的路径
     * @param isOverWrite 是否覆盖
     * @return
     */
    // public native int PlEmojiImport(String name, boolean isOverWrite);

    /**
     * 导出符号文件
     *
     * @param name 导出的符号文件路径
     * @return -1 失败。 >0 导出个数
     */
    // public native int PlEmojiExport(String name);

    /**
     * 生成空的词库文件 支持:中文自造词(uz.bin),中文分类词信息文件(cell.bin),英文自造词(ue2.bin),个性短语(phrase.bin),联系人联想(contact.bin)
     *
     * @param name 需要生成的空词库文件路径
     * @param type 类型 0：中文自造词(uz.bin) 1：英文自造词(ue2.bin) 2：中文分类词信息文件(cell.bin) 3：个性短语(phrase.bin) 4：联系人联想(contact.bin)
     * @return 成功返回true，失败返回false
     */
    // public native boolean PlCreateEmptyLibFile(String name, int type);

    /**
     * 获取二元关系库的版本号 -1表示没有载入二元库
     *
     * @return 二元词库版本号
     */
    public native int PlGetGramVersion();

    /**
     * 获取二元关系库的大版本号
     *
     * @return 二元词库大版本号
     */
    public native int PlGetGramCateVersion();

    /**
     * 获取拼音是否为长简拼(<0 表示发生错误,=0 表示不是长简拼, >0 表示是长简拼
     *
     * @return
     */
    public native int PlIsLongJP();

    /**
     * check gram.bin文件的MD5值，目前版本为MD5 (gram.bin) = 52f8f50fc2cbddbc03728904dedf0b86
     *
     * @param name   gram.bin文件的路径
     * @param digest MD5值 ， 32个字节
     */
    public native void PlCheckFileMD5(String name, byte[] digest);

    public native int PlOldUeExport(String inputFile, String outputFile);

    public native int PlOldCpExport(String inputFile, String outputFile);

    /**
     * 设置手写状态下拼音查询的前几个字的unicode(最多3)
     *
     * @param preword — 前几个字的unicode
     * @return 若设置成功, 返回0; 若设置失败, 返回-1
     */
    public native int PlSetHWPreword(String preword);

    /**
     * 手写查询拼音
     *
     * @param unicode — 要注音的字对应的unicode编码
     * @param isall   — 是否给所有字注音(0-只标注生僻字， 1-标注所有字)
     * @return 若查询成功, 返回unicode编码对应汉字的注音(结构为: "yin,pin"); 若查询失败, 返回NULL
     */
    public native byte[] PlGetHWPinyin(char unicode, int isall);

    /**
     * 获取个性化词的状态
     *
     * @param type — 待编辑的个性化词名称
     * @return 0 表示未启用; 1 表示启用; <0 表示错误
     */
    public native int PlOtherwordGetStatus(String type);

    /**
     * 切换个性化词开关
     *
     * @param type   — 待编辑的个性化词名称(以'\0'结尾)
     * @param enable — 是否启用, 1表示启用, 0表示不起用
     * @return 若切换成功, 返回0; 若切换失败, 返回值<0
     */
    public native int PlOtherwordEnable(String type, int enable);

    // //////////////////////////////////////////////////////////////////////////////////////////////////////////////

    /**
     * 设置debug log 路径 0成功，-1失败
     *
     * @param path 文件路径
     * @param mode 是append，还是rewrite
     * @return 0成功，-1失败
     */
    public native int PlSetDebugLogPath(String path, int mode);

    /**
     * 关闭debug log 文件
     *
     * @return 0成功，-1失败，-100当前没有打开的debug log 文件
     */
    public native int PlCloseDebugLog();

    /**
     * 获取搜索词的version
     *
     * @return 当前搜索词的version
     */
    public native int PlKeywordGetSearchVersion();

    /**
     * 获取多媒体词的version
     *
     * @return 当前多媒体词的version
     */
    public native int PlKeywordGetMediaVersion();

    /**
     * 导出关键词
     *
     * @param fileName 待导出文件的路径名
     * @return 导出关键词的数量; 若返回值<0, 表示导出出错
     */
    public native int PlKeywordExport(String fileName);

    /**
     * 获取关键词库个数
     *
     * @return 关键词库的个数; 若返回值<0, 表示关键词库文件未载入成功
     */
    public native int PlKeywordCellCount();

    /**
     * 通过索引获取关键词库信息
     *
     * @param keywordInfo 关键词库对象
     * @param index       关键词库索引
     * @return 若返回值为0, 代表获取成功; 若返回值<0, 表示关键词库信息文件未载入成功
     */
    public native int PlKeywordCellInfoByIndex(KeywordInfo keywordInfo, int index);

    /**
     * 通过cellid获取关键词库信息
     *
     * @param keywordInfo 关键词库对象
     * @param cellId      待查询的关键词库cellid
     * @return 若返回值为0, 代表获取成功; 若返回值<0, 表示关键词库信息文件未载入成功
     */
    public native int PlKeywordCellInfoByCellId(KeywordInfo keywordInfo, int cellId);

    /**
     * 安装关键词库文件
     *
     * @param fileName 待安装的细胞词库路径名 与buff参数选其一即可
     * @param buff     待安装的细胞词库文件内容
     * @param size     （本参数>0代表第二个参数是文件内容, 此参数表示该文件的大小; 本参数=0时, 表示第二个参数是文件路径）
     * @return 若返回值为非负整数, 安装成功, 返回值为该词库的cellid 返回值为-10100, 表示关键词库信息文件未载入成功 返回值为-10102, 表示新旧关键词库版本号不匹配 返回值为-10103,
     * 表示无法分配cell_id(安装的关键词库个数已经达到最大值) 返回值为-10104, 表示不能打开待安装的关键词库 返回值为-10105, 表示待安装的关键词库文件大小不符合标准 返回值为-10106,
     * 表示待安装的关键词库文件识别码错误 返回值为-10107, 表示已安装的关键词库过多(>=255)
     */
    public native int PlKeywordCellInstall(String fileName, byte[] buff, int size);

    /**
     * 删除关键词库
     *
     * @param cellId 待删除的关键词库cell_id
     * @return 卸载成功, 返回0; 返回值为-10100, 表示关键词库信息文件未载入成功 返回值为-10101, 表示未找到要操作的关键词库
     */
    public native int PlKeywordCellUninstall(int cellId);

    /**
     * 切换关键词库开关
     *
     * @param cellId  待编辑的关键词库cell_id
     * @param enabled 是否启用, true表示启用, false表示不起用
     * @return 若切换成功, 返回0; 若切换失败, 返回值<0
     */
    public native int PlKeywordCellEnable(int cellId, boolean enabled);

    /**
     * 给手写和云输入提供的查询表情和搜索词标记接口
     *
     * @param _str — 为要查询的内容
     * @return 长度为2的int数组 array[0]:若查询的后续词应该加上搜索词标记，则返回值为搜索词类型，否则结果为0; array[1]:若查到和候选词对应的表情，用于存储表情的值；查不到时，返回值为0
     */
    public native int[] PlKeywordGetCandType(String str);

    /**
     * 暂存旧版颜文字数据信息
     *
     * @param oldywzsysdata 暂存旧版系统数据
     * @param oldywzsyssize 暂存旧版系统数据大小
     * @param oldywzusrdata 暂存旧版自造数据
     * @param oldywzusrsize 暂存旧版自造数据大小
     * @param cellidmap 新旧cell_id映射文件
     * @return 暂存的旧版数据个数，若数据文件未载入成功，返回-10100
     */
    //public native int PlKeywordYanwenziExportOlddata(byte[] oldywzsysdata, int oldywzsyssize, byte[] oldywzusrdata,
    //        int oldywzusrsize, int[] cellidmap);

    /**
     * 导入旧版颜文字数据信息
     *
     * @param oldywzsysdata 待导入旧版系统数据
     * @param oldywzsyssize 待导入旧版系统数据大小
     * @param oldywzusrdata 待导入旧版自造数据
     * @param oldywzusrsize 待导入旧版自造数据大小
     * @param cellidmap 新旧cell_id映射文件
     * @return 导入的旧版数据个数，若数据文件未载入成功，返回-10100
     */
    //public native int PlKeywordYanwenziImportOlddata(byte[] oldywzsysdata, int oldywzsyssize, byte[] oldywzusrdata,
    //       int oldywzusrsize, int[] cellidmap);

    /**
     * 专供Android6.5->7.0版本解决保留旧版不带精确/模糊功能的颜文字自造数据提供的安装接口
     *
     * @param fileName 待安装的细胞词库路径名 与buff参数选其一即可
     * @param buff     待安装的细胞词库文件内容
     * @param size     （本参数>0代表第二个参数是文件内容, 此参数表示该文件的大小; 本参数=0时, 表示第二个参数是文件路径）
     * @return 若返回值为非负整数, 安装成功, 返回值为该词库的cellid 返回值为-10100, 表示关键词库信息文件未载入成功 返回值为-10102, 表示新旧关键词库版本号不匹配 返回值为-10103,
     * 表示无法分配cell_id(安装的关键词库个数已经达到最大值) 返回值为-10104, 表示不能打开待安装的关键词库 返回值为-10105, 表示待安装的关键词库文件大小不符合标准 返回值为-10106,
     * 表示待安装的关键词库文件识别码错误 返回值为-10107, 表示已安装的关键词库过多(>=255)
     */
    public native int PlKeywordEmoticonCellInstall(String fileName, byte[] buff, int size);

    /**
     * 查找二次元内容
     *
     * @return 二次元输出结果
     */
    public native char[] PlKeywordFindNijigen();

    /**
     * 查找彩蛋数据
     *
     * @return 彩蛋输出结果
     */
    public native char[] PlKeywordFindEgg();

    /**
     * 语音联想查找emoji, 颜文字
     *
     * @param content      查询内容
     * @param emojiList    emoji查找结果, 数组大小为5个
     * @param emoticonList 颜文字查找结果, 数组大小为5个
     * @return 返回值<0 发生错误
     */
    public native int PlKeywordFindVoiceLian(String content, int[] emojiList, int[] emoticonList);

    /**
     * 语音联想查找彩蛋
     *
     * @param content 语音内容
     * @return 彩蛋输出结果
     */
    public native char[] PlKeywordFindVoiceEgg(String content);

    /**
     * 获取整句中的其他关键词(搜索词，多媒体等)
     *
     * @param cs 关键词内容及类型
     * @return 返回值代表关键词的类型
     */
    public native int PlKeywordGetSentenceKeyword(CoreStringInfo cs);

    /**
     * 用于语音输入的结果查找多媒体词（用之前需清除逐词和整句keyword状态）, 查询后用PlKeywordGetSentenceKeyword获取结果
     *
     * @param word 联想词内容
     * @param len  联想词长度
     * @return
     */
    public native int PlKeywordFindLian(String word, int len);

    /**
     * 获取整句中的表情
     *
     * @param emojiValues 整句中的表情
     * @return 返回值代表个数(0-2个)
     */
    public native int PlEmojiGetSentenceLian(short[] emojiValues);

    /**
     * 表情关系调频（大于8个字符的将不会调频，只支持中文）
     *
     * @param word       待调频的词
     * @param len        待调频的词长度
     * @param emojiValue 待调频的表情联想(客户端需事先判断是否在内核处理的表情范围内（公共表情）),emoji的id
     * @param cellId     包名的id
     * @return 调频成功, 返回0 返回值为-10111, 表示表情的范围有误 返回值为-10112, 表示关系不是纯中文，暂不支持 返回值为-10113, 长度>8，不参与调频
     */
    public native int PlEmojiAdjustEmojiRelation(String word, int len, char emojiValue, int cellId);

    /**
     * 符号联想关系调频（联想词最多8个字符，符号最多32个字符）
     *
     * @param prestr  联想词
     * @param prelen  联想词的长度
     * @param tailstr 联想表情
     * @param taillen 联想表情长度
     * @return 调频成功, 返回0 返回值为-10114, 符号联想文件未载入成功 返回值为-10115, 数据长度有误
     */
    public native int PlSymAdjustSylianRelation(String prestr, int prelen, String tailstr, int taillen);

    /**
     * 查询仓颉字库的版本
     */
    public native int PlGetCangjieVer();

    /**
     * 查询注音字库的版本
     */
    public native int PlGetZhuyinHzVer();

    /**
     * 查询注音词库的版本
     */
    public native int PlGetZhuyinCzVer();

    // //////////////////////////////////////////////////////////////////////////////////////////////////
    // //////////////////////////////////////////////////////////////////////////////////////////////////
    // //////////////////////////////////////////////////////////////////////////////////////////////////
    // //////////////////////////////////////////////////////////////////////////////////////////////////
    // //////////////////////////////////////////////////////////////////////////////////////////////////
    // 手写相关

    /**
     * 手写添加一笔,只能一笔不支持多笔
     *
     * @param point_data — 手写数据以-1,0结尾
     * @param input_type — 手写类型（具体内容请参照HW_INPUT_TYPE）
     * @return 返回值<0表示失败,0表示成功
     */
    public native int PlHandWritingRecognizeAppand(short[] point_data, int input_type);

    /**
     * 获取手写版本号
     *
     * @return 获取手写版本号
     */
    public native int PlHandWritingVersion();

    /**
     * 获取手写设置项
     *
     * @return 手写设置项 {识别范围，每个字的最大匹配笔画数}
     */
    public native int[] PlHandWritingGetConfig();

    /**
     * 设置手写设置项
     *
     * @param config 手写设置项 {识别范围，每个字的最大匹配笔画数}
     * @return 返回值<0表示失败,0表示成功
     */
    public native int PlHandWritingSetConfig(int[] config);

    /**
     * 手写数据压缩
     *
     * @param candId 候选字idx
     * @return 手写压缩数据
     */
    public native byte[] PlHandWritingEncodePoints(int candId);

    // //////////////////////////////////////////////////////////////////////////////////////////////////
    // //////////////////////////////////////////////////////////////////////////////////////////////////
    // //////////////////////////////////////////////////////////////////////////////////////////////////
    // //////////////////////////////////////////////////////////////////////////////////////////////////
    // //////////////////////////////////////////////////////////////////////////////////////////////////
    // 纠错相关

    /**
     * 输入法输入一个点查询
     *
     * @param find_type  — 查询类型（具体内容请参照FIND_TYPE类型）
     * @param pos        — 增加一个点的位置，取值范围0~63
     * @param x          — 增加一个点的x坐标
     * @param y          — 增加一个点的y坐标
     * @param input_case — 输入方式，（大写或上划等）
     * @param input      — 待查询的字符
     * @param contextId  场景类别Id
     * @return 0代表查找成功; 返回值<0代表查找失败
     */
    public native int PlKpAppendPoint(int findType, int pos, short x, short y, int inputCase, byte input, int contextId);

    /**
     * 输入法删除一个点查询
     *
     * @param find_type 查询类型（具体内容请参照FIND_TYPE类型）
     * @param pos       删除一个点的位置
     * @param num       删除点的个数（0表示不删除重新做一次查询）
     * @param contextId 场景类别Id
     * @return 0代表查找成功; 返回值<0代表查找失败
     */
    public native int PlKpDeletePoint(int findType, int pos, int num, int contextId);

    /**
     * 输入法重新开始一次输入时清理
     *
     * @return 0代表查找成功
     */
    public native int PlKpClean();

    /**
     * 设定键盘上的每一个键
     *
     * @param key  键位对应字母（A-Z)
     * @param viewRect 视觉布局矩形
     * @param touchRect 热区布局矩形
     * @return 0 代表设置成功
     */
    public native int PlKpAddKey(byte key, int[] viewRect, int[] touchRect);

    /**
     * 根据坐标返回对应按键
     * @param x    — 当前的点坐标x
     * @param y    — 当前的点坐标y
     * @return 返回为当前坐标对应的按键，为0表示无法识别
     */
    public native int PlGetTouchedKey(int x, int y);

    /**
     * 清空当前的动态热区
     * @return 返回为0表示清楚前动态热区未初始化，为1表示正常
     */
    public native int PlCleanDynamicRects();

    /**
     * 设置当前使用的皮肤Tokken和手机状态
     * @param skinToken      — 皮肤token
     * @param len  — 皮肤token长度
     * @param phoneStat      — 手机当前的状态，参见PHONE_STATE_LIST列表
     * @return 返回为0表示清楚前动态热区未初始化，为1表示正常
     */
    public native int PlSetSkinToken(char[] skinToken, int len, int phoneStat);

    /**
     * 导出上次上传至今的误触情况
     */
    public native String PlUsrTouchExportFreshData();

    /**
     * 导出这次commit的误触情况用于trace
     */
    public native String PlExportMisKeyForTrace();

    /**
     * 设定键盘的大小
     *
     * @param rect — 键盘的布局
     * @return 0 代表设置成功
     */
    public native int PlKpSetRect(int[] rect);

    /**
     * 获取当前输入的所有点
     *
     * @return 获取当前输入的所有点{点的总个数n，x1, y1, x2, y2, ..., xn, yn}
     */
    public native short[] PlKpGetPoint();

    /**
     * 查询idx位置的纠错提示信息
     *
     * @param idx      对应候选字的位置
     * @param iec_str  存放纠错信息的数组，只有该位置进行了纠错才会有相应纠错字符
     * @param iec_info 存放纠错类型的数组，对应某一位置上进行的纠错类型
     */
    public native int PlKpQueryIecTip(int idx, byte[] iec_str, byte[] iec_info);

    /**
     * 查找候选词是否存在需要模糊音纠错的情况
     *
     * @param idx 欲操作对象的下标
     * @return 若需要模糊纠错，存储纠错后的对应结果（读音错误的字后加上正确的注音，外加音调和注音位置）
     */
    public native String PlKpQueryIecMohuStr(int idx);

    /**
     * 查找候选词是否存在需要读音纠错的情况
     *
     * @param idx 欲操作对象的下标
     * @return 若需要读音纠错，存储纠错后的对应结果（读音错误的字后加上正确的注音，外加音调和注音位置）
     */
    public native String PlKpFindChCor(int idx);

    /**
     * 设置部首过滤
     *
     * @param point_data 部首数据,单笔用(-1,0)隔离,结尾用(-1,-1)隔离
     */
    public native int PlHandWritingSetBsFilter(short[] points);

    /**
     * 导入旧版本的细胞词、自造词文件
     *
     * @param cell_file — 旧版本的细胞词库头
     * @param uz_file   — 旧版本的自造词文件
     * @return 是否导入成功 0表示导入成功，-1表示导入失败
     */
    public native int PlImportOldUzFile(String cell_file, String uz_file);

    /**
     * 获取直达号词的version(废弃）
     */
    @Deprecated
    public native int PlGetZhiDaHaoVer();

    /**
     * 获取云输入请求数据,请注意连续调用二次改函数数据会被覆盖
     *
     * @param requestData   - 云输入需要的内核返回数据
     * @param cloud_setting — 云输入设置(具体内容参见s_cloud_setting)
     * @param cloud_info    — 云输入附带信息(具体内容参见s_cloud_info)
     * @param cloud_log—    云输入要收集的数据(具体内容参见s_cloud_sug_log)
     * @return
     */
    public native void PlCloudGetReqData(ICloudRequestData requestData,
                                         ICloudSetting cloud_setting,
                                         ICloudInfo cloud_info,
                                         CloudLog[] cloud_log);

    /**
     * 查询整句预测结果
     * @param unicode 输入unicode
     * @return 整句预测结果
     */
    public native CloudForecastOutput PlFindCloudZj(String unicode);

    /**
     * 根据输入码code，能匹配到一句话sentence的几个字
     * @param sentence 待匹配的unicode码
     * @param inputCode 待调频词的拼音
     * @return 返回匹配到的字数.如果参数异常返回-1
     */
    public native int PlGetSentenceMatchLen(String sentence, String inputCode);

    /**
     * 清除整句预测缓存
     */
    public native void PlCloudZjClean();

    /**
     * 查询云输入结果
     *
     * @param buf            — 云返回内容
     * @param buf_len        — 云返回内容长度
     * @param cand_redup_cnt — 和现有cand条上前cand_redup_cnt去重
     * @param cloud_manager
     * @return
     */
    public native int PlCloudInputBuf(byte[] buf, int buf_len, int cand_redup_cnt, ICloudDataManager cloud_manager);

    /**
     * 获取云输入结果 请注意返回值中的内存会被云输入接口重复利用
     *
     * @return cloudOutputArray
     */
    public native CloudOutputService[] PlCloudOutput();

    /**
     * 获取搜索结果 请注意返回值中的内存会被云输入接口重复利用
     *
     * @return cloudOutputSearchArray
     * <AUTHOR> on 16/10/9
     */
    public native CloudOutputSearch[] PlCloudSearch();

    /**
     * 获取云输入sug相关接口结果
     *
     * @param sugType — sug相关类型
     * @return 返回值=null为失败,!=null成功
     */
    public native Object PlSugOutput(int sugType);

    /**
     * 获取云输入白名单版本号
     *
     * @return
     */
    public native int PlCellGetCloudWhiteVer();

    /**
     * 判断当前手机CPU是否支持neon,
     *
     * @param cpuMsg cpu指令集信息
     * @return 返回值是{@link #CPU_NEON_SUPPORT}表示支持neon指令集,小于{@link #CPU_NEON_SUPPORT}表示不支持
     */
    public native int PlSetCpuMsg(String cpuMsg);

    /**
     * 盲人输入法的辅助接口，给出指定汉字的组合词
     *
     * @param idx 欲操作对象的下标
     * @return 查询到的汉字组合，用'|'分割，最长为64个字符
     */
    public native String PlQueryGetCandContext(int idx);

    /**
     * 盲人输入法数据导出,
     *
     * @param fname 导出文件路径
     * @return 返回值是{@link #CPU_NEON_SUPPORT}表示支持neon指令集,小于{@link #CPU_NEON_SUPPORT}表示不支持
     */
    public native int PlCandContextExport(String fname);

    /**
     * VKWORD导入
     *
     * @param filename  文件路径
     * @param overWrite 是否覆盖文件，true覆盖，false追加
     * @return 0表示成功
     */
    public native int PlVkwordImport(String filename, boolean overWrite);

    /**
     * VKWORD导出
     *
     * @param filename 文件路径
     * @return 0表示成功
     */
    public native int PlVkwordExport(String filename);

    /**
     * 导入ini格式的个性化数据文件
     *
     * @param filename 文件路径
     * @return 0表示成功
     */
    public native int PlImportUsrinfo(String filename);

    /**
     * 个性化数据导出为ini格式的文件
     *
     * @param filename 文件路径
     * @return 0表示成功
     */
    public native int PlExportUsrinfo(String filename);

    /* 查找邮箱后缀(返回值是0表示查询成功  <0表示查询失败
     *
     * @return
     */
    public native int PlQueryGetEmailSuffix();

    /**
     * push一个unicode的候选到cmd的栈中
     * @param unicode 上屏词内容
     * @param pinyin 输入码
     * @return true表示成功
     */
    public native boolean PlQueryCmdPushUni(String unicode, String pinyin);

    /**
     * 安装搜索关键词文件
     *
     * @param filename 待安装文件路径
     * @return 返回值表示安装的词条数
     */
    public native int PlSearchInstall(String filename);

    /**
     * 获取搜索关键字版本
     *
     * @return 返回值为版本号
     */
    public native int PlGetSearchVersion();

    /**
     * 查询ACS搜索词结果
     *
     * @param queryArray 查找字符串的内容
     * @param queryLen   查找字符串的长度
     * @return 返回值表示最优匹配内容的index, 最优匹配内容的长度
     */
    public native int[] PlSearchFind(char[] queryArray, int queryLen);

    /**
     * 获取光标前的字符
     *
     * @param uniArray 光标前的字符串指针
     * @param uniLen   光标前的字符串长度
     * @return 返回值0表示设置成功
     */
    public native int PlSetStrBeforeCursor(char[] uniArray, int uniLen);

    /**
     * 获取候选词埋点标记
     *
     * @param idx 候选项目的下标
     * @return 返回值为该候选的埋点标记
     */
    public native int PlCandGetFlag(int idx);

    /**
     * 注音用户词导入
     *
     * @param filename 文件路径
     * @return 返回值为非负整数, 表示导入中文用户词的数量; 返回值<0, 表示导入出错
     */
    public native int PlZywordImport(String filename);

    /**
     * 注音用户词导出
     *
     * @param filename 文件路径
     * @return 返回值为非负整数, 表示导入中文用户词的数量; 返回值<0, 表示导入出错
     */
    public native int PlZywordExport(String filename);

    /**
     * 获取智能回复的答案
     *
     * @param quArray 问题的Unicode串
     * @param quLen   问题串的长度
     * @return 答案的数组
     */
    public native String[] PlGetAutoReplyAns(char[] quArray, int quLen);

    /**
     * 获得智能回复文件版本号
     *
     * @return 智能回复文件版本号
     */
    public native int PlGetAutoReplyVersion();

    /**
     * 安装智能回复库
     *
     * @param filepath 待安装的智能回复词库路径
     * @return 返回值0表示安装成功，负数表示不符合安装条件没有安装，或者其他安装问题
     */
    public native int PlAutoReplyInstall(String filepath);

    /**
     * 卸载智能回复库
     *
     * @return 返回值0表示卸载成功，负数表示发生问题，只会卸载智能回复的内存部分，不会清除iptcore中的文件路径，也不会删除文件
     */
    public native boolean PlAutoReplyUninstall();

    /**
     *  抽取个人信息
     * @param orgWstr   抽取信息的原始Unicode串
     * @param orgLen    原始Unicode串的长度
     * @param infType   需要抽取的信息类型，详见枚举类型AUTOREPLY_INTENT
     * @return  抽取出的信息的长度，返回为null表示没提取出有用的信息
     */
    public native char[] PlAutoreplyInfExtract(String orgWstr, int orgLen, int infType);

    /**
     * 用于本地意图识别的接口
     *
     * @param intent 进行意图识别的unicode串
     * @param len    意图识别unicode串的长度
     * @return
     */
    public native String PlAutoreplyIntentDecode(String intent, int len);

    /**
     * 场景映射表导入
     *
     * @param filename 文件路径
     * @return 返回值为非负整数, 表示导入成功; 返回值<0, 表示导入出错
     */
    public native int PlAppmapImport(String filename);

    /**
     * 场景映射表导出
     *
     * @param filename 文件路径
     * @return 返回值为非负整数, 表示导出成功; 返回值<0, 表示导出出错
     */
    public native int PlAppmapExport(String filename);

    /**
     * 输入法查询
     * @param input     待查询的字符串
     * @param pos       对应的list选项值(用于筛选查询结果), 0代表返回所有list的结果
     * @param type      查询类型
     * @param appName   对应包名
     * @param editorId  输入框属性
     * @return          0代表查找成功; 返回值<0代表查找失败
     */
    public native int PlFind(byte[] input, int pos, int type, String appName, int editorId);

    /**
     * 输入法输入一个点查询
     * @param findType  查询类型
     * @param pos       增加一个点的位置
     * @param x         增加一个点的x坐标
     * @param y         增加一个点的y坐标
     * @param inputCase 输入方式，（大写或上划等）
     * @param input     待查询的字符
     * @param appName   对应包名
     * @param editorId  输入框属性
     * @return          0代表查找成功; 返回值<0代表查找失败
     */
    public native int PlKpAppendPoint(int findType, int pos, short x, short y, int inputCase, byte input,
                                      String appName, int editorId);

    /**
     * 输入法删除一个点查询
     * @param findType  查询类型
     * @param pos       删除一个点的位置
     * @param num       删除点的个数（0表示不删除重新做一次查询）
     * @param appName   对应包名
     * @param editorId  输入框属性
     * @return          0代表查找成功; 返回值<0代表查找失败
     */
    public native int PlKpDeletePoint(int findType, int pos, int num, String appName, int editorId);

    /**
     * 获取场景映射表版本号
     * @return 返回值为0表示文件不存在，其他正整数为版本号
     */
    public native int PlGetAppmapVersion();

    /**
     * 获取当前场景需要启用哪些场景词库
     * @param appName   对应包名
     * @param editorId  输入框属性
     * @return          返回值为对应细胞词个数
     */
    public native int[] PlGetAppCellId(String appName, int editorId);

    /**
     * 获取当前场景需要启用哪些场景词库(未安装列表)
     * @param appName   对应包名
     * @param editorId  输入框属性
     * @return          返回值为对应细胞词个数
     */
    public native int[] PlGetAppLackCellId(String appName, int editorId);

    /**
     * 刷新内核，重载cz3down.bin词库
     *
     * @param fileName 内核词库数据文件, 指定内核需要刷新的数据文件的位置，若文件不存在不刷新；如果为null表示只卸载
     * @return 0代表刷新成功; 返回值<0代表刷新失败或没有刷新
     */
    public native int PlCzDownRefresh(String fileName);

    /**
     * 符号面板导入符号，更多符号面板重新排序
     * @param listId 左侧list序号
     * @param lastPad 上一级面板
     *   1 代表从中文符号26键过来，中文26键上的符号，需往后调整
     *   2 代表从英文符号26键过来，英文26键上的符号，需往后调整
     *   0 及其它值 代表未经过符号26键面板，无需调整符号顺序
     *  @param syms 导入符号
     *  @param count 导入符号数量
     * @return 0代表成功; 返回值 < 0代表失败
     */
    public native int PlSymImportReorder(int listId, int lastPad, char[][] syms, int count);

    /**
     * 使用内核优化语音识别结果
     * @param orgResult 原始语音识别结果
     * @return 内核的优化识别结果，如果没有优化，返回null
     */
    public native String PlGetOptimizedVoiceResult(String orgResult);

    /**
     * 将用户的原始识别内容和修改轨迹发送给内核
     * @param orgResult 原始结果
     * @param datas 轨迹信息
     */
    // public native int PlSendVoiceTrace(String orgResult, TraceBean.TraceEntity[] datas);


    /**
     * 设置剪切板更新的内容
     *
     * @param uniArray 最近的剪切板内容
     * @param uniLen   最近的剪切板内容长度
     * @return 是否需要保存剪切板的内容，返回值0不需要，1需要
     */
    public native int PlCheckClip(char[] uniArray, int uniLen);
}
