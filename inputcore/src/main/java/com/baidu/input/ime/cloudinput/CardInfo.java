package com.baidu.input.ime.cloudinput;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 15/12/15.
 */
public class CardInfo {

    public static final byte CARD_TYPE_NONE = 0; // 打开
    public static final byte CARD_TYPE_APP = 1; // 下载
    public static final byte CARD_TYPE_VIDEO = 2; // 播放
    public static final byte CARD_TYPE_BOOK = 3; // 阅读
    public static final byte CARD_TYPE_AUDIO = 4; // 播放

    int cardId;
    String cardKey;
    String title;
    /** 版本号 */
    String content1;
    /** 大小 */
    String content2;
    /** 说明 */
    String content3;
    String img_url;
    String icon_url;

    /** 卡片app下载地址 */
    public String downloadUrl;

    public void set_card_id(int a) {
        cardId = a;
    }

    public void set_str(String a, String b, String c, String d, String e, String f, String g) {
        cardKey = a;
        title = b;
        content1 = c;
        content2 = d;
        content3 = e;
        img_url = f;
        icon_url = g;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("cardId=");
        sb.append(cardId);
        sb.append(",cardKey=");
        sb.append(cardKey);
        sb.append(",title=");
        sb.append(title);
        sb.append(",content1=");
        sb.append(content1);
        sb.append(",content2=");
        sb.append(content2);
        sb.append(",content3=");
        sb.append(content3);
        sb.append(",img_url=");
        sb.append(img_url);
        sb.append(",icon_url=");
        sb.append(icon_url);
        sb.append('\n');
        return sb.toString();
    }

    public int getCardId() {
        return cardId;
    }

    public String getCardKey() {
        return cardKey;
    }

    public String getTitle() {
        return title;
    }

    public String getContent1() {
        return content1;
    }

    public String getContent2() {
        return content2;
    }

    public String getContent3() {
        return content3;
    }

    public String getSourceMsg() {
        return null;
    }

    public String getImg_url() {
        return img_url;
    }

    public String getIcon_url() {
        return icon_url;
    }

    public void setCardId(int cardId) {
        this.cardId = cardId;
    }

    public void setCardKey(String cardKey) {
        this.cardKey = cardKey;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public void setContent1(String content1) {
        this.content1 = content1;
    }

    public void setContent2(String content2) {
        this.content2 = content2;
    }

    public void setContent3(String content3) {
        this.content3 = content3;
    }

    public void setImg_url(String img_url) {
        this.img_url = img_url;
    }

    public void setIcon_url(String icon_url) {
        this.icon_url = icon_url;
    }
}
