package com.baidu.input.ime.cloudinput;

import android.support.annotation.Keep;
import android.text.TextUtils;

/**
 * 整句预测的结果
 *
 * created by cdf on 19/5/9
 */
public class CloudForecastOutput {

    /** 显示的结果 */
    private String cand;
    /** 整句结果中已经上屏部分的长度 */
    private int hasCommittedLen;
    /** 上屏的结果 */
    private String commitCand;
    /** 整句结果中当前输入码匹配的长度 */
    private int curMatchLen;

    /**
     * 内核设置数据的方法
     */
    @Keep
    public void set(String cand, int candColorLen, int curMatchLen, String commitCand) {
        this.cand = cand;
        this.hasCommittedLen = candColorLen;
        this.curMatchLen = curMatchLen;
        this.commitCand = commitCand;
    }

    /**
     * 获得显示的结果
     */
    public String getCand() {
        return cand;
    }

    /**
     * 获得变色的长度
     */
    public int getHasCommittedLen() {
        return hasCommittedLen;
    }

    /**
     * 获得上屏的结果
     */
    public String getCommitCand() {
        return commitCand;
    }


    /**
     * 获得输入码已经匹配的长度
     * @return
     */
    public int getCurMatchLen() {
        return curMatchLen;
    }

    @Override
    public String toString() {
        return "CloudForecastOutput{" +
                "cand='" + cand + '\'' +
                ", hasCommittedLen=" + hasCommittedLen +
                ", commitCand='" + commitCand + '\'' +
                ", curMatchLen=" + curMatchLen +
                '}';
    }

    /**
     * 根据给定的实例赋值给当前的实例
     *
     * @param newOutput 给定的实例
     */
    public void copy(CloudForecastOutput newOutput) {
        if (newOutput != null) {
            this.cand = newOutput.cand;
            this.hasCommittedLen = newOutput.hasCommittedLen;
            this.commitCand = newOutput.commitCand;
            this.curMatchLen = newOutput.curMatchLen;
        }
    }

    /**
     * 数据重置
     */
    public void reset() {
        this.cand = null;
        this.hasCommittedLen = 0;
        this.curMatchLen = 0;
        this.commitCand = null;
    }

    /**
     * 数据是否为空
     */
    public boolean isEmpty() {
        return TextUtils.isEmpty(commitCand) || TextUtils.isEmpty(cand);
    }

    public void setCurMatchLen(int curMatchLen) {
        this.curMatchLen = curMatchLen;
    }
}
