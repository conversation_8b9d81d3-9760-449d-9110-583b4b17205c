package com.baidu.input.ime.cloudinput;


import android.text.TextUtils;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 15/12/15.
 */
public class CloudLog {

    public static final int LOGTYPE_REQ_SUG = 1;
    public static final int LOGTYPE_SHOW_SUG = 2;
    public static final int LOGTYPE_CLICK_SUG = 3;
    public static final int LOGTYPE_CLICK_CARD = 4;
    
    /** 信息收集数组的大小 */
    public static final int ARRAY_COUNT = 3;
    /** 展示sug */
    public static final int INDEX_SHOW_SUG = 0;
    /** 点击某个sug */
    public static final int INDEX_CLICK_SUG = 1;
    /** 点击卡片内的按钮 */
    public static final int INDEX_CLICK_CARD = 2;

    /** 所处应用的包名 */
    public String packageId;
    /** 输入框id */
    public int editorId;
    /** 点击类型 */
    public int type;
    /** 统计的候选词 */
    public String word;
    /** 源Id */
    public int sourceId;
    /** 上屏词+输入码 */
    public String requestCode;

    public int sugLogType;
    
    /** 框中文字  */
    private static String sEditorText;


    public int get_sug_log_type() {
        return sugLogType;
    }

    public int get_input_id() {
        return editorId;
    }

    public int get_click_type() {
        return type;
    }

    public int get_sug_source_id() {
        return sourceId;
    }

    public String get_pkg_name() {
        return packageId;
    }

    public String get_sug_word() {
        return word;
    }

    public String get_input_inline() {
        // String req = null;
        // try {
        // req = new String(requestCode.getBytes(), "unicode");
        // } catch (UnsupportedEncodingException e) {
        // e.printStackTrace();
        // }
        // String s = string2Unicode(requestCode);
        // Log.d("lzk", "get_input_inline = " + s);
        return requestCode;
    }
    
    /**
     * 字符串转换unicode
     */
    @SuppressWarnings("unused")
    private String string2Unicode(String string) {
        if (string != null && !TextUtils.isEmpty(string)) {
            StringBuffer unicode = new StringBuffer();
            for (int i = 0; i < string.length(); i++) {
                // 取出每一个字符
                char c = string.charAt(i);
                // 转换为unicode
                unicode.append("\\u" + Integer.toHexString(c));
            }
            return unicode.toString();
        }
        return string;
    }


    public static final String getEditorString() {
        return sEditorText;
    }


    /**
     * 框中文字，每次发起请求时都需要设置
     * @param text 框中文字
     */
    public static final void setEditorString(String text) {
        sEditorText = text;
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        
        sb.append("sugLogType=");
        sb.append(sugLogType);
        sb.append(",packageId=");
        sb.append(packageId);
        sb.append(",editorId=");
        sb.append(editorId);
        sb.append(",type=");
        sb.append(type);
        sb.append(",word=");
        sb.append(word);
        sb.append(",sourceId=");
        sb.append(sourceId);
        sb.append(",requestCode=");
        sb.append(requestCode);
        
        sb.append('\n');
        return sb.toString();
    }
}
