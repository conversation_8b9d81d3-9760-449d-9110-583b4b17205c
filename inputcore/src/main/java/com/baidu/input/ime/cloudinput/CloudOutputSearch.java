package com.baidu.input.ime.cloudinput;

/**
 * 云输入搜索服务
 *
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/10/8.
 */
public class CloudOutputSearch {
    public int type;
    public int jump_tab;
    public int word_type;
    public int show_count;
    public String keyword;
    public String hint;

    /**
     * 内核反射调用
     * 设置类型（目前只有搜索关键字，CLOUD_OUTPUT_SEARCH_TYPE_KEYWORD）,搜索内容的类型，如文字、图片等
     *
     * @param type         类型
     * @param jump_tab 搜索内容的类型
     * @param word_type 内核与服务端约定字段
     * <AUTHOR> on 16/10/9.
     */
    public void set_int(int type, int jump_tab, int word_type, int show_count) {
        this.type = type;
        this.jump_tab = jump_tab;
        this.word_type = word_type;
        this.show_count = show_count;
    }

    /**
     * 内核反射调用
     * 设置关键字和提示
     *
     * @param keyword 关键字
     * @param hint    提示
     * <AUTHOR> on 16/10/9
     */
    public void set_str(String keyword, String hint) {
        this.keyword = keyword;
        this.hint = hint;
    }
}
