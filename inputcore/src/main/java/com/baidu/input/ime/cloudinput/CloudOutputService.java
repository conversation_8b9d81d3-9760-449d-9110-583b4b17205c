package com.baidu.input.ime.cloudinput;

import android.text.TextUtils;

import com.baidu.input.devtool.log.BDLog;
import com.baidu.input.ime.utils.Utils;
import com.baidu.input.inputcore.BuildConfig;

/**
 * 云输入服务端返回的结果，可能一次有多个结果，详细可参考_pub_iptcore.h的struct s_cloud_output 由内核回调
 *
 * <AUTHOR>
 */
public class CloudOutputService {
    /**
     * 预留
     */
    public static final byte CLOUD_OUTPUT_SERVICE_TYPE_RESERVE = 1;
    /**
     * 云输入文本结果
     */
    public static final byte CLOUD_OUTPUT_SERVICE_TYPE_PLAIN = 2;
    /**
     * 文字类（文字彩蛋，文字链，特效文字等）
     */
    public static final byte CLOUD_OUTPUT_SERVICE_TYPE_TEXT = 3;
    /**
     * 电影
     */
    public static final byte CLOUD_OUTPUT_SERVICE_TYPE_MOVIE = 4;
    /**
     * emoji表情
     */
    public static final byte CLOUD_OUTPUT_SERVICE_TYPE_EMOJI = 5;
    /**
     * 颜文字
     */
    public static final byte CLOUD_OUTPUT_SERVICE_TYPE_EMOTION_ICON = 6;
    /**
     * 纯图片类型
     */
    public static final byte CLOUD_OUTPUT_SERVICE_TYPE_PICTURE = 7;
    /**
     * ios特技字体
     */
    public static final byte CLOUD_OUTPUT_SERVICE_TYPE_MAGIC_TEXT = 8;
    /**
     * SUG
     */
    public static final byte CLOUD_OUTPUT_SERVICE_TYPE_SUG = 9;
    /**
     * 可早后台配置的自定义SUG
     */
    public static final byte CLOUD_OUTPUT_SERVICE_TYPE_SUG_CUSTOM = 10;
    /**
     * SUG 广告
     */
    public static final byte CLOUD_OUTPUT_SERVICE_TYPE_SUG_AD = 11;
    /**
     * 网盟 SUG 广告
     */
    public static final byte CLOUD_OUTPUT_SERVICE_TYPE_SUG_AD_WANGMENG_APP = 12;

    /**
     * 智能回复普通词
     */
    public static final byte CLOUD_OUTPUT_SERVICE_TYPE_AI_REPLY = 101;

    /**
     * 智能回复意图词
     */
    public static final byte CLOUD_OUTPUT_SERVICE_TYPE_AI_INTENT = 102;

    /**
     * 智能回复Emoji表情
     */
    public static final byte CLOUD_OUTPUT_SERVICE_TYPE_AI_EMOJI = 103;

    /**
     * 智能回复-问题
     */
    public static final byte CLOUD_OUTPUT_SERVICE_TYPE_AI_QUERY_KEY = 104;

    /**
     * 唯一结果
     */
    public static final int CLOUD_OUTPUT_SERVICE_TYPE_UNIQUE_RESULT = 201;

    public static final byte CLOUD_LINK_OPEN_TYPE_DEFAULT = 1;
    public static final byte CLOUD_LINK_OPEN_TYPE_WEBVIEW = 2;
    public static final byte CLOUD_LINK_OPEN_TYPE_BAIDU = 3;
    public static final byte CLOUD_LINK_OPEN_TYPE_BAIDUBROWSER = 4;

    public static final int ID_SUG_INFO_BEGIN = 100;

    protected static final byte COUNT_NINEPATCH_RECT = 4;

    protected static final int SCALE_RECT_LEFT = 0;
    protected static final int SCALE_RECT_RIGHT = 1;
    protected static final int SCALE_RECT_TOP = 2;
    protected static final int SCALE_RECT_BOTTOM = 3;

    /**
     * 展示结果（含背景图）所占的宽
     */
    //    private int areaWidth;

    /**
     * 在结果中的顺序
     */
    public int pos;
    /**
     * 唯一标示当前结果
     */
    public String id;
    /**
     * 云输入下发内容类型
     */
    public int type;
    /**
     * 该云结果的优先级
     */
    public int priority;

    /**
     * 内容展链接
     */
    public String contentUrl;

    /**
     * 图片链接，如果有的话
     */
    public String imageUrl;

    /**
     * 内容，可能是文字，也可能是base64加密后的图片二进制数据
     */
    public String data;

    /**
     * 图片缓存地址，如果有的话
     */
    public String cachePath;
    /**
     * 云输入的结果,展示在候选兰上
     */
    public String word;

    /**
     * 图片显示位置类型, 左右上下相对于左上角的距离，左右上下
     */
    public int[] scaleRect;

    /**
     * 是否在联想条上显示
     */
    public boolean isInAssociate;

    /**
     * 显示标记位
     */
    public byte showFlag;

    /**
     * 此条记录的最大展示次数
     */
    public int displayTimes;

    private SugAction action;
    private CardInfo card;

    /**
     * sug广告样式
     */
    private Object sugAdStyle;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        CloudOutputService that = (CloudOutputService) o;

        if (type != that.type) {
            return false;
        }
        return TextUtils.equals(word, that.word);
    }

    @Override
    public int hashCode() {
        int result = type;
        result = 31 * result + (TextUtils.isEmpty(word) ? 0 : word.hashCode());
        return result;
    }

    /**
     * 设置显示标记位
     *
     * @param flag
     */
    public void set_show_flag(byte flag) {
        if (BuildConfig.DEBUG) {
            BDLog.d("lzk", "CloudOutputService set_show_flag = " + flag);
        }
        showFlag = flag;
        isInAssociate = (flag == 1);
    }

    public void set_service_type(int serviceType) {
        if (BuildConfig.DEBUG) {
            BDLog.v("lzk", "CloudOutputService set_service_type = " + serviceType);
        }
        type = serviceType;
    }

    /**
     * 设置候选字
     *
     * @param uni 候选，NULL时表示没有文字候选，例如只有图片结果的情况
     */
    public void set_uni(String uni) {
        //        if (Macro.IS_DEBUG) {
        //            Log.v("lzk", "CloudOutputService set_uni = " + uni);
        //        }
        word = uni;
    }

    /**
     * 二进制图片，通过img.getBytes()获取；或者是匹配的上屏词，取决于type
     *
     * @param img
     */
    public void set_img(String img) {
        if (BuildConfig.DEBUG) {
            BDLog.v("lzk", "CloudOutputService set_img = " + img);
        }
        if (img != null) {
            this.data = img;
        }
    }

    public void set_img_url(byte[] imgUrl) {
        if (imgUrl == null) {
            this.imageUrl = null;
        } else {
            this.imageUrl = Utils.addWebUrlProtocolIfNeed(new String(imgUrl));
        }
        if (BuildConfig.DEBUG) {
            BDLog.v("lzk", "CloudOutputService set_imgUrl = " + this.imageUrl);
        }
    }

    public void set_goto_url(byte[] gotoUrl) {

        if (gotoUrl == null) {
            this.contentUrl = null;
        } else {
            String url = new String(gotoUrl);
            if (Utils.isCustomUrl(url)) {
                this.contentUrl = url;
            } else {
                this.contentUrl = Utils.addWebUrlProtocolIfNeed(url);
            }
        }
        if (BuildConfig.DEBUG) {
            BDLog.v("lzk", "CloudOutputService set_gotoUrl = " + this.contentUrl);
        }
    }

    public void set_uid(byte[] uid) {
        if (uid != null && uid.length > 0) {
            id = new String(uid);
            if (BuildConfig.DEBUG) {
                BDLog.v("lzk",
                        "set_uid = " + id);
            }
        }
    }

    /**
     * 设置最大展现次数，>=0为服务端给的值,<0为没有获取到服务端给的值
     *
     * @param num
     */
    public void set_max_show_num(int num) {
        displayTimes = num;
    }

    public void set_img_rect(int[] points) {
        if (BuildConfig.DEBUG) {
            String str = "";
            if (points != null) {
                for (int i = 0; i < points.length; i++) {
                    str += points[i] + ", ";
                    System.err.println("points " + i + ":" + points[i]);
                }
            }
            BDLog.v("lzk", "CloudOutputService set_img_rect = " + str);
        }

        // 图片类型暂时不支持拉伸，如果支持需要做防错处理
        if (type == CLOUD_OUTPUT_SERVICE_TYPE_MOVIE && points != null) {
            if (points.length == COUNT_NINEPATCH_RECT) {
                scaleRect = points;
                int tmp = scaleRect[2];
                scaleRect[SCALE_RECT_TOP] = scaleRect[1];
                scaleRect[SCALE_RECT_RIGHT] = tmp;
                for (int i = 0; i < scaleRect.length; i++) {
                    if (scaleRect[i] < 0) {
                        scaleRect = null;
                        return;
                    }
                }

                if (scaleRect[SCALE_RECT_RIGHT] < scaleRect[SCALE_RECT_LEFT]
                        || scaleRect[SCALE_RECT_BOTTOM] < scaleRect[SCALE_RECT_TOP]
                        || (scaleRect[SCALE_RECT_RIGHT] == 0 && scaleRect[SCALE_RECT_LEFT] == 0
                                    && scaleRect[SCALE_RECT_TOP] == 0 && scaleRect[SCALE_RECT_BOTTOM] == 0)) {
                    scaleRect = null;
                    return;
                }

            }
        }
    }

    public boolean isTypeValid() {
        if (type >= CLOUD_OUTPUT_SERVICE_TYPE_PLAIN && type <= CLOUD_OUTPUT_SERVICE_TYPE_SUG_CUSTOM) {
            return true;
        }

        if (type >= CLOUD_OUTPUT_SERVICE_TYPE_UNIQUE_RESULT) {
            return true;
        }

        return false;
    }

    // @Override
    // public int[] getScaleRect() {
    // if (CloudDataManager.USE_FAKE_DATA) {
    // if (type == CLOUD_OUTPUT_SERVICE_TYPE_MOVIE) {
    // String path = FileSys.getResPath();
    // if (path.equals("720/") || path.equals("1080/")) {
    // scaleRect = new int[] { 14, 56, 0, 110 };
    // } else if (path.equals("480/")) {
    // scaleRect = new int[] { 4, 22, 0, 44 };
    // } else {
    // scaleRect = new int[] { 3, 15, 0, 30 };
    // }
    // }
    // }
    // return scaleRect;
    // }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("id=");
        sb.append(id);
        sb.append(",type=");
        sb.append(type);
        sb.append(",displayTimes=");
        sb.append(displayTimes);
        sb.append(",priority=");
        sb.append(priority);
        sb.append(",word=");
        sb.append(word);
        sb.append(",isInAssociate=");
        sb.append(isInAssociate);
        sb.append(",showFlag=");
        sb.append(showFlag);
        sb.append(",data=");
        sb.append(data);
        sb.append(",imageUrl=");
        sb.append(imageUrl);
        sb.append(",contentUrl=");
        sb.append(contentUrl);
        sb.append('\n');
        return sb.toString();
    }

    /**
     * 判断是否需要插入到次cand，目前只有纯文本插入到次cand数据中
     * @auther shehonghao
     * @return
     *
     */
    public boolean isNeedInsert2MinorCand() {
        return !(type == CLOUD_OUTPUT_SERVICE_TYPE_PLAIN
                         || (type == CLOUD_OUTPUT_SERVICE_TYPE_TEXT && TextUtils.isEmpty(contentUrl)));
    }

    /**
     * Sug打开的行为是否是卡片
     */
    public boolean isCard2Click() {
        return null != action && action.openCard && type == CloudOutputService.CLOUD_OUTPUT_SERVICE_TYPE_SUG;
    }

    public void setSugAction(SugAction action) {
        this.action = action;
    }

    public SugAction getSugAction() {
        if (action == null) {
            return new SugAction();
        }
        return action;
    }

    public void setCardInfo(CardInfo info) {
        this.card = info;
    }

    public CardInfo getCardInfo() {
        // if (card == null) {
        // return new CardInfo();
        // }
        return card;
    }

    public Object getSugAdStyle() {
        return sugAdStyle;
    }

    public void setSugAdStyle(Object sugAdStyle) {
        this.sugAdStyle = sugAdStyle;
    }

    // ////////////////////////////////////////// 新内核 //////////////////////////////////////////
    public boolean hasContentUrl = false;
    public boolean containsSugCard = false;
    public boolean isCloudServiceLian = false;
}
