package com.baidu.input.ime.cloudinput;

/**
 * 给内核使用的，云输入需要设备及应用信息
 *
 * <AUTHOR>
 */
public interface ICloudInfo {

    // CLOUD_NET_TYPE
    int CLOUD_NET_TYPE_UNKNOW = 0;
    int CLOUD_NET_TYPE_2G = 1;
    int CLOUD_NET_TYPE_3G = 2;
    int CLOUD_NET_TYPE_4G = 3;
    int CLOUD_NET_TYPE_4_NG = 4;
    int CLOUD_NET_TYPE_WIFI = 10;
    int CLOUD_NET_TYPE_END = 255;

    String get_cuid();

    // 系统和型号,可以不带型号，系统|型号|其它信息1|其它信息2 例如an|小米2S或ios|iphone5s或mac或an或win7 多个信息用|分割
    String get_cname();

    String get_input_ver();

    // 当前输入应用 "短信" "记事本"
    String get_app_name();

    // 联网方式0:未知,1:2g,2:3g,3:4g,4:>4g,10:wifi 参见CLOUD_NET_TYPE
    int get_net_type();

    /**
     * 短的一边
     */
    char get_screen_width();

    /**
     * 长的一边
     */
    char get_screen_height();

    String get_channel();

    String get_city();

    byte[] get_log();

    /**
     * 客户端和服务端透传的json数据
     */
    byte[] get_json_buf();

}
