package com.baidu.input.ime.cloudinput;

/**
 * 给内核使用的，输入法当前各个状态
 * 
 * <AUTHOR>
 * 
 */
public interface ICloudSetting {
    // 内核CLOUD_INPUT_TYPE常量begin
    int CLOUD_INPUT_TYPE_PY = 0; // /拼音输入方式
    int CLOUD_INPUT_TYPE_EPY = 1;// /带纠错的拼音输入方式
    int CLOUD_INPUT_TYPE_T9 = 2; // /T9拼音输入方式
    int CLOUD_INPUT_TYPE_SP = 3; // /双拼输入方式
    int CLOUD_INPUT_TYPE_HW = 4; // /手写输入方式
    int CLOUD_INPUT_TYPE_LIAN = 5; // /联想输入方式
    int CLOUD_INPUT_TYPE_SUG_BEGAN = 6;
    int CLOUD_INPUT_TYPE_SUG_CARD = 7; // /sug卡片请求
    int CLOUD_INPUT_TYPE_SUG_T9 = 8; // /sug请求T9
    int CLOUD_INPUT_TYPE_SUG_PY = 9; // /sug请求拼音
    int CLOUD_INPUT_TYPE_SUG_PRE = 10; // /sug请求只支持前缀查找
    // sug请求双拼
    int CLOUD_INPUT_TYPE_SUG_SP = 11;
    int CLOUD_INPUT_TYPE_SUG_END = 100;
    int CLOUD_INPUT_TYPE_SEARCH_BEGAN = 101;
    int CLOUD_INPUT_TYPE_SEARCH_KEYBOARD = 102; // 云搜索请求
    int CLOUD_INPUT_TYPE_SEARCH_END = 200;

    int CLOUD_INPUT_TYPE_AI_REPLY = 202;

    // CLOUD_COMPRESS_TYPE
    int CLOUD_COMPRESS_TYPE_NONE = 0; // /无压缩
    int CLOUD_COMPRESS_TYPE_GZIP = 1; // /gzip压缩方式
    int CLOUD_COMPRESS_TYPE_BEZIER = 2; // /bezier拟合压缩方式

    // CLOUD_ENCRY_TYPE
    int CLOUD_ENCRY_TYPE_NONE = 0; // /无加密
    int CLOUD_ENCRY_TYPE_AES = 1; // /AES方式加密

    // 输入方式，参见 CLOUD_INPUT_TYPE
    char get_input_type();

    ///期望返回普通候选云词的条数
    char get_ret_cnt();

    // 请求数据的压缩方式,拼音建议CLOUD_COMPRESS_TYPE_GZIP,手写建议CLOUD_COMPRESS_TYPE_NONE
    byte get_req_compress_type();

    // 请求数据的加密方式,拼音和手写都建议CLOUD_ENCRY_TYPE_AES
    byte get_req_encry_type();

    // 返回数据的压缩方式,拼音和手写都建议CLOUD_COMPRESS_TYPE_GZIP
    byte get_ret_compress_type();

    // 返回数据的加密方式,拼音和手写都建议CLOUD_ENCRY_TYPE_AES
    byte get_ret_encry_type();

    // 手写块使用的压缩方式,只在手写输入时使用,建议CLOUD_COMPRESS_TYPE_BEZIER
    byte get_hw_block_compress_type();

    // 输入框id,场景化下发
    char get_input_id();

    // 云输入请求等级，默认是1，代表最高等级。后续可以制订等级2、3等等。
    int get_trigger_level();

    /**
     * uni有3种用途<br>
     * 1.发起请求时需要带上的输入框中的词
     * 2.五笔，笔画时带上的首选词
     * 3.发起sug卡片请求是带上的选中sug词
     */
    String get_lian_uni();

    /**
     * 该输入框是否是需要云输入服务强推，没有用
     * @return
     */
    int get_yun_input_id();

    /**
     * 给内核使用
     * @return
     */
    int get_sug_source_id();

    /**
     * 给内核使用校验id
     *
     * @return
     */
    int get_check_id();

    /**
     * 获得整句预测的开关，该方法提供给JNI调用
     */
    int getZjForecast();

}
