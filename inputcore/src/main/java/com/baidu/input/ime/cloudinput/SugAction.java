package com.baidu.input.ime.cloudinput;

/**
 * 搜索建议点击行为
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 15/12/15.
 */
public class SugAction {
    /**
     * 直接上屏
     */
    public static final byte TYPE_NATIVE_SUBMIT = 0;
    /**
     * 当前app跳转链接
     */
    public static final byte TYPE_NATIVE_OPEN_URL = 1;
    /**
     * 当前app搜索
     */
    public static final byte TYPE_NATIVE_SEARCH = 2;
    /**
     * 跳转第三方打开链接
     */
    public static final byte TYPE_APP_OPEN_URL = 3;
    /**
     * 跳转第三方搜索
     */
    public static final byte TYPE_APP_SEARCH = 4;
    /**
     * 直接下载
     */
    public static final byte TYPE_DOWNLOAD = 5;

    public static final byte STATE_CARD = 1;
    public static final byte STATE_COMMIT = 2;

    public static final byte MI_SOURCE_GAME = 1;
    public static final byte MI_SOURCE_APP = 2;

    /**
     * 4种行为类型，直接上屏,第三方app打开等
     */
    public static int type = TYPE_NATIVE_SEARCH;
    /**
     * 源ID
     */
    public static int sourceId;
    /**
     * sug 的 ID
     */
    public static int sugId;
    /**
     * sug的类型,电影,音乐等
     */
    public int cardType;
    /**
     * 额外行为标记,1:需要卡片
     */
    public boolean openCard;
    /**
     * sug来源的提示信息
     */
    public String sourceMsg;
    public int miSourceType;
    /**
     * 点击打开按钮后的执行指令
     */
    public String command;
    /**
     * 临时新增需求，点击卡片其他区域需执行的指令
     */
    public String command2;
    /**
     * 第三方包信息，用于跳转
     */
    public PackageInfo[] packageInfos;

    private byte cardState;

    public class PackageInfo {
        /**
         * 包名
         */
        public String packageName;
        /**
         * 最大最小版本
         */
        public int[] version;
    }

    public SugAction() {
        cardState = STATE_CARD;
    }

    public byte getCardState() {
        return cardState;
    }

    public void setCardState(byte state) {
        cardState = state;
    }

    public void set_int(int a, int b, int c, int d, int e) {
        type = a;
        cardType = b;
        sourceId = c;
        openCard = (d == 1);
        if (packageInfos != null) {
            packageInfos = null;
        }
        packageInfos = new PackageInfo[e];
    }

    public void set_str(String a, String b) {
        sourceMsg = a;
        command = b;
    }

    public void set_pkg(int i, String a, int[] b) {
        if (packageInfos != null && packageInfos.length > i) {
            packageInfos[i] = new PackageInfo();
            packageInfos[i].packageName = a;
            packageInfos[i].version = b;
        }
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("type=");
        sb.append(type);
        sb.append(",sourceId=");
        sb.append(sourceId);
        sb.append(",cardType=");
        sb.append(cardType);
        sb.append(",openCard=");
        sb.append(openCard);
        sb.append(",command=");
        sb.append(command);
        sb.append(",command2=");
        sb.append(command2);
        if (packageInfos != null) {
            for (int i = 0; i < packageInfos.length; i++) {
                if (packageInfos[i] != null) {
                    sb.append(",pkg=");
                    sb.append(packageInfos[i].packageName);

                    int[] vers = packageInfos[i].version;
                    String verStr = "";
                    if (vers != null) {
                        for (int j = 0; j < vers.length; j++) {
                            verStr += vers[j] + "_";
                        }
                    }

                    sb.append(",ver=");
                    sb.append(verStr);
                }
            }
        }

        sb.append('\n');
        return sb.toString();
    }
}
