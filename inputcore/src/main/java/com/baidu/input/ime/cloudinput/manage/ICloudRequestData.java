/*
 * Copyright (C) 2016 Baidu, Inc. All Rights Reserved.
 */
package com.baidu.input.ime.cloudinput.manage;

/**
 * 内核产生的云输入请求数据的包装类，包括上传给云服务器的byte数组和云输入的延迟参数。
 */
public interface ICloudRequestData {

    /**
     * 内核反射调用。内核通过此方法返回云输入请求内容
     */
    void setReqContent(byte[] reqContent);

    /**
     * 内核反射调用。获取云输入请求数据过程中，内核返回延迟时间，上层根据delay来延迟发送请求
     * @param delay 内核返回的云输入延迟时间
     */
    void setDelayTime(int delay);

    /**
     * 内核反射调用。获取云输入请求数据过程中，上层是否要显示云动画（云预测请求时不显示云动画）
     * @param needArrow 是否需要显示云动画（的箭头）。内核返回的是int型数据
     */
    void setNeedArrow(int needArrow);
}
