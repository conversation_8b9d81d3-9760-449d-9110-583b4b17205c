package com.baidu.input.ime.utils;

import android.text.TextUtils;

/**
 * 工具类
 */
public class Utils {
    /**
     * 默认协议
     */
    private static final String DEFAULT_PROTOCOL = "http://";

    /**
     * 协议数组
     */
    private static String[] protocols = null;

    /**
     * 自定义链接都是以open开头
     */
    private static final String CUSTOM_URL_PREFIX = "open";

    /**
     * 检查url，需要的话添加默认的http协议头
     * @param url url
     * @return 处理过的url
     */
    public static String addWebUrlProtocolIfNeed(String url) {
        if (!hasWebUrlProtocol(url)) {
            url = DEFAULT_PROTOCOL + url;
        }
        return url;
    }

    /**
     * url时候以网络协议开头
     * @param url url
     * @return true:以协议开头
     */
    public static boolean hasWebUrlProtocol(String url) {
        if (null == protocols) {
            String protocol = "http|https|Http|Https|rtsp|Rtsp|file";
            protocols = protocol.split("\\|");
        }

        for (String protocol : protocols) {
            if (url.startsWith(protocol)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 是否是自定义Url
     *
     * @param url
     * @return
     */
    public static boolean isCustomUrl(String url) {
        if (TextUtils.isEmpty(url)) {
            return false;
        }
        return url.startsWith(CUSTOM_URL_PREFIX);
    }
}
