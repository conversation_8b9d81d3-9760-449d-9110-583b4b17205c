package com.baidu.input.pub;

public class CellInfo {
    /** 词库类型：默认 */
    private static final int CELL_TYPE_DEFAULT = 0;
    /** 词库类型：历史位置，含当前地 */
    public static final int CELL_TYPE_HISTORY_LOC = 1;
    /** 词库类型：常驻地 */
    public static final int CELL_TYPE_RESIDENCE = 2;

    /**
     * 细胞词库可见,{@link #isHide}
     */
    public static final int CELL_VISIBAL = 0;
    /**
     * 细胞词库不可见,{@link #isHide}
     */
    public static final int CELL_INVISIBAL = 1;
    
    public int server_guid; // 词库唯一识别号
    public int client_guid; // 词库唯一识别号
    public int ver1; // 主版号
    public int ver2; // 次版号
    public int ver3; // 次次版本号

    public int data_type;// 1表示增量词库 表示完整词库
    public int inner_ver_from; // 完整词库下始终为0，增量词库下表示该增量词库对应的上一个版本的内部版本号
    public int inner_ver; // 完整词库下表示内部版本号（流水线号），增量词库下表示该增量词库对应的最新版本的内部版本号

    public int type1; // 主分类ID
    public int type2; // 二级分类ID
    public int type3; // 三级分类ID
    public int ci_count;// 词条数

    public String name;
    public String author;
    public String keywords;
    public String info;
    
    /** 服务端维护的词库类型。0：普通类型，1：所在地；2：常驻地 */
    public int serverType;
    /** 服务端维护的词库更新时间 */
    public int serverTime;
    /** 是否隐藏,{@link #CELL_INVISIBAL},{@link #CELL_VISIBAL}*/
    public int isHide;
    
    /**
     * 内核反射方法
     * @param a server_guid
     * @param b client_guid
     * @param c ver1
     * @param d ver2
     * @param e ver3
     * @param f data_type
     * @param g inner_ver_from
     * @param h inner_ver
     * @param i type1
     * @param j type2
     * @param k type3
     * @param l ci_count
     * @param m loc_type
     * @param n install_time
     * @param o is_hide
     */
    public final void set_int(int a, int b, int c, int d, int e, int f, int g, int h, int i, int j, int k,
                              int l, int m, int n, int o) {
        server_guid = a;
        client_guid = b;
        ver1 = c;
        ver2 = d;
        ver3 = e;
        data_type = f;
        inner_ver_from = g;
        inner_ver = h;
        type1 = i;
        type2 = j;
        type3 = k;
        ci_count = l;
        serverType = m;
        serverTime = n;
        isHide = o;
    }

    /**
     * 内核反射方法
     * 
     * @param s1 name
     * @param s2 author
     * @param s3 keywords
     * @param s4 info
     */
    public final void set_str(String s1, String s2, String s3, String s4) {
        name = s1;
        author = s2;
        keywords = s3;
        info = s4;
    }

    public boolean getEnabled() {
        return ((client_guid & 0x80000000) != 0 && client_guid != -1);
    }

    /**
     * 获取细胞词库的id 目前输入法最多只能安装40个细胞词库
     * 
     * @return 细胞词库的id
     */
    public final int getID() {
        return (client_guid & 0xFF);
    }

    public CellInfo copy() {
        CellInfo info = new CellInfo();
        info.client_guid = client_guid;
        info.server_guid = server_guid;
        info.ver1 = ver1;
        info.ver2 = ver2;
        info.ver3 = ver3;
        info.name = name;
        info.author = author;
        info.ci_count = ci_count;
        info.keywords = keywords;
        info.serverType = serverType;
        info.serverTime = serverTime;
        info.isHide = isHide;
        return info;
    }

    @Override
    public String toString() {
        return "{" + name + "(id=" + server_guid + ",type=" + serverType + ",time=" + serverTime + ")}";
    }

    /**
     * 是否是静默下载的地理词库
     * @return true: 是静默下载的地理词库
     */
    public boolean isAutoDownloadGeo() {
        return serverType == CELL_TYPE_HISTORY_LOC || serverType == CELL_TYPE_RESIDENCE;
    }
}