package com.baidu.input.pub;

/**
 * 对候选词的封装，该数据结构既可以由上层构造，也可以由内核调用生成
 *
 * 2013-10-21
 */
public class CoreStringInfo {

    // //////////////////////////////////////////////////////////////////////////////////////////////////////////////
    // //////////////////////////候选词属性定义////////////////////////////////////////////////////////////////
    // //////////////////////////////////////////////////////////////////////////////////////////////////////////////
    // //////////////////////////////////////////////内核通用类型属性定义//////////////////////////////////////////
    /** 中文候选词属性定义 */
    protected static final int EX_CAND_FLAG_CH = 0x1;
    /** 英文候选词属性定义 */
    protected static final int EX_CAND_FLAG_EN = 0x2;
    /** 长句候选项属性定义 */
    protected static final int EX_CAND_FLAG_CJ = 0x4;
    /** 保留 */
    protected static final int EX_CAND_FLAG_RESERVED_1 = 0x8;
    /** 用户词属性定义（不包含细胞词, 即可以删除的词） */
    protected static final int EX_CAND_FLAG_US = 0x10;
    /** 细胞词属性定义 */
    protected static final int EX_CAND_FLAG_CELL = 0x20;
    /** 中文笔画或五笔输入词属性定义 */
    protected static final int EX_CAND_FLAG_BW = 0x40;
    /** 中文联想词属性定义 */
    protected static final int EX_CAND_FLAG_LX = 0x80;
    /** 输入码候选词属性定义 */
    protected static final int EX_CAND_FLAG_EN_INPUT = 0x100;
    /** 长句候选项属性定义(带有英文单词结果) */
    protected static final int EX_CAND_FLAG_CJ_CNEN = 0x200;
    /** 逐词输入词属性定义 */
    protected static final int EX_CAND_FLAG_PRED = 0x400;
    /** 最高逐词输入词属性定义 */
    protected static final int EX_CAND_FLAG_TOP_PRED = 0x800;
    /** 三维词库词属性定义(执行联想查询时或者长句带三维词库结果) */
    protected static final int EX_CAND_FLAG_GRAM = 0x1000;
    /** 是否纠错候选标记 */
    protected static final int EX_CAND_FLAG_IEC = 0x2000;
    /** 联系人词 */
    protected static final int EX_CAND_FLAG_CONTACT = 0x4000;
    /** 上划输入造的特殊词 */
    protected static final int EX_CAND_FLAG_EN_PRECISE = 0x8000;
    /** 个性短语属性定义 */
    protected static final int EX_CAND_FLAG_PHRASE = 0x10000;
    /** 中英混合词定义 */
    protected static final int EX_CAND_FLAG_MIXWORD = 0x20000;
    /** 卖萌词定义 */
    protected static final int EX_CAND_FLAG_OTHERWORD = 0x40000;
    /** 拆字组合属性定义 */
    protected static final int EX_CAND_FLAG_CHAIZI = 0x80000;

    // ///////////////////////////////////特殊类型属性定义////////////////////////////////////////////////////////
    /** 搜索词属性定义-- 中文、英文、长句、短语类通用 output内容为命中的搜索词 */
    protected static final int EX_CAND_FLAG_SEARCH = 0x100000;
    /** 多媒体词属性定义 -- 中文、英文、长句、短语类通用 output格式为(1,2位指示media的类型(1定位，2图片，3录音，4随写，5涂鸦)， 3-结束 提示 */
    protected static final int EX_CAND_FLAG_MEDIA = 0x200000;
    /** 排在第一个的纠错词 */
    protected static final int EX_CAND_FLAG_TOP_IEC = 0x800000;
    // //////为了保证以后可以扩展，低24位每一个bit都是独立的含义，可以直接与操作判断，高八位用于做附加的属性定义，用等号做判断在做高八位判断时，记得对低24位清零////////////////////
    // ///////////////////////////////////特殊类型属性定义，当低24位都是0的时候，使用等号做判断(==)/////////////////////////////////////////////////////
    /** 符号属性定义 */
    protected static final int EX_CAND_FLAG_SYM = (2 << 24);
    /** 旧版自定义输入法属性定义 */
    protected static final int EX_CAND_FLAG_OLDDEF = (3 << 24);
    /** 符号联想属性定义 */
    protected static final int EX_CAND_FLAG_SYM_LIAN = (4 << 24);
    /** 网址和邮箱符号属性定义 */
    protected static final int EX_CAND_FLAG_FORM = (5 << 24);
    /** 固定首位词属性定义 */
    protected static final int EX_CAND_FLAG_FIRST = (6 << 24);
    /** 表情联想属性定义 */
    protected static final int EX_CAND_FLAG_EMOJI_LIAN = (7 << 24);
    /** 表情属性定义 */
    protected static final int EX_CAND_FLAG_EMOJI = (8 << 24);
    /** 仓颉输入法属性定义 */
    protected static final int EX_CAND_FLAG_CANGJIE = (9 << 24);
    /** 注音输入法属性定义 */
    protected static final int EX_CAND_FLAG_ZY = (10 << 24);
    /** 歇后语词属性定义 */
    protected static final int EX_CAND_FLAG_XIEHOUYU = (13 << 24);
    /** 快速输入属性定义 */
    protected static final int EX_CAND_FLAG_FAST_INPUT = (14 << 24);
    /** 直达号属性定义(废弃） */
    @Deprecated
    protected static final int EX_CAND_FLAG_ZHIDA = (15 << 24);
    /** Cand春节运营活动(废弃） */
    @Deprecated
    protected static final int EX_CAND_FLAG_OP = (16 << 24);
    /** 颜文字联想属性定义 */
    protected static final int EX_CAND_FLAG_YAN_LIAN = (20 << 24);
    /** 颜文字属性定义 */
    protected static final int EX_CAND_FLAG_EMOTICON = (22 << 24);

    /** 云预测标记 */
    protected static final int EX_CAND_FLAG_CLOUD_CACHE = (25 << 24);

    /** 更多颜文字标识 */
    public static final int EX_CAN_FLAG_MORE_EMOJI_ICON = (17 << 24);

    /** 联系人联想标识 */
    public static final int EX_CAND_FLAG_CONTACT_LIAN = (28 << 24);

    /** 云输入联想标识 */
    public static final int EX_CAND_FLAG_CLOUD_LIAN = (30 << 24);

    /** 云输入通过预测得到的联想 */
    public static final int EX_CAND_FLAG_CLOUD_FORCAST_LIAN = (31 << 24);
    /** 内核联想词运营 */
    public static final int EX_CAND_FLAG_SPECIAL_LIAN = (32 << 24);

    /**
     * 该词的内容
     *
     * 另外的特殊情况会保存额外信息: 1.当coreString为INDEX_EMOJI_PKG类型时，存uid
     */
    public String value;

    /** 该词的内核属性 */
    protected int flag;

    public void set(String v, int f) {
        value = v;
        flag = f;
    }
}
