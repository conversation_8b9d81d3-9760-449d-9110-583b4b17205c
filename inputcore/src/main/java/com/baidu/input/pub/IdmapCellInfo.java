package com.baidu.input.pub;

/**
 * 颜文字及emoji元素结构体
 * 
 * <AUTHOR>
 * 
 */
public class IdmapCellInfo {
    public int type;
    public int cell_id;
    public int flag;
    public String data;
    public byte[] datas;

    public final void set_str(String s) {
        this.data = s;
    }

    public final void set_int(int a, int b, int c) {
        this.type = a;
        this.cell_id = b;
        this.flag = c;
    }

    public final void set_byte(byte[] d) {
        this.datas = d;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        return sb.append("[type: " + type + " - cell_id: " + cell_id + " - flag: " + flag + " - data: "
                + (data == null ? datas : data)).toString();
    }
}
