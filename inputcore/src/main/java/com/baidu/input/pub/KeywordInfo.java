package com.baidu.input.pub;

/**
 * 词库对象的信息
 */
public class KeywordInfo {
    public int time;// 文件生成日期(由服务端指定，频率相同时，生成日期较晚的优先出)
    // ---------------------------------------------------
    public int server_guid; // 词库唯一识别码,服务端生成.
    public int client_guid; // 客户端安装时指定的本地ID。
    public int ver1; // 主版本
    public int ver2; // 次版本
    // ----------------------------
    public int ver3; // 次次版本
    public int data_type;// 1表示增量词库。0 表示完整词库。
    public int inner_ver_from; // 完整词库下始终为0，增量词库下表示该增量词库对应的上一个版本的内部版本号。
    public int inner_ver; // 完整词库下表示内部版本号（流水线号），增量词库下表示该增量词库对应的最新版本的内部版本号。
    // -----------------------------
    public int type1; // 主分类ID ，分类ID 对应信息由服务端管理，空表示未分类。
    public int type2; // 二级分类ID
    public int type3; // 三级分类ID
    public int type4; // 四级分类ID
    // -----------------------------
    public int keyword_count;// 增加表情个数

    public final void set_int(int a, int b, int c, int d, int e, int f, int g, int h, int i, int j, int k, int l,
            int m, int n) {
        time = a;
        server_guid = b;
        client_guid = c;
        ver1 = d;
        ver2 = e;
        ver3 = f;
        data_type = g;
        inner_ver_from = h;
        inner_ver = i;
        type1 = j;
        type2 = k;
        type3 = l;
        type4 = m;
        keyword_count = n;
    }

}
