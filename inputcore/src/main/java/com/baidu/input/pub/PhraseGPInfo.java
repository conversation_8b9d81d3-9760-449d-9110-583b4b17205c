package com.baidu.input.pub;

/**
 * 个性短语组结构体
 * 
 * <AUTHOR>
 * 
 */
public final class PhraseGPInfo {
    public int group_OFF;// ////组GUID
    public int group_CRC;// 组CRC校验.修改时将会验证这个数据.完整性.

    public int group_id;// //修改组ID = 0;
    public int phrase_cnt;// ///个性短语个数.
    public boolean is_open; // //个性短语分组,是否打开.
    public String word;// //最长32个中文字符.

    public int index;

    public String summary;

    public final void set_str(String s1) {
        // BDLog.v("PhraseGPInfo set_str");
        word = s1;
    }

    public final void set_int(int a, int b, int c, int d, int e) {
        // BDLog.v("PhraseGPInfo set_str");
        group_OFF = a;
        group_CRC = b;
        group_id = c;
        phrase_cnt = d;
        is_open = (e == 1) ? true : false;
    }

    public final int getGroup_OFF() {
        return group_OFF;
    }

    public final int getGroup_CRC() {
        return group_CRC;
    }

    public final int getGroup_id() {
        return group_id;
    }

    public final int getPhrase_cnt() {
        return phrase_cnt;
    }

    public final int getIs_open() {
        return is_open ? 1 : 0;
    }

    public final String getWord() {
        return word;
    }

    public final void clear() {
        group_OFF = 0;
        group_CRC = 0;
        group_id = -1;
        phrase_cnt = 0;
        is_open = false;
        word = new String("");
    }

}
