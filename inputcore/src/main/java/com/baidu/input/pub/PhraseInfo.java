package com.baidu.input.pub;

/**
 * 个性短语结构体
 * 
 * <AUTHOR>
 * 
 */
public final class PhraseInfo {
    public int phrase_OFF;// 短语GUID(不可修改)
    public int phrase_CRC;// 短语CRC校验.修改时将会验证这个数据.完整性.(不可修改)

    public int pos;// 0 - 9//修改位置
    public int group_id;// 修改组ID
    public String code;// 最长32
    public String word;// 最长64

    public final void set_str(String s1, String s2) {
        // BDLog.v("PhraseInfo set_str");
        code = s1;
        word = s2;
    }

    public final void set_int(int a, int b, int c, int d) {
        // BDLog.v("PhraseInfo set_int");
        phrase_OFF = a;
        phrase_CRC = b;
        pos = c;
        group_id = d;
    }

    public final int getPhrase_OFF() {
        return phrase_OFF;
    }

    public final int getPhrase_CRC() {
        return phrase_CRC;
    }

    public final int getPos() {
        return pos;
    }

    public final int getGroup_id() {
        return group_id;
    }

    public final String getCode() {
        return code;
    }

    public final String getWord() {
        return word;
    }

    public final void clear() {
        phrase_OFF = 0;
        phrase_CRC = 0;
        pos = -1;
        group_id = 0;
        code = new String("");
        word = new String("");
    }
}
