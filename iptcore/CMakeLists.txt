# Sets the minimum version of CMake required to build your native library.
# This ensures that a certain set of CMake features is available to
# your build.

cmake_minimum_required(VERSION 3.4.1)
project(iptcore)

set(LOCAL_PATH ${CMAKE_CURRENT_SOURCE_DIR})
file(GLOB libinputcore_cpp "${LOCAL_PATH}/jni/*.cpp")
file(GLOB libinputcore "${LOCAL_PATH}/jni/*.c")
set(SRC_LIST ${libinputcore} ${libinputcore_cpp})

if(${CMAKE_ANDROID_ARCH_ABI} MATCHES "arm64-v8a")
    set(LIBS "lib64")
elseif(${CMAKE_ANDROID_ARCH_ABI} MATCHES "armeabi-v7a")
    set(LIBS "lib")
endif()

set(DEFINES
        -DPLATFORM_ANDROID
        -DIPT_PLATFORM_RVDS_ANDROID
        -DIPT_FEATURE_NN_SLIDE_PY26
        -DUSE_PADDLE
        -DFEATURE_SEARCH
        -DIPT_FEATURE_FREE_HW
        -DCRASH_VERSION
        -DIPT_MMAP
        -DIPT_BAYESIAN_SYM_LIAN
        -DFEATURE_PAD_RETURN
        -DIPT_GAUSS
        -DIPT_CONFIG_DEBUG_MODE )

add_definitions(${DEFINES})

#include_directories(/Users/<USER>/Library/Android/sdk/ndk/android-ndk-r20b/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/include)
#include_directories(/Users/<USER>/Library/Android/sdk/ndk/android-ndk-r20b/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/include/arm-linux-androideabi)
include_directories(${LOCAL_PATH}/jni)

add_library(iptcore SHARED ${SRC_LIST})

#设置strip——不带debug信息
set_target_properties(iptcore PROPERTIES LINK_FLAGS "-s")

find_library(log-lib log)

#如果集成内核的源码进行编译，打开add_subdirectory
#add_subdirectory(./cpp)
#include_directories(./cpp/source/_pub)

target_link_libraries(
        iptcore
        ${LOCAL_PATH}/jni/${LIBS}/libiptcore.a
#        ${LOCAL_PATH}/jni/${LIBS}/libprotobuf-lite.a
#        ${LOCAL_PATH}/jni/${LIBS}/libocr.a
#        ${LOCAL_PATH}/jni/${LIBS}/libhouyi_score_android.a
#        ${LOCAL_PATH}/jni/${LIBS}/libsnappy.a
        ${log-lib}
)
