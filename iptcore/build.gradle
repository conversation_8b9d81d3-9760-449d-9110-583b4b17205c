apply plugin: 'com.android.library'
//apply plugin: 'maven'
apply from: 'maven_publishing.gradle'

def useCmake = false

android {
    compileSdkVersion rootProject.ext.compileSdkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion

    defaultConfig {
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode rootProject.ext.versionCode
        versionName rootProject.ext.versionName

        testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"

        if (useCmake) {
            ndk {
                abiFilters "armeabi-v7a", "arm64-v8a"
            }
            externalNativeBuild {
                cmake {
                    cppFlags "-mfpu=neon -O3 -std=c++11 -ffunction-sections -fdata-sections " +
                            "-Wextra -pedantic -Wno-long-long -mfloat-abi=softfp -mfpu=vfpv3 " +
                            "-mfpu=neon -fPIE -ffast-math -frtti -fexceptions"
                    cFlags "-mfpu=neon -O3 -fopenmp -DIPT_PLATFORM_RVDS_ANDROID"
                    arguments '-DANDROID_PLATFORM=android-14', "-DANDROID_ARM_NEON=TRUE",
                            "-DCMAKE_BUILD_TYPE='Release'", '-DANDROID_TOOLCHAIN=clang',
                            "-DAPP_OPTIM=Release", "-DANDROID_STL=c++_shared"
                }
            }
        }

        buildConfigField "boolean", "ENABLE_NATIVE_TRACE", rootProject.ext.enableNativeTrace ? "true" : "false"
    }
    if (useCmake) {
        externalNativeBuild {
            cmake {
                version "3.10.2"
                path './CMakeLists.txt'
            }
        }
    }

    // 签名配置信息，
    signingConfigs {
        signed {
            keyAlias "imeurltest"
            keyPassword "baiduime"
            storeFile file("../imeurltest.keystore.jks")
            storePassword "baiduime"
            v1SigningEnabled true
            v2SigningEnabled false
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        mock {
            debuggable true
        }
        debug {
            debuggable true
            signingConfig signingConfigs.signed
            jniDebuggable = true
            renderscriptDebuggable = true
        }
    }

    if (useCmake) {

        externalNativeBuild {
            cmake {
                path "CMakeLists.txt"
            }
        }
    }

    sourceSets {
        main {
            java.srcDirs = ['src/main/java']
            jni.srcDirs = [] //disable automatic ndk-build call
        }

        release {
            root 'src/release'
            java.srcDirs = ['src/release/java']
            if (useCmake) {
                jniLibs.srcDir 'src/release/jniLibs'
                jni.srcDirs = [] //disable automatic ndk-build call
            } else {
                jniLibs.srcDir 'src/release/libs'
                jni.srcDirs = [] //disable automatic ndk-build call
            }
        }

        mock {
            root 'src/mock'
            java.srcDirs = ['src/mock/java']
            if (useCmake) {
                jniLibs.srcDir 'src/release/jniLibs'
                jni.srcDirs = [] //disable automatic ndk-build call
            } else {
                jniLibs.srcDir 'src/release/libs'
                jni.srcDirs = [] //disable automatic ndk-build call
            }
            assets.srcDirs = ['src/mock/assets']

        }

        debug {
            root 'src/release'
            java.srcDirs = ['src/release/java']
            if (useCmake) {
                jniLibs.srcDir 'src/release/jniLibs'
                jni.srcDirs = [] //disable automatic ndk-build call
            } else {
                jniLibs.srcDir 'src/release/libs'
                jni.srcDirs = [] //disable automatic ndk-build call
            }
        }
    }

    compileOptions {
        sourceCompatibility rootProject.ext.sourceCompatibilityVersion
        targetCompatibility rootProject.ext.targetCompatibilityVersion
    }

    defaultPublishConfig 'release'
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])

    implementation "com.android.support:appcompat-v7:${SUPPORT_V7_VERSION}"

    implementation("com.baidu.input:imebase:${IME_BASE_VERSION}") {
        exclude group: 'com.baidu.galaxy'
    }

    // test compiles
    testImplementation 'junit:junit:4.12'
    testImplementation "org.mockito:mockito-core:1.10.19"

    // mockito android test compiles
    androidTestImplementation "org.mockito:mockito-core:${MOCKITO_VERSION}"
    androidTestImplementation "org.mockito:mockito-android:${MOCKITO_VERSION}"

    // espresso test compiles
    androidTestImplementation('com.android.support.test.espresso:espresso-core:2.2.2', {
        exclude group: 'com.android.support', module: 'support-annotations'
    })

    // OkHttp
    implementation "com.squareup.okhttp3:okhttp:3.10.0"
    // input trace
    implementation "com.baidu.input:input-trace-api:1.0.1"

    // pyramid 组件化框架
    implementation "com.baidu.pyramid:pyramid-annotation:0.1.9"
    annotationProcessor "com.baidu.coco:coco-apt:${COCO_APT_VERSION}"
}
