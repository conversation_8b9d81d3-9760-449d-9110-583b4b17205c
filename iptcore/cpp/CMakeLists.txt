cmake_minimum_required(VERSION 3.4.1)

SET(LOCAL_PATH $(call my-dir))
SET(PROJECT_NAME iptcore_static)

project(${PROJECT_NAME})

SET(SRCDIR /Users/<USER>/BaiduCode/baidu/baiduinput/iptcore/source)
SET(LIBDIR ${PROJECT_SOURCE_DIR}/../jni/lib)
#SET(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${PROJECT_SOURCE_DIR}/../jni/lib_debug/)
SET(NONEBUIDDIR ${SRCDIR}/libtool*
                ${SRCDIR}/wordseg* 
                ${SRCDIR}/_objs*
                ${SRCDIR}/znpos*
                ${SRCDIR}/pc_input*
                )
FILE(GLOB_RECURSE SOURCES ${SRCDIR}/*/*.c ${SRCDIR}/*/*.cc ${SRCDIR}/*/*.cpp ${LOCAL_PATH}/*.cpp)
FOREACH(EXC_DIR ${NONEBUIDDIR})
        LIST(FILTER SOURCES EXCLUDE REGEX ${EXC_DIR})
ENDFOREACH(EXC_DIR)

FOREACH(SRC ${SOURCES})
        MESSAGE(STATUS "${SRC}")
ENDFOREACH(SRC)

SET(INCLUDES ${SRCDIR}/_pub
        ${SRCDIR}/bezier
        ${SRCDIR}/ch_dict
        ${SRCDIR}/ch_main
        ${SRCDIR}/ch_misc
        ${SRCDIR}/ch_py
        ${SRCDIR}/chiper
        ${SRCDIR}/cloud
        ${SRCDIR}/def
        ${SRCDIR}/en_base
        ${SRCDIR}/gzip
        ${SRCDIR}/handwt
        ${SRCDIR}/inputpad
        ${SRCDIR}/inputpad/cand
        ${SRCDIR}/inputpad/input_show
        ${SRCDIR}/keypad
        ${SRCDIR}/libc
        ${SRCDIR}/net
        ${SRCDIR}/old
        ${SRCDIR}/ot_misc
        ${SRCDIR}/tinystl
        ${SRCDIR}/chiper
        ${SRCDIR}/dict_cz3
        ${SRCDIR}/dict_py3
        ${SRCDIR}/dict_usr3
        ${SRCDIR}/voice_correct
        ${SRCDIR}/en_input
        ${SRCDIR}/third-party
        ${SRCDIR}/slide
        ${SRCDIR}/iec3
        )

SET(DEFINES
        -DPLATFORM_ANDROID
        -DIPT_PLATFORM_RVDS_ANDROID
        -DIPT_FEATURE_NN_SLIDE_PY26
        -DUSE_PADDLE
        -DFEATURE_SEARCH
        -DIPT_FEATURE_FREE_HW
        -DCRASH_VERSION
        -DIPT_MMAP
        -DIPT_BAYESIAN_SYM_LIAN
        -DFEATURE_PAD_RETURN
        -DIPT_GAUSS
        -D__ARM_NEON
        -DIPT_CONFIG_DEBUG_MODE )

ADD_DEFINITIONS(${DEFINES})
IF(${ANDROID_ABI} STREQUAL "arm64-v8a")
ELSE()
        ADD_DEFINITIONS("-DTSTL_CPU_X32")
ENDIF()

INCLUDE_DIRECTORIES(${INCLUDES})

ADD_LIBRARY(dnnneon STATIC IMPORTED)
SET_TARGET_PROPERTIES(dnnneon PROPERTIES IMPORTED_LOCATION  ${LIBDIR}/dnnneon.a)
MESSAGE(STATUS "Required library 'dnnneion' path: ${LIBDIR}/dnnneon.a")
#MESSAGE(STATUS "CMAKE_LIBRARY_OUTPUT_DIRECTORY: ${CMAKE_LIBRARY_OUTPUT_DIRECTORY}")
ADD_LIBRARY(${PROJECT_NAME}
        STATIC
        ${SOURCES}
        )
TARGET_LINK_LIBRARIES(${PROJECT_NAME} dnnneon)
