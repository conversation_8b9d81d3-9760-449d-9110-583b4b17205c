LOCAL_PATH := $(call my-dir)

MY_LIB_DIR := lib64
MY_LIB_CPU := DTSTL_CPU_X64
ifeq ($(TARGET_ARCH_ABI), arm64-v8a)
MY_LIB_DIR := lib64
MY_LIB_CPU := DTSTL_CPU_X64
else
MY_LIB_DIR := lib
MY_LIB_CPU := DTSTL_CPU_X32
endif

include $(CLEAR_VARS)
LOCAL_MODULE := ime_core
LOCAL_SRC_FILES := $(MY_LIB_DIR)/libiptcore.a
include $(PREBUILT_STATIC_LIBRARY)


# include $(CLEAR_VARS)
# LOCAL_MODULE := ime_snappy
# LOCAL_SRC_FILES := $(MY_LIB_DIR)/libsnappy.a
# include $(PREBUILT_STATIC_LIBRARY)

include $(CLEAR_VARS)
LOCAL_MODULE := ime_libocr
LOCAL_SRC_FILES := $(MY_LIB_DIR)/libocr.so
include $(PREBUILT_SHARED_LIBRARY)

include $(CLEAR_VARS)
LOCAL_MODULE := ime_libBaiduOcrSDK
LOCAL_SRC_FILES := $(MY_LIB_DIR)/libBaiduOcrSDK.so
include $(PREBUILT_SHARED_LIBRARY)

# include $(CLEAR_VARS)
# LOCAL_MODULE := ime_paddle
# LOCAL_SRC_FILES := $(MY_LIB_DIR)/libpaddle_light_api_shared.so
# include $(PREBUILT_SHARED_LIBRARY)

# ifeq ($(TARGET_ARCH_ABI), arm64-v8a)
# # arm64-v8a no ime_nnrank and npu_hiai
# APP_ALLOW_MISSING_DEPS=true
# else
# include $(CLEAR_VARS)
# LOCAL_MODULE := ime_nnrank
# LOCAL_SRC_FILES := $(MY_LIB_DIR)/libnn-rank.so
# include $(PREBUILT_SHARED_LIBRARY)
#
# include $(CLEAR_VARS)
# LOCAL_MODULE := npu_hiai
# LOCAL_SRC_FILES := $(MY_LIB_DIR)/libhiai.so
# include $(PREBUILT_SHARED_LIBRARY)
# endif

# include $(CLEAR_VARS)
# LOCAL_MODULE := lib_neon
# LOCAL_SRC_FILES := $(MY_LIB_DIR)/dnnneon.a
# include $(PREBUILT_STATIC_LIBRARY)

# include $(CLEAR_VARS)
# LOCAL_MODULE := lib_pb
# LOCAL_SRC_FILES := $(MY_LIB_DIR)/libprotobuf-lite.a
# include $(PREBUILT_STATIC_LIBRARY)

include $(CLEAR_VARS)
LOCAL_MODULE_TAGS := optional

LOCAL_MODULE    := iptcore
LOCAL_SRC_FILES := 	ipt_jni_env.cpp \
					ipt_jni_gobal.cpp \
					ipt_jni_native_main.cpp \
					ipt_jni_native_config.cpp \
					ipt_jni_native_config_pad.cpp \
					ipt_jni_main.cpp \
					ipt_jni_config_callback.cpp \
					ipt_jni_net_man.cpp \
					md5.cpp \
					utility.cpp \
					jni_register.cpp \
					ipt_jni_encrypt.cpp \
#宏定义
DEFINES  = -DPLATFORM_ANDROID \
	       -$(MY_LIB_CPU) \
           -DIPT_PLATFORM_RVDS_ANDROID \
		   -DIPT_FEATURE_NN_SLIDE_PY26 \
		   -DUSE_PADDLE \
           -DFEATURE_SEARCH \
	       -DIPT_FEATURE_FREE_HW \
           -DCRASH_VERSION \
           -DIPT_BAYESIAN_SYM_LIAN \
           -DFEATURE_PAD_RETURN \
		   -DIPT_GAUSS \
		   #-DIPT_MMAP \

#c和c++编译选项
CFLAGS   = -O3 -mfloat-abi=softfp -mfpu=neon -fsigned-char
CPPFLAGS = -O3 -fsigned-char -fvisibility=hidden

LOCAL_CFLAGS     := $(CFLAGS) $(DEFINES)
LOCAL_CPPFLAGS   := $(CPPFLAGS) $(DEFINES)

LOCAL_DISABLE_FATAL_LINKER_WARNINGS := true
LOCAL_LDFLAGS := -s -Wl,--no-fatal-warnings,--gc-sections

LOCAL_STATIC_LIBRARIES := ime_core
# LOCAL_SHARED_LIBRARIES := ime_mlm ime_paddle
LOCAL_SHARED_LIBRARIES := ime_libocr ime_libBaiduOcrSDK

LOCAL_LDLIBS    := -lm -llog

LOCAL_C_INCLUDES := \
		    $(call include-path-for)

LOCAL_PRELINK_MODULE := false

include $(BUILD_SHARED_LIBRARY)

