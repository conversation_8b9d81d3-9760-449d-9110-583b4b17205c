/// Created on: 2022-04-08 ///
/// Author: men<PERSON><PERSON><PERSON> ///
/// 内核加密库-对外封装的接口 ///

#pragma once
///
using Uint8 = unsigned char;
using Int32 = signed int;
using Uint32 = unsigned int;
#ifndef CYP_SHOW_FUNC_NAME
#ifndef cyp
#define cyp ZB00
#endif
#define WrpAESHeader ZB01
#define WrpRSAHeader ZB02
#define wrp_b64_buffer_need ZB03
#define wrp_b64_encode ZB04
#define wrp_b64_decode ZB05
#define wrp_aes_buffer_need ZB06
#define wrp_aes_encrypt ZB07
#define wrp_aes_encrypt_v2 ZB08
#define wrp_aes_decrypt ZB09
#define wrp_aes_b64_buffer_need ZB10
#define wrp_aes_b64_encrypt ZB11
#define wrp_aes_b64_encrypt_v2 ZB12
#define wrp_aes_b64_decrypt ZB13
#define wrp_rsa_buffer_need ZB14
#define wrp_rsa_pub_encrypt_bydk ZB15
#define wrp_rsa_pub_decrypt_bydk ZB16
#endif
namespace cyp {
#pragma pack (push, 4)
///
struct WrpAESHeader { /// 20字节 ///
    Uint32 content_len;
    Uint8 userkey[16];///(输入时可填充随机数!!!)
};
///
struct WrpRSAHeader { /// 32字节 ///
    Uint32 content_len;
    Uint32 reserved[7];///(输入时可填充随机数!!!)
};

struct WrpECCHeader { ///40 字节头 ///
    Uint32 content_len; /// 内容长度 ///
    Uint32 crc32; /// CRC校验(明文数据区) ///
    Uint32 cli_pk[8]; /// 公钥(一次一密) ///
};

struct WrpBlockHeader {
    Uint32 block_len : 28;/// 表示数据块长度(最大256MB) ///
    Uint32 block_type : 4;/// 表示加密类型(0~15) : 0:RSA1024; 1: Curve25519
};

///
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/// base64 编码 解码////////////////////////////////////////////////////////////////////////////////////////////////
///
/// 获取Base64编码所需空间
/// @param input_size — 待编码数据的原始大小
/// @return Base64编码所需空间; 若返回值<0, 说明输入参数有误
Int32 wrp_b64_buffer_need(Uint32 input_size);
///
/// 使用b64对指定数据进行编码
/// @param buffer - 待编码的数据，执行本函数后将存放编码后的数据，需分配足够内存(通过wrp_b64_buffer_need获取)
/// @param input_size — 待编码数据的原始大小
/// @param seeds — 用于混淆的随机数种子
/// @return >0时表示编码后数据的长度;<0表示输入参数有误
Int32 wrp_b64_encode(Uint8* buffer, Uint32 input_size, Uint32 seeds);
///
/// 使用b64对指定数据进行解码
/// @param buffer - 待解码的数据，执行本函数后将存放解码后的数据
/// @param buffer_len — 待解码数据的长度
/// @param seeds — 加密时使用的随机数种子
/// @return >0时表示解码后数据的长度;<0表示输入参数有误
Int32 wrp_b64_decode(Uint8* buffer, Uint32 seeds);
///
////////////////////////////////////////////////////////////
/////AES 加密 解密////////////////////////////////////////
///
/// 获取使用AES加密所需空间(content_len 不得超过16MB)
/// @param content_len — 待编码数据的原始大小
/// @return >0表示使用AES加密所需空间;<0表示输入参数有误
Int32 wrp_aes_buffer_need(Uint32 content_len);
///
/// 使用AES对指定数据进行加密(第一种加密方式 + 第二种加密方式)
/// @param buffer - 待加密的数据(前20个字节为头文件, 格式参照WrpAESHeader, 后面为实际数据), 执行本函数后将存放加密后的数据, 需分配足够内存(通过wrp_aes_buffer_need获取)
/// @param buffer_len — 待加密数据的大小(通过wrp_aes_buffer_need获取)
/// @return =0时表示加密成功;<0表示输入参数有误
Int32 wrp_aes_encrypt(Uint8* buffer, Uint32 buffer_len);
Int32 wrp_aes_encrypt_v2(Uint8* buffer, Uint32 buffer_len);
///
/// 使用AES对指定数据进行解密(注意::只有提供给服务端的版本可以解密第二种方式)
/// @param buffer - 待解密的数据
/// @param buffer_len — 待解密数据的大小
/// @return =0时表示解密成功, 且采用第一种方式加密;(=1时表示解密成功, 且采用第二种方式加密,只有提供给服务端的版本可以解密第二种方式);<0表示输入参数有误
Int32 wrp_aes_decrypt(Uint8* buffer, Uint32 buffer_len);
///
/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/////AES_B64(相当于AES+B64(seed=0) 加密 解密////////////////////////////////////////////////////////////
///
/// 获取使用AESB64加密所需空间
/// @param content_len — 待加密数据的原始大小
/// @return >0表示使用AESB64加密所需空间;<0表示输入参数有误
Int32 wrp_aes_b64_buffer_need(Uint32 content_len);
///
/// 使用AESB64对指定数据进行加密(第一种加密方式+第二种加密方式,这种加密方式只有服务端可以解密)
/// @param buffer - 待加密的数据(前20个字节为头文件, 格式参照WrpAESHeader, 后面为实际数据), 执行本函数后将存放加密后的数据, 需分配足够内存(通过wrp_aes_b64_buffer_need获取)
/// @param buffer_len — 待加密数据的大小(通过wrp_aes_b64_buffer_need获取)
/// @return =0时表示加密成功;<0表示输入参数有误
Int32 wrp_aes_b64_encrypt(Uint8* buffer, Uint32 buffer_len);
Int32 wrp_aes_b64_encrypt_v2(Uint8* buffer, Uint32 buffer_len);
///
/// 使用AESB64对指定数据进行解密(注意::只有提供给服务端的版本可以解密第二种方式)
/// @param _iptchiper — 加密/解密工具句柄
/// @param buffer - 待解密的数据，执行本函数后将存放解密后的数据
/// @return =0时表示解密成功, 且采用第一种方式加密;(=1时表示解密成功, 且采用第二种方式加密,只有提供给服务端的版本可以解密第二种方式);<0表示输入参数有误
Int32 wrp_aes_b64_decrypt(Uint8* buffer);
///
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////RSA加密//////////////////////////////////////////////////////////////////////////////////////////////////////
///
/// 获取使用RSA加密所需空间
/// @param content_len — 待加密数据的原始大小(一次最多加密16777215 字节数据(约16M))
/// @return >0表示使用RSA加密所需空间;<0表示输入参数有误
Int32 wrp_rsa_buffer_need(Uint32 content_len);
///
/// 使用内置公钥RSA对指定数据进行加密(这个功能一般用于客户端向服务器发送加密内容)
/// @param buffer — 待加密的数据(前32个字节为头文件, 格式参照WrpRSAHeader, 后面为实际数据), 执行本函数后将存放加密后的数据, 需分配足够内存(通过wrp_rsa_buffer_need获取)
/// @param buffer_len — 待加密数据的大小(通过wrp_rsa_buffer_need获取)
/// @return =0时表示加密成功;<0表示输入参数有误
Int32 wrp_rsa_pub_encrypt_bydk(Uint8* buffer, Uint32 buffer_len);
///
/// 使用内置公钥RSA对指定数据进行解密(这个功能一般用来对签名内容做验证)
/// @param buffer — 待解密的数据,解密后的格式参考 WrpRSAHeader 结构体
/// @param buffer_len — 待解密数据的大小
/// @return =0时表示解密成功;<0表示输入参数有误
Int32 wrp_rsa_pub_decrypt_bydk(Uint8* buffer, Uint32 buffer_len);
///
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////ECC加密//////////////////////////////////////////////////////////////////////////////////////////////////////
///
/// 获取使用ECC加密所需空间
/// @param content_len — 待加密数据的原始大小(一次最多加密128M))
/// @return >0表示使用ECC加密所需空间;<0表示输入参数有误
Int32 wrp_ecc_buffer_need(Uint32 content_len);
///
/// 使用内置公钥ECC对指定数据进行加密(这个功能一般用于客户端向服务器发送加密内容)
/// @param buffer — 待加密的数据(前32个字节为头文件, 格式参照WrpECCHeader, 后面为实际数据), 执行本函数后将存放加密后的数据, 需分配足够内存(通过wrp_ecc_buffer_need获取)
/// @param buffer_len — 待加密数据的大小(通过wrp_ecc_buffer_need获取)
/// @return =0时表示加密成功;<0表示输入参数有误
Int32 wrp_ecc_encrypt_bydk(Uint8* buffer, Uint32 buffer_len);
///
///
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////ECC加密-用于皮肤加解密专用/////////////////////////////////////////////////////////////////////////////////////
/// 获取使用ECC加密所需空间
/// @param content_len — 待加密数据的原始大小(一次最多加密128M))
/// @return >0表示使用ECC加密所需空间;<0表示输入参数有误
Int32 wrp_bds_buffer_need(Uint32 content_len);
///
/// 使用内置公钥ECC对指定数据进行加密(这个功能一般用于服务端加密付费皮肤文件)
/// @param buffer — 待加密的数据(前40个字节为头文件, 格式参照WrpECCHeader, 后面为实际数据), 执行本函数后将存放加密后的数据, 需分配足够内存(通过wrp_ecc_buffer_need获取)
/// @param buffer_len — 待加密数据的大小(通过wrp_ecc_buffer_need获取)
/// @return =0时表示加密成功;<0表示输入参数有误
Int32 wrp_bds_encrypt_bydk(Uint8* buffer, Uint32 buffer_len);
////
/// 使用内置公钥RSA对指定数据进行解密(这个功能一般用来对服务端下发的付费皮肤文件解密)
/// @param buffer — 待解密的数据,解密后的格式参考 WrpECCHeader 结构体
/// @param buffer_len — 待解密数据的大小
/// @return =0时表示解密成功;<0表示输入参数有误
Int32 wrp_bds_decrypt_bydk(Uint8* buffer, Uint32 buffer_len);

#pragma pack(pop)
}
