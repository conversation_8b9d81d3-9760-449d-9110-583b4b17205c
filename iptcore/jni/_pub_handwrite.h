/// @file     _pub_handwrite.h
/// @brief    手写相关接口
/// <AUTHOR>
/// @date     2021-07-29
/// @version  *******
/// @par Copyright (c):  Baidu

#pragma once
#include "_pub_iptpad.h"

namespace hw_interface {

#pragma pack(push, 4)
/// 手写识别结果 ///
struct HwResItem {

    Uint16 code;     ///汉字的unicode编码
    Uint16 reserved; ///对齐预留使用
    Uint32 pr;       ///概率值,其值为:log(模型打分的概率)*-1000,注意模型打分的概率可能很小很小
};
#pragma pack(pop)

/// 单字手写识别接口 ///
class HwInterface {

public:
    /// 创建手写识别接口 ///
    static HwInterface* create();
    /// 关闭手写识别接口 ///
    static void close(HwInterface* hw_interface);
    /// 手写识别版本号 ///
    static Uint32 version();

public:
    virtual ~HwInterface() {};

public:
    /// 加载模型 ///
    /// @return false表示加载失败，否则成功 ///
    virtual bool load_model(const char* model_path, const char* license_path,
                            void* jni_vm = nullptr, void* jni_env = nullptr, void* context = nullptr) = 0;
    /// 卸载模型 ///
    virtual void unload_model() = 0;
    /// 模型版本号 ///
    /// @return 返回模型版本号, 返回0时表示获取失败 ///
    virtual Uint32 model_version() const = 0;

public:
    /// 识别轨迹点(单字模式)
    /// @out_res 输出结果
    /// @max_out_num 最大输出结果
    /// @points 输入轨迹点，每笔以65535,0分割，最终以65535,65535结尾
    /// @return 输出结果的数量
    virtual Uint32 reco_points(HwResItem* out_res, Uint32 max_out_num, const Uint16* points) = 0; 

};

}