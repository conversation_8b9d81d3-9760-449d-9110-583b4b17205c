///  Created on: 2011-11-9  ///
/// Author: <PERSON><PERSON><PERSON><PERSON>   ///
/// 函数名编号:DA(01 - 99)   ///

#pragma once
/// 屏蔽apple平台clang的编译warning
/// 备注：IPT_PLATFORM_MACOS可能不太规范
#if defined(PLATFORM_IOS) || defined(IPT_PLATFORM_MACOS)
#ifndef CORE_EVAL_DEBUG
#pragma clang diagnostic ignored "-Weverything"
#endif
#endif
#include "_pub_version.h"

using PLUINT32 = unsigned int;
using PLUINT16 = unsigned short;
using PLBYTE = unsigned char;
using PLUINT8 = unsigned char;
using PLINT32 = signed int;
using PLINT16 = signed short;
using PLINT8 = signed char;
using PLINT64 = long long;
using PLUINT64 = unsigned long long;
using PLREAL32 = float;
using PLREAL64 = double;

#define MAX_PY_CORRECT_CNT  (8)
#define MAX_CAND_LEN        (64)
#define IPT_SYM_MAX_LEN (128)

///////////////////////////////////输入码错误标记////////////////////////////
////////////////////////////////用于ipt_query_get_iec_tip的参数iec_info////////////////////////////
//#define PY_IEC_FLAG_TE                  (0x1) ///<输错了
//#define PY_IEC_FLAG_LE                  (0x2) ///<少输了，当前位置之前少输了一个字符
//#define PY_IEC_FLAG_ME                  (0x4) ///<多输了
//#define PY_IEC_FLAG_SE                  (0x8) /**<交换输入 */
//#define PY_IEC_FLAG_POS                 (0x10) /**<少输了，当前位置之后少输了一个字符 */
///////////////////////////////////输入码操作标记INPUT_FLAG结束////////////////////////////


//////////////////////////////////手写查询范围HW_FIND_RANGE/////////////////////////
//////////////////////////////////用于手写查询范围设置
#define HW_FIND_RANGE_CH_COMMON                 (0x01) ///<常用字
#define HW_FIND_RANGE_CH_RARE                   (0x02) /**<生僻字  */
#define HW_FIND_RANGE_CH_RADICAL                (0x04) /**<偏旁部首  */

#define HW_FIND_RANGE_NUM                       (0x08) ///<数字
#define HW_FIND_RANGE_EN_LOWER                  (0x10) /**<英文小写  */
#define HW_FIND_RANGE_EN_UPPER                  (0x20) /**<英文大写  */

#define HW_FIND_RANGE_PUN_COMMON                (0x40) ///<常用标点
#define HW_FIND_RANGE_PUN_EXT                   (0x80) /**<扩展标点  */

#define HW_FIND_RANGE_SYM_COMMON                (0x0100) ///<常用符号
#define HW_FIND_RANGE_SYM_EXT                   (0x0200) /**<扩展符号  */
#define HW_FIND_RANGE_SYM_RARE_G1               (0x0400) /**<特殊符号1  */
#define HW_FIND_RANGE_SYM_RARE_G2               (0x0800) /**<特殊符号2  */
#define HW_FIND_RANGE_SYM_RARE_G3               (0x1000) /**<特殊符号3  */

#define HW_FIND_RANGE_CH_NM                     (HW_FIND_RANGE_CH_COMMON | HW_FIND_RANGE_CH_RARE) ///<不包括偏旁部首的汉字
#define HW_FIND_RANGE_EN                        (HW_FIND_RANGE_EN_LOWER | HW_FIND_RANGE_EN_UPPER) /**<英文  */
#define HW_FIND_RANGE_PUN                       (HW_FIND_RANGE_PUN_COMMON | HW_FIND_RANGE_PUN_EXT) /**<标点  */
#define HW_FIND_RANGE_SYM                       (HW_FIND_RANGE_SYM_COMMON \
                                                  | HW_FIND_RANGE_SYM_EXT \
                                                  | HW_FIND_RANGE_SYM_RARE_G1 \
                                                  | HW_FIND_RANGE_SYM_RARE_G2 \
                                                  | HW_FIND_RANGE_SYM_RARE_G3) /**<符号  */

#define HW_FIND_RANGE_CH_ALL                    (HW_FIND_RANGE_CH_NM | HW_FIND_RANGE_CH_RADICAL) /**<所有汉字  */
#define HW_FIND_RANGE_CH_RARE_RADICAL           (HW_FIND_RANGE_CH_RARE | HW_FIND_RANGE_CH_RADICAL) /**<生僻字+偏旁部首  */
#define HW_FIND_RANGE_EN_NUM                    (HW_FIND_RANGE_EN | HW_FIND_RANGE_NUM) /**<英文+数字  */
#define HW_FIND_RANGE_PUN_SYM                   (HW_FIND_RANGE_PUN | HW_FIND_RANGE_SYM) /**<标点+符号  */

#define HW_FIND_RANGE_ALL                       (HW_FIND_RANGE_CH_ALL | HW_FIND_RANGE_EN_NUM | HW_FIND_RANGE_PUN_SYM) /**<所有范围  */

//////////////////////////////////手写查询范围HW_FIND_RANGE结束/////////////////////////

/////////////////框属性////////////////
//#define ATTRIBUTE_OTHER     (0x1) //未知属性//
//#define ATTRIBUTE_SEARCH    (0x2) //搜索框属性//
//#define ATTRIBUTE_WEB       (0x4) //网址框属性//
//#define ATTRIBUTE_EMAIL     (0x8) //邮件框属性//
//#define ATTRIBUTE_NUM     (0x10) //数字框属性//
//#define ATTRIBUTE_PASSWORD     (0x20) //密码框属性//
//#define ATTRIBUTE_DIAL     (0x40) //电话框属性//

enum ot_idp_type {
    OT_IDP_TYPE_EMOTICON = 0x1000000, //id映射中的颜文字类型(unicode字符)
    OT_IDP_TYPE_EMOJI = 0x2000000 //id映射中的颜文字类型(二进制流)
};

///英文查询结果的排序方式, 用于s_ipt_config结构体中的en_ensort变量
enum EN_SORT {
    ENSORT_BYFREQ = 0, ///<按英文词的频率排序
    ENSORT_BYLEN, ///<按英文词的长度排序
    ENSORT_BYABC, ///<按英文词的字母顺序排序
};

///中英混排方式, 用于s_ipt_config结构体中的ch_cnen变量
enum CNEN_TOGGLE {
    CNEN_OFF = 0, ///<关闭中英混排
    CNEN_ON = 1, ///<开启中英混排
    CNEN_ONLY_MUTI_CHAR = 2, ///<开启中英混排, 但屏蔽单个英文字母
};

///英文查询结果中大小写状态, 用于s_ipt_config结构体中的en_encase变量
enum EN_CASE {
    ENCASE_NORMAL = 0, ///<普通状态
    ENCASE_FIRST, ///<首字母大写状态
    ENCASE_ALL ///<全部字母大写状态
};

///输入法查询选项, 用于ipt_query_find等函数
enum FIND_TYPE {
    FINDTYPE_NULL = 0,
    FINDTYPE_PY = 1, ///<中文查询
    FINDTYPE_EN = 2, ///<英文查询
    FINDTYPE_BH = 3, ///<笔画查询
    FINDTYPE_WB = 4, ///<五笔查询
    FINDTYPE_DEF = 5, ///<自定义查询
    FINDTYPE_SYM = 6, ///<符号查询
    FINDTYPE_LIAN = 7, ///<联想查询
    FINDTYPE_FORM = 8, ///<邮箱网址查询
    FINDTYPE_HW = 9,  ///<手写查询
    FINDTYPE_ZY = 10, ///<注音输入法查询
    FINDTYPE_CANGJIE = 11, ///<仓颉输入法查询
    FINDTYPE_EN_URL = 12,///<英文面板下查询URL符号
    FINDTYPE_RARE_CHAIZI = 13, ///<生僻字部首拼音拆字查询
    FINDTYPE_COUNT ///<查询类型枚举值个数
    //
    /// 这两张查找模式不再有用 //
    ////FINDTYPE_PY_EDIT = 12, ///<在编辑区上进行的中文查询
    ////FINDTYPE_LIAN_CURSOR = 14, ///移动关标联想 不再使用
};

enum class FIND_PROPERTY : PLUINT32 {
    FIND_PROPERTY_NORMAL = 0x0,
    FIND_PROPERTY_DEL_LIAN, ///配合FIND_LIAN
    FIND_PROPERTY_EMAIL_LIAN, ///配合FIND_LIAN
    FIND_PROPERTY_EMAIL_LIAN_IN_EMAIL_BOX, ///配合FIND_LIAN
    FIND_PROPERTY_EN_SPACE, ///英文取前文空格联想，配合FIND_LIAN
};

//  FINDTYPE_PHRASE,//个性短语查询

///自造词查询选项, 用于ipt_usword_count等函数
//enum USERWORD_TYPE {
//    USERWORD_CH1 = 1, ///<汉字
//    USERWORD_CH2 = 2, ///<二字词
//    USERWORD_CH3 = 3, ///<三字词
//    USERWORD_CH4 = 4, ///<四字词
//    USERWORD_CH5 = 5, ///<五字词
//    USERWORD_CH6 = 6, ///<六字词
//    USERWORD_CH7 = 7, ///<七字词
//    USERWORD_CH8 = 8, /**< 八字词 */
//    USERWORD_MORE = 9, /**< 八字以上词 */
//    ///////////////////////////
//    USERWORD_EN = 20, ///<英文词
//    USERWORD_ALL = 30, /**< 所有中文词 */
//    ///////////////////////////
//    USERWORD_LAST_SIGNIGIE ///<自造词查询选项枚举值个数
//};

///查询候选项目的类型, 用于ipt_query_get_count, ipt_query_get_str函数
//enum GET_TYPE {
//    /////////////////////////////////////////////////////////////
//    //GETTYPE_CAND = 0, ///<普通候选词
//    //GETTYPE_LIST = 1, ///<拼音列表(或笔画筛选等)
//    //GETTYPE_HOTSYM = 2, ///<常用符号(目前不支持)
//    //GETTYPE_CAND_ORG = 3, ///<普通候选词原始形态(无繁简转换或英文大小写转换)
//    //GETTYPE_CAND_WB_TIP = 4, /**<中文候选词(带五笔编码提示) */
//    //GETTYPE_PY_LIST = 5, ///<拼音列表
//    //GETTYPE_BH_LIST = 6, /**笔画筛选列表 */
//    //GETTYPE_COMMIT_CAND = 7, ///<上屏候选词
//    GETTYPE_TOP_PROMOTION = 8, ///<PC六号位候选词
//    ///////////////////////////////////////////////////////////
//    GETTYPE_enum_count, ///<查询结果类型枚举值个数
//};

/////网址邮箱符号编辑选项
//enum FORM_EDIT_OPTION {
//    /////////////////////////////////////////////////////////////
//    FORM_EDIT = 1, ///<编辑
//    FORM_DEL = 2, ///<删除
//    FORM_ADD = 3, /**< 添加 */
//    ///////////////////////////////////////////////////////////
//    FORM_EDIT_cnt, /**< 网址邮箱符号编辑选项枚举值个数 */
///////////////////////////////
//};///////////////////////////////////////////////////////////


///个性短语编辑选项, 用于ipt_phrase_group_edit, ipt_phrase_item_edit等函数
//enum PHRASE_EDIT_OPTION {
//    /////////////////////////////////////////////////////////////
//    PHRASE_EDIT = 1,///<编辑
//    PHRASE_DEL = 2,///<删除
//    PHRASE_ADD = 3, ///<添加
//    PHRASE_REMOVE = 4, ///<移除(批量删除)
//    ///////////////////////////////////////////////////////////
//    PHRASE_EDIT_cnt, /**< 个性短语编辑选项个数 */
///////////////////////////////
//};///////////////////////////////////////////////////////////


///同步类型(词库同步操作时使用)
enum SYNC_FLAG {
    SYNC_FLAG_CH = 1, ///<中文词
    SYNC_FLAG_EN = 2, ///<英文词
    SYNC_FLAG_UN = 3  ///<其他符号
};

///同步选项(词库同步操作时使用)
enum SYNC_OPTION {
    SYNC_OPTION_PULL = 1, ///<增加词频
    SYNC_OPTION_DEL = 2,  ///<删除词
    SYNC_OPTION_REPULL = 3  ///<删除词后重新添加
};

///手写输入选项(手写输入时使用)
enum HW_INPUT_TYPE {
    HW_INPUT_TYPE_HZ = 0x1, //<单字输入
    HW_INPUT_TYPE_REDUP = 0x2, //<叠写输入
    HW_INPUT_TYPE_NM = 0x4, //<连写输入
    HW_INPUT_TYPE_EN_NUM = 0x8  //<英文数字输入
};

///场景化列表
enum CONTEXT_ID {
    NO_CONTEXT_ID = 0,  ///没有场景化
    CONTEXT_ID_MERCH = 0x1, ///电商场景
    CONTEXT_ID_APP = 0x2, ///应用市场场景
    CONTEXT_ID_LBS = 0x4,  ///地理位置场景
    CONTEXT_ID_MEDIA = 0x8, ///多媒体场景，视频
    CONTEXT_ID_GROUPON = 0x10, ///点评团购场景
    CONTEXT_ID_BROWSER = 0x20, ///浏览器场景
    CONTEXT_ID_SNS = 0x40, ///聊天软件场景
    CONTEXT_ID_MUSIC = 0x80, ///音乐类(QQ音乐、酷狗等)
    CONTEXT_ID_BILI = 0x100, ///B站类（bilibili）
    CONTEXT_ID_TIEBA = 0x200, ///贴吧论坛类(百度贴吧)
    CONTEXT_ID_SOCIAL = 0x400, ///交友类(陌陌)
    CONTEXT_ID_WZRY = 0x800, ///王者荣耀//
    CONTEXT_ID_JDQS = 0x1000, ///绝地求生//
    CONTEXT_ID_QQFC = 0x2000, ///qq飞车//
    CONTEXT_ID_MNSJ = 0x4000, ///迷你世界//
    CONTEXT_ID_LIVE = 0x8000, ///直播//
    CONTEXT_ID_XXQG = 0x10000, ///学习强国//
    CONTEXT_ID_XS = 0x20000, ///小说//
    CONTEXT_ID_YL = 0x40000, ///医疗//
    CONTEXT_ID_WB = 0x80000, ///微博//
    CONTEXT_ID_ES = 0x100000, ///二手//
};

enum AUTOREPLY_INTENT {
    AUTOREPLY_INTENT_NAME = 1,  ///名字识别
    AUTOREPLY_INTENT_MOBILE = 2, ///手机号
    AUTOREPLY_INTENT_ADDRESS = 3, ///地址
    AUTOREPLY_INTENT_QQ = 4,  ///QQ
    AUTOREPLY_INTENT_WEIXIN = 5, ///微信
    AUTOREPLY_INTENT_BIRTH = 6, ///生日
    AUTOREPLY_INTENT_IDNUMBER = 7, ///身份证
    AUTOREPLY_INTENT_EMAIL = 8, ///电子邮箱
    AUTOREPLY_INTENT_TAX_ID = 9, ///税号
    AUTOREPLY_INTENT_STRICT_ADDRESS = 10, ///严格的地址
};

enum KEY_RECT_CN26_NAME {
    KEY_RECT_A = 1,
    KEY_RECT_B = 2,
    KEY_RECT_C = 3,
    KEY_RECT_D = 4,
    KEY_RECT_E = 5,
    KEY_RECT_F = 6,
    KEY_RECT_G = 7,
    KEY_RECT_H = 8,
    KEY_RECT_I = 9,
    KEY_RECT_J = 10,
    KEY_RECT_K = 11,
    KEY_RECT_L = 12,
    KEY_RECT_M = 13,
    KEY_RECT_N = 14,
    KEY_RECT_O = 15,
    KEY_RECT_P = 16,
    KEY_RECT_Q = 17,
    KEY_RECT_R = 18,
    KEY_RECT_S = 19,
    KEY_RECT_T = 20,
    KEY_RECT_U = 21,
    KEY_RECT_V = 22,
    KEY_RECT_W = 23,
    KEY_RECT_X = 24,
    KEY_RECT_Y = 25,
    KEY_RECT_Z = 26,
    KEY_RECT_SHIFT = 27, //shift键
    KEY_RECT_DEL = 28, ///delete键
    KEY_RECT_NUM = 29, ///数字面板切换键
    KEY_RECT_CHEN = 30, ///中英文切换键
    KEY_RECT_COMMA = 31, //逗号
    KEY_RECT_SPACE = 32, //空格
    KEY_RECT_STOP = 33, ///句号
    KEY_RECT_SYM = 34, ///符号面板切换键
    KEY_RECT_ENTER = 35, ///回车
    KEY_RECT_EMOJI = 36, /// emoji键
    KEY_RECT_MAX = 36, /// 最大值，新增按键时，需要同时调整
    KEY_RECT_SPLIT = 39, /// 分词符"'"，已被使用，目前和KEY_RECT_SHIFT相同
};
///详见Layout::key_char2idx
static_assert(KEY_RECT_MAX < 48, "KEY_RECT_MAX must less than '0'(48)");

enum PHONE_STATE_LIST {
    PHONE_STATE_PORTRAIT = 0, //竖屏
    PHONE_STATE_LANDSCAPE = 1 //横屏
};

//这个列表用来兼容升级用户用的, 原先用户的列表是否锁定是记录在客户端的
//最近-锁定
//中文-不锁定
//英文-锁定
//..
//升级安装客户端要导入这个设置
//应为是为了兼容旧数据, 因此新增的符号项不需要修改这个表
enum SYM_CATE {
    SYM_CATE_RECENT,    //最近
    SYM_CATE_CN,        //中文
    SYM_CATE_EN,        //英文
    SYM_CATE_EMOJI,     //表情
    SYM_CATE_NET,       //网络
    SYM_CATE_SPECIAL,   //特殊
    SYM_CATE_MATH,      //数学
    SYM_CATE_NUMBER,    //序号
    SYM_CATE_RUSSIA,    //希俄
    SYM_CATE_ARROW,     //箭头
    SYM_CATE_PINGJIA,   //平假名
    SYM_CATE_PIANJIA,   //片假名
    SYM_CATE_ZY,        //注音
    SYM_CATE_BUSHOU,    //部首
    SYM_CATE_TAB,       //制表
    SYM_CATE_MAX
    //Never add, just from compable, @songliantao
} ;

///////////////////////////////////////////////////////////////////////////////////////////////////
struct s_Point_v2 {
    ////笔迹点,结构体(16位)
    PLUINT16 x;
    PLUINT16 y;
};

struct s_Rect_v2 {
    ////笔迹点,结构体(8位)
    s_Point_v2 TL;  //矩形左上角坐标
    s_Point_v2 BM;  //矩形右下角坐标
};
struct s_Click_v1 {
    ////点击力及点击面积,结构体(8位)
    PLBYTE power;
    PLBYTE area;
};

///内核数据文件对象，在ipt_core_load，ipt_core_create_emptylib等函数中使用（除了gram_file 和cz_file ,其他文件没什么必要尽量不要传文件内容指针）
//#define IPT_LIBFILE_NUM (72)//内核数据文件个数，增加文件注意修改这个!!!

struct py_correct_info {
    PLUINT32 count;               //纠错的数量//
    PLUINT8 position[8];       //需要展示纠错的位置//
    PLUINT16 pinyin[8][8];      //纠错拼音的内容//
} ;
typedef struct {
    PLUINT32 cand_num;
    PLUINT32 run_time;
} nn_ranker_info;
///输入法双拼配置(不建议直接使用, 建议使用ipt_util_parseSp_Byfile函数从文件读取双拼方案)
struct s_ipt_shuangpin {
    PLBYTE sheng_config[24]; ///<声母映射表
    PLBYTE yun_config[36]; ///<韵母映射表(最后三个字节只用于字节对齐)
    PLBYTE yinjie_config[24]; /**< 音节映射表 */
    /////////////////////////////////////////
};

//enum CLOUD_INPUT_TYPE {
//    CLOUD_INPUT_TYPE_PY = 0, ///拼音输入方式
//    CLOUD_INPUT_TYPE_EPY = 1,///带纠错的拼音输入方式
//    CLOUD_INPUT_TYPE_T9 = 2, ///T9拼音输入方式
//    CLOUD_INPUT_TYPE_SP = 3, ///双拼输入方式
//    CLOUD_INPUT_TYPE_HW = 4, ///手写输入方式
//    CLOUD_INPUT_TYPE_LIAN = 5, ///联想输入方式
//    //
//    CLOUD_INPUT_TYPE_SUG_BEGAN = 6,
//    CLOUD_INPUT_TYPE_SUG_CARD = 7, ///sug卡片请求
//    CLOUD_INPUT_TYPE_SUG_T9 = 8, ///sug请求T9
//    CLOUD_INPUT_TYPE_SUG_PY = 9,  ///sug请求拼音
//    CLOUD_INPUT_TYPE_SUG_PRE = 10, ///sug请求只支持前缀查找
//    CLOUD_INPUT_TYPE_SUG_SP = 11, ///sug请求双拼
//    CLOUD_INPUT_TYPE_SUG_END = 100,
//    //
//    CLOUD_INPUT_TYPE_SEARCH_BEGAN = 101,
//    CLOUD_INPUT_TYPE_SEARCH_KEYWORD = 102, //云搜索请求
//    CLOUD_INPUT_TYPE_SEARCH_END = 200,
//    //
//    CLOUD_INPUT_TYPE_AI_BEGAN = 201,
//    CLOUD_INPUT_TYPE_AI_REPLY = 202, //智能回复
//    CLOUD_INPUT_TYPE_AI_END = 300,
//    CLOUD_INPUT_TYPE_AUTO_CORRECT_BEGIN = 301,
//    CLOUD_INPUT_TYPE_AUTO_CORRECT = 302, //自动撰写和纠错
//    CLOUD_INPUT_TYPE_AUTO_CORRECT_END = 400,
//    //
//    CLOUD_INPUT_TYPE_KV_BEGAN = 401, /**只走KV的TYPE没有预留空间了暂时通过添加输入类型来处理 **/
//    CLOUD_INPUT_TYPE_KV_PY = 402,
//    CLOUD_INPUT_TYPE_KV_T9 = 403,
//    CLOUD_INPUT_TYPE_KV_LIAN = 404,
//    CLOUD_INPUT_TYPE_KV_PY_PC = 405,//硬键盘和PC云预取使用
//    CLOUD_INPUT_TYPE_KV_END = 500,
//    //
//    CLOUD_INPUT_TYPE_HW_LIAN = 501, ///手写联想输入方式
//};

enum CLOUD_OUTPUT_ITEM_TYPE {
    CLOUD_OUTPUT_ITEM_TYPE_CAND = 0, ///cand
    CLOUD_OUTPUT_ITEM_TYPE_URL = 1,  ///url
    CLOUD_OUTPUT_ITEM_TYPE_BINARY = 2, ///binary
    CLOUD_OUTPUT_ITEM_TYPE_IMG = 3,  ///img
    CLOUD_OUTPUT_ITEM_TYPE_JSON = 4, ///json
    CLOUD_OUTPUT_ITEM_TYPE_SERVICE = 5, ///服务
    CLOUD_OUTPUT_ITEM_TYPE_SUG_ACTION = 6,///sug行为
    CLOUD_OUTPUT_ITEM_TYPE_SUG_CARD = 7,///sug卡片
    CLOUD_OUTPUT_ITEM_TYPE_SEARCH = 8, ///搜索结果
    CLOUD_OUTPUT_ITEM_TYPE_AI_REPLY = 9, ///智能回复
    CLOUD_OUTPUT_ITEM_TYPE_AI_INTEN = 10, ///智能回复-意图理解
    CLOUD_OUTPUT_ITEM_TYPE_MULTI_LIAN = 11, ///多级联想结果
    CLOUD_OUTPUT_ITEM_TYPE_AUTO_COMPOSE = 12, ///自动撰写结果
    CLOUD_OUTPUT_ITEM_TYPE_CORRECT = 13, ///纠错结果
    CLOUD_OUTPUT_ITEM_TYPE_AI_CLOUD = 14, ///智能云词结果
};

enum  CLOUD_OUTPUT_SERVICE_TYPE {
    CLOUD_OUTPUT_SERVICE_TYPE_NONE = 0,  //此类型说明资源为nullptr，不参与资源匹配
    CLOUD_OUTPUT_SERVICE_TYPE_HOLDER = 1,  //place holder 留空占位
    CLOUD_OUTPUT_SERVICE_TYPE_CANDIDATE = 2,  //candidate type 候选类型，此类型会让正常的云输入结果填充
    CLOUD_OUTPUT_SERVICE_TYPE_TEXT = 3,  //plaint text 普通文本（文字彩蛋）
    CLOUD_OUTPUT_SERVICE_TYPE_MOVIE = 4,  //movie resource  电影资源
    CLOUD_OUTPUT_SERVICE_TYPE_SMILIES = 5,  //face expression 表情符号
    CLOUD_OUTPUT_SERVICE_TYPE_EMOTICONS = 6,  //cloud emoticons 颜文字
    CLOUD_OUTPUT_SERVICE_TYPE_IMAGE = 7,  //image 图片
    CLOUD_OUTPUT_SERVICE_TYPE_MAGIC_TEXT = 8, //ios特技字体
    CLOUD_OUTPUT_SERVICE_TYPE_SUG = 9,  //sug
    CLOUD_OUTPUT_SERVICE_TYPE_DIY_SUG = 10,  //自定义sug
    CLOUD_OUTPUT_SERVICE_TYPE_SUG_AD = 11, // Sug广告 ///
    //
    CLOUD_OUTPUT_SERVICE_TYPE_AI_BEGAN = 100,
    CLOUD_OUTPUT_SERVICE_TYPE_AI_REPLY = 101, //智能回复
    CLOUD_OUTPUT_SERVICE_TYPE_AI_INTENT = 102, //智能回复-意图理解
    CLOUD_OUTPUT_SERVICE_TYPE_AI_EMOJI = 103, //智能回复-emoji表情
    CLOUD_OUTPUT_SERVICE_TYPE_AI_QUERY_KEY = 104, //智能回复-问题
    CLOUD_OUTPUT_SERVICE_TYPE_AI_END = 200,
    //
    CLOUD_OUTPUT_SERVICE_TYPE_JSON = 201 //json结果
                                     //
};

enum CLOUD_OUTPUT_SEARCH_TYPE {
    CLOUD_OUTPUT_SEARCH_TYPE_NONE = 0,
    CLOUD_OUTPUT_SEARCH_TYPE_KEYWORD = 1 //搜索关键字
};

enum  CLOUD_OUTPUT_SERVICE_SHOW_TYPE {
    CLOUD_OUTPUT_SERVICE_SHOW_TYPE_NM = 0, //一般展现效果
    CLOUD_OUTPUT_SERVICE_SHOW_TYPE_LIAN = 1,  //候选字展现效果,context为unicode汉字
    CLOUD_OUTPUT_SERVICE_SHOW_TYPE_JSON = 2 //json展现效果,context为json格式
};

enum CLOUD_COMPRESS_TYPE {
    CLOUD_COMPRESS_TYPE_NONE = 0,  ///无压缩
    CLOUD_COMPRESS_TYPE_GZIP = 1,  ///gzip压缩方式
    CLOUD_COMPRESS_TYPE_BEZIER = 2, ///bezier拟合压缩方式
    CLOUD_COMPRESS_TYPE_SNAPPY = 3 /// SNAPPY压缩方式
};

enum CLOUD_ENCRY_TYPE {
    CLOUD_ENCRY_TYPE_NONE = 0,  ///无加密
    CLOUD_ENCRY_TYPE_AES = 1,   ///AES方式加密 
    CLOUD_ENCRY_TYPE_HARDWARE_SPEEDUP_AES = 2 ///硬件加密
};

enum CLOUD_NET_TYPE : PLUINT32 {
    CLOUD_NET_TYPE_UNKNOW = 0,
    CLOUD_NET_TYPE_2G = 1,
    CLOUD_NET_TYPE_3G = 2,
    CLOUD_NET_TYPE_4G = 3,
    CLOUD_NET_TYPE_4_NG = 4,
    CLOUD_NET_TYPE_5G = 5,
    CLOUD_NET_TYPE_WIFI = 10,
    CLOUD_NET_TYPE_END = 255
};

//云输入配置
struct s_cloud_setting {

    PLUINT16 input_type; ///输入方式，参见 CLOUD_INPUT_TYPE
    PLUINT16 ret_cnt;    ///期望返回词条数，暂时填1
    /////////////////////////////////////////////////////////
    PLUINT8 req_compress_type;///请求数据的压缩方式,拼音建议CLOUD_COMPRESS_TYPE_GZIP,手写建议CLOUD_COMPRESS_TYPE_NONE
    PLUINT8 req_encry_type;  ///请求数据的加密方式,拼音和手写都建议CLOUD_ENCRY_TYPE_AES
    PLUINT8 ret_compress_type;///返回数据的压缩方式,拼音和手写都建议CLOUD_COMPRESS_TYPE_GZIP
    PLUINT8 ret_encry_type;///返回数据的加密方式,拼音和手写都建议CLOUD_ENCRY_TYPE_AES
    /////////////////////////////////////////////////////////
    PLUINT16 hw_block_compress_type;///手写块使用的压缩方式,只在手写输入时使用,建议CLOUD_COMPRESS_TYPE_BEZIER
    PLUINT16 input_id;///输入框id
    /////////////////////////////////////////////////////////
    PLUINT32 trigger_level;///云输入请求等级，一般是1，代表最高等级。后续可以制订等级2、3等等。
    PLUINT32 is_edit_filt;//该输入框是否是需要云输入服务强推
    /////////////////////////////////////////////////////////
    const PLUINT16* lian_uni; ///需要联想的词
    PLUINT32 lian_uni_len;///需要联想词的长度
    /////////////////////////////////////////////////////////
    PLUINT32 sug_source_id;///sug源id
    PLUINT32 check_id;//唯一校验id
    /////////////////////////////////////////////////////////
    //    input==nullptr时,会根据input检测是否发起云请求（会忽略白名单，二字等情况）
    //    input!=nullptr时会根据内核状态检测是否发起云请求
    const char* input;
    /////////////////////////////////////////////////////////
    PLUINT32 need_zj_forcast; /// 是否需要整句预测结果, 0:不需要, 1:需要 ///

};

///已0结尾,长度不超过255,中文已UTF8编码
struct s_phone_info {
    const char* cuid;///手机唯一标识码
    ///系统和型号,可以不带型号，平台号|型号|其它信息1|其它信息2  例如a5|小米2S或i6|iphone5s或mac或a5或 多个信息用|分割//
    const char* cname;
    const char* input_ver;///输入法版本号 如5.0.2
    const char* app_name;///当前输入应用的唯一名字
    const char* channel;///渠道号 1000e
    const char* city;///城市 shanghai
    PLUINT32 net_type;///联网方式0:未知,1:2g,2:3g,3:4g,4:>4g,10:wifi 参见CLOUD_NET_TYPE
    PLUINT16 screen_width;///竖屏状态屏幕宽度
    PLUINT16 screen_height;///竖屏状态屏幕高度
    ////////////////////////
    PLUINT32 exist_mb_pkg; /** 是否安装了手百 **/
    ////////////////////////
    PLUINT8* log;
    PLUINT32 log_len;
    ///////////////////////
    const PLUINT8* json_buf; ///json信息, 最大长度为4096
    PLUINT32 json_buf_len;
    //////////////////////
    const char* cuid3; /** cuid3.0 **/
    const char* oaid; /** oaid **/
    //////////////////////
    const PLUINT8* attachment_data; ///protocol buf等附加数据
    PLUINT32 attachment_data_len;
    //////////////////////
    bool no_more_user_trace;
    const PLUINT8* user_trace_data; ///用户轨迹数据(云输入通道传输)
    PLUINT32 user_trace_data_len;
    //
    const PLUINT8* ai_cloud_data; ///智能云词数据////
    PLUINT32 ai_cloud_data_len;
};

enum SugLogType {
    SUGLOGTYPE_NONE = 0,
    SUGLOGTYPE_UNKNOW = 1,
    SUGLOGTYPE_SHOW = 2,
    SUGLOGTYPE_CLICK = 3,
    SUGLOGTYPE_CARD = 4,
};

//云输入收集信息相关
struct s_cloud_sug_log {
    PLUINT32 sug_log_type;//
    PLUINT32 input_id;///输入框id
    PLUINT32 click_type;///点击类型
    PLUINT32 sug_source_id;///sug
    char* pkg_name;///包名
    PLUINT16* sug_word;///sug词
    PLUINT32 sug_word_len;///sug词长
    PLUINT16* input_inline;///输入码+框中内容
    PLUINT32 input_inline_len;///
};

//云输入返回


struct s_cloud_output;
struct s_cloud_output_item;
struct s_cloud_output_service;
struct s_cloud_output_pkg_ver;
struct s_cloud_output_pkg;
struct s_cloud_output_sug_action;
struct s_cloud_output_sug_card;
struct s_cloud_output_search;
struct s_cloud_forecast_output;

struct s_cloud_output_item {
    PLUINT32 item_type;//附加彩蛋类型,参见CLOUD_OUTPUT_ITEM_TYPE
    PLUINT32 item_size;//附加彩蛋长度
    PLBYTE*  item_buf;//附加彩蛋数据
};

struct s_cloud_output_service {

public:
    /// 过滤掉sug无法处理的类型 ///
    inline bool sug_filter() const {
        if (show_type != CLOUD_OUTPUT_SERVICE_SHOW_TYPE_NM) { return true; }
        if (service_type == CLOUD_OUTPUT_SERVICE_TYPE_NONE ||
            service_type == CLOUD_OUTPUT_SERVICE_TYPE_HOLDER) { return true; }
        return false;
    }
    /// 是否需要图片下载好后再展示 ///
    inline bool is_need_source_image() const {
        if (service_type == CLOUD_OUTPUT_SERVICE_TYPE_MOVIE ||
            service_type == CLOUD_OUTPUT_SERVICE_TYPE_IMAGE ||
            service_type == CLOUD_OUTPUT_SERVICE_TYPE_DIY_SUG) {
            return img_url_len > 0;            
        }
        return false;
    }

public:
    PLUINT8 show_type;//显示标记位
    PLUINT8 is_zids;//是否是zid//
    PLUINT16 reserved2;//对齐预留2
    ///////////////////////////////////////////////////////////
    PLUINT16 service_type;//服务类型
    PLUINT16 uni_len;//服务文字长度
    PLUINT16 img_url_len;//服务图片URL长度
    PLUINT16 goto_url_len;//服务跳转URL长度
    PLUINT32 content_len;//内容长度
    //////////////////////////////////////////////////////////
    PLUINT16* uni;//服务文字
    PLBYTE* content;//内容,根据show_type不同代表不同内容，参见CLOUD_OUTPUT_SERVICE_SHOW_TYPE
    PLBYTE* img_url;//服务图片URL
    PLBYTE* goto_url;//服务跳转URL
    /////////////////////////////////////////////////////////
    PLUINT16* expression_key; //智能回复关键字
    PLUINT32  expression_key_len; //智能回复关键字长度
    /////////////////////////////////////////////////////////
    PLBYTE* uid;///
    PLUINT32 uid_len;///
    PLINT32 max_show_num;//最大展现次数，>=0为服务端给的值,<0为没有获取到服务端给的值
    /////////////////////////////////////////////////////////
    s_Rect_v2 img_rect;//服务图片内切矩阵
    /////////////////////////////////////////////////////////
    PLUINT32 flag; //云输入标记
    PLUINT32 is_replace; //是否替换本地结果
};

struct s_cloud_output_pkg_ver {
    PLUINT32 min_ver;
    PLUINT32 max_ver;
};

struct s_cloud_output_pkg {
    PLBYTE* pkg_name;
    PLUINT32 pkg_name_len;
    //////////////////////////////////
    s_cloud_output_pkg_ver* ver_list;
    PLUINT32 ver_cnt;
};

//sug行为
struct s_cloud_output_sug_action {
    PLUINT32 action_type;//4种行为类型，直接上屏,第三方app打开等
    PLUINT32 sug_type;//sug的类型,电影,音乐等
    PLUINT32 sug_source_id;//sug源id
    PLUINT32 ex_action;//额外行为标记,1:需要卡片
    PLUINT16* source_msg;//提供源信息
    PLUINT32 source_msg_len;//
    PLBYTE* command;//执行指令
    PLUINT32 command_len;//执行指令长度

    s_cloud_output_pkg* pkg_list;//使用第三方app列表
    PLUINT32 pkg_cnt;//使用第三方app数量
};

//sug卡片
struct s_cloud_output_sug_card {
    PLUINT32 card_id;//卡片id
    PLUINT16* card_key;//卡片key
    PLUINT32 card_key_len;
    PLUINT16* title;//卡片标题
    PLUINT32 title_len;
    PLUINT16* content1;//卡片内容1
    PLUINT32 content1_len;
    PLUINT16* content2;//卡片内容2
    PLUINT32 content2_len;
    PLUINT16* content3;//卡片内容3
    PLUINT32 content3_len;
    PLUINT8* img_url;//卡片图片url
    PLUINT32 img_url_len;
    PLUINT8* icon_url;//卡片图标url
    PLUINT32 icon_url_len;
    PLUINT8* download_url;//下载url
    PLUINT32 download_url_len;
};

//云搜索关键字
struct s_cloud_output_search {
    PLUINT32  type;//类型, 目前只有搜索关键字, CLOUD_OUTPUT_SEARCH_TYPE_KEYWORD
    PLUINT32  jump_tab;//搜索内容的类型,如文字，图片等
    PLUINT32  word_type;//
    PLUINT32  show_count;//每日展示次数
    PLUINT16* keyword;//
    PLUINT16* hint;//
    PLUINT16  keyword_len;//
    PLUINT16  hint_len;//
};

struct s_cloud_output {
    struct BitFiledFlag {
        PLUINT32 cand_len : 31;//候选字unicode/zids长度,具体类型在cand_flag的is_zids标识//
        PLUINT32 is_zids : 1; //(2020/10/29,最高位表示当前cand的内容是否为转换后的zids)//
    };
    PLUINT16* cand;//候选,=nullptr时表示没有文字候选，例如只有图片结果的情况
private:
    BitFiledFlag  bitfield;
    PLUINT32 candflag;              //可以在外直接设置//
    ////////////////////////////////////////////
public:
    PLUINT32  item_cnt;//附加彩蛋个数
    s_cloud_output_item* item_list;//附加彩蛋内容
public:
    PLUINT32 cand_flag() const { return candflag; }
    void set_flag(PLUINT32 flag) { candflag = flag; }
    void set_cand_len(PLUINT32 wlen) { bitfield.cand_len = wlen; }
    PLUINT32 cand_len() const { return bitfield.cand_len; }
    bool is_cand_zids() const { return !!bitfield.is_zids; }
    void set_cand_zids(bool is_zids) { bitfield.is_zids = is_zids ? 1 : 0; }
};

typedef void (*sig_debug_function)(const char* text);

#ifdef CRASH_VERSION
#define call_int_method AU68
#define s_wchar_lsp AU76
#define al32 AL32
#define al58 AL58
#define al76 AL76
void* al32(void* prm1, void* prm2, void** out1, sig_debug_function call_back);
int al58(void* prm1, void* prm2, void* prm3);
#endif
#ifdef PLATFORM_ANDROID
int al76(void* prm1, void* prm2, void** out1);
#endif
#ifdef IOS_ACTIVATION
#define al58 AL58
int al58(const char* prm1, char* err);
#endif

//云输入整句预测结果////
struct s_cloud_forecast_output {
    const PLUINT16* cand;//显示的结果unicode////
    PLUINT32 cand_len;//显示的结果unicode长度////
    PLUINT32 cand_color_len;//前序长度////
    PLUINT32 cur_match_len;//当前输入码匹配的长度, feature 1934////
    const PLUINT16* commit_cand;//上屏结果////
    PLUINT32 commit_cand_len;//上屏结果长度////
    PLUINT16 cand_zids[128];
    PLUINT32 cand_zids_len;
    //
    bool is_valid() const { return cand_len > 0 && commit_cand_len > 0; }
};

//自动撰写结果////
struct s_cloud_auto_compose_cand {
    PLUINT16 cand[64]; //候选unicode////
    PLUINT32  cand_len;//候选字unicode长度////
    PLUINT32 his_len; //前文长度////
};
struct s_cloud_auto_compose_output {
    PLUINT32 item_cnt;
    s_cloud_auto_compose_cand* items;
};

//纠错结果////
struct s_cloud_correct_output {
    PLUINT32 org_unis_len;
    PLUINT32 replace_unis_len;
    PLUINT32 begin_pos;
    PLUINT32 end_pos;
    PLUINT16 org_unis[64];
    PLUINT16 replace_unis[64];
};

///细胞词库结构体, 用于ipt_cell_info_byIndex、ipt_cell_info_byCellId等函数
struct s_cellinfo {
    PLUINT32 server_guid; ///<细胞词库唯一识别码（服务端生成）
    PLUINT32 client_guid; ///<细胞词库唯一识别吗（客户端生成）
    PLUINT32 ver1; ///<主版本号(注意,这个版本号,只用于显示,判断细胞词库升级时候,应该使用流水线号(即:inner_ver))
    PLUINT32 ver2; ///<次版本号(注意,这个版本号,只用于显示,判断细胞词库升级时候,应该使用流水线号(即:inner_ver))
    PLUINT32 ver3; /**< 次次版本(注意,这个版本号,只用于显示,判断细胞词库升级时候,应该使用流水线号(即:inner_ver)) */
    ////////////////////////////////////////////////////////////////////
    PLUINT32 data_type; ///<细胞词库的类型(1表示增量词库, 0表示完整词库)
    PLUINT32 inner_ver_from; ///<完整词库下始终为0, 增量词库下表示该增量词库对应的上一个版本的内部版本号
    PLUINT32 inner_ver; /**< 完整词库下表示内部版本号（流水线号）, 增量词库下表示该增量词库对应的最新版本的内部版本号 */
    ///////////////////////////////////////////////////////////////////
    PLUINT32 type1; ///<主分类ID
    PLUINT32 type2; ///<二级分类ID
    PLUINT32 type3; ///<三级分类ID
    PLUINT32 ci_count; ///<词条个数
    PLUINT32 name_len; ///<词库名字长度
    PLUINT16 name_buf[32]; ///<词库名字
    PLUINT32 author_len; ///<作者信息长度
    PLUINT16 author_buf[32]; ///<作者信息
    PLUINT32 keyword_len; ///<关键词长度
    PLUINT16 keyword_buf[32]; ///<关键词
    PLUINT32 info_len; ///<词库介绍长度
    PLUINT16 info_buf[128]; ///<词库介绍
    ///////////////////////////////////////////////////////////////////
    PLUINT16 loc_type; ///地理位置词库类型  0-普通，1-临时地理位置， 2-常居地理位置
    PLUINT16 is_hide;  ///是否隐藏  0-不隐藏  1-隐藏
    PLUINT32 install_time; ///安装时间
};
//智能问答用于封装答案的结构体
struct s_autoreply_answers {
    PLUINT16* ans;
    PLUINT32 ans_len;
};

struct s_keyword_info_header {
    PLUINT32 time;//文件生成日期(由服务端指定，频率相同时，生成日期较晚的优先出)
    //---------------------------------------------------
    PLUINT32 server_guid; //词库唯一识别码,服务端生成.
    PLUINT32 client_guid; //客户端安装时指定的本地ID。
    PLUINT32 ver1; //主版本
    PLUINT32 ver2; //次版本
    //----------------------------
    PLUINT32 ver3; //次次版本
    PLUINT32 data_type;//1表示增量词库。0 表示完整词库。
    PLUINT32 inner_ver_from; //完整词库下始终为0，增量词库下表示该增量词库对应的上一个版本的内部版本号。
    PLUINT32 inner_ver; //完整词库下表示内部版本号（流水线号），增量词库下表示该增量词库对应的最新版本的内部版本号。
    //-----------------------------
    PLUINT32 type1; //主分类ID ，分类ID 对应信息由服务端管理，空表示未分类。
    PLUINT32 type2; //二级分类ID
    PLUINT32 type3; //三级分类ID
    PLUINT32 type4; //四级分类ID
    //-----------------------------
    PLUINT32 keyword_count;//增加表情个数
};

struct s_idmap_node { //id映射节点
    PLUINT32 word_len;  //31-25表示分类  后24位标识实际长度(颜文字等unicode字符长度*2, emoji等二进制文件为实际长度)
    PLUINT16 cell_id;  //对应分组
    PLUINT16 flag;  //预留区域
    PLBYTE data_buff[4]; //内容
};

struct s_idmap_cell_header {
    PLUINT32 uid1; //文件头识别码1 数值待定
    PLUINT32 uid2; //文件头识别码2 数值待定
    PLUINT32 time;//文件生成日期
    PLUINT32 type;//分类
    //---------------------------------------------------
    PLUINT32 server_guid; //词库唯一识别码,服务端生成.
    PLUINT32 client_guid; //客户端安装时指定的本地ID，首位标识是否打开
    PLUINT32 ver; //对外的版本号
    PLUINT32 idmap_count;
    //-----------------------------
    PLUINT32 inner_ver; //内部版本号
    PLUINT32 inner_ver_from;
    PLUINT32 data_type;
    PLUINT32 reserved3;
    //-----------------------------
    PLUINT32 reserveds[8];
};

struct PhraseItemInfo {
    ///<个性短语的位置(0表示默认位置(由s_ipt_config结构体中的ot_phrase属性确定),///<1-9为在候选项列表中的位置)
    PLBYTE pos;
    PLBYTE group_id; ///<个性短语所在分组的id
    PLBYTE code_len; ///<个性短语代码长度, 取值范围为1-32
    PLBYTE word_len; /**< 个性短语内容长度, 取值范围为1-64 */
    ///////////////////////////////
    PLBYTE code[32];   ///<个性短语代码, 最多32个字符(只能为0-9或a-z中的字符)
    PLUINT16 word[64]; ///<个性短语内容, 最多64个字符
};

//EDIT(只能修改名字和是否打开)//DELETE(当数据为空时,才可以删除)//ADD:
///仅用于PC端细胞词库示例展示
struct DictExport {
    DictExport() : name_len(0) {
        name[0] = 0;
        reversed[0] = 0;
    }
    PLUINT16 name[16];
    PLBYTE name_len;
    PLBYTE reversed[3];
};
struct PhraseGroupInfo {
    PLBYTE group_name_len; ///<个性短语分组名字的长度(取值范围为1-15)
    PLBYTE group_id;       ///<个性短语分组id
    //////////////////////////////////////////////
    PLUINT16 group_cnt; /**< 个性短语分组中的短语个数 */
    PLBYTE is_open;     ///<个性短语分组是否打开
    PLBYTE reserve3;    ///<为了内存对齐而作的保留空间
    /////////////////////////////////////
    PLUINT16 word[16]; ///<个性短语分组名字(最长32个中文字符)
};

enum NLP_REQ_TYPE {
    NLP_REQ_COMPOSE = 1 //请求撰写
    , NLP_REQ_CORRECT = 1 << 1 //请求纠错
};
enum ORIENTATION : PLUINT32 {
    ORIENTATION_UNKNOWN = 0,
    ORIENTATION_LANDSCAPE = 1,
    ORIENTATION_PORTRAIT = 2,
};

enum CLOUD_IN_STYLE {
    CLOUD_IN_STYLE_UNKNOWN = 0, /// 当前没有来源信息 ///
    CLOUD_IN_STYLE_TEXT = 1,
    CLOUD_IN_STYLE_IMAGE = 2,
    CLOUD_IN_STYLE_TEXT_AND_IMAGE = 3
};

enum class OEM_NAME {
    MAINLINE = 0,
    OEM_HUAWEI = 1,
    OEM_HONOR = 2,
    OEM_OPPO = 3,
    OEM_VIVO = 4,
    OEM_XIAOMI = 5,
    OEM_OTHER = 255
};

// 内核日志收集等级-默认不开启-通过编译宏控制 ///
// 谨慎-开启后可带入线上 ///
enum class CORE_ONLINE_LOG {
    CORE_ONLINE_LOG_ALL = 0, // 开启所有日志 ///
    CORE_ONLINE_LOG_TIMECOST = 1, // 内核关键逻辑-耗时 ///
    CORE_ONLINE_LOG_NONE = 64, // 关闭线上日志(默认) ///
};

///内核词库数据句柄(一般来说, 只创建唯一个, 用于承载词库数据和设置项)
namespace ipt3 { class Core; }
using  s_iptcore = ipt3::Core;
///内核会话句柄(可创建多个,用于承载查询结果)
namespace ipt3 { class Session; }
using s_session = ipt3::Session ;
/// 语音纠错的用户操作记录信息
struct s_voice_correct_user_act {
    enum VOICE_CORRECT_USER_ACT_TYPE {
        VOICE_CORRECT_USER_ADD = 1,
        VOICE_CORRECT_USER_DEL = 2,
        VOICE_CORRECT_USER_NONE = 3,
    };
public:
    VOICE_CORRECT_USER_ACT_TYPE act_type;
    PLUINT16 pre[128]; //操作词语对应的前缀
    PLUINT16 suf[128]; //操作词语对应的后缀
    PLUINT16 txt[128]; //词语本身 （前缀 +词语 + 后缀 获取词语在句子中的位置)
};

struct U16Str {
    const PLUINT16* data = nullptr;
    PLUINT32 len = 0;
};

//////////////////////////////////////////////////////////////////////////////////////////////
//////////////////////////////给评测单字识别率时使用的裸露出来的单字接口,框架请别使用

/**查询idx位置的纠错提示信息
* @param _session — 内核会话句柄
* @param idx    |   对应候选字的位置
* @param iec_str    |   存放纠错信息的数组，只有该位置进行了纠错才会有相应纠错字符
* @param iec_info   |   存放纠错类型的数组，对应某一位置上进行的纠错类型
*/
//以下接口给qa使用
#if defined(PLATFORM_WINDOWS) || defined(IPT_PLATFORM_LINUX)

//struct s_test_cloud_result {
//    PLUINT16* uni;
//    PLUINT32 uni_len;
//    PLUINT8* py;
//    PLUINT32 py_len;
//    PLUINT8* key;
//    PLUINT32 key_len;
//    PLUINT32 type;
//    PLUINT32 freq;
//};
// 获取云输入的全部结果，好像没地方调用 //
//s_test_cloud_result* ipt_test_get_cloud_result(s_session* _session, PLUINT32* len);

#endif

//PLINT32 ipt_query_rand(PLUINT32 min, PLUINT32 max);

/**添加一个unicode的候选到cmd的上屏词中，替代ipt_set_str_before_cursor。预上屏部分不用包含在uni里。
 * @param _session — 内核会话句柄
 * @param uni — 字符串指针
 * @param len — 字符串长度
 * @param pad_property - 面板属性，1代表英文面板，其他为0
 * @return 返回值0代表成功
 */
//PLINT32 ipt_query_cmd_add_uni_history(s_session* _session,
//        PLUINT16* uni,
//        PLUINT32 uni_len,
//        PLUINT32 pad_property = 0);
/**清除云缓存信息(可以在关闭云预测开关时调用，可以清除已有的预测词）
 * @param _session — 内核会话句柄
 * @return 返回值目前无意义
 */
PLUINT32 ipt_cloud_cache_clean(s_session* _session);

/**清空当前的动态热区
* @param _session    — 内核会话句柄
* @return 返回为0表示清楚前动态热区未初始化，为1表示正常
*/
PLINT32 ipt_clean_dynamic_rects(s_session* _session);

/**设置当前使用的皮肤Tokken和手机状态
* @param _iptcore        — 内核句柄
* @param skin_token      — 皮肤token
* @param skin_token_len  — 皮肤token长度
* @param phone_stat      — 手机当前的状态，参见PHONE_STATE_LIST列表
* @return 返回为0表示正确，返回-1表示设置失败
*/
//PLINT32 ipt_set_skin_token(s_iptcore* _iptcore, char* skin_token,
//                                      PLUINT16 skin_token_len,
//                                      PLUINT16 phone_state);

/**导出上次上传至今的误触情况
* @param _iptcore        — 内核句柄
* @param out_json        — 用来保存返回json的指针
* @param out_json_len    — 最大返回的json长度
* @return 返回值表示实际返回的json长度，返回-1表示获取失败
*/
//PLINT32 ipt_usr_touch_export_fresh_data(s_iptcore* _iptcore,
//        PLUINT16* out_json, PLUINT16 out_json_len);

/**导出这次commit的误触情况用于trace
* @param _iptcore        — 内核会话句柄
* @param out_buf         — 用来保存返回结果的指针
* @param out_max_len     — 最大返回的长度
* @return 返回值表示实际返回的json长度，返回-1表示获取失败
*/
PLINT32 ipt_export_mis_key_for_trace(s_session* _session,
                                     PLUINT16* out_buf,
                                     PLUINT16 out_max_len);

/**导出用户本地统计的误触信息，信息保存的数据为从上次上传之后统计的数据
* @param _iptcore        — 内核句柄
* @param out_json        — 用来保存返回json的指针
* @param out_json_len    — 最大返回的json长度
* @return 返回值表示实际返回的json长度，返回-1表示获取失败
*         如果返回成功则会将本地统计的数据清空，返回-1则本地数据不会被清空，返回-1可尝试将out_json的分配空间加大
*/
PLINT32 ipt_usr_touch_export_stats_data(s_iptcore* _iptcore,
                                        PLUINT16* out_json, PLUINT16 out_json_len);
/**根据输入码code，能匹配到一句话sen的几个字
 * @param _session — 内核会话句柄
 * @param sen — 待匹配的unicode码
 * @param code — 待调频词的拼音
 * @return 返回匹配到的字数
 */

/*****************************8.7新增功能***********************************
* 1.1945 优化英文键盘下预测、联想等出词。英文面板下的用户词暂时与中文面板下的英文用户词分开
* 新增接口列表
* ipt_query_cmd_add_uni_history 添加一个unicode的候选到cmd的上屏词中，替代ipt_set_str_before_cursor
* ipt_usr_touch_export_stats_data 导出用户本地统计的误触信息
* 新增数据文件
* en_sys.dic;  //英文面板下的英文系统词库
* en_user.dic; //英文面板下的英文用户词库
*/


