/// @file      _pub_iptcore.h
/// @brief    新版用户接口
/// <AUTHOR>
/// @date     2020-03-08
/// @version  ********
/// @par Copyright (c):  Baidu

#pragma once
#include "_pub_iptcore.h"
#include <string>

#ifdef IPT_STRATEGY_TRACE
#include <vector>
#include <string>
#endif
#ifdef PLATFORM_WINDOWS
#pragma warning(disable : 26439)///可能不会 throw 此类型的函数。将其声明为 "noexcept" (f.6)。///
#pragma warning(disable : 4100) /// 未引用的形参 ///
#pragma warning(disable : 4389) ///“!=”: 有符号/无符号不匹配 ///
#pragma warning(disable : 4244) /// 从“Uint32”转换到“Uint8”，可能丢失数据 ///
#pragma warning(disable : 4245) /// 从“int”转换到“Uint32”，有符号/无符号不匹配 ///
#pragma warning(disable : 26451) /// 算术溢出: 使用 4 字节值上的运算符 * ，然后将结果转换到 8 字节值 ///
#pragma warning(disable : 26495) /// 未初始化变量 ipt3::HistoryItem::_zids。始终初始化成员变量 ///
#pragma warning(disable : 26812) /// 枚举类型未设定范围 相比于 "enum"，首选 "enum class" /// 
#define IPT_IMPORT __declspec(dllexport)
#else
#define IPT_IMPORT
#endif

using Int8 = signed char;
using Uint8 = unsigned char;
using Int16 = signed short;
using Uint16 = unsigned short;
using Int32 = signed int;
using Uint32 = unsigned int;
using Int64 = signed long long;
using Uint64 = unsigned long long;
using Real32 = float;
using Real64 = double;
using TstlVoidPtr = void*;

template<Uint32 N> struct tstl_cpubitwidth_to_sizetype_pub {
    using sizetype = TstlVoidPtr;
    using sizetype_signed = TstlVoidPtr;
};
template<> struct tstl_cpubitwidth_to_sizetype_pub<8> {
    using sizetype = Uint64;
    using sizetype_signed = Int64;
};
template<> struct tstl_cpubitwidth_to_sizetype_pub<4> {
    using sizetype = Uint32;
    using sizetype_signed = Int32;
};

using Tsize = tstl_cpubitwidth_to_sizetype_pub<sizeof(TstlVoidPtr)>::sizetype;
using TsignedInt = tstl_cpubitwidth_to_sizetype_pub<sizeof(TstlVoidPtr)>::sizetype_signed;
/// @brief 输入法内核
///
namespace iptcore {
const Uint32 MAX_SHOWINFO_LEN = 512; // 最长的拼音码长度,对应于64个unicode
const Uint32 MAX_RECENT_SYM = 32; //最多用户最近使用符号个数
#pragma pack(push, 4)

enum AiSceneType : Uint32 {
    AI_CHAT_SCENE = 0, /// 助聊-聊天场景 ///
    AI_SHOP_SCENE = 1, /// 助聊-购物场景 ///
    AI_POST_SCENE = 2,  ///助聊-发布模型 ///
};

/// @brief Ai正负能量条状态 ///
enum AiEnergyType : Uint32 {
    AI_ENERGY_NORMAL = 0, ///< 常规模式
    AI_ENERGY_NEG = 1, ///< 抑郁模式
    AI_ENERGY_POS = 2, ///< 治愈模式
    AI_ENERGY_NONE = 3, ///<不显示正负能量条 ///模糊模式(客户端根据配置, 可能在次cand发起请求模糊撰写)
};

enum ErrorNoticeType { /// 内核希望UI层展示的, 错误提示信息 ///
    ERR_NOTICE_NONE = 0,
    ERR_NOTICE_FAIL_TO_OPEN_AI_PAD = 1,  /// 当前场景无法打开Ai助聊卡片 ///
};

enum TriggerCloudShowType : Uint32 { /// 云端触发词 配置的show_type类型 //
    CloudTriggerShowTypeNone = 0, //占位，无意义 ///
    CloudTriggerShowTypeOrigin = 1,  /// 原词 ///
    CloudTriggerShowTypePrompt = 2, // 提示词 ///
    CloudTriggerShowTypeValue = 3, // 生成结果 ///
};

// OEM的OPPO意图 ///
enum CloudIntentionType : Uint32 {
    CloudIntentionNone = 0, // 无意义，占位 ///
    CloudIntentionOPPOContact = 1,  // 联系人 ///
    CloudIntentionOPPOCalendar = 2, // 日历 //
    CloudIntentionOPPOWeather = 3, // 天气 ///
    CloudIntentionOPPOCalculator = 4, // 计算器 ///
    CloudIntentionOPPODelicacy = 5, // 美食 ///
    CloudIntentionOPPOMap = 6, // 地图 ///
};

// OEM的荣耀意图 ///
enum CloudSlotIntentionType : Uint32 {
    CloudSlotIntentionNode = 0, // 无意义，占位 ///
    CloudSlotIntentionPhoto = 1, // 照片 ///
    CloudSlotIntentionSchedule = 2, // 日程 ///
};

// 云端意图-主线-一级tab分类 ///
enum CloudIntentionTypeFirstLevel : Uint32 {
    CloudIntentionFirNone = 0, // 无意义，占位 ///
    CloudIntentionFirBaiduBaike = 1, // 百度百科 ///
    CloudIntentionFirLifeServices = 2, // 生活服务 //
    CloudIntentionFirNovel = 3, // 小说 ///
    CloudIntentionFirMovie = 4,  // 电影 ///
    CloudIntentionFirMusic = 5,  // 音乐 ///
    CloudIntentionFirGame = 6,  // 游戏 ///
    CloudIntentionFirExpression = 7,  // 表情 ///
    CloudIntentionFirApp = 8,  // APP ///
    //
};

// 云端意图-主线-二级tab分类 ///
enum CloudIntentionTypeSecondLevel : Uint32 {
    CloudIntentionSecNone = 0, // 无意义，占位 ///
    CloudIntentionSecBaiduBaike = 1, // 百度百科 ///(对应一级:CloudIntentionFirBaiduBaike)
    CloudIntentionSecMap = 2, // 地图服务 //(对应一级:CloudIntentionFirLifeServices)
    CloudIntentionSecWeather = 3, // 天气卡片 ///(对应一级:CloudIntentionFirLifeServices)
    CloudIntentionSecRate = 4,  // 汇率卡片 ///(对应一级:CloudIntentionFirLifeServices)
    CloudIntentionSecLottery = 5,  // 彩票卡片 ///(对应一级:CloudIntentionFirLifeServices)
    CloudIntentionSecTakeout = 6,  // 外卖卡片 ///(对应一级:CloudIntentionFirLifeServices)
    CloudIntentionSecStar = 7,  // 星座卡片 ///(对应一级:CloudIntentionFirLifeServices)
    CloudIntentionSecZodiacSign = 8,  // 生肖卡片 ///(对应一级:CloudIntentionFirLifeServices)
    CloudIntentionSecNovel = 9,  // 小说卡片 ///(对应一级:CloudIntentionFirNovel)
    CloudIntentionSecMovie = 10,  // 电影卡片 ///(对应一级:CloudIntentionFirMusic)
    CloudIntentionSecMusic = 11,  // 音乐卡片 ///(对应一级:CloudIntentionFirMusic)
    CloudIntentionSecGame = 12,  // 游戏(待定，一期不需要) ///(对应一级:CloudIntentionFirGame)
    CloudIntentionSecExpression = 13,  // 表情 ///(对应一级:CloudIntentionFirExpression)
    CloudIntentionSecApp = 14,  // APP ///(对应一级:CloudIntentionFirApp)
    //
};
///客户端职责信息类 ///
class DutyInfo;
/// @brief 个性短语分组类
///
class PhraseGroup {
public:
    /// @brief 获取个性短语分组编号
    /// @param   void
    /// @return   个性短语分组的编号
    ///
    virtual Uint32 group_id() const = 0;
    /// @brief 获取个性短语分组名字长度
    /// @param   void
    /// @return   个性短语分组名字长度
    ///
    virtual Uint32 name_len() const = 0;
    /// @brief 获取个性短语分组名字
    /// @param   void
    /// @return   个性短语分组名字
    ///
    virtual const Uint16* name_buff() const = 0;
    /// @brief 获取个性短语分组是否打开的状态
    /// @param   void
    /// @return 返回值为true，表示该个性短语分组打开，返回值为false，表示该个性短语分组关闭
    ///
    virtual bool is_open() const = 0;
    /// @brief 获取个性短语分组里的元素个数
    /// @param   void
    /// @return 返回值为该个性短语分组里的元素个数
    ///
    virtual Uint32 item_cnt() const = 0;
};

///@brief  个性短语项目类
///
class PhraseItem {
public:
    /// @brief 获取所示分组编号
    /// @param   void
    /// @return 返回值为该个性短语所在分组编号
    ///
    virtual Uint32 group_id() const = 0;
    /// @brief 获取个性短语显示位置(0~9,0表示默认位置)
    /// @param   void
    /// @return 返回值为显示位置
    ///
    virtual Uint32 pos() const = 0;
    /// @brief 获取个性短语编码长度
    /// @param   void
    /// @return 个性短语编码长度
    ///
    virtual Uint32 code_len() const = 0;
    /// @brief 获取个性短语编码buff
    /// @param   void
    /// @return 个性短语编码buff指针
    ///
    virtual const Uint8* code_buff() const = 0;
    /// @brief 获取个性短语内容长度
    /// @param   void
    /// @return 个性短语内容长度
    ///
    virtual Uint32 word_len() const = 0;
    /// @brief 获取个性短语内容
    /// @param   void
    /// @return 个性短语内容
    ///
    virtual const Uint16* word_buff() const = 0;
};

#ifdef IPT_FEATURE_PC_ONLY
///PC版个性短语对象 ///
struct PC_PhraseItem {
    std::u16string key;
    std::u16string value;
    std::u16string show;
    Uint32 pos;
};

#endif

///@brief  细胞词库头信息类
///
class CellInfo {
public:
    /// @brief 获取cell_id编号
    /// @param   void
    /// @return cell_id编号
    ///
    virtual Uint32 cell_id() const = 0;
    /// @brief 获取server_id编号
    /// @param   void
    /// @return server_id编号
    ///
    virtual Uint32 server_id() const = 0;
    /// @brief 获取词数量
    /// @param   void
    /// @return  词数量
    ///
    virtual Uint32 ci_count() const = 0;
    /// @brief 获取词库打开状态
    /// @param   void
    /// @return  true 打开状态，false 关闭状态
    ///
    virtual bool is_open() const = 0;
    /// @brief 获取安装包类型
    /// @param   void
    /// @return 0代表覆盖安装, 1代表升级安装
    ///
    virtual Uint32 data_type() const = 0;
    /// @brief 获取升级后版本号
    /// @param   void
    /// @return 升级后版本号
    ///
    virtual Uint32 inner_ver() const = 0;
    /// @brief 获取升级前版本号
    /// @param   void
    /// @return 升级前版本号
    ///
    virtual Uint32 inner_ver_from() const = 0;
    /// @brief 获取版本号1
    /// @param   void
    /// @return 版本号1
    ///
    virtual Uint32 ver1() const = 0;
    /// @brief 获取版本号2
    /// @param   void
    /// @return 版本号2
    ///
    virtual Uint32 ver2() const = 0;
    /// @brief 获取版本号3
    /// @param   void
    /// @return 版本号3
    ///
    virtual Uint32 ver3() const = 0;
    /// @brief 获取类型1
    /// @param   void
    /// @return 类型1
    ///
    virtual Uint32 type1() const = 0;
    /// @brief 获取类型2
    /// @param   void
    /// @return 类型2
    ///
    virtual Uint32 type2() const = 0;
    /// @brief 获取类型3
    /// @param   void
    /// @return 类型3
    ///
    virtual Uint32 type3() const = 0;
    /// @brief 获取词库名字段长度
    /// @param   void
    /// @return 词库名字段长度
    ///
    virtual Uint32 name_len() const = 0;
    /// @brief 获取作者字段长度
    /// @param   void
    /// @return 作者字段长度
    ///
    virtual Uint32 author_len() const = 0;
    /// @brief 获取关键词字段长度
    /// @param   void
    /// @return 关键词字段长度
    ///
    virtual Uint32 keyword_len() const = 0;
    /// @brief 获取词库信息字段长度
    /// @param   void
    /// @return 词库信息字段长度
    ///
    virtual Uint32 info_len() const = 0;
    /// @brief 获取词库名字段内容
    /// @param   void
    /// @return 词库名字段内容
    ///
    virtual const Uint16* name_buf() const = 0;
    /// @brief 获取作者字段
    /// @param   void
    /// @return 作者字段
    ///
    virtual const Uint16* author_buf() const = 0;
    /// @brief 获取关键词字段
    /// @param   void
    /// @return 关键词字段
    ///
    virtual const Uint16* keyword_buf() const = 0;
    /// @brief 获取词库信息字段
    /// @param   void
    /// @return 词库信息字段
    ///
    virtual const Uint16* info_buf() const = 0;
public:
    /// @brief 获取地理位置词库类型
    /// @param   void
    /// @return 0-普通，1-临时地理位置， 2-常居地理位置
    ///
    virtual const Uint16 loc_type() const = 0;
    /// @brief 获取是否隐藏
    /// @param   void
    /// @return 0-不隐藏 1-隐藏
    ///
    virtual const Uint16 is_hide() const = 0;
    /// @brief 获取安装时间
    /// @param   void
    /// @return 安装时间
    ///
    virtual const Uint32 install_time() const = 0;
};
///@brief  关键词头信息类
///
class KwdCellInfo {
public:
    /// @brief 获取cell_id编号
    /// @param   void
    /// @return Uint32 cell_id编号
    ///
    virtual Uint32 cell_id() const = 0;
    /// @brief 获取server_id编号
    /// @param   void
    /// @return Uint32 server_id编号
    ///
    virtual Uint32 server_id() const = 0;
    /// @brief 获取关键词数量
    /// @param   void
    /// @return Uint32 关键词数量
    ///
    virtual Uint32 kwd_count() const = 0;
    /// @brief 获取词库是否打开状态
    /// @param   void
    /// @return bool true -打开，false-关闭
    ///
    virtual bool is_open() const = 0;
    /// @brief 获取 安装包类型
    /// @param   void
    /// @return Uint32 0代表覆盖安装, 1代表升级安装
    ///
    virtual Uint32 data_type() const = 0;
    /// @brief 获取升级后版本号
    /// @param   void
    /// @return Uint32 升级后版本号
    ///
    virtual Uint32 inner_ver() const = 0;
    /// @brief 获取升级前版本号
    /// @param   void
    /// @return Uint32 升级前版本号
    ///
    virtual Uint32 inner_ver_from() const = 0;
public:
    /// @brief 获取版本号1
    /// @param   void
    /// @return 版本号1
    ///
    virtual Uint32 ver1() const = 0;
    /// @brief 获取版本号2
    /// @param   void
    /// @return 版本号2
    ///
    virtual Uint32 ver2() const = 0;
    /// @brief 获取版本号3
    /// @param   void
    /// @return 版本号3
    ///
    virtual Uint32 ver3() const = 0;
public:
    /// @brief 获取类型1
    /// @param   void
    /// @return 类型1
    ///
    virtual Uint32 type1() const = 0;
    /// @brief 获取类型2
    /// @param   void
    /// @return 类型2
    ///
    virtual Uint32 type2() const = 0;
    /// @brief 获取类型3
    /// @param   void
    /// @return 类型3
    ///
    virtual Uint32 type3() const = 0;
    /// @brief 获取类型4
    /// @param   void
    /// @return 类型4
    ///
    virtual Uint32 type4() const = 0;
};

///@brief   idm头信息类
///
class IdmCellInfo {
public:
    /// @brief 获取cell_id编号
    /// @param   void
    /// @return Uint32 cell_id编号
    ///
    virtual Uint32 cell_id() const = 0;
    /// @brief 获取server_id编号
    /// @param   void
    /// @return Uint32 server_id编号
    ///
    virtual Uint32 server_id() const = 0;
    /// @brief 获取idm数量
    /// @param   void
    /// @return Uint32 idm数量
    ///
    virtual Uint32 idm_count() const = 0;
    /// @brief 词库是否打开
    /// @param   void
    /// @return bool true-打开 false-没打开
    ///
    virtual bool is_open() const = 0;

public:
    /// @brief 获取安装包类型
    /// @param   void
    /// @return Uint32 0代表覆盖安装, 1代表升级安装
    ///
    virtual Uint32 data_type() const = 0;
    /// @brief 获取升级后版本号
    /// @param   void
    /// @return Uint32 升级后版本号
    ///
    virtual Uint32 inner_ver() const = 0;
    /// @brief 获取升级前版本号
    /// @param   void
    /// @return Uint32 升级前版本号
    ///
    virtual Uint32 inner_ver_from() const = 0;
    /// @brief 获取版本号
    /// @param   void
    /// @return Uint32 版本号
    ///
    virtual Uint32 ver() const = 0;
    /// @brief 获取类型
    /// @param   void
    /// @return Uint32 类型
    ///
    virtual Uint32 type() const = 0;
};

class AiCorrectInfo {
public:
    /// @brief  获取被纠错词在原文中的起始位置(含)
    /// @param   void
    /// @return Uint32  被纠错词在原文中的起始位置
    virtual Uint32 error_begin() const = 0;
    /// @brief  获取被纠错词在原文中的结束位置(含)
    /// @param   void
    /// @return Uint32  被纠错词在原文中的结束位置
    virtual Uint32 error_end() const = 0;
    /// @brief  获取被纠错词对应的原文文本的长度
    /// @param   void
    /// @return Uint32  被纠错词对应的原文文本的长度
    virtual Uint32 error_uni_len() const = 0;
    /// @brief  获取被纠错词对应的原文文本
    /// @param   void
    /// @return Uint16*  被纠错词对应的原文文本
    virtual const Uint16* error_uni() const = 0;
    /// @brief  获取纠错结果长度
    /// @param   void
    /// @return Uint32  纠错结果长度,删除长度为0
    virtual Uint32 correct_uni_len() const = 0;
    /// @brief  获取纠错结果内容
    /// @param   void
    /// @return Uint16*  纠错结果内容
    virtual const Uint16* correct_uni() const = 0;
};


/// Sug广告的样式 ///
class SugAdIconStyle {
public:
    enum SugAdIconType : Uint32 {
        TYPE_ICON_NONE = 0,    ///< 无图标
        TYPE_ICON_WENZI,     ///< 文字+背景+icon
        TYPE_ICON_IMAGE,   ///< 纯图片
    };
public:
    // 图标样式 ///
    virtual SugAdIconType ad_icon_type() const = 0;
    // Sug, 纯图片的icon展示 //
    virtual const Uint8* ad_icon_image_url() const = 0;
    virtual Uint32 ad_icon_image_url_len() const = 0;
    // Sug, 文字的icon展示 //
    // Sug光标小图标的文字- 文字-展示文字 ///
    virtual const Uint16* ad_icon_wenzi_uni() const = 0;
    virtual Uint32 ad_icon_wenzi_uni_len() const = 0;
    // Sug光标小图标的文字- 文字- 文字颜色///
    virtual Uint32 ad_color_wenzi_icon_uni() const = 0;
    // Sug光标小图标的文字- 文字- 背景颜色///
    virtual Uint32 ad_color_wenzi_icon_background() const = 0;
    /// 小图标的url(现在用于Sug) ///
    virtual const Uint8* ad_icon_wenzi_image_url() const = 0;
    virtual Uint32 ad_icon_wenzi_image_url_len() const = 0;
    // Sug光标小图标的文字- 文字- 图标背景色///
    virtual Uint32 ad_color_wenzi_icon_image() const = 0;
};

/// @brief  sug新增业务场景
enum SugSourceType : Uint32 {
    SUGSOURCETYPE_WISE = 0, /// 浏览器搜索框来源 ///
    SUGSOURCETYPE_APP = 1, /// 应用商店搜索框来源 ///
    SUGSOURCETYPE_SHOPPING = 2, /// 电商搜索框来源 ///
    SUGSOURCETYPE_OTHER = 3
};

///@brief  客户端Cand条信息
///

class CandInfo {
public:
    ///@brief  CandFlag 枚举
    ///
    enum CandFlag : Uint32 {
        CANDFLAG_NONE = 0,          ///< 无角标
        CANDFLAG_CONTACT = 0x1,     ///< 联系人角标
        CANDFLAG_CLOUD_CACHE = 0x2, ///< 云缓存角标
        CANDFLAG_CLOUD = 0x4,       ///< 云角标
        CANDFLAG_TRIANGLE = 0x8,    ///< 三角形角标(以前的逐词和二元整句，以后只标识本地二元整句)
        CANDFLAG_CORRECT = 0x10,    ///< 读音纠错
        CANDFLAG_HW = 1 << 5,       ///< 手写
        CANDFLAG_UNDER_LINE = 1 << 6,    ///< 下划线
        CANDFLAG_CLOUD_CACHE_REPLACE = 1 << 7,    ///< 云结果替换本地词（所有云结果替换本地结果都用这个标志）
        CANDFLAG_NNRANK = 1 << 8,    ///< nnrank重排結果
        CANDFLAG_IEC = 1 << 9,           /// < 纠错候选标记
        CANDFLAG_LOCAL_WORDBYWORD_PRED = 1 << 10,//本地逐词结果
        CANDFLAG_CLOUD_INLINE = 1 << 11, //cloud_inline云角标
        CANDFLAG_CLOUD_WENXIN = 1 << 12, //文心模型结果
        CANDFLAG_CLOUD_NGRAM = 1 << 13,//云端ngram
        CANDFLAG_CLOUD_NORMAL_KV = 1 << 14,//普通kv
        CANDFLAG_CLOUD_HOT_KV = 1 << 15,//热词kv
        CANDFLAG_CLOUD_WORDBYWORD_PRED = 1 << 16,//云逐词
        CANDFLAG_CLOUD_PREDICT = 1 << 17,//云预测词（向后预测若干个输入码）
        CANDFLAG_CLOUD_ZJ_SHORT_PREDICT = 1 << 18,//移动端短句预测（例如输入：kanzhek，次cand直接出：看着看着）
        CANDFLAG_WENXIN_DUP_REMOVED = 1 << 19,//云词中的文心结果被本地词去重，该标志打在本地词头上
        CANDFLAG_NONWENXIN_DUP_REMOVED = 1 << 20,//非文心云词被本地去重，该标志打在本地词头上
        CANDFLAG_CLOUD_WITH_REPLACE_TAG = 1 << 21,//云结果带有替换标签（有的云结果不一定能真正替换本地词）
        CANDFLAG_CLOUD_SCENE_KV = 1 << 22,//云端场景化kv
        CANDFLAG_ONE_GRAM_ZJ = 1 << 23,//本地一元整句
        CANDFLAG_USR = 1 << 24, //自造词
        CANDFLAG_COMMON_TRIGGER = 1 << 25, // 通用触发词
        CANDFLAG_MAP_TRIGGER = 1 << 26, // 地图触发词
        CANDFLAG_COMMON_QUOTE = 1 << 27, // 灵感语录-常用短语
        CANDFLAG_USRINFO_LIAN = 1 << 28, // 智能表单-个人化信息联想
        CANDFLAG_CLOUD_SUG_HW_RESULT = 1 << 29, // Sug进手写Cand（原有功能，补充标记）
    };

public:
    ///@brief  CandType 枚举
    ///
    enum CandType : Uint32 {
        CANDTYPE_NONE = 0,    ///< 无候选类型
        CANDTYPE_NORMAL = 1,      ///< 普通候选,正常展示汉字即可
        CANDTYPE_CLOUD_ARROW = 2, ///< 云动画箭头 -- 带废弃 //
        CANDTYPE_SERVICE = 3,     ///< 云服务结果
        CANDTYPE_FAST_INPUT = 4,  ///< 快速输入
        //CANDTYPE_MEDIA_POS = 5,   ///< 多媒体-定位
        //CANDTYPE_MEDIA_PIC = 6,   ///< 多媒体-图片
        //CANDTYPE_MEDIA_SOUND = 7, ///< 多媒体-声音
        //CANDTYPE_MEDIA_WRITE = 8, ///< 多媒体-笔记
        CANDTYPE_PC_BRILLIANTWRITING = 5, /// < PC 6号位妙笔生花候选 //
        CANDTYPE_PC_LONGTERM = 6, /// < PC 6号位长句联想候选 //
        CANDTYPE_PC_CALENDAR = 7, /// < PC 6号位农历候选 //
        CANDTYPE_PC_NETADDR = 8, /// < PC 6号位网址链接候选 ///
        CANDTYPE_MEDIA_PEN = 9,   ///< 多媒体-涂鸦 -- 带废弃 //
        CANDTYPE_ZHIDAHAO = 10,    ///< 直达号 -- 带废弃 //
        CANDTYPE_OP = 11,          ///< 运营词
        CANDTYPE_XIEHOUYU = 12,    ///< 歇后语
        CANDTYPE_EMOJI = 13,       ///< 表情
        CANDTYPE_EMOTICON = 14,    ///< 颜文字
        CANDTYPE_EMOJI_LIAN = 15,  ///< 表情联想
        CANDTYPE_EMOTICON_LIAN = 16, ///< 颜文字联想
        CANDTYPE_NIJIGEN = 17,      ///< 二次元输入
        CANDTYPE_CONTACT_LIAN = 18, ///< 联系人联想
        CANDTYPE_USER_PASTE = 19, ///< 上层获取的剪切板数据
        CANDTYPE_USER_AUDIO = 20, ///< 上层获取的语音数据
        CANDTYPE_USER_OCR = 21, ///< 上层获取的OCR数据
        CANDTYPE_VOICE_EGG = 22,  ///< 语音联想彩蛋
        CANDTYPE_CLOUD_LIAN = 23, ///< 云输入联想
        CANDTYPE_CLOUD_FORCAST_LIAN = 24, ///< 云输入通过预测得到的联想
        CANDTYPE_AUTO_REPLY = 25, ///< 智能回复
        CANDTYPE_TOP_PROMOTION = 26, ///< PC平台6号推荐位数据 -- 带废弃 //
        ///< 移动端的整句预测（包含输入前预测和输入后预测，输入后预测：zaiganmane，次cand直接出：在干嘛呢吃饭了吗）
        CANDTYPE_CLOUD_ZJ_FORCAST = 27,
        CANDTYPE_CLOUD_PREFETCH = 28, ///< 云输入预取
        CANDTYPE_SMART_SEARCH = 29, ///< 云输入智能搜索
        CANDTYPE_FIXTERM = 30,            ///< 固守词库标识
        CANDTYPE_VMODE = 31,               ///< V模式的标识
        CANDTYPE_CLOUD_EMOJI = 32,        ///< 云输入表情
        CANDTYPE_FROM_LIST = 33,          ///点击list, 上屏文字, 类型是这个
        CANDTYPE_ZJ_PRE_HINT_LOCAL = 34,  ///< 整句前序本地词
        CANDTYPE_SERVICE_LIAN = 35, ///< 云输入联想服务
        CANDTYPE_NLP_FORCAST = 36, //输入后整句结果 (AI助聊输入前预测结果)
        CANDTYPE_NLP_COMPOOSE = 37, ///< Ai撰写-来自聊天模型
        CANDTYPE_NLP_CORRECTION = 38, ///< 智能云词纠错
        CANDTYPE_CHAIZI = 39, ///< 智能云词生僻字
        CANDTYPE_CALC = 40,  ///< 计算器结果
        CANDTYPE_CALC_EQUATION = 41,  ///< 计算器等式
        CANDTYPE_CLOUD_CAMPAIGN = 42,     ///智能云词活动
        CANDTYPE_NLP_FUNCHAT_RAP = 43,    ///AI趣聊RAP
        CANDTYPE_NLP_FUNCHAT_ACROSTIC = 44,  ///AI趣聊藏头诗
        CANDTYPE_NLP_FUNCHAT_MODERNPOERTY = 45, //现代诗
        CANDTYPE_AI_CORRECT_ENTRY = 46,  /// AiCand上的AI校对入口///
        CANDTYPE_AIPEITU_PREFERENCE = 47,///次Cand上的神句配图开关 ///
        CANDTYPE_SHOP_SCENE_PREFERENCE = 48,///次Cand上购物评价的开关 ///
        CANDTYPE_AI_HINT = 49,         //次Cand上没有任何候选词时展示（最低优先级）///
        CANDTYPE_NLP_COMPOOSE_SHOP = 50, ///< Ai撰写-购物评论模型
        CANDTYPE_CLOUD_PLACEHOLDER = 51, ///云输入占位符，直接出现在候选条，内容无意义。同时点击无效果
        CANDTYPE_CLOUD_INLINE = 52, ///云输入直接出现在候选条
        CANDTYPE_NOT_USED = 53, ///<暂时未使用 ///
        CANDTYPE_AI_COMMON_HIGTEST_PRIORITY = 54, // < 次cand通知，由端上设置，内核在次cand最高优展示,内容和逻辑端上控制 ///
        CANDTYPE_NLP_COMPOOSE_POST = 55, ///<Ai撰写-发布模型
        CANDTYPE_NAME_MODE = 56, ///<查找人名提示、PC人名模式入口提示///
        CANDTYPE_EMOJI_LIAN_MULTI = 57, ///<emoji自动倍数上屏（现展示在主cand上）
        CANDTYPE_AI_KEYBOARD_CLOSE_SWTICH = 58,  ////<次cand上 收键盘开关（四期-AI智能联想时废弃）
        CANDTYPE_CALC_MAX_LENGTH = 59, ///<计算器超长提示
        CANDTYPE_AI_COMPOSE_PREFERENCE = 60, ///< Ai创作-是否展示开关
        CANDTYPE_SHUANGPIN_KEY = 61,///<PC全双拼混输下真实双拼码，用户不可点击
        CANDTYPE_PC_NAME_MODE_EXIT = 62,  ///< PC人名模式出口提示///
        CANDTYPE_PH_PHRASE = 63,  ///<个性短语，不包括拆字、品牌词，MAC使用///
        CANDTYPE_CANCEL_AICAND_INSERT = 64, ///<点击次cand上屏后触发撤销操作 ///
        CANDTYPE_NLP_MMODEL = 65, /// <AI智能联想mmodel模型
        CANDTYPE_AICAND_CONTENT_HINT = 66, ///< 正文框次cand默认底纹词(最低优先级) ///
        CANDTYPE_AICAND_INTENTION = 67, /// << 意图模型 ///
        CANDTYPE_AICAND_HIGHEQ_NOTIFY = 68, /// <<< 高情商常驻提示 ///
        CANDTYPE_RECENT_RARE_ZI = 69, /// <<< 最近常用的生僻字 ///
        CANDTYPE_SYS_RARE_ZI = 70, /// <<< 生僻字面板（拆字/手写）查出的系统生僻字 ///
        CANDTYPE_PC_SENT_FORCAST = 71, /// <<< PC整句预测结果 ///
        CANDTYPE_COMMON_TRIGER_WORDS = 72, /// <<< 通用触发词
        CANDTYPE_MAP_TRIGGER_WORDS = 73, /// <<< 地图触发词
        CANDTYPE_CLOUD_TRIGGER_WORDS = 74, /// <<< 云端触发词
        CANDTYPE_CLOUD_INTENTION = 75, /// <<< 云端意图(OEM) ///
        CANDTYPE_COMMON_QUOTE = 76, /// <<< 常用短语功能 ///
        CANDTYPE_USRINFO_LIAN = 77, /// <<< 智能表单功能 - 个人化信息联想 ///
    };
    ///@brief  COMPOSE_SOURCE 枚举
    ///
    enum COMPOSE_SOURCE : Uint32 {
        COMPOSE_SOURCE_AUTO = 0, // 算法自动判断
        COMPOSE_SOURCE_ACCURATE = 1, // 重点场景
        COMPOSE_SOURCE_FUZZY = 2// 非重点场景
    };
    ///@brief  帮写和趣聊 反馈时要回传的来源信息 ///
    ///
    enum ComposeSrc : Uint32 {
        COMPOSE_SRC_NLP = 0, // nlp
        COMPOSE_SRC_WHITELIST = 1, // 白名单
    };
    ///
    ///@brief  AI_PAD_FUNCHAT_TYPE 枚举
    /// AI趣聊 RAP的类型
    enum AiFunCharRapType : Uint32 {
        AI_FUNCHAT_RAP_NONE = 0, /// 当前不是AI趣聊的RAP类型///
        AI_FUNCHAT_RAP_SINGLE = 1, /// RAP - 单押///
        AI_FUNCHAT_RAP_DOUBLE = 2, /// RAP - 双押 ///
        AI_FUNCHAT_RAP_TRIPLE = 3, /// RAP - 三押 ///
    };
    /// AI智能联想 文心结果文本类型 ///
    ///@brief
    enum AiWenxinTextType : Uint32 {
        AUTO_TEXT = 0, /// 未知 ///
        LONG_TEXT = 1, /// 长文本///
        SHORT_TEXT = 2, /// 短文本 ///
    };
    /// AI智能联想 文心结果机器人类型 ///
    ///@brief
    enum AiWenxinRobotType {
        COMMON = 0,   // 0 通用
        WOMAN = 1,   // 1 叶悠悠
        MAN = 2,      // 2 林开开
    };
public:
    ///@brief  ServiceType 枚举
    ///
    enum ServiceType : Uint32 {
        SERVICE_TYPE_NONE = 0, ///< 此类型说明资源为nullptr，不参与资源匹配
        SERVICE_TYPE_HOLDER = 1, ///<  place holder 留空占位
        SERVICE_TYPE_CANDIDATE = 2, ///<  candidate type 候选类型，此类型会让正常的云输入结果填充
        SERVICE_TYPE_TEXT = 3, ///<  plaint text 普通文本（文字彩蛋）
        SERVICE_TYPE_MOVIE = 4, ///<  movie resource  电影资源
        SERVICE_TYPE_SMILIES = 5, ///<  face expression 表情符号
        SERVICE_TYPE_EMOTICONS = 6, ///<  cloud emoticons 颜文字
        SERVICE_TYPE_IMAGE = 7, ///<  image 图片
        SERVICE_TYPE_MAGIC_TEXT = 8, ///<  ios特技字体
        SERVICE_TYPE_SUG = 9, ///<  sug
        SERVICE_TYPE_SUG_CUSTOM = 10, ///<  sug自定义
        SERVICE_TYPE_SUG_AD = 11, ///<广告Sug
        SERVICE_TYPE_SUG_AD_WANGMENG_APP = 12, ///<网盟SUG、穿山甲SUG共用 应用类型广告
        SERVICE_TYPE_END, ///< ServiceType数量

        SERVICE_TYPE_AI_BEGAN = 100,
        SERVICE_TYPE_AI_REPLY = 101, ///< 智能回复
        SERVICE_TYPE_AI_INTENT = 102, ///< 智能回复-意图理解
        SERVICE_TYPE_AI_EMOJI = 103, ///< 智能回复-emoji表情
        SERVICE_TYPE_AI_QUERY_KEY = 104, ///< 智能回复-问题
        SERVICE_TYPE_AI_END = 200,

        CLOUD_OUTPUT_SERVICE_TYPE_JSON = 201,
    };
    //
public:
    /// @brief 如果CandType是CANDTYPE_NLP_FUNCHAT_RAP,区分类型
    /// @param   void
    /// @return COMPOSE_SOURCE枚举类型 撰写来源
    ///
    virtual AiFunCharRapType rap_type() const = 0;
    /// @brief 获取候选模式(能量模式),正负能量
    /// @param   void
    /// @return 正负能量
    ///
    virtual AiEnergyType energy_type() const = 0;
    /// @brief 获取候选项类型
    /// @param   void
    /// @return CandType枚举类型 候选项类型
    ///
    virtual CandType type() const = 0;
    /// @brief 获取候选项标记
    /// @param   void
    /// @return Uint32 候选项标记
    ///
    virtual Uint32 flag() const = 0;
    /// @brief 获取候选项长度
    /// @param   void
    /// @return Uint32 候选项长度
    ///
    virtual Uint32 uni_len() const = 0;
    /// @brief  获取候选项内容
    /// @param   void
    /// @return Uint16*  候选项内容
    ///
    virtual const Uint16* uni() const = 0;
    /// @brief 获取emoji触发词的长度
    /// @param   void
    /// @return Uint32 emoji触发词的长度
    ///
    virtual Uint32 emoji_query_len() const = 0;
    /// @brief  获取emoji触发词
    /// @param   void
    /// @return Uint16*  emoji触发词
    ///
    virtual const Uint16* emoji_query() const = 0;
    /// @brief  获取拼音长度
    /// @param   void
    /// @return Uint32  拼音长度
    ///
    virtual Uint32 pinyin_len() const = 0;
    /// @brief  获取拼音内容
    /// @param   void
    /// @return Uint16*  拼音内容
    ///
    virtual const Uint16* pinyin_uni() const = 0;
    /// @brief  获取correct_info
    /// @param   void
    /// @return  py_correct_info*
    ///
    virtual const py_correct_info* correct_info(void) const = 0;
    /// @brief  获取服务信息类型
    /// @param   void
    /// @return  服务信息类型
    ///
    virtual ServiceType service_type() const = 0;
    /// @brief  获取服务中img和url长度
    /// @param   void
    /// @return  Uint32 服务中img和url长度
    ///
    virtual Uint32 service_img_url_len() const = 0;
    /// @brief  获取服务中img和url
    /// @param   void
    /// @return  Uint16* 服务中img和url
    ///
    virtual const Uint8* service_img_url() const = 0;
    virtual const s_Rect_v2* service_img_rect() const = 0;
    ///
    virtual Uint32 service_goto_url_len() const = 0;
    virtual const Uint8* service_goto_url() const = 0;
    //
    virtual Uint32 service_extra_len() const = 0;
    virtual const Uint16* service_extra() const = 0;
    /// @brief  判断候选词是否为联想词
    /// @param   void
    /// @return  bool true-是联想词 false-非联想词
    ///
    virtual bool is_lian() const = 0;
    /// @brief  判断sug是否包含卡片
    /// @param   void
    /// @return  bool true-是 false-否
    ///
    virtual bool is_contains_sug_card() const = 0;
    /// @brief  获取service_content 长度
    /// @param   void
    /// @return Uint32 长度
    ///
    virtual Uint32 service_content_len() const = 0;
    /// @brief  获取service_content 内容
    /// @param   void
    /// @return Uint8* 内容
    ///
    virtual const Uint8* service_content() const = 0;

    virtual Uint32 blue_len() const = 0;
    virtual Uint32 his_len() const = 0;
    /// @brief  获取云输入uid
    /// @param   void
    /// @return Uint8* 云输入uid
    ///
    virtual const Uint8* uid() const = 0;
    /// @brief  获取云输入uid长度
    /// @param   void
    /// @return Uint32 云输入uid长度
    ///
    virtual Uint32 uid_len() const = 0;
    /// @brief  获取活动/帮写/趣聊入口样式
    /// @param   void
    /// @return CLOUD_IN_STYLE 入口样式
    ///
    virtual CLOUD_IN_STYLE  cloud_in_style() const = 0;
    /// @brief  获取活动/帮写/趣聊/本地通用触发词的入口文本
/// @param   void
/// @return Uint8* 入口文本
///
    virtual const Uint16* cloud_in_text() const = 0;
    /// @brief  获取活动/帮写/趣聊/本地通用触发词的入口文本长度
    /// @param   void
    /// @return Uint32 入口文本长度
    ///
    virtual Uint32 cloud_in_text_len() const = 0;
    /// @brief  获取活动/帮写/趣聊/本地通用触发词的入口图片
    /// @param   void
    /// @return Uint8* 入口图片
    ///
    virtual const Uint8* cloud_in_image() const = 0;
    /// @brief  获取活动/帮写/趣聊/本地通用触发词的入口图片长度
    /// @param   void
    /// @return Uint32 入口图片长度
    ///
    virtual Uint32 cloud_in_image_len() const = 0;
    /// @brief  获取暗色模式本地通用触发词的入口图片
    /// @param   void
    /// @return Uint8* 入口图片
    ///
    virtual const Uint8* dark_mode_cloud_in_image() const = 0;
    /// @brief  获取暗色模式本地通用触发词的入口图片长度
    /// @param   void
    /// @return Uint32 入口图片长度
    ///
    virtual Uint32 dark_mode_cloud_in_image_len() const = 0;
    /// @brief  获取入口图片md5
    /// @param   void
    /// @return Uint8* 入口图片md5
    ///
    virtual const Uint8* cloud_in_image_md5() const = 0;
    /// @brief  获取入口图片md5长度
    /// @param   void
    /// @return Uint32 入口图片md5长度
    ///
    virtual Uint32 cloud_in_image_md5_len() const = 0;
    /// @brief  获取活动入口类型
    /// @param   void
    /// @return Uint8* 活动入口类型
    ///
    virtual const Uint8* cloud_resource_click_type() const = 0;
    /// @brief  获取活动入口类型长度
    /// @param   void
    /// @return Uint32 活动入口类型长度
    ///
    virtual Uint32 cloud_resource_click_type_len() const = 0;
    /// @brief  获取活动入口信息
    /// @param   void
    /// @return Uint8* 活动入口信息
    ///
    virtual const Uint8* cloud_resource_target_info() const = 0;
    /// @brief  获取活动入口信息长度
    /// @param   void
    /// @return Uint32 活动入口信息长度
    ///
    virtual Uint32 cloud_resource_target_info_len() const = 0;
    /// @brief  获取云活动的资源id
    /// @param   void
    /// @return Uint64 云活动的资源id
    /// @
    virtual Uint64 cloud_campaign_resource_id() const = 0;
    /// @brief  获取Ai校对-纠错信息项目数量 ///
    /// @param   void
    /// @return Uint32 纠错信息项目数量
    virtual Uint32 get_correct_info_cnt() const = 0;
    /// @brief  获取Ai校对-纠错信息项目///
    /// @param   idx  项目下标
    /// @return Uint32 纠错信息项目
    virtual const AiCorrectInfo* get_correct_info(Uint32 idx) const = 0;
    /// @brief  获取Ai数据-数据来源///
    /// @return ComposeSrc 包括白名单和NLP
    virtual ComposeSrc compose_src() const = 0;
    ///获取PC妙笔生花的候选词长度///
    virtual Uint32 pc_brilliant_key_len() const = 0;
    ///获取PC妙笔生花的候选词///
    virtual const Uint16* pc_brilliant_key_str() const = 0;
    // AI智能联想，获取意图模型的值 ///
    virtual Uint32 intention_style() const = 0;
    // AI智能联想，获取意图模型的来源 ///
    virtual Uint32 intention_src() const = 0;
    /// 目前通用触发词使用，图标是否刷色
    virtual bool link_icon_color() const = 0;
    /// 目前通用触发词使用，触发词的id
    virtual Uint64 item_id() const = 0;
    // 云端触发词端上埋点使用，展示的云端触发词的show_type ///
    virtual TriggerCloudShowType trigger_show_type() const = 0;
    // 云端触发词端上埋点使用，展示的云端触发词点击后会带入哪个tab ///
    virtual Uint32 trigger_tab_id() const = 0;
    // 云端意图的具体类型 - OPPO使用///
    virtual CloudIntentionType cloud_intention_type() const = 0;
    // 云端意图的一级tab - 主线使用///
    virtual CloudIntentionTypeFirstLevel cloud_intention_type_first_level() const = 0;
    // 云端意图的二级tab - 主线使用///
    virtual CloudIntentionTypeSecondLevel cloud_intention_type_second_level() const = 0;
    // 云端意图具体类型-荣耀使用 ///
    virtual CloudSlotIntentionType cloud_slot_intention_type() const = 0;
    // AI助聊意图端上埋点使用，展示的意图的requset_id ///
    virtual const Uint8* intention_requset_id() const = 0;
    // 展示的意图requset_id的长度
    virtual Uint32 intention_requset_id_len() const = 0;

    // 以下通用触发词用
    virtual const Uint8* bg_img() const = 0;     // 背景图片
    virtual Uint32 bg_img_len() const = 0;
    virtual const Uint8* bg_color() const = 0;   // 背景色
    virtual Uint32 bg_color_len() const = 0;
    virtual const Uint8* bg_color2() const = 0;   // 背景色2
    virtual Uint32 bg_color2_len() const = 0;
    virtual const Uint8* words_color() const = 0; // 文字颜色
    virtual Uint32 words_color_len() const = 0;
    virtual Uint32 bg_style_type() const = 0;   // 背景样式
    virtual Uint32 words_style_type() const = 0;    // 文字样式
    virtual bool show_ad() const = 0;           // 是否展示广告
    // 用于客户端 通用触发词 埋点 和 新增的意图请求服务端数据query ///
    virtual const Uint16* trigger_word() const = 0; // 触发词
    virtual Uint32 trigger_word_len() const = 0; // 触发词长度
    virtual Int32 trigger_type() const =
        0; // 触发类型 -1: 不是通用触发词，  0: 候选触发， 1： 联想触发
    // 用于客户端 运营活动  埋点，与实际功能无关///
    virtual const Uint16* cloud_campaign_trigger_word() const = 0;
    virtual Uint32 cloud_campaign_trigger_word_len() const = 0;
    // 本地通用触发词的类型，>=1 配置为意图///
    virtual Uint32 trigger_belong_type() const = 0;
    //
    /// Sug的背景色 ///
    virtual Uint32 ad_color_background() const = 0;
    /// 电商Sug广告的icon类型 ///
    virtual const SugAdIconStyle* get_sug_ad_icon_style() const = 0;
    // 是否需要展示广告标识 - 默认带有！///
    virtual bool is_need_show_ad_mark() const = 0;
    /// 网盟SUG展示相关 ///
    virtual bool wm_sug_app_is_laxin() const = 0;     /// 是否是拉新包         ///
    virtual bool wm_sug_app_is_act() const = 0;       /// 是否是促活包         ///
    virtual bool wm_sug_app_is_show_ad() const = 0;   /// 是否需要展示广告图标 ///
    virtual const char* wm_sug_code_bits() const = 0; /// 网盟SUG代码位        ///
    virtual const char* wm_sug_pkg() const = 0;       /// 网盟SUG APK          ///
    /// 以下接口，客户端可能在展示期需要用到 ///
    /// 网盟SUGPM配置加广告主投放相关 ///
    virtual bool is_wm_sug_app_allow_zhitou() const = 0; /// 网盟SUG是否允许直投包 ///
    virtual bool is_wm_sug_app_allow_channel() const = 0; /// 网盟SUG是否允许渠道包 ///
    virtual bool is_wm_sug_app_allow_act() const = 0; /// 网盟SUG是否允许促活 ///
    // 用于客户端 云端触发词类型，根据意图绘图等功能 ///
    virtual Uint32 cloud_trigger_intention_id() const = 0;
    /// 一个词可能同时是网盟和穿山甲的广告词，所以只能共用
    virtual bool is_from_wm() const = 0; /// 是否要请求网盟接口 ///
    virtual bool is_from_csj() const = 0; /// 是否要请求穿山甲接口（内核封装） ///
    /// Sug的原始unicode，简繁转换之前的，用于向网盟SDK传递query，理论上一定有，但不排除为空的可能性
    virtual const Uint16* sug_ori_uni() const = 0;
    virtual Uint32 sug_ori_uni_len() const = 0; /// 上面字段的长度，可能为0
};

///@brief  sug卡片信息
///
class SugCardInfo {
public:
    ///@brief  枚举SugCardState
    ///
    enum SugCardState {
        STATE_NONE = 0,    ///< 无状态
        STATE_LOADING,     ///< 正在载入
        STATE_LOAD_FAIL,   ///< 载入失败
        STATE_LOAD_SUCCESS ///< 载入成功
    };

public:
    ///@brief  枚举SugCardType
    ///
    enum SugCardType {
        TYPE_NONE = 0,   ///< 未知
        TYPE_APP = 1,    ///< 应用
        TYPE_MUSIC = 2,  ///< 音频
        TYPE_VIDEO = 3,  ///< 视频
        TYPE_BOOK = 4,   ///< 图书
        TYPE_NORMAL = 5, ///< 普通
        TYPE_END         ///< SugCardType数量
    };

public:
    /// @brief  获取sug卡片状态
    /// @param   void
    /// @return SugCardState sug卡片状态
    ///
    virtual SugCardState state() const = 0;

public:
    virtual SugCardType type() const = 0;  ///< 获取sug卡片种类
    virtual Uint32 key_len() const = 0;
    virtual const Uint16* key() const = 0;
    virtual Uint32 title_len() const = 0;
    virtual const Uint16* title() const = 0;
    virtual Uint32 content1_len() const = 0;
    virtual const Uint16* content1() const = 0;
    virtual Uint32 content2_len() const = 0;
    virtual const Uint16* content2() const = 0;
    virtual Uint32 content3_len() const = 0;
    virtual const Uint16* content3() const = 0;
    virtual Uint32 img_url_len() const = 0;
    virtual const Uint8* img_url() const = 0;
    virtual Uint32 icon_url_len() const = 0;
    virtual const Uint8* icon_url() const = 0;
};

///@brief  show信息
///
class ShowInfo {
public:
    ///@brief  枚举ShowAutofixType
    ///
    enum ShowAutofixType : Uint8 {
        TYPE_NONE = 0,          ///< 无纠错
        TYPE_INPUT = 0x1,       ///< 当前输入码输错
        TYPE_LESS = 0x2,        ///< 当前输入码前漏输了
        TYPE_MORE = 0x4,        ///< 当前输入码是多输的
        TYPE_SWAP = 0x8,        ///< 当前输入码是交换的
        TYPE_LESS_BACK = 0x10  ///< 当前输入码后漏输了
    };
    ///@brief  枚举ShowInfoType
    ///
    enum ShowInfoType : Uint32 {
        TYPE_SHOWINFO_NONE = 0,          ///< 未知
        TYPE_NORMAL = 1,      ///< 普通
        TYPE_PINYIN = 2,      ///< 拼音，需要转换声调
        TYPE_USER_PASTE = 3,  ///< 上层获取的剪切板数据
        TYPE_USER_AUDIO = 4,  ///< 上层获取的语音数据
        TYPE_USER_OCR = 5    ///< 上层获取的OCR数据
    };

public:
    ///< uni_len = hz_len + list_len + input_len,3个len有前后关系 ///
    virtual Uint32 uni_len() const = 0;
    virtual const Uint16* uni() const = 0;
public:
    /// @brief  获取汉字长度
    /// @param   void
    /// @return Uint32 汉字长度
    ///
    virtual Uint32 hz_len() const = 0;
    /// @brief  获取list长度
    /// @param   void
    /// @return Uint32 list长度
    ///
    virtual Uint32 list_len() const = 0;
    /// @brief  获取除了已选的list以外的输入码长度
    /// @param   void
    /// @return Uint32 除了已选的list以外的输入码长度
    ///
    virtual Uint32 input_len() const = 0;
public:
    /// @brief  获取showinfo的类型
    /// @param   void
    /// @return showinfo的类型
    ///
    virtual ShowInfoType showinfo_type() const = 0;
    /// @brief  PC移动编辑光标用 ，首选染色的长度
    /// @param   void
    /// @return Uint32 首选染色的长度
    ///
    virtual Uint32 pre_cand_len(void) const = 0;
    virtual void pop_range(Uint32& pop_began, Uint32& pop_end) const = 0;
    /// @brief  PC下在网址框输入码无分隔符显示
    /// @param   void
    /// @return Uint32 无分隔符的输入码长度
    ///
    virtual Uint32 combined_uni_len() const = 0;
    /// @brief  PC下在网址框输入码无分隔符显示
    /// @param   void
    /// @return const Uint16* 无分隔符的输入码首指针
    ///
    virtual const Uint16* combined_uni() const = 0;
    /// @brief  PC人名模式下染色内容长度
    /// @param   void
    /// @return Uint32 染色内容长度
    ///
    virtual Uint32 dye_uni_len() const = 0;
    /// @brief  PC人名模式下染色内容
    /// @param   void
    /// @return const Uint16* PC人名模式下染色内容
    ///
    virtual const Uint16* dye_uni() const = 0;
public:
    //cursor_info[0] = 0
    //cursor_info_len表示从hz_len之后的光标的位置个数，因为hz_len的区域不允许出现光标//
    //cursor_info[i]表示第i个光标之前有几个字符，不包括hz_len//
    virtual Uint32 cursor_info_len() const = 0;
    virtual const Uint8* cursor_info() const = 0;
    /// @brief  当前show的纠错类型信息 与当前show 去除push内容的内容长度一致
    /// @param   void
    /// @return  Uint8* 内容
    ///
    virtual const Uint8* input_autofix_info() const = 0;
public:
    /// @brief  获取光标index
    /// @param   void
    /// @return  Int32 -1表示不可编辑状态，0表示光标处在cursor_info[cursor_idx] + hz_len处
    ///
    virtual Int32 cursor_idx() const = 0;
};
///@brief  联系人item
///
class ContactItem {
public:
    virtual const Uint16* full_name() const = 0;
public:
    /// @brief  获取属性个数
    /// @param   void
    /// @return  Uint32 属性个数
    ///
    virtual Uint32 attri_cnt() const = 0;
    /// @brief  获取属性名和属性值
    /// @param[out]   attri属性
    /// @param[out]   value值
    /// @param[in]  idx index
    /// @return  Uint32
    ///
    virtual Int32 attri_value(Uint16* attri, Uint16* value, Uint32 idx) const = 0;
    virtual Int32 set_select(Uint32 idx, Uint8 is_select) = 0;
    virtual Int32 get_select(Uint32 idx) const = 0;
};

class TriggerItem {
public:
    virtual const Uint16* card_title() const = 0;
    virtual const Uint16* card_icon() const = 0;
    virtual const Uint16* card_desc() const = 0;
    virtual const Uint16* jump_text() const = 0;
    virtual const Uint16* jump_icon() const = 0;
    virtual const Uint16* jump_url() const = 0;
    virtual bool send_btn() const = 0;
};
/// 内核允许地图触发词和通用触发词同时刷新在主cand，意图版本废弃
class MapItem {
public:
    virtual const Uint16* addr_str() const = 0;
    virtual Uint32 addr_len() const = 0;
    virtual const Uint16* name_str() const = 0;
    virtual Uint32 name_len() const = 0;
    virtual const Uint16* uid_str() const = 0;
    virtual Uint32 uid_len() const = 0;
    virtual const Uint16* view_url_str() const = 0;
    virtual Uint32 view_url_len() const = 0;
    virtual const Uint16* share_url_str() const = 0;
    virtual Uint32 share_url_len() const = 0;
};

class IntentionMapItem {
public:
    virtual const Uint16* addr_str() const = 0;
    virtual Uint32 addr_len() const = 0;
    virtual const Uint16* name_str() const = 0;
    virtual Uint32 name_len() const = 0;
    virtual const char* uid_str() const = 0;
    virtual Uint32 uid_len() const = 0;
    virtual const char* view_url_str() const = 0;
    virtual Uint32 view_url_len() const = 0;
    virtual const char* share_url_str() const = 0;
    virtual Uint32 share_url_len() const = 0;
    virtual float longitude() const = 0;
    virtual float latitude() const = 0;
};

//OPPO美食意图使用百度数据源时，返回的数据
class IntentionDelicacyItemBaidu {
public:
    //名称
    virtual const Uint16* name_str() const = 0;
    virtual Uint32 name_len() const = 0;
    //地址
    virtual const Uint16* addr_str() const = 0;
    virtual Uint32 addr_len() const = 0;
    //类别
    virtual const Uint16* tag_str() const = 0;
    virtual Uint32 tag_len() const = 0;
    //uid
    virtual const char* uid_str() const = 0;
    virtual Uint32 uid_len() const = 0;
    //评分
    virtual const char* rating_str() const = 0;
    virtual Uint32 rating_len() const = 0;
    //均价
    virtual const char* price_str() const = 0;
    virtual Uint32 price_len() const = 0;
    //美食页面链接
    virtual const char* url_str() const = 0;
    virtual Uint32 url_len() const = 0;
    //美食logo链接
    virtual const char* logo_url_str() const = 0;
    virtual Uint32 logo_url_len() const = 0;
    //分享卡片的地图logo链接
    virtual const char* share_logo_url_str() const = 0;
    virtual Uint32 share_logo_url_len() const = 0;
    //经纬度
    virtual float longitude() const = 0;
    virtual float latitude() const = 0;
    //距离
    virtual Uint32 distance() const = 0;
};

//OPPO美食意图使用的数据源
enum IntentionDelicacySource {
    INTENTION_DELICACY_NONE = 0,
    INTENTION_DELICACY_MEITUAN = 1,
    INTENTION_DELICACY_BAIDU = 2,
};

class IntentionAppItem {
public:
    virtual bool is_from_wm() const = 0; /// 是否要请求网盟接口 ///
    virtual bool is_from_csj() const = 0; /// 是否要请求穿山甲接口（内核封装） ///
    virtual const char* pkg_str() const = 0;
    virtual Uint32 pkg_len() const = 0;
    virtual const char* code_bits_str() const = 0;
    virtual Uint32 code_bits_len() const = 0;
    virtual bool is_laxin() const = 0;     /// 是否是拉新包 ///
    virtual bool is_act() const = 0;       /// 是否是促活包 ///
    virtual bool is_allow_zhitou() const = 0; /// 是否允许直投包 ///
    virtual bool is_allow_channel() const = 0; /// 是否允许渠道包 ///
    virtual bool is_allow_act() const = 0; /// 是否允许促活 ///
    virtual bool is_hint_when_leave() const = 0; /// 是否在跳离APP时给予提示 ///
};

// 云端意图的卡片数据内容 ///
class IntentionItem {
public:
    virtual CloudIntentionType intention_type() const = 0;
    // 日历意图使用 ///
    virtual Uint64 timeval() const = 0;
    // 天气意图的城市 ///
    virtual const Uint16* city_str() const = 0;
    virtual Uint32 city_len() const = 0;
    // 天气意图的区县 ///
    virtual const Uint16* county_str() const = 0;
    virtual Uint32 county_len() const = 0;
    // 日历意图的节日名称 ///
    virtual const Uint16* festival_str() const = 0;
    virtual Uint32 festival_len() const = 0;
    // 关闭联系人权限时,可能是人名的云端返回, 联系人意图 ///
    virtual Uint32 contact_cnt() const = 0;
    virtual const Uint16* contact_name(Uint32 idx) const = 0;
    virtual Uint32 contact_name_len(Uint32 idx) const = 0;
    // OEM美食意图的点击数据 ///
    virtual IntentionDelicacySource delicacy_source() const = 0;
    // 主线使用，透传点击后传递的query和对应的意图list ///
    virtual const Uint16* query_unis() const = 0; //delicacy_source是美团时获取query_unis
    virtual Uint32 query_unis_len() const = 0;
    virtual const IntentionDelicacyItemBaidu* delicacy_item() const = 0; //delicacy_source是百度时获取delicacy_item
    virtual const IntentionMapItem* map_item() const = 0;
    virtual const IntentionAppItem* app_item() const = 0;
    // 日历意图使用 - 云端时间戳 //
    virtual Uint64 cloud_timestamp() const = 0;
    //
    virtual CloudIntentionTypeFirstLevel first_level() const = 0;
    virtual CloudIntentionTypeSecondLevel second_level() const = 0;
    //
    virtual CloudSlotIntentionType slot_intention_type() const = 0;
    virtual const Uint16* schedule_content() const = 0;
    virtual Uint32 schedule_content_len() const = 0;
};

///@brief  网络相关接口
///由各平台实现,创建释放
///
class NetMan {
public:
    enum Protocol : Uint32 {
        PROTOCOL_HTTPS_POST = 0,///< http post请求
        PROTOCOL_UDP = 1, ///<udp请求
        PROTOCOL_UNKNOWN = 2, ///<未知协议
        PROTOCOL_QUIC = 3, ///<quic请求-直接走内核，不经过上层。PROTOCOL_QUIC
        //
        PROTOCOL_UNDEFINDED = 10000, ///<未定义协议
        /// 同一协议enum定义 ///
        PROTOCOL_TYPE_HTTPS_POST = 0, /** https post请求 **/
        PROTOCOL_TYPE_UDP = 1, /** udp请求 **/
        PROTOCOL_TYPE_UNKNOWN = 2,
        PROTOCOL_TYPE_QUIC = 3, //
    };
    // 不同的网络通道 -- 用于发起请求时，客户端区别内核请求通道，用来记录请求数据信息///
    enum StreamReqType : Uint32 {
        STREAM_CLOUD = 0, // 普通云输入请求 ///
        STREAM_KV = 1, // 云kv -- 预取 (5.30内核版本云预取与普通云输入合并)- 暂时兼容 //
        STREAM_SUG = 2, // sug请求 ///
        STREAM_AICHAT = 3, // AI助聊请求 ///
        STREAM_UPLOAD_DATA = 4, // 上报数据 ///
        STREAM_UPLOAD_DATA_9 = 5, // 上报数据 //
        STREAM_TYPE_MAX = 255, // 不需要关心当前通道 ///
    };
public:
    /// @brief  网络回调接口，需要和网络请求同一线程调用
    ///@brief 只要网络返回了就可以回调，不需要管stream_send的顺序
    /// @param  stream stream_send中的stream
    /// @param   err_code <0表示出错，>=0 表示返回成功
    /// @param  recv_buf 返回数据,UDP请求时为直接的数据,HTTP请求时为返回的body数据
    /// @param recv_buf_len: 返回数据的长度
    /// @return void
    ///
    class StreamCallback {
    public:
        virtual void on_net_recv(void* stream,
                                 Int32 err_code,
                                 Uint8* recv_buf,
                                 Uint32 recv_buf_len) = 0;
    };

public:
    /// @brief 创建网络流
    /// @param url: 需要请求的url
    /// @param port: 需要请求的端口
    /// @param protocol: 需要请求的协议
    /// @param time_out: 超时时间,单位毫秒
    /// @param max_recv_len: 允许返回的最大长度，主要是为了http协议拼接body时，防止数据过大
    /// @param callbck: 网络回调通知网络数据已经返回了
    /// @return: 返回网络流的句柄
    virtual void* stream_create(const char* url,
                                Uint32 port,
                                Protocol protocol,
                                Uint32 time_out,
                                Uint32 max_recv_len,
                                StreamCallback* callbck) = 0;

    /// @brief 网络流发起请求，一个网络流会多次发起请求，
    /// @brief并且会存在在上一个请求没有返回前，再次发起请求
    /// @param stream: 网络流的句柄
    /// @param send: 发送的消息,HTTP请求时,仅仅为http body,http头需要有Content-Length
    /// @param send_len: 发送消息的长度
    //  @param stream_type当前的请求通道， 用于客户端云输入功能率对多通道数据进行区别, 255表示端上不需要关心 ///
    /// @return return >=0 发送成功, < 0表示发送失败
    virtual Int32 stream_send(void* stream,
                              StreamReqType stream_type,
                              Uint8* send,
                              Uint32 send_len) = 0;

    virtual bool is_same_stream(void* stream1, void* stream2) = 0;

    /// @brief 关闭网络流
    /// @param 网络流的句柄
    /// @ return void
    virtual void stream_close(void* stream) = 0;

};

struct NetAddrPort {
    inline NetAddrPort() {
        set_addr(nullptr, 0, NetMan::PROTOCOL_UNDEFINDED);
    }
public:
    inline void set_addr(const char* host_, Uint16 port_, NetMan::Protocol protocal_) {
        host = host_;
        port = port_;
        not_used_uint16 = 0;
        protocal = protocal_;
        not_used_uint64 = 0;
    }
public:
    const char* host; /// // host长度 < 128
    Uint16 port;
    Uint16 not_used_uint16;
    NetMan::Protocol protocal;
    Uint64 not_used_uint64;
};
///
struct NetAddrConfigSet {
    /// https的各地址、端口都不变
    /// quic暂时使用quicolimeok.baidu.com，端口 8041
    /// 正式上线quic使用 udpolimeok.baidu.com 8041
    /// 沙盒使用 cloud-sh.baidu.com 8041
    NetAddrPort cloud; /// 云输入通道 支持 QUIC/UDP/HTTPS（请使用QUIC)
    NetAddrPort sug; /// Sug服务通道 支持 HTTPS（请使用HTTPS)
    NetAddrPort nlp; /// Ai助聊通道 支持 QUIC/HTTPS（请使用QUIC)
    NetAddrPort kv; /// 云KV通道 支持 QUIC/UDP/HTTPS （请使用QUIC)
    NetAddrPort upl_data;/// 上报数据通道 支持 HTTPS（请使用HTTPS)
    /// 保留的空间(如果要增加通道数量，需要确保NetAddrConfigSet结构体大小不变)
    NetAddrPort not_used[11];
};

//#endif

/// @brief 手写模型加载类型
///
enum HwLoadType {
    HWLOADTYPE_NONE = 0,  ///< 未加载模型                      ///
    HWLOADTYPE_FAIL = 1,  ///< 加载模型失败                    ///
    HWLOADTYPE_IMM_HW = 2,  ///< 加载path.imm_hw6()的模型        ///
    LOADTYPE_IMM_FLOW = 3, ///< 加载移动端分流模型 ///
    LOADTYPE_IMM_FLOW_FAIL = 4, /// < load分流模型失败 ////
    LOADTYPE_IMM_WENXIN = 5, ///< 加载移动端文心模型 ///
    LOADTYPE_IMM_WENXIN_FAIL = 6, ///< load文心模型失败 ////
    LOADTYPE_IMM_WENXIN_LOADING = 7, ///< 正在加载文心模型 ///
};

/// @brief 手写笔势识别类型
///
enum HwGestureType {
    HWGESTURETYPE_NONE = 0,  /// 无效笔势 ///
    HWGESTURETYPE_SPLIT = 1,  /// 拆分笔势 ///
    HWGESTURETYPE_CHOOSE = 2,  /// 选中笔势 ///
    HWGESTURETYPE_DELETE = 3,  /// 删除笔势 ///
    HWGESTURETYPE_BREAK = 4,  /// 换行笔势，暂不使用 ///
    HWGESTURETYPE_INSERT_BELOW = 5,  /// 插入笔势,正V,插入下方，暂不使用 ///
    HWGESTURETYPE_INSERT_ABOVE = 6,  /// 插入笔势,倒V,插入上方，暂不使用 ///
};


/// @brief 内核DEBUG LOG 输出
///
enum CoreLogLevel : Uint32 {
    CORE_LOG_ALL = 0,  ///< 打印所有log                        ///
    CORE_LOG_DEBUG = 1,  ///  ///调式信息log
    CORE_LOG_WARNING = 2, ///< 警告信息log       ///
    CORE_LOG_ERROR = 3,  ///<< 错误信息log,如果需要可以带到release版本的包中        ///
    CORE_LOG_NONE = 64,  ///<不打印log                  ///
};

class InputPad;

/// @brief各个平台的环境类，由各平台实现,创建释放
/// @brief设置项类
///
class ConfigItems {
public:
    ///  模糊选项
    enum CfgMohuOption {
        CFG_MOHU_CH_C = (1 << 0), ///< ch=c模糊选项
        CFG_MOHU_SH_S = (1 << 1), ///< sh=s模糊选项
        CFG_MOHU_ZH_Z = (1 << 2), ///< zh=z模糊选项
        CFG_MOHU_G_K = (1 << 3), ///< g=k模糊选项
        CFG_MOHU_H_F = (1 << 4), ///< h=f模糊选项
        CFG_MOHU_L_N = (1 << 5), ///< l=n模糊选项
        CFG_MOHU_L_R = (1 << 6), ///< l=r模糊选项
        CFG_MOHU_ANG_AN = (1 << 7), ///< ang=an模糊选项
        CFG_MOHU_ENG_EN = (1 << 8), ///< eng=en模糊选项
        CFG_MOHU_ING_IN = (1 << 9), ///< ing=in模糊选项
        CFG_MOHU_IANG_IAN = (1 << 10), ///< iang=ian模糊选项
        CFG_MOHU_UANG_UAN = (1 << 11), ///< uang=uan模糊选项
        CFG_MOHU_AI_AN = (1 << 12), ///< ai=an模糊选项
        CFG_MOHU_UN_ONG = (1 << 13), ///< un=ong模糊选项
        CFG_MOHU_HUI_FEI = (1 << 14), ///< hui=fei模糊选项
        CFG_MOHU_HUANG_WANG = (1 << 15), ///< huang=wang模糊选项
        CFG_MOHU_FENG_HONG = (1 << 16), ///< feng=hong模糊选项
        CFG_MOHU_FU_HU = (1 << 17), ///< hu=fu模糊选项
    };
    /// brief OEM：云端意图 ///
    enum CloudIntention : Uint32 {
        INTENTION_NONE = 0,  ///< 占位 无意图 ///
        INTENTION_CONTACT = (1 << 0),  /// OPPO 联系人意图 ///
        INTENTION_CALENDAR = (1 << 1), ///< OPPO 日历意图 ///
        INTENTION_WEATHER = (1 << 2),  ///<< OPPO 天气意图 ///
        INTENTION_CALCULATOR = (1 << 3),  /// OPPO 计算器意图 ///
        INTENTION_DELICACY = (1 << 4), /// OPPO 美食意图 ///
        INTENTION_MAP = (1 << 5), /// OPPO 地图意图 ///
    };
    /// 荣耀意图 ///
    enum CloudSlotIntention : Uint32 {
        SLOTINTENTION_NONE = 0, /// 占位 无意义 ///
        SLOTINTENTION_PHOTO = (1 << 0), /// 照片意图 ///
        SLOTINTENTION_SCHEDULE = (1 << 1), /// 日程意图 ///
    };
    ///@brief  英文排序类型
    enum CfgEnSort {
        CFG_ENSORT_BYFREQ = 0, ///< 按英文词的频率排序
        CFG_ENSORT_BYLEN,      ///< 按英文词的长度排序
        CFG_ENSORT_BYABC,      ///< 按英文词的字母顺序排序
    };

    ///@brief  英文大小写类型
    enum CfgEnCase {
        CFG_ENCASE_NORMAL = 0, ///< 普通状态
        CFG_ENCASE_FIRST,      ///< 首字母大写状态
        CFG_ENCASE_ALL         ///< 全部字母大写状态
    };

    ///@brief  英文shift类型
    enum CfgEnShift : Uint8 {
        CFG_EN_SHIFT_OFF = 0, ///< shift关闭
        CFG_EN_SHIFT_ON,      ///< shift打开
        CFG_EN_SHIFT_LOCK     ///< shift锁定
    };

    ///@brief  邮件输入类型
    enum CfgUrlEmailForm {
        CFG_URLEMAIL_OFF = 0, ///< 关闭
        CFG_URLEMAIL_MANUAL,  ///< 手动
        CFG_URLEMAIL_AUTO     ///< 自动
    };

public:
    enum CfgTrackType {
        CFGTRACK_NONE = 0, ///< 无轨迹
        CFGTRACK_HW,       ///< 键盘手写
        CFGTRACK_SLIDE,    ///< 滑动输入
    };

public:
    ///@brief  手写速度类型
    enum CfgHwSpeed {
        CFGHWSPEED_SLOWEST = 700, ///< 最慢
        CFGHWSPEED_SLOWER = 600,  ///< 较慢
        CFGHWSPEED_NORMAL = 500,  ///< 正常
        CFGHWSPEED_FASTER = 400,  ///< 较快
        CFGHWSPEED_FASTEST = 300, ///< 最快
        ///
        CFGHWSPEED_SMART = 999,  ///< 智能速度
    };

public:
    ///@brief  手写速度类型
    enum CfgHwType {
        CFGHWTYPE_HZ = 1, ///<  HW_INPUT_TYPE_HZ 单字
        CFGHWTYPE_REDUP = 2, ///<  HW_INPUT_TYPE_REDUP 叠写
        CFGHWTYPE_CONTINUATION = 4, ///<  HW_INPUT_TYPE_NM 连写

        CFGHWTYPE_FREE = CFGHWTYPE_HZ | CFGHWTYPE_REDUP | CFGHWTYPE_CONTINUATION ///<  自由写
    };

public:
    ///@brief  手写符号类型
    enum CfgSymHwRange {
        CFGSYMHWRNG_123 = 0x08,  ///<  HW_FIND_RANGE_NUM 数字
        CFGSYMHWRNG_ABC = 0x30,  ///<  HW_FIND_RANGE_EN_LOWER | HW_FIND_RANGE_EN_UPPER 英文
        CFGSYMHWRNG_PUN = 0x3C0, ///<  HW_FIND_RANGE_PUN_COMMON | HW_FIND_RANGE_PUN_EXT |
        // HW_FIND_RANGE_SYM_COMMON | HW_FIND_RANGE_SYM_EXT 标点,符号
    };

    enum WubiSchema {
        WB_86_SCHEMA = 1, ///<五笔86方案
        WB_98_SCHEMA = 2,  ///<五笔98方案
        WB_DEF_SCHEMA = 3,  ///<五笔自定义方案
        WB_NEW_SCHEMA = 4, ///<五笔新世纪方案
    };

    enum CangjieSchema {
        CJ_NORMAL_SCHEMA = 1,///<普通仓颉方案
        CJ_QUICK_SCHEMA = 2  ///<速成仓颉方案
    };

    enum CloudInputType {
        CLOUD_CLOSED = 255, ///<不开启云输入
        CLOUD_WIFI = 10,   ///<仅在WiFi下开启
        CLOUD_NON_2G = 2, ///<在3G/4G/5G/WiFi下开启
        CLOUD_ALL = 1,    ///<所有网络下开启
        CLOUD_ALWAYS = 0,  ///<不管网络状况, 所有网络下开启
    };

    enum LegendMode { //0: no legend; 1: legend once; 2: legend continuously
        NO_LEGEND = 1,    ///<不联想
        LEGEND_ONCE = 2,  ///<单次联想
        LEGEND_MORE = 3   ///<多次联想
    };

    enum QpFilter : Uint32 {///<全键下的筛选列表
        NONE_FILTER = 0, ///<无筛选
        PY_FILTER = 1,  ///<拼音筛选
        BH_FILTER = 2, ///<笔画筛选
        EN_FILTER = 3,  ///<英文筛选
        EMOJI_FILTER = 4,  ///<表情筛选
        NAME_FILTER = 5,  ///<人名筛选
    };

    enum TraceMode {
        TRACE_OFF = 0,
        TRACE_ON = 1,
        TRACE_ONLY_CONFIG = 2,    ///<只有设置相关的trace被记录，输入相关的不被记录（可用在输入密码的时候）
    };

    enum TraceRecordType {
        TRACE_RECORD_FILE,
        TRACE_RECORD_CLOUD,
        TRACE_RECORD_CEILING
    };
    ///@brief手写注音模式
    enum HwToneMode : Uint32 {
        HwToneModeOdd = 0,///<默认生僻字注音
        HwToneModeAll = 1, ///<全部注音
        HwToneModeNone = 2, ///关闭注音
        HwToneModeMax
    };
    /////////////////框属性////////////////
    enum BoxAttributeType : Uint32 {
        BoxAttributeDefault = 0, // 默认属性 // - 对应旧版本 set_box_attri(0)
        BoxAttributeSearch = 1,//搜索框属性// - 对应旧版本 ATTRIBUTE_SEARCH
        BoxAttributeWebaddr = 2, //网址框属性// - 对应旧版本  ATTRIBUTE_WEB
        BoxAttributeDigital = 3,//数字框属性// - 新增
        BoxAttributeEmail = 4,//邮件框属性//- 对应旧版本  ATTRIBUTE_EMAIL
        BoxAttributePassword = 5, //密码框属性// - 新增
        BoxAttributeDial = 6, //电话框属性// - 新增
        BoxAttributeUnknown = 7,//未知属性// - 对应就旧版本 ATTRIBUTE_OTHER
    };
    ///回车键类型
    enum ReturnKeyType : Uint32 {
        ReturnKeyDefault = 0,///默认，IOS
        ReturnKeyGo = 1,///前往，安卓，IOS
        ReturnKeyGoogle = 2,///谷歌，IOS
        ReturnKeyJoin = 3,///IOS
        ReturnKeyNext = 4,///下一项，安卓，IOS
        ReturnKeyRoute = 5,///，IOS
        ReturnKeySearch = 6,///搜索，安卓，IOS
        ReturnKeySend = 7,///发送，安卓，IOS
        ReturnKeyYahoo = 8,///雅虎，IOS
        ReturnKeyDone = 9,///完成，安卓，IOS
        ReturnKeyEmergencyCall = 10,///IOS
        ReturnKeyContinue = 11,///IOS
        ReturnKeyPrevious = 12,///上一项，安卓
        ReturnKeyUnspecified = 13,///未指定，安卓
        ReturnKeyNone = 14,///无，安卓
    };
    enum AutoSpaceType {
        AST_OFF = 0, /// 不自动追加空格
        AST_APPEND = 1, /// 自动后追空格
        AST_PREPEND = 2, /// 自动前追空格
    };
    // 云输入请求的发起限制 ///
    enum CloudReqLevel : Uint32 {
        CloudReqNormal = 0, ///正常限制 - 维持旧的发起请求 ///
        CloudReqMore = 1, ///  云输入发起条件放宽 - 发起更频繁 -- 主线策略///
    };
    // 小红书各个框 ///
    //enum AppXhsBoxAttri : Uint32 {
    //    XhsNone = 0, // 无特殊属性 ///
    //    XhsText = 1,  // 小红书正文框(在次cand上刷新通知底纹词) ///
    //    XhsTitle = 2, // 小红书标题框(次cand请求mmodel) ///
    //};
    enum EditHintType : Uint32 {
        HintTypeNone = 0,// 无意义值///
        HintTypeSpannableString = 1, ///抖音普通搜索框 ///
        HintTypeString = 2, ///抖音电商搜索框///
    };

public:
    ///运营商类型
    enum CarrierType : Uint32 {
        CarrierTypeUnk = 0, /// 未知 ///
        CarrierTypeMobile = 1, /// 移动 ///
        CarrierTypeUnicom = 2, /// 联通 ///
        CarrierTypeTelecom = 3, /// 电信 ///
    };

public:
    /// @brief 手写半上屏开关, 默认关闭
    /// @param is_on 打开还是关闭
    /// @return void
    virtual void cfg_set_hw_pre_extract_mode(bool is_on) = 0;
    virtual bool cfg_get_hw_pre_extract_mode(void) = 0;
//    virtual bool cfg_get_is_sentence() = 0;  ///< 获取整句开关状态
//    virtual void cfg_set_is_sentence(bool is_on) = 0;  ///< 设置整句开关状态
    /// @brief 手写结束是否强制全上屏，默认关闭 ///
    /// @param is_on 打开还是关闭 ///
    virtual void cfg_set_hw_force_insert(bool is_on = false) = 0;
    virtual bool cfg_get_hw_force_insert() = 0;
    /// @brief 当前是否打开全局手写模式，默认关闭 ///
    /// @param is_on 打开还是关闭 ///
    virtual void cfg_set_global_hw_mode(bool is_on = false) = 0; /// 该接口暂不使用 ///
    virtual bool cfg_get_global_hw_mode() = 0;  /// 该接口暂不使用 ///

    /// @brief 获取自动纠错开关状态
    /// @param void
    /// @return bool 打开还是关闭
    virtual bool cfg_get_is_autofix() = 0;
    /// @brief 设置自动纠错开关状态
    /// @param is_on 打开还是关闭
    /// @return void
    virtual void cfg_set_is_autofix(bool is_on) = 0;
    virtual bool cfg_get_en_second_is_best() const = 0;
    /// @brief 英文第二词如果是最优选则通知上层刷新tip并candselect这个词
    /// @param is_on 打开还是关闭
    /// @return void
    virtual void cfg_set_en_second_is_best(bool is_on) = 0;
    virtual bool cfg_get_is_box_no_suggest() const = 0;
    /// @brief 设置当前输入框是否具有no_suggest属性
    /// @param is_on 打开还是关闭
    /// @return void
    virtual void cfg_set_is_box_no_suggest(bool box_no_suggest) = 0;
    /// @brief 获取预测开关状态
    /// @param void
    /// @return bool 打开还是关闭
    virtual bool cfg_get_is_predict() = 0;
    /// @brief 设置预测开关状态
    /// @param is_on 打开还是关闭
    /// @return void
    virtual void cfg_set_is_predict(bool is_on) = 0;
    /// @brief 获取中英混输开关状态
    /// @param void
    /// @return bool 打开还是关闭
    virtual bool cfg_get_is_cnen() = 0;
    /// @brief 设置中英混输开关状态
    /// @param is_on 打开还是关闭
    /// @return void
    virtual void cfg_set_is_cnen(bool is_on) = 0;
//    virtual bool cfg_get_is_cnen_sentence() = 0; ///< 获取中英混输开关状态
//    virtual void cfg_set_is_cnen_sentence(bool is_on) =
//        0;  ///< 设置中英混输开关状态
//    virtual bool cfg_get_is_jianpin_gram() =
//        0; ///< 获取简拼二元整句开关状态
//    virtual void cfg_set_is_jianpin_gram(bool is_on) =
//        0;  ///< 设置简拼二元整句开关状态
    /// @brief 获取双拼开关状态
    /// @param void
    /// @return bool 打开还是关闭
    virtual bool cfg_get_is_shuangpin() = 0;
    /// @brief 设置双拼开关状态
    /// @param is_on 打开还是关闭
    /// @return void
    virtual void cfg_set_is_shuangpin(bool is_on) = 0;
    /// @brief 获取双拼输入转换开关状态
    /// @param void
    /// @return bool 打开还是关闭
    virtual bool cfg_get_is_shuangpin_nocvt() = 0;
    /// @brief 设置双拼输入转换开关状态
    /// @param is_on 打开还是关闭
    /// @return void
    virtual void cfg_set_is_shuangpin_nocvt(bool is_on) = 0;
    /// @brief 获取全双拼混输开关状态
    /// @param is_on 打开还是关闭
    /// @return void
    virtual bool cfg_get_is_pc_quanshuang_mix() const = 0;
    /// @brief 设置全双拼混输开关状态
    /// @param is_on 打开还是关闭
    /// @return void
    virtual void cfg_set_is_pc_quanshuang_mix(bool is_on) = 0;
    /// @brief 获取全双拼混输下是否在6号位显示真实双拼码开关状态
    /// @param is_on 打开还是关闭
    /// @return void
    virtual bool cfg_get_is_pc_showshuang_key() const = 0;
    /// @brief 设置全双拼混输下是否在6号位显示真实双拼码开关状态，使用的时候需要全双拼混输打开
    /// 否则设置不生效
    /// @param is_on 打开还是关闭
    /// @return void
    virtual void cfg_set_is_pc_showshuang_key(bool is_on) = 0;
    /// @brief 设置PC单行候选数量
    /// @@param cnt 单行候选数量
    /// @return void
    virtual void cfg_pc_set_cand_count_single_page(Uint32 cnt) = 0;
    /// @brief 获取PC单行候选数量
    /// @param void
    /// @return Uint32
    virtual Uint32 cfg_pc_get_cand_count_single_page() = 0;
public:
//    virtual bool cfg_get_is_zifreq() = 0; ///< 获取字频调整开关状态
//    virtual void cfg_set_is_zifreq(bool is_on) = 0;  ///< 设置字频调整开关状态
//    virtual bool cfg_get_is_gbk() = 0; ///< 获取gbk开关状态
//    virtual void cfg_set_is_gbk(bool is_on) = 0;  ///< 设置gbk开关状态
    /// @brief 获取繁体开关状态
    /// @param void
    /// @return bool 打开还是关闭
    virtual bool cfg_get_is_fanti() = 0;
    /// @brief 设置设置繁体开关状态状态
    /// @param is_on 打开还是关闭
    /// @return void
    virtual void cfg_set_is_fanti(bool is_on) = 0;
    /// @brief 获取笔画字形优先开关状态
    /// @param void
    /// @return bool 打开还是关闭
    virtual bool cfg_get_is_bh_frist() = 0;
    /// @brief 设置笔画字形优先开关状态
    /// @param is_on 打开还是关闭
    /// @return void
    virtual void cfg_set_is_bh_frist(bool is_on) = 0;
//    virtual bool cfg_get_is_cifreq() = 0; ///< 获取词频调整开关状态
//    virtual void cfg_set_is_cifreq(bool is_on) = 0;  ///< 设置词频调整开关状态
//    virtual bool cfg_get_is_onlyzi() = 0; ///< 获取仅显示字开关状态
//    virtual void cfg_set_is_onlyzi(bool is_on) = 0;  ///< 设置仅显示字开关状态
    /// @brief 获取五笔拼音混输开关状态
    /// @param void
    /// @return bool 打开还是关闭
    virtual bool cfg_get_is_wbpy() = 0;
    /// @brief 设置五笔拼音混输开关状态
    /// @param is_on 打开还是关闭
    /// @return void
    virtual void cfg_set_is_wbpy(bool is_on) = 0;
    /// @brief 获取五笔四码唯一直接上屏开关状态
    /// @param void
    /// @return bool 打开还是关闭
    virtual bool cfg_get_is_ch_wb_4code_direct_show() = 0;
    /// @brief 设置四码唯一直接上屏开关状态
    /// @param is_on 打开还是关闭
    /// @return void
    virtual void cfg_set_is_ch_wb_4code_direct_show(bool is_on) = 0;
    /// @brief 获取五笔第五码直接上屏首个候选开关状态
    /// @param void
    /// @return bool 打开还是关闭
    virtual bool cfg_get_is_ch_wb_5code_showfirst() = 0;
    /// @brief 设置五笔第五码直接上屏首个候选开关状态
    /// @param is_on 打开还是关闭
    /// @return void
    virtual void cfg_set_is_ch_wb_5code_showfirst(bool is_on) = 0;
    /// @brief 获取五笔拼音出词的五笔编码提示开关状态
    /// @param void
    /// @return bool 打开还是关闭
    virtual bool cfg_pc_get_is_ch_wb_wbpy_tip() const = 0;
    /// @brief 设置五笔拼音出词的五笔编码提示开关状态
    /// @param bool 打开还是关闭
    /// @return void
    virtual void cfg_pc_set_is_ch_wb_wbpy_tip(bool is_on) = 0;
    /// @brief 获取五笔临时拼音面板下展示五笔编码提示开关状态，MAC上使用
    /// @param void
    /// @return bool 打开还是关闭
    virtual bool cfg_get_is_ch_py_wb_tip() const = 0;
    /// @brief 设置五笔临时拼音面板下展示五笔编码提示开关，MAC上使用
    /// @param bool 打开还是关闭
    /// @return void
    virtual void cfg_set_is_ch_py_wb_tip(bool is_on) = 0;
    /// @brief 获取五笔调频开关状态，MAC上使用
    /// @param void
    /// @return bool 打开还是关闭
    virtual bool cfg_get_is_ch_wb_freq_adjust() const = 0;
    /// @brief 设置五笔调频开关状态，MAC上使用
    /// @param bool 打开还是关闭
    /// @return void
    virtual void cfg_set_is_ch_wb_freq_adjust(bool is_on) = 0;
    /// @brief 设置五笔z键是通配
    /// @param bool 打开还是关闭
    /// @return void
    virtual void cfg_set_is_wb_z_common(bool is_on) = 0;
    /// @brief 是否五笔z键是通配
    /// @param bool 打开还是关闭
    /// @return void
    virtual bool cfg_get_is_wb_z_common() const = 0;
    /// @brief 设置五笔自定义只查询五笔/五笔+五笔拼音内容(标准内容)///
    /// @param bool 打开还是关闭
    /// @return void
    virtual void cfg_set_is_wb_def_only_standard(bool is_on) = 0;
    /// @brief 是否五笔自定义只查询五笔/五笔+五笔拼音内容(标准内容)///
    /// @param bool 打开还是关闭
    /// @return void
    virtual bool cfg_get_is_wb_def_only_standard() const = 0;
    /// @brief 设置五笔启动用户词库
    /// @param bool 打开还是关闭
    /// @return void
    /// 重点：开关联动，在设置use_userdict=false时，set_ch_wb_freq_adjust需要置为false
    virtual void cfg_set_is_wb_use_userdict(bool is_on) = 0;
    /// @brief 是否五笔启动用户词库
    /// @param bool 打开还是关闭
    /// @return void
    virtual bool cfg_get_is_wb_use_userdict() const = 0;
    /// @brief 获取自动保存开关状态
    /// @param void
    /// @return bool 打开还是关闭
    virtual bool cfg_get_is_autosave() = 0;
    /// @brief 设置自动保存开关状态
    /// @param is_on 打开还是关闭
    /// @return void
    virtual void cfg_set_is_autosave(bool is_on) = 0;
    /// @brief 获取二次元开关状态
    /// @param void
    /// @return bool 打开还是关闭
    virtual bool cfg_get_is_acgn() = 0;
    /// @brief 设置二次元开关状态
    /// @param is_on 打开还是关闭
    /// @return void
    virtual void cfg_set_is_acgn(bool is_on) = 0;
    /// @brief 获取光标联想状态开关
    /// @brief 移动光标, 联想预测
    /// @param void
    /// @return bool 打开还是关闭
    virtual bool cfg_get_lian_on_cursor() = 0;
    /// @brief 设置光标联想状态开关
    /// @param is_on 打开还是关闭
    /// @return void
    virtual void cfg_set_lian_on_cursor(bool is_on) = 0;
    /// @brief 联想时点击退格键是否要删除光标前1个字
    /// @param void
    /// @return bool 打开还是关闭
    virtual bool cfg_get_lian_back_delete() = 0;
    /// @brief 联想时点击退格键是否要删除光标前1个字
    /// @param is_on 打开还是关闭
    /// @return void
    virtual void cfg_set_lian_back_delete(bool is_on) = 0;
    /// @brief  获取云预测开关状态
    /// @param void
    /// @return bool 打开还是关闭
    virtual bool cfg_get_is_cloud_forcast() = 0;
    /// @brief 设置云预测开关状态
    /// @param is_on 打开还是关闭
    /// @return void
    virtual void cfg_set_is_cloud_forcast(bool is_on) = 0;
    /// @brief  获取英语排序方式状态
    /// @param void
    /// @return 英语排序方式状态
    virtual CfgEnSort cfg_get_ensort() = 0;
    /// @brief 设置英语排序方式状态
    /// @param en_sort 排序方式
    /// @return void
    virtual void cfg_set_ensort(CfgEnSort en_sort) = 0;
    /// @brief  获取英语大小写方式状态
    /// @param void
    /// @return 英语大小写方式状态
    virtual CfgEnCase cfg_get_encase() = 0;
    /// @brief 设置英语大小写方式状态
    /// @param en_case 大小写方式
    /// @return void
    virtual void cfg_set_encase(CfgEnCase en_case) = 0;
    /// @brief 设置表情输入开关状态
    /// @param is_on 打开还是关闭
    /// @return void
    virtual void cfg_set_is_emoji(bool is_on) = 0;
    /// @brief 设置表情联想开关状态
    /// @param is_on 打开还是关闭
    /// @return void
    virtual void cfg_set_is_emoji_lian(bool is_on) = 0;
    /// @brief 设置快速输入开关状态
    /// @param is_on 打开还是关闭
    /// @return void
    virtual void cfg_set_is_fastinput(bool is_on) = 0;
//    virtual void cfg_set_is_zdh(bool is_on) = 0; ///< 设置直达号开关状态
//    virtual void cfg_set_is_media(bool is_on) = 0;  ///< 设置多媒体开关状态
//    virtual void cfg_set_is_xhy(bool is_on) = 0; ///< 设置歇后语开关状态
//    virtual void cfg_set_is_op(bool is_on) = 0;  ///< 设置运营词开关状态
    /// @brief 设置符号联想开关状态
    /// @param is_on 打开还是关闭
    /// @return void
    virtual void cfg_set_is_sylian(bool is_on) = 0;
    /// @brief   获取表情输入开关状态
    /// @param void
    /// @return bool 打开还是关闭
    virtual bool cfg_get_is_emoji() = 0;
    /// @brief   获取表情联想开关状态
    /// @param void
    /// @return bool 打开还是关闭
    virtual bool cfg_get_is_emoji_lian() = 0;
    /// @brief  获取快速输入开关状态
    /// @param void
    /// @return bool 打开还是关闭
    virtual bool cfg_get_is_fastinput() = 0;
//    virtual bool cfg_get_is_zdh() = 0; ///< 获取直达号开关状态
//    virtual bool cfg_get_is_media() = 0; ///< 获取多媒体开关状态
//    virtual bool cfg_get_is_xhy() = 0; ///< 获取歇后语开关状态
//    virtual bool cfg_get_is_op() = 0; ///< 获取运营词开关状态
    /// @brief  获取符号联想开关状态
    /// @param void
    /// @return bool 打开还是关闭
    virtual bool cfg_get_is_sylian() = 0;
    /// @brief 设置五笔提示开关状态
    /// @param is_on 打开还是关闭
    /// @return void
    virtual void cfg_set_is_wbtip(bool is_on) = 0;
    /// @brief  获取五笔提示开关状态
    /// @param void
    /// @return bool 打开还是关闭
    virtual bool cfg_get_is_wbtip() = 0;
//    virtual bool cfg_get_is_personalized() = 0; ///< 获取个性化开关状态
//    virtual void cfg_set_is_personalized(bool is_on) = 0;  ///< 设置个性化开关状态
//    virtual bool cfg_get_is_first() = 0; ///< 获取固首开关状态
//    virtual void cfg_set_is_first(bool is_on) = 0;  ///< 设置固首开关状态
    /// @brief  获取个性化语音状态
    /// @param void
    /// @return bool 打开还是关闭
    virtual bool cfg_get_is_voice_correct() = 0;
    /// @brief 设置个性化语音状态
    /// @param is_on 打开还是关闭
    /// @return void
    virtual void cfg_set_is_voice_correct(bool is_on) = 0;
    /// @brief 设置计算器打开开关
    /// @brief 默认开启计算器
    /// @param is_on 打开还是关闭
    /// @return void
    virtual void cfg_set_enable_calc(bool enable) = 0;
    /// @brief 设置计算器超时时间
    /// @param time 毫秒
    /// @return void
    virtual void cfg_set_calc_time_interval(Uint32 time) = 0;///设置计算器超时时间
    /// @brief 设置数字联想开关状态
    /// @param is_on 打开还是关闭
    /// @return void
    virtual void cfg_set_is_num_lian(bool is_on) = 0;
    /// @brief 获取数字联想开关状态
    /// @param void
    /// @return bool 打开还是关闭
    virtual bool cfg_get_is_num_lian() = 0;

public:
    /// @brief  获取模糊输入开关状态
    /// @param void
    /// @return bool 打开还是关闭
    virtual bool cfg_get_is_mohu() = 0;
    /// @brief 设置模糊输入开关状态
    /// @param is_on 打开还是关闭
    /// @return void
    virtual void cfg_set_is_mohu(bool is_on) = 0;
    /// @brief  获取联想开关状态(iPhone)
    /// @param void
    /// @return bool 打开还是关闭
    virtual bool cfg_get_contact_lian() = 0;
    /// @brief 设置联想开关状态(iPhone)
    /// @param is_on 打开还是关闭
    /// @return void
    virtual void cfg_set_contact_lian(bool is_on) = 0;
    //[更多]有英文list的状态(为了兼容不同的皮肤类型)//
    virtual bool cfg_get_enable_pad_more_tabs() = 0;
    //[更多]有英文list的状态(为了兼容不同的皮肤类型)//
    virtual void cfg_set_enable_pad_more_tabs(bool enabled) =  0;
    /// @brief  获取模糊输入选项状态
    /// @param void
    /// @return bool 打开还是关闭
    virtual Uint32 cfg_get_mohu_option() = 0;
    /// @brief 设置模糊输入选项状态
    /// @param mohuopt
    /// @return void
    virtual void cfg_set_mohu_option(Uint32 mohuopt) = 0;
    /// @brief  获取个性短语开关状态
    /// @param void
    /// @return bool 打开还是关闭
    virtual bool cfg_get_is_phrase() = 0;
    /// @brief 设置个性短语开关状态
    /// @param is_on 打开还是关闭
    /// @return void
    virtual void cfg_set_is_phrase(bool is_on) = 0;
    /// @brief  获取个性短语位置
    /// @param void
    /// @return 个性短语位置
    virtual Uint32 cfg_get_phrase_pos() = 0;
    /// @brief 设置个性短语位置
    /// @param pos 个性短语位置
    /// @return void
    virtual void cfg_set_phrase_pos(Uint32 pos) = 0;
    /// @brief 设置手写面板是否出list, 默认关闭, Android有皮肤要出
    /// @param is_on 打开还是关闭
    /// @return void
    virtual void cfg_set_enable_hw_list(bool is_on) = 0;
    /// @brief  获取手写面板是否出list开关
    /// @param void
    /// @return bool 打开还是关闭
    virtual bool cfg_get_enable_hw_list(void) = 0;
    /// @brief 设置sym_ext面板上屏符号后是否自动返回
    /// @param is_on 打开还是关闭
    /// @return void
    virtual void cfg_set_pad_sym_ext_auto_return(bool is_on) = 0;
    /// @brief  获取sym_ext面板上屏符号后是否自动返回
    /// @param void
    /// @return bool 打开还是关闭
    virtual bool cfg_get_pad_sym_ext_auto_return() = 0;
    ///< 获取是否为硬键盘模式
    virtual bool cfg_get_is_hard_keyboard() const = 0;
    ///< 设置是否为硬键盘模式
    virtual void cfg_set_is_hard_keyboard(bool hard_keyboard) = 0;
    virtual void cfg_enable_private_mode(bool private_mode) = 0; ///<隐私模式   //IOS特有接口//
    virtual void cfg_enable_name_mode(bool name_mode) = 0; ///<是否启用人名模式，默认启用
    /// 增加是否在手写时请求云结果，主线默认关闭，需要主线内核手动开启（OEM根据情况关闭/开启） ///
    virtual void cfg_set_is_hw_req_cloud(bool is_on) = 0;
    /// 设置是否请求本地文心模型 ///
    virtual void cfg_set_need_load_model_wenxin(bool is_on) = 0;
public:
    // emoji不强插触发词后，根据词频自主排序（现在仅荣耀开启，其他端关闭）//
    virtual void cfg_set_emoji_cand_sort_byfreq(bool is_on) = 0;
public:
    virtual void cfg_set_chaizi_result_insert_maincand(bool is_on) = 0;
public:
//    //网络相关
//    virtual bool cfg_get_is_cloud_udp() = 0;  ///< 获取使用网络协议是否为UDP
//    virtual void cfg_set_is_cloud_udp(bool is_udp) = 0;  ///< 设置使用的网络协议
    virtual bool cfg_get_is_abc() = 0;  ///< 获取英文输入是否经过内核
    virtual void cfg_set_is_abc(bool is_abc) = 0;  ///< 设置英文输入是否经过内核
    virtual bool cfg_is_cn_shift_tristate() = 0;  ///< 打开中文键盘三态shift
    virtual void cfg_enable_cn_shift_tristate(bool enable) = 0;  ///< 中文键盘三态shift
    virtual void cfg_set_is_easycloud(bool is_easycloud) = 0; ///< 简单云输入
    virtual bool cfg_get_is_easycloud() = 0;
    virtual void cfg_set_is_sugopen(bool is_sugopen) = 0; ///< sug打开
    virtual bool cfg_get_is_sugopen() const = 0;
    /// 设置为false表示不请求次cand结果 ///
    virtual void cfg_set_subcand_enable(bool is_enable) = 0;
    /// 设置为false表示次cand上不展示cand活动 ///
    //virtual void cfg_set_cloud_campaign_enable(bool is_enable) = 0;
public:
    virtual bool cfg_get_is_pc_umode() const = 0;///<设置PC U模式开关
    virtual void cfg_set_is_pc_umode(bool enalbe) = 0;///<获取PC U模式开关
    virtual bool cfg_get_is_pc_vmode() const = 0; ///<获取PC V模式开关
    virtual void cfg_set_is_pc_vmode(bool is_on) = 0; ///<设置PC V模式开关
    virtual bool cfg_get_is_pc_longterm() const = 0; ///<获取PC 长句联想开关
    virtual void cfg_set_is_pc_longterm(bool is_on) = 0; ///<设置PC 长句联想开关
    virtual bool cfg_get_is_pc_brilliant() const = 0; ///<获取PC 妙笔生花开关
    virtual void cfg_set_is_pc_brilliant(bool is_on) = 0; ///<设置PC 妙笔生花开关
    virtual bool cfg_get_is_pc_nongli() const = 0; ///<获取PC 农历开关
    virtual void cfg_set_is_pc_nongli(bool is_on) = 0; ///<设置PC 农历开关

public:
    virtual WubiSchema cfg_get_wb_schema() = 0;  ///< 获取五笔编码方案
    virtual void cfg_set_wb_schema(WubiSchema wubi_schema) = 0;  ///< 设置五笔编码方案
    virtual LegendMode cfg_get_legend_mode() = 0;  ///< 获取联想类型
    virtual void cfg_set_legend_mode(LegendMode legend_mode) = 0;  ///< 设置联想类型
    virtual LegendMode cfg_get_en_legend_mode() = 0;  ///< 获取英文联想类型
    virtual void cfg_set_en_legend_mode(LegendMode legend_mode) = 0;  ///< 设置英文联想类型
    virtual LegendMode cfg_get_hw_legend_mode() = 0;  ///< 获取手写联想类型
    virtual void cfg_set_hw_legend_mode(LegendMode legend_mode) = 0;  ///< 设置手写联想类型
    virtual void cfg_set_hw_tone_mode(HwToneMode mode) = 0;
    virtual HwToneMode cfg_get_hw_tone_mode(void) = 0;
    virtual CloudInputType cfg_get_cloud_input_type() = 0;  ///< 获取云输入类型
    virtual void cfg_set_cloud_input_type(CloudInputType cloud_input_type) = 0;  ///< 设置云输入类型
    virtual CangjieSchema cfg_get_cj_schema() = 0;  ///< 获取仓颉编码方案
    virtual void cfg_set_cj_schema(CangjieSchema cj_schema) = 0;  ///< 设置仓颉编码方案
    ///< 获取空格上屏联想词选项状态  false-空格清空联想词  true-空格上屏联想词首选
    virtual bool cfg_get_space_lian() = 0;
    virtual void cfg_set_space_lian(bool is_space_lian) = 0;  ///< 设置空格上屏联想词选项状态
    virtual bool cfg_get_dot_hot_key() = 0;  ///< 获取是否开启句号快捷键
    virtual void cfg_set_dot_hot_key(bool is_dot_hot_key) = 0;  ///< 设置是否开启句号快捷键
    ///< 获取英语面板下自动添加空格方式，默认不追加。注意：联想时无视此选项总是添加空格
    virtual AutoSpaceType cfg_get_space_auto_insert() = 0;
    ///< 设置英语面板自动添加空格的方式
    virtual void cfg_set_space_auto_insert(Uint32 auto_insert) = 0;
    ///< 获取英文面板下是否开启高级查词模式（光标查词、退格查词）
    virtual bool cfg_get_en_advanced_find_mode() = 0;
    ///< 设置英文面板下是否开启高级查词模式（光标查词、退格查词）
    virtual void cfg_set_en_advanced_find_mode(bool advanced_find_mode) = 0;
    ///< 获取英语面板下是否句首自动大写，默认false。
    virtual bool cfg_get_auto_capital() = 0;
    ///< 设置英语面板下是否句首自动大写。
    virtual void cfg_set_auto_capital(bool auto_capital) = 0;
    ///< 获取筛选状态 拼音 ///
    virtual ConfigItems::QpFilter cfg_get_qp_filter() const = 0;
    ///< 设置筛选状态 拼音 ///
    virtual void cfg_set_qp_filter(ConfigItems::QpFilter qp_filter) = 0;
    //设置内核log等级，目标：在不同的场景下输出不同等级的log，降低性能消耗//
    virtual void cfg_set_debug_log_level(CoreLogLevel level) = 0;
    virtual Uint32 cfg_get_debug_log_level() = 0;  // 获取内核log等级//
    // 设置云输入发起等级，目标：根据情况限制云输入发起的次数，降低服务器压力 ///
    virtual void cfg_set_cloud_request_level(CloudReqLevel level) = 0;
    // 极速模式-内核简化出词过程，提高查词效率 ，默认关闭（影响评测准确率）///
    virtual void cfg_set_rapid_mode(bool is_on) = 0;
    // 敏感词 - 是否查询敏感词查询，默认开启。 现使用场景: OPPO极速模式，端会关闭敏感词，如有需要，配置下发开关 ///
    virtual void cfg_set_sensitive_dict_enable(bool is_on) = 0;
    // 个人信息联想开关开关 - 是否查询用户个人化信息，并给出联想候选，如手机号码，身份证号等 ///
    // 功能关闭后，请调用 cfg_person_info_reset 接口清空用户个人信息数据 ///
    virtual void cfg_set_person_info_permission_status(bool is_on) = 0;
    virtual bool cfg_get_person_info_permission_status() const = 0;
    //
    // 灵感语录功能-用于控制是否联想出词 ///
    virtual void cfg_set_common_quote_lian(bool is_on) = 0;
    virtual bool cfg_get_common_quote_lian() const = 0;
    // 
    // 大候选模式下，是否展示内核KV预测结果 ///
    virtual void cfg_set_is_pc_need_promotion(bool is_on) = 0;
    // 大候选模式下，是否展示内核KV预测结果 ///
    virtual bool cfg_get_is_pc_need_promotion() const = 0;
    //配置云输入的参数, host长度 < 128, 默认线上w服务器如下
    //
    // cloud_http_address = "http://olimenew.baidu.com/py?";
    // cloud_http_port = 80;
    // sug_http_address = "http://srfsug.baidu.com/py?";
    // sug_http_port = 80;
    // cloud_udp_address = "udpolimenew.baidu.com";
    // cloud_udp_port = 4040;
    // sug_udp_address = "udpsrfsug.baidu.com";
    // sug_udp_port = 4040;
    // kv_udp_host = "udpolimeok.baidu.com";
    // kv_udp_port = 4040;
    //如果不改变某项,传NULL, or 0
    // 备注：5.30内核进行了云输入通道合并，合并cloud和kv，仅保留cloud服务器 ///
    // 所以cfg_set_cloud_address接口中删除kv相关内容///

    // 支持 QUIC 协议 版本，一律使用该接口设置所有网络服务地址，乱七八糟的接口一律废弃 ///
    /// 只有主线需要支持quic，组件化同学需要注意
    virtual void cfg_set_cloud_address(const NetAddrConfigSet* addr_set) = 0;
    /// 目前只有主线会上QUIC，原来的接口还是需要保留的
#if defined(IPT_FEATURE_PC_ONLY) || defined(IPT_PLATFORM_MACOS)
    virtual void cfg_set_cloud_address(const char* cloud_http_host,/// 云输入http 服务器,
                                       Uint32 cloud_http_port, ///云输入http服务器端口,
                                       const char* cloud_udp_host, /// 云输入udp地址 ///
                                       Uint32 cloud_udp_port,  ///云输入udp服务端口 ///
                                       const char* sug_http_host, ///sug服务http地址///
                                       Uint32 sug_http_port,///sug服务http端口
                                       const char* sug_udp_host,///sug服务udp地址 ///
                                       Uint32 sug_udp_port,///sug服务udp端口
                                       const char* nlp_http_host, //撰写与纠错的url
                                       Uint32 nlp_http_port,      //撰写与纠错的端口
                                       const char* kv_udp_host, ///kv服务 默认是udp请求, 地址为 udpolimeok.baidu.com
                                       Uint32 kv_udp_port, //kv服务  端口为4040
                                       bool cloud_use_udp = true,
                                       bool sug_use_udp = false) = 0;
#else
    virtual void cfg_set_cloud_address(const char* cloud_http_host,/// 云输入http 服务器,
                                       Uint32 cloud_http_port, ///云输入http服务器端口,
                                       const char* cloud_udp_host, /// 云输入udp地址 ///
                                       Uint32 cloud_udp_port,  ///云输入udp服务端口 ///
                                       const char* sug_http_host, ///sug服务http地址///
                                       Uint32 sug_http_port,///sug服务http端口
                                       const char* sug_udp_host,///sug服务udp地址 ///
                                       Uint32 sug_udp_port,///sug服务udp端口
                                       const char* nlp_http_host, //撰写与纠错的url
                                       Uint32 nlp_http_port,      //撰写与纠错的端口
                                       bool cloud_use_udp = true,
                                       bool sug_use_udp = false) = 0;
#endif
    // 5.30版本，本来进行了通道合并，但因端上埋点监控到成功率降低，所以暂时将分离的通道加回来 ///
    // 这个接口现在只有Android在调用 ///
    // 这个接口默认kv和cloud是udp协议，sug和aichat是https协议 ///
    virtual void cfg_set_cloud_address_new(const char* kv_host, Uint32 kv_port, const char* cloud_host, Uint32 cloud_port,
                                           const char* sug_host, Uint32 sug_port, const char* nlp_host, Uint32 nlp_port) = 0;
    // 内核上报数据的接口地址 ///
    virtual void cfg_set_upl_data_address(const char* upload_data_host, Uint32 upload_data_port) = 0;

public:
    virtual bool cfg_get_is_newline_on_hw_enter() = 0;
    virtual void cfg_set_is_newline_on_hw_enter(bool new_line) = 0;
    //叠写,连写,单字
    virtual CfgHwType cfg_get_hw_type() = 0;  ///< 获取手写类型状态
    virtual void cfg_set_hw_type(CfgHwType hw_type) = 0;  ///< 设置手写类型状态
    //识别速度
    virtual CfgHwSpeed cfg_get_hw_speed() = 0;  ///< 获取手写速度状态
    virtual void cfg_set_hw_speed(CfgHwSpeed hw_speed) = 0;  ///< 设置手写速度状态
    //键盘手写还是滑动输入还是都没有
    virtual CfgTrackType cfg_get_track_type() = 0;  ///< 获取键盘手写状态
    virtual void cfg_set_track_type(CfgTrackType track_type) = 0;  ///< 设置键盘手写状态
    //虚框手写范围设置, 参见SymHwRange, 可以或起来, range = 0表示关闭其他值都为开启
    virtual Uint32 cfg_get_symhw_range() = 0;  ///< 获取虚框手写范围
    virtual void cfg_set_symhw_range(Uint32 range) = 0;///< 设置虚框手写范围
    virtual bool cfg_get_is_symhw() = 0;
    virtual void cfg_set_is_symhw(bool is_open_symhw) = 0;
    // sug相关
    virtual const char* cfg_get_sug_white_data() = 0;  ///< 获取sug白名单数据
    virtual void cfg_set_sug_white_data(const Uint8* data, Uint32 data_len) = 0;  ///< 设置sug白名单数据
public:
    //手机相关设置, 在初始化时就设置好
    virtual const char* cfg_get_phone_cuid() const = 0;  ///< 获取手机的cuid
    virtual void cfg_set_phone_cuid(const char* cuid) = 0; ///< 设置手机的cuid
    virtual const char* cfg_get_phone_cuid3() const = 0; ///< 获取手机的cuid3
    virtual void cfg_set_phone_cuid3(const char* cuid3) = 0; ///< 设置手机的cuid3
    virtual const char* cfg_get_phone_oaid() const = 0; ///< 获取手机的oaid
    virtual void cfg_set_phone_oaid(const char* oaid) = 0; ///< 设置手机的oaid
    virtual const char* cfg_get_honor_oaid() const = 0; ///< 获取荣耀手机专有的oaid
    virtual void cfg_set_honor_oaid(const char* oaid) = 0; ///< 设置荣耀手机专有的oaid
    virtual Uint32 cfg_get_exist_bm() const = 0; ///< 获取是否安装了手百
    virtual void cfg_set_exist_bm(Uint32 exist) = 0; ///< 设置是否安装了手百
    //以下设置为优量汇新增
    virtual void cfg_set_ua(const char* ua) = 0; ///< 设置ua
    virtual const char* cfg_get_ua() const = 0; ///< 获取ua
    virtual void cfg_set_carrier(CarrierType type) = 0; ///< 设置运营商
    virtual CarrierType cfg_get_carrier() const = 0; ///< 获取运营商
    virtual void cfg_set_device_start_sec(Uint64 sec) = 0; ///< 设置设备启动时间，单位：秒
    virtual Uint64 cfg_get_device_start_sec() = 0; ///< 获取设备启动时间，单位：秒
    virtual void cfg_set_physical_memory_byte(Uint64 mem_byte) = 0; ///< 设置物理内存，单位：字节
    virtual Uint64 cfg_get_physical_memory_byte() = 0; ///< 获取物理内存，单位：字节
    virtual void cfg_set_harddisk_size_byte(Uint64 disk_byte) = 0; ///< 设置硬盘大小，单位：字节
    virtual Uint64 cfg_get_harddisk_size_byte() = 0; ///< 获取硬盘大小，单位：字节
    virtual void cfg_set_system_update_microsec(Uint64 ms) = 0; ///< 设置系统更新时间，单位：微秒
    virtual Uint64 cfg_get_system_update_microsec() = 0; ///< 获取系统更新时间，单位：微秒
    //
    virtual Uint32 cfg_get_phone_width() const = 0; ///< 获取手机屏幕宽度
    virtual void cfg_set_phone_width(Uint32 width) = 0;  ///< 设置手机屏幕宽度
    virtual Uint32 cfg_get_phone_height() const = 0;  ///< 获取手机屏幕长度
    virtual void cfg_set_phone_height(Uint32 height) = 0;  ///< 设置手机屏幕长度
    virtual const char* cfg_get_phone_model() const = 0;  ///< 获取手机型号
    virtual void cfg_set_phone_model(const char* model) = 0;  ///< 设置手机型号
    virtual const char* cfg_get_android_id() const = 0; ///< 获取手机的android_id
    virtual void cfg_set_android_id(const char* android_id) =
        0; ///< 设置手机的android_id，长度通常为16，内核最多保留32字
    virtual const char* cfg_get_phone_vendor() const = 0; ///< 获取手机厂商
    virtual void cfg_set_phone_vendor(const char* phone_vendor) =
        0; ///< 设置手机厂商，如oppo, Samsung等，内核最多保留32字
    virtual const char* cfg_get_os_version() const = 0; ///< 获取手机操作系统版本
    virtual void cfg_set_os_version(const char* os_version) =
        0; ///< 设置手机操作系统版本，如13.1等，内核最多保留32字
    virtual const char* cfg_get_idfa() const = 0; ///< 获取IDFA，仅iOS
    virtual void cfg_set_idfa(const char* idfa) = 0; ///< 设置IDFA，仅iOS，内核最多保留64字
    virtual const char* cfg_get_idfv() const = 0;
    virtual void cfg_set_idfv(const char* idfv) = 0; ///< 内核最多保留64字
    virtual const char* cfg_get_caid1() const = 0;
    virtual void cfg_set_caid1(const char* caid1) = 0; ///< 内核最多保留64字
    virtual const char* cfg_get_caid1_version() const = 0;
    virtual void cfg_set_caid1_version(const char* caid1_version) = 0; ///< 内核最多保留32字
    virtual const char* cfg_get_caid2() const = 0;
    virtual void cfg_set_caid2(const char* caid2) = 0; ///< 内核最多保留64字
    virtual const char* cfg_get_caid2_version() const = 0;
    virtual void cfg_set_caid2_version(const char* caid2_version) = 0; ///< 内核最多保留32字

    ///////////////////////////////////////////////////////
    //输入法相关设置，在初始化时就设置好
    virtual const char* cfg_get_ipt_ver() const = 0;  ///< 获取手机的cuid
    virtual void cfg_set_ipt_ver(const char* ver) = 0;
    virtual const char* cfg_get_ipt_channel() const = 0;
    virtual void cfg_set_ipt_channel(const char* channel) = 0;
    virtual const char* cfg_get_ipt_platform() const = 0;
    virtual void cfg_set_ipt_platform(const char* platform) = 0;
    virtual Uint32 cfg_get_ipt_icon_size() const = 0;
    virtual void cfg_set_ipt_icon_size(Uint32 size) = 0;
    ///////////////////////////////////////////////////////
    //网络，城市等环境设置,面板弹起前需要设置好,发生更改时,更改设置
    virtual const char* cfg_get_env_app() const = 0;  ///< 获取APP环境信息
    virtual void cfg_set_env_app(const char* app) = 0;  ///< 设置APP环境信息
    virtual const char* cfg_get_env_city() const = 0;  ///< 获取城市环境信息
    virtual void cfg_set_env_city(const char* city) = 0;  ///< 设置城市环境信息
    virtual CLOUD_NET_TYPE cfg_get_env_net_type() const = 0;  ///< 获取网络环境信息
    virtual void cfg_set_env_net_type(CLOUD_NET_TYPE net_type) = 0;  ///< 设置网络环境信息
    virtual Uint32 cfg_get_env_edit_type() const = 0;
    virtual void cfg_set_env_edit_type(Uint32 edit_type) = 0;
    virtual Uint32 cfg_get_box_attri() const = 0;  ///< 获取当前输入框属性
    virtual void cfg_set_box_attri(Uint32 box_attri) = 0;  ///< 设置当前输入框属性 ATTRIBUTE_OTHER ATTRIBUTE_SEARCH
    virtual void cfg_set_env_edit_hint(const Uint16* edit_hint, Uint32 edit_hint_len) = 0; ///< 设置输入框内底纹词
    //  设置输入框内底纹词type，现用于区别抖音的电商搜索和普通搜索 ///
    virtual void cfg_set_env_edit_hint_type(EditHintType type) = 0;
    virtual Uint32 cfg_get_env_edit_hint_type() const = 0;
    virtual Uint32 cfg_get_enterkey_type() const = 0;///< 获取当前回车键类型，长log使用和AI助聊四期 ///
    virtual void cfg_set_enterkey_type(Uint32) = 0;///< 设置当前回车键类型，长log使用和AI助聊四期 ///
    virtual void cfg_set_ai_scene_type(AiSceneType scene) = 0; /// 聊天场景或购物场景 ///
    virtual void cfg_set_auto_correction_type(const char* type) = 0; /// iOS特有，框额外属性 ///
    virtual void cfg_set_enable_return_key_automatically(const char* ret_key) = 0; /// iOS特有，框额外属性 ///
    // 小红书框属性 - 端上自行判断后设置给内核 //
    //virtual void cfg_set_app_xhs_box_attribute(AppXhsBoxAttri attri) = 0; /// 小红书属性 ///
    //virtual AppXhsBoxAttri cfg_get_app_xhs_box_attribute() const = 0; /// 小红书属性 ///
    // Ai创作tab设置次cand是否需要展示开关，底纹词为CANDTYPE_AI_COMPOSE_PREFERENCE//
    // 是否需要展示在端上控制 //
    virtual void cfg_set_aicand_preference_enable(bool enabled) = 0;
    ///Ai助聊相关的开关配置 ///
    virtual void cfg_set_ai_chat_enable(bool enabled) = 0; /// Ai助聊总开关 ///
    ////
    //////////////////////////////// 下面接口 端上接管功能，不需要内核开关控制 ///////////////////////////////////
    virtual void cfg_set_auto_write_cand_enable(bool enabled) = 0; ///  Ai帮写 - AiCand-下的开关 ///
    virtual void cfg_set_auto_write_pad_enable(bool enabled) = 0; ///   Ai帮写 -  AiPad-下的开关 ///
    virtual void cfg_set_auto_write_auto_open(bool enabled) = 0; /// Ai帮写 - 起面板时自动打开 ////
    virtual void cfg_set_txt_error_recovery_pad_enable(bool enabled) = 0; /// Ai校对 -  AiPad-下的开关
    virtual void cfg_set_funchat_cand_enable(bool enabled) = 0; /// Ai趣聊- AiCand-下的开关
    virtual void cfg_set_funchat_pad_enable(bool enabled) = 0; ///  Ai校对 -  Ai-下的开关
    virtual void cfg_set_ai_peitu_auto_open(bool enabled) = 0; /// 神句配图 - 起面板时自动打开 ////////
    /////////////////////////////// 上面接口 端上接管功能，不需要内核开关控制//////////////////////////////////
    virtual void cfg_set_txt_error_recovery_cand_enable(bool enabled) = 0; /// Ai校对 - AiCand-下的开关
    // 临时开关 - 是否启动云输入内核2.0协议，临时方案接口，预期5.20版本上线后删除 ///
    virtual void cfg_set_cloud_version_2_0(bool enabled) = 0;
    ///< 设置该场景输入前预测是否支持正负能量, 0表示常规，以分流模型style为准，>0优先级高于分流模型 ///
    virtual void cfg_set_sent_pre_mode_enabled(bool enabled) = 0;
    ///< 设置AI智能联想请求-云端mmodel请求 ///
    virtual void cfg_set_aichat_mmodel_enabled(bool enabled) = 0;
    /// < 设置AI智能联想，高情商场景, 用于内核判断是否需要请求意图模型 ///
    virtual void cfg_set_aichat_higheq_scene(bool enabled) = 0;
    /// < 设置AI智能联想-正文框底纹词 ///
    // virtual void cfg_set_aichat_content_hint_enabled(bool enabled) = 0;
    /// < 设置AI智能联想-正文框底纹词内容 ///
    virtual void cfg_set_aichat_content_hint(const Uint16* hint, Uint32 hint_len) = 0;
    //virtual void cfg_set_sent_pre_mode(AiEnergyType mode) = 0; (已废弃 ) ///< 设置输入前预测正负能量模式
    ///< 输入前整句开关，true表示开
    virtual void cfg_set_is_sent_pre_enabled(bool enabled) = 0;
    ///< 输入中整句预测开关，1表示开
    virtual void cfg_set_is_sent_cloud_enabled(bool enabled) = 0;
    ///< 是否保留输入后整句预测，保留的话，撰写和纠错内核不会发起了
    virtual void cfg_set_is_sent_hide_compose(bool hide) = 0;
    //
    virtual void cfg_set_orientation(ORIENTATION orientation) = 0; ///< 设置屏幕方向
    virtual const char* cfg_get_theme() const = 0; ///< 获取当前皮肤
    virtual void cfg_set_theme(const char*) = 0;  ///< 设置当前皮肤
    // 设置是否展示内核标记 ///
    virtual void cfg_set_display_candtype(bool is_on) = 0;
    // 获取是否展示内核标记 ///
    virtual bool cfg_get_display_candtype() const = 0;
    ////
    /// @brief Ai助聊json配置设定接口 ///
    /// @param json_str 配置的json字符串(来自通知中心) ///
    /// @param json_str_len json字符串长度
    /// @param notice_key 配置通知中心key///
    /// @param notice_key_len 配置通知中心key的字符串长度///
    ///   内核会记忆Ai助聊配置最后一次设定信息.
    ///
    virtual void cfg_set_aiwords_json(const char* json_str, Uint32 json_str_len,
                                      const char* notice_key, Uint32 notice_key_len) = 0;
    ///////////////////////////////////////////////////////
    virtual Int32 cfg_util_set_sp_file(const char* sp_file) = 0;  ///<设置双拼方案
    virtual void cfg_set_scene_group_ids(Int64*, Int32 len) = 0; // 设置场景组 id

public:
    virtual TraceMode cfg_get_trace_mode() const = 0; ///< trace_log开关, 轨迹//
    virtual void cfg_set_trace_mode(TraceMode mode) = 0; ///输入密码时，客户端可以关闭

    //尽量写轨迹到文件的开关, 默认关闭␍
    //如果打开, 除了在起收面板的时候把轨迹写入文件, 还会开启300ms空闲timer,
    //在timer中检查到有轨迹数据就写入文件␍ 目的是减少内存中的轨迹数据,
    //以防止崩溃的时候轨迹丢失(主要针对iOS被系统kill的问题)␍
    virtual void cfg_set_trace_flush(bool enable) = 0;
    //供客户端调试用(因为默认trace文件3M才会报警,QA很难触发)␍
    virtual void cfg_set_trace_warning_size(Uint32 base, Uint32 increment) = 0;
    //写日志到文件的开关，默认关闭
    //打开后，将当天的日志记录到文件中，第二天记录的日志会将前一天日志覆盖
    virtual void cfg_set_log_open(bool enable) = 0;
    //
    virtual TraceRecordType cfg_get_trace_record_type() const = 0; // 获取用户轨迹收集方式/
    // 设置用户轨迹收集方式，仅开启用户轨迹收集时有效//
    virtual void cfg_set_trace_record_type(TraceRecordType type) = 0;

    //是否开启收集误触信息
    virtual bool cfg_get_collect_mis_info() const = 0;
    virtual void cfg_set_collect_mis_info(bool enable) = 0;
    //是否开启收集联想打点信息
    virtual bool cfg_get_collect_input_statistics() const = 0;
    virtual void cfg_set_collect_input_statistics(bool enable) = 0;

    ///<云输入要不要放在云输入区域，false是放在cand区域混排, 默认true, 单独显示//
    virtual bool cfg_get_cloud_region() const = 0;
    virtual void cfg_set_cloud_region(bool cloud_region) = 0;
    virtual Uint32 cfg_get_cloud_delay_time() const = 0;
    virtual void cfg_set_cloud_delay_time(Uint32 delay_time) = 0;
    //pc文心大模型延时时间
    virtual Uint32 cfg_get_wenxin_delay_time() const = 0;
    //如果设置的延时超过150ms，则内核强制设置成150ms，本接口必须放在查词线程调用
    virtual void cfg_set_wenxin_delay_time(Uint32 delay_time) = 0;
    //
    //拼音面板shift状态变化时是否刷新面板
    virtual void cfg_set_refresh_layout_for_pinyin_shift(bool refresh) = 0;
    virtual bool cfg_get_refresh_layout_for_pinyin_shift() = 0;
    //五笔面板shift状态变化时是否刷新面板
    virtual void cfg_set_refresh_layout_for_wubi_shift(bool refresh) = 0;
    virtual bool cfg_get_refresh_layout_for_wubi_shift() = 0;
    //无输入码时是否忽略点击的分词键
    virtual void cfg_set_ignore_split_no_input(bool ignore) = 0;
    virtual bool cfg_get_ignore_split_no_input() = 0;
    //中文面板有输入码时切换英文，是否上屏输入码
    virtual void cfg_set_commit_input_when_switch_cnen(bool commit_input) = 0;
    virtual bool cfg_get_commit_input_when_switch_cnen() = 0;
    //无输入码时按shift+code，是否直接上屏大写字母
    virtual void cfg_set_commit_alpha_when_shift_longdown(bool commit_alpha) = 0;
    virtual bool cfg_get_commit_alpha_when_shift_longdown() = 0;
    //是否开启inline输入码模式
    virtual void cfg_set_enable_inline_show(bool enable) = 0;
    virtual bool cfg_get_enable_inline_show() = 0;
    //符号扩展面板需要自动返回的字符（中文最多支持63个，英文最多支持16个）
    virtual void cfg_set_syms_auto_return(const Uint16* syms, bool is_en, bool with_input) = 0;
    virtual const Uint16* cfg_get_syms_auto_return(bool is_en, bool with_input) = 0;
    //切换到数字面板时是否上屏首选词
    virtual void cfg_set_commit_when_switch_pad123(bool commit) = 0;
    virtual bool cfg_get_commit_when_switch_pad123() = 0;
    //字写完前是否屏蔽cand刷新/预上屏
    virtual void cfg_set_disable_hw_cand_before_finish(bool disabled) = 0;
    virtual bool cfg_get_disable_hw_cand_before_finish() = 0;
    //9键面板把分词键当作数字键
    virtual void cfg_set_treat_t9_split_as_num(bool enable) = 0;
    virtual bool cfg_get_treat_t9_split_as_num() = 0;
    //最近生僻字cand上的生僻字显示数量限制，默认是8
    virtual void cfg_set_recent_rare_cand_limit(Uint32 limit) = 0;
    virtual Uint32 cfg_get_recent_rare_cand_limit() = 0;
    //生僻字面板的生僻字是否展示拼音
    virtual void cfg_set_rare_zi_with_pinyin(bool enable) = 0;
    virtual bool cfg_get_rare_zi_with_pinyin() = 0;
    //是否启用高斯个性化模型
    virtual void cfg_set_gauss_user_enabled(bool enable) = 0;
    virtual bool cfg_get_gauss_user_enabled() = 0;
    //是否屏蔽高斯个性化模型的落点收集与训练（当gauss_user_enabled开关打开时，这个开关才有效，默认不屏蔽）
    virtual void cfg_set_gauss_user_points_disabled(bool disable) = 0;
    virtual bool cfg_get_gauss_user_points_disabled() = 0;
    //数字后上屏的中文句号是否自动转换成英文句号（需要硬键盘开关打开）
    virtual void cfg_set_convert_dot_after_number(bool enable) = 0;
    virtual bool cfg_get_convert_dot_after_number() = 0;
    //硬键盘下云输入占位符的消失等待时间（单位是毫秒，0表示不消失）
    virtual void cfg_set_cloud_placeholder_vanish_time(Uint32 msecs) = 0;
    virtual Uint32 cfg_get_cloud_placeholder_vanish_time() = 0;
    //是否屏蔽手写定时器产生的cand
    virtual void cfg_set_disable_hw_timer_cand(bool disable) = 0;
    virtual bool cfg_get_disable_hw_timer_cand() = 0;
    //
    ///< 手写模型是否加载成功
    virtual HwLoadType cfg_get_hw_load_type() const = 0;
    // < AI智能联想相关, 分流模型
#ifdef USE_LOCAL_FLOW_MODEL
    //  本地分流模型不再使用，故接口注释, Feature: MICInput_common-4085 ///
    virtual HwLoadType cfg_get_flow_load_type() const = 0;
#endif
    //< 文心模型是否加载成功
    virtual HwLoadType cfg_get_local_wenxin_load_type() const = 0;
    ///< 滑行输入模型是否加载成功
    virtual bool cfg_get_is_slide_load() const = 0;
    /// 是否查询云端地图词，默认为false
    virtual void cfg_set_is_req_cloud_map_word(bool on) = 0;
    /// 是否需要通用触发词，默认为true，目前端上用于游戏键盘
    virtual void cfg_set_is_need_common_trigger_word(bool on) = 0;
    /// 是否需要云端通用触发词，默认为false
    virtual void cfg_set_is_need_cloud_trigger_word(bool on) = 0;
    /// 是否需要次cand云端触发词，受cfg_set_is_need_cloud_trigger_word控制，不能单独生效，默认为true
    virtual void cfg_set_is_need_cloud_trigger_subcand(bool on) = 0;
    /// 是否需要在打开aipad时查云端触发词，受cfg_set_is_need_cloud_trigger_word控制，不能单独生效，默认为false
    virtual void cfg_set_is_need_cloud_trigger_in_aipad(bool on) = 0;
    /// 是否需要云端通用触发词请求神句配图原因 ///
    virtual void cfg_set_is_need_cloud_trigger_peitu(bool on) = 0;
    // 云端意图 - 是否请求联系人/天气/日历/汇率 ///
    // 内核默认状态-全部关闭，由OEM控制开启 ///
    virtual void cfg_set_cloud_intention(CloudIntention type, bool is_on) = 0;
    //  云端意图 - 获取意图开关内容 ///
    virtual Uint32 cfg_get_cloud_intention() const = 0;
    // 云端意图 - 是否可以在设置项关闭的情况下请求引导入口意图 ///
    // 内核默认状态-全部关闭，OEM版本在可展示的场景设置为true给到内核 ///
    // 在 cfg_set_cloud_intention 的对应设置关闭的情况下，如果开启了该意图的引导入口 ///
    /// 内核仍请求云端询问意图结果并在匹配后展示入口/// // 这个接口可能不需要了，先放在这里一起删除///
    //virtual void cfg_set_cloud_intention_prompt(CloudIntention type, bool is_on) = 0;
    // 云端意图 - 是否有联系人的权限 ///
    /// (如果有，读取联系人词典的内容判断意图。如果没有，借用人名模式判断是否有联系人意图引导用户开启权限)///
    virtual void cfg_set_contact_permission_status(bool is_on) = 0;
    // 云端意图除表情外的开关 - 主线使用，需要受服务端AB测试开关控制 ///
    virtual void cfg_req_set_cloud_intention(bool is_on) = 0;
    // 云端表情意图、以及本地通用触发词表情单独开关 - 主线使用，需要受服务端AB测试开关控制 ///
    virtual void cfg_req_set_cloud_expression_intention(bool is_on) = 0;
    // 地图意图开关，受cfg_set_cloud_intention控制
    // 如果启用，则内核禁用地图触发词
    // 地图意图不依赖于cfg_set_is_req_cloud_map_word
    virtual void cfg_req_set_cloud_map_intention(bool is_on) = 0;
    // 荣耀意图开关 - 是否请求荣耀意图 ///
    // 内核默认关 ///
    virtual void cfg_set_cloud_slot_intention(CloudSlotIntention type, bool is_on) = 0;
    // 非设置项接口-场景接，用于告知内核当前APP+框是否是带有【发送】功能 ///
    /// 内核根据接口状态判断是否需要在可能是【发送】触发的时机，缓存发送前的候选内容 ///
    // 内核默认false，暂未使用 ///
    virtual void cfg_set_scene_with_send_function(bool is_on) = 0;
    // 设置云端触发词的联想在 主Cand 上手写面板的展示位置 ///
    // 默认是二选（主线等，与拼音面板相同），华为需要三选，客户端需要调用次接口进行设置///
    // 首选为0，以此类推 ///
    virtual void cfg_set_handwt_lian_cloud_trigger_idx(Uint32 idx) = 0;
    // 是否需要请求手写联想的结果 ///
    /// 在 is_hw_req_cloud 关闭的场景，仅看是否需要在手写时请求联想(OEM在云端触发词的时候使用)//
    // 内核默认关闭，各平台根据需求开启，开启效果：可请求hw_req的联想 ///
    // 这时，即使 is_hw_req_cloud 关闭，is_hw_req_cloud_lian开启仍需请求手写联想 ///
    virtual void cfg_set_is_hw_req_cloud_lian(bool is_on) = 0;
    // 设置云端触发词每天展示的最大次数 ///
    // 目前只给OPPO版本使用 ///
    virtual void cfg_set_max_show_cnt_cloud_trigger(Uint32 limit) = 0;
    virtual Uint32 cfg_get_max_show_cnt_cloud_trigger() = 0;
    // 设置云端触发词展示的光标前内容长度限制 ///
    // 光标前内容不超过对应长度，不展示 /// 
    // 目前只给OPPO版本使用 ///
    virtual void cfg_set_min_request_len_cloud_trigger(Uint32 limit) = 0;
    virtual Uint32 cfg_get_min_request_len_cloud_trigger() = 0;
    /// 设置品专类触发词每天的最大展示次数 ///
    // limit 为 0不限制展示次数，limit > 0 为每天最多的展示次数 ///
    virtual void cfg_set_max_daily_show_cnt_brand_trigger(Uint32 limit) = 0;
    virtual Uint32 cfg_get_max_daily_show_cnt_brand_trigger() = 0;
    // 云端意图 - 是否定位信息的权限 ///
    /// (如果有，云端可结合位置信息下发美食意图)///
    virtual void cfg_set_location_permission_status(bool is_on) = 0;
    /// 用于客户端设置定位信息，目前用于地图排序
    virtual void cfg_set_location(float latitude, float longitude) = 0;
    /// 是否需要判断输入内容是否包含身份证号 默认关闭 ///
    virtual void cfg_set_identity_num_recognize(bool is_on) = 0;
    /// 手写落笔时是否需要保留cand 默认是否，即清空的 ///
    virtual void cfg_set_track_start_reserve_cand(bool is_on) = 0;
    virtual bool cfg_get_track_start_reserve_cand() = 0;
};

///需要session的设置项////
class ConfigPad {
public:
    class ConfigCallback {
    public:
        virtual void on_config_data_change() = 0;  ///<  设置项中数据改变
    };

public:
    enum ECoreFileType {
        CFT_CZ3 = 0,
        CFT_AutoReply = 1,
        CFT_EN_NEO = 2,
        CFT_SLIDE = 3,
        CF_HandWrite = 4,//手写词库
        CFT_CANGJIE = 5, //仓颉词典
        CFT_CANGJIE_QUICK = 6,//速成仓颉词典
        CFT_ZHUYIN = 7, //注音词典
        CFT_FLOW_MODEL = 8, // 本地分流模型（废弃）
        CFT_SENS = 9, //敏感词库
        CFT_RARE_CHAIZI = 10, //生僻字部首拼音拆字词库
        CFT_LOCAL_WENXIN = 11, // 文心大模型
    };
    ///////////////////////////////////////////////////////////////////////////////////////////////////
    enum ECoreZhuyinCZType : Uint32 {
        ZHUYIN_DICT_NONE = 0, // 无注音词典  - 非注音用户等///
        ZHUYIN_DICT_JT = 1, // 注音词典内容是简体 ///
        ZHUYIN_DICT_FT = 2, //注音词典内容是繁体 ///
    };
public:
    /**获取个性短语分组内容
     * @return 获取个性短语分组内容
     */
    virtual const PhraseGroup* cfg_get_phrase_group_by_idx(Uint32 idx) = 0;
    /**添加个性短语分组内容
     * @return
     */
    virtual Int32 cfg_add_phrase_group(const Uint16* name, Uint32 name_len) = 0;
    /**删除个性短语分组
     * @return
     */
    virtual Int32 cfg_delete_phrase_group(Uint32 idx) = 0;
    /**编辑个性短语分组内容
     * @return
     */
    virtual Int32 cfg_edit_phrase_group(Uint32 idx, const Uint16* name, Uint32 name_len) = 0;
    /**启用/关闭个性短语分组
     * @return
     */
    virtual Int32 cfg_enable_phrase_group(Uint32 idx, bool is_enable) = 0;
    /**删除个性短语
     * @return
     */
    virtual Int32 cfg_delete_phrase_item(Uint32 idx) = 0;
    /**编辑个性短语
     * @return
     */
    virtual Int32 cfg_edit_phrase_item(Uint32 idx, const char* code,
                                       Uint32 code_len, const Uint16* word, Uint32 word_len, Uint32 pos) = 0;
    /**添加个性短语
     * @return
     */
    virtual Int32 cfg_add_phrase_item(const char* code, Uint32 code_len,
                                      const Uint16* word, Uint32 word_len, Uint32 pos, Uint32 group_id) = 0;
    /**获取个性短语内容
     * @return
     */
    virtual const PhraseItem* cfg_get_phrase_info(Uint32 idx) = 0;
    ///////////////////////////////////////////////////////////////////////////////////////////////////
public:
    /**获取个性短语数量
     * @return
     */
    virtual Int32 cfg_get_phrase_item_count(Uint32 group_id, const char* code, Uint32 code_len) = 0;
    /**获取个性短语分组数量
     * @return 获取个性短语分组数量
     */
    virtual Int32 cfg_get_phrase_group_count() = 0;
    /**个性短语导入
    * @return
    */
    virtual Int32 cfg_phrase_import(const char* fname, Uint32 is_over_write) = 0;
    /**个性短语导出
    * @return
    */
    virtual Int32 cfg_phrase_export(const char* fname, Uint32 group_id) = 0;
public:
    /**清理自造词
    * @return
    */
    virtual Int32 cfg_usword_reduce(Uint32 percent) = 0;
    /**获取自造词文件大小
    * @return
    */
    virtual Int32 cfg_usword_getsize() = 0;
    /**导入中文自造词
    * @return
    */
    virtual Int32 cfg_usword_import(const char* file_path) = 0;
    /**导出中文自造词
    * @return
    */
    virtual Int32 cfg_usword_export(const char* file_path) = 0;
    /**导入英文自造词
    * @return
    */
    virtual Int32 cfg_ueword_import(const char* file_path) = 0;
    /**导出英文自造词
    * @return
    */
    virtual Int32 cfg_ueword_export(const char* file_path) = 0;
    /** 根据unis 删除中文自造词，要求传入uni有0结尾可以计算长度
    * @return
    */
    virtual Int32 cfg_delete_cnword_byUni(const Uint16* uni) = 0;
    /**导入VK自造词
    * @return
    */
    virtual Int32 cfg_vkword_import(const char* file_path,
                                    Uint32 is_over_write) = 0;
    /**导出VK自造词
    * @return
    */
    virtual Int32 cfg_vkword_export(const char* file_path) = 0;
    /**导入个性化信息文件
    * @return
    */
    virtual Int32 cfg_usrinfo_import(const char* file_path) = 0;
    /**导出个性化信息文件
    * @return
    */
    virtual Int32 cfg_usrinfo_export(const char* file_path) = 0;
    /**获取细胞词库数量
    * @return
    */
    virtual Int32 cfg_get_cell_count() = 0;
    /**按索引获取细胞词库内容
    * @return
    */
    virtual CellInfo* cfg_get_cell_info_by_index(Uint32 idx) = 0;
    /**按cellid获取细胞词库内容
    * @return
    */
    virtual CellInfo* cfg_get_cell_info_by_cellid(Uint32 cellid) = 0;
    /**获取流行词库cellid
    * @return
    */
    virtual Int32 cfg_cell_get_popword_cellid() = 0;
    /**获取云优化词库cellid
    * @return
    */
    virtual Int32 cfg_cell_get_sysword_cellid() = 0;
    /**获取系统词库版本号
    * @return
    */
    virtual Int32 cfg_cell_get_sysword_ver() = 0;
    /**获取云白名单版本号
    * @return
    */
    virtual Int32 cfg_cell_get_cloud_white_ver() = 0;
    /**安装细胞词库
    * @return
    */
    virtual Int32 cfg_cell_install(const char* cell_path) = 0;
    /**卸载细胞词库
    * @return
    */
    virtual Int32 cfg_cell_uninstall(Uint32 cell_id) = 0;
    /**启用/关闭细胞词库
    * @return
    */
    virtual Int32 cfg_cell_enable(Uint32 cell_id, Uint32 is_enable) = 0;
    /**获取最近产生的自造词数量（不包含最近一天内的）
    * @return
    */
    virtual Int32 cfg_usall_get_recent_count(Uint32 days) = 0;
    /**导入所有同步类词（PC使用，参数merge表示合并的数量，返回值表示实际导入的数量）
     * @return
     */
    // 用户主动导入词库和旧词库升级会调用 //
    virtual Int32 cfg_usall_import_with_count(Uint32& merge_cnt, const char* file_path) = 0;
    /**导入所有同步类词
    * @return
    */
    virtual Int32 cfg_usall_import(const char* file_path) = 0;
    /**清理所有词库：细胞词、(中/英)自造词、联系人词、注音用户自造词///
    * @return
    */
    virtual Int32 cfg_usall_reset() = 0;
    /**导出所有同步类词
    * @return
    */
    virtual Int32 cfg_usall_export(const char* file_path) = 0;
    /**导入所有同步类词(proto格式)
    * @return
    */
    virtual Int32 cfg_usall_import_proto(const char* file_path) = 0;
    /**导出所有同步类词(proto格式)
    * @return
    */
    virtual Int32 cfg_usall_export_proto(const char* file_path) = 0;
    /**导入用户黑名单词典(proto格式)
    * @return
    */
    virtual Int32 cfg_user_black_import(const char* file_path) = 0;
    /**导出用户黑名单词典(proto格式)
    * @return
    */
    virtual Int32 cfg_user_black_export(const char* file_path) = 0;
    /**设置细胞词库地域属性
    * @return
    */
    virtual Int32 cfg_cell_set_loc_type(Uint32 cell_id, Uint16 loc_type) = 0;
    /**设置细胞词库安装时间属性
    * @return
    */
    virtual Int32 cfg_cell_set_install_time(Uint32 cell_id, Uint32 install_time) = 0;
    /**注音自造词导入
    * @return
    */
    virtual Int32 cfg_import_zyword(const char* filename) = 0;
    /**注音自造词导出
    * @return
    */
    virtual Int32 cfg_export_zyword(const char* filename) = 0;
    /**符号数据导入
    * @return
    */
    virtual Int32 cfg_sym_import(const char* _fname, Uint32 is_over_write) = 0;
    /**符号数据导出
    * @return
    */
    virtual Int32 cfg_sym_export(const char* _fname) = 0;
    /**安卓用户『最近』符号数据导入
     * usr_sym为用户常用符号，Unicode，从前往后，每个符号以0结束，最长不超过32个字符。
     * sym_cnt为用户常用符号个数，最多32个，因为常用字符不会超过32个符号；
     * @return 0表示正确导入，-1表示失败
     */
    virtual Int32 cfg_usr_sym_import(const Uint16** usr_sym, Uint16 sym_cnt) = 0;
    /**符号联想数据导入
    * @return
    */
    virtual Int32 cfg_sylian_import(const char* _fname, Uint32 is_over_write) = 0;
    /**符号联想数据导出
    * @return
    */
    virtual Int32 cfg_sylian_export(const char* _fname) = 0;
    /**仓颉数据导入
    * @return
    */
    virtual Int32 cfg_cangjie_import(const char* filename) = 0;
    /**仓颉数据导出
    * @return
    */
    virtual Int32 cfg_cangjie_export(const char* filename) = 0;
    /**获取注音字库版本号
    * @return
    */
    virtual Int32 cfg_get_zhuyin_hz_version() = 0;
    /**获取注音词库版本号
    * @return >= 0 表示成功获取，-1表示iptcore无效或者注音不存在
    */
    virtual Int32 cfg_get_zhuyin_cz_version() = 0;
    // 注音词库属性 ///
    virtual ECoreZhuyinCZType cfg_get_zhuyin_cz_info() const = 0;
    /**获取仓颉字库版本号
    * @return
    */
    virtual Int32 cfg_get_cangjie_version() = 0;
    /** 获取手写版本号
    * @return 手写版本号
    */
    virtual Int32 cfg_get_hw_version() = 0;
    /** 获取生僻字手写版本号
    * @return 生僻字手写版本号
    */
    virtual Int32 cfg_get_hw_rare_version() = 0;
public:
    /**获取关键词词库数量
    * @return
    */
    virtual Int32 cfg_get_kwd_cell_count() = 0;
    /**按索引获取关键词词库内容
    * @return
    */
    virtual KwdCellInfo* cfg_get_kwd_cell_info_by_index(Uint32 idx) = 0;
    /**按关键词获取细胞词库内容
    * @return
    */
    virtual KwdCellInfo* cfg_get_kwd_cell_info_by_cellid(Uint32 cellid) = 0;
    /**安装关键词词库
    * @return
    */
    virtual Int32 cfg_kwd_cell_install(const char* cell_path) = 0;
    /**卸载关键词词库
    * @return
    */
    virtual Int32 cfg_kwd_cell_uninstall(Uint32 cell_id) = 0;
    /**专供Android6.5->7.0版本解决保留旧版不带精确/模糊功能的颜文字自造数据提供的安装接口
    * 5G 内核 接口表示 安装颜文字 kwd 文件
    * @return
    */
    virtual Int32 cfg_kwd_emoticon_cell_install(const char* file_path) = 0;
    /**关键词词库启用/停用
    * @return
    */
    virtual Int32 cfg_kwd_cell_enable(Uint32 cell_id, Uint32 is_enable) = 0;
    /**获取多媒体词库版本号
    * @return
    */
    //virtual Uint32 cfg_kwd_get_media_version() = 0;
    /**导出关键词词库内容
    * @return
    */
    virtual Int32 cfg_kwd_export(const char* file_path) = 0;
    /**获取歇后语词库的版本号
    * @return
    */
    virtual Uint32 cfg_xhy_get_version() = 0;
    /**导出歇后语词库
    * @return
    */
    virtual Int32 cfg_xhy_export(const char* _fname) = 0;
    /**获取歇后语词库数量
    * @return
    */
    virtual Int32 cfg_get_xhy_cell_count() = 0;
    /**按索引获取歇后语词库内容
    * @return
    */
    virtual KwdCellInfo* cfg_get_xhy_cell_info_by_index(Uint32 idx) = 0;
    /**按cellid获取歇后语词库内容
    * @return
    */
    virtual KwdCellInfo* cfg_get_xhy_cell_info_by_cellid(Uint32 cellid) = 0;
    /**安装歇后语词库
    * @return
    */
    virtual Int32 cfg_xhy_cell_install(const char* cell_path) = 0;
    /**卸载歇后语词库
    * @return
    */
    virtual Int32 cfg_xhy_cell_uninstall(Uint32 cell_id) = 0;
    /**获取idm词库数量
    * @return
    */
    virtual Int32 cfg_get_idm_cell_count() = 0;
    /**按索引获取idm词库内容
    * @return
    */
    virtual IdmCellInfo* cfg_get_idm_cell_info_by_index(Uint32 idx) = 0;
    /**按cellid获取idm词库内容
    * @return
    */
    virtual IdmCellInfo* cfg_get_idm_cell_info_by_cellid(Uint32 cellid) = 0;
    /**安装idm词库
    * @return
    */
    virtual Int32 cfg_idm_cell_install(const char* cell_path) = 0;
    /**卸载idm词库
    * @return
    */
    virtual Int32 cfg_idm_cell_uninstall(Uint32 cell_id) = 0;
    /**导出idm文件
    * @return
    */
    virtual Int32  cfg_idmap_export(const char* _fname) = 0;
    /**语音查找表情, _str 输入unis串
    * @return
    */
    virtual Int32 cfg_keyword_find_voice_lian(Uint16* _str, Uint32 _len,
            Uint16* emoji_list,
            Uint32* emoji_cnt, Uint16 emoticon_list[5][64], Uint32* emoticon_cnt) = 0;
    /**语音联想查找彩蛋, _str: 输入unis串， egg_value:输出内容
    * @return
    */
    virtual Int32 cfg_keyword_find_voice_egg(Uint16* _str, Uint32 _len, Uint16* egg_value) = 0;
public:
    /**导入otherword文件
    * @return
    */
    virtual Int32 cfg_otherword_import(const char* fname, Uint32 is_over_write) = 0;
    /**导出otherword文件
    * @return
    */
    virtual Int32 cfg_otherword_export(const char* fname, Uint32 group_id) = 0;
    /**获取otherword的分组数
    * @return
    */
    virtual Int32 cfg_otherword_group_count() = 0;
    /**获取otherword的分组信息
    * @return
    */
//    virtual Int32 cfg_otherword_group_info(s_phrase_group_info* _group_info,
//                                           Uint32 idx) = 0;
    /**编辑otherword的分组信息
    * @return
    */
//    virtual Int32 cfg_otherword_group_edit(s_phrase_group_info* _group_info,
//                                           Uint32 option) = 0;
    /**获取otherword某个分组下的条目数
    * @return
    */
    //virtual Int32 cfg_otherword_item_count(Uint32 group_id) = 0;
    /**获取otherword的条目信息
    * @return
    */
    //virtual Int32 cfg_otherword_item_info(s_phrase_info* _phrase_info,
    //                                      Uint32 idx) = 0;
    /**编辑otherword的条目信息
    * @return
    */
    //virtual Int32 cfg_otherword_item_edit(s_phrase_info* _phrase_info,
    //                                      Uint32 option) = 0;
    /**获取叠字词库的状态信息
    * @return
    */
    //virtual Int32 cfg_otherword_diezi_get_status() = 0;
    /**启用/停用叠字词库
    * @return
    */
    //virtual Int32 cfg_otherword_diezi_enable(Uint32 is_enable) = 0;

    /**导出生僻字用户词库
    * @return
    */
    virtual Int32 cfg_rare_user_word_export(const char* fpath) = 0;

    //// 这两个关于卡片内输入前预测功能的接口废弃了！！！！！！（空实现） ///
    virtual Int32 cfg_sent_pre_install(const char* file_path) = 0;
    virtual Int32 cfg_sent_pre_uninstall() = 0;
public:
    /**获取某个字的读音
    * @return
    */
    virtual Int32 cfg_util_get_hw_py(Uint16 _unicode, Uint16* pystrs, Uint8 is_all) = 0;
    virtual Uint32 cfg_kwd_get_search_version() = 0;
    /**覆盖导入英文自造词库
    * @return
    */
    //virtual Int32 cfg_util_import_enword_overwrite(const char* filename) = 0;
    /**覆盖导入中文自造词库
    * @return
    */
    //virtual Int32 cfg_util_import_cnword_overwrite(const char* filename) = 0;
    /**获取自造词总数(用于清理)
    * @return
    */
    virtual Int32 cfg_util_usword_get_cnword_count() = 0;
    /**获取叠字的读音数据
    * @return
    */
    //virtual Int32 cfg_util_get_diezi_py(Uint16 _unicode, Uint8* pystrs) = 0;
    /**用户自造词本地备份
    * @return
    */
    virtual Int32 cfg_util_usrword_backup() = 0;
    /**用户自造词本地检测
    * @return
    */
    virtual Int32 cfg_util_usrword_check() = 0;
    /**根据简体词的unicode获取繁体
    * @return
    */
    virtual Int32 cfg_util_get_ft_by_uni(Uint16* _unicode) = 0;
    //virtual Int32 cfg_util_get_py_by_zids(Uint16* _zids, Uint32 _len,
    //                                      Uint16* _pinyin) = 0;
    /**判断拼音是否为长简拼
    * @return
    */
    //virtual Int32 cfg_util_get_py_islong_jp() = 0;
    /**判断是否触发云输入
    * @return
    */
    //virtual Int32 cfg_util_cloud_trigger(Uint32 level) = 0;
    /**获取适配的三维词库类别版本号
    * @return
    */
    //virtual Int32 cfg_util_get_gram_cate_ver() = 0;
    /**导出双拼方案到文件
    * @return
    */
    virtual Int32 cfg_util_export_sp_file(const char* fname) = 0;
    /**获取双拼声母映射
    * @return
    */
    virtual Int32 cfg_util_get_spmap_sheng(char keychar, char* sp_list) = 0;
    /**获取双拼韵母映射
    * @return
    */
    virtual Int32 cfg_util_get_spmap_yun(char keychar, char* sp_list) = 0;
    /**获取自定义输入模式最长输入码长度
    * @return
    */
    //virtual Int32 cfg_olddef_first_byte() = 0;
    //virtual Int32 cfg_keyword_find_voice_lian(Uint16* _str, Uint32 _len,
    //        Uint16* emoji_list, Uint32* emoji_cnt, Uint16* emoticon_list,
    //        Uint32* emoticon_cnt) = 0;
    //virtual Int32 cfg_keyword_find_voice_egg(Uint16* _str, Uint32 _len,
    //        Uint16* egg_value) = 0;
    //virtual Int32 cfg_query_get_cand_context(Uint32 idx, Uint16* cz_out) = 0;
    ////////////////////////////////////////////////////////////////////
    /**从文件导入联系人
    * @return
    */
    virtual Int32 cfg_contact_import(const char* fname, Uint32 is_retain_black) = 0;
    /**导出联系人到文件
    * @return
    */
    virtual Int32 cfg_contact_export(const char* fname) = 0;
    /**获取所有联系人名称（proto格式）
    * @return
    */
    virtual const Uint8* cfg_contact_export_names(Uint32& len) = 0;
    /**清空所有联系人
    * @return
    */
    virtual Int32 cfg_contact_reset() = 0;
    /**逐条导入联系人数据,数据字典准备
    *@return
    */
    virtual Int32 cfg_contact_build_start() = 0;
    /**逐条导入联系人数据，插入联系人名称
    *@return
    */
    virtual Int32 cfg_contact_build_add_name(const Uint16* name_str) = 0;
    /**逐条导入联系人数据，为联系人添加属性和value
    *@return
    */
    virtual Int32 cfg_contact_build_add_value(const Uint16* attri_str, const Uint16* value_str) = 0;
    /**逐条导入联系人数据,导入结束，保存联系人词典
    *@return
    */
    virtual Int32 cfg_contact_build_end(Uint32 is_retain_black) = 0;
    /**覆蓋安裝时客户端上传以拉黑的联系人(Android)
    *@return
    */
    virtual Int32 cfg_contact_build_add_black_name(const Uint16* del_name, Uint32 del_len) = 0;
    /**语音查找联系人(非通讯录)
    * @return
    */
    virtual Int32 cfg_contact_voice_find(const Uint16* _ori_word, Uint16* _ret_word) = 0;
    /**语音查找联系人(通讯录)
    * @return
    */
    virtual Int32 cfg_contact_voice_find_addressbook(const Uint16* _ori_word, Uint16* _ret_word) = 0;
    /**语音查询联系人信息(通讯录)
    * @return
    */
    virtual Int32 cfg_contact_voice_query_addrbook(const Uint16* _ori_word) = 0;
    /**语音查找联系人信息(通讯录)，获取查询到的联系人信息个数
    * @return
    */
    virtual Int32 cfg_contact_voice_get_count() const = 0;
    /**语音查找联系人信息(通讯录)，获取查询到的联系人信息内容
    * @return
    */
    virtual const ContactItem& cfg_contact_voice_get_item(Uint32 idx) const = 0;
    /**重载三维词库
     * @return
     */
    virtual Int32 cfg_install_gram(const char* gram_path) = 0;
    /**仅供测试使用
    * @return
    */
    virtual Int32 cfg_adjust_sylian_relation(Uint16* pre_str, Uint32 pre_len,
            Uint16* tail_str, Uint8 tail_len) = 0;
    //安装出词干预库//
    virtual Int32 cfg_install_wordout_checker(const char* file_path) = 0;
    //安装智能云词活动配置
    virtual Int32 cfg_install_cloud_campaign(const char* path) = 0;
    //卸载智能云词活动配置
    virtual Int32 cfg_uninstall_cloud_campaign() = 0;
    // 安装通用触发词
    virtual Int32 cfg_install_common_triger_words(const char* path) = 0;
    // 卸载通用触发词
    virtual void cfg_uninstall_common_triger_words() = 0;
    //安装云输入敏感词库,导入为全量替换
    virtual Int32 cfg_install_cloud_sensitive_dict(const char* file_path) = 0;
    //卸载云输入敏感词库，并且清空本地数据。
    virtual void cfg_uninstall_cloud_sensitive_dict() = 0;
    //场景映射表导入//
    virtual Int32 cfg_import_app_map(const char* filename) = 0;
    //获取场景映射表版本号//
    virtual Uint32 cfg_app_get_map_version() = 0;
    //刷新内核，目前支持部分后下发词库 - 针对现在IOS后下发 在load内核的时候传入固定后下发路径 所以path没有意义//
    virtual Int32 cfg_core_refresh(ECoreFileType type, const char* path) = 0;
    // iOS专用，双进程时如果是文件映射的资源文件类型，一个进程更新后调用这个接口对另一个进程的文件进行reopen//
    // 端上需进行保证：1.当需要切换进程时调用这个接口，内核判断资源文件更新情况 //
    // 2. 不会出现在当前进程写入文件的过程中另一个进程也在写入(此时会出现另一个进程丢失更新) //
    // 3. 如果一个进程在更新，另一个进程在查词，触发不查询逻辑 //
    virtual Int32 cfg_core_ios_reopen_mmap() = 0;
    //对后下发文件进行卸载(释放内存占用)，端上调用场景：在不通知内核的某些场景下对文件进行unload//
    virtual Int32 cfg_core_unload(ECoreFileType type) = 0;
    virtual Int32 cfg_nnranker_refresh(const char* config_path,
                                       const char* word_data_path, const char* model_file_path) = 0;
    //设置debug log 路径 0成功，-1失败//
    virtual Int32 cfg_set_debug_log_path(const char* path, Int32 is_append) = 0;
    //获取nnranker的运行信息，包括跑模型的候选个数和总的时间////
    virtual const nn_ranker_info* cfg_get_nnranker_info() = 0;
    virtual Int32 block_cloud_unis(const Uint16* unis, Uint32 len) = 0;
    virtual Uint32 reset_cloud_black() = 0;
    /// 导入明文自定义输入方案 ///
    /// good_cnt：成功条数（按文件行）
    /// bad_cnt：失败条数（按文件行）
    /// 返回值：
    ///     0：成功，至少一条有效，可能有失败条数
    ///     -1：失败，filename为nullptr
    ///     -11：失败，成功条数为0
    virtual Int32 cfg_import_olddef_txt(const char* filename, Uint32* good_cnt = nullptr,
                                        Uint32* bad_cnt = nullptr) = 0;
    /// 导入常用语内容 /// 内核测试使用，非端直接调用接口 ///
    /// 返回值：<0 导入出错，>0 导入数量 ///
    virtual Int32 cfg_common_quote_import(const char* fname) = 0;
    // 导入常用语内容，逐条导入-开始标记，内核开启文件写指针, 并关闭查词功能 ///
    virtual Int32 cfg_common_quote_add_line_start() = 0;
    // 导入常用语内容，数据导入  ///
    virtual Int32 cfg_common_quote_add_from_line(const Uint16* wline, Uint32 wlen) = 0;
    // 导入常用语内容，结束标记 ///
    virtual Int32 cfg_common_quote_add_line_finished() = 0;
    // 智能表单：个人信息联想功能关闭，调用该接口，清空用户个人信息数据 ///
    virtual Int32 cfg_person_info_reset() = 0;
    // 智能表单：导入个人化信息 逐条导入-开始标记，内核开启文件写指针, 并关闭查词功能 ///
    virtual Int32 cfg_person_info_add_line_start() = 0;
    /// 智能表单：导入个人化信息 逐条导入 导入结束调用 cfg_usrinfo_import_finished ///
    /// index_len：用户信息展示联想的最短索引长度，当索引长度大于 0 时，认为是联想候选，
    /// 如果导入内容为人名等需要展示在候选的, 则将索引长度设置为 0
    /// wline：个人信息字符串（邮箱，手机号等）
    /// wlen：字符串长度
    /// 返回值：<0 导入出错，=0 导入成功 ///
    virtual Int32 cfg_person_info_add_from_line(Uint32 index_len, const Uint16* wline, Uint32 wlen) = 0;
    /// 智能表单：导入个人化信息结束标志 ///
    /// 返回值：<0 结束出错，=0 结束成功 ///
    virtual Int32 cfg_person_info_add_finished() = 0;
public:
    /*** 以下函数返回值都无意义，含义和名字也无关 ***/
    virtual Int32 cfg_dct_cell_install_by_buff(const Uint8* buff, Uint32 buff_len) = 0;
    virtual Int32 cfg_dct_cell_word_in_dict(const Uint16* word, Uint32 word_len) = 0;
    virtual Int32 cfg_dct_cell_export_buff(const Uint8*& buff, Uint32& buff_len, Int32 cell_id) = 0;
    virtual Int32 cfg_dct_cell_reset_buff(Int32 cell_id) = 0;
    /*** 以上函数返回值都无意义，含义和名字也无关 ***/

#ifdef IPT_FEATURE_PC_ONLY
public:
    //
    enum PCImportSysUserTxtType : Uint32 {
        TypeSysUserTxt = 0, ///< 用户导入明文/服务端格式导入接口 ///
        TypeBatchTxt = 1, ///<  批量造词接口 ///
    };
    //
    /// 清空用户自造词(包括usr2旧的英文自造词) PC接口，只清理自造词 ///
    virtual Int32 cfg_pc_usrdict_reset() = 0;
    /// PC端用户批量造词导入接口 ///
    //virtual Int32 cfg_batchword_import(Uint32& error_cnt, const char* import_file, const char* error_file) = 0;
    /// pc上导入从老pc（深圳版本）中导出的自造词文件,unicode编码小端
    /// 导入文件每行的格式：中文“wo看见\tW'O'kan'jian'\t次数”，英文“donate\t次数”
    /// new_cnt新导入的条数，dup_cnt导入时词库中已存在的条数，file文件路径
    /// @return 返回-1表示失败，返回0表示成功
    virtual Int32 cfg_pc_oldusrword_import(Uint32& new_cnt, Uint32& dup_cnt, const char* file) = 0;
    /// PC端导入细胞词部分示例词库 ///
    virtual Int32 cfg_export_celldict_byid(DictExport* export_list, Uint32 cellid, Uint32 export_count) = 0;
public:
    /// 长句联想添加 ///
    virtual Int32 cfg_longterm_add(const Uint16* unis, Uint32 unis_len, Uint32 flag, const char* key = "") = 0;
    /// 长句联想删除 ///
    virtual Int32 cfg_longterm_del(const Uint16* unis, Uint32 unis_len) = 0;
    /// 长句联想的内容获取 ///
    /// is_only_custom = true 表示只获取用户添加或导入的类型, 用于端上 - [隐藏系统长句联想] //
    /// is_only_custom = false 表示导出长句联想全部内容, 系统预装类型为kCustom //
    virtual Int32 cfg_longterm_getall(Uint16* unis, bool is_only_custom) = 0;
    /// 长句联想导入 ///
    virtual Int32 cfg_longterm_import(const char* fname, Uint32& nSuccessCount) = 0;
    /// 长句联想导出 ///
    virtual Int32 cfg_longterm_export(const char* fname) = 0;
    ///长句联想恢复默认词库，参数为默认词库文件的路径，端上导入///
    virtual Int32 cfg_longterm_recover(const char* fname) = 0;
    /// 固守词库导入 ///
    /// PC端上使用cfg_fixterm_import接口用于将PC老内核的词库升级到现在的内核 //
    virtual Int32 cfg_fixterm_import(const char* fname) = 0;
    ///@parameter 固首词的key
    ///@return 返回值，返回-1表示异常，返回0表示无异常，如果词条存在则表示删除成功，不存在则表示未做操作
    virtual Int32 cfg_fixterm_del_bykey(const char* key) = 0;
    /// 固守词库导出 ///
    //virtual Int32 cfg_fixterm_export(const char* fname) = 0;
    ///PC导入服务器下发的txt文件-用于系统词格式的自造词，成功返回0失败返回-1///
    //virtual Int32 cfg_pc_import_extra_user_word(Uint32& success, Uint32& repeat_num, Uint32& error_num, const char* path,
    //const char* error_path) = 0;
    ///PC导入用户txt文件，成功返回0失败返回-1///
    /// 对应PC端上-设置项-批量造词的功能 和 导入用户词(txt格式), 导入内容去向->cz5user.bin//
    /// 不支持明文导出 //
    virtual Int32 cfg_pc_import_to_sysuser_from_txt(Uint32& success, Uint32& repeat_num, Uint32& error_num,
            const char* path, const char* error_path, PCImportSysUserTxtType type) = 0;
    ///  端上没调用 // 老PC才支持导出, 现在4.5后不需要 ///
    //virtual Int32 cfg_pc_export_local_user_word(Uint32& export_num, const char* path) = 0;
    ///  ///
    /// 对应PC端上-设置项-用户词库-导入词库-内容为加密之后的bin文件 //
    ///PC端导入接口，支持bin的加密格式导入, 内容包括<cnword>->usr3, <enword>->en5user, <sysword>->cz5user///
    virtual Int32 cfg_pc_import_user_word(Uint32& success, Uint32& repeat_num, Uint32& error_num, const char* path,
                                          const char* error_path) = 0;
    /// 对应PC端上-设置项-用户词库-导出词库-内容为加密之后的bin文件 //
    virtual Int32 cfg_pc_export_user_word(Uint32& export_num, const char* path) = 0;
    // 端上没调用 ///
    //virtual Int32 cfg_pc_import_word_form_bin(Uint32& success, Uint32& repeat_num, Uint32& error_num, const char* path) = 0;
    //
    //virtual void cfg_pc_remove_dict_cz_usr() = 0;
    // net_addr 生成bin文件 //
    // 用于从txt文件生成PC的网址邮箱文件//
    virtual Int32 cfg_pc_net_addr_gen_bin(const char* in_path, const char* bin_path) = 0;
    ///PC自定义短语 -恢复为默认文件（返回=0成功，返回<0失败）
    virtual Int32 cfg_pc_phrase_reset_default_bin() = 0;
    ///PC自定义短语 -导入个性短语，返回值表示导入个数
    virtual Uint32 cfg_pc_phrase_import(const char* f_in) = 0;
    ///PC自定义短语 -导出个性短语，返回值表示导出个数
    virtual Uint32 cfg_pc_phrase_export(const char* f_out) = 0;
    /// PC自定义短语 - 获取总个数
    virtual Uint32 cfg_pc_phrase_get_count(bool is_hide_default) = 0;
    ///PC自定义短语 - 按下标获取单个元素
    virtual void cfg_pc_phrase_get_item(PC_PhraseItem& item, Uint32 idx) = 0;
    ///PC自定义短语 - 添加一个个性短语（返回=0成功，返回<0失败）
    virtual Int32 cfg_pc_phrase_add(const PC_PhraseItem& item) = 0;
    ///PC自定义短语 - 添加（返回=0成功，返回<0失败）
    virtual Int32 cfg_pc_phrase_del(Uint32 idx) = 0;
    ///PC自定义短语 - edit（返回=0成功，返回<0失败）
    virtual Int32 cfg_pc_phrase_edit(Uint32 idx, const PC_PhraseItem& item) = 0;
    /// PC自定义短语 - 用户升级(传入PC老内核的文件和PC4.5的文件路径,允许为空) //
    /// 返回值>=0表示升级迁移成功的数量,返回<0失败 //
    virtual Int32 cfg_pc_phrase_update(const char* pc_old_txt, const char* pc_4_5_txt) = 0;
    ///导入品牌词和叠字，将会以覆盖方式导入到pc_otherword.dat
    ///@return 高32位表示成功导入的品牌词数量高32位0xffff表示品牌词导入失败，低32位表示成功导入的拆字数量，
    /// 低32位0xffff表示叠字导入失败
    ///
    virtual Int64 cfg_pc_otherword_diezi_import(const char* otherwordfname, const char* diezifname) = 0;
#endif
};

class PlatformEnv;

/*!资源文件类
*/
class IPT_IMPORT InputLib {
public:
    enum CallbackTag : Uint32 {
        ECBTAG_NETBEGAN = 0,
        ECBTAG_NETSTREAM = 32,
        ECBTAG_NETEND = ECBTAG_NETBEGAN + 64,
        ///
        ECBTAG_TIMEBEGAN = 65,
        ECBTAG_TIMEHW = 66, //手写计时器 //
        ECBTAG_TIMEBACK = 67,
        ECBTAG_TIMESLIP = 68, // 滑行输入计时器 //
        ECBTAG_CALC = 69, // 原用于计算器延时，现不再使用 //
        ///
        ECBTAG_TIME_KEY_CLICK = 70,
        ECBTAG_CLOUD_CAMPAIGN = 71,
        ///
        ECBTAG_TIMECLOUD = 72, /// 云输入-延后请求(旧的,即将废弃) ///
        ECBTAG_TIMECLOUDTO = 73, //云输入-网络超时(旧的,即将废弃)
        ECBTAG_TIMECLOUDDELAY = 74,//云输入请求后，开启一个延时，主要作用是延迟给客户端duty，等待云输入结果回来
        //ECBTAG_TIMECODECLICK,//codeclick事件，当次给端上刷新标志，真正的codeclick放在本次事件的下一次事件处理
        ///
        ECBTAG_CLOUD_TIME_OUT = 75, //云输入-网络超时
        ECBTAG_SUG_TIME_OUT = 76, /// sug-网络超时 ////
        ECBTAG_AI_CHAT_TIME_OUT = 77, /// Ai助聊-网络超时 ////
        ECBTAG_PREFETCH_TIME_OUT = 78, /// 云预取-网络超时 ////
        ///
        ECBTAG_CLOUD_TIME_DELAY = 79,/// 云输入-延后请求 ///
        //ECBTAG_AI_CHAT_TIME_DELAY, /// Ai助聊-延后请求(废弃) ///
        ///
        ECBTAG_AI_PAD_DELAY_REQUEST = 80, /// Ai助聊-延后请求 ///
        ECBTAG_AI_PAD_DELAY_OPEN_PEITU = 81, /// Ai助聊,延后打开神句配图(ios下的补丁逻辑) ///
        ECBTAG_EDITOR_DELAY_AFTER = 82, /// 延后处理编辑框文字变化事件(ios下的补丁逻辑) ///
        ////
        ECBTAG_CALC_MAX_LENGTH = 83, /// 计算器超长提示 //
        ECBTAG_IOS_MULTI_SYM = 84, /// iOS多符号功能键定时器，需要通知端上，根据TIP_MULTI_SYM_DOWN更新按键状态 //
        ///
        ECBTAG_AICAND_CANCEL = 85, /// 次cand的撤销功能 ///
        ///
        ECBTAG_AICAND_COMMON_HIGHEST = 86, ///起面板时通用最高优先级次cand ///
        //
        ECBTAG_AICAND_HIGH_EQ_NOTIFY = 87, ///次cand高情商提示 ///
        //
        ECBTAG_MAP_DATA_TIMEOUT = 88, /// 获取地图数据超时 ///
        //
        ECBTAG_CAND_ERINE = 89, /// erine结果进cand条的定时器 ///
        //
        ECBTAG_ADX_TIMEOUT = 90, /// 获取广告数据超时 ///
        ECBTAG_ADX_REPORT = 91, /// 延时合并处理广告事件上报 ///
        //
        ECBTAG_SEND_RESULT_DATA_SHOW = 92, ///发送后结果展示(5s)-暂未使用///
        ECBTAG_SEND_EVENT_WAIT = 93, ///等待发送的计时器(250ms)-暂未使用///
        //
        ECBTAG_CLOUD_TRIGGER_TIMEOUT = 94, // 如果云联想二刷(150ms)超时，通知客户端CLOUD_BUBBLE//
        ECBTAG_QUIC_STREAM_RECV = 95, // 从QUIC协议受到一个网络数据回包 ///
        //
        ECBTAG_CLOUD_PLACEHOLDER = 96, /// 云输入占位符消失的定时器 ///
        //
        ECBTAG_TIMEEND = 255,
        ECBTAG_REQUSET_URL_RESOURCE_BEGAN = 256,//256~511 为资源下载tag ///
        ECBTAG_REQUSET_URL_RESOURCE_END = 512,
        //
        ECBTAG_END = 513,
    };

    enum PatchFileType : Uint8 {
        PFT_MUT_CZ5DOWN = 0, /// 应用cz5down的补丁，内核完成文件替换，词典加载
        PFT_CZ5_CZ5DOWN = 1, /// 应用cz5->cz5down的补丁，内核完成文件替换，词典加载
        PFT_OTHER = 255, /// 其他文件类型，内核在原文件同级生成应用补丁后的文件
    };

#ifdef CRASH_VERSION
    struct SigInfo { //Android签名指针信息//
        void* void_vm;
        void* void_contxt;
        void* void_env;
        void* void_pkg;
        SigInfo(void* vm, void* contxt, void* env, void* pkg) :
            void_vm(vm),
            void_contxt(contxt),
            void_env(env),
            void_pkg(pkg) {
        }
    };
#endif
    enum CheckLicenseType : Uint8 {
        BD_INPUT = 0, ///默认是输入法模式 
        BD_CY = 1, /// 
        BD_NONE = 255, /// 无意义
    };
public:
    /// Android 三条路径统一 ///
    /// IOS 分为只读、可写、后发下(只读) ///
    static InputLib* create(PlatformEnv* platform_env, NetMan* net_man,
                            const char* immu_dict_dir, const char* mut_dict_dir, const char* immu_down_dir
#ifdef CRASH_VERSION
                            , SigInfo* sig_info
#endif
#ifdef CRASH_VERSION
                            , sig_debug_function call_back = nullptr
#endif
#ifdef PLATFORM_IOS
                            , bool enable_user_trace = true
#endif
#ifdef CRASH_VERSION
                            , CheckLicenseType type = BD_INPUT
#endif
    );  ///< 载入资源文件
    static void close(InputLib* input_lib);  ///< 关闭资源文件
    ConfigItems* get_config();  ///<获取ConfigItem对象
    static ConfigPad* create_config_pad(InputLib* _iptlib,
                                        ConfigPad::ConfigCallback*
                                        cfg_callback);  ///< 生成带session的ConfigPad对象
    //ConfigPad* get_config_pad();  ///<获取ConfigPad对象
    void discard_and_reload();  ///< 重载内核，丢弃内存中的未保存修改(设置项和输入面板是两个面板时使用)

    /**备份轨迹文件供上层使用。
    * @param backup_file_path — 备份文件到哪里。绝对路径。
    * @return 返回值=0表示执行成功; 返回值<0表示执行失败
    */
    virtual Int32 backup_trace_log(const char* backup_file_path) = 0;

    ///<获取系统词库版本号信息, IOS端调用，在未加载内核时获取词库版本//
    static Int32 get_cz_version(const char* dict_dir_imm, const char* dict_dir_mut, Uint32& main_ver, Uint32& zi_cnt,
                                Uint32& sys_ver, Uint32& cate_ver, Uint32& gram_ver);
    //Int32 install_gram(const char* gram_path);  ///<重载三维词库
    // 获取内核版本号修改 - 修改为 5.x.x.x 形式可读字符串 //
    static const char* core_version();  ///<获取内核版本号
    static Int32 get_core_version();  ///<获取内核版本号
    // 获取内核mlm模型so的版本号 = 0 表示so是空，>0 表示版本号 ///
    // 32位返回0表示不支持 ///
    static Uint32 get_mlm_so_version();
    // 获取传入的模型文件版本号,用于下发与安装等埋点
    //  -1表示so版本不兼容无法获取，
    // = 0 表示文件是空，>0 表示版本号 ///
    // 32 位也返回 -1 ///
    static Int32 get_mlm_dict_version(const char* file);
    //获取英文词库版本号//
    virtual Uint32 get_en_neo_sys_dict_version() = 0;
    ///获取中文词库版本号
    virtual Uint32 get_cz3_dict_sys_version() = 0;
    virtual Uint32 get_cz3_dict_gram_version() = 0;
    virtual Uint32 get_cz3_dict_cate_version() = 0;
    /// 获取cz5down状态
    /// -1 内核指针为空，这种情况下内核已无法工作
    /// -2 文件打开失败
    /// -101文件尺寸小于文件头
    /// -102文件尺寸与文件头记录不匹配
    /// -102文件尺寸与文件头记录不匹配
    /// -103文件uid不匹配
    /// -104文件版本不支持
    /// -3 汉字区长度不正确
    /// -4 索引树格式不正确
    /// -5 gram区不存在
    virtual Int32 get_cz5down_status() const = 0;
    virtual Uint32 get_slide_dict_version() = 0;
    virtual bool is_nnranker_work() = 0;
    // user trace
    ///@brief 返回轨迹log函数
    ///@return 内核轨迹log文件名
    virtual const char* get_trace_log() = 0;
    ///@brief 端上处理完了内核轨迹文件，通知内核删除内核轨迹文件。
    ///@return void
    virtual void reset_trace_log() = 0;
    /// 设置OEM名称，不设置则默认为主线
    virtual void set_oem_name(OEM_NAME oem_name);
    /// 设置是否需要开启内核线上log，默认不开启 ///
    virtual void set_online_log(CORE_ONLINE_LOG online_log);
    /// patch_file是补丁文件路径
    /// md5是应用补丁后，新文件的预期md5，固定长度32
    /// 应用补丁包，对于内核已知的可写目录的词典文件，old_file传空
    /// 非上述文件，需要传入old_file，例如下发的emoji.kwd等，内核自动生成old_file.patched文件
    /// 返回值=0代表成功，其他失败
    /// 1. 内核指针为空，这种情况下内核已无法工作，返回-1
    /// 2. 如果是无效的patch_file_type，返回-2
    /// 3. 根据补丁包和原始文件，生成临时文件：
    ///   a. 补丁包尺寸小于文件头尺寸，或者原始文件是空的，返回-101
    ///   b. 补丁包文件头uid不匹配，返回-102
    ///   c. 补丁包尺寸小于文件头的ctrl区数据尺寸，返回-103
    ///   d. 补丁包尺寸小于文件头的diff区数据尺寸，返回-104
    ///   e. 读取ctrl区数据失败，返回-111
    ///   f. 读取diff区数据失败，返回-113
    ///   g. 读取extra区数据失败，返回-115
    /// 4. MD5校验不通过，返回-3
    /// 5. 临时文件内容复制到新文件失败，返回-201
    virtual Int32 apply_patch(PatchFileType patch_file_type, const char* patch_file, const char* md5,
                              const char* old_file) = 0;
    virtual Int32 regist(const char* license) = 0;
    /// 设置quic使用基础的socket api，给安卓使用，其他平台不生效，安卓8以下需要设置为true
    virtual void set_quic_use_basic_api(bool on) = 0;
};

class PlatformEnvCallback {
public:
    /**
    *回调函数
    *@tag 唯一标示, 由PlatformEnv::run_in_main或PlatformEnv::run_delay传入
    */
    virtual void on_run_main(Uint32 tag) = 0;
};

/// @brief Ai助聊右上角图标状态的描述
///
enum AiIconState : Uint32 {
    AI_ICON_NONE = 0, ///< 不显示AI_ICON(目前不存在这个状态) ///
    AI_ICON_NORMAL = 1, ///< AI_ICON 的常规状态 ///
    AI_ICON_LOADING = 2, ///< AI_ICON 处在loading状态 ///
};

/// @brief Ai助聊面板信息类
///
class AiPadInfo {
public:
    /// @brief Ai助聊面板Tab页状态 ///
    enum AiPadTabType : Uint32 {
        AI_PAD_TAB_NONE = 0, ///<助聊面板处在关闭状态
        //AI_PAD_TAB_AI_CHAT = 1, ///< AI趣聊
        AI_PAD_TAB_AI_COMPOSE = 2, ///< AI帮写(原名 NLP撰写)
        AI_PAD_TAB_AI_CORRECT = 3, ///< AI校对(原名 NLP纠错)
        AI_PAD_TAB_SENT_GIF = 4, ///< 神句配图
        AI_PAD_TAB_TEXT_EMOJI = 5, ///<字符表情,
        AI_PAD_TAB_SPECIAL_FONT = 6, ////<特效字体
        AI_PAD_TAB_WENXIN = 7, ///<文心一言
        AI_PAD_TAB_HIGHEQ = 8, ///<高情商
        AI_PAD_TAB_MARK = 9, ///<种草文
        AI_PAD_TAB_AI_WENWEN = 10, ///<AI问问
        AI_PAD_TAB_LOVE_MASTER = 11, // < 恋爱大师
        AI_PAD_TAB_AI_EMOTICON = 12, // < AI表情创作
    };
    ///
    enum AiPadState : Uint32 {
        /// 告诉客户端,是否符合发起条件, 以及不发起的原因.
        /// 这里先举例这几种类型, 后续细节可以增删 ///
        AI_PAD_STAT_NORMAL = 0, /// 面板状态正常, 请获取内容并正常显示 ///
        AI_PAD_STAT_APP_NOT_SUPPORT = 1, /// 当前App不支持该功能 ///
        AI_PAD_STAT_TEXT_TOO_SHORT = 2, /// 当前编辑框文本过少 ///
        AI_PAD_STAT_TEXT_TOO_LONG = 3, /// 当前编辑框文本过长 ///
        AI_PAD_STAT_NET_ERROR = 4, ///<网络错误, 导致功能不可用 ///
        AI_PAD_STAT_LOADING = 5, /// 正在请求信息 ///
        AI_PAD_STAT_HIT_BLACK_LIST = 6, /// 请求文本命中黑名单,不允许发起请求 ///
        AI_PAD_STAT_TEXT_NO_TEXT = 7, /// 当前无请求文本 //
    };

public:
    ///< 获取Ai助聊面板Tab页状态
    virtual AiPadTabType get_ai_pad_tab() const = 0;
    ///<获取面板的错误状态,是否符合发起条件, 以及不发起的原因等等 ///
    virtual AiPadState get_pad_state() const = 0;
    ///< 获取AI助聊面板是否在Loading的状态
    virtual bool get_ai_pad_is_loading() const = 0;
    ///< 获取Ai面板显式的候选词内容 /趣聊/帮写/校对
    virtual Uint32 get_cand_cnt() const = 0;
    virtual const CandInfo* get_cand_item(Uint32 idx) const = 0;
    ///< 获取Ai面板显示文本(Ai纠错待修改文本, 字符表情/神句配图 的请求文本 等) ///
    virtual const Uint16* get_text_ptr() const = 0;
    virtual Uint32 get_text_len() const = 0;
    ///<获取Ai校对纠错预上屏信息(只对Android有用) ///
    virtual const CandInfo* get_ai_correct_inline_info() const = 0;
    ///<获取当前Ai助聊面板是否为自动展开(自动展开的不需要记忆tab) ///
    virtual bool is_auto_open() const = 0;
};

/// @brief 输入面板类
///

enum PopMenuId {
    MENU_NONE = 0,   ///空菜单项///
    MENU_USR_RSTO = 1, /// 恢复默认词频 ///
    MENU_USR_DEL = 2, /// 删除自造词 ///
    MENU_CONTACT_SHOW = 3, /// 联系人信息 ///
    MENU_CONTACT_DEL = 4, /// 取消联系人关联 ///
    MENU_WORD_DEL = 5, /// 删除该词(PC、移动端共用) ///
    MENU_PC_FIXTERM = 6,   ///PC端固定首位///
    MENU_PC_DEL_FIXTERM = 7,    ///PC端取消固首///
};

/// @brief 内核作为广告SDK的接口，目前只有穿山甲ADX
///
class AdManager {
public:
    class Response {
    public:
        enum AdType : Uint8 {
            TYPE_NONE = 0,
            TYPE_CSJ = 1, // 目前只有穿山甲，实际上还有网盟，但是云端是可以不断接入的，客户端不能每次都调整
            TYPE_MAX = 255, // 兜底数据，目前仅用在应用意图卡片 POS_APP_INTENTION_CARD
        };
        virtual AdType get_type() const = 0;
        virtual const Uint16* get_app_name() const = 0;
        virtual Uint32 get_app_name_len() const = 0;
        /// 出价 注：cpm 分
        virtual Uint64 get_price() const = 0;
        virtual bool is_allow_act() const = 0; // 促活
        virtual const char* get_act_url() const = 0; // 促活deeplink内容, 给iOS用于检测app是否存在
        virtual Uint32 get_act_url_len() const = 0; // 促活deeplink长度, 给iOS用于检测app是否存在
        virtual bool is_allow_zhitou() const = 0; // 拉新 直投
        virtual bool is_allow_channel() const = 0; // 拉新 渠道
        /// 六要素
        virtual const char* get_icon() const = 0; /// 图标
        virtual Uint32 get_icon_len() const = 0;
        virtual const char* get_app_version() const = 0; /// 版本号
        virtual Uint32 get_app_version_len() const = 0;
        virtual const char* get_developer_name() const = 0; /// 开发者名称
        virtual Uint32 get_developer_name_len() const = 0;
        virtual const char* get_desc_url() const = 0; /// 描述地址
        virtual Uint32 get_desc_url_len() const = 0;
        virtual const char* get_privacy_policy_url() const = 0; /// 隐私协议地址
        virtual Uint32 get_privacy_policy_url_len() const = 0;
        virtual const char* get_permissions_url() const = 0; /// 权限地址
        virtual Uint32 get_permissions_url_len() const = 0;
        /// 拉新促活sug交互
        virtual const char* get_icon_url() const = 0;
        virtual Uint32 get_icon_url_len() const = 0;
        virtual Uint32 get_icon_color() const = 0;
    };
    /// 广告位置
    enum AdPos : Uint8 {
        POS_SUG_CAND = 0, /// Sug条
        POS_APP_INTENTION_CARD = 1, /// 应用意图卡片
    };
    enum ActionType : Uint8 {
        ACTION_NONE = 0, /// 无效
        ACTION_ACT = 1, /// 促活，deeplink
        ACTION_ZHITOU = 2, /// 拉新，直投
        ACTION_CHANNEL = 3, /// 拉新，渠道
    };
    struct AdAction {
        ActionType type;
        const char* url;
    };
    /// 加载ADX广告接口，完成后会有duty回调
    /// 返回发起广告请求的广告数量，用于客户端埋点，可能为0
    virtual Uint32 load_ad() = 0;
    /// 接到duty回调后，可以调用该接口获取广告信息
    virtual Uint32 get_res_cnt() = 0;
    virtual const Response* get_res_item(Uint32 idx) = 0;
    /// 获取当前云端实际上返回的广告数量，用于客户端埋点。调用后会清空，调用前会累加
    virtual Uint32 get_ori_rsp_cnt() = 0;
    ///
    virtual SugSourceType get_scene() const = 0;
public:
    /// 如果超过客户端限制时间，仍然没有返回ADX广告结果，请调用这个接口，传回限制的超时时间（单位：毫秒）
    virtual void time_out(Uint32 max) = 0;
    /// 如果有多个词都竞价成功，每一个词都需要调用一次，包括后面的fail和show方法
    virtual void win(Uint32 idx) = 0;
    /// code: 1 价格低于网盟（需要传竞价成功者的price，单位分）；2 未满足促活>直投>渠道的规则（例如网盟有促活，ADX只有渠道）
    virtual void fail(Uint32 idx, Uint32 code, Uint64 price) = 0;
    /// 曝光（客户端正常应该和竞价成功一起调用？），border表示广告展示区域
    virtual void show(Uint32 idx, const s_Rect_v2& border = {}) = 0;
    /// 点击，直接返回操作类型和链接
    /// border: 广告的展示区域
    /// down_point: 点击广告的按下坐标
    /// up_point: 点击广告的抬起坐标
    virtual AdAction click(Uint32 idx,
                           const s_Rect_v2& border = {},
                           const s_Point_v2& down_point = {},
                           const s_Point_v2& up_point = {}) = 0;
    /// 点击，给iOS使用，iOS不能在内核接到广告数据的时候判定是否安装了app
    /// border: 广告的展示区域
    /// down_point: 点击广告的按下坐标
    /// up_point: 点击广告的抬起坐标
    virtual AdAction click(Uint32 idx, ActionType type,
                           const s_Rect_v2& border = {},
                           const s_Point_v2& down_point = {},
                           const s_Point_v2& up_point = {}) = 0;
public:
    virtual void download_start(Uint32 idx) = 0;
    virtual void download_finish(Uint32 idx) = 0;
    virtual void install_finish(Uint32 idx) = 0;
public:
    virtual void open_url_app(Uint32 idx) = 0;
    virtual void open_fallback_url(Uint32 idx) = 0;
    virtual void dpl_success(Uint32 idx) = 0;
    virtual void dpl_failed(Uint32 idx) = 0;
};

class IPT_IMPORT InputPad {
public:
    enum ReflashFlag : Uint64 {
        REFL_SHOW = (1ULL << 1),               ///< 刷新InputShow
        REFL_CAND = (1ULL << 2),               ///< 刷新Cand区
        REFL_LIST = (1ULL << 3),               ///< 刷新List区
        REFL_KEY = (1ULL << 4),                ///< 刷新Key区
        REFL_TRACK = (1ULL << 5),              ///< 刷新轨迹区
        REFL_SUG = (1ULL << 6),                ///< 刷新sug区
        REFL_SUG_CARD = (1ULL << 7),           ///< 刷新sug卡片区
        REFL_LAYOUT = (1ULL << 9),             ///< 刷新Layout区(键盘布局)
        REFL_TIPS = (1ULL << 10),              ///< 刷新Tips内容
        REFL_CANDINFO = (1ULL << 11),          ///< 刷新CandInfo区
        REFL_CONTACT = (1ULL << 12),           ///< 刷新Contact区
        REFL_SRV_CLD_WHITE_VER = (1ULL << 13), ///< 刷新服务端云输入白名单版本号
        REFL_EGG = (1ULL << 14),               ///< 刷新彩蛋内容
        REFL_TOP_PROMOTION = (1ULL << 16),     ///< 刷新PC输入法6号位
        REFL_SUG_SELECTION = (1ULL << 19),      ///< 刷新sug选择的区域
        REFL_SUG_CARD_SELECTION = (1ULL << 20),      ///< 刷新sug card选择的区域
        REFL_PRE_EXTRACT_SELECTION = (1ULL << 21),   ///< 刷新预上屏范围
        REFL_IPTCORE_TRACE = (1ULL << 22),           ///< 刷新轨迹
        REFL_TAB = (1ULL << 23),                      ///< 刷新tab区
        //REFL_ZJ_MODEL = (1 << 26),                 ///< 刷新整句预测模型，即需要取数据去跑模型
        REFL_CLOUD_BUBBILE = (1ULL << 26), ///<刷新OEM上的特定超会写区域，现在用于绑定云端触发词有无结果///
        REFL_TAB_FILTER = (1ULL << 24),  ///< 刷新PC 的tab筛选区域////
        REFL_PC_MORE = (1ULL << 25),     ///< 刷新PC的更多候选///
        REFL_INLINE_SHOW = (1ULL << 27), ///< 刷新inline输入码///
        REFL_RARE_CAND = (1ULL << 30),   ///< 刷新最近生僻字///
        ///
        ///Ai助理聊二期相关的刷新标志位 ///
        REFL_AI_ICON = (1ULL << 8),        ///< 刷新Ai助聊图标状态(目前只包含是否Loading的状态) ///
        REFL_AI_CAND = (1ULL << 15),        ///< 刷新次Cand内容 //
        REFL_AI_PAD = (1ULL << 18),        ///< 刷新Ai助聊面板内容 ///
        REFL_AI_PAD_LOADING = (1ULL << 28),        ///< 刷新Ai助聊面板是否Loading的状态 ///
        REFL_AI_CORRECT_INLINE = (1ULL << 17), /// 刷新Ai校对, 错误文本预上屏提示信息(只对Android有用) ///
        ///
        REFL_GESTURE = (1ULL << 29),  /// 更新手写笔势状态 ///
        REFL_TRIGER_WORD = (1ULL << 31), /// 刷新选择 触发词后的区域 ///
        REFL_IDENTITY_NUM_BEFORE = (1ULL << 32), /// 输入内容是身份证号 ///
        /// 位置上限64
    };
public:
    enum TraceType {
        TRACE_TYPE_NONE = 0x00, ///无意义
        TRACE_TYPE_BACKSPACE_TEXT = 0x01, ///回删上屏文字
        TRACE_TYPE_BACKSPACE_INPUT = 0x02, ///回删输入码
        TRACE_TYPE_HOTSPOT = 0x04, ///热区轨迹，上层配合调用export_mis_key_for_trace
        TRACE_TYPE_ZJ_INPUT = 0x08, ///整句预测轨迹，从insert取出上屏词
        TRACE_TYPE_LOG_FILE_WARNING = 0x10, // log文件大小超过预警值
        TRACE_TYPE_WORD_INFO = 0x20, /// 上屏词信息
        TRACE_TYPE_MIS_TOUCHINFO = 0x40 /// 误触信息收集
    };
public:
    enum PadId {
        PAD_NONE = 0, ///< 空面板(默认状态,无任何功能)
        PAD_BH = 1,   ///< 笔画
        PAD_WB9 = 2,  ///< 五笔9键
        PAD_WB26 = 3, ///< 五笔26键
        PAD_EN9 = 4, ///< 英文9键
        //5///< 英文9键大写
        PAD_EN26_LOWER = 6, ///< 英文26键小写
        PAD_EN26_UPPER = 7, ///< 内核对待PAD_EN26_LOWER, PAD_EN26_UPPER没有任何区别，当shift打开时，返回的pad_id是英文26键大写
        PAD_123_T9 = 8, ///< 数字9键输入
        PAD_123_26 = 9, ///< 数字26键输入
        PAD_PY9 = 10,  ///< 拼音9键
        PAD_PY26 = 11, ///< 拼音26键
        PAD_HW = 12,   ///< 手写
        //13///< 手写全屏
        PAD_SYM = 14, ///< 符号界面
        //15///< 中文大写
        PAD_ZY = 16,        ///<注音输入法
        PAD_CJ = 17,        ///<仓颉输入法
        PAD_EXTERNAL = 18,        ///<外部面板
        PAD_RARE_CHAIZI = 19, ///<生僻字键盘（部首拼音拆字）
        PAD_RARE_HW = 20,     ///<生僻字键盘（手写）
        //
        PAD_PY_GAME = 41,   ///<拼音游戏键盘
        PAD_WB_GAME = 42,   ///<五笔游戏键盘
        PAD_BH_GAME = 43,   ///<笔画游戏键盘
        PAD_HW_GAME = 44,   ///<手写游戏键盘
        PAD_VOICE = 45,  ///<语音输入面板
        PAD_URL = 46,  ///<URL输入面板
        PAD_MORE = 47, ///<更多候选面板(PAD_PY9 PAD_PY26 PAD_WB9 PAD_WB26 PAD_BH PAD_EN9 PAD_EN26_UPPER PAD_EN26_LOWER公用)
        //////////////////////
        PAD_SYM_EXT = 48, //特殊符号面板(Android仿iOS系统符号面板)
        PAD_CEILING = 255
    };

public:
    enum KeyID {
        //'0'~'9'按键
        FKEY_BACK = 0xE000,   ///< 退格键//
        FKEY_CLEAN = 0xE001,         ///< 清除键//
        FKEY_SPACE = 0xE002,         ///< 空格键//
        FKEY_ENTER = 0xE003,         ///< 回车键//
        FKEY_RETURN = 0xE004,        ///< 返回键//
        FKEY_SHIFT = 0xE005,         ///< 三态Shift键//
        FKEY_SHIFT2 = 0xE006,        ///< 两态Shift键//
        FKEY_MORECAND = 0xE007,      ///< 更多候选词键//
        FKEY_DELIMITER = 0xE008,     ///< 分隔符//
        FKEY_DELETE = 0xE009,        ///< PC上delete
        FKEY_TAB = 0xE00A,           ///< Tab键///
        ///////////////////////
        FKEY_ABC = 0xE000 + 64,         ///< ABC键//
        FKEY_123 = 0xE041,           ///< 123键//
        FKEY_SYM = 0xE042,           ///< 符号键//
        FKEY_MENU = 0xE043,          ///< 地球键//
        FKEY_CN = 0xE044,            ///< 英文下切到中文键//
        FKEY_LOCK = 0xE045,          ///< 符号面板下锁定键//
        FKEY_UP = 0xE046,            ///< 符号面板下向上键//
        FKEY_DOWN = 0xE047,          ///< 符号面板下向下键//
        FKEY_EN = 0xE048,            ///< 英文输入经过内核//
        FKEY_SINGLE = 0xE049,        ///< 单字/全部切换键//
        FKEY_CNEN = 0xE04A,          ///< 安卓上的中英切换键//
        FKEY_VOICE = 0xE04B,         ///< IOS语音按键//
        FKEY_MOVE_LEFT = 0xE04C,     ///< 左移光标//
        FKEY_MOVE_RIGHT = 0xE04D,    ///< 右移光标//
        FKEY_ROTATE = 0xE04E,        ///< 旋转屏幕//
        FKEY_SYM_EXT = 0xE04F,       ///< 切换到仿照iOS系统的符号面板
        FKEY_PC_OPEN_MORE = 0xE050,  ///< 打开PC更多候选
        FKEY_PC_CLOSE_MORE = 0xE051,  //< 关闭PC更多候选
        FKEY_RARE_CHAIZI_HW = 0xE052, //< 生僻字面板【部/写】切换键
        ///////////////////////
        FKEY_GAME_0 = 0xE000 + 128,   ///<游戏辅助键盘'0'-'9'键
        FKEY_GAME_1 = 0xE081,
        FKEY_GAME_2 = 0xE082,
        FKEY_GAME_3 = 0xE083,
        FKEY_GAME_4 = 0xE084,
        FKEY_GAME_5 = 0xE085,
        FKEY_GAME_6 = 0xE086,
        FKEY_GAME_7 = 0xE087,
        FKEY_GAME_8 = 0xE088,
        FKEY_GAME_9 = 0xE089,
        ///////////////////////
        //FKEY_DEFINED 内核不做处理直接上屏内容， 其他情况均应该传入对应的键值//
        FKEY_DEFINED = 0xE000 + 192,  ///<客户端自定义按键，上屏内容由客户端传入
        FKEY_BH_1 = 0xE0C1,  //横
        FKEY_BH_2 = 0xE0C2, //竖
        FKEY_BH_3 = 0xE0C3, //撇
        FKEY_BH_4 = 0xE0C4, //捺
        FKEY_BH_5 = 0xE0C5, //折
        FKEY_BH_6 = 0xE0C6, //通配符
        //////////////////////
        FKEY_LINE_FEED = 0xE0C7, //iOS换行, 内核发送action:
        FKEY_MULTI_SYM = 0xE0C8, //iOS多符号功能键
        FKEY_SPACE_0 = 0xE0C9, // 9键皮肤布局,0和空格键在一起的皮肤
        //
        FKEY_CEILING = 0xF8FF  ///Private Use Area (0xE000~0xF8FF)
    };

public:
    enum StateTrackType {
        STATETRACK_NONE = 0, ///< 无状态
        STATETRACK_HW,       ///< 正在手写状态
        STATETRACK_SYMHW,    ///< 正在虚框手写状态
        STATETRACK_SLIDE,    ///< 正在滑动输入状态
    };

public:
    enum InputType : Uint32 {         ///< 输入方式
        INPUTTYPE_CLICK = 0, ///< 普通点击
        INPUTTYPE_UP = 1,       ///< 上划
        INPUTTYPE_DOWN = 2,   ///< 下划
        INPUTTYPE_LEFT = 3,   ///< 左划
        INPUTTYPE_RIGHT = 4,  ///< 右划
        INPUTTYPE_LONG_PRESS = 5,  ///< 长按之后的选择选择
        INPUTTYPE_LONG_DOWN = 6,  ///< 长按按住不放（目前只针对shift键生效）
        INPUTTYPE_LONG_UP = 7,     ///< 长按松手（目前只针对shift键生效）
        INPUTTYPE_CLICK_DOWN = 8, //点击按下, 用于云输入预取
        INPUTTYPE_CLICK_MULTI_CHAR = 9, //PC输入法可以一次性输入多个输入码
        INPUTTYPE_EXACT_CLICK = 10, //精确点击
        //
        INPUTTYPE_CEILING = 255
    };
public:
    enum ListItemType {
        COMMEN_LIST_ITEM = 0,               ///< 不存在//
        PY_LIST_ITEM = (1 << 0),            ///< 拼音//
        BH_LIST_ITEM = (1 << 1),            ///< 笔画//
        SYM_LIST_ITEM = (1 << 2),           ///< 默认符号//
        EN_LIST_ITEM = (1 << 3),            ///< 英文//
        SYM_CATEGORY_LIST_ITEM = (1 << 4),  ///< 符号面板分类//
        ////////////以上是列表大类，相互独立////////////////
        EN_MIX_LIST_ITEM = (1 << 5),        ///< 拼音list或者笔画list中的英文选项//
        DEFINE_LIST_ITEM = (1 << 6),        ///< 默认符号中的自定义//
        CHECKED_LIST_ITEM = (1 << 7)        ///< 选中状态//
    };
public:
    enum PadSugState {
        PAD_SUG_STATE_NONE = 0,             /// <默认
        PAD_SUG_STATE_STARTUP = 1           /// 启动面板
    };
public:
    class IPT_IMPORT Callback {
    public:
        virtual void on_duty_info(const DutyInfo* dutyinfo) = 0;  ///<  duty回调函数
        virtual void on_log_write(const char* str, Uint32 len);  ///<  日志回调函数
        /** on_method_start在方法开始处调用，on_method_end 在方法出口处调用；on_method_start 和 on_method_end 必须成对调用， **/
        virtual void on_method_start(Uint32 method_id, const char* msg, Uint32 msg_len);
        virtual void on_method_end(Uint32 method_id, const char* msg, Uint32 msg_len);
        /*  内核内部函数的耗时，以参数输入，客户端实现
            一次调用，参数传递一个方法的method_id和对应的method_cost
        */
        virtual void post_inner_method_timecost(Uint32 method_id, Uint32 method_cost);
    };
public:
    /// 用于荣耀卡顿计时功能
    enum MethodId : Uint32 {
        ACT_KEYCLICK = 0,
        WRITE_KEYACTION_EVENT = 1,
        ITN_ACT_KEYCLICK_SWITCH = 2,
        FIND_INTERNAL = 3,
        HANDLE_DEFINED = 4,
        ACT_LINE_FEED = 5,
        ACT_CODE_CLICK = 6,
        ACT_BACK = 7,
        ACT_ENTER = 8,
        ACT_SPACE = 9,
        ACT_DELETE = 10,
        ACT_TAB = 11,
        ACT_CLEAR = 12,
        ACT_ABC = 13,
        PAD_TRANSITION = 14,
        ITN_ACT_KEYCLICK_SUG_BACK_VOICE = 15,
        ITN_ACK_KEYCLICK_UTRACE = 16,
        ACT_KEYCLICK_MULTI_CHAR = 17,
        ACT_CANDCLICK = 18,
        SEND_KEY_EVENT = 19,
        ACT_CODE_CLICK_MULTI_CHAR = 20,
        CODE_CLICK_APPEND_ONE = 21,
        ACT_DEFINE = 22,
        GET_UNIS_WITH_SMTI = 23,
        INPUT_APPEND = 24,
        PADCOMMONTRIGERWORDS_FIND_AFTER = 25,
        PADCOMMONTRIGERWORDS_FIND_CAND = 26,
        PADCLOUDCAMPAIGN_FIND_PRE = 27,
        PADCLOUDCAMPAIGN_FIND_CAND = 28,
        PADCLOUDCAMPAIGN_FIND_AFTER = 29,
        GET_EDIT_BEFORE_CURSOR = 30,
        GET_EDIT_AFTER_CURSOR = 31,
        GET_EDIT_TEXT_SELECTION = 32,
        WILL_ENTER_PAD = 33,
        FIND_APP = 34,
        REQUEST_URL_RESOURCE = 35,
        CLOUD_REQUEST_FIND = 36,
        CLOUD_REQUEST_RANK = 37,
        CLOUD_REQUEST_PREFETCH = 38,
        DO_REQUEST_LIAN = 39,
        DO_REQUEST_HW_LIAN = 40,
        DO_REQUEST_HW_FORCAST = 41,
        DO_REQUEST_AI_CHAT = 42,
        DO_REQUEST_PREFETCH = 43,
        DO_REQUEST_PYRANK = 44,
        DO_REQUEST_PY = 45,
        CLOUD_REQUEST_AI_CHAT = 46,
        DO_QUERY_FIND = 47,
        CLEAN_CONTAINER = 48,
        BUGFIX_IOS_TRY_MMAP_OPEN = 49, //仅iOS使用-不加耗时
        UPDATE_PREUNIS_BUFF = 50,
        FIND_BY_TYPE_ITN = 51,
        KWD_FIND_NIJIGEN = 52,
        DUP_FIND_FIR_SYM = 53, // 几乎不耗时-不加耗时
        ITN_FIND_PY = 54,
        ITN_FIND_EN = 55,
        ITN_FIND_BH = 56,
        ITN_FIND_WB = 57,
        ITN_FIND_HW = 58,
        ITN_FIND_LIAN = 59,
        TRY_LOAD_BH_FILTER = 60,
        PY_PREDICT = 61,
        PQSESS_FIND_NAME = 62,
        PQSESS_FIND = 63,
        ERINE_FIND_DOWN = 64,
        CLOUD_ITN_SET_TRIGGER = 65,
        FIND_CLOUD_CACHE_PY_SUGCAND = 66,
        TRY_CORRECT_CAND_UNIS = 67,
        TRY_REMOVE_SENSITIVE_PREDICT = 68,
        ITN_FIND_KEYWORD = 69,
        TRY_INSERT_SINGLE_CHAR = 70,
        PROCESS_NEW_EN_USR_COMMIT_BY_ENTER = 71,
        ITN_FIND_PHRASE = 72,
        ERINE_FIND_UP = 73,
        ITN_PYG_BUILD = 74,
        ITN_FIND_PINYIN = 75,
        ITN_FIND_DATE_PHONENUM = 76,
        ITN_FIND_NUM = 77,
        ITN_FIND_EN_NOT_MIX = 78,
        ITN_FIND_PINYIN_RANK = 79,
        ITN_FIND_PINYIN_CZ3_MAIN = 80,
        ITN_FIND_PINYIN_HZ = 81,
        ITN_FIND_PINYIN_USER_MAIN = 82,
        ITN_FIND_PINYIN_CELL_MAIN = 83,
        ITN_FIND_PINYIN_USER_RARE = 84,
        ITN_FIND_PINYIN_PRED = 85,
        ITN_FIND_PINYIN_CNEN = 86,
        // 预留位置
        CORE_LOAD = 87,
        DUPTREE_BUILD = 88,
        FIND_CLOUD_CACHE = 89,
        FIND_PINYIN_CZ = 90,
        GRAM_FIND = 91,
        GRAM_GRAPH = 92,
        GRAM_BFS = 93,
        GRAM_BFS_NEW = 94,
        //
        SEND_PADSEND_EVENT = 95,
        SEND_CUSTOMINPUT_EVENT = 96,
        SEND_TAB_EVENT = 97,
        SEND_CAND_EVENT = 98,
        SEND_SUGCARD_EVENT = 99, //废弃功能-不加耗时
        SEND_LIST_EVENT = 100,
        SEND_CANDINFO_EVENT = 101,
        SEND_CONTACT_EVENT = 102,
        SEND_SWITCHPAD_EVENT = 103,
        SEND_INPUT_EVENT = 104,
        SEND_CURSORMOVE_EVENT = 105,
        SEND_IMPORTDATA_EVENT = 106,
        SEND_AIPAD_EVENT = 107, // AiPad功能客户端完成不走内核逻辑-不加耗时
        SEND_SETLOCATION_EVENT = 108,
        PRESUBMIT_COMMIT = 109,
        //
        HANDLE_CLICK_DOWN_INPUT_DELETE = 110,
        DUTY_POP = 111,
        TRACE_FLUSH = 112,
        TRACE_OVERFILL = 113,
        EXPORT_PB_TRACE = 114,
        GET_EDIT_TEXT_DIALOGUE = 115,
        //
        TSTL_FILE_OPEN = 116,
        TSTL_FILE_CLOSE = 117,
        TSTL_FILE_READ = 118,
        TSTL_FILE_WRITE = 119,
        TSTL_FILE_FLUSH = 120,
        TSTL_FILE_SEEK = 121,
        TSTL_FILE_TELL = 122,
        TSTL_FILE_STAT = 123,
        TSTL_FILE_SIZE = 124,
        //
        TSTL_FILEMAP_OPEN = 125,
        TSTL_FILEMAP_CLOSE = 126,
        //
        TSTL_FILESYNC_OPEN = 127,
        TSTL_FILESYNC_CLOSE = 128,
        TSTL_FILESYNC_SYNC = 129,
        TSTL_FILESYNC_RESIZE = 130,
    };

public:
    /// 创建输入面板 ///
    static InputPad* create(InputLib* iptcore, InputPad::Callback* callback);
    static void close(InputPad* iptpad);  ///<  关闭输入面板

public:
    //AiPad上的相关的点击操作///
    enum AiPadActType {
        AI_PAD_ACT_NONE = 0, ///<空,占位数字
        AI_PAD_ACT_DISTURB = 1, ///<客户端发起打断请求
        AI_PAD_ACT_CLICK_REFRESH = 2, ///<点击 换一换
        AI_PAD_ACT_CLICK_CAND_ITEM = 3, ///<AiPad上点击 CandInfo, 允许同时多个(Ai校对)
        ///
        AI_PAD_ACT_CLOSE = 32, ///<点击AiPad关闭按键
        AI_PAD_ACT_OPEN = 33, ///<点击AiPad打开按键(会自动进入默认Tab/或上次的Tab)
        ///
        AI_PAD_ACT_OPEN_TAB_AI_COMPOSE = 35, ///<打开AiPad--并切换到Ai创作 (原名: AI帮写,Ai撰写)
        AI_PAD_ACT_OPEN_TAB_AI_CORRECT = 39, ///<打开AiPad--并切换到AI校对(原名 NLP纠错)///
        AI_PAD_ACT_OPEN_TAB_AI_PEITU = 40, ///<打开AiPad--并切换到神句配图///
        AI_PAD_ACT_OPEN_TAB_AI_TEXT_EMOJI = 41, ///<打开AiPad--并切换到花样文(原字符表情)///
        AI_PAD_ACT_OPEN_TAB_AI_SPECIAL_FONT = 42,  ///<打开AiPad并切换到特技字///
        AI_PAD_ACT_OPEN_TAB_WENXIN = 43, ///<打开AiPad并切换到文心一言 ///
        AI_PAD_ACT_OPEN_TAB_HIGHEQ = 44, ///<打开AiPad并切换到高情商 ///
        AI_PAD_ACT_OPEN_TAB_MARK = 45, ///<打开AiPad并切换到种草文 ///
        AI_PAD_ACT_OPEN_TAB_AI_WENWEN = 46, ///<打开AiPad并切换到AI问问 ///
        AI_PAD_ACT_OPEN_TAB_LOVE_MASTER = 47, ////<打开AiPad并切换到恋爱相关 ///
        AI_PAD_ACT_OPEN_TAB_AI_EMOTICON = 48, ////<打开AiPad并切换到AI表情创作 ///
    };
    //AiPad上的相关的点击操作///
    struct EventAiPadAction {
        EventAiPadAction(AiPadActType type_, const Uint16* click_list_ = nullptr, Uint32 click_cnt_ = 0)
            : type(type_), click_list(click_list_), click_cnt(click_cnt_) { }
        AiPadActType type; ///< 操作类型///
        ///< AI_PAD_ACT_CLICK_CAND_ITEM 操作 同时点击了几个CandInfo项目的序号列表///
        const Uint16* click_list;
        ///< AI_PAD_ACT_CLICK_CAND_ITEM 操作 同时点击了几个CandInfo项目///
        Uint32 click_cnt;

    };
public:
    struct EventKeyAction {
        EventKeyAction(Int32 key, InputType typ)
            : key_id(key), type(typ), point(nullptr), pad_id(PAD_NONE), uni(nullptr), keys(nullptr), click(nullptr) { }
        EventKeyAction(Int32 key, InputType typ, const s_Point_v2* pot) : EventKeyAction(key, typ) { point = pot; }
        EventKeyAction(Int32 key, InputType typ, PadId pid) : EventKeyAction(key, typ) { pad_id = pid; }
        EventKeyAction(Int32 key, InputType typ, const Uint16* uni_) : EventKeyAction(key, typ) { uni = uni_; }
        EventKeyAction(Int32 key, InputType typ, const s_Point_v2* pot, const  Uint16* uni_)
            : EventKeyAction(key, typ, pot) { uni = uni_; }
        EventKeyAction(const Uint8* keys_, InputType typ, const s_Point_v2* pot = nullptr)
            : EventKeyAction(0, typ, pot) {
            keys = keys_;
            point = pot;
        }
        EventKeyAction(Int32 keyid, InputType type, const s_Point_v2* point, const s_Click_v1* click)
            : key_id(keyid), type(type), point(point), pad_id(PAD_NONE), uni(nullptr), keys(nullptr), click(click)
        {}
        EventKeyAction(Int32 keyid, InputType type, const s_Point_v2* point, const Uint16* uni, const s_Click_v1* click)
            : key_id(keyid), type(type), point(point), pad_id(PAD_NONE), uni(uni), keys(nullptr), click(click)
        {}
        Int32 key_id;       ///< 点击按键的键值//
        InputType type;     ///< 点击按键的方式//
        const s_Point_v2* point;  ///< 点击按键的坐标点信息//
        PadId pad_id;       ///< 如果点击的是切换面板的按键，需指明切换到哪个面板//
        const Uint16* uni;
        const Uint8* keys;        ///< PC输入法上多个输入码的情况//
        const s_Click_v1* click; ///< 点击力及点击坐标，轨迹用//
    };
    //
    enum CandActionType {
        CAND_CLICK = 0, ///< 点击候选条操作//
        CAND_SELECT = 1,    ///< 选择候选词操作//
        CAND_LONGPRESS = 2, ///< 长按候选条操作//
        AI_CAND_CLICK = 3, ///<点击次Cand操作 ///
        SUG_CLICK = 4,      ///< 点击sug条操作///
        RARE_CAND_CLICK = 5, ///< 点击最近生僻字操作///
        ///
        CAND_WORD_DELETE = 8, ///< PC输入法对词条的黑名单处理//
        CAND_ADD_FIXTERM = 9, ///< PC输入法添加固守操作//
        CAND_DELETE_FIXTERM = 10, ///< PC输入法取消固守操作//
        CAND_REFRESH_FIND = 11, ///< PC输入法刷新候选项//
    };
    enum SugCardClickActionType {
        SUG_CARD_CLICK_INSERT,
        SUG_CARD_CLICK_BUTTON,
        SUG_CARD_CLICK_OUTER,
    };
    struct EventCandAction {
        EventCandAction(CandActionType action_type, Uint32 candidx)
            : type(action_type), idx(candidx) {}
        CandActionType type;    ///< 针对候选词的操作类型//
        Uint32 idx;             ///< 操作候选词的索引//
    };
    enum SugActionType {
        CARD_CLICK = 0, ///< 点击sug卡片操作//
        CARD_SELECT     ///< 选择sug卡片操作//
    };
    struct EventSugCardAction {
        EventSugCardAction(SugActionType action_type, Uint32 candidx)
            : type(action_type), idx(candidx), is_insert(false) {}
        EventSugCardAction(SugActionType action_type, Uint32 candidx, bool insert)
            : type(action_type), idx(candidx), is_insert(insert) {}
        SugActionType type; ///< 针对sug卡片的操作类型//
        Uint32 idx;             ///< 操作sug卡片的索引//
        bool is_insert;
    };
    enum TopPromotionActionType {
        TPP_CLICK = 0 ///< 点击TOP PROMOTION操作//
    };
    struct EventTopPromotionAction {
        EventTopPromotionAction(TopPromotionActionType action_type, Uint32 candidx)
            : type(action_type), idx(candidx) {}
        TopPromotionActionType type; ///< 针对TOP PROMOTION的操作类型//
        Uint32 idx;             ///< 操作TOP PROMOTION的索引//
    };
    // 想删除的接口 //
    enum FixTermActionType {
        FT_CLICK = 0, ///< 选择Fix Term的操作//
        FT_CANCEL ///< 取消选择Fix Term的操作//
    };
    struct EventFixTermAction {
        EventFixTermAction(FixTermActionType action_type, Uint32 candidx) : type(action_type), idx(candidx) {}
        FixTermActionType type; ///< 针对Fix Term的操作类型//
        Uint32 idx;             ///< 操作Fix Term的索引//
    };
    //
    struct EventListAction {
        EventListAction(Uint32 listidx) : idx(listidx) {}
        Uint32 idx;     ///< 点击list选项的索引//
    };
    enum CandInfoActionType {
        CANDINFO_CLICK = 0, ///< 弹出candinfo信息栏选择//
        CANDINFO_CANCEL,    ///< 取消candinfo信息栏//
        CANDINFO_CONFIRM    ///< 确认candinfo(ios客户端上有的)//
    };
    struct EventCandinfoAction {
        EventCandinfoAction(CandInfoActionType type) : type(type), idx(0) {}
        EventCandinfoAction(CandInfoActionType type, Uint32 idx) : type(type), idx(idx) {}
        CandInfoActionType type;    ///< 针对candinfo信息栏的操作类型//
        Uint32 idx;                 ///< candinfo信息栏内容的索引//
    };
    enum ContactActionType {
        CONTACT_INSERT = 0, ///< 插入选择的联系人信息//
        CONTACT_CANCEL = 1,     ///< 关闭联系人信息窗口//
    };
    struct EventContactAction {
        EventContactAction(ContactActionType type) : type(type) {}
        ContactActionType type; ///< 针对联系人信息窗口的操作类型//
    };
    struct EventSwitchPad {
        EventSwitchPad(PadId padid) : id(padid) {}
        PadId id;   ///< 切换到哪个面板//
    };
    enum InputActionType {
        INPUT_CURSOR = 0, ///< 点击input编辑条//0行首, -1行尾
        INPUT_POP,        ///< 点击input编辑条中的半上屏候选，包括候选词和已经筛选的list//
        CURSOR_MOVE_LEFT,  //左移光标, 一次一个字(跳过分隔符号)
        CURSOR_MOVE_RIGHT, //右移光标, 一次一个字符(跳过分隔符号)
        CURSOR_MOVE_SEARCH_LEFT, //左移光标并查询, 一次一个字(跳过分隔符号)
        CURSOR_MOVE_SEARCH_RIGHT, //右光标并查询, 一次一个字符(跳过分隔符号)
        CURSOR_MOVE_SEARCH,     //移光标并查询, 0到行首, -1到行尾, 其他'a-z', 查找'idx'
        PC_RECOVER            ///PC专属，端上ctrl+backspace，恢复上一次上屏的输入码查询状态, 前一次上屏空格无法触发重查询///
    };
    struct EventInputAction {
        EventInputAction(InputActionType type) : type(type), idx(0) {}
        EventInputAction(InputActionType type, Int32 idx) : type(type), idx(idx) {}
        InputActionType type;   ///< input编辑条的操作类型//
        Int32 idx;             ///< 点击input编辑条时，光标插入位置//
    };

    enum EventWmSugActionType {
        WM_SUG_APP_AD_CLICK_UNK = 0,     ///< 点击了网盟SUG广告，loading页面后调用了网盟的SDK进行click,
        ///< 但不知道是直投包还是渠道包还是促活
        WM_SUG_APP_AD_CLICK_ZHITOU = 1,  ///< 点击了网盟SUG广告，loading页面后调用了网盟的SDK进行click,直投包
        WM_SUG_APP_AD_CLICK_CHANNEL = 2, ///< 点击了网盟SUG广告，loading页面后调用了网盟的SDK进行click,渠道包
        WM_SUG_APP_AD_CLICK_ACT = 3,     ///< 点击了网盟SUG广告，loading页面后调用了网盟的SDK进行click,促活包
        WM_SUG_APP_NM_CLICK = 4,         ///< 点击了网盟SUG广告，loading页面后没有调用了网盟的SDK进行click

        WM_SUG_APP_SDK_RECV = 100,       ///< 必须传入idx，在sugclick前传入，表示对应的sug，网盟SUG获取到广告了
        WM_SUG_APP_SDK_SEND = 101,       ///< 必须传入idx，在sugclick前传入，表示对应的sug，网盟SUG请求了广告
    };

    struct EventWmSugAction {
        EventWmSugAction(EventWmSugActionType type_) : type(type_), idx(0) { }
        EventWmSugAction(EventWmSugActionType type_, Int32 idx_) : type(type_), idx(idx_) { }
        EventWmSugActionType type;
        Int32 idx;
    };

    enum IntentionCardActionType : Uint8 {
        ICAT_NONE = 0, /// 无效操作
        ICAT_VIEW = 1, /// 查看
        ICAT_SHARE = 2, /// 分享
        ICAT_DOWNLOAD = 3, /// 下载
        ICAT_OPEN = 4, /// 打开
    };

    struct EventIntentionCardAction {
        EventIntentionCardAction(CloudIntentionTypeFirstLevel first_level_, CloudIntentionTypeSecondLevel second_level_,
                                 const Uint16* title_, IntentionCardActionType action_type_)
                : first_level(first_level_), second_level(second_level_), title(title_), action_type(action_type_) {}
        CloudIntentionTypeFirstLevel first_level;
        CloudIntentionTypeSecondLevel second_level;
        const Uint16* title; /// 卡片标题
        IntentionCardActionType action_type;
    };

    enum CursorMoveType {
        CURSOR_MOVE_NORMAL, ///普通的光标移动
        CURSOR_MOVE_COMMIT, ///上屏时发生的光标移动(英文用, 其他不需要)
        CURSOR_MOVE_DELETE_CHAR, ///回删屏上文本时发生的光标移动(IOS正常传入，Android传入CURSOR_MOVE_NORMAL)
        PC_CURSOR_MOVE,   ////PC端光标移动时调用////
    };
    //编辑框中光标位置变化了//
    struct EventCursorMove {
        EventCursorMove(CursorMoveType cursor_move_type): _cursor_move_type(cursor_move_type) {}
        CursorMoveType _cursor_move_type;
    };
    enum ImportDataType {
        LIST_DATA = 0,  ///< 导入默认展示的符号list//
        CAND_DATA = 1,  ///< 客户端把外部获取的内容展示在cand条上//
        VOICE_DATA = 2,  ///< 语音面板传入数据，然后联想表情颜文字//
        AUTOREPLY_DATA = 3, ///< 智能回复数据//
        VOICE_CORRECT_DATA = 4, /// 语音纠错数据
        VOICEBAR_DATA = 5, /// 主面板语音条的传入数据，不联想表情颜文字
    };
    struct EventImportData {
        EventImportData(ImportDataType type, Uint16** sym_list, Uint32 sym_list_len, Uint16** value_list)
            : type(type), sym_list(sym_list), sym_list_len(sym_list_len),
              sym_value_list(value_list), cand_type(CandInfo::CANDTYPE_NONE), cand_data(nullptr) { }
        EventImportData(ImportDataType type, CandInfo::CandType cand_type, const Uint16* userdata)
            : type(type), sym_list(nullptr), sym_list_len(0),
              sym_value_list(nullptr), cand_type(cand_type), cand_data(userdata) { }
        EventImportData(ImportDataType type, const Uint16* userdata)
            : type(type), sym_list(nullptr), sym_list_len(0),
              sym_value_list(nullptr), cand_type(CandInfo::CANDTYPE_NONE), cand_data(userdata) { }
        ImportDataType type;            ///< 导入数据类型//
        Uint16** sym_list;              ///< 传入默认的符号列表//
        Uint32 sym_list_len;            ///< 默认符号列表长度//
        Uint16** sym_value_list;        ///< 符号对应的上屏文本，为NULL时，sym_value_list等同sym_list
        CandInfo::CandType cand_type;   ///< 传入候选词类型//
        const Uint16* cand_data;              ///< 传入候选词内容//
    };
    enum TrackActionType {
        TRACK_START = 0,    ///< 开始画轨迹，传入已经划的点，和是否符号手写的设置is_sym//
        TRACK_MOVE,         ///< 继续画轨迹，传入已经划的点//
        TRACK_END           ///< 停止画轨迹，传入最后一个停止的点//
    };
    struct EventTrackAction {
        EventTrackAction(TrackActionType type, s_Point_v2* point, Uint32 point_len, bool is_sym, Uint64 sample_time,
                         bool is_gesture = false)
            : type(type), point(point), point_len(point_len), is_symhw(is_sym), sample_time(sample_time), is_gesture(is_gesture) {}
        EventTrackAction(TrackActionType type, s_Point_v2* point, Uint32 point_len, Uint64 sample_time, bool is_gesture = false)
            : type(type), point(point), point_len(point_len), is_symhw(false), sample_time(sample_time), is_gesture(is_gesture) {}
        TrackActionType type;
        s_Point_v2* point;
        Uint32 point_len;
        bool is_symhw;
        Uint64 sample_time;  /// 采样时间 ///
        bool is_gesture;  /// 是否可能是笔势 ///
    };
    enum MiscCandType {
        EMOJI_CAND = 0,           ///绘文字
        KAOMOJI_CAND = 1,         ///颜文字
        MEME_CAND = 2,            ///表情图
        ARMOJI_CAND = 3           ///AR表情
    };
    ///EMOJI和KAOMOJI需要传uni_str
    struct EventMiscCandAction {
        EventMiscCandAction(MiscCandType misc_type, const Uint16* uni_str = nullptr)
            : type(misc_type), str(uni_str) { }
        MiscCandType type;
        const Uint16* str;
    };
    enum UniqueResultType {
        UNIQUE_RESULT_CLICK_DETAIL = 0,  ///< 点击详情//
        UNIQUE_RESULT_SHOW_CONTENT = 1,  ///< 内容展示//
        UNIQUE_RESULT_CLOSE_PAD = 2      ///< 关闭面板//
    };
    struct EventUniqueResultAction {
        EventUniqueResultAction(UniqueResultType type, const Uint16* uni_str = nullptr, const char* code_str = nullptr,
                                const Uint16* query_str = nullptr, const Uint16* card_type_str = nullptr)
            : type(type), uni(uni_str), code(code_str), query(query_str), card_type(card_type_str) {}
        UniqueResultType type;
        const Uint16* uni;        ///< 上屏内容//
        const char* code;         ///< 输入码//
        const Uint16* query;      ///< query//
        const Uint16* card_type;  ///< 卡片类型//
    };
    //与pad_switch的关系是先switch, 再PadEventUp, 先PadEventDown, 再switch
    enum PadEventType {
        PadEventNone = 0,
        PadEventUp = 1,     //起面板
        PadEventDown = 2,   //收面板
        // 端上调用，随系统事件，属于系统主动触发的特定行为，在这个行为中内核进行处理状态 //
        // 现与 PadEventClearAll 功能相似，但是对端上意义不同，PadEventClearAll属于端上主要调用触发的行为 //
        PadEventRestart = 3,
        /// Android 起面板完成皮肤加载后,发这个事件,iOS不会有这个事件. ///
        /// Android 下处理撰写自动打开,输入前Cand活动等 需要在这个事件里面处理 ///
        PadEventUpDone = 4,
        // 端上会在内核不关心的事件中清空面板的所有内容，但是已经上屏的内容仍要上屏(如手写/英文) //
        // 端上主要调用触发 ，内核将预上屏内容上屏并清空面板上所有内容 ////
        PadEventClearAll = 5,
        PadEventShow = 6,       //客户端真实的起面板事件
        PadEventMax = 7
    } ;
    struct EventPadSendAction {
        EventPadSendAction(PadEventType type_) : type(type_), is_restarting(false) {}
        EventPadSendAction(PadEventType type_, bool is_restarting_) : type(type_), is_restarting(is_restarting_) {}
        //
        PadEventType type;
        bool is_restarting; //Android专用，用于通知内核当前是不是正在restarting事件，如果是true，不进行自动起面板//
    };
    //
    enum CustomInputDataType {
        PASTE_DATA = 0,  ///< 客户端上屏了剪切板内容 ///
        EMOJI_DATA = 1,  ///< 客户端上屏了emoji ///
        VOICE_INPUT_DATA = 3, ///< 客户端上屏了语音文本(预上屏不发事件)///
        TEXT_EMOJI_INPUT_DATA = 4, ///< 客户端上屏了花样文文本(又名:字符表情) ///
        SPECIAL_FONT_INPUT_DATA = 5,  ///客户端上屏了特技字///
        PC_VFMODE_DATA = 6,  ///客户端上屏的翻译内容///
        EDITBOX_CHANGED_DATA = 7, /// 在同一个app内发生了框切换但是没有面板事件,需要触发输入后 ///
        SEARCH_QUERY = 8, /// 端上获取到搜索query时调用
        SEND_DATA = 9, // 客户端发送事件 - 暂未使用///
    };
    struct EventCustomInputData {
        EventCustomInputData(CustomInputDataType type, const Uint16* userdata)
            : type(type), custom_data(userdata) { }
        CustomInputDataType type;
        const Uint16* custom_data; /// 上屏/发送的具体文本 ///
    };
    // MICInput_common-2508 [Story] 【5G内核】[AI造词]次Cand字体生成提醒 //
    enum AiCandNotifyType {
        AI_TYPE_COMMON_HIGHEST_PRIORITY = 0,/// 次cand通用通知，端上负责传入展示内容，最高优先级展示 ///
    };
    struct EventAiCandNotify {
        EventAiCandNotify(AiCandNotifyType type, const Uint16* jsonstr, const Uint8* urlstr, Uint32 show_ = 0,
                          const Uint16* insert_str_ = nullptr)
            : type(type), json_str(jsonstr), goto_url(urlstr), show_time(show_), insert_str(insert_str_) { }
        AiCandNotifyType type;
        const Uint16* json_str;      /// json串！必须,客户端解析用于内容展示 //
        const Uint8* goto_url; /// 跳转链接 //
        Uint32 show_time; // 提示展示时间，默认0表示不限时间 ///
        const Uint16* insert_str; // 上屏内容,展示和上屏可能不一致,可以为空 ///
    };
    struct EventTabAction {
        explicit EventTabAction(ConfigItems::QpFilter filter) : filter(filter) {}
        ConfigItems::QpFilter filter;
    };
    /// 传入定位信息，目前用于地图数据重排序
    struct EventSetLocation {
        explicit EventSetLocation(float latitude_, float longitude_) : latitude(latitude_), longitude(longitude_) {};
        float latitude;
        float longitude;
    };
public:
    //进入符号面板, 客户端设置lock信息
    //value是一个PLUINT32数组, 每个值表示对应位置是否lock, 0不锁定, 非0锁定
    //用于Android 进入符号编辑状态锁定 符号面板的list //
    virtual void pad_set_lock(SYM_CATE* list, Uint32* value, Uint32 count) = 0;
    virtual Uint32 pad_get_lock(SYM_CATE* list, Uint32* value, Uint32 max) = 0;

    //符号调频, 导入需要调频的符号列表
    virtual void pad_set_sym_filter(PLUINT16** list, PLUINT32 count) = 0;
    virtual void send_event(EventPadSendAction* event) = 0;
    //virtual void send_pad_event(PadEventType type) = 0;
    virtual void cloud_cache_clean(void) = 0;
    //virtual void recover(void) = 0; //上次的输入在查询一次
    ///< 客户端上屏内容(客户端一定要先处理上屏,再调用接口 这样内核可以从编辑框取到已变化的内容///
    virtual void send_event(EventCustomInputData* event) = 0;
    /// < 不经过内核处理的一些客户端事件但是需要内核进行中转/通知处理//
    virtual void send_event(const EventAiCandNotify*) = 0;
    ///< 更多面板切换tab，与act_tab_click内部功能相同，会自动处理duty回调 ///
    virtual void send_event(const EventTabAction*) = 0;
    virtual void send_event(EventKeyAction*) = 0;       ///< 按键的相关操作//
    virtual void send_event(EventCandAction*) = 0;      ///< 候选区域的相关操作//
    virtual void send_event(EventSugCardAction*) = 0;   ///< sugcard的相关操作//
    virtual void send_event(EventListAction*) = 0;      ///< list区域的相关操作//
    virtual void send_event(EventCandinfoAction*) = 0;  ///< 候选弹出框的相关操作//
    virtual void send_event(EventContactAction*) = 0;   ///< 联系人弹出框的相关操作//
    virtual void send_event(EventSwitchPad*) = 0;       ///< 切面板的相关操作//
    virtual void send_event(EventInputAction*) = 0;     ///< input区域（show区域）的相关操作//
    virtual void send_event(EventWmSugAction*) = 0;       ///< 网盟SUG相关行为，都是和日志有关 ///
    virtual void send_event(EventIntentionCardAction*) = 0;       ///< 记录意图卡片的行为事件，为了记录轨迹 ///
    ///< 编辑框移动光标的相关操作，光标被人为移动时，务必调用此接口（不包括上屏导致的光标移动和替换预上屏文本）//
    virtual void send_event(EventCursorMove*) = 0;
    virtual void send_event(EventImportData*) = 0;      ///< 导入数据的相关操作//
    virtual void send_event(EventTrackAction*) = 0;     ///< 划轨迹的相关操作//
    virtual void send_event(EventTopPromotionAction*) = 0;     ///< PC平台6号展示位相关操作//
    virtual void send_event(EventFixTermAction*) = 0;     ///< 固守词库相关操作//
    virtual void send_event(EventMiscCandAction*) = 0;     ///< EMOJI、颜文字等相关操作//
    virtual void send_event(EventUniqueResultAction*) = 0;     ///唯一结果相关操作
    virtual void send_event(const EventAiPadAction* event) = 0; /// Ai助聊面板上的各种操作
    virtual void send_event(const EventSetLocation* event) = 0; /// 设置位置信息，目前用于地图数据重排序
    virtual const DutyInfo* act_keyclick(Int32 keyid, const s_Point_v2* point,
                                         InputType type, const Uint16* uni = nullptr,
                                         const s_Click_v1* click = nullptr) = 0;
    virtual const DutyInfo* act_keyclick_multi_char(const Uint8* key, InputType type,
            const s_Point_v2* point = nullptr) = 0; ///< 按键操作///
    ///< 判断是否可以弹出气泡  true-弹出  false-不弹出///
    virtual const bool check_keybubble(Int32 keyid) = 0;
    ///< 判断当前字符串是否命中云端服务请求黑名单, 如果命中, 则不允许云端服务请求 ///
    virtual const bool check_hit_black_list(const Uint16* text, Uint32 text_len) const = 0;
    virtual const DutyInfo* act_candclick(Uint32 candidx) = 0; ///< 点击候选条操作///
    virtual const DutyInfo* act_candselect(Uint32 candidx) = 0;  ///< 选择候选词操作///
    virtual const DutyInfo* act_candlongpress(Uint32 candidx) = 0; ///< 长按候选条操作///
    virtual const DutyInfo* act_sugclick(Uint32 sugidx) = 0;  ///< 点击sug条操作///
    virtual const DutyInfo* act_sugcardclick(Uint32 sugcardidx, bool is_insert) = 0;  ///< 点击sug卡片操作///
    virtual const DutyInfo* act_sugcard_select(Uint32 sugcard_select) = 0;  ///< 选择sug卡片操作///
    virtual const DutyInfo* act_listclick(Uint32 listidx) = 0;  ///< 点击筛选条操作///
    //< 点击更多面板中笔画拼音英文和emoji等筛选/////< 点击更多面板中笔画词频等筛选///
    virtual const DutyInfo* act_tab_click(ConfigItems::QpFilter filter) = 0;
    virtual const Uint8* get_tabs(Uint32& tab_cnt) = 0; //获取筛选table//
    virtual const DutyInfo* act_candinfo_click(Uint32 infoidx) = 0; ///< 弹出candinfo信息栏选择///
    virtual const DutyInfo* act_candinfo_cancel() = 0; ///< 取消candinfo信息栏///
    virtual Int32 act_contact_attri_select(Uint32 cttidx, Uint32 attidx, Uint8 is_select) = 0;
    ///< 选择联系人属性  cttidx-联系人索引  attidx-联系人属性索引  is_select:0-取消选择 1-选择  返回值:0-设置成功 <0-设置失败///
    virtual const DutyInfo* act_contact_insert() = 0; ///< 插入选择的联系人信息///
    virtual const DutyInfo* act_contact_cancel() = 0; ///< 关闭联系人信息窗口///
    virtual const DutyInfo* act_padswitch(PadId pad_id) = 0; ///< 切换面板操作///
    virtual const DutyInfo* act_change_shift(ConfigItems::CfgEnShift shift) = 0; ///< 直接设置shift状态///
public:
    virtual const DutyInfo* act_cnen_key(PadId pad_id) = 0; ///< 安卓的中英切换按键，需要指定跳转的padid///
    virtual const DutyInfo* act_rare_chaizi_hw_key(PadId pad_id) = 0; ///< 生僻字面板【部/写】切换键///
public:
    virtual const DutyInfo* act_input_cursor_move_left(void) = 0;
    virtual const DutyInfo* act_input_cursor_move_search_left(void) = 0;
    virtual const DutyInfo* act_input_cursor_move_right(void) = 0;
    virtual const DutyInfo* act_input_cursor_move_search_right(void) = 0;
    virtual const DutyInfo* act_input_cursor_search(Int32 cursor) = 0;
    virtual const DutyInfo* act_input_cursor_search_char(char key) = 0;

    virtual const DutyInfo* act_input_cursor(Int32 cursor_idx) = 0; ///<点击input编辑条///
    ///<点击input编辑条中的半上屏候选，包括候选词和已经筛选的list///
    virtual const DutyInfo* act_input_pop() = 0;
    virtual const DutyInfo* act_edit_cursor_change(CursorMoveType cursor_move_type) = 0; ///<光标位置变化了///
public:
    //暂时先用s_Point_v2///
    virtual const DutyInfo* act_track_start(const s_Point_v2* point, Uint32 point_len, bool is_symhw,
                                            Uint64 start_time = 0) = 0;  /// start_time: 该笔画落笔点采样时间 ///
    virtual const DutyInfo* act_track_move(const s_Point_v2* point, Uint32 point_len,
                                           Uint64 move_time = 0) = 0;  /// move_time: 滑动轨迹点采样时间(暂未使用) ///
    virtual const DutyInfo* act_track_end(s_Point_v2 point,
                                          Uint64 end_time = 0,   /// end_time: 该笔画抬笔点采样时间 ///
                                          bool is_gesture = false) = 0;   /// is_gesture: 是否是笔势轨迹 ///

    virtual const DutyInfo* act_find_handwrite() = 0; //主动查询手写结果
public:
    ///<插入客户端词语///
    virtual const DutyInfo* act_insert_userdata(CandInfo::CandType candtype, const Uint16* userdata) = 0;
    virtual Int32 act_adjust_emoji_relation(Uint16 emoji_value, Uint32 cellid) = 0; ///<自造表情联想///
    virtual Int32 act_shift_longdown() = 0; ///<长按shift键按下///
    virtual Int32 act_shift_longup() = 0; ///<长按shift键抬起///
    virtual const DutyInfo* act_insert_voicedata(const Uint16* voice_data,
            ImportDataType type = VOICE_DATA) = 0; ///<语音面板上屏词///
    //<语音整句纠错///
    virtual Int32 act_correct_voicedata(Uint16* _ori_sent, PLINT32 _ori_sent_len, Uint16* ret_data,
                                        Uint32* _correct_words_cnt) = 0;
    virtual Int32 act_correct_voicesend() = 0; /// <客户端接受语音识别结果
public:
    virtual Int32 act_check_clip(const Uint16* unis, Uint32 uni_len) = 0;
public:
    virtual const DutyInfo* act_rare_candclick(Uint32 candidx) = 0; ///< 点击最近生僻字候选条操作///
public:
    //把一个字符串数组传入内核///
    ///< 导入符号列表内容///
    virtual const DutyInfo* import_sym_list(Uint16** sym_list, Uint32 sym_list_len, Uint16** sym_value_list) = 0;
public:
    ///< 设置默认面板，如果返回面板没有记录，就返回默认面板///
    virtual const void set_default_pad(PadId pad_id) = 0;
    virtual const ShowInfo* get_input_show() = 0;  ///< 获取show内容///
    virtual Uint32 get_cand_count() = 0;  ///< 获取候选词数量///
    virtual Uint32 get_cand_select() = 0;  ///< 获取选中候选词下标///
    virtual const CandInfo* get_cand_item(Uint32 idx) = 0;  ///< 获取候选词内容///
    virtual const Uint16* get_inline_show(Uint32& len) = 0; ///< 获取inline输入码///
    virtual Uint32 get_rare_cand_count() = 0; ///< 获取最近生僻字的数量///
    virtual const CandInfo* get_rare_cand_item(Uint32 idx) = 0; ///< 获取最近生僻字///

    ///< 获取Ai助聊/次cand相关信息 的接口 ///
    ///< 获取次Cand内容////
    virtual Uint32 get_ai_cand_count() const = 0;
    virtual const CandInfo* get_ai_cand(Uint32 idx = 0) const = 0;
    ///< 获取右上角Ai助聊图标状态(目前只包含是否Loading的状态) ///
    virtual AiIconState get_ai_icon_state() const = 0;
    ///<获取Ai助聊面板信息 ///
    virtual const AiPadInfo* get_ai_pad() const = 0;
    // 华为-获取超会写 小气泡是否需要展示标记 //
    virtual bool is_bubble_need_show() const = 0;
    ///
    virtual ConfigItems::CfgEnShift get_en_shift() = 0;  ///< 英文键盘shift状态
    virtual ConfigItems::CfgEnShift get_cn_shift() = 0;  ///< 中文键盘shift状态
    virtual void set_en_shift(ConfigItems::CfgEnShift en_shift) = 0;  ///< 英文键盘shift状态
    ///< 通用设置键盘shift状态，包括英文键盘
    ///< 修复BAIDUINPUTBUG-85600，提供给端上在切换皮肤时，关闭shift状态。
    ///< 有些皮肤有shift键，有些没有，切换皮肤时，没有触发关闭shift状态的方式
    virtual void set_is_shift(ConfigItems::CfgEnShift is_shift) = 0;
    ///
    virtual Uint32 get_list_count() = 0;  ///< 获取筛选列表数量//
    //output最大IPT_SYM_MAX_LEN
    ///< 获取筛选条目内容,返回值是ListItemType//
    virtual Uint32 get_list_item(Uint32 idx, Uint16* output, bool need_mapping_value = false) = 0;
    // virtual void set_sug_select(Int32 select) = 0;//
    // sug 卡片//
    virtual Uint32 get_sug_card_count() = 0;  ///< 获取sug卡片数量//
    virtual const SugCardInfo* get_sug_card_item(Uint32 idx) = 0;  ///< 获取sug卡片内容
    virtual Int32 get_sug_card_select() = 0;  ///< 获取选中的sug卡片下标//
    //
    virtual Uint32 get_sug_adx_timeout_ms() = 0; ///< 获取sug adx广告的超时时间///
    virtual Uint32 get_sug_count() = 0;  ///< 获取sug数量///
    virtual const CandInfo* get_sug_item(Uint32 idx) = 0;  ///< 获取sug内容//
    virtual Int32 get_sug_select() = 0;  ///< 获取选中的sug索引//
    /// SugSourceType
    virtual SugSourceType get_sug_scene() const = 0; ///< 获取sug场景///
    /// sug数据来源信息
    virtual SugCardInfo::SugCardType get_sug_type() const = 0;
    virtual Uint32 get_sug_action_type() const = 0; ///<sug action type 信息
    virtual Uint32 get_sug_action_type(Uint32 idx) = 0; ///<sug action type 信息
    // 只有广告Sug会去取 ///
    virtual const Uint8* sug_global_id_str(Uint32 idx) = 0; ///<sug global id 信息
    virtual Uint32 sug_global_id_len(Uint32 idx) = 0; ///<sug global id 信息
    // 端上不再使用，切换到global_id ///
    virtual Uint32 get_sug_source_id() const = 0; /// <sug 源id信息
    virtual Uint32 get_sug_id() const = 0; /// <sug id信息, 用于支持可配置图标 ///
    virtual Uint32 get_sug_source_msg_len() const = 0;
    virtual const Uint16* get_sug_source_msg() const = 0;
    //
    virtual Uint32 get_cand_context(Uint32 idx, Uint16* output) = 0;  ///< 获取盲人辅助模式词内容//
    virtual Uint32 get_cand_info_count() = 0; ///< 获取候选词长按后得到选项数目//
    virtual Uint32 get_cand_info(Uint32 idx, Uint16* output) = 0; ///< 获取候选词长按后得到的信息//
    ///长按信息的候选内容获取///
    virtual Uint32 get_cand_info_title(Uint16* output) = 0;
    virtual Uint32 get_contact_count() = 0;  ///< 获取联系人个数//
    virtual ContactItem* get_contact_item(Uint32 ctt_idx) = 0; ///< 获取联系人属性
    virtual const TriggerItem* get_trigger_itm(Uint32 index) const = 0; /// 获取触发词的属性
    virtual Uint32 get_trigger_itms_cnt() const = 0; /// 获取触发词卡片数量
    // 获取通用触发词配置id(透传给端上),有cnt时才有意义///
    virtual Uint64 get_trigger_itms_id() const = 0;
    virtual const MapItem* get_map_trigger_itm(Uint32 index) const = 0; /// 获取地图卡片的数据
    virtual Uint32 get_map_trigger_itms_cnt() const = 0; /// 获取地图卡片的数量
    //
    virtual const IntentionItem* get_cloud_intention_itm(Uint32 idx) const = 0; /// 获取云端意图卡片的数据
    virtual Uint32 get_cloud_intention_cnt() const = 0; /// 获取云端意图卡片的数量
    //
    virtual PadId get_pad_id() const = 0;  ///< 获取输入面板id
    ///< 返回彩蛋个数，egg_value里为彩蛋内容编号，最大长度为64个//
    virtual Uint32 get_egg_count(Uint16* egg_value, Uint16* type = nullptr) = 0;
    ////////////////////////////////////////////////
    ///< 获取服务端云输入白名单, 默认值为0, 当从服务器获取到时会更改为相应的值//
    virtual Uint32 get_srv_cloud_white_ver() const = 0;
#if defined(IPT_FEATURE_PC_ONLY) || defined(IPT_PLATFORM_MACOS)
public:
    ///<获得PC6号位某位置内容//
    virtual Uint32 get_top_promotion_count() const = 0;   ///<获取PC6号位展示数量//
    virtual const CandInfo* get_top_promotion_item(Uint32 idx) const = 0;
#endif
#ifdef IPT_FEATURE_PC_ONLY
    ///PC vf待翻译字符串///
    virtual Uint32 get_vf_mode_str_len() const = 0;  ///获取字符串长度///
    virtual const Uint16* get_vf_mode_str() const = 0; ///获取vf功能字符串///
    ///获取是否处在PC更多候选状态///
    virtual bool is_pc_more_cand() const = 0;
#endif
    ///
public:
    /// 获取当前手写智能延时数值(仅在手写智能延时模式下调用有意义) ///
    virtual Uint32 get_hw_smart_delay_time() = 0;
    virtual StateTrackType state_get_track_type() = 0;
    virtual bool state_get_is_accept_track() = 0;

    virtual PadSugState get_sug_state() = 0;
    virtual void act_cur_sug_close() = 0; // 叉号关闭后， 本次面板不请求

    virtual void act_click_toolbar() = 0; //点击输入法工具条

    //设置面板按键位置//
    /// @param is_split 表示是否是分离键盘, 仅限26键可用 ///
    virtual void cfg_set_pad_layout(s_Point_v2* lt, s_Point_v2* rb, bool is_split = false) = 0;
    virtual void cfg_set_pad_key_pos(s_Point_v2* view_lt, s_Point_v2* view_rb,
                                     s_Point_v2* touch_lt, s_Point_v2* touch_rb,
                                     Uint32 key_id) = 0;

    virtual bool cfg_set_ltp_file(const char* fname) = 0;

    /**根据坐标返回对应按键: KEY_RECT_CN26_NAME
     * @param in_point    — 当前的点坐标
     * @param l_axis      — thp长轴
     * @param s_axis      — thp短轴
     * @param angle       — thp角度
     * @return 返回为当前坐标对应的按键，为0表示无法识别
     */
    virtual Int32 cfg_get_touched_key(s_Point_v2* in_point,
                                      PLREAL32 l_axis  = 0, PLREAL32 s_axis = 0, PLREAL32 angle = 0) = 0;
    //导出这次commit的误触情况用于trace//
    virtual Int32 export_mis_key_for_trace(Uint16* out_buf, Uint16 out_max_len) = 0;

    //导出这次commit的word_info用于trace//
    virtual Int32 export_word_info_for_trace(Uint16* out_buff, Uint16 out_max_len) = 0;

    //导出误触数据（导出后自动清空）
    virtual Int32 export_mis_touch_info(Uint16* out_buff, Uint16 out_max_len) = 0;
    //导出按键数量（导出后自动清空，与export_mis_touch_info接口配套使用）
    virtual Int32 get_key_count_for_mis_touch(Uint16* out_buff, Uint16 out_max_len) = 0;

    //导出拼音面板准确率统计数据//
    virtual void export_input_statistics(U16Str& str_list, U16Str& str_list_oem) = 0;
    // 兼容主线分支的接口，后续删除！！！！！！///
    virtual const Uint16* export_input_statistics(Uint32& len) = 0;
    //重置拼音面板准确率统计数据//
    virtual void reset_input_statistics() = 0;

    /**训练高斯个性化模型
     * @param forcible — 是否强制训练
     * @return 是否进行了训练
     */
    virtual bool cfg_train_gauss_user(bool forcible = false) = 0;

    //按键映射, 默认映射9键:2-abc, 3-def, ..., 26键:a-a, ...
    //如果要自定义映射, 则传入key->value, 同时按键的时候传入key, 比如9键映射b->abc2, 则依次调用这个接口传入
    //b->a, b->b, b->c, b->2, 同时按键的时候传入b
    virtual void cfg_keymap_load_default() = 0;
    virtual void cfg_keymap_clean() = 0;
    virtual void cfg_keymap_addchar(const char ch_input, const char ch_code, Uint8 level) = 0;
public:
    /**layout设置开始接口
    * @return
    */
    virtual Int32 user_trace_start_pad_key_layout() = 0;
    /**layout设置完成接口
    * @return
    */
    virtual Int32 user_trace_finish_pad_key_layout() = 0;
    /**传入user trace
    **************
    *需要如下轨迹数据：
        容器ID为：0x01、0x02、0x16、0x2A、0x28
        面板类型为：唯一结果、emoji面板、颜文字面板、表情图面板、AR表情面板
    **************
    *@action: 行为
    *@container: 容器
    *@content: 用户轨迹数据
    * len: 用户轨迹数据长度
    * @return
    */
    virtual Int32 user_trace_write(Uint8 action, Uint8 container, const void* content, Uint32 len) = 0;
    // 记录用户语音转文字的内容进入轨迹
    /*
    * content 语音转成的字符串
    * len: 数据字节长度
    */
    virtual Int32 user_voice_input_log(const Uint8* content, Uint32 len) = 0;
    //获取当前输入码(iOS Appstore Sug用)
    //py26面板返回英文输入码, 支持大小写, 无输入码返回长度为0
    //py9面板返回数字输入码, 无输入码返回长度0
    //其他面板暂不支持
    virtual const char* current_input(void) = 0;
    virtual const char* iec_buff(void) = 0;
    virtual const char* au68_error() = 0;
    //
    /**整句预测模型结果塞回内核
    * @param _session    — 内核回话句柄
    * @param buf         — 整句预测模型返回内容
    * @param buf_len     — 整句预测模型返回内容长度
    **/
    virtual const DutyInfo* zj_model_set_data(const Uint8* buf, Uint32 buf_len) = 0;
    /**获取整句预测模型输入数据
    * @data_len         — 返回的数据长度
    * @return           -- 返回的数据
    **/
    virtual const Uint8* zj_model_req_data(Uint32& data_len) const = 0; /// 获取整句预测输入数据////
public:
    // AI助聊请求：加解密，压缩与解压缩 的过程 //
    // AI助聊三期时，卡片逻辑转移到端上处理，端上发起请求时也经过云端的加密压缩过程，故提供接口，将端上拼接好的pb进行加密 //
    // 注：仅辅助端上对数据进行处理 //
    // @reqdata  -- 返回的pb请求结果的内容, pbdata 和 pblen -- 端上发起请求的内容 和 长度 //
    // @check_id -- 本次发起请求的checkid //
    // @return -- 返回加密压缩后的pb内容长度，0表示数据为空或者过程中有失败 //
    // 端上调用时需要保证空间有效 reqdata 内存要分配好, 内存大小可以考虑32K//
    virtual Uint32 pbdata_request_aichat_card(Uint8* reqdata, Uint32& check_id, const Uint8* pbdata, Uint32 pblen) = 0;
    // @check_id -- 本次结果包解析出来的checkid //
    // @result 和 rlen 服务器返回的结果包 //
    // @return 返回结果串pb的长度 //
    // 端上调用时需要保证空间有效 rsp_data 内存要分配好，内存大小可以考虑32K//
    // 注：如果请求的checkid与结果的checkid不匹配，认为不是同一个请求 , 这里端上需要自行check//
    // checkid = 0 或者 请求与结果的checkid不匹配 都应认为是无效请求 //
    virtual Uint32 pbdata_response_aichat_card(Uint8* rsp_data, Uint32& check_id, const Uint8* result, Uint32 rlen) = 0;
    // AI智能联想(四期),将意图模型的返回结果对外 ///
    // @return style: 0代表不请求，1-8代表不同风格（端上需要在卡片请求时自行判断优先级）
    // @param 光标前后内容，内核次cand逻辑中，光标前后超过100个字，直接认为返回0 ///
    virtual Uint32 aichat_get_intention_style(const Uint16* edittxt, Uint32 elen) = 0;
    // 是否可以命中Sug白名单，iOS用于判别是否需要拉高面板///
    // 如果在ConfigItems里调用会导致IOS异步线程在主线程崩溃，所以放在Pad里 ///
    virtual bool is_match_sug_white_data() const = 0;
    virtual AdManager* get_ad_manager(AdManager::AdPos pos = AdManager::POS_SUG_CAND) = 0;
    // 与函数名无关 ///
    virtual Int32 cfg_dct_set_name_by_buff(const Uint8* buff, Uint32 buff_len, Uint64 ts, Uint32 is_newinstall) = 0;
};

/**
各个平台的环境类，由各平台实现,创建释放
*/
class IPT_IMPORT PlatformEnv {
public:
    // get_custom_data的数据类型 ///
    enum CustomDataType {
        CUSTOM_CLOUD_JSON = 1,  //云输入广告优化用数据
        CUSTOM_CLOUD_SUG = 3,       //是否发起sug
        CUSTOM_CLOUD_ATTACH = 4,    //PB等附加数据
        //是否保留输入后整句预测，保留的话，撰写和纠错内核不会发起了//
        CUSTOM_DATA_MAX
    };
    // 网络类型 -- 端上暂时未使用 ///
    using ProtocolType = NetMan::Protocol;
    //using NetMan::PROTOCOL_TYPE_HTTPS_POST; // 引入枚举值到当前作用域
    //using PROTOCOL_TYPE_UDP = NetMan::PROTOCOL_TYPE_UDP; // 引入枚举值到当前作用域
    //using PROTOCOL_TYPE_UNKNOWN = NetMan::PROTOCOL_TYPE_UNKNOWN; // 引入枚举值到当前作用域
    //using PROTOCOL_TYPE_QUIC = NetMan::PROTOCOL_TYPE_QUIC; // 引入枚举值到当前作用域

    //enum ProtocolType : Uint32 {
    //PROTOCOL_TYPE_HTTPS_POST = 0, /** https post请求 **/
    //PROTOCOL_TYPE_UDP = 1, /** udp请求 **/
    //PROTOCOL_TYPE_UNKNOWN = 2,
    //PROTOCOL_TYPE_QUIC = 3, //
    //};
public:
    typedef void* ThreadEnv;

public:
    struct app_msg {
        app_msg() : name(nullptr), name_len(0) { }
        Uint8* name;
        Uint32 name_len;
        Uint32 min_ver[8];
        Uint32 max_ver[8];
        Uint32 ver_num;
    };
public:
    /**
     * 各平台实现, 绑定一个新的线程, 在子线程中调用, 用来创建线程所需的资源
     */
    virtual ThreadEnv attach_thread() = 0;
    /**
     * 各平台实现, 解除绑定线程, 在子线程中调用, 用来释放线程所需的资源
     * @thread_env 子线程资源
     */
    virtual void detach_thread(ThreadEnv thread_env) = 0;

public:
    /**
    * 各平台实现, 在子线程调用, 使Callback::on_run_main在主线程中调用
    * 例如在主线的消息队列中添加消息
    * @thread_env 子线程资源
    * @callback 主线程的回调
    * @tag 唯一标示
    * @asyn true:异步调用, false:同步调用
    */
    virtual Int32 run_in_main(ThreadEnv thread_env, PlatformEnvCallback* callback, Uint32 tag,
                              bool asyn) = 0;
    /**
    * 各平台实现, 在主线程调用, 取消run_in_main的回调
    * 例如在主线的消息队列中移除消息
    * @tag 唯一标示
    */
    virtual Int32 cancel_run_in_main(Uint32 tag) = 0;
    /**
    * 各平台实现, 在子线程调用, 取消子线程的run_in_main的回调
    * 目前给安卓使用，内核有默认实现，直接调用cancel_run_in_main(Uint32 tag)
    * 例如在子线程的消息队列中移除消息
    * @tag 唯一标示
    */
    virtual Int32 cancel_run_in_main(ThreadEnv thread_env, Uint32 tag);

public:
    /**
    * 各平台实现, 在主线程调用, 使Callback::on_run_main在主线程中延迟调用
    * @milli_sec 延迟时间，单位毫秒
    * @callback 主线程的回调
    * @tag 唯一标示
    */
    virtual Int32 run_delay(Uint32 milli_sec, PlatformEnvCallback* callback, Uint32 tag) = 0;
    /**
    * 各平台实现, 在主线程调用, 取消run_delay的回调
    * @tag 唯一标示
    */
    virtual Int32 cancel_run_delay(Uint32 tag) = 0;
public:
    /// @brief 请求客户端准备好 Ai助聊/cand活动/陪伴等显示所需要的资源(图片等) ///
    /// @param url_list 请求的url列表 ///
    /// @param url_cnt 请求的url个数
    /// @param callback 客户端准备好资源后,的回调指针,如果准备资源过程中,内核被析构,请放弃回调
    /// @param tag 客户端准备好图片资源后,的回调参数
    /// @return true:表示资源已经是下载好了的，无需等待，客户端无需回调，内核直接handle当前资源
    /// @return false:资源未下载，通知客户端下载，下载完成后，客户端callback
    ///
    virtual bool request_url_resource(InputPad* input_pad, const char** url_list, Uint32 url_cnt,
                                      PlatformEnvCallback* callback, Uint32 tag) = 0;

    /// @brief 是否允许自动打开AiPad(Ai助聊卡片),
    /// @brief 当内核想要自动打开AiPad时,向客户端询问,这个时候是否允许内核打开卡片
    /// @param tab_type 内核期望打开的tabtype //
    /// @return ture 表示允许自动打开 //
    ///
    virtual bool is_allow_ai_pad_auto_open(InputPad* input_pad, AiPadInfo::AiPadTabType tab_type) = 0;

    /// @brief 是否允许展示cand活动,
    /// @brief 当内核想要展示cand活动时,向客户端询问,这个时候是否可以展示cand活动
    /// @return ture 表示允许展示cand活动 //
    virtual bool is_allow_cloud_campaign_show(InputPad* input_pad) = 0;

    /// @brief 获取 ios输入框中是否有文本, Android 不需要处理这个接口 ///
    /// @return ture 表示当前编辑框中有文本内容 ///
    ///
    virtual bool is_ios_edit_has_text(InputPad* input_pad);
    ///
    /// @brief 向客户端界面展示错误提示信息
    /// @param notice_type 展示的错误信息类型 ///
    /// @return ture 表示允许自动打开 ///
    ///
    virtual void show_error_notice(InputPad* input_pad, ErrorNoticeType notice_type) = 0;
    // @brief 是否当前允许展示云输入广告 //
    virtual bool is_allow_cloud_ad_show(InputPad* input_pad);
    /// @brief 是否允许展示次Cand高情商常驻提示 ///
    // 主线双端AI智能联想版本需要实现 ///
    virtual bool is_allow_aicand_higheq_notify_show(InputPad* input_pad);
public:

/// @brief 各平台实现, 判断手机中是否安装了app_list中的应用
///
    virtual Int32 find_app(app_msg* app_list, Uint32 app_list_len) = 0;
    /// 各平台实现，获取文件的MD5，md5的长度固定32位，如果不实现需要返回-1
    /// md5_str内核只分配32字节的空间，不要越界
    virtual Int32 md5(char* md5_str, const char* file_path);
public:
    // 汇率 接口 , 客户端使用系统函数进行回调，回调的格式和结果均为string///
    // 接口参数：dst 输出参数，返回汇率转化后的值 返回值是dst的长度，是0表示转化失败///
    // dst_name 和 dst_name_len 输入参数，目标转化的外币名称，如：CNY(表示人民币)///
    // src_in 和 src_len 输入参数，转化的外币数量，支持小数点///
    // src_name 和 src_name_len 输入参数，需要转化的外币名称，如 USD(表示美元)///
    // 如果 dst_len=0 表示转换非法 /// 内核需关注dst内存 ///
    virtual Uint32 get_exchange_rate_text(Uint8* dst,
                                          const Uint8* dst_name, Uint32 dst_name_len,
                                          const Uint8* src_in, Uint32 src_len,
                                          const Uint8* src_name, Uint32 src_name_len);
public:

    ///各平台实现, 获取输入框光标前与光标后内容，因为ios平台输入框需要与InputPad绑定，故需传入当前inputpad指针 ///

    virtual Int32 get_edit_before_cursor(InputPad* input_pad, Uint16* uni, Uint32 len) = 0;
    virtual Int32 get_edit_after_cursor(InputPad* input_pad, Uint16* uni, Uint32 len) = 0;

    /// 各平台实现, 获取输入框范围选中的内容
    //
    virtual Int32 get_edit_text_selection(InputPad* input_pad, Uint16* uni, Uint32 len) = 0;

    // 获取用户多轮输入内容 ///数据内容透传到服务器端用于模型识别意图 ///
    // uni是写入内存参数，len 是限制长度：内核限制2048 ///
    virtual Int32 get_edit_text_dialogue(Uint8* str, Uint32 len);
    // 这个接口，内核相关功能均已废弃，暂时停留只是为适配编译，不可在回调！///
#ifdef IPT_FEATURE_PC_ONLY
    virtual Int32 get_custom_data(CustomDataType type, void* buffer, Uint32 len, InputPad* input_pad,
                                  void* proto = nullptr, NetMan::StreamReqType stream_type = NetMan::STREAM_TYPE_MAX) = 0;
#else
    virtual Int32 get_custom_data(CustomDataType type, void* buffer, Uint32 len, InputPad* input_pad,
                                  ProtocolType proto = NetMan::PROTOCOL_TYPE_UNKNOWN,
                                  NetMan::StreamReqType stream_type = NetMan::STREAM_TYPE_MAX) = 0;
#endif
    virtual Int32 get_energy_value(InputPad* input_pad);
    // 是否允许展示此配置的Cand活动结果 ///
    virtual bool is_cloud_campaign_finaly_show(const char* schema_type, const char* schema_info,
            Int64 user_vip_condition, Int64 vip_expire_day, Int64 vip_expire_day_to);
    // 是否允许展示此配置的通用触发词结果 ///
    virtual bool is_common_trigger_finaly_show(const char* valid_people, Uint32 belong_type);
    //切换面板的时候通知上层, 上层可以进行数据准备配置等操作
    virtual void will_enter_pad(InputPad* input_pad, InputPad::PadId from);
    virtual void did_enter_pad(InputPad* input_pad, InputPad::PadId from);

public:
    /// @brief 根据name判断应用是否已安装
    /// @name android端的包名
    /// @name ios端需要的schema
    /// @name_len 为长度，name不一定已0结尾
    virtual bool has_install_app(const char* name, uint32_t name_len);
};

class DutyInfo {
public:
    enum ActionType { ///  职责类别
        ACTTYPE_NONE   = 0,         ///< 无行为
        ACTTYPE_INSERT = 1,         ///< 上屏
        ACTTYPE_BACK   = 2,         ///< 删除
        ACTTYPE_URL    = 3,         ///< 跳转链接
        /// 枚举为4的type客户端没有使用 ///
        ACTTYPE_SUG_INSERT = 5,     ///< sug直接上屏，删除光标前15个内容并上屏insert_buff()
        ACTTYPE_SUG_CUR_APP_LINK = 6,   ///< sug当前app跳转，sug_cmd_buff()和insert_buff()拼接后打开链接
        ACTTYPE_SUG_CUR_APP_SEARCH = 7, ///< sug当前app搜索, 删除光标前15个内容并上屏insert内容后回车
        /// sug_cmd_buff(),insert_buff(),sug_data()拼接后打开sug_cmd_app_buff() ///
        ACTTYPE_SUG_THIRDPARTY_APP_LINK = 8, ///< sug第三方app跳转链接
        ACTTYPE_SUG_THIRDPARTY_APP_SEARCH = 9, ///< sug第三方app搜索, 同ACTTYPE_SUG_THIRDPARTY_APP_LINK
        ACTTYPE_DOWNLOAD = 10, ///< sug 下载
        ACTTYPE_SUG_CUSTOM = 11, ///< sug特殊动作，使用url_buff()和insert_buff()进行未知操作
        ACTTYPE_EARTH = 12, ///< 地球键
        ACTTYPE_UP = 13,    ///< 上翻键
        ACTTYPE_DOWN = 14,  ///< 下翻键
        ACTTYPE_INSERT_CURSOR_BACKWARD = 15, ///< 上屏后光标回退一格，用于成对符号上屏，iOS废弃
        ACTTYPE_LIST_DEFINE = 16, ///< 符号列表的自定义
        ACTTYPE_VOICE = 17, ///< 语音按键
        ACTTYPE_MOVE_LEFT = 18, ///< 左移光标
        ACTTYPE_MOVE_RIGHT = 19, ///< 右移光标
        ACTTYPE_BACK_INSERT_DOT = 20, ///< 插入句号
        ACTTYPE_REPLACE = 21,   ///< 替换预上屏文本, get_replace_range
        ACTTYPE_SUBMIT_PICTURE = 22, ///< 或上屏图片，或上屏对应文字, 在gotourl信息为空时使用
        ACTTYPE_INSERT_AND_ENTER = 23, ///上屏文字后, 再发送一个回车
        ACTTYPE_REPLACE_HANDWRITE = 24, ///该(Action已废弃)
        ACTTYPE_INSERT_LINE_FEED = 25, ///上屏换行(iOS会先插入\n\a, 再把\a删除, 以防止插入\n触发发送操作)
    };

    enum TipType : Uint32 {  /// Tip类型
        TIP_CAPITAL = 0x1, ///<英文大写
        TIP_CAPITAL_FIRST = TIP_CAPITAL,  ///<英文大写，原变量取名有误
        TIP_CAPITAL_ALL = 0x2,  ///<英文锁定大写
        TIP_EN_ABC = 0x4,  ///<英文联想状态
        TIP_HAS_INPUT = 0x8,  ///<有输入码状态，控制①展示更多面板入口或者x，②除英文面板外显示换行还是确认
        TIP_MORE_SINGLE = 0x10,  ///<中文更多候选字单字状态
        TIP_SYM_LOCK = 0x20,  ///<符号面板锁定状态
        TIP_CN_LIAN = 0x40,  ///<当前是否是中文联想状态
        TIP_CN_SHIFT = 0x80,  ///<当前是否是中文下的临时英文输入状态
        TIP_PC_UMODE_BIHUA = 0x100, ///<PC专用,当前是否需要展示u模式的笔画提示
        //TIP_CHAT_NOINPUT = 0x100,  ///<输入框是聊天框并且当前无候选字和输入码
        TIP_CANGJIE_SC = 0x200,  ///<当前是速成仓颉状态
        TIP_CURSOR_ASSOCIATE = 0x400, ///<移动光标产生的联想//
        TIP_CURSOR_CALC = 0x800,     ///<移动光标产生的计算器结果//
        TIP_PC_VF_MODE = 0x1000,    ////<PC端vf模式////
        TIP_EN_INPUT_FULL = 0x2000,    ////<英文输入码已满////
        TIP_EN_SECOND_IS_BEST = 0x4000,    ////<英文第二词如果是最优选则通知上层刷新tip并自动candselect这个词////
        TIP_MULTI_SYM_DOWN = 0x8000,    ////<多符号功能键处于按下态////
        TIP_EN_CONFIRM = 0x10000,    ////<英文面板需要显示确认键的场景////
    };

//  有其他行为,可以后续定义
public:
    //是否外部主动切换面板, android在刷新InputPad::REFL_LAYOUT时用(为了方便上层异步模式下状态传递)
    virtual bool is_external_layout_switch(void) const = 0;
    ///< 获取职责id, 是一个自增长唯一id
    virtual Uint32 dutyid() const = 0;
    virtual Uint64 flash_flag() const = 0;      ///< 获取刷新标记
    virtual ActionType action_type() const = 0; ///< 获取行为类别
public:
    /////////////////////////////////////////////////
    // ACTTYPE_INSERT 上屏内容
    virtual Uint32 insert_len() const = 0;   ///< 获取上屏内容长度
    virtual const Uint16* insert_buff() const = 0;  ///< 获取上屏内容长度
    virtual Uint64 cloud_campaign_resource_id() const = 0; /// 获取云活动的resource_id ///
    // 内核点击透传id : 现使用场景1、本地触发词的belong_type//
    virtual Uint64 type_id() const = 0;
    // 内核点击透传query: 现使用场景1、本地触发词的trigger_word //
    virtual const Uint16* query_buff() const = 0;
    ///< 内核点击透传query: 现使用场景1、本地触发词的trigger_word长度 //
    virtual Uint32 query_buff_len() const = 0;
    ///< 获取上屏内容的类型
    virtual CandInfo::CandType insert_type() const = 0;
    virtual Uint32 get_trace_type() const = 0; ///< 获取用户轨迹类型 ///
    ///替换预上屏文本的范围(光标前, 光标后偏移), 替换预上屏文本的时候不要发送EventCursorMove。///
    virtual void get_replace_range(PLUINT32& before, PLUINT32& after) const = 0;
    /// @brief  表示预上屏时，将要被替换的文本(最长 256 个unicode字符)
    virtual const Uint16* get_replace_text() const = 0;
    /// @brief  表示预预上屏时，将要被替换的文本长度 ///
    virtual Uint32 get_replace_text_len() const = 0;
    ///<REFL_PRE_EXTRACT_SELECTION， 获得预上屏范围 ///
    virtual void get_pre_extract_range(Uint32& before_cursor_len, Uint32& after_cursor_len, bool& show) const = 0;
    virtual bool is_replace_keep_cursor() const = 0;
    virtual ConfigItems::QpFilter get_tab_filter() const = 0;
    // ACTTYPE_URL 跳转链接
    virtual Uint32 url_len() const = 0;  ///< 获取URL内容长度
    virtual const Uint8* url_buff() const = 0;  ///< 获取URL内容
    // ACTTYPE_SUG_CMD sug相关指令
    virtual Uint32 sug_cmd_len() const = 0;  ///< 获取sug指令长度
    virtual const Uint8* sug_cmd_buff() const = 0; ///< 获取sug指令内容
    //如果获取不到app,sug_cmd直接由当前应用处理
    virtual Uint32 sug_cmd_app_len() const = 0;  ///< 获取sug_app指令长度
    virtual const Uint8* sug_cmd_app_buff() const = 0; ///< 获取sug_app指令内容
    // ACTTYPE_DOWNLOAD 下载
    virtual Uint32 download_len() const = 0;  ///< 获取下载内容长度
    virtual const Uint8* download_buff() const = 0;  ///< 获取下载内容
    virtual const Uint32 tips() const = 0;  ///获取tip
    virtual bool refresh_list_by_select(void) const = 0;
public:
    virtual const Uint16* sug_card_title() const = 0; ///< 获取sug card 的title
    virtual Uint32 sug_card_title_len() const = 0; ///< 获取sug card 的title  长度
    virtual const Uint8* sug_data() const = 0; ///< 获取sug data
    virtual Uint32 sug_data_len() const = 0; ///< 获取sug data  长度
public:
    virtual bool is_cursor_backward() const =
        0; //是否 ,上屏后光标回退一格，用于成对符号上屏，目前iOS在用，其他后续看需求
    virtual bool is_open_schema() const = 0; //是否 ,云运营活动打开schema
    virtual const Uint8* schema_type() const = 0;  //schema type
    virtual Uint32 schema_type_len() const = 0;  //schema type
    virtual const Uint8* schema_info() const = 0;  //schema info
    virtual Uint32 schema_info_len() const = 0;  //schema info
    virtual const Uint8* url_req_text() const = 0;  //url_req_text
    virtual Uint32 url_req_text_len() const = 0;  //url_req_text
    ///获取popmenu的菜单等信息,PB格式///
    ///获取Pb序列化字符串的长度///
    virtual Uint32 popmenu_info_len() const = 0;
    ///获取PB序列化字符串的指针///
    virtual const Uint8* popmenu_info() const = 0;
    ///PC端获取Tab筛选码的pb串接口///
    virtual Uint32 tab_filter_info_len() const = 0;
    virtual const Uint8* tab_filter_info_str() const = 0;
    /// 获取手写笔势状态 ///
    virtual const Uint32 hw_gesture_type() const = 0;
    /// 获取点击触发词后，需要打开的是否是地图卡片 ///
    virtual bool is_trigger_map() const = 0;
    /// 是否是刷新地图触发词的duty
    virtual bool is_refresh_map_data() const = 0;
    /// 是否是刷新广告数据的duty，可能没有结果，需要调用AdManager::get_res_cnt，获取结果数量
    virtual bool is_refresh_ad_data() const = 0;
    virtual bool is_refresh_ad_data_app_intention() const = 0;
    virtual CandInfo::CandType schema_src() const = 0;
    /// 网盟SUG相关接口，穿山甲全部复用 ///
    /// 点击触发的是否是一个网盟SUG ///
    virtual bool is_wm_sug_app() const = 0;
    /// 点击触发的网盟SUG是否允许直投包 ///
    virtual bool is_wm_sug_app_allow_zhitou() const = 0;
    /// 点击触发的网盟SUG是否允许渠道包 ///
    virtual bool is_wm_sug_app_allow_channel() const = 0;
    /// 点击触发的网盟SUG是否允许促活 ///
    virtual bool is_wm_sug_app_allow_act() const = 0;
    /// 点击触发的网盟SUG是否在跳离APP时给予提示 ///
    virtual bool is_wm_sug_app_hint_when_leave() const = 0;
    /// 一个词可能同时是网盟和穿山甲的广告词，所以只能共用
    virtual bool is_from_wm() const = 0; /// 是否要请求网盟接口 ///
    virtual bool is_from_csj() const = 0; /// 是否要请求穿山甲接口（内核封装） ///
    /// 点击触发的网盟SUG的包名，穿山甲用不到 ///
    virtual const char* wm_sug_app_pkg_name() const = 0;
    // 是否是刷新云端意图的duty
    virtual bool is_refresh_intention_data() const = 0;
    /// 是否是云词刷新候选的duty
    virtual bool is_refresh_cand_by_cloud() const = 0;
};

class ZjForecastModel {
public:
    ///////////////////////////////////////////////////////////////////////////////////////////////////
    enum ZJ_FORECAST_MODEL {
        FORECAST_MODEL_CHAT = 0,      //聊天场景
        FORECAST_MODEL_STRANGER = 1,  //陌生人聊天
        FORECAST_MODEL_SHOPPING = 2,   //电商场景
        FORECAST_MODEL_JDQS = 3,      //绝地求生
        FORECAST_MODEL_WZRY = 4,       //王者荣耀
    };
public:
    static ZjForecastModel* create();

public:
    /**
    * @return 手机是否支持NPU模型
    **/
    virtual bool zj_is_supported_npu() = 0;

    /**加载整句预测模型
    * @param _trans_model  —  整句预测模型句柄
    * @param model_info_path - 模型文件路径
    * @model_type  -模型类型，参见ZJ_FORECAST_MODEL
    * @return 加载成功返回0，失败返回-1
    */
    virtual Int32 load_zj_model(const char* model_path, ZJ_FORECAST_MODEL model_type) = 0;

    /**卸载整句预测模型
    * @param _trans_model  —  整句预测模型句柄
    * @model_type  - 模型类型，参见ZJ_FORECAST_MODEL
    * @return 卸载成功返回0，失败返回-1
    */
    virtual Int32 unload_zj_model(ZJ_FORECAST_MODEL model_type) = 0;

    /**运行模型，线程不安全
    * @param _trans_model  —  整句预测模型句柄
    * @model_type  - 模型类型，参见ZJ_FORECAST_MODEL
    * @param buf         — ipt_transformer_get_req_data返回内容
    * @param buf_len     — ipt_transformer_get_req_data返回内容长度
    * @data_len          — 结果数据的长度
    * @return 返回云手写数据,返回PLNULL为获取失败,不需要释放
    **/
    virtual const Uint8* run_zj_model(ZJ_FORECAST_MODEL model_type,
                                      const Uint8* buf, Uint32 buf_len, Uint32& res_len) = 0;

};

#pragma pack(pop)
}

