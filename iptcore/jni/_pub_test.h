/// Created on: 2014年10月21日 ///
/// Author: heliang ///
/// 方便QA测试的接口 ///

#pragma once

#include "_pub_iptcore.h"
#include "tinystl/tstl_def.h"
#if (defined IPT_PLATFORM_WIN32) || (defined IPT_PLATFORM_LINUX)
#ifdef __cplusplus
extern "C" {
#endif
#endif
#ifdef IPT_PLATFORM_WIN32

#define CH_CELLWORD_SIZE(_word_len) (4 + ((_word_len)<<2))

#pragma pack (4)
#ifndef DbCellHeader_DEF_
#define DbCellHeader_DEF_
typedef struct DbCellHeader DbCellHeader;
#endif
struct DbCellHeader {
    PLUINT32 uid1; //文件头识别码1 数值待定
    PLUINT32 uid2; //文件头识别码2 数值待定
    PLUINT16 format1; //细胞词库格式版本号1，此数值大于当前客户端可识别的版本，表示客户端不兼容此格式。（破坏兼容性版本）
    PLUINT16 format2; //细胞词库格式版本号2，此数值大于当前客户端可识别的版本号，表示客户端仍然兼容此格式，只是新版本扩展的功能老客户端不可识别。
    PLUINT32 time;//文件生成日期. 格式举例：如整数 20100428 表示2010年04月28日
    //---------------------------------------------------
    PLUINT32 server_guid; //词库唯一识别码,服务端生成.
    PLUINT32 client_guid; //客户端安装时指定的本地ID。
    PLUINT32 ver1; //主版本
    PLUINT32 ver2; //次版本
    //----------------------------
    PLUINT32 ver3; //次次版本
    PLUINT32 data_type;//1表示增量词库。0 表示完整词库。
    PLUINT32 inner_ver_from; //完整词库下始终为0，增量词库下表示该增量词库对应的上一个版本的内部版本号。
    PLUINT32 inner_ver; //完整词库下表示内部版本号（流水线号），增量词库下表示该增量词库对应的最新版本的内部版本号。
    //-----------------------------
    PLUINT32 type1; //主分类ID ，分类ID 对应信息由服务端管理，空表示未分类。
    PLUINT32 type2; //二级分类ID
    PLUINT32 type3; //三级分类ID
    PLUINT32 type4; //四级分类ID
    //-----------------------------
    PLUINT32 ch_offset; // 中文词组数据块起始偏移量（0表示没有这块内容）
    PLUINT32 ch_datasise; // 中文词组数据块大小（未压缩大小）
    PLUINT32 ch_compalg; // 中文词组数据块压缩算法，0 表示不压缩，1表示 gzip压缩 （目前只定义这一种压缩算法，考虑到手机上的复杂度也建议只用这种压缩算法）
    PLUINT32 ch_com_size; // 中文词组数据块 压缩后大小（也即数据块在文件中占有空间大小）。（如果未压缩这个数值等于 ch_datasise）
    //-------------------------------
    PLUINT32 en_offset; // 英文单词块 起始偏移量 （0表示没有这块内容）
    PLUINT32 en_datasise; // 英文单词块 大小（未压缩大小）
    PLUINT32 en_compalg; // 英文单词块 压缩算法，0 表示不压缩，1表示 gzip压缩 （目前只定义这一种压缩算法，考虑到手机上的复杂度也建议只用这种压缩算法）
    PLUINT32 en_com_size; // 英文单词块 压缩后大小（也即数据块在文件中占有空间大小）。（如果未压缩这个数值等于 en_datasise）
    //-------------------------------
    PLUINT32 uni_offset; // 通用块 起始偏移量 （0表示没有这块内容)
    PLUINT32 uni_datasise; // 通用块 大小（未压缩大小）
    PLUINT32 uni_compalg; // 通用块 压缩算法，0 表示不压缩，1表示 gzip压缩 （目前只定义这一种压缩算法，考虑到手机上的复杂度也建议只用这种压缩算法）
    PLUINT32 uni_com_size; // 通用块  压缩后大小（也即数据块在文件中占有空间大小）。（如果未压缩这个数值等于 uni_datasise）
    //-----------------------------
    PLUINT32 ch_wordcount;//中文词组数据个数
    PLUINT32 en_wordcount;//英文单词个数
    PLUINT32 uni_wordcount;//通用组数据个数
    PLUINT32 reseved0;//保留位置，字节对齐
    //----------------------------------
    PLUINT32 name_len;//词库名字长度（最多32 unicode字符）
    PLUINT32 author_len;//作者信息长度（最多32 unicode字符）
    PLUINT32 keyword_len;//关键词长度（最多32 unicode字符）
    PLUINT32 info_len;//词库介绍长度（最多128 unicode字符）
    //---------------------------------
    PLUINT16 name_buf[32];//词库名字内容
    PLUINT16 author_buf[32];//作者信息内容
    PLUINT16 keyword_buf[32];//关键词内容
    PLUINT16 info_buf[128];//词库介绍内容
    //===================
    PLUINT32 add_ch_count;//
    PLUINT32 del_ch_count;//
    PLUINT32 add_en_count;//
    PLUINT32 del_en_count;//
    PLUINT32 add_uni_count;//
    PLUINT32 del_uni_count;//
    PLUINT32 reseveds[58];//保留区,用于扩展
};

#ifndef s_cell_word_DEF_
#define s_cell_word_DEF_
typedef struct s_cell_word s_cell_word;
#endif
struct s_cell_word {
    PLBYTE word_length; //词长
    PLBYTE word_flag;//flag
    PLUINT16 word_freq; //词频 范围 0 - 60000 。
    PLUINT16 data_buff[2]; //词组和拼音（这里的长度可根据word_length变 实际起始应该是 data_buff[(word_length&0xFF)*2]）。
};
#pragma pack ()
/**导出9键错误音节对
* @param fname — 导出路径
*/
void ipt_py_export_iec_yinjie(const char* fname);
/**导出9键多音节纠错对
 * @param fname — 导出路径
 */
void ipt_py_export_multi_iec_yinjie(const char* fname);
/**生成9键错误音节对
* @param _iptcore — 内核句柄
* @param fname — 导出路径
*/
void ipt_make_iec_yinjie_info(s_session* _session, const char* in_file,
        const char* out_file);
/**生成9键多音节纠错对
 * @param _iptcore — 内核句柄
 * @param fname — 导出路径
 */
void ipt_make_iec_multi_yinjie_info(s_session* _session, const char* in_file,
        const char* out_file);
/*
*获取系统词词频
* @param _session — 内核会话句柄
* @param py_str — 输入词的拼音（格式：gao|xing）(为空或“”代表是英文词）
* @param uni — 输入词的unicode
* @return 返回词频,不存在或输入有误返回-1
*/
PLINT32 ipt_get_sysword_freq(s_session* _session, char* py_str, PLUINT16* uni);

/**导出英文系统词库
* @param _session — 内核会话句柄
* @param file_input — 英文系统词库路径(bin文件)
* @param file_output — 导出英文系统词路径(文本文件)
* @return 操作成功，返回0
*/
PLINT32 ipt_test_export_en(s_iptcore* _iptcore, const char* file_input,
                                      const char* file_output);

/**导出中文系统词库
* @param _session — 内核会话句柄
* @param file_input — 中文系统词库路径(bin文件)
* @param file_output — 导出中文系统词路径(文本文件)
* @return 操作成功，返回0
*/
PLINT32 ipt_test_export_cz(s_iptcore* _iptcore, const char* _input_file,
                                      const char* _output_file);

/**导出五笔系统词库（混输版本）
* @param _session — 内核会话句柄
* @param file_input — 五笔系统词库路径(bin文件)
* @param _qm_file — 导出五笔全码路径(文本文件)
* @param _jm1_file — 导出五笔一级简码路径(文本文件)
* @param _jm2_file — 导出五笔二级简码路径(文本文件)
* @param _jm3_file — 导出五笔三级简码路径(文本文件)
* @return 操作成功，返回0
*/
PLINT32 ipt_test_export_wb_mixword(s_iptcore* _iptcore, const char* _input_file,
        const char* _qm_file, const char* _jm1_file, const char* _jm2_file, const char* _jm3_file);

/**导出笔画数据（混输版本非纠错版）
* @param _session — 内核会话句柄
* @param file_input — 笔画数据路径(bin文件)
* @param file_output — 导出笔画数据路径(文本文件)
* @return 操作成功，返回0
*/
//PLINT32 ipt_test_export_bh_mixword(s_iptcore* _iptcore, const char* _input_file,
//        const char* _output_file);

/**导出中文自造词数据（混输版本）
* @param _session — 内核会话句柄
* @param file_input — 中文自造词数据路径(bin文件)
* @param file_output — 导出中文自造词数据路径(文本文件)
* @return 操作成功，返回0
*/
PLINT32 ipt_test_export_uz_mixword(s_iptcore* _iptcore, const char* _input_file,
        const char* _output_file);

/**导出繁体数据（混输版本）
* @param _session — 内核会话句柄
* @param file_input — 繁体数据路径(bin文件)
* @param file_output — 导出繁体数据路径(文本文件)
* @return 操作成功，返回0
*/
PLINT32 ipt_test_export_ft_mixword(s_iptcore* _iptcore, const char* _input_file,
        const char* _1to1_file, const char* _1toN_file, const char* _freq_file, const char* _self_file);

/**解析关键词文件安装包
* @param _session — 内核会话句柄
* @param file_input — 关键词文件安装包数据路径(kwd文件)
* @param file_output — 导出关键词文件安装包数据路径(文本文件)
* @return 操作成功，返回0
*/
PLINT32 ipt_test_parse_keyword_cell(const char* _input_file,
        const char* _output_file);

/**解析非混输版本细胞词库安装包
* @param _session — 内核会话句柄
* @param file_input — 细胞词库安装包数据路径(bcd文件)
* @param file_output — 导出细胞词库安装包数据路径(文本文件)
* @return 操作成功，返回0
*/
PLINT32 ipt_test_export_cell_old(const char* in_file, const char* out_file);

/**生成表情关键词安装包
* @return 操作成功，返回0
*/
PLINT32 ipt_test_make_keyword_emoji(const char* _input_file, const char* _output_file,
        PLUINT32 uid1, PLUINT32 uid2, PLUINT16 format1, PLUINT16 format2, PLUINT32 time,
        PLUINT32 server_guid, PLUINT32 ver1, PLUINT32 ver2, PLUINT32 ver3, PLUINT32 data_type,
        PLUINT32 inner_ver_from, PLUINT32 inner_ver, PLUINT32 type1, PLUINT32 type2, PLUINT32 type3,
        PLUINT32 type4);

/**生成搜索关键词安装包
* @return 操作成功，返回0
*/
PLINT32 ipt_test_make_keyword_search(const char* _input_file,
        const char* _output_file, PLUINT32 uid1, PLUINT32 uid2, PLUINT16 format1, PLUINT16 format2,
        PLUINT32 time, PLUINT32 server_guid, PLUINT32 ver1, PLUINT32 ver2, PLUINT32 ver3,
        PLUINT32 data_type, PLUINT32 inner_ver_from, PLUINT32 inner_ver, PLUINT32 type1, PLUINT32 type2,
        PLUINT32 type3, PLUINT32 type4);

/**生成多媒体关键词安装包
* @return 操作成功，返回0
*/
PLINT32 ipt_test_make_keyword_media(const char* _input_file, const char* _output_file,
        PLUINT32 uid1, PLUINT32 uid2, PLUINT16 format1, PLUINT16 format2, PLUINT32 time,
        PLUINT32 server_guid, PLUINT32 ver1, PLUINT32 ver2, PLUINT32 ver3, PLUINT32 data_type,
        PLUINT32 inner_ver_from, PLUINT32 inner_ver, PLUINT32 type1, PLUINT32 type2, PLUINT32 type3,
        PLUINT32 type4);

/**生成歇后语关键词安装包
* @return 操作成功，返回0
*/
PLINT32 ipt_test_make_keyword_xiehouyu(const char* _input_file,
        const char* _output_file, PLUINT32 uid1, PLUINT32 uid2, PLUINT16 format1, PLUINT16 format2,
        PLUINT32 time, PLUINT32 server_guid, PLUINT32 ver1, PLUINT32 ver2, PLUINT32 ver3,
        PLUINT32 data_type, PLUINT32 inner_ver_from, PLUINT32 inner_ver, PLUINT32 type1, PLUINT32 type2,
        PLUINT32 type3, PLUINT32 type4);

/**生成快速输入关键词安装包
* @return 操作成功，返回0
*/
PLINT32 ipt_test_make_keyword_fast_input(const char* _input_file,
        const char* _output_file, PLUINT32 uid1, PLUINT32 uid2, PLUINT16 format1, PLUINT16 format2,
        PLUINT32 time, PLUINT32 server_guid, PLUINT32 ver1, PLUINT32 ver2, PLUINT32 ver3,
        PLUINT32 data_type, PLUINT32 inner_ver_from, PLUINT32 inner_ver, PLUINT32 type1, PLUINT32 type2,
        PLUINT32 type3, PLUINT32 type4);

/**生成直达号关键词安装包
* @return 操作成功，返回0
*/
PLINT32 ipt_test_make_keyword_zhidahao(const char* _input_file,
        const char* _output_file, PLUINT32 uid1, PLUINT32 uid2, PLUINT16 format1, PLUINT16 format2,
        PLUINT32 time, PLUINT32 server_guid, PLUINT32 ver1, PLUINT32 ver2, PLUINT32 ver3,
        PLUINT32 data_type, PLUINT32 inner_ver_from, PLUINT32 inner_ver, PLUINT32 type1, PLUINT32 type2,
        PLUINT32 type3, PLUINT32 type4);

/**生成细胞词库安装包
* @return 操作成功，返回0
*/
PLINT32 ipt_test_make_cell(const char* ch_hz2_file, const char* text_add_file,
                                      const char* text_del_file, const char* bin_file, PLUINT32 uid1, PLUINT32 uid2, PLUINT16 format1,
                                      PLUINT16 format2, PLUINT32 time, PLUINT32 server_guid, PLUINT32 data_type, PLUINT32 inner_ver_from,
                                      PLUINT32 inner_ver, PLUINT32 type1, PLUINT32 type2, PLUINT32 type3, PLUINT32 type4, PLUINT32 ver1,
                                      PLUINT32 ver2, PLUINT32 ver3, PLUINT16* name_buf, PLUINT32 name_len, PLUINT16* author_buf,
                                      PLUINT32 author_len, PLUINT16* keyword_buf, PLUINT32 keyword_len, PLUINT16* info_buf,
                                      PLUINT32 info_len);

/**导出细胞词库安装包
*/
PLINT32 ipt_test_export_cell_mixword(s_iptcore* _iptcore, const char* in_file,
        const char* out_file);

/**生成颜文字id映射文件
*/
PLINT32 ipt_test_make_idmap_emoticon(const char* _input_file,
        const char* _output_file,
        PLUINT32 uid1, PLUINT32 uid2, PLUINT32 time,
        PLUINT32 server_guid, PLUINT32 ver, PLUINT32 data_type,
        PLUINT32 inner_ver_from, PLUINT32 inner_ver, PLUINT32 type); //颜文字和表情映射

/**生成可设置包含关系的表情安装包
*/
PLINT32 ipt_test_make_keyword_emoji_with_contain(const char* _input_file,
        const char* _output_file, PLUINT32 uid1, PLUINT32 uid2, PLUINT16 format1, PLUINT16 format2,
        PLUINT32 time, PLUINT32 server_guid, PLUINT32 ver1, PLUINT32 ver2, PLUINT32 ver3,
        PLUINT32 data_type, PLUINT32 inner_ver_from, PLUINT32 inner_ver, PLUINT32 type1,
        PLUINT32 type2, PLUINT32 type3, PLUINT32 type4, PLBYTE is_voice);

/**生成可设置包含关系的多媒体安装包
*/
PLINT32 ipt_test_make_keyword_media_with_contain(const char* _input_file,
        const char* _output_file, PLUINT32 uid1, PLUINT32 uid2, PLUINT16 format1, PLUINT16 format2,
        PLUINT32 time, PLUINT32 server_guid, PLUINT32 ver1, PLUINT32 ver2, PLUINT32 ver3,
        PLUINT32 data_type, PLUINT32 inner_ver_from, PLUINT32 inner_ver, PLUINT32 type1,
        PLUINT32 type2, PLUINT32 type3, PLUINT32 type4);

/**生成可设置包含关系的歇后语、诗词安装包
*/
PLINT32 ipt_test_make_keyword_xiehouyu_with_contain(const char* _input_file,
        const char* _output_file, PLUINT32 uid1, PLUINT32 uid2, PLUINT16 format1, PLUINT16 format2,
        PLUINT32 time, PLUINT32 server_guid, PLUINT32 ver1, PLUINT32 ver2, PLUINT32 ver3,
        PLUINT32 data_type, PLUINT32 inner_ver_from, PLUINT32 inner_ver, PLUINT32 type1,
        PLUINT32 type2, PLUINT32 type3, PLUINT32 type4);

/**生成二次元emoji转换
*/
PLINT32 ipt_test_make_keyword_nijigen(const char* _input_file,
        const char* _output_file, PLUINT32 uid1, PLUINT32 uid2, PLUINT16 format1, PLUINT16 format2,
        PLUINT32 time, PLUINT32 server_guid, PLUINT32 ver1, PLUINT32 ver2, PLUINT32 ver3,
        PLUINT32 data_type, PLUINT32 inner_ver_from, PLUINT32 inner_ver, PLUINT32 type1,
        PLUINT32 type2, PLUINT32 type3, PLUINT32 type4);

PLINT32 ipt_test_make_keyword_egg(const char* _input_file,
        const char* _output_file, PLUINT32 uid1, PLUINT32 uid2, PLUINT16 format1, PLUINT16 format2,
        PLUINT32 time, PLUINT32 server_guid, PLUINT32 ver1, PLUINT32 ver2, PLUINT32 ver3,
        PLUINT32 data_type, PLUINT32 inner_ver_from, PLUINT32 inner_ver, PLUINT32 type1,
        PLUINT32 type2, PLUINT32 type3, PLUINT32 type4);


PLINT32 ipt_test_make_new_hz_cz(const char* main_hz, const char* exp_hz,
        const char* hz_bin, const char* hz_label, const char* main_cz, const char* mix_cz,
        const char* cz_bin, const char* hz_out, const char* cz_out, const char* usr_bin,
        const char* usr_out, PLUINT32 sys_ver, PLUINT32 cate_ver);

/**
* 获取云请求云预测以及等待时间的接口
* level默认填1，进入更多候选情况填2
*/
void ipt_test_get_cloud_trigger(s_session* _session,
        PLUINT32 level,
        PLUINT32* cloud_request,
        PLUINT32* cloud_forcast,
        PLUINT32* delay_time);
/**
* 带时间参数的查询
*/
PLINT32 ipt_test_append_point(s_session* _session,
        PLINT32 find_type,
        PLUINT32 pos,
        s_Point_v2* in_data,
        PLUINT32 input_case,
        char input,
        PLUINT32 context_id,
        PLUINT32 time);
#endif

/**输出整句拼接信息
 * @param info — 拼接信息
 */
PLINT32 ipt_sentence_info(s_session* _session, PLUINT32* info);
/**获取动态热区
* @param _iptcore — 内核词库数据句柄
* @param _iptcore — 内核会话句柄
* @param keys_rect — 当前的动态热区
* @return 0代表当前动态热区未初始化，为1表示已经初始化
*/
PLINT32 ipt_get_dynamic_area(s_session* _session, s_Rect_v2* keys_rect);

/**逐词，上屏一个词
 * @return 操作成功，返回1
 */
PLUINT32 ipt_test_get_word_freq(s_session* _session, PLUINT16* uni);
/**逐词，上屏一个词
 * @return 操作成功，返回1
 */
PLUINT32 ipt_test_cmd_add_unis(s_session* _session, PLUINT16* uni, char* pystr);
/**跑评测的一个find接口
* @return 操作成功，返回0
*/
PLINT32 ipt_test_query_find(s_session* _session,
                                       PLINT32 find_type,
                                       s_Point_v2* in_data,
                                       PLBYTE* type,
                                       char* input,
                                       PLUINT32 num);
PLINT32 ipt_test_query_find_time(s_session* _session,
                                            PLINT32 find_type,
                                            s_Point_v2* in_data,
                                            PLBYTE* type,
                                            char* input,
                                            PLUINT16* time,
                                            PLUINT32 num);
PLINT32 ipt_test_query_find_time_app(s_session* _session,
                                                PLINT32 find_type,
                                                s_Point_v2* in_data,
                                                PLBYTE* type,
                                                char* input,
                                                PLUINT16* time,
                                                PLUINT32 num,
                                                char* app_name,
                                                PLUINT32 attribute);
PLINT32 ipt_test_append_points(s_session* _session,
                                         PLINT32 find_type,
                                         PLUINT32 pos,
                                         s_Point_v2* in_data,
                                         PLBYTE* type,
                                         const char* input,
                                         PLUINT32 num);
PLINT32 ipt_test_append_points_time(s_session* _session,
                                               PLINT32 find_type,
                                               PLUINT32 pos,
                                               s_Point_v2* in_data,
                                               PLBYTE* type,
                                               const char* input,
                                               PLUINT16* time,
                                               PLUINT32 num);
PLINT32 ipt_test_append_points_time_app(s_session* _session,
                                                   PLINT32 find_type,
                                                   PLUINT32 pos,
                                                   s_Point_v2* in_data,
                                                   PLBYTE* type,
                                                   const char* input,
                                                   PLUINT16* time,
                                                   PLUINT32 num,
                                                   char* app_name,
                                                   PLUINT32 attribute);
PLINT32 ipt_test_delete_points_app(s_session* _session,
                                              PLINT32 find_type,
                                              PLUINT32 pos,
                                              PLUINT32 num,
                                              char* app_name,
                                              PLUINT32 attribute);

/**
 * 根据文件夹路径载入内核
 */
s_iptcore* ipt_load_core_by_dir(const char* file_path);
/**获取候选词匹配属性
 * @param _session — 内核会话句柄
 * @param idx — 位置
 */
PLUINT64 ipt_query_get_match_type(s_session* _session, PLUINT32 idx);
/**
 * 清空用户词库
 */
PLINT32 ipt_usr_restore_empty(s_iptcore* _iptcore);
/**
* 获取系统词库中的词类版本号, 若词库未加载返回-1
*/
PLINT32 ipt_test_get_cizu_cate_ver(s_session* _session);

/**
* 获取三维词库中的词类版本号，若词库未加载返回-1
*/
PLINT32 ipt_test_get_gram_cate_ver(s_session* _session);

/**盲人输入法数据文件制作
* @param _iptcore — 内核句柄
* @param input_file — 明文数据文件
* @param output_file — 二进制数据文件
*/
PLINT32 ipt_cand_con_make(s_iptcore* _iptcore, const char* input_file,
                                     const char* output_file);

PLINT32 ipt_test_ot_vkword_merge(char* input_file1, char* input_file2,
        char* output_file);

PLUINT32 ipt_test_get_cloud_pressure(s_session* _session);
/**解析ACS安装包
*/
PLINT32 ipt_test_search_parse_cell_file(const char* input_file, const char* output_file);

PLUINT32 ipt_test_cmd_push(s_session* _session,
                                      PLUINT16* uni,
                                      PLUINT32 uni_len,
                                      const char* py_str);
/**
 * 一段输入码连续输入，平均耗时
 */
PLUINT32 ipt_test_input_time(s_session* _session,
                                        const char* input,
                                        PLUINT32 is_t9);
/**
 * 获取候选权重（取低24位）
 */
PLINT32 ipt_test_get_freq(s_session* _session, PLUINT32 idx);
/**
 * 获取候选match信息
 */
PLUINT32 ipt_test_get_match(s_session* _session, PLUINT32 idx);
/**
 * 根据候选词获取候选match信息，只有在查询后才能生效
 */
PLUINT32 ipt_test_get_match_by_uni(s_session* _session, Uint16* uni);
/**
 * 获取当前输入码与候选词的匹配类型
 * 返回0 -- 不匹配；返回1 -- 精确匹配； 返回2 -- 纠错匹配
 */
PLUINT32 ipt_test_try_match(s_session* _session,
                                       char* input,
                                       PLUINT16* uni,
                                       char* zhuyin);

/**
 * 获取当前输入码与候选词的匹配类型
 * 返回0 -- 不匹配；返回1 -- 精确匹配； 返回2 -- 纠错匹配
 */
PLUINT32 ipt_test_try_match_simulate(s_session* _session,
        char* input,
        PLUINT16* uni,
        char* zhuyin);

//获取当前最大时间戳
Int32 ipt_get_time_stamp_max(s_session* _session);

//获取指定词的时间戳
Int32 ipt_get_time_stamp_by_word(s_session* _session,
        const Uint16* zids, Uint32 wlen);
/**
 * 为了兼容性的联想接口，以后估计会因为场景化加上app信息
 *
 */
PLINT32 ipt_test_query_findlian(s_session* _session,
        char* app_name,
        PLUINT32 attribute);
//设置内核时间
Int32 ipt_set_coretimeval(s_iptcore* _iptcore, Uint32 time_val);
/**
 * 获取一次查询中，输入码与音节的匹配对（全拼）
 * shenmeh 获取 shen 和 shen的拼音id的对应，me和me的拼音id，h不是全拼不要。
 */
Uint32 ipt_query_get_iec_pair(s_session* _session, Uint32 idx, Uint32 pos[], Uint32 pyids[]);
/**
 * 根据pyid获取音节
 *
 */
Uint32 ipt_get_pystr_by_id(char* py_str, Uint16 pyid);
#ifdef CHECK_MATCH_INPUT_CODE
#ifdef __cplusplus
extern "C" {
#endif
////check user input code and relative chinese with pinyin match or not
#include <stdio.h>
    /**
     * 根据资源文件创建内核_iptcore和_session
     * 参数为 资源文件
     * 返回-1 -- 初始化失败；返回1 -- 初始化成功
     */
    int init_session(char* res_path);
    /**
     * 获取当前输入码与候选词的匹配类型
     * 参数为 input_code为用户输入码，zhuyin为注音文件，uni为中文对应的unicode码，type为26键或者9键, debug_mode为1代表打印匹配log
     * 返回0 -- 不匹配；返回1 -- 精确匹配； 返回2 -- 纠错匹配；返回-1 -- 其他错误
     */
    int check_match(char* input_code, char* zhuyin, char* uni, char* type, int debug_mode);
//释放资源
    void tear_down();
#ifdef __cplusplus
}
#endif
#endif //end CHECK_MATCH_INPUT_CODE

#if (defined IPT_PLATFORM_WIN32) || (defined IPT_PLATFORM_LINUX)
#ifdef __cplusplus
}
#endif
#endif
