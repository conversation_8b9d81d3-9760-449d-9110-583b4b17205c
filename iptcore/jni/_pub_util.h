/// Created on: 2022-11-14  ///
/// Author: zhangyungang   ///
/// 不需要加载内核就能使用的工具类接口   ///

#pragma once

using Int8 = signed char;
using Uint8 = unsigned char;
using Int16 = signed short;
using Uint16 = unsigned short;
using Int32 = signed int;
using Uint32 = unsigned int;
using Int64 = signed long long;
using Uint64 = unsigned long long;

/**旧版中文自造词文件转换成新版文件
* @param output_dir  — 输出目录（目录下生成usr3user.bin和usr3cell.bin）
* @param imm_dir     — 内核不可变词库目录
* @param usr2_path   — 旧版usr2文件路径
* @param vkword_path — 旧版vkword文件路径（可以缺省）
* @return 0表示成功，其它表示失败
*/
Int32 ipt_dict_rebuild_from_old_usr2(const char* output_dir,
                                     const char* imm_dir,
                                     const char* usr2_path, const char* vkword_path = "");

/**旧版英文自造词文件转换成新版文件
* @param output_dir      — 输出目录（目录下生成en_user.bin）
* @param imm_dir         — 内核不可变词库目录
* @param ue2_path        — 旧版ue2文件路径
* @param learn_dic1_path — 旧版国际化词库1路径（可以缺省）
* @param learn_dic2_path — 旧版国际化词库2路径（可以缺省）
* @return 0表示成功，其它表示失败
*/
Int32 ipt_dict_rebuild_from_old_ue2(const char* output_dir,
                                    const char* imm_dir,
                                    const char* ue2_path, const char* learn_dic1_path = "", const char* learn_dic2_path = "");

/**旧版keyword文件转换成新版文件
* @param output_dir   — 输出目录（目录下生成kwd_keyword.bin）
* @param keyword_path — 旧版keyword文件路径
* @return 0表示成功，其它表示失败
*/
Int32 ipt_dict_rebuild_from_old_keyword(const char* output_dir,
                                        const char* keyword_path);

/**旧版注音自造词文件转换成新版文件
* @param output_dir  — 输出目录（目录下生成usr3zy.bin）
* @param imm_dir     — 内核不可变词库目录
* @param down_dir    — 内核后下发词库目录
* @param zy_usr_path — 旧版注音自造词文件路径
* @return 0表示成功，其它表示失败
*/
Int32 ipt_dict_rebuild_from_old_zy_usr(const char* output_dir,
                                       const char* imm_dir, const char* down_dir,
                                       const char* zy_usr_path);

/**旧版注音系统文件转换成新版文件
* @param output_dir — 输出目录（目录下生成py3zhuyin.bin）
* @param imm_dir    — 内核不可变词库目录
* @param hz_zy_path — 旧版汉字注音文件路径
* @param cz_zy_path — 旧版词组注音文件路径
* @return 0表示成功，其它表示失败
*/
Int32 ipt_dict_rebuild_from_old_zy(const char* output_dir,
                                   const char* imm_dir,
                                   const char* hz_zy_path, const char* cz_zy_path);

/**旧版仓颉文件转换成新版文件（cangjie.bin ==> cj2dict.bin, cangjie_quick.bin ==> cj2dict_quick.bin）
* @param output_dir   — 输出目录（目录下根据is_quick参数生成cj2dict.bin或cj2dict_quick.bin）
* @param cangjie_path — 旧版仓颉文件路径
* @param is_quick     — 是否属于仓颉快速
* @return 0表示成功，其它表示失败
*/
Int32 ipt_dict_rebuild_from_old_cangjie(const char* output_dir,
                                        const char* cangjie_path, bool is_quick);

