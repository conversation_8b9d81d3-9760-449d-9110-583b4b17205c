/// _pub_version.h ///
/// Created on: 2020-12-31 ///
/// Author: 版本号定义文件 ///

#pragma once
#define CORE_VERSION_DEF(_a, _b, _c, _d) (((_a) << 24) | ((_b) << 16) | ((_c) << 8) | (_d))
/////
//// 版本号变化的时候,编辑以下两行
#define CORE_VERSION_VAL CORE_VERSION_DEF(5, 69, 1, 16)
const unsigned int g_core_version_check = 88408336;
//////////////////////////////////////////////////////////////////////////////////// 
//// 这几行不要动 ///
#ifdef IPT_CONFIG_DEBUG_MODE
/// debug模式的版本定义,只保留低16位/// 
#define CORE_VERSION ((CORE_VERSION_VAL)&0xFFFF)
#else
#define CORE_VERSION CORE_VERSION_VAL
#endif
//// 这几行不要动 ///
const unsigned int  g_core_version_value = CORE_VERSION_VAL;
static_assert(g_core_version_value == g_core_version_check, "version num check error");
////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////
