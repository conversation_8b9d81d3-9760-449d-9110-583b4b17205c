//
// Created by <PERSON><PERSON><PERSON><PERSON> on 18/7/8.
//

#ifndef IPTANDROIDDEMO_IPT_JNI_CONFIG_CALLBACK_H
#define IPTANDROIDDEMO_IPT_JNI_CONFIG_CALLBACK_H

#include "_pub_iptpad.h"
#include <jni.h>

namespace iptjni {

    class IptJniConfigCallback : public iptcore::ConfigPad::ConfigCallback 
    {

    public:
        IptJniConfigCallback();
        ~IptJniConfigCallback();

    public:
        void on_config_data_change(); ///<  设置项中数据改变
        
    };
}

#endif //IPTANDROIDDEMO_IPT_JNI_CONFIG_CALLBACK_H
