#include "_cyp_wrapper.h"
#include "utility.h"
#include "_pub_iptcore.h"
#include <stdlib.h>
#include <cstring>
#include "ipt_pub_jni.h"

#define CHIPER_VERSION 0x01020100
#define B64_VERSION 2

/*
 * Class:     com_baidu_util_ChiperEncrypt
 * Method:    nativeChiperGetVersion
 * Signature: ()I
 */
jint jni_get_encrypt_version(JNIEnv *env, jobject obj)
{
	return CHIPER_VERSION;
}

/*
 * Class:     com_baidu_util_ChiperEncrypt
 * Method:    nativeB64GetVersion
 * Signature: ()I
 */
jint jni_get_encrypt_b64_version(JNIEnv *env, jclass obj)
{
	return B64_VERSION;
}

/*
 * Class:     com_baidu_util_ChiperEncrypt
 * Method:    nativeB64Encode
 * Signature: ([B)[B
 */
jbyteArray jni_b64_encode(JNIEnv *env, jclass obj, jbyteArray input)
{
	if (input == NULL)
	{
		return NULL;
	}
	jbyteArray output = NULL;
	jboolean isCopy = JNI_FALSE;
	PLBYTE* in_buffer = NULL;
	PLINT32 out_len = 0;
	PLBYTE* out_buffer = NULL;
	PLINT32 in_len = (PLINT32) (env->GetArrayLength(input));
	if (in_len > 0)
	{
		in_buffer = (PLBYTE*) (env->GetByteArrayElements(input, &isCopy));
		out_len = cyp::wrp_b64_buffer_need(in_len);
		out_buffer = (PLBYTE*)_aligned4_malloc(out_len);
		memset(out_buffer, 0, out_len);
		memcpy(out_buffer, in_buffer, in_len);
		env->ReleaseByteArrayElements(input, (jbyte*)in_buffer, JNI_ABORT);

		int real_out_len = cyp::wrp_b64_encode(out_buffer, in_len, 0);
		if (real_out_len > 0)
		{
			output = env->NewByteArray(out_len - 1);
			env->SetByteArrayRegion(output, 0, (out_len - 1), (const jbyte*)out_buffer);
		}
		_aligned4_free(out_buffer);
	}
	return output;
}

/*
 * Class:     com_baidu_util_ChiperEncrypt
 * Method:    nativeB64Decode
 * Signature: ([B)[B
 */
jbyteArray jni_b64_decode(JNIEnv *env, jclass obj, jbyteArray input)
{
	if (input == NULL)
	{
		return NULL;
	}
	jbyteArray output = NULL;
	jboolean isCopy = JNI_FALSE;
	PLINT32 in_len = (PLINT32) (env->GetArrayLength(input));
	PLBYTE* input_str = NULL;
	PLBYTE *out_buffer = NULL;
	if (in_len > 0)
	{
		input_str = (PLBYTE*) (env->GetByteArrayElements(input, &isCopy));
		out_buffer = (PLBYTE*) _aligned4_malloc((sizeof(PLBYTE*) * (in_len + 1)));
		memcpy(out_buffer, input_str, in_len);
		out_buffer[in_len] = '\0';
		env->ReleaseByteArrayElements(input, (jbyte*)input_str, JNI_ABORT);

		int real_out_len = cyp::wrp_b64_decode(out_buffer, 0);
		if (real_out_len >= 0)
		{
			output = env->NewByteArray(real_out_len);
			env->SetByteArrayRegion(output, 0, real_out_len, (const jbyte*)out_buffer);
		}

		_aligned4_free(out_buffer);
	}
	return output;
}

/*
 * Class:     com_baidu_util_ChiperEncrypt
 * Method:    nativeAESEncrypt
 * Signature: ([B)[B
 */
jbyteArray jni_aes_encrypt(JNIEnv *env, jobject obj, jbyteArray input)
{
    if (input == NULL)
    {
        return NULL;
    }
    jbyteArray output = NULL;
    jboolean isCopy = JNI_FALSE;
    PLBYTE* in_buffer = NULL;
    PLINT32 out_len = 0;
    PLBYTE* out_buffer = NULL;
    PLINT32 in_len = (PLINT32) (env->GetArrayLength(input));
    cyp::WrpAESHeader *aes_head = NULL;
    if (in_len > 0)
    {
        in_buffer = (PLBYTE*) (env->GetByteArrayElements(input, &isCopy));
        out_len = cyp::wrp_aes_buffer_need(in_len);
        out_buffer = (PLBYTE*)_aligned4_malloc(out_len);
        memset(out_buffer, 0, out_len);
        aes_head = (cyp::WrpAESHeader*)out_buffer;
        aes_head->content_len = in_len;
        for (int i = 0; i < 16; i++)
        {
            aes_head->userkey[i] = (PLBYTE)(rand() % 100);
        }
        // 20是数据头的长度
        memcpy(out_buffer + 20, in_buffer, in_len);
        env->ReleaseByteArrayElements(input, (jbyte*)in_buffer, JNI_ABORT);

        int real_out_len = cyp::wrp_aes_encrypt(out_buffer, out_len);
        if (real_out_len == 0)
        {
            output = env->NewByteArray(out_len);
            env->SetByteArrayRegion(output, 0, (out_len), (const jbyte*)out_buffer);
        }
        _aligned4_free(out_buffer);
    }
    return output;
}

/*
 * Class:     com_baidu_util_ChiperEncrypt
 * Method:    nativeAESEncryptV2
 * Signature: ([B)[B
 */
jbyteArray jni_aes_encrypt_v2(JNIEnv *env, jobject obj, jbyteArray input)
{
	if (input == NULL)
	{
		return NULL;
	}
	jbyteArray output = NULL;
	jboolean isCopy = JNI_FALSE;
	PLBYTE* in_buffer = NULL;
	PLINT32 out_len = 0;
	PLBYTE* out_buffer = NULL;
	PLINT32 in_len = (PLINT32) (env->GetArrayLength(input));
    cyp::WrpAESHeader *aes_head = NULL;
	if (in_len > 0)
	{
		in_buffer = (PLBYTE*) (env->GetByteArrayElements(input, &isCopy));
		out_len = cyp::wrp_aes_buffer_need(in_len);
		out_buffer = (PLBYTE*)_aligned4_malloc(out_len);
		memset(out_buffer, 0, out_len);
		aes_head = (cyp::WrpAESHeader*)out_buffer;
		aes_head->content_len = in_len;
		for (int i = 0; i < 16; i++)
		{
			aes_head->userkey[i] = (PLBYTE)(rand() % 100);
		}
		memcpy(out_buffer + 20, in_buffer, in_len);
		env->ReleaseByteArrayElements(input, (jbyte*)in_buffer, JNI_ABORT);

		int real_out_len = cyp::wrp_aes_encrypt_v2(out_buffer, out_len);
		if (real_out_len == 0)
		{
			output = env->NewByteArray(out_len);
			env->SetByteArrayRegion(output, 0, (out_len), (const jbyte*)out_buffer);
		}
		_aligned4_free(out_buffer);
	}
	return output;
}

/*
 * Class:     com_baidu_util_ChiperEncrypt
 * Method:    nativeAESDecrypt
 * Signature: ([B)[B
 */
jbyteArray jni_aes_decrypt(JNIEnv *env, jobject obj, jbyteArray input)
{
	if (input == NULL)
	{
		return NULL;
	}
	jbyteArray output = NULL;
	jboolean isCopy = JNI_FALSE;
	PLINT32 in_len = (PLINT32) (env->GetArrayLength(input));
	PLBYTE* input_str = NULL;
	PLBYTE* out_buffer = NULL;
    cyp::WrpAESHeader *aes_head = NULL;
	int decode_data_len = 0;
	PLBYTE* decode_data = NULL;

	if (in_len > 0)
	{
		input_str = (PLBYTE*) (env->GetByteArrayElements(input, &isCopy));
		out_buffer = (PLBYTE*) _aligned4_malloc((sizeof(PLBYTE*) * in_len));
		memcpy(out_buffer, input_str, in_len);

		env->ReleaseByteArrayElements(input, (jbyte*)input_str, JNI_ABORT);

		int real_out_len = cyp::wrp_aes_decrypt(out_buffer, in_len);
		if (real_out_len == 0)
		{
			aes_head = (cyp::WrpAESHeader *)out_buffer;
			decode_data_len = aes_head->content_len;
			decode_data = (PLBYTE*)(out_buffer + 20);
			output = env->NewByteArray(decode_data_len);
			env->SetByteArrayRegion(output, 0, decode_data_len, (const jbyte*)decode_data);
		}

		_aligned4_free(out_buffer);
	}
	return output;
}

/*
 * Class:     com_baidu_util_ChiperEncrypt
 * Method:    nativeAESB64Encrypt
 * Signature: ([B)[B
 */
jbyteArray jni_aes_b64_encrypt(JNIEnv *env, jobject obj, jbyteArray input)
{
	if (input == NULL)
	{
		return NULL;
	}
	jbyteArray output = NULL;
	jboolean isCopy = JNI_FALSE;
	PLBYTE* in_buffer = NULL;
	PLINT32 out_len = 0;
	PLBYTE* out_buffer = NULL;
	PLINT32 in_len = (PLINT32) (env->GetArrayLength(input));
    cyp::WrpAESHeader *aes_head = NULL;
	if (in_len > 0)
	{
		in_buffer = (PLBYTE*) (env->GetByteArrayElements(input, &isCopy));
		out_len = cyp::wrp_aes_b64_buffer_need(in_len);
		out_buffer = (PLBYTE*)_aligned4_malloc(out_len);
		memset(out_buffer, 0, out_len);
		aes_head = (cyp::WrpAESHeader*)out_buffer;
		aes_head->content_len = in_len;
		for (int i = 0; i < 16; i++)
		{
			aes_head->userkey[i] = (PLBYTE)(rand() % 100);
		}
		memcpy(out_buffer + 20, in_buffer, in_len);
		env->ReleaseByteArrayElements(input, (jbyte*)in_buffer, JNI_ABORT);

		int real_out_len = cyp::wrp_aes_b64_encrypt(out_buffer, out_len);
		if (real_out_len == 0)
		{
			output = env->NewByteArray(out_len - 1);
			env->SetByteArrayRegion(output, 0, (out_len - 1), (const jbyte*)out_buffer);
		}
		_aligned4_free(out_buffer);
	}
	return output;
}

/*
 * Class:     com_baidu_util_ChiperEncrypt
 * Method:    nativeAESB64EncryptV2
 * Signature: ([B)[B
 */
jbyteArray jni_aes_b64_encrypt_v2(JNIEnv *env, jobject obj, jbyteArray input)
{
	if (input == NULL)
	{
		return NULL;
	}
	jbyteArray output = NULL;
	jboolean isCopy = JNI_FALSE;
	PLBYTE* in_buffer = NULL;
	PLINT32 out_len = 0;
	PLBYTE* out_buffer = NULL;
	PLINT32 in_len = (PLINT32) (env->GetArrayLength(input));
	cyp::WrpAESHeader *aes_head = NULL;
	if (in_len > 0)
	{
		in_buffer = (PLBYTE*) (env->GetByteArrayElements(input, &isCopy));
		out_len = cyp::wrp_aes_b64_buffer_need(in_len);
		out_buffer = (PLBYTE*)_aligned4_malloc(out_len);
		memset(out_buffer, 0, out_len);
		aes_head = (cyp::WrpAESHeader*)out_buffer;
		aes_head->content_len = in_len;
		for (int i = 0; i < 16; i++)
		{
			aes_head->userkey[i] = (PLBYTE)(rand() % 100);
		}
		memcpy(out_buffer + 20, in_buffer, in_len);
		env->ReleaseByteArrayElements(input, (jbyte*)in_buffer, JNI_ABORT);

		int real_out_len = cyp::wrp_aes_b64_encrypt_v2(out_buffer, out_len);
		if (real_out_len == 0)
		{
			output = env->NewByteArray(out_len - 1);
			env->SetByteArrayRegion(output, 0, (out_len - 1), (const jbyte*)out_buffer);
		}
		_aligned4_free(out_buffer);
	}
	return output;
}

/*
 * Class:     com_baidu_util_ChiperEncrypt
 * Method:    nativeAESB64Decrypt
 * Signature: ([B)[B
 */
jbyteArray jni_aes_b64_decrypt(JNIEnv *env, jobject obj, jbyteArray input)
{
	if (input == NULL)
	{
		return NULL;
	}
	jbyteArray output = NULL;
	jboolean isCopy = JNI_FALSE;
	PLINT32 in_len = (PLINT32) (env->GetArrayLength(input));
	PLBYTE* input_str = NULL;
	PLBYTE *out_buffer = NULL;
	cyp::WrpAESHeader *aes_head = NULL;
	int decode_data_len = 0;
	PLBYTE* decode_data = NULL;

	if (in_len > 0)
	{
		input_str = (PLBYTE*) (env->GetByteArrayElements(input, &isCopy));
		out_buffer = (PLBYTE*) _aligned4_malloc((sizeof(PLBYTE*) * (in_len + 1)));
		memcpy(out_buffer, input_str, in_len);
		out_buffer[in_len] = '\0';

		env->ReleaseByteArrayElements(input, (jbyte*)input_str, JNI_ABORT);

		int real_out_len = cyp::wrp_aes_b64_decrypt(out_buffer);
		if (real_out_len == 0)
		{
			aes_head = (cyp::WrpAESHeader *)out_buffer;
			decode_data_len = aes_head->content_len;
			decode_data = (PLBYTE*)(out_buffer + 20);
			output = env->NewByteArray(decode_data_len);
			env->SetByteArrayRegion(output, 0, decode_data_len, (const jbyte*)decode_data);
		}

		_aligned4_free(out_buffer);
	}
	return output;
}

/*
 * Class:     com_baidu_util_ChiperEncrypt
 * Method:    nativeRSAEncrypt
 * Signature: ([B)[B
 */
jbyteArray jni_rsa_encrypt(JNIEnv *env, jobject obj, jbyteArray input)
{
	if (input == NULL)
	{
		return NULL;
	}
	jbyteArray output = NULL;
	jboolean isCopy = JNI_FALSE;
	PLBYTE* in_buffer = NULL;
	PLINT32 out_len = 0;
	PLBYTE* out_buffer = NULL;
	PLINT32 in_len = (PLINT32) (env->GetArrayLength(input));
	cyp::WrpRSAHeader *rsa_head = NULL;
	if (in_len > 0)
	{
		in_buffer = (PLBYTE*) (env->GetByteArrayElements(input, &isCopy));
		out_len = cyp::wrp_rsa_buffer_need(in_len);
		out_buffer = (PLBYTE*)_aligned4_malloc(out_len);
		memset(out_buffer, 0, out_len);
		rsa_head = (cyp::WrpRSAHeader*)out_buffer;
		rsa_head->content_len = in_len;
		for (int i = 0; i < 7; i++)
		{
			rsa_head->reserved[i] = (PLUINT32)(rand() % 65535);
		}
		memcpy(out_buffer + 32, in_buffer, in_len);
		env->ReleaseByteArrayElements(input, (jbyte*)in_buffer, JNI_ABORT);

		int real_out_len = cyp::wrp_rsa_pub_encrypt_bydk(out_buffer, out_len);
		if (real_out_len == 0)
		{
			output = env->NewByteArray(out_len);
			env->SetByteArrayRegion(output, 0, (out_len), (const jbyte*)out_buffer);
		}
		_aligned4_free(out_buffer);
	}
	return output;
}

/*
 * Class:     com_baidu_util_ChiperEncrypt
 * Method:    nativeRSADecrypt
 * Signature: ([B)[B
 */
jbyteArray jni_rsa_decrypt(JNIEnv *env, jobject obj, jbyteArray input)
{
	if (input == NULL)
	{
		return NULL;
	}
	jbyteArray output = NULL;
	jboolean isCopy = JNI_FALSE;
	PLBYTE* in_buffer = NULL;
	PLINT32 out_len = 0;
	PLBYTE* out_buffer = NULL;
	PLINT32 in_len = (PLINT32) (env->GetArrayLength(input));
	cyp::WrpRSAHeader *rsa_head = NULL;
	if (in_len > 0)
	{
		in_buffer = (PLBYTE*) (env->GetByteArrayElements(input, &isCopy));
		out_len = in_len;
		out_buffer = (PLBYTE*)_aligned4_malloc(out_len);
		memcpy(out_buffer, in_buffer, in_len);
		env->ReleaseByteArrayElements(input, (jbyte*)in_buffer, JNI_ABORT);


		int real_out_len = cyp::wrp_rsa_pub_decrypt_bydk(out_buffer, out_len);
		if (real_out_len == 0)
		{
			rsa_head = (cyp::WrpRSAHeader*)out_buffer;
			PLINT32 output_len = (PLINT32) rsa_head->content_len;
			output = env->NewByteArray(output_len);
			env->SetByteArrayRegion(output, 0, (output_len), (const jbyte*)(out_buffer + 32));
		}
		_aligned4_free(out_buffer);
	}
	return output;
}
