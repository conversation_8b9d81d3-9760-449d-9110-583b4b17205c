/*
 * ipt_jni_env.cpp
 *
 *  Created on: 2016-3-29
 *      Author: xuxiang
 *      实现平台环境
 */

#include "ipt_jni_env.h"
#include "ipt_jni_log.h"
#include <cstring>
#include "android/log.h"
#include "ipt_jni_gobal.h"

#define g_ipt_main iptjni::IptJniGlobal::get_ipt_jni_main()

namespace iptjni
{

IptJniEnv::IptJniEnv(JavaVM* java_vm, JNIEnv* jni_env, jobject android_env) :
        _java_vm(java_vm), _g_ime_service_callback(NULL), _find_app_method_id(NULL),
        _get_text_before_method_id(NULL), _get_text_after_method_id(NULL),
        _get_text_sel_method_id(NULL),
        _get_edit_text_dialogue_method_id(NULL),
        _request_url_resource_method_id(NULL), _is_allow_ai_pad_auto_open_method_id(NULL),
        _is_allow_cloud_campaign_show_method_id(NULL),
        _is_allow_cloud_ad_show_method_id(NULL), _is_allow_aicand_higheq_notify_show_method_id(NULL),
        _get_energy_value_method_id(NULL),
        _is_cloud_campaign_finaly_show_method_id(NULL),
        _is_common_trigger_finaly_show_method_id(NULL),
         _will_enter_pad_method_id(NULL), _did_enter_pad_method_id(NULL),
        _duty_info_cb_id(NULL), _duty_info_get_id(NULL),
        _has_install_app_method_id(NULL),
        _core_method_start_id(NULL), _core_method_end_id(NULL), _core_post_inner_method_cost(NULL),
        _honor_notepad_get_response_callback_id(NULL)
{
    if (jni_env != NULL)
    {
        _g_ard_env = jni_env->NewGlobalRef(android_env);
        set_thread_env(&_main_thread_env, jni_env, _g_ard_env);

        jclass ard_env_calss = jni_env->GetObjectClass(_g_ard_env);
        _duty_info_get_id = jni_env->GetMethodID(ard_env_calss, "getDutyInfo", "()Ljava/lang/Object;");
        _duty_info_cb_id = jni_env->GetMethodID(ard_env_calss, "onDutyInfo", "(Ljava/lang/Object;)V");
        _core_method_start_id = jni_env->GetMethodID(ard_env_calss, "onCoreMethodStart", "(ILjava/lang/String;)V");
        _core_method_end_id = jni_env->GetMethodID(ard_env_calss, "onCoreMethodEnd", "(ILjava/lang/String;)V");
        _core_post_inner_method_cost = jni_env->GetMethodID(ard_env_calss, "onCoreInnerCost", "(II)V");
        _honor_notepad_get_response_callback_id = jni_env->GetMethodID(ard_env_calss, "onHonorNotepadGetResponse", "(Ljava/lang/String;I)V");
    }
    else
    {
        _g_ard_env = NULL;
        set_thread_env(&_main_thread_env, NULL, NULL);
    }
}

IptJniEnv::~IptJniEnv()
{
    if (_g_ard_env != NULL)
    {
        _main_thread_env.jni_env->DeleteGlobalRef(_g_ard_env);
        _g_ard_env = NULL;
        _get_text_before_method_id = NULL;
        _get_text_after_method_id = NULL;
        _get_text_sel_method_id = NULL;
        _get_edit_text_dialogue_method_id = NULL;
        _find_app_method_id = NULL;
        _request_url_resource_method_id = NULL;
        _is_allow_ai_pad_auto_open_method_id = NULL;
        _is_allow_cloud_campaign_show_method_id = NULL;
        _is_allow_cloud_ad_show_method_id = NULL;
        _is_allow_aicand_higheq_notify_show_method_id = NULL;
        _get_energy_value_method_id = NULL;
        _is_cloud_campaign_finaly_show_method_id = NULL;
        _is_common_trigger_finaly_show_method_id = NULL;
        _will_enter_pad_method_id = NULL;
        _did_enter_pad_method_id = NULL;

        _duty_info_get_id = NULL;
        _duty_info_cb_id = NULL;

        _has_install_app_method_id = NULL;
        _core_method_start_id = NULL;
        _core_method_end_id = NULL;
        _core_post_inner_method_cost = NULL;
        _honor_notepad_get_response_callback_id = NULL;
    }

    if (_g_ime_service_callback != NULL)
    {
        _main_thread_env.jni_env->DeleteGlobalRef(_g_ime_service_callback);
        _g_ime_service_callback = NULL;
    }

    memset(&_main_thread_env, 0, sizeof(JniThreadEnv));
    _java_vm = NULL;
}

/**
 * 平台实现: 绑定一个新的线程, 在子线程中调用, 用来创建线程所需的资源
 */
iptcore::PlatformEnv::ThreadEnv IptJniEnv::attach_thread()
{
    JniThreadEnv* thread_env = new JniThreadEnv();
    JNIEnv* jni_env = NULL;

    if (_java_vm != NULL)
    {
        _java_vm->AttachCurrentThread(&jni_env, NULL);
    }

    set_thread_env(thread_env, jni_env, _g_ard_env);

    return thread_env;
}

/**
 * 平台实现: 解除绑定线程, 在子线程中调用, 用来释放线程所需的资源
 * @thread_env 子线程资源
 */
void IptJniEnv::detach_thread(ThreadEnv thread_env)
{
    JniThreadEnv* jni_thread_env = (JniThreadEnv*) (thread_env);

    if (_java_vm != NULL)
    {
        _java_vm->DetachCurrentThread();
    }

    delete jni_thread_env;
    jni_thread_env = NULL;
}

/**
 * 各平台实现, 在子线程调用, 使Callback::on_run_main在主线程中调用
*/
    Int32
    IptJniEnv::run_in_main(ThreadEnv thread_env, iptcore::PlatformEnvCallback *callback, Uint32 tag,
                           bool asyn) {
    Tsize callback_address = (Tsize) callback;
    JniThreadEnv* jni_thread_env = (JniThreadEnv*) (thread_env);

    if (!asyn || jni_thread_env->jni_env == NULL
            || jni_thread_env->run_in_main_id == NULL
            || jni_thread_env->ard_env == NULL)
    {
        return -1;
    }

    jni_thread_env->jni_env->CallVoidMethod(jni_thread_env->ard_env,
            jni_thread_env->run_in_main_id, tag, static_cast<jlong>(callback_address));

    return 0;
}

/**
 * 取消在主线程的执行
 * @param tag 对应的tag
 */
Int32 IptJniEnv::cancel_run_in_main(Uint32 tag)
{
    return cancel_run_delay(tag);
}

/**
 * 触发延迟执行
 */
    Int32
    IptJniEnv::run_delay(Uint32 milli_sec, iptcore::PlatformEnvCallback *callback, Uint32 tag) {
    Tsize callback_address = (Tsize) callback;
    JniThreadEnv* jni_thread_env = &_main_thread_env;

    if (jni_thread_env->jni_env == NULL || jni_thread_env->run_delay_id == NULL
            || jni_thread_env->ard_env == NULL)
    {
        return -1;
    }

    jni_thread_env->jni_env->CallVoidMethod(jni_thread_env->ard_env,
            jni_thread_env->run_delay_id, milli_sec, tag, static_cast<jlong>(callback_address));
    return 0;
}

/**
 * 取消延迟执行
 */
Int32 IptJniEnv::cancel_run_delay(Uint32 tag)
{
    JniThreadEnv* jni_thread_env = &_main_thread_env;

    if (jni_thread_env->jni_env == NULL || jni_thread_env->cancel_run_id == NULL
            || jni_thread_env->ard_env == NULL)
    {
        return -1;
    }

    jni_thread_env->jni_env->CallVoidMethod(jni_thread_env->ard_env,
            jni_thread_env->cancel_run_id, tag);


    return 0;
}

/**
 * 请求客户端准备好 Ai助聊/cand活动/陪伴等显示所需要的资源(图片等)
 * @param url_list 请求的url列表
 * @param url_cnt 请求的url个数
 * @param callback 客户端准备好资源后,的回调指针,如果准备资源过程中,内核被析构,请放弃回调
 * @param tag 客户端准备好图片资源后,的回调参数
 */
bool IptJniEnv::request_url_resource(iptcore::InputPad* input_pad,
        const char** url_list, Uint32 url_cnt, iptcore::PlatformEnvCallback* callback, Uint32 tag)
{
    JNIEnv* env = _main_thread_env.jni_env;

    if (_g_ime_service_callback == nullptr || env == nullptr
        || _request_url_resource_method_id == nullptr || url_cnt == 0 || url_list == nullptr)
    {
        return false;
    }

    jclass objClass = env->FindClass("java/lang/String");
    jobjectArray urlList = env->NewObjectArray(url_cnt, objClass, nullptr);

    for(int i = 0; i < url_cnt; i++)
    {
        const char* url_ptr = url_list[i];
        jstring url = g_ipt_main.utf_to_str(env, (Uint8 *) url_ptr, strlen(url_ptr));

        env->SetObjectArrayElement(urlList, i, url);
    }

    jlong jcb = reinterpret_cast<Tsize>(callback);
    bool ret = env->CallBooleanMethod(_g_ime_service_callback, _request_url_resource_method_id, urlList, jcb, tag);
    return ret;
}

/**
 * 是否允许自动打开AiPad(Ai助聊卡片)。当内核想要自动打开AiPad时,向客户端询问,这个时候是否允许内核打开卡片
 * @param tab_type 内核期望打开的tabtype
 * @return ture 表示允许自动打开
 */
bool IptJniEnv::is_allow_ai_pad_auto_open(iptcore::InputPad* input_pad,
        iptcore::AiPadInfo::AiPadTabType tab_type)
{
    JNIEnv* env = _main_thread_env.jni_env;

    if (_g_ime_service_callback == nullptr || env == nullptr || _is_allow_ai_pad_auto_open_method_id == nullptr)
    {
        return false;
    }

    jboolean ret = env->CallBooleanMethod(_g_ime_service_callback, _is_allow_ai_pad_auto_open_method_id, tab_type);
    return ret;
}

bool IptJniEnv::is_allow_cloud_campaign_show(iptcore::InputPad* input_pad) {

    JNIEnv* env = _main_thread_env.jni_env;

    if (_g_ime_service_callback == nullptr || env == nullptr || _is_allow_cloud_campaign_show_method_id == nullptr)
    {
        return false;
    }

    jboolean ret = env->CallBooleanMethod(_g_ime_service_callback, _is_allow_cloud_campaign_show_method_id);
    return ret;
}

bool IptJniEnv::is_allow_cloud_ad_show(iptcore::InputPad* input_pad) {

    JNIEnv* env = _main_thread_env.jni_env;

    if (_g_ime_service_callback == nullptr || env == nullptr || _is_allow_cloud_ad_show_method_id == nullptr)
    {
        return false;
    }

    jboolean ret = env->CallBooleanMethod(_g_ime_service_callback, _is_allow_cloud_ad_show_method_id);
    return ret;
}

bool IptJniEnv::is_allow_aicand_higheq_notify_show(iptcore::InputPad* input_pad) {

    JNIEnv* env = _main_thread_env.jni_env;

    if (_g_ime_service_callback == nullptr || env == nullptr || _is_allow_aicand_higheq_notify_show_method_id == nullptr)
    {
        return false;
    }

    jboolean ret = env->CallBooleanMethod(_g_ime_service_callback, _is_allow_aicand_higheq_notify_show_method_id);
    return ret;
}

bool IptJniEnv::is_cloud_campaign_finaly_show(const char* schema_type, const char* schema_info, Int64 user_vip_condition, Int64 vip_expire_day, Int64 vip_expire_day_to) {
    JNIEnv *env = _main_thread_env.jni_env;

    if (_g_ime_service_callback != nullptr
            && _is_cloud_campaign_finaly_show_method_id != nullptr
            && env != nullptr)
    {
        jstring type = g_ipt_main.utf_to_str(env, (Uint8 *) schema_type, strlen(schema_type));
        jstring info = g_ipt_main.utf_to_str(env, (Uint8 *) schema_info, strlen(schema_info));
        jboolean ret = env->CallBooleanMethod(_g_ime_service_callback, _is_cloud_campaign_finaly_show_method_id, type, info, user_vip_condition, vip_expire_day, vip_expire_day_to);
        return ret;
    }
    return false;
}

bool IptJniEnv::is_common_trigger_finaly_show(const char* valid_people, Uint32 belong_type) {
    JNIEnv *env = _main_thread_env.jni_env;

    if (_g_ime_service_callback != nullptr
        && _is_common_trigger_finaly_show_method_id != nullptr
        && env != nullptr)
    {
        jstring valid_people_string = g_ipt_main.utf_to_str(env, (Uint8 *) valid_people, strlen(valid_people));
        jboolean ret = env->CallBooleanMethod(_g_ime_service_callback, _is_common_trigger_finaly_show_method_id, valid_people_string, belong_type);
        return ret;
    }
    return false;
}

/**
 * 内核通知端上显示错误的提示，一般是一个toast
 * @param input_pad     pad
 * @param notice_type   错误提示的类型，标识具体发生错误的业务
 */
void IptJniEnv::show_error_notice(iptcore::InputPad* input_pad, iptcore::ErrorNoticeType notice_type)
{
    // 目前不需要做任何事
}

Int32 IptJniEnv::find_app(app_msg* app_list, Uint32 app_list_len)
{

    JNIEnv* env = _main_thread_env.jni_env;
    if (_g_ime_service_callback == NULL || _find_app_method_id == NULL || env == NULL
                                        || app_list == NULL || app_list_len == 0)
    {
        return -1;
    }
    jobjectArray app_msg_array = env->NewObjectArray(app_list_len, env->FindClass("com/baidu/iptcore/info/IptAppMsgInfo"), NULL);

    for (int i = 0; i < app_list_len; i++)
    {
        jobject app_msg = initAppMsgInfo(env, *(app_list + i));
        env->SetObjectArrayElement(app_msg_array, i, app_msg);
    }
    jint ret = (jint) env->CallIntMethod(_g_ime_service_callback, _find_app_method_id, app_msg_array);
    return ret;
}

Int32 IptJniEnv::md5(char* md5_str, const char* file_path)
{
    JNIEnv* env = _main_thread_env.jni_env;

    jclass objClass = env->FindClass("com/baidu/iptcore/util/Md5FileHelper");
    jmethodID md5_id = env->GetStaticMethodID(objClass, "md5", "(Ljava/lang/String;)Ljava/lang/String;");

    jstring path = g_ipt_main.utf_to_str(env, (Uint8 *) file_path, strlen(file_path));

    jstring md5_result = (jstring)(env->CallStaticObjectMethod(objClass, md5_id, path));

    const char* md5_char = env->GetStringUTFChars(md5_result, nullptr);

    Int32 resultLength = env->GetStringLength(md5_result);
    if (resultLength > 32) {
        resultLength = 32;
    }

    for (int i = 0; i < resultLength; i++)
    {
        md5_str[i] = md5_char[i];
    }

    env->ReleaseStringUTFChars(md5_result, md5_char);

    return 0;
}

jobject IptJniEnv::initAppMsgInfo(JNIEnv* env, app_msg app_msg_info)
{
    jclass objClass = env->FindClass("com/baidu/iptcore/info/IptAppMsgInfo");
    static jmethodID constructor_method = 0;
    static jmethodID set_package_name_method = 0;
    static jmethodID set_version_method = 0;
    if (constructor_method == 0)
    {
        constructor_method = env->GetMethodID(objClass, "<init>","()V");
    }
    if (set_package_name_method == 0)
    {
        set_package_name_method = env->GetMethodID(objClass, "setPackageName","(Ljava/lang/String;)V");
    }
    if (set_version_method == 0)
    {
        set_version_method = env->GetMethodID(objClass, "setVersion","([I[II)V");
    }

    jstring package_name = g_ipt_main.utf_to_str(env, app_msg_info.name, app_msg_info.name_len);
    jintArray min_version = (jintArray) env->NewIntArray(app_msg_info.ver_num);
    jintArray max_version = (jintArray) env->NewIntArray(app_msg_info.ver_num);
    for (int i = 0; i < app_msg_info.ver_num; i++)
    {
        env->SetIntArrayRegion(min_version, i, 1, (const jint*) &app_msg_info.min_ver[i]);
        env->SetIntArrayRegion(max_version, i, 1, (const jint*) &app_msg_info.max_ver[i]);
    }

    jobject element = env->NewObject(objClass, constructor_method);
    env->CallVoidMethod(element, set_package_name_method, package_name);
    env->CallVoidMethod(element, set_version_method, min_version, max_version, app_msg_info.ver_num);

    return element;
}

bool IptJniEnv::ver_is_legall(app_msg* app, Int32 ver)
{
    Uint32 ver_len = app->ver_num;
    bool legall = false;
    for (int i = 0; i < ver_len; i++)
    {
        if ((ver >= app->min_ver[i] || app->min_ver[i] == 0xffffffff) && (ver <= app->max_ver[i] || app->max_ver[i] == 0))
        {
            legall = true;
            break;
        }
    }
    return legall;
}

Int32 IptJniEnv::get_edit_before_cursor(iptcore::InputPad* inputPad, Uint16* uni, Uint32 len)
{
    JNIEnv* env = _main_thread_env.jni_env;
    Int32 ret = 0;
    if (_g_ime_service_callback != NULL && _get_text_before_method_id != NULL && env != NULL)
    {
        jstring txt = (jstring) env->CallObjectMethod(_g_ime_service_callback, _get_text_before_method_id, len);
        if (txt != NULL)
        {
            ret = env->GetStringLength(txt);
            if (ret > 0)
            {
                const jchar* val = env->GetStringChars(txt, NULL);
                for (int i = 0; i < ret; i++)
                {
                    uni[i] = val[i];
                }
            }
        }
    }
    return ret;
}

Int32 IptJniEnv::get_edit_after_cursor(iptcore::InputPad* inputPad, Uint16* uni, Uint32 len)
{
    JNIEnv* env = _main_thread_env.jni_env;
    Int32 ret = 0;
    if (_g_ime_service_callback != NULL && _get_text_after_method_id != NULL && env != NULL)
    {
        jstring txt = (jstring) env->CallObjectMethod(_g_ime_service_callback, _get_text_after_method_id, len);
        if (txt != NULL)
        {
            ret = env->GetStringLength(txt);
            if (ret > 0)
            {
                const jchar* val = env->GetStringChars(txt, NULL);
                for (int i = 0; i < ret; i++)
                {
                    uni[i] = val[i];
                }
            }
        }
    }
    return ret;
}

/**
* 各平台实现, 获取输入框范围选中的内容
*/
Int32 IptJniEnv::get_edit_text_selection(iptcore::InputPad *input_pad, Uint16 *uni, Uint32 len)
{
    JNIEnv* env = _main_thread_env.jni_env;
    Int32 ret = 0;
    if (_g_ime_service_callback != NULL && _get_text_sel_method_id != NULL && env != NULL)
    {
        jstring txt = (jstring) env->CallObjectMethod(_g_ime_service_callback, _get_text_sel_method_id, len);
        if (txt != NULL)
        {
            ret = env->GetStringLength(txt);
            if (ret > 0)
            {
                const jchar* val = env->GetStringChars(txt, NULL);
                for (int i = 0; i < ret; i++)
                {
                    uni[i] = val[i];
                }
            }
        }
    }
    return ret;
}

Int32 IptJniEnv::get_edit_text_dialogue(Uint8 *uni, Uint32 len)
{
    JNIEnv* env = _main_thread_env.jni_env;
    Int32 ret = 0;
    if (_g_ime_service_callback != NULL && _get_edit_text_dialogue_method_id != NULL && env != NULL)
    {
        jstring txt = (jstring) env->CallObjectMethod(_g_ime_service_callback, _get_edit_text_dialogue_method_id, len);
        if (txt != NULL)
        {
            // 获取字符串的实际长度
            jsize contentLength = env->GetStringUTFLength(txt);
            ret = contentLength;

            if (contentLength > 0)
            {
                const char* contentChar = env->GetStringUTFChars(txt, NULL);

                // LOGI("jni call getEditTextDialogue, get_edit_text_dialogue: contentChar: %s",contentChar);
                // 确定要拷贝的长度，取实际长度和最大长度的较小值
                Uint32 beginPos = 0;
                if (contentLength > len) {
                    beginPos = contentLength - len;
                    ret = len;
                } else {
                    ret = contentLength;
                }

                // 使用内存拷贝将contentChar的内容复制到uni中
                memcpy(uni, contentChar + beginPos, ret);

                // LOGI("jni call getEditTextDialogue, get_edit_text_dialogue, uni: %s", jstr);
                // 释放内存
                env->ReleaseStringUTFChars(txt, contentChar);
            }
        }
    }
    return ret;
}

Int32 IptJniEnv::get_custom_data(CustomDataType type, void *buffer, Uint32 max_len,
                                 iptcore::InputPad *input_pad, ProtocolType proto, iptcore::NetMan::StreamReqType stream_type)
{
    return 0;
}

Int32 IptJniEnv::get_energy_value(iptcore::InputPad* inputPad) {
    JNIEnv *env = _main_thread_env.jni_env;
    if (_g_ime_service_callback != NULL && _get_energy_value_method_id != NULL && env != NULL)
    {
        jint ret = (jint) env->CallIntMethod(_g_ime_service_callback, _get_energy_value_method_id);
        return ret;
    }
    return -1;
}

void IptJniEnv::will_enter_pad(iptcore::InputPad* input_pad, iptcore::InputPad::PadId from)
{
    JNIEnv *env = _main_thread_env.jni_env;
    if (_g_ime_service_callback != NULL && _will_enter_pad_method_id != NULL && env != NULL)
    {
        iptcore::InputPad::PadId toPadId = iptcore::InputPad::PadId::PAD_NONE;
        if (input_pad != NULL)
        {
            toPadId = input_pad->get_pad_id();
        }
        env->CallVoidMethod(_g_ime_service_callback, _will_enter_pad_method_id, from, toPadId);
    }
}

void IptJniEnv::did_enter_pad(iptcore::InputPad* input_pad, iptcore::InputPad::PadId from)
{
    JNIEnv *env = _main_thread_env.jni_env;
    if (_g_ime_service_callback != NULL && _did_enter_pad_method_id != NULL && env != NULL)
    {
        iptcore::InputPad::PadId toPadId = iptcore::InputPad::PadId::PAD_NONE;
        if (input_pad != NULL)
        {
            toPadId = input_pad->get_pad_id();
        }
        env->CallVoidMethod(_g_ime_service_callback, _did_enter_pad_method_id, from, toPadId);
    }
}

void IptJniEnv::on_flog_write(const char *text) const
{
    JNIEnv* env = nullptr;
    if (_java_vm != nullptr)
    {
        _java_vm->AttachCurrentThread(&env, nullptr);
    }
    if (env == nullptr)
    {
        return;
    }

    jclass clazz = env->FindClass("com/baidu/iptcore/util/Logger");
    if (clazz == nullptr)
    {
        return;
    }

    jmethodID on_log_write_id = env->GetStaticMethodID(clazz, "onJniLogWrite", "(Ljava/lang/String;)V");
    if (on_log_write_id == nullptr)
    {
        return;
    }

    jstring message = iptjni::IptJniMain::utf_to_str(env, (Uint8 *) text, strlen(text));
    env->CallStaticVoidMethod(clazz, on_log_write_id, message);
}

void IptJniEnv::perform_callback(Uint32 tag, Tsize arg1)
{
    iptcore::PlatformEnvCallback *callback = (iptcore::PlatformEnvCallback *) arg1;
    if (NULL == callback) 
    {
        LOGI("callback null");
        return;
    }
    LOGI("callback on_run_main");
    callback->on_run_main(tag);
}

void IptJniEnv::set_ime_service_callback(JNIEnv* jni_env, jobject ime_service_callback)
{
    if (_g_ime_service_callback != NULL)
    {
        jni_env->DeleteGlobalRef(_g_ime_service_callback);
        _g_ime_service_callback = NULL;
        _find_app_method_id = NULL;
        _request_url_resource_method_id = NULL;
        _is_allow_ai_pad_auto_open_method_id = NULL;
        _is_allow_cloud_campaign_show_method_id = NULL;
        _is_allow_cloud_ad_show_method_id = NULL;
        _is_allow_aicand_higheq_notify_show_method_id = NULL;
        _get_text_before_method_id = NULL;
        _get_text_after_method_id = NULL;
        _get_text_sel_method_id = NULL;
        _get_edit_text_dialogue_method_id = NULL;
        _get_energy_value_method_id = NULL;
        _is_cloud_campaign_finaly_show_method_id = NULL;
        _is_common_trigger_finaly_show_method_id = NULL;
        _will_enter_pad_method_id = NULL;
        _did_enter_pad_method_id = NULL;
        _has_install_app_method_id = NULL;
    }

    if (ime_service_callback != NULL)
    {
        _g_ime_service_callback = jni_env->NewGlobalRef(ime_service_callback);
        jclass ime_callback_class = jni_env->GetObjectClass(_g_ime_service_callback);
        _find_app_method_id = jni_env->GetMethodID(ime_callback_class,
                                                   "findApp",
                                                   "([Lcom/baidu/iptcore/info/IptAppMsgInfo;)I");
        _request_url_resource_method_id = jni_env->GetMethodID(ime_callback_class,
                "requestUrlResource", "([Ljava/lang/String;JI)Z");
        _is_allow_ai_pad_auto_open_method_id = jni_env->GetMethodID(ime_callback_class,
                "isAllowAiPadAutoOpen", "(I)Z");
        _is_allow_cloud_campaign_show_method_id = jni_env->GetMethodID(ime_callback_class,
                "isAllowCloudCampaignShow", "()Z");
        _is_allow_cloud_ad_show_method_id = jni_env->GetMethodID(ime_callback_class,
                "isAllowCloudAdShow", "()Z");
        _is_allow_aicand_higheq_notify_show_method_id = jni_env->GetMethodID(ime_callback_class,
                        "isAllowMinorCandHighEQShow", "()Z");
        _get_text_before_method_id = jni_env->GetMethodID(ime_callback_class,
                                                          "getEditBeforeCursor",
                                                          "(I)Ljava/lang/String;");
        _get_text_after_method_id = jni_env->GetMethodID(ime_callback_class,
                                                          "getEditAfterCursor",
                                                          "(I)Ljava/lang/String;");
        _get_text_sel_method_id = jni_env->GetMethodID(ime_callback_class,
                                                         "getEditSelection",
                                                         "(I)Ljava/lang/String;");
        _get_edit_text_dialogue_method_id = jni_env->GetMethodID(ime_callback_class,
                                                       "getEditTextDialogue",
                                                       "(I)Ljava/lang/String;");
        _get_energy_value_method_id = jni_env->GetMethodID(ime_callback_class,
                                                   "getEnergyValue",
                                                   "()I");
        _is_cloud_campaign_finaly_show_method_id = jni_env->GetMethodID(ime_callback_class,
                                                                        "isCloudCampaignFinalyShow",
                                                                        "(Ljava/lang/String;Ljava/lang/String;JJJ)Z");
        _is_common_trigger_finaly_show_method_id = jni_env->GetMethodID(ime_callback_class,
                                                                        "isCommonTriggerFinallyShow",
                                                                        "(Ljava/lang/String;I)Z");
        _will_enter_pad_method_id = jni_env->GetMethodID(ime_callback_class,
                                                   "willEnterPad",
                                                   "(II)V");
        _did_enter_pad_method_id = jni_env->GetMethodID(ime_callback_class,
                                                         "didEnterPad",
                                                         "(II)V");
        _has_install_app_method_id = jni_env->GetMethodID(ime_callback_class,
                                                          "hasInstallApp",
                                                          "(Ljava/lang/String;)Z");
        LOGI("_find_app:%ld, _requesturl:%ld, _aiautoopen:%ld, _cloud_campaign_show:%ld, _allow_cloud_ad_show:%ld, _before:%ld, _after:%ld, _sel:%d",
                  _find_app_method_id, _request_url_resource_method_id, _is_allow_ai_pad_auto_open_method_id,
                  _is_allow_cloud_campaign_show_method_id,
                  _is_allow_cloud_ad_show_method_id,
                  _is_allow_aicand_higheq_notify_show_method_id,
                  _get_text_before_method_id, _get_text_after_method_id,
                  _get_text_sel_method_id);
    }
}

void IptJniEnv::set_thread_env(JniThreadEnv* thread_env, JNIEnv* jni_env,
        jobject ard_env)
{
    thread_env->jni_env = jni_env;
    thread_env->ard_env = ard_env;
    thread_env->run_in_main_id = NULL;
    thread_env->run_delay_id = NULL;
    thread_env->cancel_run_id = NULL;

    if (jni_env != NULL)
    {
        jclass ard_env_calss = jni_env->GetObjectClass(ard_env);
        LOGI("ard_env_calss:%ld", ard_env_calss);
        thread_env->run_in_main_id = jni_env->GetMethodID(ard_env_calss, "runInMain", "(IJ)V");
        LOGI("run_in_main_id:%ld",  (thread_env->run_in_main_id));
        thread_env->run_delay_id = jni_env->GetMethodID(ard_env_calss, "runDelay", "(IIJ)V");
        LOGI("run_delay_id:%ld", (thread_env->run_delay_id));
        thread_env->cancel_run_id = jni_env->GetMethodID(ard_env_calss, "cancelRun", "(I)V");
        LOGI("cancel_run_id:%ld", (thread_env->cancel_run_id));
        jni_env->DeleteLocalRef(ard_env_calss);
    }
}

bool IptJniEnv::has_install_app(const char* pkg_name, uint32_t name_len) {
    JNIEnv *env = _main_thread_env.jni_env;
    if (_g_ime_service_callback != NULL && _has_install_app_method_id != NULL && env != NULL)
    {
        jstring app_name = g_ipt_main.utf_to_str(env, (Uint8 *) pkg_name, name_len);
        jboolean ret = env->CallBooleanMethod(_g_ime_service_callback, _has_install_app_method_id, app_name);
        return ret;
    }
    return false;
}

}
