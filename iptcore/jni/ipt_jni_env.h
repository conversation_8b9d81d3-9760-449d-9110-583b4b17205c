/*
 * ipt_jni_env.h
 *
 *  Created on: 2016-3-29
 *      Author: xuxiang
 *      实现平台环境
 */

#ifndef COREBD_IPT_JNI_ENV_H
#define COREBD_IPT_JNI_ENV_H

#include "_pub_iptpad.h"
#include <jni.h>

namespace iptjni
{

class IptJniEnv : public iptcore::PlatformEnv
{
public:
    class JniThreadEnv
    {
    public:
        JNIEnv* jni_env;
        jobject ard_env;
        jmethodID run_in_main_id;
        jmethodID run_delay_id;
        jmethodID cancel_run_id;
    };

public:
    IptJniEnv(JavaVM* java_vm, JNIEnv* jni_env, jobject android_env);
    ~IptJniEnv();

public:
    ThreadEnv attach_thread() override ;
    void detach_thread(ThreadEnv thread_env) override ;

public:
    Int32 run_in_main(ThreadEnv thread_env, iptcore::PlatformEnvCallback *callback, Uint32 tag,
            bool asyn) override ;
    Int32 cancel_run_in_main(Uint32 tag) override ;

public:
    Int32 run_delay(Uint32 milli_sec, iptcore::PlatformEnvCallback *callback, Uint32 tag) override ;
    Int32 cancel_run_delay(Uint32 tag) override ;

public:
    bool request_url_resource(iptcore::InputPad* input_pad, const char** url_list,
            Uint32 url_cnt, iptcore::PlatformEnvCallback* callback, Uint32 tag) override ;
    bool is_allow_ai_pad_auto_open(iptcore::InputPad* input_pad, iptcore::AiPadInfo::AiPadTabType tab_type) override ;
    bool is_allow_cloud_campaign_show(iptcore::InputPad* input_pad) override ;
    void show_error_notice(iptcore::InputPad* input_pad, iptcore::ErrorNoticeType notice_type) override ;
    bool is_allow_cloud_ad_show(iptcore::InputPad* input_pad) override ;
    bool is_cloud_campaign_finaly_show(const char* schema_type, const char* schema_info, Int64 user_vip_condition, Int64 vip_expire_day, Int64 vip_expire_day_to) override ;
    bool is_common_trigger_finaly_show(const char* valid_people, Uint32 belong_type) override ;
    bool is_allow_aicand_higheq_notify_show(iptcore::InputPad* input_pad) override ;
public:
    Int32 find_app(app_msg* app_list, Uint32 app_list_len) override ;
    Int32 md5(char* md5_str, const char* file_path) override ;
    bool ver_is_legall(app_msg* app, Int32 ver);
    jobject initAppMsgInfo(JNIEnv* env, app_msg app_msg_info);

public:
    Int32 get_edit_before_cursor(iptcore::InputPad* inputPad, Uint16* uni, Uint32 len) override ;
    Int32 get_edit_after_cursor(iptcore::InputPad* input_pad, Uint16* uni, Uint32 len) override ;
    Int32 get_edit_text_selection(iptcore::InputPad* input_pad, Uint16* uni, Uint32 len) override ;
    Int32 get_edit_text_dialogue(Uint8* str, Uint32 len) override ;
    Int32 get_custom_data(CustomDataType type, void *buffer, Uint32 len,
            iptcore::InputPad *input_pad, ProtocolType proto, iptcore::NetMan::StreamReqType stream_type) override ;
    Int32 get_energy_value(iptcore::InputPad* input_pad) override ;
    void will_enter_pad(iptcore::InputPad* input_pad, iptcore::InputPad::PadId from) override ;
    void did_enter_pad(iptcore::InputPad* input_pad, iptcore::InputPad::PadId from) override ;
    bool has_install_app(const char* pkg_name, uint32_t name_len) override ;
public:
    void on_flog_write(const char* text) const;

public:
    void perform_callback(Uint32 tag, Tsize arg1);
    void set_ime_service_callback(JNIEnv* jni_env, jobject ime_service_callback);

private:
    void set_thread_env(JniThreadEnv* thread_env, JNIEnv* jni_env,
            jobject ard_env);

public:
    JavaVM* _java_vm;
    JniThreadEnv _main_thread_env;
    jobject _g_ard_env;
    jobject _g_ime_service_callback;
    jmethodID _get_text_before_method_id;
    jmethodID _get_text_after_method_id;
    jmethodID _get_text_sel_method_id;
    jmethodID _get_edit_text_dialogue_method_id;
    jmethodID _find_app_method_id;
    jmethodID _request_url_resource_method_id;
    jmethodID _is_allow_ai_pad_auto_open_method_id;
    jmethodID _is_allow_cloud_campaign_show_method_id;
    jmethodID _is_allow_cloud_ad_show_method_id;
    jmethodID _is_allow_aicand_higheq_notify_show_method_id;
    jmethodID _get_energy_value_method_id;
    jmethodID _is_cloud_campaign_finaly_show_method_id;
    jmethodID _is_common_trigger_finaly_show_method_id;
    jmethodID _will_enter_pad_method_id;
    jmethodID _did_enter_pad_method_id;
    jmethodID _duty_info_cb_id;
    jmethodID _duty_info_get_id;
    jmethodID _has_install_app_method_id;
    jmethodID _core_method_start_id;
    jmethodID _core_method_end_id;
    jmethodID _core_post_inner_method_cost;
    jmethodID _honor_notepad_get_response_callback_id;
};

}

#endif /* COREBD_IPT_JNI_ENV_H */
