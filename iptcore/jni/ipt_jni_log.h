/*
 * ipt_jni_log.h
 *
 *  Created on: 2016-7-12
 *      Author: xuxiang
 *      jni日志相关
 */

#ifndef COREBD_IPT_JNI_LOG
#define COREBD_IPT_JNI_LOG

#include "ipt_pub_jni.h"

#ifdef IPT_JNI_LOG_PRINT
#include <android/log.h>
#define log_print(type, tag, format, ...) __android_log_print(type, tag, format, ## __VA_ARGS__)
#define LOG_TAG    "JNIMsg"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#else
#define log_print(type, tag, format, ...)
#define LOGI(...)
#endif

#endif /* COREBD_IPT_JNI_LOG */
