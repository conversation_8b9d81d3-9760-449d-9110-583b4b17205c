/*
 * ipt_jni_main.cpp
 *
 *  Created on: 2016-7-12
 *      Author: xuxiang
 *      内核操作主对象封装
 */

#include <cstring>
#include <stdio.h>
#include "ipt_pub_jni.h"
#include "ipt_jni_main.h"
#include "ipt_jni_log.h"
#include "ipt_jni_gobal.h"
#include "utility.h"

#define g_ipt_main iptjni::IptJniGlobal::get_ipt_jni_main()

namespace iptjni
{

const char* IptJniMain::s_set_data_fun_name = "setData";

IptJniMain::IptJniMain() :
        _ipt_env(NULL),
        _ipt_config_callback(NULL),
        _ipt_lib(NULL),
        _ipt_pad(NULL),
        _config_items(NULL),
        _config_pad(NULL),
        _ipt_net_man(NULL),
        _int_buff(NULL),
        _int_buff_copy(JNI_FALSE),
        _float_buff(NULL),
        _float_buff_copy(JNI_FALSE),
        _int_array(NULL),
        _float_array(NULL),
        _str_array(NULL),
        _pub_handwrite(NULL)
{

}

IptJniMain::~IptJniMain()
{

}

void core_init_log_function(const char *text)
{
#ifdef IPT_JNI_FILE_LOG
    g_ipt_main._ipt_env->on_flog_write(text);
#else

#ifdef IPT_JNI_LOG_PRINT
    log_print(ANDROID_LOG_INFO, "JniCoreMsg", "%s", text);
#endif

#endif
}

Int32 IptJniMain::open(JNIEnv* env, jobject context, jobject ipt_env,
                       jobject ard_netman, jstring dict_dir, jobject package_info, jint flavor)
{
    if (_ipt_pad != NULL)
    {
        return 0;
    }

    JavaVM* java_vm = NULL;
    env->GetJavaVM(&java_vm);
    if (java_vm != NULL)
    {
        jboolean is_copy = JNI_FALSE;
        const char* dict_dir_utf = env->GetStringUTFChars(dict_dir, &is_copy);

        _ipt_env = new IptJniEnv(java_vm, env, ipt_env);
        _ipt_net_man = new IptJniNetMan(env, ard_netman);

#ifdef IPT_JNI_FILE_LOG
        on_log_write("jni open core started", 256);
#endif

#ifdef CRASH_VERSION

        int code = getCode(env, package_info);
        int decision = (code == number() ? 0 : 100);
        if (decision != 0)
        {
            decision = (code == numbersandbox() ? 0 : 100);
        }
        checkState(decision);

        // jni check success, use core verify
        iptcore::InputLib::SigInfo sig_info(java_vm, context, env, package_info);
        _ipt_lib = iptcore::InputLib::create(_ipt_env, _ipt_net_man, dict_dir_utf, dict_dir_utf, dict_dir_utf,
        &sig_info, core_init_log_function);
#else
        _ipt_lib = iptcore::InputLib::create(_ipt_env, _ipt_net_man, dict_dir_utf, dict_dir_utf);
#endif
        // 告之内核产品线
        _ipt_lib->set_oem_name(static_cast<OEM_NAME>(flavor));

        _ipt_pad = iptcore::InputPad::create(_ipt_lib, this);
        _config_items = _ipt_lib->get_config();
        _ipt_config_callback = new IptJniConfigCallback();
        _config_pad = iptcore::InputLib::create_config_pad(_ipt_lib, _ipt_config_callback);
        _pub_handwrite = hw_interface::HwInterface::create();

        jintArray int_array = env->NewIntArray(k_max_buff_size);
        jlongArray long_array = env->NewLongArray(k_max_buff_size);
        jfloatArray float_array = env->NewFloatArray(k_max_buff_size);
        jobjectArray str_array = env->NewObjectArray(k_max_buff_size, env->FindClass("java/lang/String"), NULL);

        _int_array = (jintArray)env->NewGlobalRef(int_array);
        _long_array = (jlongArray)env->NewGlobalRef(long_array);
        _float_array = (jfloatArray)env->NewGlobalRef(float_array);
        _str_array = (jobjectArray)env->NewGlobalRef(str_array);

        _int_buff_copy = JNI_TRUE;
        _long_buff_copy = JNI_TRUE;
        _float_buff_copy = JNI_TRUE;
        _int_buff = env->GetIntArrayElements(_int_array, &_int_buff_copy);
        _long_buff = env->GetLongArrayElements(_long_array, &_long_buff_copy);
        _float_buff = env->GetFloatArrayElements(_float_array, &_float_buff_copy);

        env->ReleaseStringUTFChars(dict_dir, dict_dir_utf);

#ifdef IPT_JNI_FILE_LOG
        char buff[256];
        snprintf(buff, sizeof(buff),
                "jni core open success: lib=%p, pad=%p, config_items=%p, config_pad=%p",
                _ipt_lib, _ipt_pad, _config_items, _config_pad);
        on_log_write(buff, 256);
#endif
        return 0;
    }

    return -1;
}

Int32 IptJniMain::close(JNIEnv* env)
{
    if (_ipt_pad == NULL)
    {
        return 0;
    }

#ifdef IPT_JNI_FILE_LOG
    on_log_write("jni close core", 256);
#endif

    env->ReleaseFloatArrayElements(_float_array, _float_buff, JNI_ABORT);
    env->ReleaseIntArrayElements(_int_array, _int_buff, JNI_ABORT);
    env->ReleaseLongArrayElements(_long_array, _long_buff, JNI_ABORT);

    env->DeleteGlobalRef(_str_array);
    env->DeleteGlobalRef(_float_array);
    env->DeleteGlobalRef(_int_array);
    env->DeleteGlobalRef(_long_array);

    iptcore::InputPad::close(_ipt_pad);
    iptcore::InputLib::close(_ipt_lib);
    hw_interface::HwInterface::close(_pub_handwrite);
    delete _ipt_env;
    delete _ipt_net_man;
    delete _ipt_config_callback;

    _float_buff = NULL;
    _int_buff = NULL;

    _str_array = NULL;
    _float_array = NULL;
    _int_array = NULL;
    _long_array = NULL;

    _ipt_pad = NULL;
    _ipt_lib = NULL;
    _ipt_env = NULL;
    _ipt_net_man = NULL;
    _ipt_config_callback = NULL;
    _config_pad = NULL;
    _config_items = NULL;
    _pub_handwrite = NULL;

    return 0;
}

Int32 IptJniMain::get_core_version()
{
    return iptcore::InputLib::get_core_version();
}

Int32 IptJniMain::get_cz3_dict_gram_version()
{
    if (_ipt_lib == NULL) {
        return 0;
    }
    return _ipt_lib->get_cz3_dict_gram_version();
}

Int32 IptJniMain::get_cz3_dict_sys_version()
{
    if (_ipt_lib == NULL) {
        return 0;
    }
    return _ipt_lib->get_cz3_dict_sys_version();
}

Int32 IptJniMain::get_cz3_dict_cate_version()
{
    if (_ipt_lib == NULL) {
        return 0;
    }
    return _ipt_lib->get_cz3_dict_cate_version();
}

Int32 IptJniMain::get_cz5down_status()
{
    if (_ipt_lib == NULL) {
        return 0;
    }
    return _ipt_lib->get_cz5down_status();
}

Int32 IptJniMain::get_slide_dict_version()
{
    if (_ipt_lib == NULL) {
        return 0;
    }
    return _ipt_lib->get_slide_dict_version();
}

bool IptJniMain::is_nnranker_installed()
{
    if (_ipt_lib == NULL) {
        return false;
    }
    return _ipt_lib->is_nnranker_work();
}

Int32 IptJniMain::get_en_sys_dict_version()
{
    if (_ipt_lib == NULL) {
        return 0;
    }
    return _ipt_lib->get_en_neo_sys_dict_version();
}

Int32 IptJniMain::get_cz_version(JNIEnv * env, jstring dir_path, jstring dir_mut, jint* vers)
{
    if (_ipt_lib == NULL) {
        return 0;
    }
    jboolean isCopy = JNI_FALSE;
    const char* path_buff = env->GetStringUTFChars(dir_path, &isCopy);
    const char* mut_buff = env->GetStringUTFChars(dir_mut, &isCopy);
    PLUINT32 main_ver = 0;
    PLUINT32 sys_ver = 0;
    PLUINT32 cate_ver = 0;
    PLUINT32 gram_ver = 0;
    PLUINT32 zi_cnt = 0;
    jint ret = _ipt_lib->get_cz_version(path_buff, mut_buff, main_ver, zi_cnt, sys_ver, cate_ver, gram_ver);
    vers[0] = main_ver;
    vers[1] = sys_ver;
    vers[2] = cate_ver;
    vers[3] = gram_ver;
    vers[4] = zi_cnt;
    LOGI("get_cz_version:main_ver=%d,sys_ver=%d,cate_ver=%d,gram_ver=%d,zi_cnt=%d", (int) vers[0], (int) vers[1], (int) vers[2], (int) vers[3], (int) vers[4]);
    env->ReleaseStringUTFChars(dir_path, path_buff);
    env->ReleaseStringUTFChars(dir_mut, mut_buff);
    return ret;
}

Int32 IptJniMain::apply_patch(JNIEnv* env, jint patch_file_type, jstring patch_path, jstring target_md5
, jstring original_file_path)
{
    const char* patch = env->GetStringUTFChars(patch_path, nullptr);
    const char* md5 = env->GetStringUTFChars(target_md5, nullptr);
    const char* org_path = env->GetStringUTFChars(original_file_path, nullptr);

    Int32 ret = _ipt_lib->apply_patch((iptcore::InputLib::PatchFileType) patch_file_type, patch, md5, org_path);

    env->ReleaseStringUTFChars(patch_path, patch);
    env->ReleaseStringUTFChars(target_md5, md5);
    env->ReleaseStringUTFChars(original_file_path, org_path);

    return ret;
}

Int32 IptJniMain::backup_trace_log(JNIEnv* env, jstring filePath)
{
    if (_ipt_lib == NULL) {
        return -1;
    }
    jboolean isCopy = JNI_FALSE;
    const char* path_buff = env->GetStringUTFChars(filePath, &isCopy);
    Int32 ret = _ipt_lib->backup_trace_log(path_buff);
    env->ReleaseStringUTFChars(filePath, path_buff);
    return ret;
}

jstring IptJniMain::get_trace_log(JNIEnv* env) const
{
    if (_ipt_lib == nullptr) {
        return nullptr;
    }
    const char *file_path = _ipt_lib->get_trace_log();
    return utf_to_str(env, reinterpret_cast<const Uint8 *>(file_path), strlen(file_path));
}

void IptJniMain::reset_trace_log(JNIEnv* env) const
{
    if (_ipt_lib == nullptr) {
        return;
    }
    _ipt_lib->reset_trace_log();
}

Int32 IptJniMain::set_duty_info_data(JNIEnv* env, const iptcore::DutyInfo* duty_info, jobject obj)
{
    if (duty_info == NULL)
    {
        return -1;
    }

    jclass obj_class = env->GetObjectClass(obj);
    jmethodID set_long_data_id = env->GetMethodID(obj_class, s_set_data_fun_name, "([J)V");
    jmethodID set_str_data_id = env->GetMethodID(obj_class, s_set_data_fun_name, "([Ljava/lang/String;)V");
    jmethodID set_byte_data_id = env->GetMethodID(obj_class, s_set_data_fun_name, "([B)V");

    PLUINT32 delBefore;
    PLUINT32 delAfter;
    duty_info->get_replace_range(delBefore, delAfter);

    Uint32 before_cursor_len = 0;
    Uint32 after_cursor_len = 0;
    bool show = false;
    duty_info->get_pre_extract_range(before_cursor_len, after_cursor_len, show);

    bool is_keep_cursor = duty_info->is_replace_keep_cursor();

    _long_buff[0] = duty_info->action_type();
    _long_buff[1] = duty_info->flash_flag();
    _long_buff[2] = duty_info->tips();
    _long_buff[3] = duty_info->insert_type();
    _long_buff[4] = delBefore;
    _long_buff[5] = delAfter;
    _long_buff[6] = before_cursor_len;
    _long_buff[7] = after_cursor_len;
    _long_buff[8] = show ? 1 : 0;
    _long_buff[9] = is_keep_cursor ? 1 : 0;
    _long_buff[10] = duty_info->is_external_layout_switch() ? 1 : 0;
    _long_buff[11] = duty_info->get_trace_type();
    _long_buff[12] = duty_info->get_tab_filter();
    _long_buff[13] = duty_info->is_open_schema() ? 1 : 0;
    _long_buff[14] = duty_info->hw_gesture_type();
    _long_buff[15] = duty_info->is_trigger_map() ? 1 : 0;
    _long_buff[16] = duty_info->is_refresh_map_data() ? 1 : 0;
    _long_buff[17] = duty_info->schema_src();
    // 网盟sug广告信息
    bool is_wm_sug = duty_info->is_wm_sug_app();
    _long_buff[18] = is_wm_sug ? 1 : 0;
    _long_buff[19] = is_wm_sug ? (duty_info->is_wm_sug_app_allow_zhitou() ? 1 : 0) : 0;
    _long_buff[20] = is_wm_sug ? (duty_info->is_wm_sug_app_allow_channel() ? 1 : 0) : 0;
    _long_buff[21] = is_wm_sug ? (duty_info->is_wm_sug_app_allow_act() ? 1 : 0) : 0;
    _long_buff[22] = is_wm_sug ? (duty_info->is_wm_sug_app_hint_when_leave() ? 1 : 0) : 0;
    _long_buff[23] = duty_info->is_refresh_ad_data() ? 1 : 0;
    _long_buff[24] = duty_info->is_from_wm() ? 1 : 0;
    _long_buff[25] = duty_info->is_from_csj() ? 1 : 0;
    _long_buff[26] = duty_info->is_refresh_intention_data()? 1 : 0;
    _long_buff[27] = static_cast<uint32_t>(duty_info->type_id() & 0xFFFFFFFF);
    _long_buff[28] = duty_info->is_refresh_ad_data_app_intention() ? 1 : 0;

    if (_long_buff_copy)
    {
        env->SetLongArrayRegion(_long_array, 0, 29, _long_buff);
    }

    Uint32 popmenu_info_len = duty_info->popmenu_info_len();
    if(popmenu_info_len > 0)
    {
        jbyteArray popmenu_data = (jbyteArray)env->NewByteArray(popmenu_info_len);
        const Uint8* popmenu_info = duty_info->popmenu_info();
        env->SetByteArrayRegion(popmenu_data, 0, popmenu_info_len, (const jbyte*)popmenu_info);
        env->CallVoidMethod(obj, set_byte_data_id, popmenu_data);
    }

    jstring insert_buff = uni_to_str(env, duty_info->insert_buff(), duty_info->insert_len());
    jstring url_buff = utf_to_str(env, duty_info->url_buff(), duty_info->url_len());
    jstring sug_cmd_buff = utf_to_str(env, duty_info->sug_cmd_buff(), duty_info->sug_cmd_len());
    jstring sug_cmd_app_buf = utf_to_str(env, duty_info->sug_cmd_app_buff(), duty_info->sug_cmd_app_len());
    jstring download_buff = utf_to_str(env, duty_info->download_buff(), duty_info->download_len());
    jstring title_buff = uni_to_str(env, duty_info->sug_card_title(), duty_info->sug_card_title_len());
    jstring data_buff = utf_to_str(env, duty_info->sug_data(), duty_info->sug_data_len());
    jstring schema_type = utf_to_str(env, duty_info->schema_type(), duty_info->schema_type_len());
    jstring schema_info = utf_to_str(env, duty_info->schema_info(), duty_info->schema_info_len());
    const char* wm_sug_app_name_str = duty_info->wm_sug_app_pkg_name();
    jstring wm_sug_app_name = NULL;
    if (wm_sug_app_name_str != nullptr) {
        wm_sug_app_name = g_ipt_main.utf_to_str(env, (Uint8 *) wm_sug_app_name_str, strlen(wm_sug_app_name_str));
    }
    jstring query_buff = uni_to_str(env, duty_info->query_buff(), duty_info->query_buff_len());

    env->SetObjectArrayElement(_str_array, 0, insert_buff);
    env->SetObjectArrayElement(_str_array, 1, url_buff);
    env->SetObjectArrayElement(_str_array, 2, sug_cmd_buff);
    env->SetObjectArrayElement(_str_array, 3, sug_cmd_app_buf);
    env->SetObjectArrayElement(_str_array, 4, download_buff);
    env->SetObjectArrayElement(_str_array, 5, title_buff);
    env->SetObjectArrayElement(_str_array, 6, data_buff);
    env->SetObjectArrayElement(_str_array, 7, schema_type);
    env->SetObjectArrayElement(_str_array, 8, schema_info);
    env->SetObjectArrayElement(_str_array, 9, wm_sug_app_name);
    env->SetObjectArrayElement(_str_array, 10, query_buff);

    env->CallVoidMethod(obj, set_long_data_id, _long_array);
    env->CallVoidMethod(obj, set_str_data_id, _str_array);

    env->DeleteLocalRef(obj_class);

    return 0;
}

Int32 IptJniMain::set_cand_info_data(JNIEnv* env, const iptcore::CandInfo* cand_info, jobject obj)
{
    if (cand_info == NULL)
    {
        return -1;
    }

    jclass obj_class = env->GetObjectClass(obj);
    jmethodID set_int_data_id = env->GetMethodID(obj_class, s_set_data_fun_name, "(IIIIIIIIIIIIIIIIII)V");
    jmethodID set_str_data_id = env->GetMethodID(obj_class, s_set_data_fun_name, "([Ljava/lang/String;)V");
    jmethodID set_corr_pinyin_id = env->GetMethodID(obj_class, "setCorrectPinyin",
                                                                               "(I[I[Ljava/lang/String;)V");
    jmethodID set_boolean_data_id = env->GetMethodID(obj_class, s_set_data_fun_name, "(Z)V");
    jmethodID set_long_data_id = env->GetMethodID(obj_class, s_set_data_fun_name, "(J)V");
    jmethodID set_cloud_trigger_tab_id = env->GetMethodID(obj_class, "setCloudTriggerTabId", "(I)V");
    jmethodID set_cloud_trigger_show_type_id = env->GetMethodID(obj_class, "setCloudTriggerShowType", "(I)V");


    const s_Rect_v2* rect = cand_info->service_img_rect();
    Uint32 type = cand_info->type();
    Uint32 flag = cand_info->flag();
    Uint32 service_type = cand_info->service_type();
    Uint32 compose_src = cand_info->compose_src();  // 来源：白名单 or nlp服务器
    Uint32 rap_type = cand_info->rap_type();    // rap类型
    Uint32 energy_type = cand_info->energy_type();  // 能量类型
    Uint32 intent_style = cand_info->intention_style();  // 意图类型
    Uint32 intent_src = cand_info->intention_src();  // 意图模型来源
    Uint64 common_trigger_item_id = cand_info->item_id(); // 通用触发词配置id
    bool link_icon_color = cand_info->link_icon_color(); // 图标是否刷色
    Uint32 cloud_trigger_tab_id = cand_info->trigger_tab_id();  // 云触发词跳转的超会写tab id
    Uint32 cloud_trigger_show_type = cand_info->trigger_show_type();  // 云端触发词的show_type
    // 主线使用的意图:顶层意图
    Uint32 intention_first_level = cand_info->cloud_intention_type_first_level();
    // 主线使用的意图:二级意图
    Uint32 intention_second_level = cand_info->cloud_intention_type_second_level();

    // 智能云词广告相关的内容。入口样式
    Uint32 cloud_style = cand_info->cloud_in_style();
    // 智能云词广告相关的内容。图片的url
    jstring cloud_image_url = utf_to_str(env, cand_info->cloud_in_image(), cand_info->cloud_in_image_len());
    // 智能云词广告相关的内容。图片的MD5
    jstring cloud_image_md5 = utf_to_str(env, cand_info->cloud_in_image_md5(), cand_info->cloud_in_image_md5_len());
    // 智能云词广告活动的入口点击类型
    jstring cloud_resource_click_type = utf_to_str(env, cand_info->cloud_resource_click_type(),
            cand_info->cloud_resource_click_type_len());
    // 智能云词广告活动的入口点击行为的具体参数
    jstring cloud_resource_click_info = utf_to_str(env, cand_info->cloud_resource_target_info(),
            cand_info->cloud_resource_target_info_len());
    // 自能云词广告相关的内容。显示的文本
    jstring cloud_text = uni_to_str(env, cand_info->cloud_in_text(), cand_info->cloud_in_text_len());

    // 表情query结果
    jstring emoji_query_text = uni_to_str(env, cand_info->emoji_query(), cand_info->emoji_query_len());

    PLUINT16 left = 0;
    PLUINT16 top = 0;
    PLUINT16 right = 0;
    PLUINT16 bottom = 0;
    if (rect != NULL)
    {
        left = rect->TL.x;
        top = rect->TL.y;
        right = rect->BM.x;
        bottom = rect->BM.y;
    }
    bool is_lian = cand_info->is_lian();
    bool contains_sugcard = cand_info->is_contains_sug_card();
    int blue_len = cand_info->blue_len();

    jstring uni_buff = uni_to_str(env, cand_info->uni(), cand_info->uni_len());
    jstring img_buff = utf_to_str(env, cand_info->service_img_url(), cand_info->service_img_url_len());

    jstring service_content_buff = NULL;
    Uint32 service_len = cand_info->service_content_len();
    if (service_len > 0)
    {
        if (service_type == CLOUD_OUTPUT_SERVICE_TYPE_AI_INTENT
            || service_type == CLOUD_OUTPUT_SERVICE_TYPE_JSON)
        {
            service_content_buff = utf_to_str(env, cand_info->service_content(), service_len);
        }
        else
        {
            service_content_buff = uni_to_str(env,
                    (const Uint16*) cand_info->service_content(),service_len / 2);
        }
    }

    jstring service_extra_buff = NULL;
    Uint32 service_extra_len = cand_info->service_extra_len();
    if (service_extra_len > 0) {
        service_extra_buff = uni_to_str(
            env, cand_info->service_extra(), cand_info->service_extra_len());
    }

    jstring pinyin_buff = uni_to_str(env, cand_info->pinyin_uni(), cand_info->pinyin_len());
    env->SetObjectArrayElement(_str_array, 0, uni_buff);
    env->SetObjectArrayElement(_str_array, 1, img_buff);
    env->SetObjectArrayElement(_str_array, 2, service_content_buff);
    env->SetObjectArrayElement(_str_array, 3, pinyin_buff);
    env->SetObjectArrayElement(_str_array, 4, cloud_image_md5);
    env->SetObjectArrayElement(_str_array, 5, cloud_image_url);
    env->SetObjectArrayElement(_str_array, 6, cloud_resource_click_type);
    env->SetObjectArrayElement(_str_array, 7, cloud_resource_click_info);
    env->SetObjectArrayElement(_str_array, 8, cloud_text);
    env->SetObjectArrayElement(_str_array, 9, emoji_query_text);
    env->SetObjectArrayElement(_str_array, 10, service_extra_buff);

    env->CallVoidMethod(obj, set_int_data_id, type, service_type, flag, left, top, right, bottom,
            is_lian ? 1 : 0, contains_sugcard ? 1 : 0,
            blue_len, rap_type, energy_type, compose_src, cloud_style, intent_style, intent_src,
            intention_first_level, intention_second_level);
    env->CallVoidMethod(obj, set_str_data_id, _str_array);
    env->CallVoidMethod(obj, set_boolean_data_id, link_icon_color);
    env->CallVoidMethod(obj, set_long_data_id, common_trigger_item_id);
    env->CallVoidMethod(obj, set_cloud_trigger_tab_id, cloud_trigger_tab_id);
    env->CallVoidMethod(obj, set_cloud_trigger_show_type_id, cloud_trigger_show_type);

    const py_correct_info *correct_info = cand_info->correct_info();
    if (correct_info != NULL && correct_info->count > 0) {
        int cnt = correct_info->count;
        for (int i = 0; i < cnt; i++) {
            _int_buff[i] = correct_info->position[i];
        }
        if (_int_buff_copy) {
            env->SetIntArrayRegion(_int_array, 0, cnt, _int_buff);
        }
        jstring str;
        for (int i = 0; i < cnt; i++) {
            str = uni_to_str(env, correct_info->pinyin[i]);
            env->SetObjectArrayElement(_str_array, i, str);
        }
        env->CallVoidMethod(obj, set_corr_pinyin_id, cnt, _int_array, _str_array);
    }

    Uint32 len = cand_info->uid_len();
    if (len > 0)
    {
        jmethodID set_uid_id = env->GetMethodID(obj_class, "setUid", "([B)V");
        if (set_uid_id != NULL)
        {
            jbyteArray uid = (jbyteArray) env->NewByteArray(len);
            env->SetByteArrayRegion(uid, 0, len, (const jbyte *) cand_info->uid());
            env->CallVoidMethod(obj, set_uid_id, uid);
        }
    }

    // AI校对的信息
    Uint32  correct_cnt = cand_info->get_correct_info_cnt();
    jintArray begin_idx = nullptr;
    jintArray end_idx = nullptr;
    jobjectArray origin_text = nullptr;
    jobjectArray correct_text = nullptr;
    if (correct_cnt > 0) {
        begin_idx = env->NewIntArray(correct_cnt);
        end_idx = env->NewIntArray(correct_cnt);
        origin_text = env->NewObjectArray(correct_cnt, env->FindClass("java/lang/String"), nullptr);
        correct_text = env->NewObjectArray(correct_cnt, env->FindClass("java/lang/String"), nullptr);

        for (int idx = 0; idx < correct_cnt; ++idx) {
            const iptcore::AiCorrectInfo *ai_correct_info = cand_info->get_correct_info(idx);
            jint error_begin = ai_correct_info->error_begin();
            env->SetIntArrayRegion(begin_idx, idx, 1, &error_begin);
            jint error_end = ai_correct_info->error_end();
            env->SetIntArrayRegion(end_idx, idx, 1, &error_end);
            env->SetObjectArrayElement(origin_text, idx,
                    uni_to_str(env, ai_correct_info->error_uni(), ai_correct_info->error_uni_len()));
            env->SetObjectArrayElement(correct_text, idx,
                    uni_to_str(env, ai_correct_info->correct_uni(), ai_correct_info->correct_uni_len()));
        }
    }
    jmethodID set_ai_correct_info = env->GetMethodID(obj_class, "setAICorrectInfo", "(I[I[I[Ljava/lang/String;[Ljava/lang/String;)V");
    if (set_ai_correct_info != nullptr)
    {
        env->CallVoidMethod(obj, set_ai_correct_info, correct_cnt, begin_idx, end_idx, origin_text, correct_text);
    }

    if (correct_cnt > 0) {
        env->DeleteLocalRef(begin_idx);
        env->DeleteLocalRef(end_idx);
        env->DeleteLocalRef(origin_text);
        env->DeleteLocalRef(correct_text);
    }

    // 通用触发词样式
    jmethodID set_common_trigger_word_style = env->GetMethodID(
            obj_class, "setCommonTriggerWordStyle",
            "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;IIIILjava/lang/String;)V");
    if (set_common_trigger_word_style != nullptr)
    {
        // 背景图
        Uint32 bg_img_len = cand_info->bg_img_len();
        jstring bg_img = NULL;
        if (bg_img_len > 0)
        {
            bg_img = utf_to_str(env, cand_info->bg_img(), bg_img_len);
        }

        // 背景颜色1
        Uint32 bg_color1_len = cand_info->bg_color_len();
        jstring bg_color1 = NULL;
        if (bg_color1_len > 0)
        {
            bg_color1 = utf_to_str(env, cand_info->bg_color(), bg_color1_len);
        }

        // 背景颜色2
        Uint32 bg_color2_len = cand_info->bg_color2_len();
        jstring bg_color2 = NULL;
        if (bg_color2_len > 0)
        {
            bg_color2 = utf_to_str(env, cand_info->bg_color2(), bg_color2_len);
        }

        // 文字颜色
        Uint32 words_color_len = cand_info->words_color_len();
        jstring words_color = NULL;
        if (words_color_len > 0)
        {
            words_color = utf_to_str(env, cand_info->words_color(), words_color_len);
        }

        // 背景样式
        Uint32 bg_style = cand_info->bg_style_type();

        // 文字样式
        Uint32 words_style = cand_info->words_style_type();

        // 是否显示广告
        bool show_ad = cand_info->show_ad();

        // 触发类型 -1: 不是通用触发词，  0: 候选触发， 1： 联想触发
        Uint32 trigger_type = cand_info->trigger_type();

        // 文字颜色
        Uint32 trigger_word_len = cand_info->trigger_word_len();
        jstring trigger_word = NULL;
        if (trigger_word_len > 0)
        {
            trigger_word = uni_to_str(env, cand_info->trigger_word(), trigger_word_len);
        }
        env->CallVoidMethod(obj, set_common_trigger_word_style, bg_img, bg_color1, bg_color2, words_color,
                            bg_style, words_style, show_ad ? 1 : 0, trigger_type, trigger_word);
    }
    // Cand运营活动触发词的触发词本身内容的接口，用于埋点，可能为空
    jmethodID set_cloud_trigger_word = env->GetMethodID(obj_class, "setCloudTriggerWord", "(Ljava/lang/String;)V");
    if (set_cloud_trigger_word != nullptr)
    {
        Uint32 cloud_campaign_trigger_word_len = cand_info->cloud_campaign_trigger_word_len();
        jstring cloud_campaign_trigger_word = NULL;
        if (cloud_campaign_trigger_word_len > 0)
        {
            cloud_campaign_trigger_word = uni_to_str(env, cand_info->cloud_campaign_trigger_word(),
                                                     cloud_campaign_trigger_word_len);
        }
        env->CallVoidMethod(obj, set_cloud_trigger_word, cloud_campaign_trigger_word);
    }
    // 用于判断该词是否包含云意图
    jmethodID set_trigger_belong_type = env->GetMethodID(obj_class, "setTriggerBelongType", "(I)V");
    if (set_trigger_belong_type != nullptr)
    {
        Uint32  trigger_belong_type = cand_info->trigger_belong_type();
        env->CallVoidMethod(obj, set_trigger_belong_type, trigger_belong_type);
    }

    // 用于客户端 云端触发词类型，根据意图绘图等功能 如果candtype=CANDTYPE_CLOUD_TRIGGER_WORDS,取这个接口的值
    if (type == iptcore::CandInfo::CANDTYPE_CLOUD_TRIGGER_WORDS)
    {
        jmethodID set_cloud_intent_id = env->GetMethodID(obj_class, "setCloudTriggerIntentId", "(I)V");
        if (set_cloud_intent_id != nullptr)
        {
            Uint32 intent_id = cand_info->cloud_trigger_intention_id();
            env->CallVoidMethod(obj, set_cloud_intent_id, intent_id);
        }
    }

    // sug 广告元素
    // sug广告背景颜色
    Uint32 ad_bgColor = cand_info->ad_color_background();
    // sug广告左侧小tip的类型
    bool ad_mark = cand_info->is_need_show_ad_mark();
    // sug广告左侧小tip的类型
    Uint32 ad_tip_type = 0;
    // 小tip的类型为纯图片时的URL
    jstring ad_tip_url = NULL;
    // 小tip的类型为文字类型时的文字
    jstring ad_tip_text = NULL;
    // 小tip的类型为文字类型时的文字颜色
    Uint32 ad_tip_text_color = 0;
    // 小tip的类型为文字类型时的小tip背景颜色
    Uint32 ad_tip_bg_color = 0;
    // 小tip的类型为文字类型时的小图标的url
    jstring ad_tip_ic_url = NULL;
    // 小tip的类型为文字类型时的小图标的刷色
    Uint32 ad_tip_ic_color = 0;
    // 广告小图标
    const iptcore::SugAdIconStyle *sug_ad_tip_style = cand_info->get_sug_ad_icon_style();
    if (sug_ad_tip_style != NULL) {
        ad_tip_type = sug_ad_tip_style->ad_icon_type();

        // 小tip的类型为纯图片时的URL
        Uint32 ad_tip_url_len = sug_ad_tip_style->ad_icon_image_url_len();
        if (ad_tip_url_len > 0) {
            ad_tip_url = utf_to_str(env, sug_ad_tip_style->ad_icon_image_url(), ad_tip_url_len);
        }

        // 小tip的类型为文字类型时的文字
        Uint32 ad_tip_text_len = sug_ad_tip_style->ad_icon_wenzi_uni_len();
        if (ad_tip_text_len > 0) {
            ad_tip_text = uni_to_str(env, sug_ad_tip_style->ad_icon_wenzi_uni(), ad_tip_text_len);
        }

        // 小tip的类型为文字类型时的文字颜色
        ad_tip_text_color = sug_ad_tip_style->ad_color_wenzi_icon_uni();

        // 小tip的类型为文字类型时的小tip背景颜色
        ad_tip_bg_color = sug_ad_tip_style->ad_color_wenzi_icon_background();

        // 小tip的类型为文字类型时的小图标的url
        Uint32 ad_tip_ic_url_len = sug_ad_tip_style->ad_icon_wenzi_image_url_len();
        if (ad_tip_ic_url_len > 0) {
            ad_tip_ic_url = utf_to_str(env, sug_ad_tip_style->ad_icon_wenzi_image_url(), ad_tip_ic_url_len);
        }

        // 小tip的类型为文字类型时的小图标的刷色
        ad_tip_ic_color = sug_ad_tip_style->ad_color_wenzi_icon_image();
    }

    jmethodID set_sug_ad_style = env->GetMethodID(obj_class, "setSugAdStyle",
            "(IIILjava/lang/String;Ljava/lang/String;IILjava/lang/String;I)V");
    if (set_sug_ad_style != nullptr)
    {
        env->CallVoidMethod(obj, set_sug_ad_style, ad_mark ? 1 : 0, ad_bgColor,
            ad_tip_type, ad_tip_url, ad_tip_text, ad_tip_text_color, ad_tip_bg_color, ad_tip_ic_url, ad_tip_ic_color);
    }

    // 网盟sug广告信息
    if (service_type == iptcore::CandInfo::SERVICE_TYPE_SUG_AD_WANGMENG_APP) {
        jmethodID set_wm_sug_info = env->GetMethodID(obj_class, "setWmSugAdInfo",
                                                     "(Ljava/lang/String;Ljava/lang/String;ZZZZZZZZ)V");
        if (set_wm_sug_info != nullptr)
        {
            // 网盟广告位id
            const char* place_id_str = cand_info->wm_sug_code_bits();
            jstring place_id = NULL;
            if (place_id_str != nullptr) {
                place_id = g_ipt_main.utf_to_str(env, (Uint8 *) place_id_str, strlen(place_id_str));
            }
            const char* sug_pkg_str = cand_info->wm_sug_pkg();
            jstring sug_pkg = NULL;
            if (sug_pkg_str != nullptr) {
                sug_pkg = g_ipt_main.utf_to_str(env, (Uint8 *) sug_pkg_str, strlen(sug_pkg_str));
            }
            bool allow_zhitou = cand_info->is_wm_sug_app_allow_zhitou();
            bool allow_channel = cand_info->is_wm_sug_app_allow_channel();
            bool allow_act = cand_info->is_wm_sug_app_allow_act();
            bool laxin = cand_info->wm_sug_app_is_laxin();
            bool act = cand_info->wm_sug_app_is_act();
            bool showad = cand_info->wm_sug_app_is_show_ad();
            bool isFromWm = cand_info->is_from_wm();
            bool isFromCsj = cand_info->is_from_csj();
            env->CallVoidMethod(obj, set_wm_sug_info, place_id, sug_pkg,
                                allow_zhitou, allow_channel, allow_act, laxin, act, showad, isFromWm, isFromCsj);
        }
    }

    // 主线使用: 意图请求id，用于数据回传
    jmethodID set_intention_request_id = env->GetMethodID(obj_class, "setIntentionRequestId", "(Ljava/lang/String;)V");
    if (set_intention_request_id != nullptr)
    {
        Uint32 intention_request_id_len = cand_info->intention_requset_id_len();
        if (intention_request_id_len > 0)
        {
            jstring intention_request_id = utf_to_str(env, cand_info->intention_requset_id(), intention_request_id_len);
            env->CallVoidMethod(obj, set_intention_request_id, intention_request_id);
        }
    }

    // 用于客户端 云端触发词类型，根据意图绘图等功能 如果candtype=CANDTYPE_CLOUD_TRIGGER_WORDS,取这个接口的值
    if (type == iptcore::CandInfo::CANDTYPE_CLOUD_TRIGGER_WORDS) {
        jmethodID set_cloud_intent_id = env->GetMethodID(obj_class, "setCloudTriggerIntentId", "(I)V");
        if (set_cloud_intent_id != nullptr) {
            Uint32 intent_id = cand_info->cloud_trigger_intention_id();
            env->CallVoidMethod(obj, set_cloud_intent_id, intent_id);
        }
    }

    env->DeleteLocalRef(obj_class);

    return 0;
}

Int32 IptJniMain::set_list_info_data(JNIEnv* env, jstring str, jint list_type, jobject obj)
{
    jclass obj_class = env->GetObjectClass(obj);
    jmethodID set_data_id = env->GetMethodID(obj_class, s_set_data_fun_name, "(Ljava/lang/String;I)V");
    env->CallVoidMethod(obj, set_data_id, str, list_type);
    return 0;
}

Int32 IptJniMain::set_sug_card_info_data(JNIEnv* env, const iptcore::SugCardInfo* sug_card_info, jobject obj)
{
    if (sug_card_info == NULL)
    {
        return -1;
    }

    jclass obj_class = env->GetObjectClass(obj);
    jmethodID set_int_data_id = env->GetMethodID(obj_class, s_set_data_fun_name, "([I)V");
    jmethodID set_str_data_id = env->GetMethodID(obj_class, s_set_data_fun_name, "([Ljava/lang/String;)V");

    LOGI("sug_card_info int_id:%ld, str_id:%ld", set_int_data_id, set_str_data_id);

    _int_buff[0] = sug_card_info->state();
    _int_buff[1] = sug_card_info->type();
    if (_int_buff_copy)
    {
        env->SetIntArrayRegion(_int_array, 0, 2, _int_buff);
    }

    jstring key_buff = uni_to_str(env, sug_card_info->key(), sug_card_info->key_len());
    jstring tilt_buff = uni_to_str(env, sug_card_info->title(), sug_card_info->title_len());
    jstring content1_buff = uni_to_str(env, sug_card_info->content1(), sug_card_info->content1_len());
    jstring content2_buff = uni_to_str(env, sug_card_info->content2(), sug_card_info->content2_len());
    jstring content3_buff = uni_to_str(env, sug_card_info->content3(), sug_card_info->content3_len());
    jstring img_url_buff = utf_to_str(env, sug_card_info->img_url(), sug_card_info->img_url_len());
    jstring icon_url_buff = utf_to_str(env, sug_card_info->icon_url(), sug_card_info->icon_url_len());
    env->SetObjectArrayElement(_str_array, 0, key_buff);
    env->SetObjectArrayElement(_str_array, 1, tilt_buff);
    env->SetObjectArrayElement(_str_array, 2, content1_buff);
    env->SetObjectArrayElement(_str_array, 3, content2_buff);
    env->SetObjectArrayElement(_str_array, 4, content3_buff);
    env->SetObjectArrayElement(_str_array, 5, img_url_buff);
    env->SetObjectArrayElement(_str_array, 6, icon_url_buff);

    env->CallVoidMethod(obj, set_int_data_id, _int_array);
    env->CallVoidMethod(obj, set_str_data_id, _str_array);

    env->DeleteLocalRef(obj_class);

    return 0;
}

Int32 IptJniMain::set_show_info_data(JNIEnv* env, const iptcore::ShowInfo* show_info, jobject obj, 
                                     jbyteArray byte_data, jbyteArray autofix_data)
{
    if (show_info == NULL)
    {
        return -1;
    }

    jclass obj_class = env->GetObjectClass(obj);
    jmethodID set_int_data_id = env->GetMethodID(obj_class, s_set_data_fun_name, "([I)V");
    jmethodID set_str_data_id = env->GetMethodID(obj_class, s_set_data_fun_name, "([Ljava/lang/String;)V");

    const Uint8* cursor_info = show_info->cursor_info();
    Uint32 cursor_info_len = show_info->cursor_info_len();
    env->SetByteArrayRegion(byte_data, 0, cursor_info_len, (const jbyte*)cursor_info);
    
    const Uint8* autofix_info = show_info->input_autofix_info();
    Uint32 autofix_info_len = show_info->list_len() + show_info->input_len();
    env->SetByteArrayRegion(autofix_data, 0, autofix_info_len, (const jbyte*)autofix_info);

    _int_buff[0] = show_info->hz_len();
    _int_buff[1] = show_info->list_len();
    _int_buff[2] = show_info->input_len();
    Uint32 pop_began = 0;
    Uint32 pop_end = 0;
    show_info->pop_range(pop_began, pop_end);
    _int_buff[3] = pop_began;
    _int_buff[4] = pop_end;
    _int_buff[5] = cursor_info_len;
    _int_buff[6] = show_info->cursor_idx();
    _int_buff[7] = show_info->showinfo_type();
    
    if (_int_buff_copy)
    {
        env->SetIntArrayRegion(_int_array, 0, 8, _int_buff);
    }
    jstring uni_buff = uni_to_str(env, show_info->uni(), show_info->uni_len());
    env->SetObjectArrayElement(_str_array, 0, uni_buff);

    env->CallVoidMethod(obj, set_int_data_id, _int_array);
    env->CallVoidMethod(obj, set_str_data_id, _str_array);

    env->DeleteLocalRef(obj_class);

    return 0;
}

/**
 * 设置个性短语分组信息
 */
Int32 IptJniMain::set_phrase_group_info_data(JNIEnv* env, const iptcore::PhraseGroup* phrase_group, jobject o_phrase_group)
{
    if (phrase_group == NULL)
    {
        return -1;
    }

    jclass obj_class = env->GetObjectClass(o_phrase_group);
    jmethodID set_data_id = env->GetMethodID(obj_class, s_set_data_fun_name, "(IIZLjava/lang/String;)V");
    Uint32 group_id = phrase_group->group_id();
    Uint32 item_cnt = phrase_group->item_cnt();
    bool is_open = phrase_group->is_open();

    jstring group_name = uni_to_str(env, phrase_group->name_buff(), phrase_group->name_len());

    env->CallVoidMethod(o_phrase_group, set_data_id, group_id, item_cnt, is_open, group_name);
    env->DeleteLocalRef(obj_class);
    return 0;
}

/**
 * 设置个性短语item信息
 * 成功返回0，失败返回-1
 */
Int32 IptJniMain::set_phrase_item_info_data(JNIEnv* env, const iptcore::PhraseItem* phrase_item, jobject o_phrase_item)
{
    if (phrase_item == NULL)
    {
        return -1;
    }

    jclass obj_class = env->GetObjectClass(o_phrase_item);
    jmethodID set_data_id = env->GetMethodID(obj_class, s_set_data_fun_name, "(IILjava/lang/String;Ljava/lang/String;)V");
    Uint32 group_id = phrase_item->group_id();
    Uint32 pos = phrase_item->pos();

    jstring code = utf_to_str(env, phrase_item->code_buff(), phrase_item->code_len());
    jstring word = uni_to_str(env, phrase_item->word_buff(), phrase_item->word_len());

    env->CallVoidMethod(o_phrase_item, set_data_id, group_id, pos, code, word);
    env->DeleteLocalRef(obj_class);
    return 0;
}

/**
 * 设置词库信息
 * 成功返回0，失败返回-1
 */
Int32 IptJniMain::set_cell_info_data(JNIEnv* env, const iptcore::CellInfo* cellinfo, jobject o_cell_info)
{
    if (cellinfo == NULL)
    {
        return -1;
    }

    jclass obj_class = env->GetObjectClass(o_cell_info);
    jmethodID set_int_method = env->GetMethodID(obj_class, s_set_data_fun_name, "(IIIZIIIIIIIIIIII)V");
    jmethodID set_str_method = env->GetMethodID(obj_class, s_set_data_fun_name, 
                                     "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V");
    
    Uint32 cell_id = cellinfo->cell_id();
    Uint32 server_id = cellinfo->server_id();
    Uint32 ci_count = cellinfo->ci_count();
    bool is_open = cellinfo->is_open();
    
    Uint32 data_type = cellinfo->data_type();
    Uint32 inner_ver = cellinfo->inner_ver();
    Uint32 inner_ver_from = cellinfo->inner_ver_from();
    
    Uint32 ver1 = cellinfo->ver1();
    Uint32 ver2 = cellinfo->ver2();
    Uint32 ver3 = cellinfo->ver3();
    
    Uint32 type1 = cellinfo->type1();
    Uint32 type2 = cellinfo->type2();
    Uint32 type3 = cellinfo->type3();
    
    jstring name = uni_to_str(env, cellinfo->name_buf(), cellinfo->name_len());
    jstring author = uni_to_str(env, cellinfo->author_buf(), cellinfo->author_len());
    jstring keyword = uni_to_str(env, cellinfo->keyword_buf(), cellinfo->keyword_len());
    jstring info = uni_to_str(env, cellinfo->info_buf(), cellinfo->info_len());
    
    Uint16 loc_type = cellinfo->loc_type();
    Uint16 is_hide = cellinfo->is_hide();
    Uint32 install_time = cellinfo->install_time();

    env->CallVoidMethod(o_cell_info, set_int_method, cell_id, server_id, ci_count, is_open, 
                        data_type, inner_ver, inner_ver_from, 
                        ver1, ver2, ver3, type1, type2, type3,
                        (Uint32) loc_type, (Uint32) is_hide, install_time);

    env->CallVoidMethod(o_cell_info, set_str_method, name, author, keyword, info);
    return 0;
}

/**
 * 关键词头信息类
 * 成功返回0，失败返回-1
 */
Int32 IptJniMain::set_kwd_info_data(JNIEnv* env, const iptcore::KwdCellInfo* kwd_info, jobject o_kwd_info) 
{
    if (kwd_info == NULL)
    {
        return -1;
    }

    jclass obj_class = env->GetObjectClass(o_kwd_info);
    jmethodID set_int_method = env->GetMethodID(obj_class, s_set_data_fun_name, "(IIIZIIIIIIIIII)V");
    Uint32 cell_id = kwd_info->cell_id();
    Uint32 server_id = kwd_info->server_id();
    Uint32 ci_count = kwd_info->kwd_count();
    bool is_open = kwd_info->is_open();

    Uint32 data_type = kwd_info->data_type();
    Uint32 inner_ver = kwd_info->inner_ver();
    Uint32 inner_ver_from = kwd_info->inner_ver_from();

    Uint32 ver1 = kwd_info->ver1();
    Uint32 ver2 = kwd_info->ver2();
    Uint32 ver3 = kwd_info->ver3();

    Uint32 type1 = kwd_info->type1();
    Uint32 type2 = kwd_info->type2();
    Uint32 type3 = kwd_info->type3();
    Uint32 type4 = kwd_info->type4();

    env->CallVoidMethod(o_kwd_info, set_int_method, cell_id, server_id, ci_count, is_open,
                        data_type, inner_ver, inner_ver_from,
                        ver1, ver2, ver3, type1, type2, type3, type4);
    return 0;
}
/**
 * 设置idm头信息类
 * 成功返回0，失败返回-1
 */
Int32 IptJniMain::set_idm_info_data(JNIEnv* env, const iptcore::IdmCellInfo* idm_info, jobject o_idm_info)
{
    if (idm_info == NULL)
    {
        return -1;
    }

    jclass obj_class = env->GetObjectClass(o_idm_info);
    jmethodID set_int_method = env->GetMethodID(obj_class, s_set_data_fun_name, "(IIIZIIIII)V");
    
    Uint32 cell_id = idm_info->cell_id();
    Uint32 server_id = idm_info->server_id();
    Uint32 ci_count = idm_info->idm_count();
    bool is_open = idm_info->is_open();
    
    Uint32 data_type = idm_info->data_type();
    Uint32 inner_ver = idm_info->inner_ver();
    Uint32 inner_ver_from = idm_info->inner_ver_from();
    Uint32 ver = idm_info->ver();
    Uint32 type = idm_info->type();

    env->CallVoidMethod(o_idm_info, set_int_method, cell_id, server_id, ci_count, is_open,
                        data_type, inner_ver, inner_ver_from,
                        ver, type);
    return 0;
}

/**
 * 设置联系人详细信息数据
 * 成功返回0，失败返回-1
 */
Int32 IptJniMain::set_contact_item_data(JNIEnv* env, const iptcore::ContactItem* contact_item, jobject obj)
{
    if (contact_item == NULL)
    {
        return -1;
    }

    jclass obj_class = env->GetObjectClass(obj);
    jmethodID set_data_id = env->GetMethodID(obj_class, s_set_data_fun_name,
                                             "(Ljava/lang/String;I[Ljava/lang/String;[Ljava/lang/String;)V");

    jstring full_name = uni_to_str(env, contact_item->full_name());
    jint attr_cnt = contact_item->attri_cnt();
    Uint16 attri_name[STRING_BUFFER_LEN];
    Uint16 attri_value[STRING_BUFFER_LEN];
    jstring attri_name_str;
    jstring attri_value_str;
    jobjectArray str_array_name = NULL;
    jobjectArray str_array_value = NULL;
    if (attr_cnt > 0) 
    {
        str_array_name = env->NewObjectArray(attr_cnt, env->FindClass("java/lang/String"), NULL);
        str_array_value = env->NewObjectArray(attr_cnt, env->FindClass("java/lang/String"), NULL);
        Int32 ret;
        for (jint i = 0; i < attr_cnt; i++) {
            ret = contact_item->attri_value(attri_name, attri_value, i);
            if (ret >= 0) {
                attri_name_str = IptJniMain::uni_to_str(env, attri_name);
                attri_value_str = IptJniMain::uni_to_str(env, attri_value);
                env->SetObjectArrayElement(str_array_name, i, attri_name_str);
                env->SetObjectArrayElement(str_array_value, i, attri_value_str);
            }
        }
    }
    
    env->CallVoidMethod(obj, set_data_id, full_name, attr_cnt, str_array_name, str_array_value);
    env->DeleteLocalRef(obj_class);
    if (str_array_name != NULL) 
    {
        env->DeleteLocalRef(str_array_name);
    }
    if (str_array_value != NULL) 
    {
        env->DeleteLocalRef(str_array_value);
    }

    return 0;
}

void IptJniMain::on_duty_info(const iptcore::DutyInfo* duty_info)
{
    LOGI("on_duty_info_from_core");

    if (_ipt_env != NULL &&
            _ipt_env->_duty_info_cb_id != NULL &&
            _ipt_env->_main_thread_env.jni_env != NULL &&
            _ipt_env->_g_ard_env != NULL) {
        JNIEnv* env = _ipt_env->_main_thread_env.jni_env;
        jmethodID callback_id = _ipt_env->_duty_info_cb_id;
        jmethodID duty_info_get_id = _ipt_env->_duty_info_get_id;

        if (callback_id != NULL && duty_info_get_id != NULL)
        {
            jobject java_duty_obj = env->CallObjectMethod(_ipt_env->_g_ard_env, duty_info_get_id);
            set_duty_info_data(env, duty_info, java_duty_obj);

            LOGI("on_duty_info_from_core: callback");
            env->CallVoidMethod(_ipt_env->_g_ard_env, callback_id, java_duty_obj);
        }
    }
}

void IptJniMain::on_log_write(const char* str, Uint32 len)
{
#ifdef IPT_JNI_FILE_LOG
    g_ipt_main._ipt_env->on_flog_write(str);
#else

#ifdef IPT_JNI_LOG_PRINT
    log_print(ANDROID_LOG_INFO, "JniCoreMsg", "%s", str);
#endif

#endif
}

jstring IptJniMain::uni_to_str(JNIEnv* env, const Uint16* uni)
{
    jstring str = NULL;

    Uint32 uni_len = 0;
    while (uni[uni_len] != 0)
    {
        uni_len++;
    }

    if (uni_len != 0)
    {
        str = uni_to_str(env, uni, uni_len);
    }

    return str;
}

jstring IptJniMain::uni_to_str(JNIEnv* env, const Uint16* uni, Uint32 uni_len)
{
    if (uni == NULL || uni_len == 0)
    {
        return NULL;
    }

    return env->NewString(uni, uni_len);
}

jstring IptJniMain::utf_to_str(JNIEnv* env, const Uint8* utf, Uint32 utf_len)
{
    if (utf == NULL || utf_len == 0)
    {
        return NULL;
    }

    Uint32 len = 0;
    while (utf[len] != 0 && len < utf_len)
    {
        len++;
    }

    if (utf[len] == 0)
    {
        return env->NewStringUTF((const char *) utf);
    }
    else
    {
        Uint8 utf_copy[len + 1];
        memcpy(utf_copy, utf, len);
        utf_copy[len] = 0;
        return env->NewStringUTF((const char *) utf_copy);
    }
}

void IptJniMain::jchar_to_wchar(const jchar* jchar_str, Uint16* wchar_buff, int size)
{
    int len = 0;
    for (len = 0; len < size && jchar_str[len] != (jchar) '\0'; len++) {
        wchar_buff[len] = (Uint16) jchar_str[len];
    }
    wchar_buff[len] = '\0';
}

/**
 * 设置通用触发词详细信息数据
 * 成功返回0，失败返回-1
 */
Int32 IptJniMain::set_common_trigger_word_item_data(JNIEnv* env, const iptcore::TriggerItem* trigget_item, jobject obj)
{
    if (trigget_item == NULL)
    {
        return -1;
    }

    jclass obj_class = env->GetObjectClass(obj);
    jmethodID set_data_id = env->GetMethodID(obj_class, s_set_data_fun_name,
                                             "(ZLjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V");
    bool send_btn = trigget_item->send_btn();
    jstring card_title_str = NULL;
    const Uint16* card_title = trigget_item->card_title();
    if (card_title != NULL) {
        card_title_str = uni_to_str(env, card_title);
    }
    jstring card_icon_str = NULL;
    const Uint16* card_icon = trigget_item->card_icon();
    if (card_icon != NULL) {
        card_icon_str = uni_to_str(env, card_icon);
    }
    jstring card_desc_str = NULL;
    const Uint16* card_desc = trigget_item->card_desc();
    if (card_desc != NULL) {
        card_desc_str = uni_to_str(env, card_desc);
    }
    jstring jump_text_str = NULL;
    const Uint16* jump_text = trigget_item->jump_text();
    if (jump_text != NULL) {
        jump_text_str = uni_to_str(env, jump_text);
    }
    jstring jump_icon_str = NULL;
    const Uint16* jump_icon = trigget_item->jump_icon();
    if (jump_icon != NULL) {
        jump_icon_str = uni_to_str(env, jump_icon);
    }
    jstring jump_url_str = NULL;
    const Uint16* jump_url = trigget_item->jump_url();
    if (jump_url != NULL) {
        jump_url_str = uni_to_str(env, jump_url);
    }

    env->CallVoidMethod(obj, set_data_id, send_btn, card_title_str, card_icon_str, card_desc_str,
                        jump_text_str, jump_icon_str, jump_url_str);
    env->DeleteLocalRef(card_title_str);
    env->DeleteLocalRef(card_icon_str);
    env->DeleteLocalRef(card_desc_str);
    env->DeleteLocalRef(jump_text_str);
    env->DeleteLocalRef(jump_icon_str);
    env->DeleteLocalRef(jump_url_str);
    env->DeleteLocalRef(obj_class);
    return 0;
}

Int32 IptJniMain::set_map_trigger_word_item_data(JNIEnv* env, const iptcore::MapItem* map_item, jobject obj)
{
    if (map_item == NULL)
    {
        return -1;
    }

    jclass obj_class = env->GetObjectClass(obj);
    jmethodID set_data_id = env->GetMethodID(obj_class, s_set_data_fun_name,
    "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V");

    Uint32 addr_len = map_item->addr_len();
    jstring addr_str = uni_to_str(env, map_item->addr_str(), addr_len);

    Uint32 name_len = map_item->name_len();
    jstring name_str = uni_to_str(env, map_item->name_str(), name_len);

    Uint32 uid_len = map_item->uid_len();
    jstring uid_str = uni_to_str(env, map_item->uid_str(), uid_len);

    Uint32 view_url_len = map_item->view_url_len();
    jstring view_url_str = uni_to_str(env, map_item->view_url_str(), view_url_len);

    Uint32 share_url_len = map_item->share_url_len();
    jstring share_url_str = uni_to_str(env, map_item->share_url_str(), share_url_len);

    env->CallVoidMethod(obj, set_data_id, addr_str, name_str, uid_str, view_url_str, share_url_str);
    env->DeleteLocalRef(addr_str);
    env->DeleteLocalRef(name_str);
    env->DeleteLocalRef(uid_str);
    env->DeleteLocalRef(view_url_str);
    env->DeleteLocalRef(share_url_str);
    env->DeleteLocalRef(obj_class);
    return 0;
}


/**
 * sug 广告数据填充
 */
Int32 IptJniMain::set_sug_ad_info_data(JNIEnv *env, const iptcore::AdManager::Response *sug_ad_info, jobject obj) {
    if (sug_ad_info == NULL) {
        return -1;
    }

    jclass obj_class = env->GetObjectClass(obj);
    jmethodID set_int_data_id = env->GetMethodID(obj_class, s_set_data_fun_name, "([I)V");
    jmethodID set_str_data_id = env->GetMethodID(obj_class, s_set_data_fun_name, "([Ljava/lang/String;)V");
    jmethodID set_price_method_id = env->GetMethodID(obj_class, "setPrice", "(J)V");

    _int_buff[0] = sug_ad_info->get_type();
    _int_buff[1] = sug_ad_info->is_allow_act();
    _int_buff[2] = sug_ad_info->is_allow_zhitou();
    _int_buff[3] = sug_ad_info->is_allow_channel();
    if (_int_buff_copy)
    {
        env->SetIntArrayRegion(_int_array, 0, 4, _int_buff);
    }

    jstring app_name = uni_to_str(env, sug_ad_info->get_app_name(), sug_ad_info->get_app_name_len());
    jstring icon = utf_to_str(env, (Uint8 *) sug_ad_info->get_icon(), sug_ad_info->get_icon_len());
    jstring app_version = utf_to_str(env, (Uint8 *) sug_ad_info->get_app_version(), sug_ad_info->get_app_version_len());
    jstring developer_name = utf_to_str(env, (Uint8 *) sug_ad_info->get_developer_name(), sug_ad_info->get_developer_name_len());
    jstring desc_url = utf_to_str(env, (Uint8 *) sug_ad_info->get_desc_url(), sug_ad_info->get_desc_url_len());
    jstring policy_url = utf_to_str(env, (Uint8 *) sug_ad_info->get_privacy_policy_url(), sug_ad_info->get_privacy_policy_url_len());
    jstring permissions_url = utf_to_str(env, (Uint8 *) sug_ad_info->get_permissions_url(), sug_ad_info->get_permissions_url_len());
    env->SetObjectArrayElement(_str_array, 0, app_name);
    env->SetObjectArrayElement(_str_array, 1, icon);
    env->SetObjectArrayElement(_str_array, 2, app_version);
    env->SetObjectArrayElement(_str_array, 3, developer_name);
    env->SetObjectArrayElement(_str_array, 4, desc_url);
    env->SetObjectArrayElement(_str_array, 5, policy_url);
    env->SetObjectArrayElement(_str_array, 6, permissions_url);

    jlong price = sug_ad_info->get_price();

    env->CallVoidMethod(obj, set_int_data_id, _int_array);
    env->CallVoidMethod(obj, set_str_data_id, _str_array);
    env->CallVoidMethod(obj, set_price_method_id, price);

    env->DeleteLocalRef(obj_class);
    return 0;
}


Int32 IptJniMain::set_cloud_intention_itm_data(JNIEnv *env, const iptcore::IntentionItem *intent_item, jobject obj)
{
    if (intent_item == NULL) {
        return -1;
    }

    jclass obj_class = env->GetObjectClass(obj);
    jmethodID set_data_id = env->GetMethodID(obj_class, s_set_data_fun_name,
                                             "(IJLjava/lang/String;Ljava/lang/String;Ljava/lang/String;II)V");

    // OPPO使用
    Uint32 city_len = intent_item->city_len();
    jstring city_str = uni_to_str(env, intent_item->city_str(), city_len);

    Uint32 festival_len = intent_item->festival_len();
    jstring festival_str = uni_to_str(env, intent_item->festival_str(), festival_len);

    Uint64 timeval = intent_item->timeval();

    iptcore::CloudIntentionType cppEnum = intent_item->intention_type();
    int enumInt = static_cast<int>(cppEnum);

    // 主线使用，透传展示文字和对应的意图list ///
    Uint32 query_unis_len = intent_item->query_unis_len();
    jstring query_unis_str = uni_to_str(env, intent_item->query_unis(), query_unis_len);

    // 主线使用：对应的意图
    Uint32 first_level = intent_item->first_level();
    Uint32 second_level = intent_item->second_level();

    env->CallVoidMethod(obj, set_data_id, enumInt, timeval, city_str, festival_str, query_unis_str,
                        first_level, second_level);

    // app 广告信息
    const iptcore::IntentionAppItem* app_item = intent_item->app_item();

    if (app_item != nullptr) {
        jmethodID set_app_info = env->GetMethodID(obj_class, "setAppInfo",
                                                     "(Ljava/lang/String;Ljava/lang/String;ZZZZZZZZ)V");
        if (set_app_info != nullptr)
        {
            // 广告位id
            Uint32 place_id_len = app_item->code_bits_len();
            jstring place_id = utf_to_str(env, (Uint8 *) app_item->code_bits_str(), place_id_len);
            Uint32 pkg_len = app_item->pkg_len();
            jstring pkg = utf_to_str(env, (Uint8 *) app_item->pkg_str(), pkg_len);
            bool allow_zhitou = app_item->is_allow_zhitou();
            bool allow_channel = app_item->is_allow_channel();
            bool allow_act = app_item->is_allow_act();
            bool laxin = app_item->is_laxin();
            bool act = app_item->is_act();
            bool showad = false;
            bool isFromWm = app_item->is_from_wm();
            bool isFromCsj = app_item->is_from_csj();
            env->CallVoidMethod(obj, set_app_info, place_id, pkg,
                                allow_zhitou, allow_channel, allow_act, laxin, act, showad, isFromWm, isFromCsj);
        }
    }

    // 地图信息
    const iptcore::IntentionMapItem* map_item = intent_item->map_item();
    if (map_item != nullptr) {
        jmethodID set_map_info = env->GetMethodID(obj_class, "setMapInfo",
                                          "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;"
                                          "Ljava/lang/String;Ljava/lang/String;)V");
        if (set_map_info != nullptr) {
            Uint32 addr_len = map_item->addr_len();
            jstring addr_str = uni_to_str(env, map_item->addr_str(), addr_len);

            Uint32 name_len = map_item->name_len();
            jstring name_str = uni_to_str(env, map_item->name_str(), name_len);

            Uint32 uid_len = map_item->uid_len();
            jstring uid_str = utf_to_str(env, (Uint8 *) map_item->uid_str(), uid_len);

            Uint32 view_url_len = map_item->view_url_len();
            jstring view_url_str = utf_to_str(env, (Uint8 *) map_item->view_url_str(), view_url_len);

            Uint32 share_url_len = map_item->share_url_len();
            jstring share_url_str = utf_to_str(env, (Uint8 *) map_item->share_url_str(), share_url_len);
            env->CallVoidMethod(obj, set_map_info, addr_str, name_str, uid_str, view_url_str, share_url_str);
        }
    }

    env->DeleteLocalRef(city_str);
    env->DeleteLocalRef(festival_str);
    env->DeleteLocalRef(query_unis_str);
    env->DeleteLocalRef(obj_class);
    return 0;
}

void IptJniMain::on_method_start(Uint32 method_id, const char* msg, Uint32 msg_len) {
    LOGI("on_method_start");
    if (_ipt_env != NULL && _ipt_env->_main_thread_env.jni_env != NULL && _ipt_env->_g_ard_env != NULL) {
        JNIEnv* env = _ipt_env->_main_thread_env.jni_env;
        jmethodID core_method_start = _ipt_env->_core_method_start_id;

        if (core_method_start != NULL) {
            jstring message = utf_to_str(env, (Uint8 *) msg, msg_len);
            env->CallVoidMethod(_ipt_env->_g_ard_env, core_method_start, method_id, message);
        }
    }
}

void IptJniMain::on_method_end(Uint32 method_id, const char* msg, Uint32 msg_len) {
    LOGI("on_method_end");
    if (_ipt_env != NULL && _ipt_env->_main_thread_env.jni_env != NULL && _ipt_env->_g_ard_env != NULL) {
        JNIEnv* env = _ipt_env->_main_thread_env.jni_env;
        jmethodID core_method_end = _ipt_env->_core_method_end_id;

        if (core_method_end != NULL) {
            jstring message = NULL; // utf_to_str(env, (Uint8 *) msg, msg_len);
            env->CallVoidMethod(_ipt_env->_g_ard_env, core_method_end, method_id, message);
        }
    }
}

void IptJniMain::post_inner_method_timecost(Uint32 method_id, Uint32 method_cost) {
    LOGI("post_inner_method_timecost");
    if (_ipt_env != NULL && _ipt_env->_main_thread_env.jni_env != NULL && _ipt_env->_g_ard_env != NULL) {
        JNIEnv* env = _ipt_env->_main_thread_env.jni_env;
        jmethodID core_method_cost = _ipt_env->_core_post_inner_method_cost;

        if (core_method_cost != NULL) {
            env->CallVoidMethod(_ipt_env->_g_ard_env, core_method_cost, method_id, method_cost);
        }
    }
}

//void IptJniMain::on_notepad_get_response(const char* data, Uint32 data_len, Int32 ecode) {
//    if (_ipt_pad != NULL && _ipt_env->_main_thread_env.jni_env != NULL && _ipt_env->_g_ard_env != NULL) {
//        LOGI("on_notepad_get_response");
//        JNIEnv* env = _ipt_env->_main_thread_env.jni_env;
//        jmethodID callback_id = _ipt_env->_honor_notepad_get_response_callback_id;
//
//        if (callback_id != NULL) {
//            jstring message = utf_to_str(env, (Uint8 *) data, data_len);
//            env->CallVoidMethod(_ipt_env->_g_ard_env, callback_id, message, ecode);
//        }
//    }
//}


}
