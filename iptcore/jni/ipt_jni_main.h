/*
 * ipt_jni_main.h
 *
 *  Created on: 2016-7-14
 *      Author: xuxiang
 *      内核操作主对象封装
 */

#ifndef COREBD_IPT_JNI_MAIN_H
#define COREBD_IPT_JNI_MAIN_H

#include "_pub_iptpad.h"
#include "_pub_handwrite.h"
#include "ipt_jni_env.h"
#include "ipt_jni_config_callback.h"
#include "ipt_jni_net_man.h"
#include <jni.h>
#include <stdlib.h>

namespace iptjni
{

class IptJniMain : public iptcore::InputPad::Callback
{
public:
    IptJniMain();
    ~IptJniMain();

public:
    Int32 open(JNIEnv* env, jobject context, jobject ipt_env,
               jobject ard_netman, jstring dict_dir, jobject package_info, jint flavor);
    Int32 close(JNIEnv* env);
    Int32 get_core_version();
    Int32 get_en_sys_dict_version();
    Int32 get_cz3_dict_gram_version();
    Int32 get_cz3_dict_sys_version();
    Int32 get_cz3_dict_cate_version();
    Int32 get_cz5down_status();
    Int32 get_slide_dict_version();
    bool is_nnranker_installed();
    Int32 backup_trace_log(JNIEnv* env, jstring file_path);
    jstring get_trace_log(JNIEnv* env) const;
    void reset_trace_log(JNIEnv* env) const;
    Int32 get_cz_version(JNIEnv * env, jstring dir_path, jstring dir_mut, jint* versions);
    Int32 apply_patch(JNIEnv* env, jint type, jstring patch_path, jstring target_md5
    , jstring original_file_path);

public:
    Int32 set_duty_info_data(JNIEnv* env, const iptcore::DutyInfo* duty_info, jobject obj);
    Int32 set_cand_info_data(JNIEnv* env, const iptcore::CandInfo* cand_info, jobject obj);
    Int32 set_list_info_data(JNIEnv* env, jstring str, jint list_type, jobject obj);
    Int32 set_sug_card_info_data(JNIEnv* env, const iptcore::SugCardInfo* sug_card_info, jobject obj);
    Int32 set_show_info_data(JNIEnv* env, const iptcore::ShowInfo* show_info, jobject obj, jbyteArray byte_data, 
                             jbyteArray autofix_data);
    Int32 set_phrase_group_info_data(JNIEnv* env, const iptcore::PhraseGroup* phrase_group, 
                                     jobject obj);
    Int32 set_phrase_item_info_data(JNIEnv* env, const iptcore::PhraseItem* phrase_item, 
                                    jobject obj);
    Int32 set_cell_info_data(JNIEnv* env, const iptcore::CellInfo* cellinfo, jobject obj);
    Int32 set_kwd_info_data(JNIEnv* env, const iptcore::KwdCellInfo* cellinfo, jobject obj);
    Int32 set_idm_info_data(JNIEnv* env, const iptcore::IdmCellInfo* cellinfo, jobject obj);
    Int32 set_contact_item_data(JNIEnv* env, const iptcore::ContactItem* contact_item, jobject obj);
    Int32 set_common_trigger_word_item_data(JNIEnv* env, const iptcore::TriggerItem* triggerItem, jobject obj);
    Int32 set_map_trigger_word_item_data(JNIEnv* env, const iptcore::MapItem* triggerItem, jobject obj);
    Int32 set_sug_ad_info_data(JNIEnv* env, const iptcore::AdManager::Response* sug_ad_info, jobject obj);
    Int32 set_cloud_intention_itm_data(JNIEnv* env, const iptcore::IntentionItem* triggerItem, jobject obj);
public:
    void on_duty_info(const iptcore::DutyInfo *dutyinfo) override;
    void on_log_write(const char *str, Uint32 len) override;
    /** on_method_start在方法开始处调用，on_method_end 在方法出口处调用；on_method_start 和 on_method_end 必须成对调用， **/
    void on_method_start(Uint32 method_id, const char* msg, Uint32 msg_len) override;  ///<  方法开始，method_id为方法id，内核可以使用从 0 - 1023 的数字标志
    void on_method_end(Uint32 method_id, const char* msg, Uint32 msg_len) override;  ///<  方法结束，method_id为方法id，内核可以使用从 0 - 1023 的数字标志
    /*  内核内部函数的耗时，以参数输入，客户端实现
            一次调用，参数传递一个方法的method_id和对应的method_cost
            包含的函数有:
            "do_query_find:"
            "core_load:"
            "duptree_build:"
            "find_cloud_cache"
            "find_pinyin_cz:"
            "gram_find:"
            "gram_graph:"
            "gram_bfs:"
        */
    void post_inner_method_timecost(Uint32 method_id, Uint32 method_cost) override;
    // void on_notepad_get_response(const char* data, Uint32 data_len, Int32 ecode) override; ///< 荣耀笔记智能纠错的回调函数

public:
    static jstring uni_to_str(JNIEnv* env, const Uint16* uni);
    static jstring uni_to_str(JNIEnv* env, const Uint16* uni, Uint32 uni_len);
    static jstring utf_to_str(JNIEnv* env, const Uint8* utf, Uint32 utf_len);
    void jchar_to_wchar(const jchar* jchar_str, Uint16* wchar_buff, int len);

private:
    const static Uint32 k_max_buff_size = 64;
    const static char* s_set_data_fun_name;

public:
    const static Uint16 STRING_BUFFER_LEN = 64; // 从内核取字符串时的最大buffer长度
    const static Uint16 MAX_OUTPUT = 8196;

public:
    IptJniEnv* _ipt_env;
    IptJniNetMan* _ipt_net_man;
    IptJniConfigCallback* _ipt_config_callback;
    iptcore::InputLib* _ipt_lib;
    iptcore::InputPad* _ipt_pad;
    iptcore::ConfigItems* _config_items;
    iptcore::ConfigPad* _config_pad;
    hw_interface::HwInterface* _pub_handwrite;
//    iptcore::UserWordManager* _user_word_manager;

public:
    jint* _int_buff;
    jboolean _int_buff_copy;
    jfloat* _float_buff;
    jboolean _float_buff_copy;
    jlong* _long_buff;
    jboolean _long_buff_copy;
    Uint16 _g_string_buff[MAX_OUTPUT];

private:
    jintArray _int_array;
    jlongArray _long_array;
    jfloatArray _float_array;
    jobjectArray _str_array;
};

}  // namespace iptjni


#endif /* COREBD_IPT_JNI_MAIN_H */
