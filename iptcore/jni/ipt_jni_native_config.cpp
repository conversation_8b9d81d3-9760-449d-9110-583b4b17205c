/*
 * ipt_jni_native_config.cpp
 *
 *  Created on: 2019-02-22
 *      Author: cdf
 *      jni config接口
 */

#include "ipt_pub_jni.h"
#include "ipt_jni_main.h"
#include "ipt_jni_log.h"
#include "ipt_jni_gobal.h"

#define g_ipt_main iptjni::IptJniGlobal::get_ipt_jni_main()

enum ConfigKey {

    /** boolean key */
    /** 手写半上屏开关 */
    HW_PRE_EXTRACT_MODE = 0,
    /** 自动纠错 */
    AUTOFIX = HW_PRE_EXTRACT_MODE + 1,
    /** 中英混输 */
    CNEN = AUTOFIX + 1,
    /** 繁体 */
    FANTI = CNEN + 1,
    /** 二次元 */
    ACGN = FANTI + 1,

    /** 联想预测 */
    CURSOR_LIAN = ACGN + 1,
    /** emoji候选 */
    EMOJI = CURSOR_LIAN + 1,
    /** emoji联想 */
    EMOJI_LIAN = EMOJI + 1,
    /** 双拼 */
    SHUANGPIN = EMOJI_LIAN + 1,
    /** 自动保存 */
    AUTOSAVE = SHUANGPIN + 1,

    /** 符号联想 */
    SYLIAN = AUTOSAVE + 1,
    /** 模糊输入 */
    MOHU = SYLIAN + 1,
    /** 更多候选里是否需要有list类型筛选 */
    ENABLE_PAD_MORE_TABS = MOHU + 1,
    /** 笔画字形优先开关状态 */
    BH_FIRST = ENABLE_PAD_MORE_TABS + 1,
    /** 快速输入 */
    FASTINPUT = BH_FIRST + 1,

    /** 五笔提示开关 */
    WBTIP = FASTINPUT + 1,
    /** 五笔拼音混输 */
    WBPY = WBTIP + 1,
    /** 五笔 自造词记忆开关 */
    WB_USE_USERDICT = WBPY + 1,
    /** 五笔 个性化短语开关 */
    WB_ONLY_STANDARD = WB_USE_USERDICT + 1,
    /** 五笔 智能调频开关 */
    WB_FREQ_ADJUST = WB_ONLY_STANDARD + 1,

    /** 五笔 Z键通配开关 */
    WB_Z_COMMON = WB_FREQ_ADJUST + 1,
    /** 个性短语开关 */
    PHRASE = WB_Z_COMMON + 1,
    /** 英文排序方式 */
    ENABLE_HW_LIST = PHRASE + 1,
    /** 隐私模式 */
    PRIVATE_MODE = ENABLE_HW_LIST + 1,

    /** 双拼输入转换开关 */
    SHUANGPIN_NOCVT = PRIVATE_MODE + 1,
    /** 空格上屏联想词选项状态  false-空格清空联想词  true-空格上屏联想词首选 */
    SPACE_LIAN = SHUANGPIN_NOCVT + 1,
    /** AI助聊的总开关是否打开。该变量会影响到次cand是否显示 */
    AI_CHAT_ENABLE = SPACE_LIAN + 1,
    /** 该场景下，次cand是否支持AI帮写 */
    AUTO_WRITE_CAND_ENABLE = AI_CHAT_ENABLE + 1,
    /** 该场景下，卡片是否支持AI帮写 */
    AUTO_WRITE_PAD_ENABLE = AUTO_WRITE_CAND_ENABLE + 1,

    /** 该场景（如购物场景）下，是否需要自动打开AI帮写的tab */
    AUTO_WRITE_AUTO_OPEN = AUTO_WRITE_PAD_ENABLE + 1,
    /** 该场景下，次cand是否支持AI校对 */
    TXT_ERROR_RECOVERY_CAND_ENABLE = AUTO_WRITE_AUTO_OPEN + 1,
    /** 该场景下，卡片是否支持AI校对 */
    TXT_ERROR_RECOVERY_PAD_ENABLE = TXT_ERROR_RECOVERY_CAND_ENABLE + 1,
    /** 该场景下，次cand是否支持AI趣聊 */
    FUNCHAT_CAND_ENABLE = TXT_ERROR_RECOVERY_PAD_ENABLE + 1,
    /** 该场景下，次cand是否支持AI趣聊 */

    FUNCHAT_PAD_ENABLE = FUNCHAT_CAND_ENABLE + 1,
    /** 该场景下，神句配图是否支持自动打开 */
    AI_PEITU_AUTO_OPEN = FUNCHAT_PAD_ENABLE + 1,
    /** 该场景输入前预测是否支持正负能量 */
    SENT_PRE_MODE_ENABLED = AI_PEITU_AUTO_OPEN + 1,
    /** 输入前整句开关，true表示开 */
    IS_SENT_PRE_ENABLED = SENT_PRE_MODE_ENABLED + 1,
    /** 输入中整句开关，true表示开 */
    IS_SENT_CLOUD_ENABLED = IS_SENT_PRE_ENABLED + 1,
    /** 是否保留输入后整句预测，保留的话，撰写和纠错内核不会发起了 */
    IS_SENT_HIDE_COMPOSE = IS_SENT_CLOUD_ENABLED + 1,

    /** 写轨迹到文件的开关 */
    TRACE_FLUSH = IS_SENT_HIDE_COMPOSE + 1,
    /** 是否开启收集误触信息 */
    COLLECT_MIS_INFO = TRACE_FLUSH + 1,
    /** 英文输入是否经过内核 */
    IS_ABC = COLLECT_MIS_INFO + 1,
    /** 简单云输入 */
    IS_EASYCLOUD = IS_ABC + 1,
    /** 个性化语音状态 */
    IS_VOICE_CORRECT = IS_EASYCLOUD + 1,

    /** 设置英语面板下是否句首自动大写 */
    AUTO_CAPITAL = IS_VOICE_CORRECT + 1,
    /** 设置SUG开关 */
    IS_SUGOPEN = AUTO_CAPITAL + 1,
    /** 硬键盘模式开关 */
    IS_HARD_KEYBOARD = IS_SUGOPEN + 1,
    /** 设置计算器可用 */
    ENABLE_CALC = IS_HARD_KEYBOARD + 1,
    /** 联想打点信息 */
    INPUT_ASSOCIATE_STATE = ENABLE_CALC + 1,

    /** 四码唯一时自动上屏 */
    WB_FOUR_CODE_AUTO_SEND_STATE = INPUT_ASSOCIATE_STATE + 1,
    /** 第五码将首选上屏 */
    WB_FIVE_CODE_SEND_FIRST_STATE = WB_FOUR_CODE_AUTO_SEND_STATE + 1,
    /** 次cand是否打开 */
    SUB_CAND_STATE = WB_FIVE_CODE_SEND_FIRST_STATE + 1,
    /** 数字联想是否打开 */
    NUM_LIAN = SUB_CAND_STATE + 1,
    /** 删除键联想 */
    BACKSPACE_LIAN = NUM_LIAN + 1,
    /** 是否启用人名模式。默认启用 */
    ENABLE_NAME_MODE = BACKSPACE_LIAN + 1,
    /** 英文纠错开关。纠错打开时，第二词为最优选 */
    EN_SECOND_IS_BEST = ENABLE_NAME_MODE + 1,
    /** 设置当前输入框是否具有no_suggest属性。参考OEMinput-2367第一条 */
    BOX_NO_SUGGEST = EN_SECOND_IS_BEST + 1,
    /** 设置英文面板下是否开启高级查词模式（光标查词、退格查词） */
    EN_ADVANCED_FIND_MODE = BOX_NO_SUGGEST + 1,
    /** 拼音面板shift状态变化时是否刷新面板 */
    REFRESH_KEYBOARD_WHEN_SHIFT_CHANGE_IN_CN = EN_ADVANCED_FIND_MODE + 1,
    /** 无输入码时是否忽略点击的分词键 */
    IGNORE_SPLIT_NO_INPUT = REFRESH_KEYBOARD_WHEN_SHIFT_CHANGE_IN_CN + 1,
    /** 中文面板有输入码时切换英文，是否上屏输入码 */
    COMMIT_INPUT_WHEN_SWITCH_LANGUAGE = IGNORE_SPLIT_NO_INPUT + 1,
    /** 无输入码时按shift+code，是否直接上屏大写字母 */
    COMMIT_ALPHA_WHEN_SHIFT_LONGDOWN = COMMIT_INPUT_WHEN_SWITCH_LANGUAGE + 1,
    /** inline输入码模式 */
    INLINE_SHOW = COMMIT_ALPHA_WHEN_SHIFT_LONGDOWN + 1,
    /** 手写未结束的情况下会屏蔽REFL_CAND和REFL_CAND刷新标记，屏蔽手写预上屏 */
    DISABLE_HW_CAND_BEFORE_FINISH = INLINE_SHOW + 1,
    /** 拼音面板shift状态变化时是否刷新面板 */
    REFRESH_PINYIN_SHIFT = DISABLE_HW_CAND_BEFORE_FINISH + 1,
    /** 五笔面板shift状态变化时是否刷新面板 */
    REFRESH_WUBI_SHIFT = REFRESH_PINYIN_SHIFT + 1,
    /** 当前是否是高情商场景 */
    HIGHEQ_ENABLE = REFRESH_WUBI_SHIFT + 1,
    /** 当前是否请求小模型 */
    REQUEST_MMODLE = HIGHEQ_ENABLE + 1,
    /** 当前是否展示次 Cand 最低优先级提示 */
    AICHAT_CONTENT_HINT = REQUEST_MMODLE + 1,
    /** 是否将拼音九键分词修改为 1 ，默认为关闭状态 */
    TREAT_T9_SPLIT = AICHAT_CONTENT_HINT + 1,
    /**生僻字面板的生僻字带拼音（例如【犇(bēn)】）,关闭后不带拼音（例如【犇】）,默认是开启状态 */
    RARE_ZI_WITH_PINYIN = TREAT_T9_SPLIT + 1,
    /** 云端意图的开关（主线） */
    REQ_CLOUD_INTENT = RARE_ZI_WITH_PINYIN + 1,
    /** 是否是发送的场景 */
    SCENE_WITH_SEND_BOX = REQ_CLOUD_INTENT + 1,
    /** 地图触发词开关 */
    REQ_MAP_CLOUD_INTENT = SCENE_WITH_SEND_BOX + 1,
    /** Boolean 类型设置项的key的结束 */
    BOOL_KEY_END = REQ_MAP_CLOUD_INTENT,

    /** Int Key */
    /** 英文大小写 */
    ENCASE = BOOL_KEY_END + 1,
    /** 英文排序方式 */
    ENSORT = ENCASE + 1,
    /** 模糊输入选项 */
    MOHU_OPTION = ENSORT + 1,
    /** 个性短语位置 */
    PHRASE_POS = MOHU_OPTION + 1,
    /** 手写模式 */
    HW_TYPE = PHRASE_POS + 1,

    /** 手写识别速度 */
    HW_SPEED = HW_TYPE + 1,
    /** 键盘手写状态 */
    TRACK_TYPE = HW_SPEED + 1,
    /** 五笔编码方案 */
    WB_SCHEMA = TRACK_TYPE + 1,
    /** 联想类型 */
    LEGEND_MODE = WB_SCHEMA + 1,

    /** 手写联想类型 */
    HW_LEGEND_MODE = LEGEND_MODE + 1,
    /** 英文联想类型 */
    EN_LEGEND_MODE = HW_LEGEND_MODE + 1,
    /** 手写注音类型 */
    HW_TONE_MODE = EN_LEGEND_MODE + 1,
    /** 云输入类型 */
    CLOUD_INPUT_TYPE = HW_TONE_MODE + 1,
    /** 仓颉编码类型 */
    CJ_SCHEMA = CLOUD_INPUT_TYPE + 1,
    /** 全拼筛选状态 拼音/五笔 */
    QP_FILTER = CJ_SCHEMA + 1,

    /** 是否安装了手百 */
    EXIST_BM = QP_FILTER + 1,
    /** 手机宽度 */
    PHONE_WIDTH = EXIST_BM + 1,
    /** 手机高度 */
    PHONE_HEIGHT = PHONE_WIDTH + 1,
    /** 用于计算Icon大小的base width值 */
    ICON_SIZE = PHONE_HEIGHT + 1,
    /** 网络环境信息 */
    ENV_NET_TYPE = ICON_SIZE + 1,

    /** 屏幕方向 */
    ORIENTATION = ENV_NET_TYPE + 1,
    /** 框属性 */
    ENV_EDIT_TYPE = ORIENTATION + 1,
    /** 框属性 */
    BOX_ATTRI = ENV_EDIT_TYPE + 1,
    /** 回车键类型 */
    ENTER_KEY_TYPE = BOX_ATTRI + 1,
    /** 当前是聊天场景或购物场景 */
    AI_SCENE_TYPE = ENTER_KEY_TYPE + 1,

    /** 设置轨迹的模式，也即开关 */
    TRACE_MODE = AI_SCENE_TYPE + 1,
    /** 设置英文shift状态 */
    EN_SHIFT = TRACE_MODE + 1,
    /** 当前是聊天场景或购物场景 */
    CLOUD_DELAY_TIME = EN_SHIFT + 1,
    /** 设置计算器time时间, 单位ms(默认100ms) */
    CALC_TIME_INTERVAL = CLOUD_DELAY_TIME + 1,
    /** 内核log的等级 */
    DEBUG_LOG_LEVEL = CALC_TIME_INTERVAL + 1,
    /** 英语面板下点击按键是否自动添加空格 */
    SPACE_AUTO_INSERT = DEBUG_LOG_LEVEL + 1,
    /** 设置生僻字历史词最大个数 */
    RECENT_RARE_CAND_LIMIT = SPACE_AUTO_INSERT + 1,
    /** 文心模型加载的状态（仅有get） */
    WENXIN_LOAD_STATE = RECENT_RARE_CAND_LIMIT + 1,
    /** 输入框hint文本的类型 */
    EDIT_HINT_TYPE = WENXIN_LOAD_STATE + 1,
    /**
     * 是否需要请求手写联想的结果
     * 在 is_hw_req_cloud 关闭的场景，仅看是否需要在手写时请求联想(OEM在云端触发词的时候使用)
     * 内核默认关闭，各平台根据需求开启，开启效果：可请求hw_req的联想
     * 这时，即使 is_hw_req_cloud 关闭，is_hw_req_cloud_lian开启仍需请求手写联想
     */
    IS_HANDWRITING_REQUEST_CLOUD_LAIN = EDIT_HINT_TYPE + 1,


    /** String Key */
    /** sug白名单数据 */
    SUG_WHITE_DATA = IS_HANDWRITING_REQUEST_CLOUD_LAIN + 1,
    /** 手机CUID */
    CUID = SUG_WHITE_DATA + 1,
    /** 手机CUID3 */
    CUID3 = CUID + 1,
    /** 手机OAID */
    OAID = CUID3 + 1,
    /** 手机型号 */
    MODEL = OAID + 1,

    /** ipt_version */
    IPT_VER = MODEL + 1,
    /** 渠道号 */
    CHANNEL = IPT_VER + 1,
    /** 平台号 */
    IPT_PLATFORM = CHANNEL + 1,
    /** APP环境信息:包名 */
    ENV_APP = IPT_PLATFORM + 1,
    /** 城市 */
    CITY = ENV_APP + 1,

    /** 当前皮肤 */
    THEME = CITY + 1,
    /** 框内HINT词 */
    EDIT_HINT = THEME + 1,
    /** 双拼方案 */
    SP_FILE = EDIT_HINT + 1,
    /** 内核云输入1.3协议和2.0协议切换 */
    CLOUD_VERSION = SP_FILE + 1,
    /** 手写是否请求云 */
    HW_REQ_CLOUD = CLOUD_VERSION + 1,
    /** 设置云输入发起等级 */
    CLOUD_REQUEST_LEVEL = HW_REQ_CLOUD + 1,
    /** 设置用户画像 */
    USER_PROFILE = CLOUD_REQUEST_LEVEL + 1,
    /** 设置地图触发词 */
    MAP_TRIGGER_WORDS = USER_PROFILE + 1,
    /** 设置通用触发词 */
    COMMON_TRIGGER_WORDS = MAP_TRIGGER_WORDS + 1,

    /**
     * 云端触发词
     */
    CLOUND_TRIGGER_WORDS = COMMON_TRIGGER_WORDS + 1,

    /**
     * android Id
     */
    ANDROID_ID = CLOUND_TRIGGER_WORDS + 1,
    /**
     * 手机厂商
     */
    PHONE_VENDOR = ANDROID_ID + 1,
    /**
     * 系统版本
     */
    OS_VERSION = PHONE_VENDOR + 1,
    /** 配置手写结束后是否强制全上屏，默认关闭 */
    HW_FORCE_INSERT = OS_VERSION + 1,
    /** 配置当前场景是否是全局手写，默认关闭 */
    GLOBAL_HW_MODE = HW_FORCE_INSERT + 1,
    HK_CONVERT_DOT_AFTER_NUMBER = GLOBAL_HW_MODE + 1,
    /** 设置是否启用高斯个性化模型 */
    GAUSS_CUSTOMIZE = HK_CONVERT_DOT_AFTER_NUMBER + 1,
    /** 用于表示拆字的位置是否需要移动到主cand上，默认是false */
    CHAIZI_RESULT_INSERT_MAINCAND = GAUSS_CUSTOMIZE + 1,
    /** 设置是否启用高斯个性化模型 */
    HANDWRITING_LIAN_CLOUD_TRIGGER_INDEX = CHAIZI_RESULT_INSERT_MAINCAND + 1,
    /**
     * 荣耀特有的 OAID
     */
    HONOR_OAID = HANDWRITING_LIAN_CLOUD_TRIGGER_INDEX + 1,
    /** 是否屏蔽手写定时器产生的cand */
    DISABLE_HW_TIMER_CAND = HONOR_OAID + 1,
    /** 联想人联想功能 */
    CONTACT_LIAN = DISABLE_HW_TIMER_CAND + 1,
    /** 高斯个性化模型落点收集与训练 */
    GAUSS_CUSTOMIZE_USER_POINTS = CONTACT_LIAN + 1,

    /**
     * 浏览器 UA
     */
    USER_AGENT = GAUSS_CUSTOMIZE_USER_POINTS + 1,

    /**
     * 运营商
     */
    MOBILE_CARRIER = USER_AGENT + 1,

    /**
     * 设备启动时间
     */
    DEVICE_START_SEC = MOBILE_CARRIER + 1,

    /**
     * 物理内存
     */
    PHYSICAL_MEM_BYTE = DEVICE_START_SEC + 1,

    /**
     * 硬盘大小
     */
    HARD_DISK_SIZE_BYTE = PHYSICAL_MEM_BYTE + 1,

    /**
     * 系统更新时间
     */
     SYSTEM_UPDATE_MICRO_SEC = HARD_DISK_SIZE_BYTE + 1,
    /**
     * 是否需要云端通用触发词请求神句配图
     */
    IS_NEED_CLOUD_TRIGGER_PEITU = SYSTEM_UPDATE_MICRO_SEC + 1,
    /**
     * emoji不强插触发词后，根据词频自主排序（现在仅荣耀开启，其他端关闭）
     */
    EMOJI_CAND_SORT_BYFREQ = IS_NEED_CLOUD_TRIGGER_PEITU + 1,
    /**
    * 个人信息联想开关开关 - 是否查询用户个人化信息，并给出联想候选，如手机号码，身份证号等 ///
    */
    PERSON_INFO_PERMISSION_STATUS = EMOJI_CAND_SORT_BYFREQ + 1,
    /**
    * 是否需要判断输入内容是否包含身份证号 默认关闭
    */
    IDENTITY_NUM_RECOGNIZE = PERSON_INFO_PERMISSION_STATUS + 1

};

const Uint32 MAX_LOG_LEN = 256;

void jni_set_long(JNIEnv* env, jobject obj, jint key, jlong value) {
    if (g_ipt_main._config_items == nullptr)
    {
        return;
    }
    switch (key) {
        case ConfigKey::DEVICE_START_SEC:
            g_ipt_main._config_items->cfg_set_device_start_sec(value);
            break;
        case ConfigKey::PHYSICAL_MEM_BYTE:
            g_ipt_main._config_items->cfg_set_physical_memory_byte(value);
            break;
        case ConfigKey::HARD_DISK_SIZE_BYTE:
            g_ipt_main._config_items->cfg_set_harddisk_size_byte(value);
            break;
        case ConfigKey::SYSTEM_UPDATE_MICRO_SEC:
            g_ipt_main._config_items->cfg_set_system_update_microsec(value);
            break;
        default:
            g_ipt_main.on_log_write("Long Set NOT-IMPLEMENTED!", MAX_LOG_LEN);
            break;
        }
}

void jni_set_int(JNIEnv* env, jobject obj, jint key, jint value)
{
    if (g_ipt_main._config_items == nullptr)
    {
        return;
    }
    switch (key) {
        case ConfigKey::ENCASE:
            g_ipt_main._config_items->cfg_set_encase(static_cast<iptcore::ConfigItems::CfgEnCase>(value));
            break;
        case ConfigKey::ENSORT:
            g_ipt_main._config_items->cfg_set_ensort(static_cast<iptcore::ConfigItems::CfgEnSort>(value));
            break;
        case ConfigKey::MOHU_OPTION:
            g_ipt_main._config_items->cfg_set_mohu_option(value);
            break;
        case ConfigKey::PHRASE_POS:
            g_ipt_main._config_items->cfg_set_phrase_pos(value);
            break;
        case ConfigKey::HW_TYPE:
            g_ipt_main._config_items->cfg_set_hw_type(static_cast<iptcore::ConfigItems::CfgHwType>(value));
            break;

        case ConfigKey::HW_SPEED:
            g_ipt_main._config_items->cfg_set_hw_speed(static_cast<iptcore::ConfigItems::CfgHwSpeed>(value));
            break;
        case ConfigKey::TRACK_TYPE:
            g_ipt_main._config_items->cfg_set_track_type(static_cast<iptcore::ConfigItems::CfgTrackType>(value));
            break;
        case ConfigKey::WB_SCHEMA:
            g_ipt_main._config_items->cfg_set_wb_schema(static_cast<iptcore::ConfigItems::WubiSchema>(value));
            break;
        case ConfigKey::LEGEND_MODE:
            g_ipt_main._config_items->cfg_set_legend_mode(static_cast<iptcore::ConfigItems::LegendMode>(value));
            break;
        case ConfigKey::EN_LEGEND_MODE:
            g_ipt_main._config_items->cfg_set_en_legend_mode(static_cast<iptcore::ConfigItems::LegendMode>(value));
            break;
        case ConfigKey::HW_LEGEND_MODE:
            g_ipt_main._config_items->cfg_set_hw_legend_mode(static_cast<iptcore::ConfigItems::LegendMode>(value));
            break;
        case ConfigKey::HW_TONE_MODE:
            g_ipt_main._config_items->cfg_set_hw_tone_mode(static_cast<iptcore::ConfigItems::HwToneMode>(value));
            break;
        case ConfigKey::CLOUD_INPUT_TYPE:
            g_ipt_main._config_items->cfg_set_cloud_input_type(
                    static_cast<iptcore::ConfigItems::CloudInputType>(value));
            break;
        case ConfigKey::CJ_SCHEMA:
            g_ipt_main._config_items->cfg_set_cj_schema(static_cast<iptcore::ConfigItems::CangjieSchema>(value));
            break;
        case ConfigKey::QP_FILTER:
            g_ipt_main._config_items->cfg_set_qp_filter(static_cast<iptcore::ConfigItems::QpFilter>(value));
            break;
        case ConfigKey::EXIST_BM:
            g_ipt_main._config_items->cfg_set_exist_bm(value);
            break;

        case ConfigKey::PHONE_WIDTH:
            g_ipt_main._config_items->cfg_set_phone_width(value);
            break;
        case ConfigKey::PHONE_HEIGHT:
            g_ipt_main._config_items->cfg_set_phone_height(value);
            break;
        case ConfigKey::ICON_SIZE:
            g_ipt_main._config_items->cfg_set_ipt_icon_size(value);
            break;
        case ConfigKey::ENV_NET_TYPE:
            g_ipt_main._config_items->cfg_set_env_net_type(static_cast<CLOUD_NET_TYPE>(value));
            break;
        case ConfigKey::ORIENTATION:
            g_ipt_main._config_items->cfg_set_orientation(static_cast<enum ORIENTATION>(value));
            break;
        case ConfigKey::ENV_EDIT_TYPE:
            g_ipt_main._config_items->cfg_set_env_edit_type(value);
            break;
        case ConfigKey::BOX_ATTRI:
            g_ipt_main._config_items->cfg_set_box_attri(value);
            break;
        case ConfigKey::ENTER_KEY_TYPE:
            g_ipt_main._config_items->cfg_set_enterkey_type(value);
            break;
        case ConfigKey::AI_SCENE_TYPE:
            g_ipt_main._config_items->cfg_set_ai_scene_type(static_cast<iptcore::AiSceneType>(value));
            break;
        case ConfigKey::TRACE_MODE:
            g_ipt_main._config_items->cfg_set_trace_mode(static_cast<iptcore::ConfigItems::TraceMode>(value));
            break;

        case ConfigKey::EN_SHIFT:
            if (g_ipt_main._ipt_pad == nullptr)
            {
                return;
            }
            g_ipt_main._ipt_pad->set_en_shift(iptcore::ConfigItems::CfgEnShift(value));
            break;
        case ConfigKey::CLOUD_DELAY_TIME:
            g_ipt_main._config_items->cfg_set_cloud_delay_time(value);
            break;
        case ConfigKey::CALC_TIME_INTERVAL:
            g_ipt_main._config_items->cfg_set_calc_time_interval(value);
            break;
        case ConfigKey::DEBUG_LOG_LEVEL:
            g_ipt_main._config_items->cfg_set_debug_log_level(static_cast<iptcore::CoreLogLevel>(value));
            break;
        case ConfigKey::CLOUD_REQUEST_LEVEL:
            g_ipt_main._config_items->cfg_set_cloud_request_level(
                static_cast<iptcore::ConfigItems::CloudReqLevel>(value));
            break;
        case ConfigKey::SPACE_AUTO_INSERT:
            g_ipt_main._config_items->cfg_set_space_auto_insert(value);
            break;
        case ConfigKey::WENXIN_LOAD_STATE:
            // do nothing
            break;
        case ConfigKey::EDIT_HINT_TYPE:
            g_ipt_main._config_items->cfg_set_env_edit_hint_type(static_cast<iptcore::ConfigItems::EditHintType>(value));
            break;
        case ConfigKey::RECENT_RARE_CAND_LIMIT:
            g_ipt_main._config_items->cfg_set_recent_rare_cand_limit(value);
            break;
        case ConfigKey::MAP_TRIGGER_WORDS:
            g_ipt_main._config_items->cfg_set_is_req_cloud_map_word(value);
            break;
        case ConfigKey::COMMON_TRIGGER_WORDS:
            g_ipt_main._config_items->cfg_set_is_need_common_trigger_word(value);
            break;
        case ConfigKey::CLOUND_TRIGGER_WORDS:
            g_ipt_main._config_items->cfg_set_is_need_cloud_trigger_word(value);
            break;
        case ConfigKey::HANDWRITING_LIAN_CLOUD_TRIGGER_INDEX:
            g_ipt_main._config_items->cfg_set_handwt_lian_cloud_trigger_idx(value);
            break;
        case ConfigKey::IS_HANDWRITING_REQUEST_CLOUD_LAIN:
            g_ipt_main._config_items->cfg_set_is_hw_req_cloud_lian(value);
            break;
        case ConfigKey::MOBILE_CARRIER:
            g_ipt_main._config_items->cfg_set_carrier(static_cast<iptcore::ConfigItems::CarrierType>(value));
            break;
        default:
            g_ipt_main.on_log_write("Int Set NOT-IMPLEMENTED!", MAX_LOG_LEN);
            break;
    }
}

jint jni_get_int(JNIEnv* env, jobject obj, jint key)
{
    if (g_ipt_main._config_items == nullptr)
    {
        return 0;
    }

    jint ret = 0;
    switch (key) {
        case ConfigKey::ENCASE:
            ret = g_ipt_main._config_items->cfg_get_encase();
            break;
        case ConfigKey::ENSORT:
            ret = g_ipt_main._config_items->cfg_get_ensort();
            break;
        case ConfigKey::MOHU_OPTION:
            ret = g_ipt_main._config_items->cfg_get_mohu_option();
            break;
        case ConfigKey::PHRASE_POS:
            ret = g_ipt_main._config_items->cfg_get_phrase_pos();
            break;
        case ConfigKey::HW_TYPE:
            ret = g_ipt_main._config_items->cfg_get_hw_type();
            break;

        case ConfigKey::HW_SPEED:
            ret = g_ipt_main._config_items->cfg_get_hw_speed();
            break;
        case ConfigKey::TRACK_TYPE:
            ret = g_ipt_main._config_items->cfg_get_track_type();
            break;
        case ConfigKey::WB_SCHEMA:
            ret = g_ipt_main._config_items->cfg_get_wb_schema();
            break;
        case ConfigKey::LEGEND_MODE:
            ret = g_ipt_main._config_items->cfg_get_legend_mode();
            break;
        case ConfigKey::EN_LEGEND_MODE:
            ret = g_ipt_main._config_items->cfg_get_en_legend_mode();
            break;
        case ConfigKey::HW_LEGEND_MODE:
            ret = g_ipt_main._config_items->cfg_get_hw_legend_mode();
            break;

        case ConfigKey::HW_TONE_MODE:
            ret = g_ipt_main._config_items->cfg_get_hw_tone_mode();
            break;
        case ConfigKey::CLOUD_INPUT_TYPE:
            ret = g_ipt_main._config_items->cfg_get_cloud_input_type();
            break;
        case ConfigKey::CJ_SCHEMA:
            ret = g_ipt_main._config_items->cfg_get_cj_schema();
            break;
        case ConfigKey::QP_FILTER:
            ret = g_ipt_main._config_items->cfg_get_qp_filter();
            break;
        case ConfigKey::EXIST_BM:
            ret = g_ipt_main._config_items->cfg_get_exist_bm();
            break;

        case ConfigKey::PHONE_WIDTH:
            ret = g_ipt_main._config_items->cfg_get_phone_width();
            break;
        case ConfigKey::PHONE_HEIGHT:
            ret = g_ipt_main._config_items->cfg_get_phone_height();
            break;
        case ConfigKey::ICON_SIZE:
            ret = g_ipt_main._config_items->cfg_get_ipt_icon_size();
            break;
        case ConfigKey::ENV_NET_TYPE:
            ret = g_ipt_main._config_items->cfg_get_env_net_type();
            break;

        case ConfigKey::ENV_EDIT_TYPE:
            ret = g_ipt_main._config_items->cfg_get_env_edit_type();
            break;
        case ConfigKey::BOX_ATTRI:
            ret = g_ipt_main._config_items->cfg_get_box_attri();
            break;
        case ConfigKey::ENTER_KEY_TYPE:
            ret = g_ipt_main._config_items->cfg_get_enterkey_type();
            break;
        case ConfigKey::TRACE_MODE:
            ret = g_ipt_main._config_items->cfg_get_trace_mode();
            break;
        case ConfigKey::EN_SHIFT:
            if (g_ipt_main._ipt_pad == nullptr)
            {
                return JNI_FALSE ;
            }
            ret = g_ipt_main._ipt_pad->get_en_shift();
            break;
        case ConfigKey::CLOUD_DELAY_TIME:
            ret = g_ipt_main._config_items->cfg_get_cloud_delay_time();
            break;
        case ConfigKey::DEBUG_LOG_LEVEL:
            ret = g_ipt_main._config_items->cfg_get_debug_log_level();
            break;
        case ConfigKey::SPACE_AUTO_INSERT:
            ret = g_ipt_main._config_items->cfg_get_space_auto_insert();
            break;
        case ConfigKey::RECENT_RARE_CAND_LIMIT:
            ret = g_ipt_main._config_items->cfg_get_recent_rare_cand_limit();
            break;
        case ConfigKey::WENXIN_LOAD_STATE:
            ret = g_ipt_main._config_items->cfg_get_local_wenxin_load_type();
            break;
        case ConfigKey::EDIT_HINT_TYPE:
            ret = g_ipt_main._config_items->cfg_get_env_edit_hint_type();
            break;
        default:
            g_ipt_main.on_log_write("Int Get NOT-IMPLEMENTED!", MAX_LOG_LEN);
            break;
    }
    return ret;
}

void jni_set_boolean(JNIEnv* env, jobject obj, jint key, jboolean value)
{
    if (g_ipt_main._config_items == nullptr)
    {
        return;
    }

    switch (key) {
        case ConfigKey::HW_PRE_EXTRACT_MODE:
            g_ipt_main._config_items->cfg_set_hw_pre_extract_mode(value);
            break;
        case ConfigKey::AUTOFIX:
            g_ipt_main._config_items->cfg_set_is_autofix(value);
            break;
        case ConfigKey::CNEN:
            g_ipt_main._config_items->cfg_set_is_cnen(value);
            break;
        case ConfigKey::FANTI:
            g_ipt_main._config_items->cfg_set_is_fanti(value);
            break;
        case ConfigKey::ACGN:
            g_ipt_main._config_items->cfg_set_is_acgn(value);
            break;

        case ConfigKey::CURSOR_LIAN:
            g_ipt_main._config_items->cfg_set_lian_on_cursor(value);
            break;
        case ConfigKey::EMOJI:
            g_ipt_main._config_items->cfg_set_is_emoji(value);
            break;
        case ConfigKey::EMOJI_LIAN:
            g_ipt_main._config_items->cfg_set_is_emoji_lian(value);
            break;
        case ConfigKey::SHUANGPIN:
            g_ipt_main._config_items->cfg_set_is_shuangpin(value);
            break;
        case ConfigKey::AUTOSAVE:
            g_ipt_main._config_items->cfg_set_is_autosave(value);
            break;

        case ConfigKey::SYLIAN:
            g_ipt_main._config_items->cfg_set_is_sylian(value);
            break;
        case ConfigKey::MOHU:
            g_ipt_main._config_items->cfg_set_is_mohu(value);
            break;
        case ConfigKey::ENABLE_PAD_MORE_TABS:
            g_ipt_main._config_items->cfg_set_enable_pad_more_tabs(value);
            break;
        case ConfigKey::BH_FIRST:
            g_ipt_main._config_items->cfg_set_is_bh_frist(value);
            break;
        case ConfigKey::FASTINPUT:
            g_ipt_main._config_items->cfg_set_is_fastinput(value);
            break;

        case ConfigKey::WBTIP:
            g_ipt_main._config_items->cfg_set_is_wbtip(value);
            break;
        case ConfigKey::WBPY:
            g_ipt_main._config_items->cfg_set_is_wbpy(value);
            break;
        case ConfigKey::WB_USE_USERDICT:
            g_ipt_main._config_items->cfg_set_is_wb_use_userdict(value);
            break;
        case ConfigKey::WB_ONLY_STANDARD:
            g_ipt_main._config_items->cfg_set_is_wb_def_only_standard(value);
            break;
        case ConfigKey::WB_FREQ_ADJUST:
            g_ipt_main._config_items->cfg_set_is_ch_wb_freq_adjust(value);
            break;
        case ConfigKey::WB_Z_COMMON:
            g_ipt_main._config_items->cfg_set_is_wb_z_common(value);
            break;
        case ConfigKey::PHRASE:
            g_ipt_main._config_items->cfg_set_is_phrase(value);
            break;
        case ConfigKey::ENABLE_HW_LIST:
            g_ipt_main._config_items->cfg_set_enable_hw_list(value);
            break;
        case ConfigKey::PRIVATE_MODE:
            g_ipt_main._config_items->cfg_enable_private_mode(value);
            break;

        case ConfigKey::SHUANGPIN_NOCVT:
            g_ipt_main._config_items->cfg_set_is_shuangpin_nocvt(value);
            break;
        case ConfigKey::SPACE_LIAN:
            g_ipt_main._config_items->cfg_set_space_lian(value);
            break;
        case ConfigKey::AI_CHAT_ENABLE:
            g_ipt_main._config_items->cfg_set_ai_chat_enable(value);
            break;
        case ConfigKey::AUTO_WRITE_CAND_ENABLE:
            g_ipt_main._config_items->cfg_set_auto_write_cand_enable(value);
            break;
        case ConfigKey::AUTO_WRITE_PAD_ENABLE:
            g_ipt_main._config_items->cfg_set_auto_write_pad_enable(value);
            break;
        case ConfigKey::AUTO_WRITE_AUTO_OPEN:
            g_ipt_main._config_items->cfg_set_auto_write_auto_open(value);
            break;

        case ConfigKey::TXT_ERROR_RECOVERY_CAND_ENABLE:
            g_ipt_main._config_items->cfg_set_txt_error_recovery_cand_enable(value);
            break;
        case ConfigKey::TXT_ERROR_RECOVERY_PAD_ENABLE:
            g_ipt_main._config_items->cfg_set_txt_error_recovery_pad_enable(value);
            break;
        case ConfigKey::FUNCHAT_CAND_ENABLE:
            g_ipt_main._config_items->cfg_set_funchat_cand_enable(value);
            break;
        case ConfigKey::FUNCHAT_PAD_ENABLE:
            g_ipt_main._config_items->cfg_set_funchat_pad_enable(value);
            break;
        case ConfigKey::AI_PEITU_AUTO_OPEN:
            g_ipt_main._config_items->cfg_set_ai_peitu_auto_open(value);
            break;
        case ConfigKey::CLOUD_VERSION:
            g_ipt_main._config_items->cfg_set_cloud_version_2_0(value);
            break;
        case ConfigKey::SENT_PRE_MODE_ENABLED:
            g_ipt_main._config_items->cfg_set_sent_pre_mode_enabled(value);
            break;
        case ConfigKey::IS_SENT_PRE_ENABLED:
            g_ipt_main._config_items->cfg_set_is_sent_pre_enabled(value);
            break;
        case ConfigKey::IS_SENT_CLOUD_ENABLED:
            g_ipt_main._config_items->cfg_set_is_sent_cloud_enabled(value);
            break;
        case ConfigKey::IS_SENT_HIDE_COMPOSE:
            g_ipt_main._config_items->cfg_set_is_sent_hide_compose(value);
            break;
        case ConfigKey::TRACE_FLUSH:
            g_ipt_main._config_items->cfg_set_trace_flush(value);
            break;

        case ConfigKey::COLLECT_MIS_INFO:
            g_ipt_main._config_items->cfg_set_collect_mis_info(value);
            break;
        case ConfigKey::IS_ABC:
            g_ipt_main._config_items->cfg_set_is_abc(value);
            break;
        case ConfigKey::IS_EASYCLOUD:
            g_ipt_main._config_items->cfg_set_is_easycloud(value);
            break;
        case ConfigKey::IS_VOICE_CORRECT:
            g_ipt_main._config_items->cfg_set_is_voice_correct(value);
            break;

        case ConfigKey::AUTO_CAPITAL:
            g_ipt_main._config_items->cfg_set_auto_capital(value);
            break;
        case ConfigKey::IS_SUGOPEN:
            g_ipt_main._config_items->cfg_set_is_sugopen(value);
            break;
        case ConfigKey::IS_HARD_KEYBOARD:
            g_ipt_main._config_items->cfg_set_is_hard_keyboard(value);
            break;
        case ConfigKey::ENABLE_CALC:
            g_ipt_main._config_items->cfg_set_enable_calc(value);
            break;

        case ConfigKey::INPUT_ASSOCIATE_STATE:
            g_ipt_main._config_items->cfg_set_collect_input_statistics(value);
            break;
        case ConfigKey::WB_FOUR_CODE_AUTO_SEND_STATE:
            g_ipt_main._config_items->cfg_set_is_ch_wb_4code_direct_show(value);
            break;
        case ConfigKey::WB_FIVE_CODE_SEND_FIRST_STATE:
            g_ipt_main._config_items->cfg_set_is_ch_wb_5code_showfirst(value);
            break;
        case ConfigKey::SUB_CAND_STATE:
            g_ipt_main._config_items->cfg_set_subcand_enable(value);
            break;
        case ConfigKey::NUM_LIAN:
            g_ipt_main._config_items->cfg_set_is_num_lian(value);
            break;
        case ConfigKey::BACKSPACE_LIAN:
            g_ipt_main._config_items->cfg_set_lian_back_delete(value);
            break;
        case ConfigKey::ENABLE_NAME_MODE:
            g_ipt_main._config_items->cfg_enable_name_mode(value);
            break;
        case ConfigKey::HW_REQ_CLOUD:
            g_ipt_main._config_items->cfg_set_is_hw_req_cloud(value);
            break;
        case ConfigKey::EN_SECOND_IS_BEST:
            g_ipt_main._config_items->cfg_set_en_second_is_best(value);
            break;
        case ConfigKey::BOX_NO_SUGGEST:
            g_ipt_main._config_items->cfg_set_is_box_no_suggest(value);
            break;
        case ConfigKey::EN_ADVANCED_FIND_MODE:
            g_ipt_main._config_items->cfg_set_en_advanced_find_mode(value);
            break;
        case ConfigKey::REFRESH_PINYIN_SHIFT:
            g_ipt_main._config_items->cfg_set_refresh_layout_for_pinyin_shift(value);
            break;
        case ConfigKey::REFRESH_WUBI_SHIFT:
            g_ipt_main._config_items->cfg_set_refresh_layout_for_wubi_shift(value);
            break;
        case ConfigKey::TREAT_T9_SPLIT:
            g_ipt_main._config_items->cfg_set_treat_t9_split_as_num(value);
            break;
         case ConfigKey::RARE_ZI_WITH_PINYIN:
            g_ipt_main._config_items->cfg_set_rare_zi_with_pinyin(value);
            break;
        case ConfigKey::REQ_CLOUD_INTENT:
            g_ipt_main._config_items->cfg_req_set_cloud_intention(value);
            break;
        case ConfigKey::REQ_MAP_CLOUD_INTENT:
            g_ipt_main._config_items->cfg_req_set_cloud_map_intention(value);
            break;
        case ConfigKey::SCENE_WITH_SEND_BOX:
            g_ipt_main._config_items->cfg_set_scene_with_send_function(value);
            break;
        case ConfigKey::REFRESH_KEYBOARD_WHEN_SHIFT_CHANGE_IN_CN:
            g_ipt_main._config_items->cfg_set_refresh_layout_for_pinyin_shift(value);
            break;
        case ConfigKey::IGNORE_SPLIT_NO_INPUT:
            g_ipt_main._config_items->cfg_set_ignore_split_no_input(value);
            break;
        case ConfigKey::COMMIT_INPUT_WHEN_SWITCH_LANGUAGE:
            g_ipt_main._config_items->cfg_set_commit_input_when_switch_cnen(value);
            break;
        case ConfigKey::COMMIT_ALPHA_WHEN_SHIFT_LONGDOWN:
            g_ipt_main._config_items->cfg_set_commit_alpha_when_shift_longdown(value);
            break;
        case ConfigKey::INLINE_SHOW:
            g_ipt_main._config_items->cfg_set_enable_inline_show(value);
            break;
        case ConfigKey::DISABLE_HW_CAND_BEFORE_FINISH:
            g_ipt_main._config_items->cfg_set_disable_hw_cand_before_finish(value);
            break;
        case ConfigKey::HIGHEQ_ENABLE:
            g_ipt_main._config_items->cfg_set_aichat_higheq_scene(value);
            break;
        case ConfigKey::REQUEST_MMODLE:
            g_ipt_main._config_items->cfg_set_aichat_mmodel_enabled(value);
            break;
        case ConfigKey::HW_FORCE_INSERT:
            g_ipt_main._config_items->cfg_set_hw_force_insert(value);
            break;
        case ConfigKey::GLOBAL_HW_MODE:
            g_ipt_main._config_items->cfg_set_global_hw_mode(value);
            break;
        case ConfigKey::HK_CONVERT_DOT_AFTER_NUMBER:
            g_ipt_main._config_items->cfg_set_convert_dot_after_number(value);
            break;
        case ConfigKey::GAUSS_CUSTOMIZE:
            g_ipt_main._config_items->cfg_set_gauss_user_enabled(value);
            break;
        case ConfigKey::CHAIZI_RESULT_INSERT_MAINCAND:
            g_ipt_main._config_items->cfg_set_chaizi_result_insert_maincand(value);
            break;
        case ConfigKey::IS_NEED_CLOUD_TRIGGER_PEITU:
            g_ipt_main._config_items->cfg_set_is_need_cloud_trigger_peitu(value);
            break;
        case ConfigKey::EMOJI_CAND_SORT_BYFREQ:
            g_ipt_main._config_items->cfg_set_emoji_cand_sort_byfreq(value);
            break;
        case ConfigKey::PERSON_INFO_PERMISSION_STATUS:
            g_ipt_main._config_items->cfg_set_person_info_permission_status(value);
            break;
        case ConfigKey::IDENTITY_NUM_RECOGNIZE:
            g_ipt_main._config_items->cfg_set_identity_num_recognize(value);
            break;
        default:
            g_ipt_main.on_log_write("Bool Set NOT-IMPLEMENTED!", MAX_LOG_LEN);
            break;
    }
}

jboolean jni_get_boolean(JNIEnv* env, jobject obj, jint key)
{
    if (g_ipt_main._config_items == nullptr)
    {
        return JNI_FALSE;
    }

    jboolean ret = JNI_FALSE;
    switch (key) {
        case ConfigKey::HW_PRE_EXTRACT_MODE:
            ret = g_ipt_main._config_items->cfg_get_hw_pre_extract_mode();
            break;
        case ConfigKey::AUTOFIX:
            ret = g_ipt_main._config_items->cfg_get_is_autofix();
            break;
        case ConfigKey::CNEN:
            ret = g_ipt_main._config_items->cfg_get_is_cnen();
            break;
        case ConfigKey::FANTI:
            ret = g_ipt_main._config_items->cfg_get_is_fanti();
            break;
        case ConfigKey::ACGN:
            ret = g_ipt_main._config_items->cfg_get_is_acgn();
            break;

        case ConfigKey::CURSOR_LIAN:
            ret = g_ipt_main._config_items->cfg_get_lian_on_cursor();
            break;
        case ConfigKey::EMOJI:
            ret = g_ipt_main._config_items->cfg_get_is_emoji();
            break;
        case ConfigKey::EMOJI_LIAN:
            ret = g_ipt_main._config_items->cfg_get_is_emoji_lian();
            break;
        case ConfigKey::SHUANGPIN:
            ret = g_ipt_main._config_items->cfg_get_is_shuangpin();
            break;
        case ConfigKey::AUTOSAVE:
            ret = g_ipt_main._config_items->cfg_get_is_autosave();
            break;

        case ConfigKey::SYLIAN:
            ret = g_ipt_main._config_items->cfg_get_is_sylian();
            break;
        case ConfigKey::MOHU:
            ret = g_ipt_main._config_items->cfg_get_is_mohu();
            break;
        case ConfigKey::ENABLE_PAD_MORE_TABS:
            ret = g_ipt_main._config_items->cfg_get_enable_pad_more_tabs();
            break;
        case ConfigKey::BH_FIRST:
            ret = g_ipt_main._config_items->cfg_get_is_bh_frist();
            break;
        case ConfigKey::FASTINPUT:
            ret = g_ipt_main._config_items->cfg_get_is_fastinput();
            break;

        case ConfigKey::WBTIP:
            ret = g_ipt_main._config_items->cfg_get_is_wbtip();
            break;
        case ConfigKey::WBPY:
            ret = g_ipt_main._config_items->cfg_get_is_wbpy();
            break;
        case ConfigKey::WB_USE_USERDICT:
            ret = g_ipt_main._config_items->cfg_get_is_wb_use_userdict();
            break;
        case ConfigKey::WB_ONLY_STANDARD:
            ret = g_ipt_main._config_items->cfg_get_is_wb_def_only_standard();
            break;
        case ConfigKey::WB_FREQ_ADJUST:
            ret = g_ipt_main._config_items->cfg_get_is_ch_wb_freq_adjust();
            break;
        case ConfigKey::WB_Z_COMMON:
            ret = g_ipt_main._config_items->cfg_get_is_wb_z_common();
            break;
        case ConfigKey::PHRASE:
            ret = g_ipt_main._config_items->cfg_get_is_phrase();
            break;
        case ConfigKey::ENABLE_HW_LIST:
            ret = g_ipt_main._config_items->cfg_get_enable_hw_list();
            break;

        case ConfigKey::SHUANGPIN_NOCVT:
            ret = g_ipt_main._config_items->cfg_get_is_shuangpin_nocvt();
            break;
        case ConfigKey::SPACE_LIAN:
            ret = g_ipt_main._config_items->cfg_get_space_lian();
            break;
        case ConfigKey::COLLECT_MIS_INFO:
            ret = g_ipt_main._config_items->cfg_get_collect_mis_info();
            break;
        case ConfigKey::IS_ABC:
            ret = g_ipt_main._config_items->cfg_get_is_abc();
            break;
        case ConfigKey::IS_EASYCLOUD:
            ret = g_ipt_main._config_items->cfg_get_is_easycloud();
            break;
        case ConfigKey::IS_VOICE_CORRECT:
            ret = g_ipt_main._config_items->cfg_get_is_voice_correct();
            break;

        case ConfigKey::AUTO_CAPITAL:
            ret = g_ipt_main._config_items->cfg_get_auto_capital();
            break;
        case ConfigKey::IS_SUGOPEN:
            ret = g_ipt_main._config_items->cfg_get_is_sugopen();
            break;
        case ConfigKey::IS_HARD_KEYBOARD:
            ret = g_ipt_main._config_items->cfg_get_is_hard_keyboard();
            break;

        case ConfigKey::INPUT_ASSOCIATE_STATE:
            ret = g_ipt_main._config_items->cfg_get_collect_input_statistics();
            break;
        case ConfigKey::WB_FOUR_CODE_AUTO_SEND_STATE:
            ret = g_ipt_main._config_items->cfg_get_is_ch_wb_4code_direct_show();
            break;
        case ConfigKey::WB_FIVE_CODE_SEND_FIRST_STATE:
            ret = g_ipt_main._config_items->cfg_get_is_ch_wb_5code_showfirst();
            break;
        case ConfigKey::EN_SECOND_IS_BEST:
            ret = g_ipt_main._config_items->cfg_get_en_second_is_best();
            break;
        case ConfigKey::BOX_NO_SUGGEST:
            ret = g_ipt_main._config_items->cfg_get_is_box_no_suggest();
            break;
        case ConfigKey::EN_ADVANCED_FIND_MODE:
            ret = g_ipt_main._config_items->cfg_get_en_advanced_find_mode();
            break;
        case ConfigKey::REFRESH_PINYIN_SHIFT:
            ret = g_ipt_main._config_items->cfg_get_refresh_layout_for_pinyin_shift();
            break;
        case ConfigKey::REFRESH_WUBI_SHIFT:
            ret = g_ipt_main._config_items->cfg_get_refresh_layout_for_wubi_shift();
            break;
        case ConfigKey::TREAT_T9_SPLIT:
            ret = g_ipt_main._config_items->cfg_get_treat_t9_split_as_num();
            break;
        case ConfigKey::RARE_ZI_WITH_PINYIN:
            ret = g_ipt_main._config_items->cfg_get_rare_zi_with_pinyin();
            break;
        case ConfigKey::INLINE_SHOW:
            ret = g_ipt_main._config_items->cfg_get_enable_inline_show();
            break;
        case ConfigKey::DISABLE_HW_CAND_BEFORE_FINISH:
            ret = g_ipt_main._config_items->cfg_get_disable_hw_cand_before_finish();
            break;
        case ConfigKey::HW_FORCE_INSERT:
            ret = g_ipt_main._config_items->cfg_get_hw_force_insert();
            break;
        case ConfigKey::GLOBAL_HW_MODE:
            ret = g_ipt_main._config_items->cfg_get_global_hw_mode();
            break;
        case ConfigKey::HK_CONVERT_DOT_AFTER_NUMBER:
            ret = g_ipt_main._config_items->cfg_get_convert_dot_after_number();
            break;
        case ConfigKey::GAUSS_CUSTOMIZE:
            ret = g_ipt_main._config_items->cfg_get_gauss_user_enabled();
            break;
        case ConfigKey::PERSON_INFO_PERMISSION_STATUS:
            ret = g_ipt_main._config_items->cfg_get_person_info_permission_status();
            break;
        default:
            g_ipt_main.on_log_write("Bool Get NOT-IMPLEMENTED!", MAX_LOG_LEN);
            break;

    }
    return ret;
}

void jni_set_string(JNIEnv* env, jobject obj, jint key, jstring value)
{
    if (g_ipt_main._config_items == nullptr)
    {
        return;
    }
    jboolean is_copy = false;
    Uint32 value_len = 0;
    const jchar* value_jchar = nullptr;
    const char* value_char = nullptr;
    switch (key) {
        case ConfigKey::SUG_WHITE_DATA:
            value_len = env->GetStringUTFLength(value);
            value_char = env->GetStringUTFChars(value, &is_copy);
            g_ipt_main._config_items->cfg_set_sug_white_data(
                    reinterpret_cast<const Uint8 *>(value_char), value_len);
            break;
        case ConfigKey::CUID:
            value_len = env->GetStringUTFLength(value);
            value_char = env->GetStringUTFChars(value, &is_copy);
            g_ipt_main._config_items->cfg_set_phone_cuid(value_char);
            break;
        case ConfigKey::CUID3:
            value_len = env->GetStringUTFLength(value);
            value_char = env->GetStringUTFChars(value, &is_copy);
            g_ipt_main._config_items->cfg_set_phone_cuid3(value_char);
            break;
        case ConfigKey::OAID:
            value_len = env->GetStringUTFLength(value);
            value_char = env->GetStringUTFChars(value, &is_copy);
            g_ipt_main._config_items->cfg_set_phone_oaid(value_char);
            break;
        case ConfigKey::HONOR_OAID:
            value_len = env->GetStringUTFLength(value);
            value_char = env->GetStringUTFChars(value, &is_copy);
            g_ipt_main._config_items->cfg_set_honor_oaid(value_char);
            break;
        case ConfigKey::MODEL:
            value_len = env->GetStringUTFLength(value);
            value_char = env->GetStringUTFChars(value, &is_copy);
            g_ipt_main._config_items->cfg_set_phone_model(value_char);
            break;

        case ConfigKey::IPT_VER:
            value_len = env->GetStringUTFLength(value);
            value_char = env->GetStringUTFChars(value, &is_copy);
            g_ipt_main._config_items->cfg_set_ipt_ver(value_char);
            break;
        case ConfigKey::CHANNEL:
            value_len = env->GetStringUTFLength(value);
            value_char = env->GetStringUTFChars(value, &is_copy);
            g_ipt_main._config_items->cfg_set_ipt_channel(value_char);
            break;
        case ConfigKey::IPT_PLATFORM:
            value_len = env->GetStringUTFLength(value);
            value_char = env->GetStringUTFChars(value, &is_copy);
            g_ipt_main._config_items->cfg_set_ipt_platform(value_char);
            break;
        case ConfigKey::ENV_APP:
            value_len = env->GetStringUTFLength(value);
            value_char = env->GetStringUTFChars(value, &is_copy);
            g_ipt_main._config_items->cfg_set_env_app(value_char);
            break;
        case ConfigKey::CITY:
            value_len = env->GetStringUTFLength(value);
            value_char = env->GetStringUTFChars(value, &is_copy);
            g_ipt_main._config_items->cfg_set_env_city(value_char);
            break;

        case ConfigKey::THEME:
            value_len = env->GetStringUTFLength(value);
            value_char = env->GetStringUTFChars(value, &is_copy);
            g_ipt_main._config_items->cfg_set_theme(value_char);
            break;
        case ConfigKey::EDIT_HINT:
            value_len = env->GetStringLength(value);
            value_jchar = env->GetStringChars(value, &is_copy);
            g_ipt_main._config_items->cfg_set_env_edit_hint(value_jchar, value_len);
            break;
        case ConfigKey::SP_FILE:
            value_len = env->GetStringUTFLength(value);
            value_char = env->GetStringUTFChars(value, &is_copy);
            g_ipt_main._config_items->cfg_util_set_sp_file(value_char);
            break;
//        case ConfigKey::USER_PROFILE:
//            value_len = env->GetStringUTFLength(value);
//            value_char = env->GetStringUTFChars(value, &is_copy);
//            g_ipt_main._config_items->cfg_set_aichat_request_pfd(value_char, value_len);
//            break;
        case ConfigKey::AICHAT_CONTENT_HINT:
            value_len = env->GetStringLength(value);
            value_jchar = env->GetStringChars(value, &is_copy);
            g_ipt_main._config_items->cfg_set_aichat_content_hint(value_jchar, value_len);
            break;
        case ConfigKey::ANDROID_ID:
            value_len = env->GetStringUTFLength(value);
            value_char = env->GetStringUTFChars(value, &is_copy);
            g_ipt_main._config_items->cfg_set_android_id(value_char);
            break;
        case ConfigKey::PHONE_VENDOR:
            value_len = env->GetStringUTFLength(value);
            value_char = env->GetStringUTFChars(value, &is_copy);
            g_ipt_main._config_items->cfg_set_phone_vendor(value_char);
            break;
        case ConfigKey::OS_VERSION:
            value_len = env->GetStringUTFLength(value);
            value_char = env->GetStringUTFChars(value, &is_copy);
            g_ipt_main._config_items->cfg_set_os_version(value_char);
            break;
        case ConfigKey::USER_AGENT:
            value_char = env->GetStringUTFChars(value, &is_copy);
            g_ipt_main._config_items->cfg_set_ua(value_char);
            break;
        default:
            g_ipt_main.on_log_write("String Set NOT-IMPLEMENTED!", MAX_LOG_LEN);
            break;
    }

    if (value_jchar != nullptr)
    {
        env->ReleaseStringChars(value, value_jchar);
    }
    if (value_char != nullptr)
    {
        env->ReleaseStringUTFChars(value, value_char);
    }
}

jstring jni_get_string(JNIEnv* env, jobject obj, jint key)
{
    if (g_ipt_main._config_items == nullptr)
    {
        return nullptr;
    }

    const char *data = nullptr;
    switch (key) {
        case ConfigKey::SUG_WHITE_DATA:
            data = g_ipt_main._config_items->cfg_get_sug_white_data();
            break;
        case ConfigKey::CUID:
            data = g_ipt_main._config_items->cfg_get_phone_cuid();
            break;
        case ConfigKey::CUID3:
            data = g_ipt_main._config_items->cfg_get_phone_cuid3();
            break;
        case ConfigKey::OAID:
            data = g_ipt_main._config_items->cfg_get_phone_oaid();
            break;
        case ConfigKey::HONOR_OAID:
            data = g_ipt_main._config_items->cfg_get_honor_oaid();
            break;
        case ConfigKey::MODEL:
            data = g_ipt_main._config_items->cfg_get_phone_model();
            break;

        case ConfigKey::IPT_VER:
            data = g_ipt_main._config_items->cfg_get_ipt_ver();
            break;
        case ConfigKey::CHANNEL:
            data = g_ipt_main._config_items->cfg_get_ipt_channel();
            break;
        case ConfigKey::IPT_PLATFORM:
            data = g_ipt_main._config_items->cfg_get_ipt_platform();
            break;
        case ConfigKey::ENV_APP:
            data = g_ipt_main._config_items->cfg_get_env_app();
            break;
        case ConfigKey::CITY:
            data = g_ipt_main._config_items->cfg_get_env_city();
            break;

        case ConfigKey::THEME:
            data = g_ipt_main._config_items->cfg_get_theme();
            break;

        case ConfigKey::ANDROID_ID:
            data = g_ipt_main._config_items->cfg_get_android_id();
            break;
        case ConfigKey::PHONE_VENDOR:
            data = g_ipt_main._config_items->cfg_get_phone_vendor();
            break;
        case ConfigKey::OS_VERSION:
            data = g_ipt_main._config_items->cfg_get_os_version();
            break;
        default:
            g_ipt_main.on_log_write("String Get NOT-IMPLEMENTED!", MAX_LOG_LEN);
            break;
    }

    if (data == nullptr)
    {
        return nullptr;
    }
    return env->NewStringUTF((const char *) data);
}

/**
 * @brief Ai助聊json配置设定接口。内核会记忆Ai助聊配置最后一次设定信息
 * @param json_str 配置的json字符串(来自通知中心)
 * @param notice_key 配置通知中心key
 */
void jni_cfg_set_aiwords_json(JNIEnv* env, jobject obj, jstring json_str, jstring notice_key)
{
    if (g_ipt_main._config_items == nullptr || json_str == nullptr || notice_key == nullptr)
    {
        return;
    }
    jboolean isCopy = JNI_FALSE;
    const char* json_char = env->GetStringUTFChars(json_str, &isCopy);
    Uint32 json_len = env->GetStringUTFLength(json_str);

    const char* notice_char = env->GetStringUTFChars(notice_key, &isCopy);
    Uint32 notice_len = env->GetStringUTFLength(notice_key);

    g_ipt_main._config_items->cfg_set_aiwords_json(json_char, json_len, notice_char, notice_len);

    env->ReleaseStringUTFChars(json_str, json_char);
    env->ReleaseStringUTFChars(notice_key, notice_char);
}

void jni_cfg_set_trace_warning_size(JNIEnv* env, jobject obj, jint base, jint increment)
{
    if (g_ipt_main._config_items == nullptr)
    {
        return;
    }
    return g_ipt_main._config_items->cfg_set_trace_warning_size(base, increment);
}

JNIEXPORT void JNICALL jni_cfg_set_collect_input_statistics(JNIEnv* env, jobject obj, jboolean enable)
{
    if (g_ipt_main._config_items == NULL)
    {
        return;
    }
    g_ipt_main._config_items->cfg_set_collect_input_statistics(enable);
}

/**
 * 设置云输入地址
 */
void jni_cfg_set_cloud_address(JNIEnv* env, jobject obj,
                               jobjectArray j_host_array, jintArray j_port_array,
                               jboolean cloud_use_udp, jboolean sug_use_udp)
{
    if (g_ipt_main._config_items == nullptr)
    {
        return;
    }

    auto jcloud_http_host = (jstring) (env->GetObjectArrayElement(j_host_array, 0));
    auto jcloud_udp_host = (jstring) (env->GetObjectArrayElement(j_host_array, 1));
    auto jsug_http_host = (jstring) (env->GetObjectArrayElement(j_host_array, 2));
    auto jsug_udp_host = (jstring) (env->GetObjectArrayElement(j_host_array, 3));
    auto jnlp_http_host = (jstring) (env->GetObjectArrayElement(j_host_array, 4));

    jint* j_ports = env->GetIntArrayElements(j_port_array, nullptr);
    Uint32 cloud_http_port = j_ports[0];
    Uint32 cloud_udp_port = j_ports[1];
    Uint32 sug_http_port = j_ports[2];
    Uint32 sug_udp_port = j_ports[3];
    Uint32 nlp_http_port = j_ports[4];

    const char* cloud_http_host = env->GetStringUTFChars(jcloud_http_host, nullptr);
    const char* cloud_udp_host = env->GetStringUTFChars(jcloud_udp_host, nullptr);
    const char* sug_http_host = env->GetStringUTFChars(jsug_http_host, nullptr);
    const char* sug_udp_host = env->GetStringUTFChars(jsug_udp_host, nullptr);
    const char* nlp_http_host = env->GetStringUTFChars(jnlp_http_host, nullptr);

    // kv没有http服务器，内核的接口有问题
    g_ipt_main._config_items->cfg_set_cloud_address(cloud_http_host, (Uint32) cloud_http_port,
                                                    cloud_udp_host, (Uint32) cloud_udp_port,
                                                    sug_http_host, (Uint32) sug_http_port,
                                                    sug_udp_host, (Uint32) sug_udp_port,
                                                    nlp_http_host, (Uint32) nlp_http_port,
                                                    cloud_use_udp, sug_use_udp);
    env->ReleaseStringUTFChars(jcloud_http_host, cloud_http_host);
    env->ReleaseStringUTFChars(jcloud_udp_host, cloud_udp_host);
    env->ReleaseStringUTFChars(jsug_http_host, sug_http_host);
    env->ReleaseStringUTFChars(jsug_udp_host, sug_udp_host);
    env->ReleaseStringUTFChars(jnlp_http_host, nlp_http_host);
}

void jni_cfg_set_scene_group_ids(JNIEnv* env, jobject obj, jlongArray arr)
{
    if (g_ipt_main._config_items == NULL)
    {
        return;
    }

    jsize len = env->GetArrayLength(arr);
    jlong* ids = nullptr;
    if (len != 0)
    {
        ids = env->GetLongArrayElements(arr, 0);
    }
    g_ipt_main._config_items->cfg_set_scene_group_ids(reinterpret_cast<Int64*>(ids), len);

    if (ids != nullptr) {
        env->ReleaseLongArrayElements(arr, ids, 0);
    }
}

void jni_cfg_set_pad_sym_ext_auto_return(JNIEnv* env, jobject obj, jboolean is_on)
{
    if (g_ipt_main._config_items == NULL)
    {
        return;
    }
    return g_ipt_main._config_items->cfg_set_pad_sym_ext_auto_return(is_on);
}

void jni_cfg_set_display_candtype(JNIEnv* env, jobject obj, jboolean is_on)
{
    if (g_ipt_main._config_items == NULL)
    {
    return;
    }
    return g_ipt_main._config_items->cfg_set_display_candtype(is_on);
}

jboolean jni_cfg_get_pad_sym_ext_auto_return(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._config_items == NULL)
    {
        return JNI_FALSE;
    }
    return g_ipt_main._config_items->cfg_get_pad_sym_ext_auto_return();
}

jboolean jni_cfg_get_display_candtype(JNIEnv* env, jobject obj)
{
if (g_ipt_main._config_items == NULL)
{
return JNI_FALSE;
}
return g_ipt_main._config_items->cfg_get_display_candtype();
}

void jni_cfg_set_cloud_intention(JNIEnv* env, jobject obj, jint type, jboolean on){

    if (g_ipt_main._config_items == NULL)
    {
        return;
    }

    g_ipt_main._config_items->cfg_set_cloud_intention(static_cast<iptcore::ConfigItems::CloudIntention>(type),on);
}


jint jni_cfg_get_cloud_intention(JNIEnv* env, jobject obj ){

    if (g_ipt_main._config_items == NULL)
    {
        return 0 ;
    }
    return g_ipt_main._config_items->cfg_get_cloud_intention();
}

void jin_cfg_set_contact_permission_status(JNIEnv* env, jobject obj, jboolean on){

    if (g_ipt_main._config_items == NULL)
    {
        return;
    }

    g_ipt_main._config_items->cfg_set_contact_permission_status(on);
}

/**
 * 设置上传数据请求地址
 */
void jni_cfg_set_upl_data_address(JNIEnv* env, jobject obj, jstring address, jint port) {
    if (g_ipt_main._config_items == nullptr || address == nullptr) {
        return;
    }

    const char* upload_data_host = env->GetStringUTFChars(address, nullptr);
    Uint32 upload_data_port = port;
    // 内核上报数据的接口地址
    g_ipt_main._config_items->cfg_set_upl_data_address(upload_data_host, upload_data_port);
    env->ReleaseStringUTFChars(address, upload_data_host);
}

/**
 * 设置是否需要开启内核线上log，默认不开启
 */
void jni_set_online_log(JNIEnv * env, jobject obj, jint level)
{
    if (g_ipt_main._ipt_lib == nullptr) {
        return;
    }
    CORE_ONLINE_LOG online_log;
    switch (level) {
        case 1:
            online_log = CORE_ONLINE_LOG::CORE_ONLINE_LOG_ALL;
            break;
        case 2:
            online_log = CORE_ONLINE_LOG::CORE_ONLINE_LOG_TIMECOST;
            break;
        default:
            online_log = CORE_ONLINE_LOG::CORE_ONLINE_LOG_NONE;
            break;
    }
    g_ipt_main._ipt_lib->set_online_log(online_log);
}
