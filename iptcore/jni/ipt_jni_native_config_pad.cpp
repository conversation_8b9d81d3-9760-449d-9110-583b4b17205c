/*
 * ipt_jni_native_config_pad.cpp
 *
 *  Created on: 2019-02-22
 *      Author: cdf
 *      jni configpad接口
 */

#include "ipt_pub_jni.h"
#include "ipt_jni_main.h"
#include "ipt_jni_log.h"
#include "ipt_jni_gobal.h"
#include "_pub_handwrite.h"
#include <cstring>

#define g_ipt_main iptjni::IptJniGlobal::get_ipt_jni_main()

enum CellKey {

    /** 细胞词库 */
    CELL = 0,
    /** 关键字词库 */
    KWD_CELL,
    /** 专供Android6.5->7.0版本解决保留旧版不带精确/模糊功能的颜文字自造数据提供的安装接口 */
    KWD_EMOTION_CELL,
    /** idm词库 */
    IDM_CELL,
    /** 整句预测的词库 */
    SENT_PRE,
    /** 敏感词词库 */
    SENSITIVE_DICT = 5,
    /** 出词干预词库 */
    WORDOUT_CHECKER,
    /** 智能云词活动入口 */
    CLOUD_CAMPAIGN,
    /**通用触发词***/
    COMMON_TRIGGER_WORDS,
};

const Uint16 MAX_CT_NAME = 32;
const Uint32 MAX_LOG_LEN = 256;

jint jni_install_cell(JNIEnv* env, jobject obj, jint key, jstring path)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }

    const char* path_buffer = env->GetStringUTFChars(path, nullptr);
    Int32 ret = -1;

    switch (key) {
        case CellKey::CELL:
            ret = g_ipt_main._config_pad->cfg_cell_install(path_buffer);
            break;
        case CellKey::KWD_CELL:
            ret = g_ipt_main._config_pad->cfg_kwd_cell_install(path_buffer);
            break;
        case CellKey::KWD_EMOTION_CELL:
            ret = g_ipt_main._config_pad->cfg_kwd_emoticon_cell_install(path_buffer);
            break;
        case CellKey::IDM_CELL:
            ret = g_ipt_main._config_pad->cfg_idm_cell_install(path_buffer);
            break;
        case CellKey::SENT_PRE:
            ret = g_ipt_main._config_pad->cfg_sent_pre_install(path_buffer);
            break;
        case CellKey::SENSITIVE_DICT:
            ret = g_ipt_main._config_pad->cfg_install_cloud_sensitive_dict(path_buffer);
            break;
        case CellKey::WORDOUT_CHECKER:
            ret = g_ipt_main._config_pad->cfg_install_wordout_checker(path_buffer);
            break;
        case CellKey::CLOUD_CAMPAIGN:
            ret = g_ipt_main._config_pad->cfg_install_cloud_campaign(path_buffer);
            break;
        case CellKey::COMMON_TRIGGER_WORDS:
            ret = g_ipt_main._config_pad->cfg_install_common_triger_words(path_buffer);
            break;
        default:
            g_ipt_main.on_log_write("cell install NOT-IMPLEMENTED!", MAX_LOG_LEN);
            break;

    }

    env->ReleaseStringUTFChars(path, path_buffer);
    return ret;
}

jint jni_uninstall_cell(JNIEnv* env, jobject obj, jint key, jint id)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }

    Int32 ret = -1;
    switch (key) {
        case CellKey::CELL:
            ret = g_ipt_main._config_pad->cfg_cell_uninstall(id);
            break;
        case CellKey::KWD_CELL:
            ret = g_ipt_main._config_pad->cfg_kwd_cell_uninstall(id);
            break;
        case CellKey::IDM_CELL:
            ret = g_ipt_main._config_pad->cfg_idm_cell_uninstall(id);
            break;
        case CellKey::SENT_PRE:
            ret = g_ipt_main._config_pad->cfg_sent_pre_uninstall();
            break;
        case CellKey::SENSITIVE_DICT:
            g_ipt_main._config_pad->cfg_uninstall_cloud_sensitive_dict();
            ret = 0;
            break;
        case CellKey::CLOUD_CAMPAIGN:
            ret = g_ipt_main._config_pad->cfg_uninstall_cloud_campaign();
            break;
        case CellKey::COMMON_TRIGGER_WORDS:
            g_ipt_main._config_pad->cfg_uninstall_common_triger_words();
            ret = 0;
            break;
        default:
            g_ipt_main.on_log_write("cell uninstall NOT-IMPLEMENTED!", MAX_LOG_LEN);
            break;

    }

    return ret;

}

/**
 * 获取个性短语分组数量
 * @return 分组数量。<0表示出错
 */
jint jni_cfg_get_phrase_group_count(JNIEnv* env, jobject obj)
{
    Int32 ret = -1;
    if (g_ipt_main._config_pad != NULL)
    {
        ret = g_ipt_main._config_pad->cfg_get_phrase_group_count();
    }
    return ret;
}
/**
 * 获取个性短语分组内容
 * @return 成功返回0，失败返回-1
 */
jint jni_cfg_get_phrase_group(JNIEnv* env, jobject obj, jint idx, jobject o_phrase_group)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }
    const iptcore::PhraseGroup* phraseGroup = g_ipt_main._config_pad->cfg_get_phrase_group_by_idx(idx);
    return g_ipt_main.set_phrase_group_info_data(env, phraseGroup, o_phrase_group);
}
/**
 * 添加个性短语分组
 * @return <0表示失败
 */
jint jni_cfg_set_add_phrase_group(JNIEnv* env, jobject obj, jstring name)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }
    jboolean is_copy = false;
    Uint32 name_len = env->GetStringLength(name);
    const jchar* name_jchar = env->GetStringChars(name, &is_copy);
    Int32 ret = g_ipt_main._config_pad->cfg_add_phrase_group((Uint16*) name_jchar, name_len);
    env->ReleaseStringChars(name, name_jchar);
    return ret;
}

/**
 * 删除个性短语分组
 * @return <0表示失败
 */
jint jni_cfg_set_delete_phrase_group(JNIEnv* env, jobject obj, jint idx)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }
    return g_ipt_main._config_pad->cfg_delete_phrase_group(idx);
}

/**
 * 编辑个性短语分组内容
 * @return <0表示失败
 */
jint jni_cfg_set_edit_phrase_group(JNIEnv* env, jobject obj, jint idx, jstring name)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }
    jboolean is_copy = false;
    Uint32 name_len = env->GetStringLength(name);
    const jchar* name_buff = env->GetStringChars(name, &is_copy);
    Int32 ret = g_ipt_main._config_pad->cfg_edit_phrase_group(idx, (Uint16*) name_buff, name_len);
    env->ReleaseStringChars(name, name_buff);
    return ret;
}

/**
 * 启用/关闭个性短语分组
 * @return <0表示失败
 */
jint jni_cfg_set_enable_phrase_group(JNIEnv* env, jobject obj, jint idx, jboolean is_enable)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }
    return g_ipt_main._config_pad->cfg_enable_phrase_group(idx, is_enable);
}

/**
 * 获取个性短语数量
 * @return <0表示失败
 */
jint jni_cfg_get_phrase_item_count(JNIEnv* env, jobject obj, jint group_id, jstring code)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }

    jboolean isCopy = false;
    Int32 ret = -1;
    if (NULL != code)
    {
        jint code_len = env->GetStringLength(code);
        const char *code_buff = env->GetStringUTFChars(code, &isCopy);
        ret = g_ipt_main._config_pad->cfg_get_phrase_item_count((Uint32) group_id, (char*) code_buff,
                                                                (Uint32) code_len);
        env->ReleaseStringUTFChars(code, code_buff);
    } else {
        ret = g_ipt_main._config_pad->cfg_get_phrase_item_count((Uint32) group_id, NULL, 0);
    }
    return ret;
}

/**
 * 获取个性短语内容
 * @return 成功返回0，失败返回-1
 */
jint jni_cfg_get_phrase_item(JNIEnv* env, jobject obj, jint idx, jobject o_phrase_item)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }
    const iptcore::PhraseItem* phrase_item = g_ipt_main._config_pad->cfg_get_phrase_info((Uint32) idx);
    return g_ipt_main.set_phrase_item_info_data(env, phrase_item, o_phrase_item);
}

/**
 * 添加个性短语
 * @return 成功返回0，失败返回-1
 */
jint jni_cfg_set_add_phrase_item(JNIEnv* env, jobject obj,
                                       jstring code, jstring word, jint pos, jint group_id)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }
    jboolean is_copy = false;
    Uint32 code_len = env->GetStringLength(code);
    const char* code_buff = env->GetStringUTFChars(code, &is_copy);
    Uint32 word_len = env->GetStringLength(word);
    const jchar* word_buff = env->GetStringChars(word, &is_copy);
    Int32 ret = g_ipt_main._config_pad->cfg_add_phrase_item(code_buff, code_len, (Uint16*) word_buff,
                     word_len, pos, group_id);
    env->ReleaseStringUTFChars(code, code_buff);
    env->ReleaseStringChars(word, word_buff);
    return ret;
}

/**
 * 编辑个性短语
 * @return 成功返回0，失败返回-1
 */
jint jni_cfg_set_edit_phrase_item(JNIEnv* env, jobject obj,
                                    jint idx, jstring code, jstring word, jint pos)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }
    jboolean is_copy = false;
    Uint32 code_len = env->GetStringLength(code);
    const char* code_buff = env->GetStringUTFChars(code, &is_copy);
    Uint32 word_len = env->GetStringLength(word);
    const jchar* word_buff = env->GetStringChars(word, &is_copy);

    Int32 ret = g_ipt_main._config_pad->cfg_edit_phrase_item(idx, code_buff, code_len,
                        (Uint16*) word_buff, word_len, pos);
    env->ReleaseStringUTFChars(code, code_buff);
    env->ReleaseStringChars(word, word_buff);
    return ret;
}

/**
 * 删除个性短语分组
 * @return <0表示失败
 */
jint jni_cfg_set_delete_phrase_item(JNIEnv* env, jobject obj, jint idx)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }
    return g_ipt_main._config_pad->cfg_delete_phrase_item(idx);
}

/**
 * 导入个性短语
 * @return 0表示成功，<0表示失败
 */
jint jni_cfg_phrase_import(JNIEnv* env, jobject obj, jstring jfilename, jboolean overwrite)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }
    jboolean isCopy = JNI_FALSE;
    Uint32 isOverwrite = overwrite ? 1 : 0;
    const char* file_name = env->GetStringUTFChars(jfilename, &isCopy);
    Int32 ret = g_ipt_main._config_pad->cfg_phrase_import(file_name, isOverwrite);
    env->ReleaseStringUTFChars(jfilename, file_name);
    return ret;
}

/**
 * 导出个性短语
 * @return 0表示成功，<0表示失败
 */
jint jni_cfg_phrase_export(JNIEnv* env, jobject obj, jstring jfileName, jint group_id)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }
    jboolean isCopy = JNI_FALSE;
    const char* fileName = env->GetStringUTFChars(jfileName, &isCopy);
    Int32 ret = g_ipt_main._config_pad->cfg_phrase_export(fileName, group_id);
    env->ReleaseStringUTFChars(jfileName, fileName);
    return ret;
}

/**
 * 清理自造词
 */
jint jni_cfg_usword_reduce(JNIEnv* env, jobject obj, jint percent)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }
    return g_ipt_main._config_pad->cfg_usword_reduce((Uint32) percent);
}

/**
 * 获取自造词文件大小
 */
jint jni_cfg_usword_getsize(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }
    return g_ipt_main._config_pad->cfg_usword_getsize();
}

/**
 * 导入中文自造词
 */
jint jni_cfg_usword_import(JNIEnv* env, jobject obj, jstring file_path)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }
    jboolean isCopy = JNI_FALSE;
    const char* path_buff = env->GetStringUTFChars(file_path, &isCopy);
    Int32 ret = g_ipt_main._config_pad->cfg_usword_import(path_buff);
    env->ReleaseStringUTFChars(file_path, path_buff);
    return ret;
}

/**
 * 导出中文自造词
 */
jint jni_cfg_usword_export(JNIEnv* env, jobject obj, jstring file_path)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }
    jboolean isCopy = JNI_FALSE;
    const char* path_buff = env->GetStringUTFChars(file_path, &isCopy);
    Int32 ret = g_ipt_main._config_pad->cfg_usword_export(path_buff);
    env->ReleaseStringUTFChars(file_path, path_buff);
    return ret;
}

/**
 * 导入英文自造词
 */
jint jni_cfg_ueword_import(JNIEnv* env, jobject obj, jstring file_path)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }
    jboolean isCopy = JNI_FALSE;
    const char* path_buff = env->GetStringUTFChars(file_path, &isCopy);
    Int32 ret = g_ipt_main._config_pad->cfg_ueword_import(path_buff);
    env->ReleaseStringUTFChars(file_path, path_buff);
    return ret;
}

/**
 * 导出英文自造词
 */
jint jni_cfg_ueword_export(JNIEnv* env, jobject obj, jstring file_path)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }
    jboolean isCopy = JNI_FALSE;
    const char* path_buff = env->GetStringUTFChars(file_path, &isCopy);
    Int32 ret = g_ipt_main._config_pad->cfg_ueword_export(path_buff);
    env->ReleaseStringUTFChars(file_path, path_buff);
    return ret;
}

/**
 * 导出生僻字用户词库的明文
 */
jint jni_cfg_rare_user_word_export(JNIEnv* env, jobject obj, jstring file_path)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }
    jboolean isCopy = JNI_FALSE;
    const char* path_buff = env->GetStringUTFChars(file_path, &isCopy);
    Int32 ret = g_ipt_main._config_pad->cfg_rare_user_word_export(path_buff);
    env->ReleaseStringUTFChars(file_path, path_buff);
    return ret;
}

/**
 * 获取生僻字手写版本号
 * @return <0表示失败
 */
jint jni_cfg_get_hw_rare_version(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }
    return g_ipt_main._config_pad->cfg_get_hw_rare_version();
}


/**
 * 获取细胞词库数量
 */
jint jni_cfg_get_cell_count(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }
    return g_ipt_main._config_pad->cfg_get_cell_count();
}

/**
 * 按索引获取细胞词库内容
 */
jint jni_cfg_get_cell_info_by_index(JNIEnv* env, jobject obj, jint idx, jobject o_cell_info)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }
    iptcore::CellInfo* cellinfo = g_ipt_main._config_pad->cfg_get_cell_info_by_index(idx);
    return g_ipt_main.set_cell_info_data(env, cellinfo, o_cell_info);
}
/**
 * 按cellid获取细胞词库内容
 */
jint jni_cfg_get_cell_info_by_cellid(JNIEnv* env, jobject obj, jint cellid, jobject o_cell_info)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }
    iptcore::CellInfo* cellinfo = g_ipt_main._config_pad->cfg_get_cell_info_by_cellid(cellid);
    return g_ipt_main.set_cell_info_data(env, cellinfo, o_cell_info);
}

/**
 * 设置地理位置词库类型
 */
jint jni_cfg_cell_set_loc_type(JNIEnv* env, jobject obj, jint cellid, jint loc_type)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }
    return g_ipt_main._config_pad->cfg_cell_set_loc_type(cellid, loc_type);
}

/**
 * 设置细胞词库安装时间
 */
jint jni_cfg_cell_set_install_time(JNIEnv* env, jobject obj, jint cellid, jint install_time)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }
    return g_ipt_main._config_pad->cfg_cell_set_install_time(cellid, install_time);
}

/**
 * 获取流行词库cellid
 */
jint jni_cfg_cell_get_popword_cellid(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }
    return g_ipt_main._config_pad->cfg_cell_get_popword_cellid();
}

/**
 * 获取系统词库cellid
 */
jint jni_cfg_cell_get_sysword_cellid(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }
    return g_ipt_main._config_pad->cfg_cell_get_sysword_cellid();
}

///**
// * 查询系统词库版本号
// */
//jint jni_cfg_cell_get_sysword_ver(JNIEnv* env, jobject obj)
//{
//    if (g_ipt_main._config_pad == NULL)
//    {
//        return 0;
//    }
//    return g_ipt_main._config_pad->cfg_cell_get_sysword_ver();
//}

/**
 * 获取云白名单版本号
 */
jint jni_cfg_cell_get_cloud_white_ver(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return 0;
    }
    return g_ipt_main._config_pad->cfg_cell_get_cloud_white_ver();
}

/**
 * 启用/关闭细胞词库
 */
jint jni_cfg_cell_enable(JNIEnv* env, jobject obj, jint cell_id, jboolean is_enable)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }
    return g_ipt_main._config_pad->cfg_cell_enable(cell_id, (Uint32) is_enable);
}

/**
 * 获取最近产生的自造词数量
 */
jint jni_cfg_usall_get_recent_count(JNIEnv* env, jobject obj, jint days)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }
    return g_ipt_main._config_pad->cfg_usall_get_recent_count(days);
}

///**
// * 导入所有同步类词
// */
//jint jni_cfg_usall_import(JNIEnv* env, jobject obj, jstring filepath)
//{
//    if (g_ipt_main._config_pad == NULL)
//    {
//        return -1;
//    }
//    jboolean isCopy = JNI_FALSE;
//    const char* filepath_buff = env->GetStringUTFChars(filepath, &isCopy);
//    Uint32 ret = g_ipt_main._config_pad->cfg_usall_import(filepath_buff);
//    env->ReleaseStringUTFChars(filepath, filepath_buff);
//    return ret;
//}

///**
// * 导出所有同步类词
// */
//jint jni_cfg_usall_export(JNIEnv* env, jobject obj, jstring filepath)
//{
//    if (g_ipt_main._config_pad == NULL)
//    {
//        return -1;
//    }
//    jboolean isCopy = JNI_FALSE;
//    const char* filepath_buff = env->GetStringUTFChars(filepath, &isCopy);
//    Uint32 ret = g_ipt_main._config_pad->cfg_usall_export(filepath_buff);
//    env->ReleaseStringUTFChars(filepath, filepath_buff);
//    return ret;
//}

/**
 * 获取关键词词库数量
 */
jint jni_cfg_get_kwd_cell_count(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }
    return g_ipt_main._config_pad->cfg_get_kwd_cell_count();
}

/**
 * 按索引获取关键词词库内容
 */
jint jni_cfg_get_kwd_cell_info_by_index(JNIEnv* env, jobject obj, jint idx, jobject o_kwd_cell_info)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }
    iptcore::KwdCellInfo* kwd_info = g_ipt_main._config_pad->cfg_get_kwd_cell_info_by_index(idx);
    return g_ipt_main.set_kwd_info_data(env, kwd_info, o_kwd_cell_info);

}

/**
 * 按关键词获取细胞词库内容
 */
jint jni_cfg_get_kwd_cell_info_by_cellid(JNIEnv* env, jobject obj, jint cellid, jobject o_kwd_cell_info)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }
    iptcore::KwdCellInfo* kwd_info = g_ipt_main._config_pad->cfg_get_kwd_cell_info_by_cellid(cellid);
    return g_ipt_main.set_kwd_info_data(env, kwd_info, o_kwd_cell_info);
}

/**
 * 导出关键词词库内容
 */
jint jni_cfg_kwd_export(JNIEnv* env, jobject obj, jstring file_path)
{
    if (g_ipt_main._config_pad == NULL || file_path == NULL)
    {
        return -1;
    }
    jboolean isCopy = JNI_FALSE;
    const char *filepath_buff = env->GetStringUTFChars(file_path, &isCopy);
    Int32 ret = g_ipt_main._config_pad->cfg_kwd_export(filepath_buff);
    env->ReleaseStringUTFChars(file_path, filepath_buff);
    return ret;
}

/**
 * 获取idm词库数量
 */
jint jni_cfg_get_idm_cell_count(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }
    return g_ipt_main._config_pad->cfg_get_idm_cell_count();
}

/**
 * 按索引获取idm词库内容
 */
jint jni_cfg_get_idm_cell_info_by_index(JNIEnv* env, jobject obj, jint idx, jobject o_idm_cell_info)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }
    iptcore::IdmCellInfo* idm_info = g_ipt_main._config_pad->cfg_get_idm_cell_info_by_index(idx);
    return g_ipt_main.set_idm_info_data(env, idm_info, o_idm_cell_info);
}

/**
 * 压缩手写数据
 */
jbyteArray jni_cfg_hw_encode_point(JNIEnv* env, jobject obj, jint candIdx)
{
    // 5g内核删除
    return NULL;
//    if (g_ipt_main._config_pad == NULL)
//    {
//        return NULL;
//    }
//    jbyteArray encodesArray = NULL;
//    Uint32 encodesLen = 0;
//    Uint8 *encodes = g_ipt_main._config_pad->cfg_hw_encode_point(candIdx, &encodesLen);
//
//    if (encodes != NULL && encodesLen > 0) {
//        encodesArray = env->NewByteArray(encodesLen);
//        env->SetByteArrayRegion(encodesArray, 0, encodesLen, (const jbyte *) encodes);
//    }
//    return encodesArray;
}

/**
 * 查询指定字符的拼音
 */
jstring jni_cfg_util_getHW_Py(JNIEnv* env, jobject obj, jchar unicode, jint isall)
{
    jstring pinyin = NULL;
    if (g_ipt_main._config_pad == NULL)
    {
        return pinyin;
    }

    Uint16 pystrs[64];
    memset(pystrs, 0, sizeof(Uint8) * 64);
    int ret = g_ipt_main._config_pad->cfg_util_get_hw_py((Uint16) unicode, (Uint16*) pystrs, (Uint8) isall);

    if (ret > 0)
    {
        pinyin = g_ipt_main.uni_to_str(env, pystrs);
    }
    return pinyin;
}

/**
 * 获取双拼声母映射
 */
jint jni_cfg_util_get_spmap_sheng(JNIEnv* env, jobject obj, jbyte keychar, jbyteArray sp_list)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }
    jboolean isCopy = JNI_FALSE;
    jint ret = -1;
    jbyte* sheng_list = env->GetByteArrayElements(sp_list, &isCopy);
    ret = g_ipt_main._config_pad->cfg_util_get_spmap_sheng((char) keychar, (char*) sheng_list);
    env->ReleaseByteArrayElements(sp_list, sheng_list, 0);
    return ret;
}

/**
 * 获取双拼韵母映射
 */
jint jni_cfg_util_get_spmap_yun(JNIEnv* env, jobject obj, jbyte keychar, jbyteArray sp_list)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }
    jboolean isCopy = JNI_FALSE;
    jint ret = -1;
    jbyte* yun_list = env->GetByteArrayElements(sp_list, &isCopy);
    ret = g_ipt_main._config_pad->cfg_util_get_spmap_yun((char) keychar, (char*) yun_list);
    env->ReleaseByteArrayElements(sp_list, yun_list, 0);
    return ret;
}


/**
 * 清空键盘映射表
 */
void jni_cfg_keymap_clean(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return;
    }
    g_ipt_main._ipt_pad->cfg_keymap_clean();
}

/**
 * 添加键盘映射
 */
void jni_cfg_keymap_addchar(JNIEnv* env, jobject obj, jint input, jchar jchar_code, jint level)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return;
    }
    g_ipt_main._ipt_pad->cfg_keymap_addchar((char) input, (char) jchar_code, (Uint8) level);
}

/**
 * 导出所有的联系人名字，用于隐私合规
 * @return 联系人的数据，格式为pb
 */
jbyteArray jni_cfg_contact_export_names(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return NULL;
    }

    Uint32 len = 0;
    const Uint8* contact_ptr = g_ipt_main._config_pad->cfg_contact_export_names(len);
    if(len > 0)
    {
        jbyteArray contact_data = (jbyteArray)env->NewByteArray(len);
        env->SetByteArrayRegion(contact_data, 0, len, (const jbyte*)contact_ptr);
        return contact_data;
    }

    return NULL;
}



/**
 * ** 已废弃 **
 * 清空所有联系人
 * @return 清空成功返回0; 失败返回-1
 */
jint jni_cfg_contact_reset(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }
    return g_ipt_main._config_pad->cfg_contact_reset();
}

/**
 * ** 已废弃 **
 * 添加属性(必须先定义属性)
 * @param attr 联系人的属性名
 * @return 若添加成功, 返回分配到的属性id号; 若添加失败, 返回-1
 */
jint jni_cfg_contact_append_attri(JNIEnv* env, jobject obj, jstring attr)
{
    // 5G内核删除
    return -1;
//    if (g_ipt_main._config_pad == NULL)
//    {
//        return -1;
//    }
//    jboolean isCopy = JNI_FALSE;
//    const jchar* attr_jchar = env->GetStringChars(attr, &isCopy);
//    jint attr_len = env->GetStringLength(attr);
//    Uint16 attr_buff[attr_len + 1];
//    g_ipt_main.jchar_to_wchar(attr_jchar, attr_buff, attr_len);
//    Int32 ret = g_ipt_main._config_pad->cfg_contact_append_attri(attr_buff);
//    env->ReleaseStringChars(attr, attr_jchar);
//    return ret;
}

/**
 * ** 已废弃 **
 * 添加联系人信息
 * @param name — 联系人姓名
 * @param attr — 联系人的属性名
 * @param value — 联系人的属性值
 * @return 若添加成功, 返回0; 若添加失败, 返回-1
 */
jint jni_cfg_contact_append_value(JNIEnv* env, jobject obj, jstring name, jstring attr, jstring value)
{
    // 5G内核删除
    return -1;
//    if (g_ipt_main._config_pad == NULL || name == NULL || attr == NULL || value == NULL)
//    {
//        return -1;
//    }
//    jboolean isCopy = JNI_FALSE;
//    const jchar* name_jchar = env->GetStringChars(name, &isCopy);
//    const jchar* attr_jchar = env->GetStringChars(attr, &isCopy);
//    const jchar* value_jchar = env->GetStringChars(value, &isCopy);
//    jint name_len = env->GetStringLength(name);
//    jint attr_len = env->GetStringLength(attr);
//    jint value_len = env->GetStringLength(value);
//    Uint16 name_buff[name_len + 1];
//    Uint16 attr_buff[attr_len + 1];
//    Uint16 value_buff[value_len + 1];
//    g_ipt_main.jchar_to_wchar(name_jchar, name_buff, name_len);
//    g_ipt_main.jchar_to_wchar(attr_jchar, attr_buff, attr_len);
//    g_ipt_main.jchar_to_wchar(value_jchar, value_buff, value_len);
//    Int32 ret = g_ipt_main._config_pad->cfg_contact_append_value(name_buff, attr_buff, value_buff);
//    env->ReleaseStringChars(name, name_jchar);
//    env->ReleaseStringChars(attr, attr_jchar);
//    env->ReleaseStringChars(value, value_jchar);
//    return ret;
}

/**
 * 逐条导入联系人数据,数据字典准备
 */
jint jni_cfg_contact_build_start(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }
    return g_ipt_main._config_pad->cfg_contact_build_start();
}

/**
 * 逐条导入联系人数据，插入联系人名称
 */
jint jni_cfg_contact_build_add_name(JNIEnv* env, jobject obj, jstring name)
{
    if (g_ipt_main._config_pad == NULL || name == NULL)
    {
        return -1;
    }
    jboolean isCopy = JNI_FALSE;
    const jchar* name_jchar = env->GetStringChars(name, &isCopy);
    jint name_len = env->GetStringLength(name);
    Uint16 name_buff[name_len + 1];
    g_ipt_main.jchar_to_wchar(name_jchar, name_buff, name_len);
    Int32 ret = g_ipt_main._config_pad->cfg_contact_build_add_name(name_buff);
    env->ReleaseStringChars(name, name_jchar);
    return ret;
}
/**
 * 逐条导入联系人数据，为联系人添加属性和value
 */
jint jni_cfg_contact_build_add_value(JNIEnv* env, jobject obj, jstring attr, jstring value)
{
    if (g_ipt_main._config_pad == NULL || attr == NULL || value == NULL)
    {
        return -1;
    }
    jboolean isCopy = JNI_FALSE;
    const jchar* attr_jchar = env->GetStringChars(attr, &isCopy);
    const jchar* value_jchar = env->GetStringChars(value, &isCopy);
    jint attr_len = env->GetStringLength(attr);
    jint value_len = env->GetStringLength(value);
    Uint16 attr_buff[attr_len + 1];
    Uint16 value_buff[value_len + 1];
    g_ipt_main.jchar_to_wchar(attr_jchar, attr_buff, attr_len);
    g_ipt_main.jchar_to_wchar(value_jchar, value_buff, value_len);
    Int32 ret = g_ipt_main._config_pad->cfg_contact_build_add_value(attr_buff, value_buff);
    env->ReleaseStringChars(attr, attr_jchar);
    env->ReleaseStringChars(value, value_jchar);
    return ret;
}

/**
 * 逐条导入联系人数据,导入结束，保存联系人词典
 */
jint jni_cfg_contact_build_end(JNIEnv* env, jobject obj, jboolean is_retain_black)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }
    Uint32 retain_black_flag = is_retain_black ? 1 : 0;
    return g_ipt_main._config_pad->cfg_contact_build_end(retain_black_flag);
}

/**
 * 覆蓋安裝时客户端上传以拉黑的联系人
 */
jint jni_cfg_contact_build_add_black_name(JNIEnv* env, jobject obj, jstring jdelName)
{
    if (g_ipt_main._config_pad == NULL || jdelName == NULL)
    {
        return -1;
    }
    jboolean isCopy = JNI_FALSE;
    const jchar* name_jchar = env->GetStringChars(jdelName, &isCopy);
    jint name_len = env->GetStringLength(jdelName);
    Uint16 name_buff[name_len + 1];
    g_ipt_main.jchar_to_wchar(name_jchar, name_buff, name_len);
    Int32 ret = g_ipt_main._config_pad->cfg_contact_build_add_black_name(name_buff, (Uint32) name_len);
    env->ReleaseStringChars(jdelName, name_jchar);
    return ret;
}

/**
 * 删除对应联系人
 * @param name — 联系人姓名
 * @param option — 删除类型    CMD_CONTACT_DEL  CMD_CONTACT_RESTORE_FREQ  CMD_CONTACT_DEL_ALL
 * @return 若删除成功, 返回0; 若删除失败, 返回-1;未找到要删除的项目，返回-2
 */
jint jni_cfg_contact_delete(JNIEnv* env, jobject obj, jstring name, jint option)
{
    // 5G内核删除
    return -1;
//    if (g_ipt_main._config_pad == NULL || name == NULL)
//    {
//        return -1;
//    }
//    jboolean isCopy = JNI_FALSE;
//    const jchar* name_jchar = env->GetStringChars(name, &isCopy);
//    jint name_len = env->GetStringLength(name);
//    Uint16 name_buff[name_len + 1];
//    g_ipt_main.jchar_to_wchar(name_jchar, name_buff, name_len);
//    Int32 ret = g_ipt_main._config_pad->cfg_contact_delete(name_buff, (Uint8) option);
//    env->ReleaseStringChars(name, name_jchar);
//    return ret;
}

/**
 * 语音查找联系人(非通讯录)
 */
jstring jni_cfg_contact_voice_find(JNIEnv* env, jobject obj, jstring oriword)
{
    if (g_ipt_main._config_pad == NULL || oriword == NULL)
    {
        return NULL;
    }
    jint ret;
    jstring str = NULL;
    jboolean isCopy = JNI_FALSE;
    jint size = env->GetStringLength(oriword);
    if (size > 0 && size < MAX_CT_NAME)
    {
        const jchar *tmpName = env->GetStringChars(oriword, &isCopy);
        Uint16 unicode[MAX_CT_NAME + 1];
        memset(unicode, 0, (MAX_CT_NAME + 1) * sizeof(Uint16));
        g_ipt_main.jchar_to_wchar(tmpName, unicode, size);

        Uint16 retunicode[MAX_CT_NAME + 1];
        memset(retunicode, 0, (MAX_CT_NAME + 1) * sizeof(Uint16));
        ret = g_ipt_main._config_pad->cfg_contact_voice_find(unicode, retunicode);
        if (ret > 0)
        {
            str = g_ipt_main.uni_to_str(env, retunicode);
        }
        env->ReleaseStringChars(oriword, tmpName);
    }
    return str;
}

/**
 * 语音查找联系人(通讯录)
 */
jstring jni_cfg_contact_voice_find_addressbook(JNIEnv* env, jobject obj, jstring oriword)
{
    if (g_ipt_main._config_pad == NULL || oriword == NULL)
    {
        return NULL;
    }
    jint ret;
    jstring str = NULL;
    jboolean isCopy = JNI_FALSE;
    jint size = env->GetStringLength(oriword);
    if (size > 0 && size < MAX_CT_NAME)
    {
        const jchar *tmpName = env->GetStringChars(oriword, &isCopy);
        Uint16 unicode[MAX_CT_NAME + 1];
        memset(unicode, 0, (MAX_CT_NAME + 1) * sizeof(Uint16));
        g_ipt_main.jchar_to_wchar(tmpName, unicode, size);

        Uint16 retunicode[MAX_CT_NAME + 1];
        memset(retunicode, 0, (MAX_CT_NAME + 1) * sizeof(Uint16));
        ret = g_ipt_main._config_pad->cfg_contact_voice_find_addressbook(unicode, retunicode);
        if (ret > 0)
        {
            str = g_ipt_main.uni_to_str(env, retunicode);
        }
        env->ReleaseStringChars(oriword, tmpName);
    }
    return str;
}

/**
 * 导出老内核的个性短语
 */
jint jni_cfg_old_cp_export(JNIEnv* env, jobject obj, jstring jinputFile, jstring joutputFile)
{
    // 5G内核删除
    return -1;
//    if (g_ipt_main._config_pad == NULL || jinputFile == NULL || joutputFile == NULL)
//    {
//        return -1;
//    }
//    jboolean isCopy = JNI_FALSE;
//    jint ret = -1;
//    const char* inputFile = env->GetStringUTFChars(jinputFile, &isCopy);
//    const char* outputFile = env->GetStringUTFChars(joutputFile, &isCopy);
//    ret = g_ipt_main._config_pad->cfg_old_cp_export(inputFile, outputFile);
//    env->ReleaseStringUTFChars(jinputFile, inputFile);
//    env->ReleaseStringUTFChars(joutputFile, outputFile);
//    return ret;
}


/**
 * 获取搜索关键字版本
 */
jint jni_cfg_search_get_version(JNIEnv* env, jobject obj)
{
    // 5G内核删除
    return -1;
//    if (g_ipt_main._config_pad == NULL)
//    {
//        return -1;
//    }
//    return g_ipt_main._config_pad->cfg_search_get_version();
}

/**
 * 查询ACS搜索词结果
 */
jintArray jni_cfg_search_find(JNIEnv* env, jobject obj, jstring queryWord)
{
    // 5G内核删除
    return NULL;
//    if (g_ipt_main._config_pad == NULL || queryWord == NULL)
//    {
//        return NULL;
//    }
//
//    jboolean isCopy = JNI_FALSE;
//    const jchar * query_str = env->GetStringChars(queryWord, &isCopy);
//    jint queryLen = env->GetStringLength(queryWord);
//    auto result =  (jintArray) env->NewIntArray(2);
//    Uint16 wordIdx = 0;
//    Uint32 wordLen = 0;
//    jint ret = (jint) g_ipt_main._config_pad->cfg_search_find((Uint16 *)(query_str),
//                                 (Uint32) queryLen, &wordIdx, &wordLen);
//    LOGI("searchFind: ret=%d, wordIdx=%d, wordLen=%d", ret, wordIdx, wordLen);
//    env->SetIntArrayRegion(result, 0, 1, (const jint*) &wordIdx);
//    env->SetIntArrayRegion(result, 1, 1, (const jint*) &wordLen);
//
//    env->ReleaseStringChars(queryWord, query_str);
//
//    return result;
}

/**
 * 导出老内核的英文自造词
 */
jint jni_cfg_old_ue_export(JNIEnv* env, jobject obj, jstring jinputFile, jstring joutputFile)
{
    // 5G内核删除
    return -1;
//    if (g_ipt_main._config_pad == NULL || jinputFile == NULL || joutputFile == NULL)
//    {
//        return -1;
//    }
//    jboolean isCopy = JNI_FALSE;
//    jint ret = -1;
//    const char* inputFile = env->GetStringUTFChars(jinputFile, &isCopy);
//    const char* outputFile = env->GetStringUTFChars(joutputFile, &isCopy);
//    ret = g_ipt_main._config_pad->cfg_old_ue_export(inputFile, outputFile);
//    env->ReleaseStringUTFChars(jinputFile, inputFile);
//    env->ReleaseStringUTFChars(joutputFile, outputFile);
//    return ret;
}

/**
 * 注音自造词导入
 */
jint jni_cfg_import_zyword(JNIEnv* env, jobject obj, jstring jfileName)
{
    if (g_ipt_main._config_pad == NULL || jfileName == NULL)
    {
        return -1;
    }
    jboolean isCopy = JNI_FALSE;
    jint ret = -1;
    const char* fileName = env->GetStringUTFChars(jfileName, &isCopy);
    ret = g_ipt_main._config_pad->cfg_import_zyword(fileName);
    env->ReleaseStringUTFChars(jfileName, fileName);
    return ret;
}

/**
 * 注音自造词导出
 */
jint jni_cfg_export_zyword(JNIEnv* env, jobject obj, jstring jfileName)
{
    if (g_ipt_main._config_pad == NULL || jfileName == NULL)
    {
        return -1;
    }
    jboolean isCopy = JNI_FALSE;
    jint ret = -1;
    const char* fileName = env->GetStringUTFChars(jfileName, &isCopy);
    ret = g_ipt_main._config_pad->cfg_export_zyword(fileName);
    env->ReleaseStringUTFChars(jfileName, fileName);
    return ret;
}

/**
 * 符号数据导入
 */
jint jni_cfg_sym_import(JNIEnv* env, jobject obj, jstring jfileName, jboolean isOverWrite)
{
    if (g_ipt_main._config_pad == NULL || jfileName == NULL)
    {
        return -1;
    }
    jboolean isCopy = JNI_FALSE;
    jint ret = -1;
    const char* fileName = env->GetStringUTFChars(jfileName, &isCopy);
    ret = g_ipt_main._config_pad->cfg_sym_import(fileName, isOverWrite == JNI_TRUE ? 1 : 0);
    env->ReleaseStringUTFChars(jfileName, fileName);
    return ret;
}

/**
 * 符号数据导出
 */
jint jni_cfg_sym_export(JNIEnv* env, jobject obj, jstring jfileName)
{
    if (g_ipt_main._config_pad == NULL || jfileName == NULL)
    {
        return -1;
    }
    jboolean isCopy = JNI_FALSE;
    jint ret = -1;
    const char* fileName = env->GetStringUTFChars(jfileName, &isCopy);
    ret = g_ipt_main._config_pad->cfg_sym_export(fileName);
    env->ReleaseStringUTFChars(jfileName, fileName);
    return ret;
}

/**
 * 安卓用户『最近』符号数据导入
 */
jint jni_cfg_usr_sym_import(JNIEnv* env, jobject obj, jobjectArray jsyms)
{
    if (g_ipt_main._config_pad == NULL || jsyms == NULL)
    {
        return -1;
    }

    const int MAX_SYM_LEN = 32;

    int length = env->GetArrayLength(jsyms);;
    const Uint16 **syms = new const Uint16 *[length];
    Uint16 arr[length][MAX_SYM_LEN + 1];
    jstring sym;
    jint len;
    jboolean isCopy = JNI_FALSE;

    for (int i = 0; i < length; i++) {
        sym = (jstring) env->GetObjectArrayElement(jsyms, i);
        if (sym == NULL) {
            arr[i][0] = '\0';
            syms[i] = arr[i];
        } else {
            len = env->GetStringLength(sym);
            const jchar *data = env->GetStringChars(sym, &isCopy);
            len = len > MAX_SYM_LEN ? MAX_SYM_LEN : len;
            g_ipt_main.jchar_to_wchar(data, arr[i], len);
            syms[i] = arr[i];
            env->ReleaseStringChars(sym, data);
        }
    }

    return g_ipt_main._config_pad->cfg_usr_sym_import(syms, (Uint16) length);
}

/**
 * 符号联想数据导入
 */
jint jni_cfg_sylian_import(JNIEnv* env, jobject obj, jstring jfileName, jboolean isOverWrite)
{
    if (g_ipt_main._config_pad == NULL || jfileName == NULL)
    {
        return -1;
    }
    jboolean isCopy = JNI_FALSE;
    jint ret = -1;
    const char* fileName = env->GetStringUTFChars(jfileName, &isCopy);
    ret = g_ipt_main._config_pad->cfg_sylian_import(fileName, isOverWrite == JNI_TRUE ? 1 : 0);
    env->ReleaseStringUTFChars(jfileName, fileName);
    return ret;
}

/**
 * 导入VK自造词
 */
jint jni_cfg_vkword_import(JNIEnv* env, jobject obj, jstring jfileName, jboolean isOverWrite)
{
    if (g_ipt_main._config_pad == NULL || jfileName == NULL)
    {
        return -1;
    }
    jboolean isCopy = JNI_FALSE;
    jint ret = -1;
    const char* fileName = env->GetStringUTFChars(jfileName, &isCopy);
    ret = g_ipt_main._config_pad->cfg_vkword_import(fileName, isOverWrite == JNI_TRUE ? 1 : 0);
    env->ReleaseStringUTFChars(jfileName, fileName);
    return ret;
}

/**
 * 导出VK自造词
 */
jint jni_cfg_vkword_export(JNIEnv* env, jobject obj, jstring jfileName)
{
    if (g_ipt_main._config_pad == NULL || jfileName == NULL)
    {
        return -1;
    }
    jboolean isCopy = JNI_FALSE;
    jint ret = -1;
    const char* fileName = env->GetStringUTFChars(jfileName, &isCopy);
    ret = g_ipt_main._config_pad->cfg_vkword_export(fileName);
    env->ReleaseStringUTFChars(jfileName, fileName);
    return ret;
}

/**
 * 导入个性化信息文件
 */
jint jni_cfg_usrinfo_import(JNIEnv* env, jobject obj, jstring jfileName)
{
    if (g_ipt_main._config_pad == NULL || jfileName == NULL)
    {
        return -1;
    }
    jboolean isCopy = JNI_FALSE;
    jint ret = -1;
    const char* fileName = env->GetStringUTFChars(jfileName, &isCopy);
    ret = g_ipt_main._config_pad->cfg_usrinfo_import(fileName);
    env->ReleaseStringUTFChars(jfileName, fileName);
    return ret;
}

/**
 * 导出个性化信息文件
 */
jint jni_cfg_usrinfo_export(JNIEnv* env, jobject obj, jstring jfileName)
{
    if (g_ipt_main._config_pad == NULL || jfileName == NULL)
    {
        return -1;
    }
    jboolean isCopy = JNI_FALSE;
    jint ret = -1;
    const char* fileName = env->GetStringUTFChars(jfileName, &isCopy);
    ret = g_ipt_main._config_pad->cfg_usrinfo_export(fileName);
    env->ReleaseStringUTFChars(jfileName, fileName);
    return ret;
}

/**
 * otherword导入
 */
jint jni_cfg_otherword_import(JNIEnv* env, jobject obj, jstring jfileName, jboolean isOverWrite)
{
    if (g_ipt_main._config_pad == NULL || jfileName == NULL)
    {
        return -1;
    }
    jboolean isCopy = JNI_FALSE;
    jint ret = -1;
    const char* fileName = env->GetStringUTFChars(jfileName, &isCopy);
    ret = g_ipt_main._config_pad->cfg_otherword_import(fileName, isOverWrite == JNI_TRUE ? 1 : 0);
    env->ReleaseStringUTFChars(jfileName, fileName);
    return ret;
}

jint jni_cfg_get_zhuyin_hz_version(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }
    return g_ipt_main._config_pad->cfg_get_zhuyin_hz_version();
}

jint jni_cfg_get_zhuyin_cz_version(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }
    return g_ipt_main._config_pad->cfg_get_zhuyin_cz_version();
}

/**
 * 注音词库的类型，见ECoreZhuyinCZType
 */
jint jni_cfg_get_zhuyin_cz_info(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }
    return g_ipt_main._config_pad->cfg_get_zhuyin_cz_info();
}

jint jni_cfg_get_cangjie_version(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }
    return g_ipt_main._config_pad->cfg_get_cangjie_version();
}

/**
 * 获取手写版本号
 */
jint jni_cfg_get_hw_version(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }
    return g_ipt_main._config_pad->cfg_get_hw_version();
}

jint jni_cfg_adjust_sylian_relation(JNIEnv* env, jobject obj, jstring jprestr, jint prelen,
        jstring jtailstr, jint taillen)
{
    if (g_ipt_main._config_pad == NULL || jprestr == NULL || jtailstr == NULL)
    {
        return -1;
    }
    int ret = -1;
    jboolean isCopy = JNI_FALSE;
    const jchar* prestr = env->GetStringChars(jprestr, &isCopy);
    const jchar* tailstr = env->GetStringChars(jtailstr, &isCopy);
    ret = g_ipt_main._config_pad->cfg_adjust_sylian_relation((Uint16 *) prestr, prelen, (Uint16 *) tailstr, taillen);
    env->ReleaseStringChars(jprestr, prestr);
    env->ReleaseStringChars(jtailstr, tailstr);
    return ret;
}

/**
 * 抽取特征信息
 */
jcharArray jni_tool_feature_inf_extract(JNIEnv* env, jobject obj, jstring jorgWstr, jint orgLen, jint infType)
{
    // 5G内核删除
    return NULL;
//    if (g_ipt_main._config_pad == NULL || jorgWstr == NULL)
//    {
//        return NULL;
//    }
//    int ret = -1;
//    jboolean isCopy = JNI_FALSE;
//    const jchar* orgWstr = env->GetStringChars(jorgWstr, &isCopy);
//    Uint16 infWstr[orgLen];
//    memset(infWstr, 0, orgLen * sizeof(Uint16));
//    ret = g_ipt_main._config_pad->tool_feature_inf_extract((Uint16 *) orgWstr, orgLen, infType,
//            (Uint16 *) infWstr, orgLen);
//    jcharArray jinfWstr = NULL;
//    if (ret > 0)
//    {
//        jinfWstr = (jcharArray) env->NewCharArray(ret);
//        if (jinfWstr != NULL)
//        {
//            for (Uint16 i = 0; i < ret; i++)
//            {
//                env->SetCharArrayRegion(jinfWstr, i, 1, (const jchar *) (infWstr + i));
//            }
//        }
//    }
//    env->ReleaseStringChars(jorgWstr, orgWstr);
//    return jinfWstr;
}

/**
 * 导出盲人辅助文件
 */
jint jni_cfg_cand_context_export(JNIEnv* env, jobject obj, jstring jfileName)
{
    if (g_ipt_main._config_pad == NULL || jfileName == NULL)
    {
        return -1;
    }
    jboolean isCopy = JNI_FALSE;
    jint ret = -1;
    const char* fileName = env->GetStringUTFChars(jfileName, &isCopy);
//    ret = g_ipt_main._config_pad->cfg_cand_context_export(fileName);
    env->ReleaseStringUTFChars(jfileName, fileName);
    return 1;
}

/**
 * 根据简体词的unicode获取繁体
 */
jstring jni_cfg_util_get_ft_by_uni(JNIEnv* env, jobject obj, jstring junicode)
{
    if (g_ipt_main._config_pad == NULL || junicode == NULL)
    {
        return NULL;
    }
    jboolean isCopy = JNI_FALSE;
    const jchar* unicode = env->GetStringChars(junicode, &isCopy);
    jint size = env->GetStringLength(junicode);

    Uint16 tmp_unicode[size + 1];
    memset(tmp_unicode, 0, (size + 1) * sizeof(Uint16));
    g_ipt_main.jchar_to_wchar(unicode, tmp_unicode, size);

    g_ipt_main._config_pad->cfg_util_get_ft_by_uni(tmp_unicode);

    jstring ftstr = g_ipt_main.uni_to_str(env, tmp_unicode);
    env->ReleaseStringChars(junicode, unicode);
    return ftstr;
}

jint jni_cfg_util_import_old_us_file(JNIEnv* env, jobject obj, jstring joldCellFile, jstring joldUzFile)
{
    // 5G内核删除
    return -1;
//    if (g_ipt_main._config_pad == NULL || joldCellFile == NULL || joldUzFile == NULL) {
//        return -1;
//    }
//    jboolean isCopy = JNI_FALSE;
//    jint ret = -1;
//    const char *cellFileName = env->GetStringUTFChars(joldCellFile, &isCopy);
//    const char *uzFileName = env->GetStringUTFChars(joldUzFile, &isCopy);
//    ret = g_ipt_main._config_pad->cfg_util_import_old_us_file(cellFileName, uzFileName);
//    env->ReleaseStringUTFChars(joldCellFile, uzFileName);
//    env->ReleaseStringUTFChars(joldUzFile, cellFileName);
//    return ret;
}

jint jni_cfg_kwd_get_search_version(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }
    return g_ipt_main._config_pad->cfg_kwd_get_search_version();
}

/**
 * 获取智能回复本地库版本号
 */
jint jni_cfg_get_autoreply_ver(JNIEnv* env, jobject obj)
{
    // 5G内核删除
    return -1;
//    if (g_ipt_main._config_pad == NULL)
//    {
//        return -1;
//    }
//    return g_ipt_main._config_pad->cfg_get_autoreply_ver();
}

/**
 * 场景映射表导入
 */
jint jni_cfg_import_app_map(JNIEnv* env, jobject obj, jstring jfilePath)
{
    if (g_ipt_main._config_pad == NULL || jfilePath == NULL)
    {
        return -1;
    }
    jboolean isCopy = JNI_FALSE;
    jint ret = -1;
    const char* fileName = env->GetStringUTFChars(jfilePath, &isCopy);
    ret = g_ipt_main._config_pad->cfg_import_app_map(fileName);
    env->ReleaseStringUTFChars(jfilePath, fileName);
    return ret;
}

/**
 * 获得场景化词库版本
 */
jint jni_cfg_app_get_map_version(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }
    return g_ipt_main._config_pad->cfg_app_get_map_version();
}

/**
 * 导出这次commit的误触情况用于trace
 */
jstring jni_export_mis_touch_info(JNIEnv* env, jobject obj)
{
    jstring result = NULL;
    if (g_ipt_main._ipt_pad != NULL)
    {
        memset(g_ipt_main._g_string_buff, 0, g_ipt_main.MAX_OUTPUT * sizeof(Uint16));
        jint ret = (jint) g_ipt_main._ipt_pad->export_mis_touch_info(
                g_ipt_main._g_string_buff, g_ipt_main.MAX_OUTPUT);

        if (ret > 0)
        {
            result = g_ipt_main.uni_to_str(env, g_ipt_main._g_string_buff);
        }
    }
    return result;
}

/**
 * 导出这次commit的误触情况用于trace
 */
jstring jni_get_key_count_for_mis_touch(JNIEnv* env, jobject obj)
{
    jstring result = NULL;
    if (g_ipt_main._ipt_pad != NULL)
    {
        memset(g_ipt_main._g_string_buff, 0, g_ipt_main.MAX_OUTPUT * sizeof(Uint16));
        jint ret = (jint) g_ipt_main._ipt_pad->get_key_count_for_mis_touch(
                g_ipt_main._g_string_buff, g_ipt_main.MAX_OUTPUT);

        if (ret > 0)
        {
            result = g_ipt_main.uni_to_str(env, g_ipt_main._g_string_buff);
        }
    }
    return result;
}

/**
 * 获取联想信息
 */
void jni_export_input_statistics(JNIEnv* env, jobject obj, jobjectArray jstrings)
{
    if (g_ipt_main._ipt_pad != NULL)
    {
        // 前者是发送给百度的数据，后者是发送给华为的数据
        U16Str str_list{}, str_list_oem{};
        g_ipt_main._ipt_pad->export_input_statistics(str_list, str_list_oem);
        int count = env->GetArrayLength(jstrings);

        if (str_list.data && str_list.len)
        {
            jstring result = g_ipt_main.uni_to_str(env, str_list.data, str_list.len);
            env->SetObjectArrayElement(jstrings, 0, result);
        } else {
            env->SetObjectArrayElement(jstrings, 0, nullptr);
        }

        if (count > 1) {
            if (str_list_oem.data && str_list_oem.len)
            {
                jstring result = g_ipt_main.uni_to_str(env, str_list_oem.data, str_list_oem.len);
                env->SetObjectArrayElement(jstrings, 1, result);
            } else {
                env->SetObjectArrayElement(jstrings, 1, nullptr);
            }
        }
    }
}

/**
 * 导出这次commit的word_info用于trace
 */
jstring jni_export_word_info_for_trace(JNIEnv* env, jobject obj)
{
    jstring result = NULL;
    if (g_ipt_main._ipt_pad != NULL)
    {
        memset(g_ipt_main._g_string_buff, 0, g_ipt_main.MAX_OUTPUT * sizeof(Uint16));
        jint ret = (jint) g_ipt_main._ipt_pad->export_word_info_for_trace(
                g_ipt_main._g_string_buff, g_ipt_main.MAX_OUTPUT);

        if (ret > 0)
        {
            result = g_ipt_main.uni_to_str(env, g_ipt_main._g_string_buff);
        }
    }
    return result;
}

/**
 * 重载czdown文件
 */
jint jni_cfg_cz_down_refresh(JNIEnv* env, jobject obj, jstring jfilePath)
{
    if (g_ipt_main._config_pad == NULL || jfilePath == NULL)
    {
        return -1;
    }
    jboolean isCopy = JNI_FALSE;
    jint ret = -1;
    const char* fileName = env->GetStringUTFChars(jfilePath, &isCopy);
    ret = g_ipt_main._config_pad->cfg_core_refresh(iptcore::ConfigPad::ECoreFileType::CFT_CZ3, fileName);
    env->ReleaseStringUTFChars(jfilePath, fileName);
    return ret;
}

jint jni_cfg_core_refresh(JNIEnv* env, jobject obj, jint type)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }
    jboolean isCopy = JNI_FALSE;
    jint ret = -1;
    iptcore::ConfigPad::ECoreFileType fileType = iptcore::ConfigPad::ECoreFileType(type);
    ret = g_ipt_main._config_pad->cfg_core_refresh(fileType, NULL);
    return ret;
}

jint jni_cfg_core_unload(JNIEnv* env, jobject obj, jint type)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }
    jint ret = -1;
    iptcore::ConfigPad::ECoreFileType fileType = iptcore::ConfigPad::ECoreFileType(type);
    ret = g_ipt_main._config_pad->cfg_core_unload(fileType);
    return ret;
}

jint jni_cfg_nnranker_refresh(JNIEnv* env, jobject obj,
        jstring jConfigPath, jstring jWordPath, jstring jModelPath)
{
    if (g_ipt_main._config_pad == NULL || jConfigPath == NULL
        || jWordPath == NULL || jModelPath == NULL) {
        return -1;
    }
    jboolean isCopy = JNI_FALSE;
    jint ret = -1;
    const char* configName = env->GetStringUTFChars(jConfigPath, &isCopy);
    const char* wordName = env->GetStringUTFChars(jWordPath, &isCopy);
    const char* modelName = env->GetStringUTFChars(jModelPath, &isCopy);
    ret = g_ipt_main._config_pad->cfg_nnranker_refresh(configName, wordName, modelName);
    env->ReleaseStringUTFChars(jConfigPath, configName);
    env->ReleaseStringUTFChars(jWordPath, wordName);
    env->ReleaseStringUTFChars(jModelPath, modelName);
    return ret;
}

void jni_cfg_get_nnranker_info(JNIEnv* env, jobject obj,
                                                 jintArray jintArgs) {
    if (g_ipt_main._config_pad == NULL || jintArgs == NULL) {
        return;
    }
    int len = env->GetArrayLength(jintArgs);
    if (len < 2) {
        return;
    }
    const nn_ranker_info *info = g_ipt_main._config_pad->cfg_get_nnranker_info();
    if (info == NULL) {
        return;
    }
    jint cand_num = info->cand_num;
    jint time = info->run_time;
    env->SetIntArrayRegion(jintArgs, 0, 1, &cand_num);
    env->SetIntArrayRegion(jintArgs, 1, 1, &time);
}

/**
 * 重载智能回复文件
 */
jint jni_cfg_auto_reply_refresh(JNIEnv* env, jobject obj, jstring jfilePath)
{
    if (g_ipt_main._config_pad == NULL || jfilePath == NULL)
    {
        return -1;
    }
    jboolean isCopy = JNI_FALSE;
    jint ret = -1;
    const char* fileName = env->GetStringUTFChars(jfilePath, &isCopy);
    ret = g_ipt_main._config_pad->cfg_core_refresh(iptcore::ConfigPad::ECoreFileType::CFT_AutoReply, fileName);
    env->ReleaseStringUTFChars(jfilePath, fileName);
    return ret;
}

/**
 * 创建用户词管理类
 */
void jni_create_usword_manager(JNIEnv* env, jobject obj, jstring jsearch_word, jint type)
{
    // 5G内核删除
//    if (g_ipt_main._config_pad == NULL || g_ipt_main._user_word_manager != NULL)
//	{
//	    return;
//	}
//	jboolean isCopy = JNI_FALSE;
//	const jchar* search_word = env->GetStringChars(jsearch_word, &isCopy);
//	jint len = env->GetStringLength(jsearch_word);
//	Uint16 search_word_buff[len + 1];
//	memset(search_word_buff, 0, (len + 1) * sizeof(Uint16));
//	g_ipt_main.jchar_to_wchar(search_word, search_word_buff, len);
//	g_ipt_main._user_word_manager = g_ipt_main._config_pad->create_usword_manager(search_word_buff,
//	        (iptcore::ConfigPad::CONFIG_USERWORD_TYPE) type);
}

/**
 * 创建用户词管理类
 */
void jni_destroy_usword_manager(JNIEnv* env, jobject obj)
{
    // 5G内核删除
//    if (g_ipt_main._config_pad == NULL || g_ipt_main._user_word_manager == NULL)
//    {
//        return;
//    }
//    g_ipt_main._config_pad->destroy_usword_manager(g_ipt_main._user_word_manager);
//    g_ipt_main._user_word_manager = NULL;
}

/**
 * 获得用户词数量
 */
jint jni_usword_get_count(JNIEnv* env, jobject obj)
{
    // 5G内核删除
    return 0;
//    if (g_ipt_main._config_pad == NULL || g_ipt_main._user_word_manager == NULL)
//    {
//        return 0;
//    }
//    return g_ipt_main._user_word_manager->count();
}

/**
 * 获得用户词内容
 */
jstring jni_usword_get_str(JNIEnv* env, jobject obj, jint idx)
{
    // 5G内核删除
    return NULL;
//    if (g_ipt_main._config_pad == NULL || g_ipt_main._user_word_manager == NULL)
//    {
//        return NULL;
//    }
//    memset(g_ipt_main._g_string_buff, g_ipt_main.MAX_OUTPUT, 0);
//    Int32 ret = g_ipt_main._user_word_manager->get_str(g_ipt_main._g_string_buff, idx);
//    if (ret >=0)
//    {
//        return g_ipt_main.uni_to_str(env, g_ipt_main._g_string_buff);
//    }
//    return NULL;
}

/**
 * 获得用户词动作
 */
jint jni_usword_get_action(JNIEnv* env, jobject obj, jint idx)
{
    // 5G内核删除
    return -1;
//    if (g_ipt_main._config_pad == NULL || g_ipt_main._user_word_manager == NULL)
//    {
//        return -1;
//    }
//    return g_ipt_main._user_word_manager->get_action(idx);
}

/**
 * 执行用户词动作
 */
void jni_usword_do_action(JNIEnv* env, jobject obj, jint idx)
{
    // 5G内核删除
//    if (g_ipt_main._config_pad == NULL || g_ipt_main._user_word_manager == NULL)
//    {
//        return;
//    }
//    log_print(ANDROID_LOG_INFO, "JNIMsg", "usword_do_action! idx:%d", idx);
//    g_ipt_main._user_word_manager->do_action(idx);
}

/**
 * 获得所有中文用户词个数
 */
jint jni_usword_get_cnword_count(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return 0;
    }
    return g_ipt_main._config_pad->cfg_util_usword_get_cnword_count();
}

/**
 * 语音查找表情
 */
jint jni_cfg_keyword_find_voice_lian(JNIEnv* env, jobject obj, jstring str, jintArray emojiList,
        jobjectArray emoticonList)
{
    Int32 ret = -1;

    if (str != NULL && g_ipt_main._config_pad != NULL)
    {
        jboolean isCopy = JNI_FALSE;
        jint *emojis = env->GetIntArrayElements(emojiList, &isCopy);
        const jchar *tmpStr = env->GetStringChars(str, &isCopy);
        int len = env->GetStringLength(str);
        Uint16 unicode[len + 1];
        memset(unicode, 0, (len + 1) * sizeof(Uint16));
        g_ipt_main.jchar_to_wchar(tmpStr, unicode, len);

        const Uint32 max_emoji_len = 5;
        const Uint32 max_emoticon_len = 5;
        const Uint32 max_emoticon_strlen = 64;

        Uint16 tmp_emojis[max_emoji_len];
        memset(tmp_emojis, 0, max_emoji_len * sizeof(Uint16));
        Uint16 tmp_emotions[max_emoticon_len][max_emoticon_strlen];
        memset(tmp_emotions, 0, max_emoticon_len * max_emoticon_strlen * sizeof(Uint16));

        Uint32 emoji_cnt = 0;
        Uint32 emotion_cnt = 0;
        ret = g_ipt_main._config_pad->cfg_keyword_find_voice_lian(unicode, (Uint32) len,
                                                                  (Uint16 *) tmp_emojis, &emoji_cnt,
                                                                  tmp_emotions,
                                                                  &emotion_cnt);
        int i;
        for (i = 0; i < max_emoji_len; i++)
        {
            emojis[i] = tmp_emojis[i];
        }

        Uint16 *emoticon_item;
        for (i = 0; i < max_emoticon_len; i++)
        {
            emoticon_item = tmp_emotions[i];
            jstring emoticon_jstr = g_ipt_main.uni_to_str(env, emoticon_item);
            env->SetObjectArrayElement(emoticonList, i, emoticon_jstr);
        }
        env->ReleaseIntArrayElements(emojiList, emojis, 0);
        env->ReleaseStringChars(str, tmpStr);
    }
    return ret;
}

/**
 * 语音联想查找彩蛋
 */
jcharArray jni_cfg_keyword_find_voice_egg(JNIEnv* env, jobject obj, jstring str)
{
    jboolean isCopy = JNI_FALSE;
    jcharArray eggstr = NULL;
    if (g_ipt_main._config_pad != NULL && str != NULL)
    {
        const jchar *tmpStr = env->GetStringChars(str, &isCopy);
        int len = env->GetStringLength(str);
        Uint16 unicode[len + 1];
        memset(unicode, 0, (len + 1) * sizeof(Uint16));
        g_ipt_main.jchar_to_wchar(tmpStr, unicode, len);
        env->ReleaseStringChars(str, tmpStr);

        Uint16 egg[64];
        memset(egg, 0, 64 * sizeof(Uint16));

        int ret = g_ipt_main._config_pad->cfg_keyword_find_voice_egg(unicode, len, (Uint16 *) egg);
        if (ret > 0)
        {
            eggstr = (jcharArray) env->NewCharArray(ret);
            for (Uint32 i = 0; i < ret; i++)
            {
                const jchar tmpChar = (jchar) egg[i];
                env->SetCharArrayRegion(eggstr, i, 1, &tmpChar);
            }
        }
    }
    return eggstr;
}

jint jni_block_cloud_unis(JNIEnv* env, jobject obj, jstring content)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return 0;
    }

    jboolean is_copy = false;
    Uint32 content_len = env->GetStringLength(content);
    const jchar* content_jchar = env->GetStringChars(content, &is_copy);
    Int32 ret =  g_ipt_main._config_pad->block_cloud_unis((Uint16*) content_jchar, content_len);
    env->ReleaseStringChars(content, content_jchar);
    return ret;
}

jint jni_reset_cloud_black(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return 0;
    }
    return g_ipt_main._config_pad->reset_cloud_black();
}

jint jni_cfg_usall_reset(JNIEnv *env, jobject obj) {
    if (g_ipt_main._config_pad == NULL) {
        return -1;
    }
    int result = g_ipt_main._config_pad->cfg_usall_reset();
    return result;
}

/**
 * 加载ai font手写模型
 */
jboolean jni_aifont_handwrite_load_model(JNIEnv* env, jobject obj, jobject context, jstring model_path, jstring license_path)
{
    if (g_ipt_main._pub_handwrite == NULL)
    {
        return false;
    }

    jboolean isCopy = JNI_FALSE;
    const char* path = env->GetStringUTFChars(model_path, &isCopy);
    const char* lic_path = env->GetStringUTFChars(license_path, &isCopy);
    bool ret = g_ipt_main._pub_handwrite->load_model(path, lic_path, g_ipt_main._ipt_env->_java_vm, context);
    LOGI("path：%s result：%d, (java_vm == NULL)?：%d", path, ret, NULL == g_ipt_main._ipt_env->_java_vm);
    env->ReleaseStringUTFChars(model_path, path);
    env->ReleaseStringUTFChars(license_path, lic_path);
    return ret;
}

/**
 * 卸载ai font手写模型
 */
void jni_aifont_handwrite_unload_model(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._pub_handwrite == NULL)
    {
        return;
    }

    g_ipt_main._pub_handwrite->unload_model();
}

/**
 * 导出全部自造词-pb格式
 */
JNIEXPORT jint JNICALL jni_cfg_usall_export_proto(JNIEnv* env, jobject obj, jstring file_path)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }
    jboolean isCopy = JNI_FALSE;
    const char* path_buff = env->GetStringUTFChars(file_path, &isCopy);
    Int32 ret = g_ipt_main._config_pad->cfg_usall_export_proto(path_buff);
    env->ReleaseStringUTFChars(file_path, path_buff);
    return ret;
}

/**
 * 导入全部自造词-pb格式
 */
JNIEXPORT jint JNICALL jni_cfg_usall_import_proto(JNIEnv* env, jobject obj, jstring file_path)
{
    if (g_ipt_main._config_pad == NULL)
    {
        return -1;
    }
    jboolean isCopy = JNI_FALSE;
    const char* path_buff = env->GetStringUTFChars(file_path, &isCopy);
    Int32 ret = g_ipt_main._config_pad->cfg_usall_import_proto(path_buff);
    env->ReleaseStringUTFChars(file_path, path_buff);
    return ret;
}

/**
 * 获取ai font手写模型version
 */
jint jni_aifont_handwrite_model_version(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._pub_handwrite == NULL)
    {
        return 0;
    }

    int ver = g_ipt_main._pub_handwrite->model_version();
    return ver;
}

/**
 * 获取ai font手写模型version
 */
jint jni_aifont_reco_points(JNIEnv* env, jobject obj, jobject o_ai_hw_res, jint max_out_um, jintArray points)
{
    if (g_ipt_main._pub_handwrite == nullptr)
    {
        return 0;
    }

    jboolean isCopy = JNI_FALSE;
    jclass obj_class = env->GetObjectClass(o_ai_hw_res);
    jmethodID set_ai_hw_characters = env->GetMethodID(obj_class, "setData", "(I[Ljava/lang/String;[S[I)V");
    hw_interface::HwResItem* res = (hw_interface::HwResItem*) malloc(sizeof(hw_interface::HwResItem) * max_out_um);
    int length = env->GetArrayLength(points);
    jint* point_element = env->GetIntArrayElements(points, &isCopy);
    Uint16 temp[length];
    hw_interface::HwResItem items[max_out_um];
    jshortArray testShort = env->NewShortArray(length);
    for (int i = 0; i < length; i++)
    {
        temp[i] = (Uint16) point_element[i];
//        LOGI("temp：%d", temp[i]);
    }
    const Uint16* out = (const Uint16*) temp;
    Uint32 count = g_ipt_main._pub_handwrite->reco_points(res, max_out_um, out);

    jobjectArray chars = nullptr;
    jshortArray reserveds = nullptr;

    jintArray prs = nullptr;
    LOGI("count：%x", count);
    if (count > 0) {
        jclass objClass = (env)->FindClass("java/lang/String");
        chars = env->NewObjectArray(count, objClass, NULL);
        reserveds = env->NewShortArray(count);
        prs = env->NewIntArray(count);
        for(int i = 0; i < count; i++) {
            hw_interface::HwResItem item = res[i];
            jstring str = g_ipt_main.uni_to_str(env, (const Uint16 *) &item.code);
            env->SetObjectArrayElement(chars, i, str);
            env->SetShortArrayRegion(reserveds, i, 1, (jshort*) &(item.reserved));
            env->SetIntArrayRegion(prs, i, 1, (jint*) &(item.pr));
        }
    }
    if (set_ai_hw_characters != nullptr) {
        env->CallVoidMethod(o_ai_hw_res, set_ai_hw_characters, count, chars, reserveds, prs);
    }

    if (count > 0) {
        env->DeleteLocalRef(chars);
        env->DeleteLocalRef(reserveds);
        env->DeleteLocalRef(prs);
    }
    env->ReleaseIntArrayElements(points, point_element, JNI_ABORT);
    env->DeleteLocalRef(obj_class);
    free(res);
    return count;
}

/**
 * 导入自定义输入方案
 */
jint jni_cfg_import_olddef_txt(JNIEnv* env, jobject obj, jstring jfilePath)
{
    if (g_ipt_main._config_pad == NULL || jfilePath == NULL)
    {
        return -1;
    }
    jboolean isCopy = JNI_FALSE;
    jint ret = -1;
    const char* fileName = env->GetStringUTFChars(jfilePath, &isCopy);
    ret = g_ipt_main._config_pad->cfg_import_olddef_txt(fileName);
    env->ReleaseStringUTFChars(jfilePath, fileName);
    return ret;
}

/*** 以下函数返回值都无意义，含义和名字也无关 ***/
jint jni_cfg_dct_cell_install_by_buff(JNIEnv* env, jobject obj, jstring value) {
    if (g_ipt_main._config_pad == NULL || value == NULL) {
        return -1;
    }
    jboolean isCopy = JNI_FALSE;
    Uint32 len = env->GetStringUTFLength(value);
    const char* chars = env->GetStringUTFChars(value, &isCopy);
    Int32 ret = g_ipt_main._config_pad->cfg_dct_cell_install_by_buff(reinterpret_cast<const Uint8 *>(chars), (Uint32) len);
    LOGI("cfg_dct_cell_install_by_buff, contentLen=%d,ret=%d", len, ret);
    env->ReleaseStringUTFChars(value, chars);
    return ret;
}

jint jni_cfg_dct_cell_word_in_dict(JNIEnv* env, jobject obj, jstring value) {
    if (g_ipt_main._config_pad == NULL || value == NULL) {
        return -1;
    }
    jboolean isCopy = JNI_FALSE;
    Uint32 len = env->GetStringLength(value);
    const jchar* chars = env->GetStringChars(value, &isCopy);
    Int32 ret = g_ipt_main._config_pad->cfg_dct_cell_word_in_dict((Uint16*) (chars), (Uint32) len);
    LOGI("cfg_dct_cell_word_in_dict, contentLen=%d,ret=%d", len, ret);
    env->ReleaseStringChars(value, chars);
    return ret;
}

jstring jni_cfg_dct_cell_export_buff(JNIEnv* env, jobject obj, jint cell_id) {
    if (g_ipt_main._config_pad == NULL) {
        return NULL;
    }
    const Uint8* code = nullptr;
    Uint32 code_len = 0;
    Int32 ret = g_ipt_main._config_pad->cfg_dct_cell_export_buff(code, code_len, cell_id);
    if (code_len > 0) {
        // 将 buffer 转换为 jstring
        jstring str = env->NewStringUTF(reinterpret_cast<const char*>(code));
        return str;
    }
    return NULL;
}

jint jni_cfg_dct_cell_reset_buff(JNIEnv* env, jobject obj, jint cell_id) {
    if (g_ipt_main._config_pad == NULL) {
        return -1;
    }
    Int32 ret = g_ipt_main._config_pad->cfg_dct_cell_reset_buff(cell_id);
    return ret;
}

/**
 * 智能表单：导入个人化信息 逐条导入-开始标记，内核开启文件写指针, 并关闭查词功能
 */
jint jni_cfg_person_info_add_line_start(JNIEnv * env, jobject obj) {
    if (g_ipt_main._config_pad == NULL) {
        return -1;
    }
    Int32 ret = g_ipt_main._config_pad->cfg_person_info_add_line_start();
    return ret;
}

/**
 * 智能表单：导入个人化信息 逐条导入 导入结束调用 cfg_usrinfo_import_finished
 * index_len：用户信息展示联想的最短索引长度，当索引长度大于 0 时，认为是联想候选，
 * 如果导入内容为人名等需要展示在候选的, 则将索引长度设置为 0
 * wline：个人信息字符串（邮箱，手机号等）
 * wlen：字符串长度
 * 返回值：<0 导入出错，=0 导入成功 ///
 */
jint jni_cfg_person_info_add_from_line(JNIEnv * env, jobject obj, jint index_len, jstring wline) {
    if (g_ipt_main._config_pad == NULL || wline == NULL) {
        return -1;
    }
    jboolean isCopy = JNI_FALSE;
    const jchar* wline_jchar = env->GetStringChars(wline, &isCopy);
    jint wlen = env->GetStringLength(wline);
    Uint16 wline_buff[wlen + 1];
    g_ipt_main.jchar_to_wchar(wline_jchar, wline_buff, wlen);
    Int32 ret = g_ipt_main._config_pad->cfg_person_info_add_from_line((Uint32) index_len, wline_buff, (Uint32) wlen);
    env->ReleaseStringChars(wline, wline_jchar);
    return ret;
}

/**
 * 智能表单：导入个人化信息结束标志
 * 返回值：<0 结束出错，=0 结束成功
 */
jint jni_cfg_person_info_add_finished(JNIEnv * env, jobject obj) {
    if (g_ipt_main._config_pad == NULL) {
        return -1;
    }
    Int32 ret = g_ipt_main._config_pad->cfg_person_info_add_finished();
    return ret;
}

/**
 * 智能表单：个人信息联想功能关闭，调用该接口，清空用户个人信息数据
 */
jint jni_cfg_person_info_reset(JNIEnv * env, jobject obj) {
    if (g_ipt_main._config_pad == NULL) {
        return -1;
    }
    Int32 ret = g_ipt_main._config_pad->cfg_person_info_reset();
    return ret;
}

/*** 以上函数返回值都无意义，含义和名字也无关 ***/