/*
 * ipt_jni_native_main.cpp
 *
 *  Created on: 2019-02-22
 *      Author: cdf
 *      jni主接口
 */

#include "ipt_pub_jni.h"
#include "ipt_jni_main.h"
#include "ipt_jni_log.h"
#include "ipt_jni_gobal.h"
#include <cstring>
#include "md5.h"
#include <cstdio>
#include "_pub_util.h"
#include "utility.h"

#define g_ipt_main iptjni::IptJniGlobal::get_ipt_jni_main()

jint jni_open(JNIEnv * env, jobject obj, jobject context, jobject ard_env, jobject ard_netman, jstring dict_dir,
        jobject package_info, jint flavor)
{
    return g_ipt_main.open(env, context, ard_env, ard_netman, dict_dir, package_info, flavor);
}

jint jni_close(JNIEnv * env, jobject obj)
{
    return g_ipt_main.close(env);
}

jint jni_get_core_version(JNIEnv * env, jobject obj)
{
    return g_ipt_main.get_core_version();
}

jint jni_get_en_sys_dict_version(JNIEnv * env, jobject obj)
{
    return g_ipt_main.get_en_sys_dict_version();
}

jint jni_get_cz3_dict_gram_version(JNIEnv * env, jobject obj)
{
    return g_ipt_main.get_cz3_dict_gram_version();
}

jint jni_get_cz3_dict_sys_version(JNIEnv * env, jobject obj)
{
    return g_ipt_main.get_cz3_dict_sys_version();
}

jint jni_get_cz3_dict_cate_version(JNIEnv * env, jobject obj)
{
    return g_ipt_main.get_cz3_dict_cate_version();
}

jint jni_get_cz5down_status(JNIEnv * env, jobject obj)
{
    return g_ipt_main.get_cz5down_status();
}

jint jni_get_slide_dict_version(JNIEnv * env, jobject obj)
{
    return g_ipt_main.get_slide_dict_version();
}

jintArray jni_get_cz_version(JNIEnv * env, jobject obj, jstring dict_dir)
{

    jintArray versions = env->NewIntArray(5);
    jint *arr = env->GetIntArrayElements(versions, NULL);
    jint ret = g_ipt_main.get_cz_version(env, dict_dir, dict_dir, arr);
    env->SetIntArrayRegion(versions, 0, 5, arr);
    env->ReleaseIntArrayElements(versions, arr, 0);
    return versions;
}

jboolean jni_is_nnranker_installed(JNIEnv * env, jobject obj)
{
    return g_ipt_main.is_nnranker_installed();
}

jint jni_backup_trace_log(JNIEnv * env, jobject obj, jstring file_path)
{
    return g_ipt_main.backup_trace_log(env, file_path);
}

jstring jni_get_trace_log(JNIEnv * env, jobject obj)
{
    return g_ipt_main.get_trace_log(env);
}

void jni_reset_trace_log(JNIEnv * env, jobject obj)
{
    g_ipt_main.reset_trace_log(env);
}

void jni_send_pad_event(JNIEnv* env, jobject obj, jint pad_event, jboolean restart)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return;
    }

    iptcore::InputPad::EventPadSendAction eventPadSendAction(
            (iptcore::InputPad::PadEventType) pad_event, restart);

    g_ipt_main._ipt_pad->send_event(&eventPadSendAction);
}

jint jni_pad_switch(JNIEnv* env, jobject obj, jint pad_id, jobject o_duty)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }
    log_print(ANDROID_LOG_INFO, "JNIMsg", "pad_switch:%d", pad_id);
    // g_ipt_main._ipt_pad->pad_attach_config(g_ipt_main._config_items);
    const iptcore::DutyInfo* duty_info = g_ipt_main._ipt_pad->act_padswitch((iptcore::InputPad::PadId) pad_id);
    return g_ipt_main.set_duty_info_data(env, duty_info, o_duty);
}

jint jni_act_cnen_key(JNIEnv* env, jobject obj, jint pad_id, jobject o_duty)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }
    const iptcore::DutyInfo* duty_info = g_ipt_main._ipt_pad->act_cnen_key(
            (iptcore::InputPad::PadId)pad_id);
    return g_ipt_main.set_duty_info_data(env, duty_info, o_duty);
}

jint jni_act_rare_chaizi_hw_key(JNIEnv* env, jobject obj, jint pad_id, jobject o_duty)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }
    const iptcore::DutyInfo* duty_info = g_ipt_main._ipt_pad->act_rare_chaizi_hw_key(
            (iptcore::InputPad::PadId)pad_id);
    return g_ipt_main.set_duty_info_data(env, duty_info, o_duty);
}

void jni_set_default_pad(JNIEnv* env, jobject obj, jint pad_id)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return;
    }
    g_ipt_main._ipt_pad->set_default_pad((iptcore::InputPad::PadId) pad_id);
}

jint jni_act_key_click(JNIEnv* env, jobject obj, jint key_id, jint input_type, jstring junicode, jobject o_duty)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    Uint16* uni = NULL;
    const jchar* unicode_jchar = NULL;
    jint len = 0;
    if (junicode != NULL)
    {
        jboolean isCopy = JNI_FALSE;
        unicode_jchar = env->GetStringChars(junicode, &isCopy);
        len = env->GetStringLength(junicode);
        uni = (Uint16*) malloc(sizeof(Uint16) * (len + 1));
        g_ipt_main.jchar_to_wchar(unicode_jchar, uni, len);

    }

    LOGI("jni_act_key_click:keyId=%d,inputType=%d,uniLen=%d", (int) key_id, (int) input_type, (int) len);

    Int32 ret = -1;
    int clickDownEnumValue = iptcore::InputPad::INPUTTYPE_CLICK_DOWN;
    if (((int) input_type) == clickDownEnumValue)
    {
        LOGI("act_key_click_down_event:: %d", (Int32) key_id);
        iptcore::InputPad::EventKeyAction keyAction((Int32) key_id,
                                                    iptcore::InputPad::INPUTTYPE_CLICK_DOWN);
        g_ipt_main._ipt_pad->send_event(&keyAction);
    }
    else
    {
        const iptcore::DutyInfo *duty_info = g_ipt_main._ipt_pad->act_keyclick(key_id, NULL,
                                                        iptcore::InputPad::InputType(input_type),
                                                                               uni, NULL);
        ret = g_ipt_main.set_duty_info_data(env, duty_info, o_duty);
    }
    if (junicode != NULL && unicode_jchar != NULL && uni != NULL)
    {
        env->ReleaseStringChars(junicode, unicode_jchar);
        free(uni);
    }
    return ret;
}

void jni_act_cand_action(JNIEnv* env, jobject obj, jint cand_action_type, jint candidx)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return;
    }

    log_print(ANDROID_LOG_INFO, "JNIMsg","jni_act_cand_action:cand_action_type=%d,cand_idx=%d",
              (int) cand_action_type, (int) candidx);

    iptcore::InputPad::EventCandAction cand_action((iptcore::InputPad::CandActionType) cand_action_type, candidx);
    g_ipt_main._ipt_pad->send_event(&cand_action);
}

/**
 * AI助聊 卡片相关的点击操作
 */
void jni_act_ai_pad_click(JNIEnv* env, jobject obj, jint event_type, jshortArray click_list)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return;
    }

    jshort* jclick_list = nullptr;
    jint jclick_len = 0;

    if (click_list != nullptr)
    {
        jboolean is_copy = JNI_FALSE;
        jclick_list = env->GetShortArrayElements(click_list, &is_copy);
        jclick_len = env->GetArrayLength(click_list);
    }

    iptcore::InputPad::EventAiPadAction aiPadAction(
            (iptcore::InputPad::AiPadActType) event_type, (Uint16*) jclick_list, jclick_len);
    g_ipt_main._ipt_pad->send_event(&aiPadAction);
}

/**
 * 客户端自定义的上屏事件
 * @param user_data 用户上屏的文本
 */
void jni_act_custom_input_action(JNIEnv* env, jobject obj, jint event_type, jstring user_data)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return;
    }

    const jchar* user_data_ptr = env->GetStringChars(user_data, nullptr);
    jint len = env->GetStringLength(user_data);
    Uint16* uni = (Uint16*) malloc(sizeof(Uint16) * (len + 1));
    // 给内核的字符，末尾需要补'\0'
    g_ipt_main.jchar_to_wchar(user_data_ptr, uni, len);

    iptcore::InputPad::EventCustomInputData customInputDataType(
            (iptcore::InputPad::CustomInputDataType) event_type, uni);

    g_ipt_main._ipt_pad->send_event(&customInputDataType);

    if (user_data != nullptr && user_data_ptr != nullptr) {
        env->ReleaseStringChars(user_data, user_data_ptr);
        free(uni);
    }
}

/**
 * ai font 生成，通知内核ai助聊展示
 */
void jni_act_aifont_generated_notify(JNIEnv* env, jobject obj, jint type, jstring show_content
    , jstring scheme, jstring insert_content, jlong show)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return;
    }

    const jchar* content = env->GetStringChars(show_content, nullptr);
    jint content_len = env->GetStringLength(show_content);

    Uint16* content_uni = (Uint16*) malloc(sizeof(Uint16) * (content_len + 1));
    // 给内核的字符，末尾需要补'\0'
    g_ipt_main.jchar_to_wchar(content, content_uni, content_len);

    const jchar* insert = env->GetStringChars(insert_content, nullptr);
    jint insert_len = env->GetStringLength(insert_content);

    Uint16* insert_uni = (Uint16*) malloc(sizeof(Uint16) * (insert_len + 1));
    g_ipt_main.jchar_to_wchar(insert, insert_uni, insert_len);

    const char* url = env->GetStringUTFChars(scheme, nullptr);

    iptcore::InputPad::EventAiCandNotify eventAiCandNotify(
            (iptcore::InputPad::AiCandNotifyType) type, content_uni, (const Uint8 *) url, show, insert_uni);

    g_ipt_main._ipt_pad->send_event(&eventAiCandNotify);

    if (content != nullptr && show_content != nullptr) {
        env->ReleaseStringChars(show_content, content);
        free(content_uni);
    }

    if (insert != nullptr && insert_content != nullptr) {
        env->ReleaseStringChars(insert_content, insert);
        free(insert_uni);
    }

    if (url != nullptr && scheme != nullptr) {
        env->ReleaseStringUTFChars(scheme, url);
    }
}

jint jni_get_intent_style(JNIEnv* env, jobject obj, jstring content)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }
    const jchar* content_buffer = env->GetStringChars(content, nullptr);
    jint content_len = env->GetStringLength(content);
    Int32 ret = g_ipt_main._ipt_pad->aichat_get_intention_style((Uint16 *)content_buffer, content_len);

    if (content != nullptr && content_buffer != nullptr) {
        env->ReleaseStringChars(content, content_buffer);
    }
    return ret;
}

jint act_key_click_touch_info(JNIEnv* env, jobject obj, jint key_id, jint x, jint y,
        s_Click_v1* click_v1, jint input_type, jstring junicode, jobject o_duty)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    Uint16* uni = NULL;
    const jchar* unicode_jchar = NULL;
    jint len = 0;
    if (junicode != NULL)
    {
        jboolean isCopy = JNI_FALSE;
        unicode_jchar = env->GetStringChars(junicode, &isCopy);
        len = env->GetStringLength(junicode);
        uni = (Uint16*) malloc(sizeof(Uint16) * (len + 1));
        g_ipt_main.jchar_to_wchar(unicode_jchar, uni, len);

    }

    if (click_v1 == nullptr) {
        LOGI("jni_act_key_click:keyId=%d,x=%d,y=%d,inputType=%d,uniLen=%d",
             (int) key_id, (int) x, (int) y, (int) input_type, (int) len);
    } else {
        LOGI("jni_act_key_click:keyId=%d,x=%d,y=%d,inputType=%d,uniLen=%d,power=%d,area=%d",
             (int) key_id, (int) x, (int) y, (int) input_type, (int) len,
             (int) (click_v1->power), (int) (click_v1->area));
    }

    s_Point_v2 point = {static_cast<PLUINT16>(x), static_cast<PLUINT16>(y)};
    Int32 ret = -1;
    int clickDownEnumValue = iptcore::InputPad::INPUTTYPE_CLICK_DOWN;
    if (((int) input_type) == clickDownEnumValue)
    {
        iptcore::InputPad::EventKeyAction keyAction((Int32) key_id,
                iptcore::InputPad::INPUTTYPE_CLICK_DOWN,
                &point);
        g_ipt_main._ipt_pad->send_event(&keyAction);
    }
    else
    {
        const iptcore::DutyInfo *duty_info = g_ipt_main._ipt_pad->act_keyclick(key_id, &point,
                                                    iptcore::InputPad::InputType(input_type),
                                                    uni, click_v1);
        ret = g_ipt_main.set_duty_info_data(env, duty_info, o_duty);
    }
    if (junicode != NULL && unicode_jchar != NULL && uni != NULL)
    {
        env->ReleaseStringChars(junicode, unicode_jchar);
        free(uni);
    }
    return ret;
}

jint jni_act_key_click_xy(JNIEnv* env, jobject obj, jint key_id, jint x, jint y,
                                            jint input_type, jstring junicode, jobject o_duty)
{
    return act_key_click_touch_info(env, obj, key_id, x, y, nullptr, input_type, junicode, o_duty);
}

jint jni_act_key_click_touch_info(JNIEnv* env, jobject obj, jint key_id, jint x, jint y,
                                            jbyte power, jbyte area,
                                            jint input_type, jstring junicode, jobject o_duty)
{
    s_Click_v1 click_v1 = { static_cast<PLBYTE>(power),  static_cast<PLBYTE>(area)};
    return act_key_click_touch_info(env, obj, key_id, x, y, &click_v1, input_type, junicode, o_duty);
}

jint jni_act_cand_click(JNIEnv* env, jobject obj, jint cand_idx, jobject o_duty)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    const iptcore::DutyInfo* duty_info = g_ipt_main._ipt_pad->act_candclick(cand_idx);
    return g_ipt_main.set_duty_info_data(env, duty_info, o_duty);
}

jint jni_act_rare_cand_click(JNIEnv* env, jobject obj, jint cand_idx, jobject o_duty)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    const iptcore::DutyInfo* duty_info = g_ipt_main._ipt_pad->act_rare_candclick(cand_idx);
    return g_ipt_main.set_duty_info_data(env, duty_info, o_duty);
}

jint jni_act_cand_select(JNIEnv* env, jobject obj, jint cand_idx, jobject o_duty)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    const iptcore::DutyInfo* duty_info = g_ipt_main._ipt_pad->act_candselect(cand_idx);
    return g_ipt_main.set_duty_info_data(env, duty_info, o_duty);
}

jint jni_act_cand_longpress(JNIEnv* env, jobject obj, jint cand_idx, jobject o_duty)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    const iptcore::DutyInfo* duty_info = g_ipt_main._ipt_pad->act_candlongpress(cand_idx);
    return g_ipt_main.set_duty_info_data(env, duty_info, o_duty);
}

jint jni_act_sug_click(JNIEnv* env, jobject obj, jint sug_idx, jobject o_duty)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    const iptcore::DutyInfo* duty_info = g_ipt_main._ipt_pad->act_sugclick(sug_idx);
    return g_ipt_main.set_duty_info_data(env, duty_info, o_duty);
}

jint jni_act_sug_card_click(JNIEnv* env, jobject obj, jint sug_card_idx, jboolean is_insert, jobject o_duty)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    const iptcore::DutyInfo* duty_info = g_ipt_main._ipt_pad->act_sugcardclick(sug_card_idx, is_insert);
    return g_ipt_main.set_duty_info_data(env, duty_info, o_duty);
}

jint jni_act_sug_card_select(JNIEnv* env, jobject obj, jint sug_card_idx, jobject o_duty)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    const iptcore::DutyInfo* duty_info = g_ipt_main._ipt_pad->act_sugcard_select(sug_card_idx);
    return g_ipt_main.set_duty_info_data(env, duty_info, o_duty);
}


jint jni_act_list_click(JNIEnv* env, jobject obj, jint list_idx, jobject o_duty)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    const iptcore::DutyInfo* duty_info = g_ipt_main._ipt_pad->act_listclick(list_idx);
    return g_ipt_main.set_duty_info_data(env, duty_info, o_duty);
}

/**
 * 点击更多候选词面板中的tab筛选条操作
 * @return 成功返回0，失败返回-1
 */
jint jni_act_tab_click(JNIEnv* env, jobject obj, jint qp_filter, jobject o_duty)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    const iptcore::DutyInfo* duty_info = g_ipt_main._ipt_pad->act_tab_click(iptcore::ConfigItems::QpFilter (qp_filter));
    return g_ipt_main.set_duty_info_data(env, duty_info, o_duty);
}

/**
 * 获取更多候选词面板的tab
 */
jbyteArray jni_get_tabs(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return NULL;
    }
    jbyteArray tabArray = NULL;
    Uint32 tabLen = 0;
    const Uint8 *tabs = g_ipt_main._ipt_pad->get_tabs(tabLen);

    if (tabs != NULL && tabLen > 0) {
        tabArray = env->NewByteArray(tabLen);
        env->SetByteArrayRegion(tabArray, 0, tabLen, (const jbyte *) tabs);
    }
    return tabArray;
}

/**
 * 弹出candinfo信息栏选择
 * @return 成功返回0，失败返回-1
 */
jint jni_act_candinfo_click(JNIEnv* env, jobject obj, jint idx, jobject o_duty)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    const iptcore::DutyInfo* duty_info = g_ipt_main._ipt_pad->act_candinfo_click(idx);
    return g_ipt_main.set_duty_info_data(env, duty_info, o_duty);
}

/**
 * 取消candinfo信息栏
 * @return 成功返回0，失败返回-1
 */
jint jni_act_candinfo_cancel(JNIEnv* env, jobject obj, jobject o_duty)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    const iptcore::DutyInfo* duty_info = g_ipt_main._ipt_pad->act_candinfo_cancel();
    return g_ipt_main.set_duty_info_data(env, duty_info, o_duty);
}

/**
 * 插入选择的联系人信息
 * @return 成功返回0，失败返回-1
 */
jint jni_act_contact_insert(JNIEnv* env, jobject obj, jobject o_duty)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    const iptcore::DutyInfo* duty_info = g_ipt_main._ipt_pad->act_contact_insert();
    return g_ipt_main.set_duty_info_data(env, duty_info, o_duty);
}

/**
 * 设置指定的ContactItem为选中/非选中状态
 * @return 成功返回0，失败返回-1
 */
jint jni_act_contact_info_select(JNIEnv* env, jobject obj, jint contact_idx, jint item_idx,
                                                   jboolean is_select)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    iptcore::ContactItem *contact_item = g_ipt_main._ipt_pad->get_contact_item(contact_idx);
    if (contact_item == NULL)
    {
        return -1;
    }
    return contact_item->set_select(item_idx, is_select);
}

/**
 * 关闭联系人信息窗口
 * @return 成功返回0，失败返回-1
 */
jint jni_act_contact_cancel(JNIEnv* env, jobject obj, jobject o_duty)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    const iptcore::DutyInfo* duty_info = g_ipt_main._ipt_pad->act_contact_cancel();
    return g_ipt_main.set_duty_info_data(env, duty_info, o_duty);
}

jint jni_act_track_start(JNIEnv* env, jobject obj, jshortArray point, jboolean is_sym_hw, jobject o_duty, jlong time)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    jboolean is_copy = JNI_FALSE;
    jshort* jpoint = env->GetShortArrayElements(point, &is_copy);
    jint jpoint_len = env->GetArrayLength(point);
    const iptcore::DutyInfo* duty_info = g_ipt_main._ipt_pad->act_track_start((s_Point_v2*)jpoint,
                                                                              jpoint_len, is_sym_hw, time);
    env->ReleaseShortArrayElements(point, jpoint, JNI_ABORT);

    return g_ipt_main.set_duty_info_data(env, duty_info, o_duty);
}

jint jni_act_track_start_xy(JNIEnv* env, jobject obj, jshort point_x, jshort point_y,
        jboolean is_sym_hw, jobject o_duty, jlong time)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }
    log_print(ANDROID_LOG_INFO, "JNIMsg", "jni_act_track_start_xy x:%d, y:%d", (int)point_x, (int)point_y);

    s_Point_v2 point = {static_cast<PLUINT16>(point_x), static_cast<PLUINT16>(point_y)};
    const iptcore::DutyInfo* duty_info = g_ipt_main._ipt_pad->act_track_start(&point, 1, is_sym_hw, time);
    return g_ipt_main.set_duty_info_data(env, duty_info, o_duty);
}

jint jni_act_track_move(JNIEnv* env, jobject obj, jshortArray point, jobject o_duty, jlong time)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    jboolean is_copy = JNI_FALSE;
    jshort* jpoint = env->GetShortArrayElements(point, &is_copy);
    Uint32 jpoint_len = env->GetArrayLength(point);
    const iptcore::DutyInfo* duty_info = g_ipt_main._ipt_pad->act_track_move((s_Point_v2*)jpoint, jpoint_len, time);
    env->ReleaseShortArrayElements(point, jpoint, JNI_ABORT);

    return g_ipt_main.set_duty_info_data(env, duty_info, o_duty);
}

jint jni_act_track_move_xy(JNIEnv* env, jobject obj, jshort point_x, jshort point_y, jobject o_duty, jlong time)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    log_print(ANDROID_LOG_INFO, "JNIMsg", "jni_act_track_move_xy x:%d, y:%d", (int)point_x, (int)point_y);

    s_Point_v2 point = {static_cast<PLUINT16>(point_x), static_cast<PLUINT16>(point_y)};
    const iptcore::DutyInfo* duty_info = g_ipt_main._ipt_pad->act_track_move(&point, 1, time);
    return g_ipt_main.set_duty_info_data(env, duty_info, o_duty);
}

jint jni_act_track_end(JNIEnv* env, jobject obj, jshort point_x, jshort point_y, jobject o_duty, jlong time, jboolean is_gesture)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    log_print(ANDROID_LOG_INFO, "JNIMsg", "jni_act_track_end x:%d, y:%d", (int)point_x, (int)point_y);

    s_Point_v2 point = {static_cast<PLUINT16>(point_x), static_cast<PLUINT16>(point_y)};
    const iptcore::DutyInfo* duty_info = g_ipt_main._ipt_pad->act_track_end(point, time, is_gesture == JNI_TRUE);
    return g_ipt_main.set_duty_info_data(env, duty_info, o_duty);
}

/**
 * 设置Input的光标位置
 */
jint jni_act_input_cursor(JNIEnv* env, jobject obj, jint cursor_idx, jobject o_duty)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    const iptcore::DutyInfo* duty_info = g_ipt_main._ipt_pad->act_input_cursor(cursor_idx);
    return g_ipt_main.set_duty_info_data(env, duty_info, o_duty);
}

/**
 * Input的光标位置左移
 */
void jni_act_input_cursor_left(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return;
    }

    iptcore::InputPad::EventInputAction inputAction(iptcore::InputPad::CURSOR_MOVE_LEFT);
    g_ipt_main._ipt_pad->send_event(&inputAction);
}

/**
 * Input的光标位置右移
 */
void jni_act_input_cursor_right(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return;
    }

    iptcore::InputPad::EventInputAction inputAction(iptcore::InputPad::CURSOR_MOVE_RIGHT);
    g_ipt_main._ipt_pad->send_event(&inputAction);
}

/**
 * 光标发生了变化
 */
jint jni_act_edit_cursor_change(JNIEnv* env, jobject obj, jobject o_duty)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    log_print(ANDROID_LOG_INFO, "JNIMsg", "act_edit_cursor_change");

    const iptcore::DutyInfo *duty_info = g_ipt_main._ipt_pad->act_edit_cursor_change(
            iptcore::InputPad::CursorMoveType::CURSOR_MOVE_NORMAL);
    return g_ipt_main.set_duty_info_data(env, duty_info, o_duty);
}

/**
 * input区域rollback
 */
jint jni_act_input_pop(JNIEnv* env, jobject obj, jobject o_duty)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    log_print(ANDROID_LOG_INFO, "JNIMsg", "jni_act_input_pop");

    const iptcore::DutyInfo* duty_info = g_ipt_main._ipt_pad->act_input_pop();
    return g_ipt_main.set_duty_info_data(env, duty_info, o_duty);
}

/**
 * 导入符号列表内容
 */
jint jni_import_sym_list(JNIEnv* env, jobject obj, jobjectArray sym_name_array,
        jobjectArray sym_value_array, jobject o_duty)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }
    if (sym_name_array == NULL || sym_value_array == NULL)
    {
        const iptcore::DutyInfo* duty_info = g_ipt_main._ipt_pad->import_sym_list(NULL, 0, NULL);
        return g_ipt_main.set_duty_info_data(env, duty_info, o_duty);
    }
    jint len = env->GetArrayLength(sym_name_array);
    jint lenValues = env->GetArrayLength(sym_value_array);
    len = len > lenValues ? lenValues : len;
    if (len <= 0) {
        const iptcore::DutyInfo* duty_info = g_ipt_main._ipt_pad->import_sym_list(NULL, 0, NULL);
        return g_ipt_main.set_duty_info_data(env, duty_info, o_duty);
    }

    Uint16 *sym_names[len];
    Uint16 *sym_values[len];
    memset(sym_names, 0, sizeof(Uint16*) * len);
    memset(sym_values, 0, sizeof(Uint16*) * len);

    jboolean isCopy = JNI_FALSE;
    jstring sym_name[len];
    jstring sym_value[len];
    const jchar* jchar_sym_names[len];
    const jchar* jchar_sym_values[len];
    jint stringLen;
    for (jint i = 0; i < len; i++) {
        sym_name[i] = (jstring) env->GetObjectArrayElement(sym_name_array, i);
        stringLen = env->GetStringLength(sym_name[i]);
        const jchar* sym_item_jchar = env->GetStringChars(sym_name[i], &isCopy);
        Uint16* sym_item_buff = (Uint16*) malloc(sizeof(Uint16) * (stringLen + 1));
        g_ipt_main.jchar_to_wchar(sym_item_jchar, sym_item_buff, stringLen);
        sym_names[i] = sym_item_buff;
        jchar_sym_names[i] = sym_item_jchar;
    }
    for (jint i = 0; i < len; i++) {
        sym_value[i] = (jstring) env->GetObjectArrayElement(sym_value_array, i);
        stringLen = env->GetStringLength(sym_value[i]);
        const jchar* sym_value_jchar = env->GetStringChars(sym_value[i], &isCopy);
        Uint16* sym_value_buff = (Uint16*) malloc(sizeof(Uint16) * (stringLen + 1));
        g_ipt_main.jchar_to_wchar(sym_value_jchar, sym_value_buff, stringLen);
        sym_values[i] = sym_value_buff;
        jchar_sym_values[i] = sym_value_jchar;
    }

    const iptcore::DutyInfo* duty_info = g_ipt_main._ipt_pad->import_sym_list(
            (Uint16 **) sym_names, (Uint32) len, (Uint16 **) sym_values);

    for (jint i = 0; i < len; i++) {
        env->ReleaseStringChars(sym_name[i], jchar_sym_names[i]);
        env->ReleaseStringChars(sym_value[i], jchar_sym_values[i]);
        free(sym_names[i]);
        free(sym_values[i]);
    }
    return g_ipt_main.set_duty_info_data(env, duty_info, o_duty);
}

/**
 * 进入符号面板, 客户端设置lock信息
 */
void jni_pad_set_lock(JNIEnv* env, jobject obj,
        jintArray symCateList, jintArray symLockList)
{
    if (g_ipt_main._ipt_pad == NULL || symCateList == NULL || symLockList == NULL)
    {
        return;
    }
    jint len1 = env->GetArrayLength(symCateList);
    jint len2 = env->GetArrayLength(symLockList);
    jint len = len1 < len2 ? len1 : len2;

    jboolean isCopy = JNI_FALSE;
    jint * sym_cates = env->GetIntArrayElements(symCateList, &isCopy);
    jint * sym_locks = env->GetIntArrayElements(symLockList, &isCopy);
    g_ipt_main._ipt_pad->pad_set_lock((SYM_CATE *) sym_cates,
                                      (PLUINT32 *) sym_locks, (PLUINT32) len);
    env->ReleaseIntArrayElements(symCateList, sym_cates, JNI_ABORT);
    env->ReleaseIntArrayElements(symLockList, sym_locks, JNI_ABORT);
}

/**
 * 进入符号面板, 获取lock信息
 */
void jni_pad_get_lock(JNIEnv* env, jobject obj,
                                        jintArray symCateList, jintArray symLockList)
{
    if (g_ipt_main._ipt_pad == NULL || symCateList == NULL || symLockList == NULL)
    {
        return;
    }
    jint len1 = env->GetArrayLength(symCateList);
    jint len2 = env->GetArrayLength(symLockList);
    jint max = len1 < len2 ? len1 : len2;

    jboolean isCopy = JNI_FALSE;
    jint * sym_cates = env->GetIntArrayElements(symCateList, &isCopy);
    jint * sym_locks = env->GetIntArrayElements(symLockList, &isCopy);
    g_ipt_main._ipt_pad->pad_get_lock((SYM_CATE *) sym_cates,
                                      (PLUINT32 *) sym_locks, (PLUINT32) max);
    env->ReleaseIntArrayElements(symCateList, sym_cates, 0);
    env->ReleaseIntArrayElements(symLockList, sym_locks, 0);
}

/**
 * 符号调频, 导入需要调频的符号列表
 */
void jni_pad_set_sym_filter(JNIEnv* env, jobject obj,
                                        jobjectArray sym_list, jint count)
{
    int length = count;
    if (length < 0)
    {
        length = 0;
    }
    PLUINT16** syms = new PLUINT16*[length];
    PLUINT16 arr[length][4];
    if (NULL != sym_list)
    {
        jint i = 0;
        jint j = 0;
        jarray array;
        int size = env->GetArrayLength(sym_list);

        for (i = 0; i < size && i < length; i++)
        {
            array = (jarray) env->GetObjectArrayElement(sym_list, i);
            int len = env->GetArrayLength(array);

            jchar *data = env->GetCharArrayElements((jcharArray) array, 0);
            for (j = 0; j < len && j < 4; j++)
            {
                arr[i][j] = data[j];
            }
            syms[i] = arr[i];
            env->ReleaseCharArrayElements((jcharArray) array, data, 0);
        }
    }
    jint ret = -1;
    if (NULL != g_ipt_main._ipt_pad && NULL != syms)
    {
        g_ipt_main._ipt_pad->pad_set_sym_filter(syms, length);
    }
}

/**
 * 长按shift按下
 */
jint jni_act_shift_longdown(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    log_print(ANDROID_LOG_INFO, "JNIMsg", "jni_act_shift_longdown");

    return g_ipt_main._ipt_pad->act_shift_longdown();
}

/**
 * 长按shift抬起
 */
jint jni_act_shift_longup(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    log_print(ANDROID_LOG_INFO, "JNIMsg", "jni_act_shift_longup");

    return g_ipt_main._ipt_pad->act_shift_longup();
}

/**
 * 语音整句纠错
 */
jstring jni_act_correct_voicedata(JNIEnv* env, jobject obj, jstring joriResult)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return NULL;
    }

    jboolean isCopy = JNI_FALSE;
    const jchar* ori_result = env->GetStringChars(joriResult, &isCopy);
    jint size = env->GetStringLength(joriResult);
    Uint16* ori_result_buff = (Uint16*) malloc(sizeof(Uint16) * (size + 1));
    g_ipt_main.jchar_to_wchar(ori_result, ori_result_buff, size);

    memset(g_ipt_main._g_string_buff, 0, g_ipt_main.MAX_OUTPUT * sizeof(Uint16));
    Uint32 correct_words_cnt = 0;
    jint ret = g_ipt_main._ipt_pad->act_correct_voicedata(ori_result_buff, (PLINT32) size,
                                                          (Uint16*) g_ipt_main._g_string_buff,
                                                          &correct_words_cnt);
    jstring result = NULL;
    if (ret >= 0)
    {
        result = g_ipt_main.uni_to_str(env, g_ipt_main._g_string_buff);
    }
    else
    {
        result = joriResult;
    }
    env->ReleaseStringChars(joriResult, ori_result);

    return result;
}

jint jni_act_correct_voicesend(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }
    return g_ipt_main._ipt_pad->act_correct_voicesend();
}

jint jni_act_check_clip(JNIEnv* env, jobject obj, jcharArray uniArray, jint uniLen)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }
    jboolean isCopy = JNI_FALSE;
    jchar *uni = env->GetCharArrayElements(uniArray, &isCopy);
    jint ret = (jint) g_ipt_main._ipt_pad->act_check_clip((Uint16*) uni, (Uint32) uniLen);
    env->ReleaseCharArrayElements(uniArray, uni, JNI_ABORT);
    return ret;
}

jint jni_act_adjust_emoji_relation(JNIEnv* env, jobject obj, jint emoji_value, jint cellid)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }
    return g_ipt_main._ipt_pad->act_adjust_emoji_relation(emoji_value, cellid);
}

jint jni_get_input_show(JNIEnv* env, jobject obj, jobject o_show_info, jbyteArray o_cursor_info,
                                          jbyteArray o_autofix_info)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    const iptcore::ShowInfo* show_info = g_ipt_main._ipt_pad->get_input_show();
    return g_ipt_main.set_show_info_data(env, show_info, o_show_info, o_cursor_info, o_autofix_info);
}

jint jni_get_cand_count(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    Uint32 ret = g_ipt_main._ipt_pad->get_cand_count();

    log_print(ANDROID_LOG_INFO, "JNIMsg", "get_cand_count:%d, ", ret);

    return ret;
}

jint jni_get_list_count(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    Uint32 ret = g_ipt_main._ipt_pad->get_list_count();

    log_print(ANDROID_LOG_INFO, "JNIMsg", "get_list_count:%d, ", ret);

    return ret;
}

jint jni_get_cand_item(JNIEnv* env, jobject obj, jint cand_idx, jobject o_cand_info)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    const iptcore::CandInfo* cand_info = g_ipt_main._ipt_pad->get_cand_item(cand_idx);
    return g_ipt_main.set_cand_info_data(env, cand_info, o_cand_info);
}


jint jni_get_rare_cand_count(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    Uint32 ret = g_ipt_main._ipt_pad->get_rare_cand_count();

    log_print(ANDROID_LOG_INFO, "JNIMsg", "get_rare_cand_count:%d, ", ret);

    return ret;
}

jint jni_get_rare_cand_item(JNIEnv* env, jobject obj, jint cand_idx, jobject o_cand_info)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    const iptcore::CandInfo* cand_info = g_ipt_main._ipt_pad->get_rare_cand_item(cand_idx);
    return g_ipt_main.set_cand_info_data(env, cand_info, o_cand_info);
}

/**
 * 获取list的具体信息
 */
jint jni_get_list_item(JNIEnv* env, jobject obj, jint list_idx, jobject o_list_item)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    Uint16 listBuffer[128];
    Uint32 ret = g_ipt_main._ipt_pad->get_list_item(list_idx, listBuffer);
    jstring str = g_ipt_main.uni_to_str(env, listBuffer);

    return g_ipt_main.set_list_info_data(env, str, ret, o_list_item);
}

jint jni_get_sug_count(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    return g_ipt_main._ipt_pad->get_sug_count();
}

jint jni_get_sug_ad_timeout_ms(JNIEnv* env, jobject obj) {
    if (g_ipt_main._ipt_pad == NULL) {
        return -1;
    }
    return g_ipt_main._ipt_pad->get_sug_adx_timeout_ms();
}

jint jni_get_ai_cand_item_count(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    return g_ipt_main._ipt_pad->get_ai_cand_count();
}

jint jni_get_sug_item(JNIEnv* env, jobject obj, jint sug_idx, jobject o_sug_info)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    const iptcore::CandInfo* cand_info = g_ipt_main._ipt_pad->get_sug_item(sug_idx);
    return g_ipt_main.set_cand_info_data(env, cand_info, o_sug_info);
}

jint jni_get_sug_select(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    return g_ipt_main._ipt_pad->get_sug_select();
}

jint jni_get_sug_card_count(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    return g_ipt_main._ipt_pad->get_sug_card_count();
}

jint jni_get_sug_card_item(JNIEnv* env, jobject obj, jint sug_card_idx, jobject o_sug_card)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    const iptcore::SugCardInfo* sug_card_info = g_ipt_main._ipt_pad->get_sug_card_item(sug_card_idx);
    return g_ipt_main.set_sug_card_info_data(env, sug_card_info, o_sug_card);
}

jint jni_get_sug_card_select(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    return g_ipt_main._ipt_pad->get_sug_card_select();
}

//virtual Uint32 get_cand_context(Uint32 idx, Uint16* output) = 0;  ///< 获取盲人辅助模式词内容

/**
 * 获取候选词长按后得到选项数目
 */
jint jni_get_cand_info_count(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return 0;
    }
    return g_ipt_main._ipt_pad->get_cand_info_count();
}

/**
 * 获取候选词长按后得到的信息
 */
jstring jni_get_cand_info(JNIEnv* env, jobject obj, jint idx)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return NULL;
    }
    Uint16 output[g_ipt_main.STRING_BUFFER_LEN];
    Int32 ret = g_ipt_main._ipt_pad->get_cand_info(idx, output);
    if (ret > 0)
    {
        return g_ipt_main.uni_to_str(env, output, ret);
    }
    return NULL;
}

/**
 * 获取联系人信息个数
 */
jint jni_get_contact_count(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return 0;
    }
    return g_ipt_main._ipt_pad->get_contact_count();
}

/**
 * 获取联系人属性
 */
jint jni_get_contact_item(JNIEnv* env, jobject obj, jint idx, jobject o_contact_item)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }
    iptcore::ContactItem* contact_item = g_ipt_main._ipt_pad->get_contact_item(idx);
    return g_ipt_main.set_contact_item_data(env, contact_item, o_contact_item);
}

/**
 * 获取触发词个数
 */
jint jni_get_trigger_items_count(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return 0;
    }
    return g_ipt_main._ipt_pad->get_trigger_itms_cnt();
}

/**
 * 获取通用触发词内容
 */
jint jni_get_trigger_item(JNIEnv* env, jobject obj, jint idx, jobject o_trigger_item)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    const iptcore::TriggerItem* trigger_item = g_ipt_main._ipt_pad->get_trigger_itm(idx);
    return g_ipt_main.set_common_trigger_word_item_data(env, trigger_item, o_trigger_item);
}

/**
 * 获取地图触发词个数
 */
jint jni_get_map_trigger_items_count(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return 0;
    }
    return g_ipt_main._ipt_pad->get_map_trigger_itms_cnt();
}

/**
 * 获取通用触发词内容
 */
jint jni_get_map_trigger_item(JNIEnv* env, jobject obj, jint idx, jobject o_map_item)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    const iptcore::MapItem* map_item = g_ipt_main._ipt_pad->get_map_trigger_itm(idx);
    return g_ipt_main.set_map_trigger_word_item_data(env, map_item, o_map_item);
}

/**
 * 获取云端意图卡片的数量
 */
jint jni_get_cloud_intention_cnt(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return 0;
    }
    return g_ipt_main._ipt_pad->get_cloud_intention_cnt();
}

/**
 * 获取云端意图卡片的数据
 */
jint jni_get_cloud_intention_item(JNIEnv* env, jobject obj, jint idx, jobject o_intent_item)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    const iptcore::IntentionItem* intent_item = g_ipt_main._ipt_pad->get_cloud_intention_itm(idx);
    return g_ipt_main.set_cloud_intention_itm_data(env, intent_item, o_intent_item);
}

/**
 * 获取触发词id
 */
jlong jni_get_trigger_items_id(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return 0;
    }
    return g_ipt_main._ipt_pad->get_trigger_itms_id();
}

jint jni_get_pad_id(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    return g_ipt_main._ipt_pad->get_pad_id();
}

jint jni_get_hw_smart_delay_time(JNIEnv* env, jobject obj) {
    if (g_ipt_main._ipt_pad == NULL) {
        return -1;
    }
    return g_ipt_main._ipt_pad->get_hw_smart_delay_time();
}

jint jni_state_get_track_type(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    return g_ipt_main._ipt_pad->state_get_track_type();
}

jboolean jni_state_get_is_accept_track(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return false;
    }

    return g_ipt_main._ipt_pad->state_get_track_type();
}

jstring jni_get_cand_context(JNIEnv* env, jobject obj, jint idx)
{
    if (g_ipt_main._ipt_pad == NULL || idx < 0)
    {
        return NULL;
    }
    jstring str = NULL;
    Uint16 unicode[64];
    memset(unicode, 0, 64 * sizeof(Uint16));

    int ret = g_ipt_main._ipt_pad->get_cand_context(idx, unicode);
    if (ret > 0) {
        str = g_ipt_main.uni_to_str(env, unicode);
    }
    return str;
}

jcharArray jni_get_egg(JNIEnv* env, jobject obj)
{
    jcharArray eggstr = NULL;
    if (g_ipt_main._ipt_pad != NULL)
    {
        Uint16 egg[64];
        memset(egg, 0, 64 * sizeof(Uint16));

        int ret = g_ipt_main._ipt_pad->get_egg_count((Uint16 *)egg);
        if (ret > 0)
        {
            eggstr = (jcharArray) env->NewCharArray(ret);
            for (Uint32 i = 0; i < ret; i++)
            {
                const jchar element_tmp = (const jchar) egg[i];
                env->SetCharArrayRegion(eggstr, i, 1, (&element_tmp));
            }
        }
    }
    return eggstr;
}

jint jni_get_srv_cloud_white_ver(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return 0;
    }
    return g_ipt_main._ipt_pad->get_srv_cloud_white_ver();
}

jint jni_get_sug_type(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return 0; //TYPE_NONE
    }
    return g_ipt_main._ipt_pad->get_sug_type();
}

jint jni_get_sug_action_type(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return 0; //TYPE_NONE
    }
    return g_ipt_main._ipt_pad->get_sug_action_type();
}

jint jni_get_sug_action_type_by_index(JNIEnv* env, jobject obj, jint index)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return 0; //TYPE_NONE
    }
    return g_ipt_main._ipt_pad->get_sug_action_type(index);
}

jint jni_get_sug_id(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return 0; //TYPE_NONE
    }
    return g_ipt_main._ipt_pad->get_sug_id();
}

jstring jni_get_sug_ad_global_id(JNIEnv* env, jobject obj, jint index)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return NULL;
    }

    const Uint8* text_ptr = g_ipt_main._ipt_pad->sug_global_id_str(index);
    const Uint32 len = g_ipt_main._ipt_pad->sug_global_id_len(index);
    return g_ipt_main.utf_to_str(env, text_ptr, len);
}

jint jni_get_sug_source_id(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return 0; //TYPE_NONE
    }
    return g_ipt_main._ipt_pad->get_sug_source_id();
}

jstring jni_get_sug_source_msg(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return NULL;
    }
    const Uint16* source_msg = g_ipt_main._ipt_pad->get_sug_source_msg();
    const Uint32 len = g_ipt_main._ipt_pad->get_sug_source_msg_len();
    return g_ipt_main.uni_to_str(env, source_msg, len);
}

void jni_cfg_set_pad_layout(JNIEnv* env, jobject obj, jintArray jrect, jboolean value)
{
    jboolean isCopy = JNI_FALSE;
    if (g_ipt_main._ipt_pad != NULL && jrect != NULL)
    {
        jint * rect = env->GetIntArrayElements(jrect, &isCopy);
        s_Point_v2 TL;
        s_Point_v2 BR;

        memset(&TL, 0, sizeof(s_Point_v2));
        TL.x = rect[0];
        TL.y = rect[1];

        memset(&BR, 0, sizeof(s_Point_v2));
        BR.x = rect[2];
        BR.y = rect[3];

        g_ipt_main._ipt_pad->cfg_set_pad_layout(&TL, &BR, value);

        env->ReleaseIntArrayElements(jrect, rect, JNI_ABORT);
    }
}

void jni_cfg_set_pad_key_pos(JNIEnv* env, jobject obj, jbyte key, jintArray jvrect, jintArray jtrect)
{
    jboolean isCopy = JNI_FALSE;
    if (g_ipt_main._ipt_pad != NULL && jvrect != NULL && jtrect != NULL)
    {
        jint * v_rect = env->GetIntArrayElements(jvrect, &isCopy);
        jint * t_rect = env->GetIntArrayElements(jtrect, &isCopy);
        s_Point_v2 TL;
        s_Point_v2 BR;

        memset(&TL, 0, sizeof(s_Point_v2));
        TL.x = v_rect[0];
        TL.y = v_rect[1];

        memset(&BR, 0, sizeof(s_Point_v2));
        BR.x = v_rect[2];
        BR.y = v_rect[3];

        s_Point_v2 touch_TL;
        s_Point_v2 touch_BR;
        memset(&touch_TL, 0, sizeof(s_Point_v2));
        touch_TL.x = t_rect[0];
        touch_TL.y = t_rect[1];

        memset(&touch_BR, 0, sizeof(s_Point_v2));
        touch_BR.x = t_rect[2];
        touch_BR.y = t_rect[3];

        g_ipt_main._ipt_pad->cfg_set_pad_key_pos(&TL, &BR, &touch_TL, &touch_BR, key);

        env->ReleaseIntArrayElements(jvrect, v_rect, JNI_ABORT);
        env->ReleaseIntArrayElements(jtrect, t_rect, JNI_ABORT);
    }
}

jint jni_cfg_get_touched_key(JNIEnv* env, jobject obj, jint px, jint py)
{
    jint ret = 0;
    if (g_ipt_main._ipt_pad != NULL)
    {
        s_Point_v2 inPoint;
        memset(&inPoint, 0, sizeof(s_Point_v2));

        inPoint.x = px;
        inPoint.y = py;
        // todo 待接入thp功能默认设置为0，接入后传入对应的值
        ret = g_ipt_main._ipt_pad->cfg_get_touched_key(&inPoint, 0, 0, 0);
    }
    return ret;
}

jint jni_get_sug_state(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return 0; // PadSugState.PAD_SUG_STATE_NONE
    }
    return g_ipt_main._ipt_pad->get_sug_state();
}

void jni_act_cur_sug_close(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return;
    }
    g_ipt_main._ipt_pad->act_cur_sug_close();
}

jint jni_get_py_hotletter(JNIEnv* env, jobject obj, jbyteArray hotLetter)
{
    // n5G内核已删除
    return -1;
//    if (g_ipt_main._config_items == NULL)
//    {
//        return -1;
//    }
//    jboolean isCopy = JNI_FALSE;
//    jint length = (jint) env->GetArrayLength(hotLetter);
//
//    if (length != 27)
//    {
//        return -1;
//    }
//    jbyte *letter = env->GetByteArrayElements(hotLetter, &isCopy);
//    Int32 ret = g_ipt_main._config_items->cfg_util_get_py_hotletter((char*) letter);
//    env->ReleaseByteArrayElements(hotLetter, letter, 0);
//    return ret;
}

void jni_user_trace_start_pad_key_layout(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return;
    }
    Int32 ret = g_ipt_main._ipt_pad->user_trace_start_pad_key_layout();
    LOGI("user_trace_start_pad_key_layout, ret%d", ret);
}

void jni_user_trace_finish_pad_key_layout(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return;
    }
    Int32 ret = g_ipt_main._ipt_pad->user_trace_finish_pad_key_layout();
    LOGI("user_trace_start_pad_key_layout, ret:%d", ret);
}

void jni_user_trace_write(JNIEnv* env, jobject obj, jint action, jint container,
        jbyteArray jcontent)
{
    if (g_ipt_main._ipt_pad == NULL || jcontent == NULL)
    {
        return;
    }

    jboolean isCopy = JNI_FALSE;
    jbyte *content = env->GetByteArrayElements(jcontent, &isCopy);
    jint len = env->GetArrayLength(jcontent);

    int ret = g_ipt_main._ipt_pad->user_trace_write((Uint8) action, (Uint8) container,
            content, (Uint32) len);
    LOGI("user_trace_write, action=%d,container=%d,contentLen=%d,ret=%d",
            action, container, len,ret);
    env->ReleaseByteArrayElements(jcontent, content, JNI_ABORT);
}

void jni_user_voice_input_log(JNIEnv* env, jobject obj, jstring value)
{
    if (g_ipt_main._ipt_pad == NULL || value == NULL)
    {
        return;
    }

    jboolean isCopy = JNI_FALSE;
    Uint32 len = env->GetStringUTFLength(value);
    const char* chars = env->GetStringUTFChars(value, &isCopy);
    int ret = g_ipt_main._ipt_pad->user_voice_input_log(reinterpret_cast<const Uint8 *>(chars), (Uint32) len);
    LOGI("jni_user_voice_input_log, contentLen=%d,ret=%d", len, ret);
    env->ReleaseStringUTFChars(value, chars);
}

JNIEXPORT jint JNICALL jni_callback(JNIEnv * env, jobject obj, jint tag, jlong arg0)
{
    if (g_ipt_main._ipt_env == NULL)
    {
        return -1;
    }

    g_ipt_main._ipt_env->perform_callback(static_cast<Uint32>(tag), static_cast<Tsize>(arg0));
    return 0;
}

/**
 * 云输入结果返回的回调
 */
JNIEXPORT jint JNICALL jni_on_net_recv(JNIEnv* env, jobject obj, jobject stream, jlong callback_ptr,
                                       jint errorCode, jbyteArray response_data)
{
    if (g_ipt_main._ipt_net_man == NULL)
    {
        return -1;
    }

    jboolean isCopy = JNI_FALSE;
    jbyte *recv_data = NULL;
    jint len = 0;
    if (response_data != NULL)
    {
        recv_data = env->GetByteArrayElements(response_data, &isCopy);
        len = env->GetArrayLength(response_data);
        g_ipt_main._ipt_net_man->on_net_recv(stream, static_cast<Tsize>(callback_ptr),
                                             (Int32) errorCode, (Uint8 *)recv_data, (Uint32) len);
        env->ReleaseByteArrayElements(response_data, recv_data, JNI_ABORT);

    }
    else
    {
        LOGI("jni_on_net_recv#response_data is null.");
        g_ipt_main._ipt_net_man->on_net_recv(stream, static_cast<Tsize>(callback_ptr), (Int32) errorCode, NULL, 0);
    }
    return 0;
}

jint jni_set_ime_service_callback(JNIEnv* env, jobject obj, jobject ime_service_callback)
{
    if (g_ipt_main._ipt_env == NULL)
    {
        return -1;
    }

    g_ipt_main._ipt_env->set_ime_service_callback(env, ime_service_callback);
    return 0;
}

jstring jni_get_prot_code(JNIEnv *env, jobject obj)
{
    const char *tmpstr = "gp32fcd49e20190809194258";
    return env->NewStringUTF(tmpstr);
}

static iptcore::ZjForecastModel *zjForecastModel = NULL;

void jni_check_file_md5
        (JNIEnv *env, jobject obj, jstring jfileName, jbyteArray jMD5Digest)
{
    jboolean isCopy = JNI_FALSE;
    const char *fileName = env->GetStringUTFChars(jfileName, &isCopy);

    if (NULL == fileName) {
        return;
    }
    int size = env->GetArrayLength(jMD5Digest);

    if (size < 32) {
        return;
    }
    jbyte *MD5Digest = env->GetByteArrayElements(jMD5Digest, &isCopy);

    FILE *file;
    MD5_CTX context;
    int len;
    int i;
    unsigned char buffer[1024];
    unsigned char digest[16];

    if ((file = fopen(fileName, "rb")) == NULL) {
        LOGI("%s can't be opened", fileName);
    } else {
        MD5Init(&context);

        while ((len = fread(buffer, 1, 1024, file))) {
            MD5Update(&context, buffer, len);
        }

        MD5Final(digest, &context);

        jbyte value;
        for (i = 0; i < 16; i++) {
            value = (jbyte) (digest[i] / 16);
            env->SetByteArrayRegion(jMD5Digest, 2 * i, 1, (const jbyte *) (&value));
            value = (jbyte) (digest[i] % 16);
            env->SetByteArrayRegion(jMD5Digest, 2 * i + 1, 1, (const jbyte *) (&value));
        }

        fclose(file);
    }

    env->ReleaseStringUTFChars(jfileName, fileName);
    env->ReleaseByteArrayElements(jMD5Digest, MD5Digest, JNI_ABORT);
}

/**
 * 设置shift状态
 */
jint jni_act_change_shift(JNIEnv* env, jobject obj, jint shiftId, jobject o_duty)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }
    log_print(ANDROID_LOG_INFO, "JNIMsg", "change shift:%d", shiftId);

    const iptcore::DutyInfo* duty_info = g_ipt_main._ipt_pad->act_change_shift((iptcore::ConfigItems::CfgEnShift) shiftId);
    return g_ipt_main.set_duty_info_data(env, duty_info, o_duty);
}

/**
 * 获取智能云词卡片的Loading的状态
 */
jboolean jni_get_ai_pad_loading_state(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return false;
    }

    const iptcore::AiPadInfo* ai_pad_info = g_ipt_main._ipt_pad->get_ai_pad();

    if (ai_pad_info == NULL) {
        return false;
    }

    return ai_pad_info->get_ai_pad_is_loading();
}

/**
 * 获取智能云词卡片的Loading的状态
 */
jboolean jni_get_ai_bubble_need_show(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return false;
    }
    return g_ipt_main._ipt_pad->is_bubble_need_show();
}

/**
 * 获取智能云词Tab的状态
 */
jint jni_get_ai_pad_tab(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    const iptcore::AiPadInfo* ai_pad_info = g_ipt_main._ipt_pad->get_ai_pad();

    if (ai_pad_info == NULL) {
        return -1;
    }

    return ai_pad_info->get_ai_pad_tab();
}

/**
 * 获取智能云词面板的错误状态，是否符合发起条件，以及不发起的原因等
 */
jint jni_get_ai_pad_state(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    const iptcore::AiPadInfo* ai_pad_info = g_ipt_main._ipt_pad->get_ai_pad();

    if (ai_pad_info == NULL) {
        return -1;
    }

    return ai_pad_info->get_pad_state();
}

/**
 * 获取智能云词面板的候选词个数
 */
jint jni_get_ai_pad_cnt(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    const iptcore::AiPadInfo* ai_pad_info = g_ipt_main._ipt_pad->get_ai_pad();

    if (ai_pad_info == NULL) {
        return -1;
    }

    return ai_pad_info->get_cand_cnt();
}

/**
 * 获取智能云词面板的候选词
 */
jint jni_get_ai_pad_item(JNIEnv* env, jobject obj, jint cand_idx, jobject o_cand_info)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    const iptcore::AiPadInfo* ai_pad_info = g_ipt_main._ipt_pad->get_ai_pad();

    if (ai_pad_info == NULL)
    {
        return -1;
    }

    const iptcore::CandInfo* cand_info = ai_pad_info->get_cand_item(cand_idx);
    return g_ipt_main.set_cand_info_data(env, cand_info, o_cand_info);
}

/**
 * 获取智能云词的显示文本(Ai纠错待修改文本, 字符表情的请求文本 等)
 */
jstring jni_get_ai_pad_origin_text(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return NULL;
    }

    const iptcore::AiPadInfo* ai_pad_info = g_ipt_main._ipt_pad->get_ai_pad();
    if (ai_pad_info == NULL) {
        return NULL;
    }

    const Uint16* text_ptr = ai_pad_info->get_text_ptr();
    const Uint32 len = ai_pad_info->get_text_len();
    return g_ipt_main.uni_to_str(env, text_ptr, len);
}

/**
 * 获取智能云词的纠错预上屏信息（仅Android）
 */
jint jni_ai_correct_inline_info(JNIEnv* env, jobject obj, jobject o_cand_info)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    const iptcore::AiPadInfo* ai_pad_info = g_ipt_main._ipt_pad->get_ai_pad();

    if (ai_pad_info == NULL)
    {
        return -1;
    }

    const iptcore::CandInfo* cand_info = ai_pad_info->get_ai_correct_inline_info();
    return g_ipt_main.set_cand_info_data(env, cand_info, o_cand_info);
}

/**
 * 获取当前Ai助聊面板是否为自动展开(自动展开的不需要记忆tab)
 */
jboolean jni_ai_pad_is_auto_open(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    const iptcore::AiPadInfo* ai_pad_info = g_ipt_main._ipt_pad->get_ai_pad();

    if (ai_pad_info == NULL)
    {
        return -1;
    }

    return ai_pad_info->is_auto_open();
}

/**
 * 获取智能云词次Cand条的候选词
 */
jint jni_get_ai_cand(JNIEnv* env, jobject obj, jobject o_cand_info)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    const iptcore::CandInfo* cand_info = g_ipt_main._ipt_pad->get_ai_cand();
    return g_ipt_main.set_cand_info_data(env, cand_info, o_cand_info);
}


/**
 * 获取小红书标题场景内核返回的所有结果
 */
jint jni_get_ai_cand_item(JNIEnv* env, jobject obj, jint cand_idx, jobject o_cand_info)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    const iptcore::CandInfo* cand_info = g_ipt_main._ipt_pad->get_ai_cand(cand_idx);
    return g_ipt_main.set_cand_info_data(env, cand_info, o_cand_info);
}

/**
 * 获取智能云词次Cand条的Icon状态（是否处于Loading等）
 */
jint jni_get_ai_icon_state(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return -1;
    }

    return g_ipt_main._ipt_pad->get_ai_icon_state();
}

/**
 * 敏感词词库卸载
 */
jboolean jni_cfg_check_hit_black_list(JNIEnv* env, jobject obj, jstring content)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return false;
    }

    jboolean is_copy = false;
    Uint32 content_len = env->GetStringLength(content);
    const jchar* content_jchar = env->GetStringChars(content, &is_copy);
    bool ret = g_ipt_main._ipt_pad->check_hit_black_list((Uint16*) content_jchar, content_len);
    env->ReleaseStringChars(content, content_jchar);
    return ret;
}

/**
 * 加密pb请求数据
 */
jbyteArray jni_pbdata_request_aichat_card(JNIEnv *env, jobject obj, jint check_id,
                                       jbyteArray inContent) {
    jbyteArray result = nullptr;
    if (g_ipt_main._ipt_pad == nullptr) {
        return result;
    }
    if (inContent != nullptr) {
        jsize size = env->GetArrayLength(inContent);
        if (size > 0) {
            jbyte* in_content_jbyte = env->GetByteArrayElements(inContent, nullptr);
            // 申请32k内存
            auto* content_uni = (Uint8*) malloc(sizeof(Uint8) * 1024 * 32);
            Uint32 length = g_ipt_main._ipt_pad->pbdata_request_aichat_card(
                    content_uni,
                    reinterpret_cast<Uint32 &>(check_id),
                    (Uint8*) in_content_jbyte,
                    size);

            if (length > 0) {
                result = env->NewByteArray(length);
                env->SetByteArrayRegion(result, 0, length,
                                        reinterpret_cast<const jbyte *>(content_uni));
            }
            env->ReleaseByteArrayElements(inContent, in_content_jbyte, JNI_ABORT);
            free(content_uni);
        }
    }
    return result;
}

/**
 * 解密pb返回数据
 */
jbyteArray jni_pbdata_response_aichat_card(JNIEnv *env, jobject obj, jint check_id,
                                           jbyteArray inContent) {
    jbyteArray result = nullptr;
    if (g_ipt_main._ipt_pad == nullptr) {
        return result;
    }
    if (inContent != nullptr) {
        jsize size = env->GetArrayLength(inContent);
        if (size > 0) {
            jbyte *in_content_jbyte = env->GetByteArrayElements(inContent, nullptr);
            // 申请32k内存
            auto *content_uni = (Uint8 *) malloc(sizeof(Uint8) * 1024 * 32);
            Uint32 length = g_ipt_main._ipt_pad->pbdata_response_aichat_card(
                    content_uni,
                    reinterpret_cast<Uint32 &>(check_id),
                    (Uint8 *) in_content_jbyte,
                    size);

            if (length > 0) {
                result = env->NewByteArray(length);
                env->SetByteArrayRegion(result, 0, length,
                                        reinterpret_cast<const jbyte *>(content_uni));
            }
            env->ReleaseByteArrayElements(inContent, in_content_jbyte, JNI_ABORT);
            free(content_uni);
        }
    }
    return result;
}

void jni_act_click_toolbar(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return;
    }
    g_ipt_main._ipt_pad->act_click_toolbar();
}

/**
 * 获取内核文心mlm模型so的版本号 = 0 表示so是空，>0 表示版本号
 * 32位返回0表示不支持
 */
jint jni_get_mlm_so_version(JNIEnv* env) {
    return iptcore::InputLib::get_mlm_so_version();
}

/**
 * 获取传入文心模型文件版本号,用于下发与安装等埋点
 * -1表示so版本不兼容无法获取(32 位也返回 -1)
 * = 0 表示文件是空，>0 表示版本号
 */
jint jni_get_mlm_dict_version(JNIEnv* env, jobject obj, jstring filePath) {
    const char* patch = env->GetStringUTFChars(filePath, nullptr);
    Int32 ret = iptcore::InputLib::get_mlm_dict_version(patch);
    env->ReleaseStringUTFChars(filePath, patch);
    return ret;
}

jstring jni_get_core_version_str(JNIEnv* env) {
    const char *ver = iptcore::InputLib::core_version();
    return env->NewStringUTF(ver);
}

jint jni_apply_patch(JNIEnv* env, jobject obj, jint type, jstring patch_path, jstring target_md5
    , jstring original_file_path) {
    return g_ipt_main.apply_patch(env, type, patch_path, target_md5, original_file_path);
}

/**
 * 获取inline输入码
 *
 * @return inline输入码
 */
jstring jni_cfg_get_inline_show(JNIEnv* env, jobject obj)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return NULL;
    }
    Uint32 len = 0;
    // 返回指向inline输入码字符串的指针，len被赋值为字符串长度
    const Uint16* inline_ptr = g_ipt_main._ipt_pad->get_inline_show(len);
    if (len > 0)
    {
        jstring str = g_ipt_main.uni_to_str(env, inline_ptr, len);
        return str;
    }
    return NULL;
}

void jni_ipt_dict_rebuild_from_old_usr2(JNIEnv* env, jobject obj,
                                        jstring joutput_dir, jstring jimm_dir,
                                        jstring jusr2_path, jstring jvkword_path)
{
	jboolean is_copy = false;
    const char* output_dir = env->GetStringUTFChars(joutput_dir, &is_copy);
    const char* imm_dir = env->GetStringUTFChars(jimm_dir, &is_copy);
    const char* usr2_path = env->GetStringUTFChars(jusr2_path, &is_copy);
    const char* vkword_path = env->GetStringUTFChars(jvkword_path, &is_copy);

    Int32 ret = ipt_dict_rebuild_from_old_usr2(output_dir, imm_dir, usr2_path, vkword_path);
    LOGI("jni_ipt_dict_rebuild_from_old_usr2 fileName %s,%s，%d", usr2_path, vkword_path, ret);

    env->ReleaseStringUTFChars(jimm_dir, imm_dir);
    env->ReleaseStringUTFChars(joutput_dir, output_dir);
    env->ReleaseStringUTFChars(jusr2_path, usr2_path);
    env->ReleaseStringUTFChars(jvkword_path, vkword_path);
}

void jni_ipt_dict_rebuild_from_old_ue2(JNIEnv* env, jobject obj,
                                       jstring joutput_dir, jstring jimm_dir, jstring jue2_path,
                                       jstring jlearn_dic1_path, jstring jlearn_dic2_path)
{
	jboolean is_copy = false;
    const char* output_dir = env->GetStringUTFChars(joutput_dir, &is_copy);
    const char* imm_dir = env->GetStringUTFChars(jimm_dir, &is_copy);
    const char* ue2_path = env->GetStringUTFChars(jue2_path, &is_copy);
    const char* learn_dic1_path = env->GetStringUTFChars(jlearn_dic1_path, &is_copy);
    const char* learn_dic2_path = env->GetStringUTFChars(jlearn_dic2_path, &is_copy);

    Int32 ret = ipt_dict_rebuild_from_old_ue2(output_dir, imm_dir, ue2_path,
                                              learn_dic1_path, learn_dic2_path);
    LOGI("jni_ipt_dict_rebuild_from_old_ue2 fileName=%s, %d", ue2_path, ret);

    env->ReleaseStringUTFChars(jlearn_dic2_path, learn_dic2_path);
    env->ReleaseStringUTFChars(jlearn_dic1_path, learn_dic1_path);
    env->ReleaseStringUTFChars(jue2_path, ue2_path);
    env->ReleaseStringUTFChars(jimm_dir, imm_dir);
    env->ReleaseStringUTFChars(joutput_dir, output_dir);
}

void jni_ipt_dict_rebuild_from_old_keyword(JNIEnv* env, jobject obj,
    jstring jkwd_keyword_path, jstring jkeyword_path)
{
	jboolean is_copy = false;
    const char* kwd_keyword_path = env->GetStringUTFChars(jkwd_keyword_path, &is_copy);
    const char* keyword_path = env->GetStringUTFChars(jkeyword_path, &is_copy);

    Int32 ret = ipt_dict_rebuild_from_old_keyword(kwd_keyword_path, keyword_path);
    LOGI("jni_ipt_dict_rebuild_from_old_keyword fileName=%s %d", keyword_path, ret);

    env->ReleaseStringUTFChars(jkeyword_path, keyword_path);
    env->ReleaseStringUTFChars(jkwd_keyword_path, kwd_keyword_path);
}

void jni_ipt_dict_rebuild_from_old_zy_usr(JNIEnv* env, jobject obj,
    jstring joutput_dir, jstring jimm_dir, jstring jdown_dir, jstring jzy_usr_path)
{
    jboolean is_copy = false;
    const char* output_dir = env->GetStringUTFChars(joutput_dir, &is_copy);
    const char* imm_dir = env->GetStringUTFChars(jimm_dir, &is_copy);
    const char* down_dir = env->GetStringUTFChars(jdown_dir, &is_copy);
    const char* zy_usr_path = env->GetStringUTFChars(jzy_usr_path, &is_copy);

    Int32 ret = ipt_dict_rebuild_from_old_zy_usr(output_dir, imm_dir,
                                                 down_dir, zy_usr_path);
    LOGI("jni_ipt_dict_rebuild_from_old_zy_usr fileName=%s %d", zy_usr_path, ret);

    env->ReleaseStringUTFChars(jzy_usr_path, zy_usr_path);
    env->ReleaseStringUTFChars(jdown_dir, down_dir);
    env->ReleaseStringUTFChars(jimm_dir, imm_dir);
    env->ReleaseStringUTFChars(joutput_dir, output_dir);

}

void jni_ipt_dict_rebuild_from_old_zy(JNIEnv* env, jobject obj,
                                          jstring joutput_dir, jstring jimm_dir,
                                          jstring jhz_zy_path, jstring jcz_zy_path)
{
    jboolean is_copy = false;
    const char* output_dir = env->GetStringUTFChars(joutput_dir, &is_copy);
    const char* imm_dir = env->GetStringUTFChars(jimm_dir, &is_copy);
    const char* hz_zy_path = env->GetStringUTFChars(jhz_zy_path, &is_copy);
    const char* cz_zy_path = env->GetStringUTFChars(jcz_zy_path, &is_copy);

    Int32 ret = ipt_dict_rebuild_from_old_zy(output_dir, imm_dir,
                                                 hz_zy_path, cz_zy_path);
    LOGI("jni_ipt_dict_rebuild_from_old_zy fileName=%s %s %d", hz_zy_path, cz_zy_path, ret);

    env->ReleaseStringUTFChars(jcz_zy_path, cz_zy_path);
    env->ReleaseStringUTFChars(jhz_zy_path, hz_zy_path);
    env->ReleaseStringUTFChars(jimm_dir, imm_dir);
    env->ReleaseStringUTFChars(joutput_dir, output_dir);
}

void jni_ipt_dict_rebuild_from_old_cangjie(JNIEnv* env, jobject obj,
    jstring joutput_dir, jstring jcangjie_path, jboolean is_quick)
{
	jboolean is_copy = false;
    const char* output_dir = env->GetStringUTFChars(joutput_dir, &is_copy);
    const char* cangjie_path = env->GetStringUTFChars(jcangjie_path, &is_copy);

    Int32 ret = ipt_dict_rebuild_from_old_cangjie(output_dir, cangjie_path, is_quick == JNI_TRUE);
    LOGI("jni_ipt_dict_rebuild_from_old_cangjie fileName=%s %d", cangjie_path, ret);

    env->ReleaseStringUTFChars(jcangjie_path, cangjie_path);
    env->ReleaseStringUTFChars(joutput_dir, output_dir);
}


/**
 * 向内核发送地理位置信息
 */
void jni_send_location_event(JNIEnv* env, jobject obj,jfloat latitude, jfloat longitude)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return;
    }

    iptcore::InputPad::EventSetLocation locationEvent((float)latitude, (float)longitude);
    g_ipt_main._ipt_pad->send_event(&locationEvent);
}

/**
 * 点击网盟广告后通知内核（需要在sug点击通知内核后调用）
 */
void jni_send_wm_sug_ad_event(JNIEnv* env, jobject obj, jint index, jint wm_sug_action_type)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return;
    }
    iptcore::InputPad::EventWmSugAction sugAction((iptcore::InputPad::EventWmSugActionType) wm_sug_action_type, index);
    g_ipt_main._ipt_pad->send_event(&sugAction);
}

/**
 * 点击智能推荐卡片之后通知内核
 */
void jni_send_intention_card_event(JNIEnv* env, jobject obj, jint first_level, jint second_level, jstring title, jint action_type)
{
    if (g_ipt_main._ipt_pad == NULL)
    {
        return;
    }
    Uint16* uni_title = NULL;
    const jchar* unicode_jchar = NULL;
    jint len = 0;
    if (title != NULL)
    {
        jboolean isCopy = JNI_FALSE;
        unicode_jchar = env->GetStringChars(title, &isCopy);
        len = env->GetStringLength(title);
        uni_title = (Uint16*) malloc(sizeof(Uint16) * (len + 1));
        g_ipt_main.jchar_to_wchar(unicode_jchar, uni_title, len);
    }
    iptcore::InputPad::EventIntentionCardAction intention_card_action(
            (iptcore::CloudIntentionTypeFirstLevel) first_level,
            (iptcore::CloudIntentionTypeSecondLevel) second_level,
            uni_title, (iptcore::InputPad::IntentionCardActionType) action_type);
    g_ipt_main._ipt_pad->send_event(&intention_card_action);

    if (title != NULL && unicode_jchar != NULL && uni_title != NULL)
    {
        env->ReleaseStringChars(title, unicode_jchar);
        free(uni_title);
    }
}

/**
 * 加载sug广告
 */
void jni_load_core_sug_ad(JNIEnv* env, jobject obj, jint ad_trigger) {
    if (g_ipt_main._ipt_pad == NULL) {
        return;
    }
    iptcore::AdManager *adManager = g_ipt_main._ipt_pad->get_ad_manager((iptcore::AdManager::AdPos) ad_trigger);
    if (adManager == nullptr) {
        return;
    }
    adManager->load_ad();
}

/**
 * 获取内核广告数量
 */
jint jni_get_core_sug_ad_count(JNIEnv* env, jobject obj, jint ad_trigger) {
    if (g_ipt_main._ipt_pad == NULL) {
        return 0;
    }
    iptcore::AdManager *adManager = g_ipt_main._ipt_pad->get_ad_manager((iptcore::AdManager::AdPos) ad_trigger);
    if (adManager == nullptr) {
        return 0;
    }
    return adManager->get_res_cnt();
}

/**
 * 获取具体某个item的广告
 */
jint jni_get_core_sug_ad_at(JNIEnv* env, jobject obj, jint index, jobject ad_info, jint ad_trigger) {
    if (g_ipt_main._ipt_pad == NULL) {
        return -1;
    }
    iptcore::AdManager *adManager = g_ipt_main._ipt_pad->get_ad_manager((iptcore::AdManager::AdPos) ad_trigger);
    if (adManager == nullptr) {
        return -1;
    }

    const iptcore::AdManager::Response* sug_ad_info = adManager->get_res_item(index);
    return g_ipt_main.set_sug_ad_info_data(env, sug_ad_info, ad_info);
}

/**
 * 加载广告超时
 */
jint jni_core_sug_ad_time_out(JNIEnv* env, jobject obj, jint time, jint ad_trigger) {
    if (g_ipt_main._ipt_pad == NULL) {
        return -1;
    }
    iptcore::AdManager *adManager = g_ipt_main._ipt_pad->get_ad_manager((iptcore::AdManager::AdPos) ad_trigger);
    if (adManager == nullptr) {
        return -1;
    }
    adManager->time_out(time);
    return 0;
}

/**
 * win
 */
jint jni_core_sug_ad_win(JNIEnv* env, jobject obj, jint idx, jint ad_trigger) {
    if (g_ipt_main._ipt_pad == NULL) {
        return -1;
    }
    iptcore::AdManager *adManager = g_ipt_main._ipt_pad->get_ad_manager((iptcore::AdManager::AdPos) ad_trigger);
    if (adManager == nullptr) {
        return -1;
    }
    adManager->win(idx);
    return 0;
}

/**
 * fail
 */
jint jni_core_sug_ad_fail(JNIEnv* env, jobject obj, jint idx, jint reason, jlong price, jint ad_trigger) {
    if (g_ipt_main._ipt_pad == NULL) {
        return -1;
    }
    iptcore::AdManager *adManager = g_ipt_main._ipt_pad->get_ad_manager((iptcore::AdManager::AdPos) ad_trigger);
    if (adManager == nullptr) {
        return -1;
    }
    adManager->fail(idx, reason, price);
    return 0;
}

/**
 * Sug ad show
 */
jint jni_core_sug_ad_show(JNIEnv* env, jobject obj, jint index, jintArray bounds, jint ad_trigger) {
    if (g_ipt_main._ipt_pad == NULL) {
        return -1;
    }
    iptcore::AdManager *adManager = g_ipt_main._ipt_pad->get_ad_manager((iptcore::AdManager::AdPos) ad_trigger);
    if (adManager == nullptr) {
        return -1;
    }
    jint* border_point_array = env->GetIntArrayElements(bounds, JNI_FALSE);
    s_Rect_v2 rect;
    rect.TL.x = border_point_array[0];
    rect.TL.y = border_point_array[1];
    rect.BM.x = border_point_array[2];
    rect.BM.y = border_point_array[3];
    adManager->show(index, rect);
    env->ReleaseIntArrayElements(bounds, border_point_array, JNI_ABORT);
    return 0;
}


/**
 * click
 */
jint jni_core_sug_ad_click(JNIEnv* env, jobject obj, jint idx, jobject ad_action, jint ad_trigger, jintArray border, jintArray click_point) {
    if (g_ipt_main._ipt_pad == NULL) {
        return -1;
    }
    iptcore::AdManager *adManager = g_ipt_main._ipt_pad->get_ad_manager((iptcore::AdManager::AdPos) ad_trigger);
    if (adManager == nullptr) {
        return -1;
    }
    jint* border_point_array = env->GetIntArrayElements(border, JNI_FALSE);
    jint* click_point_array = env->GetIntArrayElements(click_point, JNI_FALSE);
    s_Rect_v2 rect;
    rect.TL.x = border_point_array[0];
    rect.TL.y = border_point_array[1];
    rect.BM.x = border_point_array[2];
    rect.BM.y = border_point_array[3];

    s_Point_v2 down_point;
    down_point.x = click_point_array[0];
    down_point.y = click_point_array[1];

    s_Point_v2 up_point;
    up_point.x = click_point_array[2];
    up_point.y = click_point_array[3];
    const iptcore::AdManager::AdAction action = adManager->click(idx, rect, down_point, up_point);
    env->ReleaseIntArrayElements(border, border_point_array, JNI_ABORT);
    env->ReleaseIntArrayElements(click_point, click_point_array, JNI_ABORT);

    jclass obj_class = env->GetObjectClass(ad_action);
    jmethodID set_url_id = env->GetMethodID(obj_class, "setUrl", "(Ljava/lang/String;)V");
    jmethodID set_type_id = env->GetMethodID(obj_class, "setType", "(I)V");

    const char* url = action.url;
    jstring url_str = g_ipt_main.utf_to_str(env, (Uint8 *) url, strlen(url));

    env->CallVoidMethod(ad_action, set_url_id, url_str);
    env->CallVoidMethod(ad_action, set_type_id, action.type);
    env->DeleteLocalRef(url_str);
    env->DeleteLocalRef(obj_class);
    return 0;
}

/**
 * download_start
 */
jint jni_core_sug_ad_download_start(JNIEnv* env, jobject obj, jint idx, jint ad_trigger) {
    if (g_ipt_main._ipt_pad == NULL) {
        return -1;
    }
    iptcore::AdManager *adManager = g_ipt_main._ipt_pad->get_ad_manager((iptcore::AdManager::AdPos) ad_trigger);
    if (adManager == nullptr) {
        return -1;
    }
    adManager->download_start(idx);
    return 0;
}

/**
 * download_finish
 */
jint jni_core_sug_ad_download_finish(JNIEnv* env, jobject obj, jint idx, jint ad_trigger) {
    if (g_ipt_main._ipt_pad == NULL) {
        return -1;
    }
    iptcore::AdManager *adManager = g_ipt_main._ipt_pad->get_ad_manager((iptcore::AdManager::AdPos) ad_trigger);
    if (adManager == nullptr) {
        return -1;
    }
    adManager->download_finish(idx);
    return 0;
}

/**
 * install_finish
 */
jint jni_core_sug_ad_install_finish(JNIEnv* env, jobject obj, jint idx, jint ad_trigger) {
    if (g_ipt_main._ipt_pad == NULL) {
        return -1;
    }
    iptcore::AdManager *adManager = g_ipt_main._ipt_pad->get_ad_manager((iptcore::AdManager::AdPos) ad_trigger);
    if (adManager == nullptr) {
        return -1;
    }
    adManager->install_finish(idx);
    return 0;
}

/**
 * open_url_app
 */
jint jni_core_sug_ad_open_url_app(JNIEnv* env, jobject obj, jint idx, jint ad_trigger) {
    if (g_ipt_main._ipt_pad == NULL) {
        return -1;
    }
    iptcore::AdManager *adManager = g_ipt_main._ipt_pad->get_ad_manager((iptcore::AdManager::AdPos) ad_trigger);
    if (adManager == nullptr) {
        return -1;
    }
    adManager->open_url_app(idx);
    return 0;
}

/**
 * open_fallback_url
 */
jint jni_core_sug_ad_open_fallback_url(JNIEnv* env, jobject obj, jint idx, jint ad_trigger) {
    if (g_ipt_main._ipt_pad == NULL) {
        return -1;
    }
    iptcore::AdManager *adManager = g_ipt_main._ipt_pad->get_ad_manager((iptcore::AdManager::AdPos) ad_trigger);
    if (adManager == nullptr) {
        return -1;
    }
    adManager->open_fallback_url(idx);
    return 0;
}

/**
 * dpl_success
 */
jint jni_core_sug_ad_dpl_success(JNIEnv* env, jobject obj, jint idx, jint ad_trigger) {
    if (g_ipt_main._ipt_pad == NULL) {
        return -1;
    }
    iptcore::AdManager *adManager = g_ipt_main._ipt_pad->get_ad_manager((iptcore::AdManager::AdPos) ad_trigger);
    if (adManager == nullptr) {
        return -1;
    }
    adManager->dpl_success(idx);
    return 0;
}

/**
 * dpl_failed
 */
jint jni_core_sug_ad_dpl_failed(JNIEnv* env, jobject obj, jint idx, jint ad_trigger) {
    if (g_ipt_main._ipt_pad == NULL) {
        return -1;
    }
    iptcore::AdManager *adManager = g_ipt_main._ipt_pad->get_ad_manager((iptcore::AdManager::AdPos) ad_trigger);
    if (adManager == nullptr) {
        return -1;
    }
    adManager->dpl_failed(idx);
    return 0;
}

jboolean jni_ipt_cfg_train_gauss_user(JNIEnv* env, jobject obj, jboolean forcible)
{
    jboolean ret = JNI_FALSE;
    if (g_ipt_main._ipt_pad != NULL)
    {
        ret = g_ipt_main._ipt_pad->cfg_train_gauss_user(forcible);
    }
    return ret;
}

/**
 * is_match_sug_white_data
 */
jboolean jni_is_match_sug_white_data(JNIEnv* env, jobject obj) {
    if (g_ipt_main._ipt_pad == NULL) {
        return JNI_FALSE;
    }
    return g_ipt_main._ipt_pad->is_match_sug_white_data();
}

/**
 * 荣耀笔记的几个需求
 */
jint jni_honor_notepad_intelligent_request(JNIEnv* env, jobject obj, jstring input, jint mode, jlong request_id, jint limit_cnt) {
//    if (g_ipt_main._ipt_pad == NULL) {
//        return -1;
//    }
//
//    jboolean isCopy = JNI_FALSE;
//    jsize length = env->GetStringLength(input);
//    if (length <= 0) {
//        return -1;
//    }
//
//    Uint16 utf16Str[length + 1];
//    const jchar* chars = nullptr;
//
//    try {
//        chars = env->GetStringChars(input, &isCopy);
//        std::memcpy(utf16Str, chars, length * sizeof(Uint16));
//        utf16Str[length] = 0;
//
//        iptcore::InputPad::NotePadFindMode find_mode;
//        switch (mode) {
//            case 1: find_mode = iptcore::InputPad::NotePadFindMode::NOTEPAD_FIND_MODE_PY; break;
//            case 2: find_mode = iptcore::InputPad::NotePadFindMode::NOTEPAD_FIND_MODE_CORRECT; break;
//            case 3: find_mode = iptcore::InputPad::NotePadFindMode::NOTEPAD_FIND_MODE_PY_AND_CORRECT; break;
//            default: find_mode = iptcore::InputPad::NotePadFindMode::NOTEPAD_FIND_MODE_NONE; break;
//        }
//
//        LOGI("honor_notepad_intelligent_request#input:%s, mode:%d, request_id: %ld, limitCnt: %d", utf16Str, mode, request_id, limit_cnt);
//
//        Int32 ret = g_ipt_main._ipt_pad->notepad_request_result(
//            utf16Str, length, find_mode,
//            static_cast<Uint64>(request_id),
//            static_cast<Uint32>(limit_cnt)
//        );
//
//        env->ReleaseStringChars(input, chars);
//        return ret;
//
//    } catch (...) {
//        LOGI("honor_notepad_intelligent_request#error.");
//        if (chars) env->ReleaseStringChars(input, chars);
//        return -1;
//    }
    return -1;
}

/** 函数名与功能无关（send app name and ts to core） */
jint jni_cfg_dct_set_name_by_buff(JNIEnv* env, jobject obj, jstring value, jlong t, jboolean is_new) {
    if (g_ipt_main._ipt_pad == NULL) {
        return -1;
    }
    jboolean isCopy = JNI_FALSE;
    Uint32 len = env->GetStringUTFLength(value);
    const char* chars = env->GetStringUTFChars(value, &isCopy);
    Int32 ret = g_ipt_main._ipt_pad->cfg_dct_set_name_by_buff(reinterpret_cast<const Uint8 *>(chars),
                                                              (Uint32) len, t, is_new ? 1 : 0);
    LOGI("cfg_dct_cell_install_by_buff, contentLen=%d,ret=%d", len, ret);
    env->ReleaseStringUTFChars(value, chars);
    return ret;
}