/**
 * 网络接口的实现类
 * Created by cdf on 2018/9/9.
 */

#include "ipt_jni_net_man.h"
#include "ipt_jni_log.h"

namespace iptjni
{
    IptJniNetMan::IptJniNetMan(JNIEnv* env, jobject andrd_netman)
    {
        LOGI("net man create");
        _g_main_jni_env = env;
        if (_g_main_jni_env == NULL || andrd_netman == NULL)
        {
            return;
        } 

        _g_andrd_netman = _g_main_jni_env->NewGlobalRef(andrd_netman);
        jclass andrd_netman_calss = _g_main_jni_env->GetObjectClass(andrd_netman);
        _stream_create_method_id = _g_main_jni_env->GetMethodID(andrd_netman_calss, "streamCreate", 
                                                "(Ljava/lang/String;IIIIJ)Lcom/baidu/iptcore/net/IptCloudStream;");
        _stream_send_method_id = _g_main_jni_env->GetMethodID(andrd_netman_calss, "streamSend", 
                                                "(Lcom/baidu/iptcore/net/IptCloudStream;I[BI)I");
        _stream_close_method_id = _g_main_jni_env->GetMethodID(andrd_netman_calss, "streamClose", 
                                                "(Lcom/baidu/iptcore/net/IptCloudStream;)V");
    }

    IptJniNetMan::~IptJniNetMan()
    {
        LOGI("net man destroy");
        _stream_close_method_id = NULL;
        _stream_send_method_id = NULL;
        _stream_create_method_id = NULL;
        
        if (_g_main_jni_env != NULL)
        {
            if (_g_andrd_netman != NULL)
            {
                _g_main_jni_env->DeleteGlobalRef(_g_andrd_netman);
                _g_andrd_netman = NULL;
            }
            _g_main_jni_env = NULL;
        }
        
    }

    void* IptJniNetMan::stream_create(const char* url, Uint32 port, Protocol protocol,
                                           Uint32 time_out, Uint32 max_recv_len, StreamCallback* callbck)
    {
        if (_g_main_jni_env != NULL && _g_andrd_netman != NULL)
        {
            Tsize callbackPtr = (Tsize) callbck;
            jstring url_str = _g_main_jni_env->NewStringUTF(url);
            jobject stream = _g_main_jni_env->CallObjectMethod(_g_andrd_netman, _stream_create_method_id,
                                            url_str, port, protocol, time_out, max_recv_len,
                                                               static_cast<jlong>(callbackPtr));
            if (stream != NULL)
            {
                void* streamPtr = (void *) (_g_main_jni_env->NewGlobalRef(stream));
                LOGI("stream_create: %s, %p", url, (void*) streamPtr);
                return streamPtr;
            }
        }
        return NULL;
    }

    bool IptJniNetMan::is_same_stream(void *stream1, void *stream2)
    {
        bool is_same = false;
        if (_g_main_jni_env != NULL)
        {
            is_same = _g_main_jni_env->IsSameObject((jobject) stream1, (jobject) stream2);
            LOGI("stream_is_same: s1=%p, s2=%p, is_same=%d", stream1, stream2, is_same);
        }
        return is_same;
    }

    Int32 IptJniNetMan::stream_send(void* stream, StreamReqType stream_type, Uint8* send, Uint32 send_len)
    {
        LOGI("stream_send: stream=%p", stream);
        if (_g_main_jni_env != NULL && _g_andrd_netman != NULL)
        {
            if (stream != NULL) 
            {
                jbyte *jb =  (jbyte*) send;
                jbyteArray jarray = _g_main_jni_env->NewByteArray(send_len);
                _g_main_jni_env->SetByteArrayRegion(jarray, 0, send_len, jb);
                return _g_main_jni_env->CallIntMethod(_g_andrd_netman, _stream_send_method_id, stream,
                                                      (jint) stream_type,
                                                      jarray, (jint) send_len);
            }
        }
        return -1;
    }

    void IptJniNetMan::stream_close(void* stream)
    {
        LOGI("stream_close:");
        if (_g_main_jni_env != NULL && _g_andrd_netman != NULL)
        {
            if (stream != NULL) 
            {
                _g_main_jni_env->CallVoidMethod(_g_andrd_netman, _stream_close_method_id, stream);
                _g_main_jni_env->DeleteGlobalRef((jobject) stream);
            }
        }
    }

    Int32 IptJniNetMan::on_net_recv(void* stream, Tsize callback_ptr, Int32 errorCode, Uint8 * recv_data, Uint32 len)
    {
        StreamCallback *callback = (StreamCallback*) callback_ptr;
        if (NULL == callback)
        {
            return -1;
        }
        LOGI("stream_on_net_recv: send_len=%d, callback=%d, stream=%p", len, callback, stream);
        callback->on_net_recv(stream, errorCode, recv_data, len);
        return 0;
    }
}
