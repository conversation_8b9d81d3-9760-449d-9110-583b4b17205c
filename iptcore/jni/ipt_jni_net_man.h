/**
 * 网络接口的实现类
 * Created by cdf on 2018/9/9.
 */

#ifndef IPTANDROIDDEMO_IPT_JNI_NET_MAN_H
#define IPTANDROIDDEMO_IPT_JNI_NET_MAN_H

#include "_pub_iptpad.h"
#include <jni.h>

namespace iptjni {

    class IptJniNetMan : public iptcore::NetMan
    {

    public:
        IptJniNetMan(JNIEnv* env, jobject andrd_netman);
        ~IptJniNetMan();

    public:
        JNIEnv* _g_main_jni_env; // 内核主线程JNIEnv* 对象
        jobject _g_andrd_netman; // android平台的netman实现对象的全局引用
        jmethodID _stream_create_method_id; // StreamCreate的方法id
        jmethodID _stream_send_method_id; // StreamSend的方法id
        jmethodID _stream_close_method_id; // StreamClose的方法id

    public:
        void* stream_create(const char* url,
                                    Uint32 port,
                                    Protocol protocol,
                                    Uint32 time_out,
                                    Uint32 max_recv_len,
                                    StreamCallback* callbck);
        Int32 stream_send(void* stream,
                          StreamReqType stream_type,
                          Uint8* send,
                          Uint32 send_len);

        bool is_same_stream(void* stream1, void* stream2);

        void stream_close(void* stream);

    public:
        Int32 on_net_recv(void* stream, Tsize callback_ptr, Int32 errorCode, Uint8 * recv_data, Uint32 len);

    };
}

#endif //IPTANDROIDDEMO_IPT_JNI_NET_MAN_H
