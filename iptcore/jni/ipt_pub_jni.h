/*
 * ipt_pub_jni.h
 *
 *  Created on: 2016-7-12
 *      Author: xuxiang
 *      jni对外接口
 */

#ifndef COREBD__PUB_IPT_JNI_H
#define COREBD__PUB_IPT_JNI_H

#include <jni.h>

//////////////////////////宏开关区域///////////////////////////

//#define IPT_JNI_LOG_PRINT // 是否开启JNI日志打印
#define IPT_JNI_FILE_LOG // 是否开启JNI日志反射到上层打印（用于文件记录）

#define IPT_FEATURE_NEW_KP // 新纠错策略的宏开关

#ifdef __cplusplus
extern "C"
{
#endif

//////////////////////////内核开关区域///////////////////////////
jint jni_open(JNIEnv * env, jobject obj, jobject context, jobject ard_env, jobject ard_netman, jstring dict_dir,
        jobject package_info, jint flavor);
jint jni_close(JNIEnv * env, jobject obj);
jint jni_get_core_version(JNIEnv* env, jobject obj);
jint jni_get_en_sys_dict_version(JNIEnv * env, jobject obj);
jint jni_get_cz3_dict_gram_version(JNIEnv * env, jobject obj);
jint jni_get_cz3_dict_sys_version(JNIEnv * env, jobject obj);
jint jni_get_cz3_dict_cate_version(JNIEnv * env, jobject obj);
jint jni_get_cz5down_status(JNIEnv * env, jobject obj);
jint jni_get_slide_dict_version(JNIEnv * env, jobject obj);
jintArray jni_get_cz_version(JNIEnv * env, jobject obj, jstring dict_dir);
jboolean jni_is_nnranker_installed(JNIEnv * env, jobject obj);
jint jni_backup_trace_log(JNIEnv * env, jobject obj, jstring file_path);
jstring jni_get_trace_log(JNIEnv * env, jobject obj);
void jni_reset_trace_log(JNIEnv * env, jobject obj);

//////////////////////////面板切换区域///////////////////////////
void jni_send_pad_event(JNIEnv* env, jobject obj, jint pad_event, jboolean restart);
jint jni_pad_switch(JNIEnv* env, jobject obj, jint pad_id, jobject o_duty);
jint jni_act_cnen_key(JNIEnv* env, jobject obj, jint pad_id, jobject o_duty);
jint jni_act_rare_chaizi_hw_key(JNIEnv* env, jobject obj, jint pad_id, jobject o_duty);
void jni_set_default_pad(JNIEnv* env, jobject obj, jint pad_id);
//////////////////////////用户行为区域///////////////////////////
jint jni_act_key_click(JNIEnv* env, jobject obj, jint key_id, jint input_type, jstring uni, jobject o_duty);
jint jni_act_key_click_xy(JNIEnv* env, jobject obj, jint key_id, jint x, jint y,
        jint input_type, jstring uni, jobject o_duty);
jint jni_act_key_click_touch_info(JNIEnv* env, jobject obj, jint key_id, jint x, jint y,
        jbyte power, jbyte area, jint input_type, jstring junicode, jobject o_duty);
void jni_act_ai_pad_click(JNIEnv* env, jobject obj, jint event_type, jshortArray click_list);
void jni_act_custom_input_action(JNIEnv* env, jobject obj, jint event_type, jstring user_data);
void jni_act_cand_action(JNIEnv* env, jobject obj, jint cand_action_type, jint candidx);
jint jni_act_cand_click(JNIEnv* env, jobject obj, jint cand_idx, jobject o_duty);
jint jni_cfg_get_hw_rare_version(JNIEnv* env, jobject obj);
jint jni_act_rare_cand_click(JNIEnv* env, jobject obj, jint cand_idx, jobject o_duty);
jint jni_act_cand_select(JNIEnv* env, jobject obj, jint cand_idx, jobject o_duty);
jint jni_act_cand_longpress(JNIEnv* env, jobject obj, jint cand_idx, jobject o_duty);
jint jni_act_sug_click(JNIEnv* env, jobject obj, jint sug_idx, jobject o_duty);
jint jni_act_sug_card_click(JNIEnv* env, jobject obj, jint sug_card_idx, jboolean is_insert, jobject o_duty);
jint jni_act_sug_card_select(JNIEnv* env, jobject obj, jint sug_card_idx, jobject o_duty);
jint jni_act_list_click(JNIEnv* env, jobject obj, jint list_idx, jobject o_duty);
jint jni_act_tab_click(JNIEnv* env, jobject obj, jint qp_filter, jobject o_duty);
jbyteArray jni_get_tabs(JNIEnv* env, jobject obj);
jint jni_act_candinfo_click(JNIEnv* env, jobject obj, jint idx, jobject o_duty);
jint jni_act_candinfo_cancel(JNIEnv* env, jobject obj, jobject o_duty);
jint jni_act_contact_insert(JNIEnv* env, jobject obj, jobject o_duty);
jint jni_act_contact_cancel(JNIEnv* env, jobject obj, jobject o_duty);
jint jni_act_contact_info_select(JNIEnv* env, jobject obj, jint contact_idx, jint item_idx, jboolean is_select);
jint jni_act_track_start(JNIEnv* env, jobject obj, jshortArray point, jboolean is_sym_hw, jobject o_duty, jlong time);
jint jni_act_track_start_xy(JNIEnv* env, jobject obj, jshort point_x, jshort point_y,
        jboolean is_sym_hw, jobject o_duty, jlong time);
jint jni_act_track_move(JNIEnv* env, jobject obj, jshortArray point, jobject o_duty, jlong time);
jint jni_act_track_move_xy(JNIEnv* env, jobject obj, jshort point_x, jshort point_y, jobject o_duty, jlong time);
jint jni_act_track_end(JNIEnv* env, jobject obj, jshort point_x, jshort point_y, jobject o_duty, jlong time, jboolean is_gesture);
jint jni_act_input_cursor(JNIEnv* env, jobject obj, jint cursor_idx, jobject o_duty);
void jni_act_input_cursor_left(JNIEnv* env, jobject obj);
void jni_act_input_cursor_right(JNIEnv* env, jobject obj);
jint jni_act_edit_cursor_change(JNIEnv* env, jobject obj, jobject o_duty);
jint jni_act_input_pop(JNIEnv* env, jobject obj, jobject o_duty);
jint jni_import_sym_list(JNIEnv* env, jobject obj, jobjectArray sym_object_array,
                                           jobjectArray sym_value_array, jobject o_duty);
void jni_pad_set_lock(JNIEnv* env, jobject obj,
                                        jintArray symCateList, jintArray symLockList);
void jni_pad_get_lock(JNIEnv* env, jobject obj,
                                        jintArray symCateList, jintArray symLockList);
void jni_pad_set_sym_filter(JNIEnv* env, jobject obj,
                                              jobjectArray sym_list, jint count);
jint jni_act_shift_longdown(JNIEnv* env, jobject obj);
jint jni_act_shift_longup(JNIEnv* env, jobject obj);
jstring jni_act_correct_voicedata(JNIEnv* env, jobject obj, jstring joriResult);
jint jni_act_correct_voicesend(JNIEnv* env, jobject obj);
jint jni_act_check_clip(JNIEnv* env, jobject obj, jcharArray uniArray, jint uniLen);
jint jni_act_adjust_emoji_relation(JNIEnv* env, jobject obj, jint emoji_value, jint cellid);
//////////////////////////数据获取区域////////////////////////////
jint jni_get_input_show(JNIEnv* env, jobject obj, jobject o_show_info, jbyteArray o_cursor_info,
                                          jbyteArray o_autofix_info);
jint jni_get_cand_count(JNIEnv* env, jobject obj);
jint jni_get_list_count(JNIEnv* env, jobject obj);
jint jni_get_cand_item(JNIEnv* env, jobject obj, jint cand_idx, jobject o_cand_info);
jint jni_get_rare_cand_count(JNIEnv* env, jobject obj);
jint jni_get_rare_cand_item(JNIEnv* env, jobject obj, jint cand_idx, jobject o_cand_info);
jint jni_get_ai_cand_item(JNIEnv* env, jobject obj, jint cand_idx, jobject o_cand_info);
jint jni_get_list_item(JNIEnv* env, jobject obj, jint list_idx, jobject o_list_item);
jint jni_get_sug_count(JNIEnv* env, jobject obj);
jint jni_get_sug_ad_timeout_ms(JNIEnv* env, jobject obj);
jint jni_get_ai_cand_item_count(JNIEnv* env, jobject obj);
jint jni_get_sug_item(JNIEnv* env, jobject obj, jint sug_idx, jobject o_sug_info);
jint jni_get_sug_select(JNIEnv* env, jobject obj);
jint jni_get_sug_card_count(JNIEnv* env, jobject obj);
jint jni_get_sug_card_item(JNIEnv* env, jobject obj, jint sug_card_idx, jobject o_sug_card);
jint jni_get_sug_card_select(JNIEnv* env, jobject obj);
jint jni_get_cand_info_count(JNIEnv* env, jobject obj);
jstring jni_get_cand_info(JNIEnv* env, jobject obj, jint idx);
jint jni_get_contact_count(JNIEnv* env, jobject obj);
jint jni_get_contact_item(JNIEnv* env, jobject obj, jint idx, jobject o_contact_item);
jint jni_get_trigger_items_count(JNIEnv* env, jobject obj);
jint jni_get_trigger_item(JNIEnv* env, jobject obj, jint idx, jobject o_trigger_item);
jint jni_get_map_trigger_items_count(JNIEnv* env, jobject obj);
jint jni_get_cloud_intention_cnt(JNIEnv* env, jobject obj);
jint jni_get_cloud_intention_item(JNIEnv* env, jobject obj, jint idx, jobject o_intent_item);
jint jni_get_map_trigger_item(JNIEnv* env, jobject obj, jint idx, jobject o_map_item);
jlong jni_get_trigger_items_id(JNIEnv* env, jobject obj);
jint jni_get_pad_id(JNIEnv* env, jobject obj);
jboolean jni_get_ai_pad_loading_state(JNIEnv* env, jobject obj);
jboolean jni_get_ai_bubble_need_show(JNIEnv* env, jobject obj);
jint jni_get_ai_pad_tab(JNIEnv* env, jobject obj);
jint jni_get_ai_pad_state(JNIEnv* env, jobject obj);
jint jni_get_ai_pad_cnt(JNIEnv* env, jobject obj);
jint jni_get_ai_pad_item(JNIEnv* env, jobject obj, jint cand_idx, jobject o_cand_info);
jstring jni_get_ai_pad_origin_text(JNIEnv* env, jobject obj);
jint jni_ai_correct_inline_info(JNIEnv* env, jobject obj, jobject o_cand_info);
jboolean jni_ai_pad_is_auto_open(JNIEnv* env, jobject obj);
jint jni_get_ai_cand(JNIEnv* env, jobject obj, jobject o_cand_info);
jint jni_get_ai_icon_state(JNIEnv* env, jobject obj);
jboolean jni_cfg_check_hit_black_list(JNIEnv* env, jobject obj, jstring content);
jbyteArray jni_pbdata_request_aichat_card(JNIEnv *env, jobject obj, jint check_id,
                                          jbyteArray inContent);
jbyteArray jni_pbdata_response_aichat_card(JNIEnv *env, jobject obj, jint check_id,
                                           jbyteArray inContent);
///////////////////////////状态获取区域////////////////////////////
jint jni_get_hw_smart_delay_time(JNIEnv* env, jobject obj);
jint jni_state_get_track_type(JNIEnv* env, jobject obj);
jboolean jni_state_get_is_accept_track(JNIEnv* env, jobject obj);
jstring jni_get_cand_context(JNIEnv* env, jobject obj, jint idx);
jcharArray jni_get_egg(JNIEnv* env, jobject obj);
jint jni_get_srv_cloud_white_ver(JNIEnv* env, jobject obj);
jint jni_get_sug_type(JNIEnv* env, jobject obj);
jint jni_get_sug_action_type(JNIEnv* env, jobject obj);
jint jni_get_sug_action_type_by_index(JNIEnv* env, jobject obj, jint index);
jint jni_get_sug_id(JNIEnv* env, jobject obj);
jstring jni_get_sug_ad_global_id(JNIEnv* env, jobject obj, jint index);
jint jni_get_sug_source_id(JNIEnv* env, jobject obj);
jstring jni_get_sug_source_msg(JNIEnv* env, jobject obj);
void jni_cfg_set_pad_layout(JNIEnv* env, jobject obj, jintArray jrect, jboolean value);
void jni_cfg_set_pad_key_pos(JNIEnv* env, jobject obj, jbyte key, jintArray jvrect, jintArray jtrect);
jint jni_cfg_get_touched_key(JNIEnv* env, jobject obj, jint px, jint py);
jint jni_get_sug_state(JNIEnv* env, jobject obj);
void jni_act_cur_sug_close(JNIEnv* env, jobject obj);
void jni_user_trace_start_pad_key_layout(JNIEnv* env, jobject obj);
void jni_user_trace_finish_pad_key_layout(JNIEnv* env, jobject obj);
void jni_user_trace_write(JNIEnv* env, jobject obj, jint action, jint container,
                                            jbyteArray jcontent);
void jni_user_voice_input_log(JNIEnv* env, jobject obj, jstring value);
//////////////////////////设置项区域//////////////////////////////

void jni_set_int(JNIEnv* env, jobject obj, jint key, jint value);
jint jni_get_int(JNIEnv* env, jobject obj, jint key);
void jni_set_boolean(JNIEnv* env, jobject obj, jint key, jboolean value);
jboolean jni_get_boolean(JNIEnv* env, jobject obj, jint key);
void jni_set_string(JNIEnv* env, jobject obj, jint key, jstring value);
jstring jni_get_string(JNIEnv* env, jobject obj, jint key);
void jni_set_long(JNIEnv* env, jobject obj, jint key, jlong value);

void jni_cfg_set_aiwords_json(JNIEnv* env, jobject obj, jstring json_str, jstring notice_key);
void jni_cfg_set_trace_warning_size(JNIEnv* env, jobject obj, jint base, jint increment);
void jni_cfg_keymap_clean(JNIEnv* env, jobject obj);
void jni_cfg_keymap_addchar(JNIEnv* env, jobject obj, jint input, jchar jchar_code, jint level);
void jni_cfg_set_cloud_address(JNIEnv* env, jobject obj,
                                                 jobjectArray j_host_array, jintArray j_port_array,
                                                 jboolean cloud_use_udp, jboolean sug_use_udp);
void jni_set_online_log(JNIEnv * env, jobject obj, jint level);
/////////////////////////// 需要session的设置项(核心基础词库区域) ////////////////////////////
jint jni_cfg_get_phrase_group_count(JNIEnv* env, jobject obj);
jint jni_cfg_get_phrase_group(JNIEnv* env, jobject obj, jint idx, jobject o_phrase_group);
jint jni_cfg_set_add_phrase_group(JNIEnv* env, jobject obj, jstring name);
jint jni_cfg_set_delete_phrase_group(JNIEnv* env, jobject obj, jint idx);
jint jni_cfg_set_edit_phrase_group(JNIEnv* env, jobject obj, jint idx, jstring name);
jint jni_cfg_set_enable_phrase_group(JNIEnv* env, jobject obj, jint idx, jboolean is_enable);
jint jni_cfg_get_phrase_item_count(JNIEnv* env, jobject obj, jint group_id, jstring code);
jint jni_cfg_get_phrase_item(JNIEnv* env, jobject obj, jint idx, jobject o_phrase_item);
jint jni_cfg_set_add_phrase_item(JNIEnv* env, jobject obj, jstring code, jstring word, jint pos, jint group_id);
jint jni_cfg_set_edit_phrase_item(JNIEnv* env, jobject obj, jint idx, jstring code, jstring word,
                                         jint pos);
jint jni_cfg_set_delete_phrase_item(JNIEnv* env, jobject obj, jint idx);
jint jni_cfg_phrase_import(JNIEnv* env, jobject obj, jstring jfilename, jboolean overwrite);
jint jni_cfg_phrase_export(JNIEnv* env, jobject obj, jstring jfileName, jint group_id);

jint jni_cfg_usword_reduce(JNIEnv* env, jobject obj, jint percent);
jint jni_cfg_usword_getsize(JNIEnv* env, jobject obj);
jint jni_cfg_usword_import(JNIEnv* env, jobject obj, jstring path);
jint jni_cfg_usword_export(JNIEnv* env, jobject obj, jstring path);
jint jni_cfg_ueword_import(JNIEnv* env, jobject obj, jstring path);
jint jni_cfg_ueword_export(JNIEnv* env, jobject obj, jstring path);
jint jni_cfg_rare_user_word_export(JNIEnv* env, jobject obj, jstring path);
jint jni_cfg_usall_import_proto(JNIEnv* env, jobject obj, jstring path);
jint jni_cfg_usall_export_proto(JNIEnv* env, jobject obj, jstring path);

jint jni_install_cell(JNIEnv* env, jobject obj, jint key, jstring path);
jint jni_uninstall_cell(JNIEnv* env, jobject obj, jint key, jint id);
jint jni_cfg_get_cell_count(JNIEnv* env, jobject obj);
jint jni_cfg_get_cell_info_by_index(JNIEnv* env, jobject obj, jint idx, jobject o_cell_info);
jint jni_cfg_get_cell_info_by_cellid(JNIEnv* env, jobject obj, jint idx, jobject o_cell_info);
jint jni_cfg_cell_set_loc_type(JNIEnv* env, jobject obj, jint cellid, jint loc_type);
jint jni_cfg_cell_set_install_time(JNIEnv* env, jobject obj, jint cellid, jint install_time);
jint jni_cfg_cell_get_popword_cellid(JNIEnv* env, jobject obj);
jint jni_cfg_cell_get_sysword_cellid(JNIEnv* env, jobject obj);
jint jni_cfg_cell_get_cloud_white_ver(JNIEnv* env, jobject obj);
jint jni_cfg_cell_enable(JNIEnv* env, jobject obj, jint cell_id, jboolean is_enable);
jint jni_cfg_usall_get_recent_count(JNIEnv* env, jobject obj, jint days);

jint jni_cfg_get_kwd_cell_count(JNIEnv* env, jobject obj);
jint jni_cfg_get_kwd_cell_info_by_index(JNIEnv* env, jobject obj, jint idx, jobject o_kwd_cell_info);
jint jni_cfg_get_kwd_cell_info_by_cellid(JNIEnv* env, jobject obj,
                                                           jint cellid, jobject o_kwd_cell_info);
jint jni_cfg_kwd_export(JNIEnv* env, jobject obj, jstring file_path);
jint jni_cfg_get_idm_cell_count(JNIEnv* env, jobject obj);
jint jni_cfg_get_idm_cell_info_by_index(JNIEnv* env, jobject obj, jint idx, jobject o_idm_cell_info);
jbyteArray jni_cfg_hw_encode_point(JNIEnv* env, jobject obj, jint candIdx);
jstring jni_cfg_util_getHW_Py(JNIEnv* env, jobject obj, jchar unicode, jint isall);
jint jni_cfg_util_get_spmap_sheng(JNIEnv* env, jobject obj, jbyte keychar, jbyteArray sp_list);
jint jni_cfg_util_get_spmap_yun(JNIEnv* env, jobject obj, jbyte keychar, jbyteArray sp_list);
jint jni_cfg_contact_reset(JNIEnv* env, jobject obj);
jint jni_cfg_contact_append_attri(JNIEnv* env, jobject obj, jstring attr);
jint jni_cfg_contact_append_value(JNIEnv* env, jobject obj, jstring name, jstring attr,
                                                    jstring value);
jint jni_cfg_contact_build_start(JNIEnv* env, jobject obj);
jint jni_cfg_contact_build_add_name(JNIEnv* env, jobject obj, jstring name);
jint jni_cfg_contact_build_add_value(JNIEnv* env, jobject obj, jstring attr,
        jstring value);
jint jni_cfg_contact_build_end(JNIEnv* env, jobject obj,
        jboolean is_retain_black);
jint jni_cfg_contact_build_add_black_name(JNIEnv* env, jobject obj, jstring jdelName);
jint jni_cfg_contact_delete(JNIEnv* env, jobject obj, jstring name, jint option);
jstring jni_cfg_contact_voice_find(JNIEnv* env, jobject obj, jstring oriword);
jstring jni_cfg_contact_voice_find_addressbook(JNIEnv* env, jobject obj, jstring oriword);
jint jni_cfg_old_cp_export(JNIEnv* env, jobject obj, jstring jinputFile, jstring joutputFile);
jint jni_cfg_old_ue_export(JNIEnv* env, jobject obj, jstring jinputFile, jstring joutputFile);
jint jni_cfg_import_zyword(JNIEnv* env, jobject obj, jstring jfileName);
jint jni_cfg_export_zyword(JNIEnv* env, jobject obj, jstring jfileName);
jint jni_cfg_sym_import(JNIEnv* env, jobject obj, jstring jfileName, jboolean isOverWrite);
jint jni_cfg_sym_export(JNIEnv* env, jobject obj, jstring jfileName);
jint jni_cfg_usr_sym_import(JNIEnv* env, jobject obj, jobjectArray jsyms);
jint jni_cfg_sylian_import(JNIEnv* env, jobject obj, jstring jfileName, jboolean isOverWrite);
jint jni_cfg_vkword_import(JNIEnv* env, jobject obj, jstring jfileName, jboolean isOverWrite);
jint jni_cfg_vkword_export(JNIEnv* env, jobject obj, jstring jfileName);
jint jni_cfg_usrinfo_import(JNIEnv* env, jobject obj, jstring jfileName);
jint jni_cfg_usrinfo_export(JNIEnv* env, jobject obj, jstring jfileName);
jint jni_cfg_otherword_import(JNIEnv * env, jobject obj, jstring jfileName, jboolean isOverWrite);
jint jni_cfg_get_zhuyin_hz_version(JNIEnv* env, jobject obj);
jint jni_cfg_get_zhuyin_cz_version(JNIEnv* env, jobject obj);
jint jni_cfg_get_zhuyin_cz_info(JNIEnv* env, jobject obj);
jint jni_cfg_get_cangjie_version(JNIEnv* env, jobject obj);
jint jni_cfg_get_hw_version(JNIEnv* env, jobject obj);
jint jni_cfg_search_get_version(JNIEnv* env, jobject obj);
jintArray jni_cfg_search_find(JNIEnv* env, jobject obj, jstring queryWord);
jint jni_cfg_adjust_sylian_relation(JNIEnv* env, jobject obj, jstring jprestr, jint prelen, jstring
                                                    jtailstr, jint taillen);
jcharArray jni_tool_feature_inf_extract(JNIEnv* env, jobject obj, jstring jorgWstr, jint orgLen,
                                                    jint infType);
jint jni_cfg_cand_context_export(JNIEnv* env, jobject obj, jstring jfileName);
jstring jni_cfg_util_get_ft_by_uni(JNIEnv* env, jobject obj, jstring junicode);
jint jni_cfg_util_import_old_us_file(JNIEnv* env, jobject obj, jstring joldCellFile,
                                                    jstring joldUzFile);
jint jni_cfg_kwd_get_search_version(JNIEnv* env, jobject obj);
jint jni_cfg_get_autoreply_ver(JNIEnv* env, jobject obj);
jint jni_cfg_import_app_map(JNIEnv* env, jobject obj, jstring jfilePath);
jint jni_cfg_app_get_map_version(JNIEnv* env, jobject obj);
jstring jni_export_mis_touch_info(JNIEnv* env, jobject obj);
jstring jni_get_key_count_for_mis_touch(JNIEnv* env, jobject obj);
jstring jni_export_word_info_for_trace(JNIEnv* env, jobject obj);
void jni_export_input_statistics(JNIEnv* env, jobject obj, jobjectArray jstrings);
jint jni_cfg_cz_down_refresh(JNIEnv* env, jobject obj, jstring name);
jint jni_cfg_core_refresh(JNIEnv* env, jobject obj, jint type);
jint jni_cfg_core_unload(JNIEnv* env, jobject obj, jint type);
jint jni_cfg_nnranker_refresh(JNIEnv* env, jobject obj,
                                                jstring jConfigPath, jstring jWordPath, jstring
                                                jModelPath);
void jni_cfg_get_nnranker_info(JNIEnv* env, jobject obj, jintArray jintArgs);
jint jni_block_cloud_unis(JNIEnv* env, jobject obj, jstring content);
jint jni_reset_cloud_black(JNIEnv* env, jobject obj);
jint jni_cfg_auto_reply_refresh(JNIEnv* env, jobject obj, jstring name);
void jni_create_usword_manager(JNIEnv* env, jobject obj, jstring jsearch_word, jint type);
void jni_destroy_usword_manager(JNIEnv* env, jobject obj);
jint jni_usword_get_count(JNIEnv* env, jobject obj);
jstring jni_usword_get_str(JNIEnv* env, jobject obj, jint idx);
jint jni_usword_get_action(JNIEnv* env, jobject obj, jint idx);
void jni_usword_do_action(JNIEnv* env, jobject obj, jint idx);
jint jni_usword_get_cnword_count(JNIEnv* env, jobject obj);
jint jni_cfg_keyword_find_voice_lian(JNIEnv* env, jobject obj, jstring str, jintArray emojiList,
        jobjectArray emoticonList);
jcharArray jni_cfg_keyword_find_voice_egg(JNIEnv* env, jobject obj, jstring str);
jint jni_get_py_hotletter(JNIEnv* env, jobject obj, jbyteArray hotLetter);
///////////////////////////android环境相关区域////////////////////////////
jint JNICALL jni_set_ime_service_callback(JNIEnv* env, jobject obj, jobject ime_service_callback);
jint JNICALL jni_callback(JNIEnv* env, jobject obj, jint tag, jlong arg0);
jint JNICALL jni_on_net_recv(JNIEnv* env, jobject obj, jobject stream, jlong callback_ptr,
                                       jint errorCode, jbyteArray response_data);
/////////////////////////////////////////////////////////////////////////
jstring jni_get_prot_code(JNIEnv* env, jobject obj);

void jni_check_file_md5(JNIEnv *env, jobject obj, jstring jfileName, jbyteArray jMD5Digest);

jint jni_cfg_usall_reset(JNIEnv *env, jobject obj);

jint jni_act_change_shift(JNIEnv* env, jobject obj, jint shiftId, jobject o_duty);

jboolean jni_aifont_handwrite_load_model(JNIEnv* env, jobject obj, jobject context, jstring model_path, jstring license_path);
void jni_aifont_handwrite_unload_model(JNIEnv* env, jobject obj);
jint jni_aifont_handwrite_model_version(JNIEnv* env, jobject obj);
jint jni_aifont_reco_points(JNIEnv* env, jobject obj, jobject o_ai_hw_res, jint max_out_um, jintArray points);
void jni_act_aifont_generated_notify(JNIEnv* env, jobject obj, jint type, jstring show_content
    , jstring scheme, jstring insert_content, jlong show);
jint jni_get_intent_style(JNIEnv* env, jobject obj, jstring content);
void jni_act_click_toolbar(JNIEnv* env, jobject obj);
jint jni_get_mlm_so_version(JNIEnv* env);
jint jni_get_mlm_dict_version(JNIEnv* env, jobject obj, jstring filePath);
jint jni_cfg_import_olddef_txt(JNIEnv* env, jobject obj, jstring jfilePath);
jstring jni_get_core_version_str(JNIEnv* env);
jint jni_apply_patch(JNIEnv* env, jobject obj, jint type, jstring patch_path, jstring target_md5
    , jstring original_file_path);
jboolean jni_cfg_get_pad_sym_ext_auto_return(JNIEnv* env, jobject obj);
void jni_cfg_set_pad_sym_ext_auto_return(JNIEnv* env, jobject obj, jboolean is_on);
void jni_cfg_set_scene_group_ids(JNIEnv* env, jobject obj, jlongArray ids);
void jni_cfg_set_display_candtype(JNIEnv* env, jobject obj, jboolean is_on);
jboolean jni_cfg_get_display_candtype(JNIEnv* env, jobject obj);
jbyteArray jni_cfg_contact_export_names(JNIEnv* env, jobject obj);
jstring jni_cfg_get_inline_show(JNIEnv* env, jobject obj);
void jni_ipt_dict_rebuild_from_old_usr2(JNIEnv* env, jobject obj,
                                        jstring joutput_dir, jstring jimm_dir,
                                        jstring jusr2_path, jstring jvkword_path);
void jni_ipt_dict_rebuild_from_old_ue2(JNIEnv* env, jobject obj,
                                       jstring joutput_dir, jstring jimm_dir, jstring jue2_path,
                                       jstring jlearn_dic1_path, jstring jlearn_dic2_path);
void jni_ipt_dict_rebuild_from_old_keyword(JNIEnv* env, jobject obj,
                                           jstring jkwd_keyword_path, jstring jkeyword_path);
void jni_ipt_dict_rebuild_from_old_zy_usr(JNIEnv* env, jobject obj,
                                          jstring joutput_dir, jstring jimm_dir, jstring jdown_dir, jstring jzy_usr_path);
void jni_ipt_dict_rebuild_from_old_zy(JNIEnv* env, jobject obj,
                                      jstring output_dir, jstring imm_dir,
                                      jstring down_dir, jstring jzy_usr_path);
void jni_ipt_dict_rebuild_from_old_cangjie(JNIEnv* env, jobject obj,
                                           jstring joutput_dir, jstring jcangjie_path, jboolean is_quick);
void jni_send_location_event(JNIEnv* env, jobject obj,jfloat latitude, jfloat longitude);
void jni_cfg_set_cloud_intention(JNIEnv* env, jobject obj,jint type, jboolean on);
jint jni_cfg_get_cloud_intention(JNIEnv* env, jobject obj);
void jin_cfg_set_contact_permission_status(JNIEnv* env,jobject obj,jboolean on);
void jni_send_wm_sug_ad_event(JNIEnv* env, jobject obj, jint index, jint wm_sug_action_type);
void jni_send_intention_card_event(JNIEnv* env, jobject obj, jint first_level, jint second_level, jstring title, jint action_type);
void jni_load_core_sug_ad(JNIEnv* env, jobject obj, jint ad_trigger);
jint jni_get_core_sug_ad_count(JNIEnv* env, jobject obj, jint ad_trigger);
jint jni_get_core_sug_ad_at(JNIEnv* env, jobject obj, jint index, jobject ad_info, jint ad_trigger);
jint jni_core_sug_ad_time_out(JNIEnv* env, jobject obj, jint max, jint ad_trigger);
jint jni_core_sug_ad_win(JNIEnv* env, jobject obj, jint index, jint ad_trigger);
jint jni_core_sug_ad_fail(JNIEnv* env, jobject obj, jint index, jint reason, jlong price, jint ad_trigger);
jint jni_core_sug_ad_show(JNIEnv* env, jobject obj, jint index, jintArray bounds, jint ad_trigger);
jint jni_core_sug_ad_click(JNIEnv* env, jobject obj, jint index, jobject ad_action, jint ad_trigger, jintArray border, jintArray click_point);
jint jni_core_sug_ad_download_start(JNIEnv* env, jobject obj, jint index, jint ad_trigger);
jint jni_core_sug_ad_download_finish(JNIEnv* env, jobject obj, jint index, jint ad_trigger);
jint jni_core_sug_ad_install_finish(JNIEnv* env, jobject obj, jint index, jint ad_trigger);
jint jni_core_sug_ad_open_url_app(JNIEnv* env, jobject obj, jint index, jint ad_trigger);
jint jni_core_sug_ad_open_fallback_url(JNIEnv* env, jobject obj, jint index, jint ad_trigger);
jint jni_core_sug_ad_dpl_success(JNIEnv* env, jobject obj, jint index, jint ad_trigger);
jint jni_core_sug_ad_dpl_failed(JNIEnv* env, jobject obj, jint index, jint ad_trigger);
///////////////////////////加解密相关接口////////////////////////////
jint jni_get_encrypt_version(JNIEnv *env, jobject obj);
jint jni_get_encrypt_b64_version(JNIEnv *env, jclass obj);
jbyteArray jni_b64_encode(JNIEnv *env, jclass obj, jbyteArray input);
jbyteArray jni_b64_decode(JNIEnv *env, jclass obj, jbyteArray input);
jbyteArray jni_aes_encrypt(JNIEnv *env, jobject obj, jbyteArray input);
jbyteArray jni_aes_encrypt_v2(JNIEnv *env, jobject obj, jbyteArray input);
jbyteArray jni_aes_decrypt(JNIEnv *env, jobject obj, jbyteArray input);
jbyteArray jni_aes_b64_encrypt(JNIEnv *env, jobject obj, jbyteArray input);
jbyteArray jni_aes_b64_encrypt_v2(JNIEnv *env, jobject obj, jbyteArray input);
jbyteArray jni_aes_b64_decrypt(JNIEnv *env, jobject obj, jbyteArray input);
jbyteArray jni_rsa_encrypt(JNIEnv *env, jobject obj, jbyteArray input);
jbyteArray jni_rsa_decrypt(JNIEnv *env, jobject obj, jbyteArray input);
jboolean jni_ipt_cfg_train_gauss_user(JNIEnv* env, jobject obj, jboolean forcible);
jint jni_cfg_dct_cell_install_by_buff(JNIEnv* env, jobject obj, jstring value);
jint jni_cfg_dct_cell_word_in_dict(JNIEnv* env, jobject obj, jstring value);
jstring jni_cfg_dct_cell_export_buff(JNIEnv* env, jobject obj, jint cell_id);
jint jni_cfg_dct_cell_reset_buff(JNIEnv* env, jobject obj, jint cell_id);
void jni_cfg_set_upl_data_address(JNIEnv* env, jobject obj, jstring address, jint port);
jboolean jni_is_match_sug_white_data(JNIEnv* env, jobject obj);
jint jni_honor_notepad_intelligent_request(JNIEnv* env, jobject obj, jstring input, jint mode, jlong request_id, jint limit_cnt);
jint jni_cfg_dct_set_name_by_buff(JNIEnv* env, jobject obj, jstring value, jlong t, jboolean is_new);
jint jni_cfg_person_info_add_line_start(JNIEnv * env, jobject obj);
jint jni_cfg_person_info_add_from_line(JNIEnv * env, jobject obj, jint index_len, jstring wline);
jint jni_cfg_person_info_add_finished(JNIEnv * env, jobject obj);
jint jni_cfg_person_info_reset(JNIEnv * env, jobject obj);

#ifdef __cplusplus
};
#endif

#endif /* COREBD_PUB_IPT_JNI_H */
