#include <jni.h>
#include "ipt_pub_jni.h"
#include "ipt_jni_log.h"

static const JNINativeMethod core_method_table[] = {
        // name, signature, fnPtr
        {"setInt", "(II)V", (void *) jni_set_int},
        {"getInt", "(I)I", (void *) jni_get_int},
        {"setBoolean", "(IZ)V", (void *) jni_set_boolean},
        {"getBoolean", "(I)Z", (void *) jni_get_boolean},
        {"setString", "(ILjava/lang/String;)V", (void *) jni_set_string},
        {"getString", "(I)Ljava/lang/String;", (void *) jni_get_string},
        {"setLong", "(IJ)V", (void *) jni_set_long},
        {"installCell", "(ILjava/lang/String;)I", (void *) jni_install_cell},
        {"uninstallCell", "(II)I", (void *) jni_uninstall_cell},

        {"open",
         "(Landroid/content/Context;Lcom/baidu/iptcore/IptCoreEnv;Lcom/baidu/iptcore/IptCoreNetMan;"
         "Ljava/lang/String;Landroid/content/pm/PackageInfo;I)I",
         (void *) jni_open},
        {"close", "()I", (void *) jni_close},
        {"getCoreVersion", "()I", (void *) jni_get_core_version},
        {"getCoreVersionStr", "()Ljava/lang/String;", (void *) jni_get_core_version_str},
        {"getEnSysDictVersion", "()I", (void *) jni_get_en_sys_dict_version},
        {"getCz3DictGramVersion", "()I", (void *) jni_get_cz3_dict_gram_version},
        {"getCz3DictSysVersion", "()I", (void *) jni_get_cz3_dict_sys_version},
        {"getCz3DictCateVersion", "()I", (void *) jni_get_cz3_dict_cate_version},
        {"getCz5DownStatus", "()I", (void *) jni_get_cz5down_status},
        {"getSlideDictVersion", "()I", (void *) jni_get_slide_dict_version},
        {"isNnrankerInstalled", "()Z", (void *) jni_is_nnranker_installed},
        {"getUnloadedCzVersion", "(Ljava/lang/String;)[I", (void *) jni_get_cz_version},
        {"getTraceLog", "()Ljava/lang/String;", (void *) jni_get_trace_log},
        {"resetTraceLog", "()V", (void *) jni_reset_trace_log},
        {"backupTraceLog", "(Ljava/lang/String;)I", (void *) jni_backup_trace_log},
        {"sendPadEvent", "(IZ)V", (void *) jni_send_pad_event},
        {"padSwitch", "(ILcom/baidu/iptcore/info/IptCoreDutyInfo;)I", (void *) jni_pad_switch	},
        {"actCnEnKey", "(ILcom/baidu/iptcore/info/IptCoreDutyInfo;)I", (void *) jni_act_cnen_key	},
        {"actRareChaiZiHwKey", "(ILcom/baidu/iptcore/info/IptCoreDutyInfo;)I", (void *) jni_act_rare_chaizi_hw_key	},
        {"setDefaultPad", "(I)V", (void *) jni_set_default_pad	},
        {"actKeyClick", "(IILjava/lang/String;Lcom/baidu/iptcore/info/IptCoreDutyInfo;)I", (void *) jni_act_key_click},
        {"actKeyClickXY", "(IIIILjava/lang/String;Lcom/baidu/iptcore/info/IptCoreDutyInfo;)I",
         (void *) jni_act_key_click_xy},
        {"actAIPadClick", "(I[S)V", (void *) jni_act_ai_pad_click},
        {"actCustomInputAction", "(ILjava/lang/String;)V", (void *) jni_act_custom_input_action},
        {"actCandAction", "(II)V", (void *) jni_act_cand_action},
        {"actKeyClickTouchInfo", "(IIIBBILjava/lang/String;Lcom/baidu/iptcore/info/IptCoreDutyInfo;)I",
         (void *) jni_act_key_click_touch_info},
        {"actCandClick", "(ILcom/baidu/iptcore/info/IptCoreDutyInfo;)I", (void *) jni_act_cand_click},
        {"actRareCandClick", "(ILcom/baidu/iptcore/info/IptCoreDutyInfo;)I", (void *) jni_act_rare_cand_click},
        {"actCandSelect", "(ILcom/baidu/iptcore/info/IptCoreDutyInfo;)I", (void *) jni_act_cand_select},
        {"actCandLongPress", "(ILcom/baidu/iptcore/info/IptCoreDutyInfo;)I", (void *) jni_act_cand_longpress},
        {"actSugClick", "(ILcom/baidu/iptcore/info/IptCoreDutyInfo;)I", (void *) jni_act_sug_click},
        {"actSugCardClick", "(IZLcom/baidu/iptcore/info/IptCoreDutyInfo;)I", (void *) jni_act_sug_card_click},
        {"actSugCardSelect", "(ILcom/baidu/iptcore/info/IptCoreDutyInfo;)I", (void *) jni_act_sug_card_select},
        {"actListClick", "(ILcom/baidu/iptcore/info/IptCoreDutyInfo;)I", (void *) jni_act_list_click},
        {"actTabClick", "(ILcom/baidu/iptcore/info/IptCoreDutyInfo;)I", (void *) jni_act_tab_click},
        {"getTabs", "()[B", (void *) jni_get_tabs},
        {"actCandInfoClick", "(ILcom/baidu/iptcore/info/IptCoreDutyInfo;)I", (void *) jni_act_candinfo_click},
        {"actCandInfoCancel", "(Lcom/baidu/iptcore/info/IptCoreDutyInfo;)I", (void *) jni_act_candinfo_cancel},
        {"actContactInsert", "(Lcom/baidu/iptcore/info/IptCoreDutyInfo;)I", (void *) jni_act_contact_insert},
        {"actContactCancel", "(Lcom/baidu/iptcore/info/IptCoreDutyInfo;)I", (void *) jni_act_contact_cancel},
        {"actContactInfoSelect", "(IIZ)I", (void *) jni_act_contact_info_select},
        {"actTrackStart", "([SZLcom/baidu/iptcore/info/IptCoreDutyInfo;J)I", (void *) jni_act_track_start},
        {"actTrackStartXY", "(SSZLcom/baidu/iptcore/info/IptCoreDutyInfo;J)I", (void *) jni_act_track_start_xy},
        {"actTrackMove", "([SLcom/baidu/iptcore/info/IptCoreDutyInfo;J)I", (void *) jni_act_track_move},
        {"actTrackMoveXY", "(SSLcom/baidu/iptcore/info/IptCoreDutyInfo;J)I", (void *) jni_act_track_move_xy},
        {"actTrackEnd", "(SSLcom/baidu/iptcore/info/IptCoreDutyInfo;JZ)I", (void *) jni_act_track_end},
        {"actInputCursor", "(ILcom/baidu/iptcore/info/IptCoreDutyInfo;)I", (void *) jni_act_input_cursor},
        {"actInputCursorLeft", "()V", (void *) jni_act_input_cursor_left},
        {"actInputCursorRight", "()V", (void *) jni_act_input_cursor_right},
        {"actInputPop", "(Lcom/baidu/iptcore/info/IptCoreDutyInfo;)I", (void *) jni_act_input_pop},
        {"actEditCursorChange", "(Lcom/baidu/iptcore/info/IptCoreDutyInfo;)I", (void *) jni_act_edit_cursor_change},
        {"importSymList", "([Ljava/lang/String;[Ljava/lang/String;Lcom/baidu/iptcore/info/IptCoreDutyInfo;)I",
         (void *) jni_import_sym_list},
        {"padSetLock", "([I[I)V", (void *) jni_pad_set_lock},
        {"padGetLock", "([I[I)V", (void *) jni_pad_get_lock},
        {"padSetSymFilter", "([[CI)V", (void *) jni_pad_set_sym_filter},
        {"actShiftLongDown", "()I", (void *) jni_act_shift_longdown},
        {"actShiftLongUp", "()I", (void *) jni_act_shift_longup},
        {"actCorrectVoiceData", "(Ljava/lang/String;)Ljava/lang/String;", (void *) jni_act_correct_voicedata},
        {"actCorrectVoiceSend", "()I", (void *) jni_act_correct_voicesend},
        {"actCheckClip", "([CI)I", (void *) jni_act_check_clip},
        {"actAdjustEmojiRelation", "(II)I", (void *) jni_act_adjust_emoji_relation},
        {"getInputShow", "(Lcom/baidu/iptcore/info/IptCoreShowInfo;[B[B)I", (void *) jni_get_input_show},
        {"getCandCount", "()I", (void *) jni_get_cand_count},
        {"getListCount", "()I", (void *) jni_get_list_count},
        {"getCandItem", "(ILcom/baidu/iptcore/info/IptCoreCandInfo;)I", (void *) jni_get_cand_item},
        {"getRareCandCount", "()I", (void *) jni_get_rare_cand_count},
        {"getRareCandItem", "(ILcom/baidu/iptcore/info/IptCoreCandInfo;)I", (void *) jni_get_rare_cand_item},
        {"getAiCandItem", "(ILcom/baidu/iptcore/info/IptCoreCandInfo;)I", (void *) jni_get_ai_cand_item},
        {"getSugCount", "()I", (void *) jni_get_sug_count},
        {"getSugAdTimeoutMs", "()I", (void *) jni_get_sug_ad_timeout_ms},
        {"getAiCandItemCount", "()I", (void *) jni_get_ai_cand_item_count},
        {"getSugItem", "(ILcom/baidu/iptcore/info/IptCoreCandInfo;)I", (void *) jni_get_sug_item},
        {"getSugSelect", "()I", (void *) jni_get_sug_select},
        {"getSugCardCount", "()I", (void *) jni_get_sug_card_count},
        {"getSugCardItem", "(ILcom/baidu/iptcore/info/IptCoreSugCardInfo;)I", (void *) jni_get_sug_card_item},
        {"getSugCardSelect", "()I", (void *) jni_get_sug_card_select},
        {"getCandInfoCount", "()I", (void *) jni_get_cand_info_count},
        {"getCandInfo", "(I)Ljava/lang/String;", (void *) jni_get_cand_info},
        {"getContactCount", "()I", (void *) jni_get_contact_count},
        {"getContactItem", "(ILcom/baidu/iptcore/info/IptContactItem;)I", (void *) jni_get_contact_item},
        {"getPadId", "()I", (void *) jni_get_pad_id},
        {"getAIPadLoadingState", "()Z", (void *) jni_get_ai_pad_loading_state},
        {"getAiIconInLXNeedShow", "()Z", (void *) jni_get_ai_bubble_need_show},
        {"getAIPadTab", "()I", (void *) jni_get_ai_pad_tab},
        {"getAIPadState", "()I", (void *) jni_get_ai_pad_state},
        {"getAIPadCnt", "()I", (void *) jni_get_ai_pad_cnt},
        {"getAIPadItem", "(ILcom/baidu/iptcore/info/IptCoreCandInfo;)I", (void *) jni_get_ai_pad_item},
        {"getAIPadOriginText", "()Ljava/lang/String;", (void *) jni_get_ai_pad_origin_text},
        {"getAIPabIsAutoOpen", "()Z", (void *) jni_ai_pad_is_auto_open},
        {"getAICand", "(Lcom/baidu/iptcore/info/IptCoreCandInfo;)I", (void *) jni_get_ai_cand},
        {"getAICandIconState", "()I", (void *) jni_get_ai_icon_state},
        {"getAICorrectInlineInfo", "(Lcom/baidu/iptcore/info/IptCoreCandInfo;)I", (void *) jni_ai_correct_inline_info},
        {"checkHitBlackList", "(Ljava/lang/String;)Z", (void *) jni_cfg_check_hit_black_list},
        {"encryptionAICardRequestData", "(I[B)[B", (void *) jni_pbdata_request_aichat_card},
        {"decryptAICardResponseData", "(I[B)[B", (void *) jni_pbdata_response_aichat_card},
        {"getListItem", "(ILcom/baidu/iptcore/info/IptCoreListInfo;)I", (void *) jni_get_list_item},
        {"getHwSmartDelayTime", "()I", (void *) jni_get_hw_smart_delay_time},
        {"stateGetTrackType", "()I", (void *) jni_state_get_track_type},
        {"stateGetIsAcceptTrack", "()Z", (void *) jni_state_get_is_accept_track},
        {"getCandContext", "(I)Ljava/lang/String;", (void *) jni_get_cand_context},
        {"getEgg", "()[C", (void *) jni_get_egg},
        {"getSrvCloudWhiteVer", "()I", (void *) jni_get_srv_cloud_white_ver},
        {"getSugType", "()I", (void *) jni_get_sug_type},
        {"getSugActionType", "()I", (void *) jni_get_sug_action_type},
        {"getSugActionTypeByIndex", "(I)I", (void *) jni_get_sug_action_type_by_index},
        {"getSugId", "()I", (void *) jni_get_sug_id},
        {"getSugAdGlobalId", "(I)Ljava/lang/String;", (void *) jni_get_sug_ad_global_id},
        {"getSugSourceId", "()I", (void *) jni_get_sug_source_id},
        {"getSugSourceMsg", "()Ljava/lang/String;", (void *) jni_get_sug_source_msg},
        {"blockCloudUnis", "(Ljava/lang/String;)I", (void *) jni_block_cloud_unis},
        {"resetCloudBlack", "()I", (void *) jni_reset_cloud_black},
        {"setPadLayout", "([IZ)V", (void *) jni_cfg_set_pad_layout},
        {"setPadKeyPos", "(B[I[I)V", (void *) jni_cfg_set_pad_key_pos},
        {"getTouchedKey", "(II)I", (void *) jni_cfg_get_touched_key},
        {"getSugState", "()I", (void *) jni_get_sug_state},
        {"getPyHotLetter", "([B)I", (void *) jni_get_py_hotletter},
        {"actCurSugClose", "()V", (void *) jni_act_cur_sug_close},
        {"userTraceStartPadKeyLayout", "()V", (void *) jni_user_trace_start_pad_key_layout},
        {"userTraceFinishPadKeyLayout", "()V", (void *) jni_user_trace_finish_pad_key_layout},
        {"userTraceWrite", "(II[B)V", (void *) jni_user_trace_write},
        {"userVoiceInputLog", "(Ljava/lang/String;)V", (void *) jni_user_voice_input_log},
        {"setImeServiceCallback", "(Lcom/baidu/iptcore/ImePlatformEnv;)I", (void *) jni_set_ime_service_callback},
        {"execCallback", "(IJ)I", (void *) jni_callback},
        {"onNetReceive", "(Lcom/baidu/iptcore/net/IptCloudStream;JI[B)I", (void *) jni_on_net_recv},
        {"setAiWordsJson", "(Ljava/lang/String;Ljava/lang/String;)V", (void *) jni_cfg_set_aiwords_json},
        {"setTraceWarningSize", "(II)V", (void *) jni_cfg_set_trace_warning_size},
        {"keymapClean", "()V", (void *) jni_cfg_keymap_clean},
        {"keymapAddChar", "(ICI)V", (void *) jni_cfg_keymap_addchar},
        {"setCloudAddress", "([Ljava/lang/String;[IZZ)V", (void *) jni_cfg_set_cloud_address},
        {"getPhraseGroupCount", "()I", (void *) jni_cfg_get_phrase_group_count},
        {"getPhraseGroup", "(ILcom/baidu/iptcore/info/IptPhraseGroup;)I", (void *) jni_cfg_get_phrase_group},
        {"addPhraseGroup", "(Ljava/lang/String;)I", (void *) jni_cfg_set_add_phrase_group},
        {"deletePhraseGroup", "(I)I", (void *) jni_cfg_set_delete_phrase_group},
        {"editPhraseGroup", "(ILjava/lang/String;)I", (void *) jni_cfg_set_edit_phrase_group},
        {"enablePhraseGroup", "(IZ)I", (void *) jni_cfg_set_enable_phrase_group},
        {"getPhraseItemCount", "(ILjava/lang/String;)I", (void *) jni_cfg_get_phrase_item_count},
        {"getPhraseItem", "(ILcom/baidu/iptcore/info/IptPhraseItem;)I", (void *) jni_cfg_get_phrase_item},
        {"addPhraseItem", "(Ljava/lang/String;Ljava/lang/String;II)I", (void *) jni_cfg_set_add_phrase_item},
        {"editPhraseItem", "(ILjava/lang/String;Ljava/lang/String;I)I", (void *) jni_cfg_set_edit_phrase_item},
        {"deletePhraseItem", "(I)I", (void *) jni_cfg_set_delete_phrase_item},
        {"importPhrase", "(Ljava/lang/String;Z)I", (void *) jni_cfg_phrase_import},
        {"exportPhrase", "(Ljava/lang/String;I)I", (void *) jni_cfg_phrase_export},
        {"reduceUsWord", "(I)I", (void *) jni_cfg_usword_reduce},
        {"getUsWordSize", "()I", (void *) jni_cfg_usword_getsize},
        {"importUsWord", "(Ljava/lang/String;)I", (void *) jni_cfg_usword_import},
        {"exportUsWord", "(Ljava/lang/String;)I", (void *) jni_cfg_usword_export},
        {"importUeWord", "(Ljava/lang/String;)I", (void *) jni_cfg_ueword_import},
        {"exportUeWord", "(Ljava/lang/String;)I", (void *) jni_cfg_ueword_export},
        {"getHwRareVersion", "()I", (void *) jni_cfg_get_hw_rare_version},
        {"exportRareUserWord", "(Ljava/lang/String;)I", (void *) jni_cfg_rare_user_word_export},
        {"importAllUserWord", "(Ljava/lang/String;)I", (void *) jni_cfg_usall_import_proto},
        {"exportAllUserWord", "(Ljava/lang/String;)I", (void *) jni_cfg_usall_export_proto},
        {"getCellCount", "()I", (void *) jni_cfg_get_cell_count},
        {"getCellInfoByIndex", "(ILcom/baidu/iptcore/info/IptCellInfo;)I", (void *) jni_cfg_get_cell_info_by_index},
        {"getCellInfoByCellId", "(ILcom/baidu/iptcore/info/IptCellInfo;)I", (void *) jni_cfg_get_cell_info_by_cellid},
        {"setCellLocType", "(II)I", (void *) jni_cfg_cell_set_loc_type},
        {"setCellInstallTime", "(II)I", (void *) jni_cfg_cell_set_install_time},
        {"getPopWordCellId", "()I", (void *) jni_cfg_cell_get_popword_cellid},
        {"getSysWordCellId", "()I", (void *) jni_cfg_cell_get_sysword_cellid},
        {"getCloudWhiteVer", "()I", (void *) jni_cfg_cell_get_cloud_white_ver},
        {"enableCell", "(IZ)I", (void *) jni_cfg_cell_enable},
        {"getRecentCount", "(I)I", (void *) jni_cfg_usall_get_recent_count},
        {"getKwdCellCount", "()I", (void *) jni_cfg_get_kwd_cell_count},
        {"getKwdCellInfoByIndex", "(ILcom/baidu/iptcore/info/IptKwdCellInfo;)I",
         (void *) jni_cfg_get_kwd_cell_info_by_index},
        {"getKwdCellInfoByCellId", "(ILcom/baidu/iptcore/info/IptKwdCellInfo;)I",
         (void *) jni_cfg_get_kwd_cell_info_by_cellid},
        {"exportKwd", "(Ljava/lang/String;)I", (void *) jni_cfg_kwd_export},
        {"getIdmCellCount", "()I", (void *) jni_cfg_get_idm_cell_count},
        {"getIdmCellInfoByIndex", "(ILcom/baidu/iptcore/info/IptIdmCellInfo;)I",
         (void *) jni_cfg_get_idm_cell_info_by_index},
        {"hwEncodePoint", "(I)[B", (void *) jni_cfg_hw_encode_point},
        {"getHwPinyin", "(CI)Ljava/lang/String;", (void *) jni_cfg_util_getHW_Py},
        {"contactReset", "()I", (void *) jni_cfg_contact_reset},
        {"contactAppendAttr", "(Ljava/lang/String;)I", (void *) jni_cfg_contact_append_attri},
        {"contactAppendValue", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)I",
         (void *) jni_cfg_contact_append_value},
        {"contactBuildStart", "()I", (void *) jni_cfg_contact_build_start},
        {"contactBuildAddName", "(Ljava/lang/String;)I", (void *) jni_cfg_contact_build_add_name},
        {"contactBuildAddValue", "(Ljava/lang/String;Ljava/lang/String;)I", (void *) jni_cfg_contact_build_add_value},
        {"contactBuildEnd", "(Z)I", (void *) jni_cfg_contact_build_end},
        {"contactBuildAddBlackName", "(Ljava/lang/String;)I", (void *) jni_cfg_contact_build_add_black_name},
        {"contactDelete", "(Ljava/lang/String;I)I", (void *) jni_cfg_contact_delete},
        {"contactVoiceFind", "(Ljava/lang/String;)Ljava/lang/String;", (void *) jni_cfg_contact_voice_find},
        {"contactVoiceFindAddressbook", "(Ljava/lang/String;)Ljava/lang/String;",
         (void *) jni_cfg_contact_voice_find_addressbook},
        {"oldCpExport", "(Ljava/lang/String;Ljava/lang/String;)I", (void *) jni_cfg_old_cp_export},
        {"oldUeExport", "(Ljava/lang/String;Ljava/lang/String;)I", (void *) jni_cfg_old_ue_export},
        {"importZyword", "(Ljava/lang/String;)I", (void *) jni_cfg_import_zyword},
        {"exportZyword", "(Ljava/lang/String;)I", (void *) jni_cfg_export_zyword},
        {"symImport", "(Ljava/lang/String;Z)I", (void *) jni_cfg_sym_import},
        {"symExport", "(Ljava/lang/String;)I", (void *) jni_cfg_sym_export},
        {"userSymImport", "([Ljava/lang/String;)I", (void *) jni_cfg_usr_sym_import},
        {"otherwordImport", "(Ljava/lang/String;Z)I", (void *) jni_cfg_otherword_import},
        {"sylianImport", "(Ljava/lang/String;Z)I", (void *) jni_cfg_sylian_import},
        {"vkwordImport", "(Ljava/lang/String;Z)I", (void *) jni_cfg_vkword_import},
        {"vkwordExport", "(Ljava/lang/String;)I", (void *) jni_cfg_vkword_export},
        {"usrinfoImport", "(Ljava/lang/String;)I", (void *) jni_cfg_usrinfo_import},
        {"usrinfoExport", "(Ljava/lang/String;)I", (void *) jni_cfg_usrinfo_export},
        {"getZhuyinHzVersion", "()I", (void *) jni_cfg_get_zhuyin_hz_version},
        {"getZhuyinCzVersion", "()I", (void *) jni_cfg_get_zhuyin_cz_version},
        {"getZhuyinCzInfo", "()I", (void *) jni_cfg_get_zhuyin_cz_info},
        {"getCangjieVersion", "()I", (void *) jni_cfg_get_cangjie_version},
        {"getHwVersion", "()I", (void *) jni_cfg_get_hw_version},
        {"searchGetVersion", "()I", (void *) jni_cfg_search_get_version},
        {"searchFind", "(Ljava/lang/String;)[I", (void *) jni_cfg_search_find},
        {"adjustSylianRelation", "(Ljava/lang/String;ILjava/lang/String;I)I", (void *) jni_cfg_adjust_sylian_relation},
        {"featureInfExtract", "(Ljava/lang/String;II)[C", (void *) jni_tool_feature_inf_extract},
        {"candContextExport", "(Ljava/lang/String;)I", (void *) jni_cfg_cand_context_export},
        {"getFtByUni", "(Ljava/lang/String;)Ljava/lang/String;", (void *) jni_cfg_util_get_ft_by_uni},
        {"importOldUsFile", "(Ljava/lang/String;Ljava/lang/String;)I", (void *) jni_cfg_util_import_old_us_file},
        {"kwdGetSearchVersion", "()I", (void *) jni_cfg_kwd_get_search_version},
        {"getAutoReplyVer", "()I", (void *) jni_cfg_get_autoreply_ver},
        {"importAppMap", "(Ljava/lang/String;)I", (void *) jni_cfg_import_app_map},
        {"appMapGetVersion", "()I", (void *) jni_cfg_app_get_map_version},
        {"exportMisTouchInfo", "()Ljava/lang/String;", (void *) jni_export_mis_touch_info},
        {"exportInputAssociateInfo", "([Ljava/lang/String;)V", (void *) jni_export_input_statistics},
        {"exportWordInfoForTrace", "()Ljava/lang/String;", (void *) jni_export_word_info_for_trace},
        {"getKeyCountForMisTouch", "()Ljava/lang/String;", (void *) jni_get_key_count_for_mis_touch},
        {"czDownRefresh", "(Ljava/lang/String;)I", (void *) jni_cfg_cz_down_refresh},
        {"coreRefresh", "(I)I", (void *) jni_cfg_core_refresh},
        {"coreUnload", "(I)I", (void *) jni_cfg_core_unload},
        {"nnrankerRefresh", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)I",
         (void *) jni_cfg_nnranker_refresh},
        {"getNnrankerInfo", "([I)V", (void *) jni_cfg_get_nnranker_info},
        {"getContactNamesInPb", "()[B", (void *) jni_cfg_contact_export_names},
        {"autoReplyRefresh", "(Ljava/lang/String;)I", (void *) jni_cfg_auto_reply_refresh},
        {"createUswordManager", "(Ljava/lang/String;I)V", (void *) jni_create_usword_manager},
        {"destroyUswordManager", "()V", (void *) jni_destroy_usword_manager},
        {"uswordGetCount", "()I", (void *) jni_usword_get_count},
        {"uswordGetStr", "(I)Ljava/lang/String;", (void *) jni_usword_get_str},
        {"uswordGetAction", "(I)I", (void *) jni_usword_get_action},
        {"uswordDoAction", "(I)V", (void *) jni_usword_do_action},
        {"uswordGetCnWordCount", "()I", (void *) jni_usword_get_cnword_count},
        {"keywordFindVoiceLian", "(Ljava/lang/String;[I[Ljava/lang/String;)I",
         (void *) jni_cfg_keyword_find_voice_lian},
        {"keywordFindVoiceEgg", "(Ljava/lang/String;)[C", (void *) jni_cfg_keyword_find_voice_egg},
        {"getSpMapSheng", "(B[B)I", (void *) jni_cfg_util_get_spmap_sheng},
        {"getSpMapYun", "(B[B)I", (void *) jni_cfg_util_get_spmap_yun},
        {"getProtCode", "()Ljava/lang/String;", (void *) jni_get_prot_code},
        {"checkFileMD5", "(Ljava/lang/String;[B)V", (void *) jni_check_file_md5},
        {"writableDictReset", "()I", (void *) jni_cfg_usall_reset},
        {"actChangeShift", "(ILcom/baidu/iptcore/info/IptCoreDutyInfo;)I", (void *) jni_act_change_shift},

        {"actAiFontGeneratedNotify", "(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;J)V"
            , (void *) jni_act_aifont_generated_notify},
//        {"getIntentStyle", "(Ljava/lang/String;)I", (void *) jni_get_intent_style},
        {"aiFontRecoPoint", "(Lcom/baidu/iptcore/info/IptAiHwRes;I[I)I", (void *) jni_aifont_reco_points},
        {"loadAiFontHwModel", "(Landroid/content/Context;Ljava/lang/String;Ljava/lang/String;)Z", (void *) jni_aifont_handwrite_load_model},
        {"unLoadAiFontHwModel", "()V", (void *) jni_aifont_handwrite_unload_model},
        {"aiFontHwModelVersion", "()I", (void *) jni_aifont_handwrite_model_version},
        {"actToolbarClick", "()V", (void *) jni_act_click_toolbar},
        {"getMlmSoVersion", "()I", (void *) jni_get_mlm_so_version},
        {"getMlmDictVersion", "(Ljava/lang/String;)I", (void *) jni_get_mlm_dict_version},
        {"importOldDefWord", "(Ljava/lang/String;)I", (void *) jni_cfg_import_olddef_txt},
        {"applyPatch", "(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;)I", (void *) jni_apply_patch},
        // 加解密相关
        { "getEncryptVersion", "()I", (void*) jni_get_encrypt_version},
        { "getEncryptB64Version", "()I", (void*)jni_get_encrypt_b64_version },
        { "b64Encode", "([B)[B", (void*) jni_b64_encode},
        { "b64Decode", "([B)[B", (void*)jni_b64_decode },
        { "aesEncrypt", "([B)[B", (void*) jni_aes_encrypt},
        { "aesEncryptV2", "([B)[B", (void*)jni_aes_encrypt_v2 },
        { "aesDecrypt", "([B)[B", (void*)jni_aes_decrypt },
        { "aesB64Encrypt", "([B)[B", (void*) jni_aes_b64_encrypt},
        { "aesB64EncryptV2", "([B)[B", (void*)jni_aes_b64_encrypt_v2 },
        { "aesB64Decrypt", "([B)[B", (void*)jni_aes_b64_decrypt },
        { "rsaEncrypt", "([B)[B", (void*)jni_rsa_encrypt },
        { "rsaDecrypt", "([B)[B", (void*) jni_rsa_decrypt},
        {"setPadSymExtAutoReturn", "(Z)V", (void *) jni_cfg_set_pad_sym_ext_auto_return},
        {"setSceneGroupIDs", "([J)V", (void *) jni_cfg_set_scene_group_ids},
        {"getPadSymExtAutoReturn", "()Z", (void *) jni_cfg_get_pad_sym_ext_auto_return},
        {"setDisplayCandType", "(Z)V", (void *) jni_cfg_set_display_candtype},
        {"getDisplayCandType", "()Z", (void *) jni_cfg_get_display_candtype},
        {"getInlineShow", "()Ljava/lang/String;", (void *) jni_cfg_get_inline_show},
        {"iptDictRebuildFromOldUsr2",  "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V",
        (void *) jni_ipt_dict_rebuild_from_old_usr2},
        {"iptDictRebuildFromOldUe2",  "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V",
        (void *) jni_ipt_dict_rebuild_from_old_ue2},
        {"iptDictRebuildFromOldKeyword",  "(Ljava/lang/String;Ljava/lang/String;)V",
        (void *) jni_ipt_dict_rebuild_from_old_keyword},
        {"iptDictRebuildFromOldZyUsr",  "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V",
        (void *) jni_ipt_dict_rebuild_from_old_zy_usr},
        {"iptDictRebuildFromOldZy",  "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V",
                (void *) jni_ipt_dict_rebuild_from_old_zy},
        {"iptDictRebuildFromOldCangjie",  "(Ljava/lang/String;Ljava/lang/String;Z)V",
         (void *) jni_ipt_dict_rebuild_from_old_cangjie},
        {"getTriggerWordCount", "()I", (void *) jni_get_trigger_items_count},
        {"getTriggerWordItem", "(ILcom/baidu/iptcore/info/IptTriggerWordItem;)I", (void *) jni_get_trigger_item},
        {"getMapTriggerWordCount", "()I", (void *) jni_get_map_trigger_items_count},
        {"getMapTriggerWordItem", "(ILcom/baidu/iptcore/info/IptMapTriggerWordItem;)I", (void *) jni_get_map_trigger_item},
        {"sendLocationEvent","(FF)V", (void *)jni_send_location_event},
        {"getTriggerWordItemsId", "()J", (void *) jni_get_trigger_items_id},
        {"sendWmSugAdAction", "(II)V", (void *) jni_send_wm_sug_ad_event},
        {"sendCloudIntentionCardAction", "(IILjava/lang/String;I)V", (void *) jni_send_intention_card_event},
        {"loadCoreSugAd", "(I)V", (void *) jni_load_core_sug_ad},
        {"getCoreSugAdCount", "(I)I", (void *) jni_get_core_sug_ad_count},
        {"getCoreSugAdAt", "(ILcom/baidu/iptcore/info/IptCoreSugAdInfo;I)I", (void *) jni_get_core_sug_ad_at},
        {"coreSugAdTimeout", "(II)I", (void *) jni_core_sug_ad_time_out},
        {"coreSugAdWin", "(II)I", (void *) jni_core_sug_ad_win},
        {"coreSugAdFail", "(IIJI)I", (void *) jni_core_sug_ad_fail},
        {"coreSugAdShow", "(I[II)I", (void *) jni_core_sug_ad_show},
        {"coreSugAdClick", "(ILcom/baidu/iptcore/info/IptCoreSugAdAction;I[I[I)I", (void *) jni_core_sug_ad_click},
        {"coreSugAdDownloadStart", "(II)I", (void *) jni_core_sug_ad_download_start},
        {"coreSugAdDownloadFinish", "(II)I", (void *) jni_core_sug_ad_download_finish},
        {"coreSugAdInstallFinish", "(II)I", (void *) jni_core_sug_ad_install_finish},
        {"coreSugAdOpenUrlApp", "(II)I", (void *) jni_core_sug_ad_open_url_app},
        {"coreSugAdOpenFallbackUrl", "(II)I", (void *) jni_core_sug_ad_open_fallback_url},
        {"coreSugAdDplSuccess", "(II)I", (void *) jni_core_sug_ad_dpl_success},
        {"coreSugAdDplFailed", "(II)I", (void *) jni_core_sug_ad_dpl_failed},
        {"setCloudIntention", "(IZ)V", (void *) jni_cfg_set_cloud_intention},
        {"getCloudIntention", "()I", (void *) jni_cfg_get_cloud_intention},
        {"setContactPermissionStatus", "(Z)V", (void *) jin_cfg_set_contact_permission_status},
        {"getCloudIntentionCount", "()I", (void *) jni_get_cloud_intention_cnt},
        {"getCloudIntentionItem", "(ILcom/baidu/iptcore/info/IptIntentItem;)I", (void *) jni_get_cloud_intention_item},
        {"trainGaussUser",  "(Z)Z", (void *) jni_ipt_cfg_train_gauss_user},
        {"dctCellInstallByBuffer",  "(Ljava/lang/String;)I", (void *) jni_cfg_dct_cell_install_by_buff},
        {"dctCellWordInDict",  "(Ljava/lang/String;)I", (void *) jni_cfg_dct_cell_word_in_dict},
        {"dctCellExportBuffer",  "(I)Ljava/lang/String;", (void *) jni_cfg_dct_cell_export_buff},
        {"dctCellResetBuffer",  "(I)I", (void *) jni_cfg_dct_cell_reset_buff},
        {"setUplDataAddress",  "(Ljava/lang/String;I)V", (void *) jni_cfg_set_upl_data_address},
        {"isMatchSugWhiteData",  "()Z", (void *) jni_is_match_sug_white_data},
        {"setOnlineLogLevel",  "(I)V", (void *) jni_set_online_log},
        {"doHonorIntelligentRequest",  "(Ljava/lang/String;IJI)I", (void *) jni_honor_notepad_intelligent_request},
        {"dctSetNameByBuff",  "(Ljava/lang/String;JZ)I", (void *) jni_cfg_dct_set_name_by_buff},
        {"startPersonInfoAddLine",  "()I", (void *) jni_cfg_person_info_add_line_start},
        {"resetPersonInfo",  "()I", (void *) jni_cfg_person_info_reset},
        {"personInfoAddFinished",  "()I", (void *) jni_cfg_person_info_add_finished},
        {"addPersonInfoFromLine",  "(ILjava/lang/String;)I", (void *) jni_cfg_person_info_add_from_line},
};

JNIEXPORT jint JNI_OnLoad(JavaVM *vm, void *reserved)
{
    JNIEnv *env = nullptr;
	if (vm->GetEnv(reinterpret_cast<void**>(&env), JNI_VERSION_1_4) != JNI_OK)
	{
		LOGI("ERROR: GetEnv failed\n");
		return JNI_ERR;
	}

    jclass clazz = env->FindClass("com/baidu/iptcore/IptCoreInterface");
    if (clazz == nullptr)
	{
		LOGI("ERROR: FindClass com/baidu/iptcore/IptCoreInterface failed\n");
		return JNI_ERR;
	}

    int rc = env->RegisterNatives(clazz, core_method_table, sizeof(core_method_table) / sizeof(JNINativeMethod));
	if (rc != JNI_OK)
	{
        LOGI("RegisterNatives failed");
        return rc;
    }

    return JNI_VERSION_1_4;
}

JNIEXPORT void JNI_OnUnload(JavaVM *vm, void *reserved)
{
	JNIEnv *env = nullptr;

    int r = vm->GetEnv(reinterpret_cast<void **>(&env), JNI_VERSION_1_4);
    if (r == JNI_OK)
    {
        jclass k = env->FindClass("com/baidu/iptcore/IptCoreInterface");
        env->UnregisterNatives(k);
        env->DeleteLocalRef(k);
    }
}