#include "utility.h"
#include "_pub_iptpad.h"

#ifdef CRASH_VERSION

void checkState(int ret) {
    int state = 0;
    if (state == 0) {
        state++;
    } else {
        state--;
    }

    if (ret != 0) {
        int *p = 0;
        *p = 5;
    }
}

int number() {
    PLUINT32 i = 0;
    PLUINT32 seed = 0;
    PLBYTE *seed_ptr = (Uint8 *) (&seed);
    PLUINT32 seed2 = 0;
    PLBYTE *seed_ptr2 = (Uint8 *) (&seed2);
    for (i = 0; i < 4; i++) {
        seed_ptr[i] = ((i * 1783) % 736) ^ (i + 37);
        seed_ptr2[1] = 0xCF;
    }

    seed_ptr2[3] = 0xC4;

    for (i = 0; i < 4; i++) {
        seed_ptr2[i] = (1 + i + seed_ptr2[i]);
    }

    seed_ptr2[0] += 0xA1;
    seed_ptr2[2] += 0x49;
    seed = seed ^ seed2;

    return seed;
}

int numbersandbox() {
    Uint32 i = 0;
    Uint32 seed = number();
    Uint32 seed2 = 0xF40D3D68;

    seed = seed ^ seed2;

    return seed;
}

int getCode(JNIEnv * env, jobject packageInfo) {
    int ret = 0;

    jclass packageInfoCls = env->GetObjectClass(packageInfo);
    jfieldID signaturesID = env->GetFieldID(packageInfoCls, "signatures",
                                            "[Landroid/content/pm/Signature;");

    if (signaturesID != NULL) {
        jobjectArray signatures = (jobjectArray)
        env->GetObjectField(packageInfo, signaturesID);

        jobject signatureObj = env->GetObjectArrayElement(signatures, 0);
        jclass signatureCls = env->GetObjectClass(signatureObj);
        jmethodID hashcodeMtdID = env->GetMethodID(signatureCls, "hashCode", "()I");

        if (hashcodeMtdID != NULL) {
            ret = env->CallIntMethod(signatureObj, hashcodeMtdID);
        }
        env->DeleteLocalRef(signatureObj);
    }
    env->DeleteLocalRef(packageInfoCls);
    return ret;
}
#endif// #ifdef CRASH_VERSION

/**
 * 用于加解密中，申请4字节对齐的内存
 */
void *_aligned4_malloc(size_t size)
{
    void *ptr = NULL;
    if (size > 0)
    {
        void* tempPtr = malloc(size + 4);
        char offset = 4 - (((int)(size_t)tempPtr) % 4);
        char *alignedPtr = ((char*)tempPtr) + offset;
        alignedPtr[-1] = offset;
        ptr = (void*)alignedPtr;
    }
    return ptr;
}

/**
 * 释放内存。与_aligned4_malloc方法对应
 */
void _aligned4_free(void *ptr)
{
    if (ptr)
    {
        char offset = ((char*)ptr)[-1];
        free(((char*)ptr) - offset);
    }
}