// The Maven plugin adds support for deploying artifacts to Maven repositories.
// 一个可以让你把库上传到maven仓库的插件
apply plugin: 'maven'
// The signing plugin adds the ability to digitally sign built files and artifacts.
// These digital signatures can then be used to prove who built the artifact the signature is attached to
// as well as other information such as when the signature was generated.
// 对库文件进行数字签名的插件,可以通过签名知道谁创建了这个库文件,签名的时间等等信息
apply plugin: 'signing'

// 声明变量记录maven库地址
def mavenRepositoryUrl
// 判断是发布到正式库,还是snapshots库
if (isReleaseBuild()) {
    println 'RELEASE BUILD'
    // 下面的库地址指向的是我们私有仓库的Releases 仓库
    mavenRepositoryUrl = hasProperty('RELEASE_REPOSITORY_URL') ? RELEASE_REPOSITORY_URL
            : "${MAVEN_URL}/maven-releases/"
} else {
    println 'SNAPSHOTS BUILD'
    // 下面的库地址指向的是我们私有仓库的snapshots 仓库
    mavenRepositoryUrl = hasProperty('SNAPSHOT_REPOSITORY_URL') ? SNAPSHOT_REPOSITORY_URL
            : "${MAVEN_URL}/maven-snapshots/"
}
// NEXUS_USERNAME等变量在我们主项目的gradle.properties中可以找到
def getRepositoryUsername() {
    return hasProperty('NEXUS_USERNAME') ? NEXUS_USERNAME : ""
}

def getRepositoryPassword() {
    return hasProperty('NEXUS_PASSWORD') ? NEXUS_PASSWORD : ""
}
// 根据我们在gradle.properties中声明的版本名称,来分辨是Release版本还是 snapshots版本
def isReleaseBuild() {
    return !POM_VERSION_NAME_IPTCORE.contains("SNAPSHOT");
}

// "afterEvaluate是在配置阶段要结束，项目评估完会走到这一步。"
// 引用自http://jiajixin.cn/2015/08/07/gradle-android/
afterEvaluate { project ->
    // 我们声明我们要执行的上传到maven的task
    uploadArchives {
        repositories {
            mavenDeployer {
                beforeDeployment { MavenDeployment deployment -> signing.signPom(deployment) }
                pom.artifactId = POM_ARTIFACT_ID_IPTCORE
                pom.groupId = POM_GROUP_ID
                pom.version = POM_VERSION_NAME_IPTCORE
                // 授权验证,这里也就是登陆搭建的私服服务器时候的用户名\密码
                repository(url: mavenRepositoryUrl) {
                    authentication(userName: getRepositoryUsername(), password: getRepositoryPassword())
                }

                // 这里是配置我们maven库需要的pom.xml文件的各个内容,具体意思我们在主目录gradle.properties中解释
                pom.project {
                    name POM_NAME_IPTCORE
                    packaging POM_PACKAGING_IPTCORE
                    description POM_DESCRIPTION_IPTCORE
                    url POM_URL

                    scm {
                        url POM_SCM_URL
                        connection POM_SCM_CONNECTION
                        developerConnection POM_SCM_DEV_CONNECTION
                    }

                    licenses {
                        license {
                            name POM_LICENCE_NAME
                            url POM_LICENCE_URL
                            distribution POM_LICENCE_DIST
                        }
                    }

                    developers {
                        developer {
                            id POM_DEVELOPER_ID
                            name POM_DEVELOPER_NAME
                        }
                    }
                }
            }
        }
    }

    // 进行数字签名
    signing {
        required { isReleaseBuild() && gradle.taskGraph.hasTask("uploadArchives") }
        sign configurations.archives
    }

    // type显示指定任务类型或任务, 这里指定要执行Javadoc这个task,这个task在gradle中已经定义
    task androidJavadocs(type: Javadoc) {
        // 设置源码所在的位置
        source = android.sourceSets.main.java.sourceFiles
    }

    // 生成javadoc.jar
    task androidJavadocsJar(type: Jar) {
        // 指定文档名称
        classifier = 'javadoc'
        from androidJavadocs.destinationDir
    }

    // 生成sources.jar
    task androidSourcesJar(type: Jar) {
        classifier = 'sources'
        from android.sourceSets.main.java.sourceFiles
    }

    // 产生相关配置文件的任务
    artifacts {
        archives androidSourcesJar
        archives androidJavadocsJar
    }
}