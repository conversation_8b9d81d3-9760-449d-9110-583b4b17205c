package com.baidu.iptcore;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import android.content.Context;
import android.support.test.InstrumentationRegistry;

import org.junit.AfterClass;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * 内核接口的词库相关接口的测试
 */
@RunWith(MockitoJUnitRunner.class)
public class IptCoreInterfaceCellTest {

    @BeforeClass
    public static void setupCore() {

        Context context = InstrumentationRegistry.getInstrumentation().getContext();
        IptCoreTestGlobal.init(context);

        IptCoreEnv andEnv = Mockito.mock(IptCoreEnv.class);
        IptCoreNetMan netMan = Mockito.mock(IptCoreNetMan.class);
        int ret = IptCoreInterface.get().open(andEnv, netMan, IptCoreTestGlobal.getDictPath(),
                IptCoreTestGlobal.getInstalledPkgInfo(), ImeCoreConsts.Flavor.MAINLINE);
        System.out.println("setupCore: openCore: " + ret);
    }

    @Test
    public void testCoreVer() {
        int coreVer = IptCoreInterface.get().getCoreVersion();
        int ver1 = (coreVer & 0xff000000) >>> 24;
        int ver2 = (coreVer & 0xff0000) >>> 16;
        int ver3 = (coreVer & 0xff00) >>> 8;
        int ver4 = coreVer & 0xff;
        String verName = ver1 + "." + ver2 + "." + ver3 + "." + ver4;
        assertEquals(verName, ImeCoreConsts.FILE_DATA_IPTCORE_VERSION);
    }

    @Test
    public void testCoreVerStr() {
        String coreVerStr = IptCoreInterface.get().getCoreVersionStr();
        assertNotNull(coreVerStr);
    }

    @Test
    public void testEnSysVer() {
        int enSysVer = IptCoreInterface.get().getEnSysDictVersion();
        assertTrue(enSysVer >= 773);
    }

    @Test
    public void testCz3SysVer() {
        int cz3DictGramVer = IptCoreInterface.get().getCz3DictGramVersion();
        int cz3DictSysVer = IptCoreInterface.get().getCz3DictSysVersion();
        int cz3DictCateVer = IptCoreInterface.get().getCz3DictCateVersion();
        assertEquals(cz3DictGramVer, 0);
        assertTrue(cz3DictSysVer >= 101129);
        assertEquals(cz3DictCateVer, 0);
    }

    @AfterClass
    public static void closeCore() {
        int ret = IptCoreInterface.get().close();
        System.out.println("closeCore: " + ret);
    }
}
