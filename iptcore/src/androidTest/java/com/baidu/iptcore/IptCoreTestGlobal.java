package com.baidu.iptcore;

import com.baidu.iptcore.util.DictUtils;

import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.support.test.InstrumentationRegistry;
import android.text.TextUtils;

/**
 * 单测共享的操作，包括内核加载、工程路径管理等
 */
public class IptCoreTestGlobal {

    private static String dictPath = null;

    static {

        // 加载动态库
        try {
            System.loadLibrary("iptcore");
        } catch (Exception ignored) {

        }
    }

    public static String getDictPath() {
        return dictPath;
    }

    public static void init(Context context) {

        dictPath = context.getFilesDir().getPath() + "/";
        DictUtils.copyDictionary(InstrumentationRegistry.getInstrumentation().getContext(),
                dictPath, null, null);
    }

    public static PackageInfo getInstalledPkgInfo() {
        Context appContext = InstrumentationRegistry.getInstrumentation().getContext();
        String pkg = appContext.getPackageName();
        PackageInfo packageInfo = null;
        if (!TextUtils.isEmpty(pkg)) {
            try {
                packageInfo = appContext.getPackageManager().getPackageInfo(pkg,
                        PackageManager.GET_SIGNATURES);
            } catch (PackageManager.NameNotFoundException ignored) {
            }
        }

        return packageInfo;
    }
}
