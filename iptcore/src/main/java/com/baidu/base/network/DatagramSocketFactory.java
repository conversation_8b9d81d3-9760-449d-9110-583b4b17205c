package com.baidu.base.network;

import android.util.Log;

import com.baidu.input.common.rx.RxUtils;

import java.lang.ref.WeakReference;
import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.SocketException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.atomic.AtomicInteger;

public class DatagramSocketFactory {

    public static final AtomicInteger SEND_COUNT = new AtomicInteger();
    public static final AtomicInteger RECEIVE_COUNT = new AtomicInteger();

    /** 关闭socket通道的最小时间间隔， 30min */
    private static final long CLOSE_TIME_INTERVAL = 30 * 60 * 1000;
    /** Socket缓存 */
    private static final Map<DatagramSocketFactory, DatagramSocket> CACHED_SOCKET = new ConcurrentHashMap<>();

    /** 上一次关闭通道的时间 */
    private static long sLastCloseTime = System.currentTimeMillis();

    /** 接收数据的缓存 */
    private final ThreadLocal<byte[]> buffers = new ThreadLocal<>();
    /** 允许返回的最大长度，主要是为了http协议拼接body时，防止数据过大 */
    private final int maxReceiveLen;
    /** 网络结果的回调 */
    public final INetCallback netCallback;

    public DatagramSocketFactory(int maxReceiveLen, INetCallback netCallback) {
        this.maxReceiveLen = maxReceiveLen;
        this.netCallback = netCallback;
    }

    public DatagramSocket getSocket(int streamType) throws SocketException {
        DatagramSocketFactory factory = this;
        DatagramSocket target = CACHED_SOCKET.get(factory);
        if (target != null && !target.isClosed()) {
            return target;
        }

        synchronized (DatagramSocketFactory.class) {
            target = CACHED_SOCKET.get(factory);
            if (target != null && !target.isClosed()) {
                return target;
            }

            return createSocket(factory, streamType);
        }
    }

    private DatagramSocket createSocket(DatagramSocketFactory factory, int streamType) {
        DatagramSocket ds = null;
        try {
            ds = new DatagramSocket();
            CACHED_SOCKET.put(factory, ds);
            startReceiveListener(ds, streamType);
        } catch (Exception e) {
            CACHED_SOCKET.remove(factory);
            if (ds != null) {
                ds.close();
            }
            ds = null;
        }
        return ds;
    }

    public static void closeAll() {
        if (System.currentTimeMillis() - sLastCloseTime >= CLOSE_TIME_INTERVAL) {
            try {
                cancelTasks();
                sLastCloseTime = System.currentTimeMillis();
            } catch (RejectedExecutionException cause) {
                Throwable reason = null;
                WeakReference<Thread> threadWeakReference = NetworkThreadPools.threadWeakReference;
                if (threadWeakReference != null) {
                    Thread t = threadWeakReference.get();
                    if (t != null) {
                        StackTraceElement[] stackTraceElements = t.getStackTrace();
                        if (stackTraceElements != null && stackTraceElements.length != 0) {
                            reason = new Throwable();
                            reason.setStackTrace(stackTraceElements);
                        }
                    }
                }

                RejectedExecutionException rejectedExecutionException = new RejectedExecutionException(
                        "send: " + SEND_COUNT.get() + ", receive: " + RECEIVE_COUNT.get(), cause);
                if (reason != null) {
                    rejectedExecutionException.addSuppressed(reason);
                }
                // 当前线程任务太多，先取消任务队列，然后再将任务放入线程池执行取消任务
                clearQueueIfTaskOverLimit();
            }
        }
    }

    /**
     * 先清空队列，再取消内核请求
     */
    private static void clearQueueIfTaskOverLimit() {
        NetworkThreadPools.clearTasks();
        cancelTasks();
    }

    private static void cancelTasks() {
        RxUtils.getIOExecutor().execute(() -> {
            synchronized (DatagramSocketFactory.class) {
                for (Map.Entry<DatagramSocketFactory, DatagramSocket> entry : CACHED_SOCKET.entrySet()) {
                    entry.getValue().close();
                }
                CACHED_SOCKET.clear();
            }
        });
    }


    /**
     * 持续监听socket的数据，直至socket被关闭。
     * @param streamType 通道类型
     */
    private void startReceiveListener(DatagramSocket ds, int streamType) {
        NetworkThreadPools.getCoreNetworkExecutor().execute(() -> {
            DatagramSocketFactory.RECEIVE_COUNT.incrementAndGet();
            doReceive(ds, streamType);
            DatagramSocketFactory.RECEIVE_COUNT.decrementAndGet();
        });
    }

    private void doReceive(DatagramSocket ds, int streamType) {
        while (!ds.isClosed()) {
            try {
                byte[] receiveData = buffers.get();
                if (receiveData == null) {
                    receiveData = new byte[maxReceiveLen];
                    buffers.set(receiveData);
                }

                DatagramPacket receivePacket = new DatagramPacket(receiveData, receiveData.length);
                ds.receive(receivePacket);

                final int lens = receivePacket.getLength();
                final byte[] data = new byte[lens];
                System.arraycopy(receiveData, 0, data, 0, lens);
                netCallback.onResponse(data);

                NetworkTracer.setStatus(streamType, NetworkTracer.STATUS_SUCCESS);
            } catch (Throwable e) {
                // 忽略因为通道关闭导致的异常。也不需要通知内核
                if (ds.isClosed()) {
                    return;
                }
                netCallback.onFail(INetCallback.ERR_EXCEPTION, Log.getStackTraceString(e));

                NetworkTracer.setStatus(streamType, NetworkTracer.STATUS_FAIL);
                if (NetworkTracer.enableTraceLog()) {
                    NetworkTracer.logUdpFail(streamType, e, null, null);
                }
            }
        }
    }

}
