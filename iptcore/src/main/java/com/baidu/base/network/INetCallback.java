package com.baidu.base.network;

/**
 * 联网请求的回调
 * Created by cdf on 2018/9/11.
 */
public interface INetCallback {

    /** 发送错误 */
    int ERR_CODE_SEND = 800;
    /** 联网出现Exception */
    int ERR_EXCEPTION = 801;

    /**
     * 联网请求成功的回调
     * @param responseBody 返回数据
     */
    void onResponse(byte[] responseBody);

    /**
     * 联网请求失败/错误的回调
     * @param errorCode 错误码，小于0
     * @param errorMsg 错误信息
     */
    void onFail(int errorCode, String errorMsg);
}
