package com.baidu.base.network;

import java.lang.ref.WeakReference;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

public class NetworkThreadPools {

    private static final int CPU_COUNT = Runtime.getRuntime().availableProcessors();
    private static final int MAXIMUM_POOL_SIZE = CPU_COUNT * 2 + 1;
    private static final long KEEP_ALIVE = 60L;
    private static final int MAX_CAPACITY = 128;

    public static WeakReference<Thread> threadWeakReference;

    private static final ThreadFactory THREAD_FACTORY = new ThreadFactory() {
        private final AtomicInteger mCount = new AtomicInteger(1);

        public Thread newThread(Runnable r) {
            Thread t = new Thread(r, "CoreThreadPools #" + mCount.getAndIncrement());
            if (mCount.get() == 0) {
                threadWeakReference = new WeakReference<>(t);
            }
            return t;
        }
    };

    private static final ThreadPoolExecutor THREAD_POOL_NETWORK = new ThreadPoolExecutor(0, MAX_CAPACITY,
            KEEP_ALIVE, TimeUnit.SECONDS,
            new SynchronousQueue<>(),
            THREAD_FACTORY);

    public static ThreadPoolExecutor getCoreNetworkExecutor() {
        return THREAD_POOL_NETWORK;
    }

    public static void clearTasks() {
        getCoreNetworkExecutor().getQueue().clear();
    }
}
