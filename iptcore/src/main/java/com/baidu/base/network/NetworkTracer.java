package com.baidu.base.network;

import android.util.SparseArray;

import com.baidu.iptcore.ImeCoreConsts;
import com.baidu.iptcore.util.NetworkLogAdapter;

import java.io.IOException;

/**
 * 网络状态
 */
public class NetworkTracer {

    /** 云输入状态：成功 */
    public static final int STATUS_SUCCESS = 1;
    /** 云输入状态：Socket异常 */
    public static final int STATUS_SOCKET_EXCEPTION = 2;
    /** 云输入状态：失败 */
    public static final int STATUS_FAIL = 3;
    /** 云输入状态: Socket读写超时 */
    public static final int STATUS_SOCKET_TIMEOUT_EXCEPTION = 4;

    /** 网络状态记录。目前只统计云输入相关的通道 */
    private static final SparseArray<NetworkTracer.TraceData> TRACE_DATA_SPARSE_ARRAY = new SparseArray<>();

    private static boolean enableNetworkLog;

    private static NetworkLogAdapter printer;

    public static void init(boolean enableLog, NetworkLogAdapter logAdapter) {
        enableNetworkLog = enableLog;
        printer = logAdapter;

        // 目前只统计云输入相关的通道
        TRACE_DATA_SPARSE_ARRAY.put(ImeCoreConsts.StreamType.STREAM_CLOUD, new TraceData());
        TRACE_DATA_SPARSE_ARRAY.put(ImeCoreConsts.StreamType.STREAM_KV, new TraceData());
    }

    public static void setIp(int streamType, String ip) {
        TraceData data = TRACE_DATA_SPARSE_ARRAY.get(streamType);
        if (data != null) {
            data.ip = ip;
        }
    }

    public static void setCost(int streamType, long cost) {
        TraceData data = TRACE_DATA_SPARSE_ARRAY.get(streamType);
        if (data != null) {
            data.cost = cost;
        }
    }

    public static void setStatus(int streamType, int status) {
        TraceData data = TRACE_DATA_SPARSE_ARRAY.get(streamType);
        if (data != null) {
            data.status = status;
        }
    }

    /**
     * 当前的streamType是否有统计数据
     */
    public static boolean isIncludedStatistics(int streamType) {
        return TRACE_DATA_SPARSE_ARRAY.get(streamType) != null;
    }

    public static int getStatus(int streamType) {
        TraceData data = TRACE_DATA_SPARSE_ARRAY.get(streamType);
        if (data != null) {
            return data.status;
        }
        return STATUS_FAIL;
    }

    public static long getCost(int streamType) {
        TraceData data = TRACE_DATA_SPARSE_ARRAY.get(streamType);
        if (data != null) {
            return data.cost;
        }
        return 0;
    }

    public static String getIp(int streamType) {
        TraceData data = TRACE_DATA_SPARSE_ARRAY.get(streamType);
        if (data != null) {
            return data.ip;
        }
        return "";
    }

    public static boolean enableTraceLog() {
        return enableNetworkLog;
    }

    public static void logHttpFail(String url, IOException e) {
        if (printer != null) {
            printer.logHttpFail(url, e);
        }
    }

    public static void logHttpResponse(String url, int code, long cost) {
        if (printer != null) {
            printer.logHttpResponse(url, code, cost);
        }
    }

    public static void logUdpResponse(int cloudInputType) {
        if (printer != null) {
            printer.logUdpResponse(cloudInputType);
        }
    }

    public static void logUdpFail(int streamType, Throwable e, byte[] sendData, byte[] receivedData) {
        if (printer != null) {
            printer.logUdpFail(streamType, e, sendData, receivedData);
        }
    }

    /**
     * 网络状态的数据
     */
    private static class TraceData {
        /**
         * 状态：1成功，2超时，3失败
         */
        private volatile int status;
        /**
         * 响应时长单位毫秒，超时就填写超时时长
         */
        private volatile long cost;
        /**
         * 外网IP
         */
        private volatile String ip;
    }
}
