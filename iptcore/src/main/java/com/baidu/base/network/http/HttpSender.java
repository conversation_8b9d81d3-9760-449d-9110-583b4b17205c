package com.baidu.base.network.http;

import android.util.Log;

import com.baidu.base.network.INetCallback;
import com.baidu.base.network.NetworkTracer;
import com.baidu.input.common.network.retrofit.RetrofitUtils;
import com.baidu.iptcore.BuildConfig;
import com.baidu.iptcore.ImeCoreConsts;
import com.baidu.iptcore.dependency.ImeNetworkComponent;
import com.baidu.iptcore.dependency.IImeNetworkProvider;
import com.baidu.iptcore.util.Logger;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public class HttpSender {

    private static volatile OkHttpClient sClient;
    private final INetCallback netCallback;
    private final String url;
    private final byte[] sendData;
    private final int connectTimeout;
    private long startTime;
    private int streamType;

    public HttpSender(INetCallback netCallback, String url, int streamType, byte[] sendData, int timeout) {
        this.netCallback = netCallback;
        this.url = url;
        this.streamType = streamType;
        this.sendData = sendData;
        this.connectTimeout = timeout;
    }

    public void startLink() {
        Callback httpCallback = buildCallback();
        RequestBody requestBody =
                RequestBody.create(MediaType.parse("application/octet-stream"), sendData);

        startTime = System.currentTimeMillis();

        // 上传数据，需要拼接上层的自定义参数，所以使用Retrofit
        if (streamType == ImeCoreConsts.StreamType.STREAM_UPLOAD_DATA
                || streamType == ImeCoreConsts.StreamType.STREAM_UPLOAD_DATA_9) {
            doUploadRtData(requestBody, httpCallback, streamType);
            return;
        }

        OkHttpClient client = getClient(connectTimeout);
        Request request = new Request.Builder()
                .url(url)
                .post(requestBody)
                .build();
        client.newCall(request).enqueue(httpCallback);

    }

    /**
     * 内核实时数据上传
     *
     * @param requestFile 请求体
     * @param callback    回调
     */
    private void doUploadRtData(RequestBody requestFile, Callback callback, int streamType) {
        MultipartBody body = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("cfile", "cfile", requestFile)
                .build();
        StringBuilder sb = new StringBuilder(url);
        if (!url.endsWith("?") && !url.endsWith("&")) {
            if (url.contains("?")) {
                sb.append("&");
            } else {
                sb.append("?");
            }
        }
        switch (streamType) {
            case ImeCoreConsts.StreamType.STREAM_UPLOAD_DATA:
                sb.append("log_type=7");
                break;
            case ImeCoreConsts.StreamType.STREAM_UPLOAD_DATA_9:
                sb.append("log_type=9");
                break;
            default:
                break;
        }
        Request request = new Request.Builder().url(sb.toString()).post(body).build();
        RetrofitUtils.getRetrofit().callFactory().newCall(request).enqueue(callback);
    }

    private Callback buildCallback() {
        return new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {

                NetworkTracer.setStatus(streamType, NetworkTracer.STATUS_FAIL);

                if (null == netCallback) {
                    return;
                }

                if (NetworkTracer.enableTraceLog()) {
                    NetworkTracer.logHttpFail(url, e);
                }

                netCallback.onFail(INetCallback.ERR_EXCEPTION, Log.getStackTraceString(e));
            }

            @Override
            public void onResponse(Call call, Response response) {
                long cost = System.currentTimeMillis() - startTime;
                if (NetworkTracer.enableTraceLog()) {
                    NetworkTracer.logHttpResponse(url, response.code(), cost);
                }

                NetworkTracer.setStatus(streamType,
                        response.isSuccessful() ? NetworkTracer.STATUS_SUCCESS : NetworkTracer.STATUS_FAIL);

                if (null == netCallback) {
                    return;
                }

                if (!response.isSuccessful()) {
                    netCallback.onFail(response.code(), response.message());
                    return;
                }

                try {
                    byte[] data = response.body().bytes();
                    netCallback.onResponse(data);
                } catch (Exception e) {
                    if (BuildConfig.DEBUG) {
                        Logger.printStackTrace(e);
                    }
                    netCallback.onFail(INetCallback.ERR_EXCEPTION, Log.getStackTraceString(e));
                }
            }
        };
    }

    private static OkHttpClient getClient(long connectTimeout) {
        if (sClient == null || sClient.connectTimeoutMillis() != connectTimeout) {
            synchronized (HttpSender.class) {
                if (sClient == null || sClient.connectTimeoutMillis() != connectTimeout) {
                    sClient = createHttpClient(connectTimeout);
                }
            }
        }
        return sClient;
    }

    private static OkHttpClient createHttpClient(long connectTimeout) {

        IImeNetworkProvider provider = ImeNetworkComponent.of().getNetworkProvider();
        OkHttpClient client = null;
        if (provider != null) {
            client = provider.provideOkHttpClient(connectTimeout);
        }
        if (client == null) {
            client = new OkHttpClient.Builder()
                    .connectTimeout(connectTimeout, TimeUnit.MILLISECONDS)
                    .build();
        }

        return client;
    }

}
