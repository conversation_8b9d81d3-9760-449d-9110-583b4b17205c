package com.baidu.base.network.udp;

import android.support.annotation.NonNull;
import android.util.Log;

import com.baidu.base.network.DatagramSocketFactory;
import com.baidu.base.network.INetCallback;
import com.baidu.base.network.NetworkTracer;
import com.baidu.iptcore.BuildConfig;
import com.baidu.iptcore.util.Config;
import com.baidu.iptcore.util.Logger;

import java.io.Closeable;
import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.InetAddress;
import java.net.SocketException;
import java.net.SocketTimeoutException;
import java.net.UnknownHostException;

/**
 * UDP请求
 * Created by chendan<PERSON> on 2018/10/28.
 */
@Deprecated
public class UdpSender implements Runnable {
    private static String testUrl;

    private final INetCallback netCallback;
    private final String url;
    private final byte[] sendData;
    private final int port;
    private final int connectTimeout;
    private final int maxRecvLen;
    private final int streamType;

    public UdpSender(INetCallback netCallback, String url, int streamType, byte[] sendData,
                     int port, int timeout, int maxRecvLen) {
        this.netCallback = netCallback;
        this.url = url;
        this.streamType = streamType;
        this.sendData = sendData;
        this.port = port;
        this.connectTimeout = timeout;
        this.maxRecvLen = maxRecvLen;
    }

    public void startLink() {
        new Thread(this).start();
    }

    @Override
    public void run() {
        byte[] sendData = getUdpData();
        byte[] receivedata = null;
        if (sendData != null) {
            DatagramSocket ds = null;
            try {
                // 如果当前为纯净模式
                if (Config.isPureMode()) {
                    DatagramSocketFactory.closeAll();
                    return;
                }
                DatagramPacket p = getDatagramPacket(sendData);
                if (p != null) {

                    NetworkTracer.setIp(streamType, p.getAddress().getHostAddress());
                    long start = System.currentTimeMillis();

                    ds = new DatagramSocket();
                    ds.setSoTimeout(connectTimeout);
                    ds.send(p);
                    receivedata = new byte[maxRecvLen];
                    DatagramPacket recvpacket = new DatagramPacket(receivedata, receivedata.length);
                    ds.receive(recvpacket);

                    long cost = System.currentTimeMillis() - start;
                    NetworkTracer.setCost(streamType, cost);
                    NetworkTracer.setStatus(streamType, NetworkTracer.STATUS_SUCCESS);

                    if (NetworkTracer.enableTraceLog()) {
                        NetworkTracer.logUdpResponse(streamType);
                    }

                    onResponse(receivedata);
                } else {
                    NetworkTracer.setStatus(streamType, NetworkTracer.STATUS_FAIL);
                    onFailure(INetCallback.ERR_CODE_SEND, "datagram packet data null");

                    if (NetworkTracer.enableTraceLog()) {
                        NetworkTracer.logUdpFail(streamType, null, sendData, receivedata);
                    }
                }
            } catch (SocketException e) {
                if (BuildConfig.DEBUG) {
                    Logger.printStackTrace(e);
                }
                NetworkTracer.setStatus(streamType, NetworkTracer.STATUS_SOCKET_EXCEPTION);
                onFailure(INetCallback.ERR_EXCEPTION, Log.getStackTraceString(e));

                if (NetworkTracer.enableTraceLog()) {
                    NetworkTracer.logUdpFail(streamType, e, sendData, receivedata);
                }
            } catch (SocketTimeoutException e) {
                if (BuildConfig.DEBUG) {
                    Logger.printStackTrace(e);
                }
                NetworkTracer.setStatus(streamType, NetworkTracer.STATUS_SOCKET_TIMEOUT_EXCEPTION);
                onFailure(INetCallback.ERR_EXCEPTION, Log.getStackTraceString(e));

                if (NetworkTracer.enableTraceLog()) {
                    NetworkTracer.logUdpFail(streamType, e, sendData, receivedata);
                }
            }  catch (Throwable e) {
                if (BuildConfig.DEBUG) {
                    Logger.printStackTrace(e);
                }
                NetworkTracer.setStatus(streamType, NetworkTracer.STATUS_FAIL);
                onFailure(INetCallback.ERR_EXCEPTION, Log.getStackTraceString(e));

                if (NetworkTracer.enableTraceLog()) {
                    NetworkTracer.logUdpFail(streamType, e, sendData, receivedata);
                }
            } finally {
                closeQuietly(ds);
            }
        }
    }

    /**
     * 获取UDP需要发送的数据
     */
    private byte[] getUdpData() {
        return sendData;
    }

    /**
     * 根据发送数据，获取UDP的DatagramPacket
     * @param data 待发送数据
     * @return 数据包
     */
    private DatagramPacket getDatagramPacket(@NonNull byte[] data) throws UnknownHostException {
        String nowUrl = testUrl == null ? this.url : testUrl;
        InetAddress server = InetAddress.getByName(nowUrl);
        DatagramPacket p = new DatagramPacket(data, data.length, server, port);
        return p;
    }

    /**
     * 处理接收到的数据
     * @param recvData 接收数据
     */
    private void onResponse(@NonNull byte[] recvData) {
        int lens = (recvData[10] << 8) + (recvData[11] & 0xff);
        byte[] data = new byte[lens + 12];
        System.arraycopy(recvData, 0, data, 0, lens + 12);

        if (netCallback != null) {
            netCallback.onResponse(data);
        }
    }

    /**
     * 处理发送过程中的错误
     * @param errorCode 错误码
     * @param errorMessage 错误信息
     */
    private void onFailure(int errorCode, String errorMessage) {
        if (netCallback != null) {
            netCallback.onFail(errorCode, errorMessage);
        }
    }

    public static void closeQuietly(Closeable... closeables) {
        if (closeables == null) {
            return;
        }
        for (Closeable closeable : closeables) {
            if (closeable != null) {
                try {
                    closeable.close();
                } catch (Exception e) {
                    if (BuildConfig.DEBUG) {
                        Logger.printStackTrace(e);
                    }
                }
            }
        }
    }

}
