package com.baidu.base.network.udp;

import android.util.Log;

import com.baidu.base.network.DatagramSocketFactory;
import com.baidu.base.network.INetCallback;
import com.baidu.base.network.NetworkThreadPools;
import com.baidu.base.network.NetworkTracer;
import com.baidu.iptcore.util.Config;

import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.InetAddress;
import java.util.concurrent.RejectedExecutionException;

public class UdpSender3 {

    public static void sendData(DatagramSocketFactory factory,
                                int streamType,
                                String url,
                                int port,
                                byte[] sendData) {

        try {
            if (Config.isPureMode()) {
                DatagramSocketFactory.closeAll();
                return;
            }
            NetworkThreadPools.getCoreNetworkExecutor().execute(() -> {
                DatagramSocketFactory.SEND_COUNT.incrementAndGet();
                doSend(factory, streamType, url, port, sendData);
                DatagramSocketFactory.SEND_COUNT.decrementAndGet();
            });
        } catch (RejectedExecutionException exception) {
            // 因为过于频繁打字导致线程池超过限制时，忽略本次请求，直接通知失败。记录到ErrorAnalyzer中
            factory.netCallback.onFail(INetCallback.ERR_EXCEPTION, Log.getStackTraceString(exception));

            NetworkTracer.setStatus(streamType, NetworkTracer.STATUS_FAIL);
            if (NetworkTracer.enableTraceLog()) {
                NetworkTracer.logUdpFail(streamType, exception, null, null);
            }
        }
    }

    private static void doSend(DatagramSocketFactory factory,
                               int streamType,
                               String url,
                               int port,
                               byte[] sendData) {
        DatagramSocket ds = null;
        try {
            InetAddress server = InetAddress.getByName(url);
            DatagramPacket sendPacket = new DatagramPacket(sendData, sendData.length, server, port);

            ds = factory.getSocket(streamType);
            ds.send(sendPacket);

        } catch (Throwable e) {
            // 忽略因为通道关闭导致的异常。也不需要通知内核
            if (ds != null && ds.isClosed()) {
                return;
            }

            factory.netCallback.onFail(INetCallback.ERR_EXCEPTION, Log.getStackTraceString(e));

            NetworkTracer.setStatus(streamType, NetworkTracer.STATUS_FAIL);
            if (NetworkTracer.enableTraceLog()) {
                NetworkTracer.logUdpFail(streamType, e, null, null);
            }
        }
    }
}
