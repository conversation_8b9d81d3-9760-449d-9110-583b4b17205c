package com.baidu.cocomodule;

import com.baidu.cocoannotation.CocoInterface;
import com.baidu.input.modular.ImeLifecycleModule;
import com.baidu.input.modular.ObservableImeService;

@CocoInterface(ICoreLifecycle.class)
public class CoreLifecycleModule extends ImeLifecycleModule<CoreObserver> implements ICoreLifecycle {

    @Override
    protected CoreObserver createObserverImpl(ObservableImeService service) {
        return new CoreObserver(service);
    }
}
