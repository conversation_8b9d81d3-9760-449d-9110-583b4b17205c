package com.baidu.cocomodule;

import com.baidu.base.network.DatagramSocketFactory;
import com.baidu.input.modular.ImeAbstractObserver;
import com.baidu.input.modular.ObservableImeService;

import java.util.concurrent.ExecutorService;

public class CoreObserver extends ImeAbstractObserver {

    public CoreObserver(ObservableImeService service) {
        super(service);
    }

    @Override
    protected ExecutorService moduleBlockThread() {
        return null;
    }

    @Override
    protected boolean shouldNotifyInitialization() {
        return false;
    }

    @Override
    public void onWindowHidden() {
        super.onWindowHidden();
        DatagramSocketFactory.closeAll();
    }
}
