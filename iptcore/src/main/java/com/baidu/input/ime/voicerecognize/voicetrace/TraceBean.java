package com.baidu.input.ime.voicerecognize.voicetrace;

import java.util.List;

/**
 * 用于描述语音轨迹
 * 描述一个过程：起始点：是语音识别开始；结束：1、用户点击了发送按钮；
 * 2、下次输入时光标前的文字是上次语音识别上屏结果的最后一个字；3、收起面板。
 * Created by wufeiyang on 2018/3/22.
 */

public class TraceBean implements Cloneable {

    /**
     * msgType : 1
     * bundleID : com.baidu.input
     * timeStamp : 1520996828
     * PID : 593
     * SpeedID : ["1243-4324-3244-4324","3432-4324-4324-4322"]
     * SN : ["2342-4324-4324-4223","4322-4322-4322-1233"]
     * trace : [{"traceType":0,"para":3},{"type":1,"para":"哈哈"}]
     */

    /**
     * commit  0 一天传一次; modify = 1 识别完成立刻上传，其中commit 本意为用户认可了
     * 语音识别结果，实现策略为：1、用户点击了发送按钮；2、下次输入时光标前的文字是
     * 上次语音识别上屏结果的最后一个字且光标后没有文字；3、语音识别结束后用户接着收
     * 起了面板。用户非 commit 操作均为 modify。
     */
    private int type;
    /**
     * //当前输入框所在的APP的 packageName
     */
    private String tbid;
    /**
     * //当前输入框所在的APP的 packageName
     */
    private String bid;
    /**
     * // 语音识别发生的时间戳
     */
    private long ts;
    private int pid;
    private List<String> sid;
    /**
     * // 语音识别 SN，如果 msgType 为 commit 则仅传输此字段。
     */
    private String sn;
    /**
     * Item1:
     * traceType:(number insert=1)
     * content:text(string)    // 插入内容
     * beforeInput:text(string) // 插入光标前的文字
     * afterInput:text(string)	// 插入光标后的文字
     * Item2:
     * traceType:(number delete=2)
     * beforeInput:text(string) // 删除光标前的文字
     * afterInput:text(string)	// 删除光标后的文字
     */
    private List<TraceEntity> trc;

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getTbid() {
        return tbid;
    }

    public void setTbid(String tbid) {
        this.tbid = tbid;
    }

    public String getBid() {
        return bid;
    }

    public void setBid(String bid) {
        this.bid = bid;
    }

    public long getTs() {
        return ts;
    }

    public void setTs(long ts) {
        this.ts = ts;
    }

    public int getPid() {
        return pid;
    }

    public void setPid(int pid) {
        this.pid = pid;
    }

    public List<String> getSid() {
        return sid;
    }

    public void setSid(List<String> sid) {
        this.sid = sid;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public List<TraceEntity> getTrc() {
        return trc;
    }

    public void setTrc(List<TraceEntity> trc) {
        this.trc = trc;
    }

    public static class TraceEntity {
        /**
         * traceType : 0
         * para : 3
         * type : 1
         */

        private int act;
        private String txt;
        private String pre;
        private String suf;

        public int getAct() {
            return act;
        }

        public void setAct(int act) {
            this.act = act;
        }

        public String getTxt() {
            return txt;
        }

        public void setTxt(String txt) {
            this.txt = txt;
        }

        public String getPre() {
            return pre;
        }

        public void setPre(String pre) {
            this.pre = pre;
        }

        public String getSuf() {
            return suf;
        }

        public void setSuf(String suf) {
            this.suf = suf;
        }
    }
}
