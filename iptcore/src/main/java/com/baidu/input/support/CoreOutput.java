package com.baidu.input.support;

import com.baidu.input.support.state.AICandIconState;
import com.baidu.input.support.state.AICandState;
import com.baidu.input.support.state.AICorrectInlineState;
import com.baidu.input.support.state.AIIconInLXState;
import com.baidu.input.support.state.AIPadDataState;
import com.baidu.input.support.state.AIPadLoadingState;
import com.baidu.input.support.state.CandInfoState;
import com.baidu.input.support.state.ContactInfoState;
import com.baidu.input.support.state.CoreTraceState;
import com.baidu.input.support.state.EggInfoState;
import com.baidu.input.support.state.InlineShowState;
import com.baidu.input.support.state.InputState;
import com.baidu.input.support.state.IntentState;
import com.baidu.input.support.state.IptCandState;
import com.baidu.input.support.state.IptListState;
import com.baidu.input.support.state.MapTriggerWordsInfoState;
import com.baidu.input.support.state.RareCandState;
import com.baidu.input.support.state.SugAdState;
import com.baidu.input.support.state.SugCardState;
import com.baidu.input.support.state.SugState;
import com.baidu.input.support.state.TriggerWordsInfoState;
import com.baidu.iptcore.ImeCoreConsts;
import com.baidu.iptcore.info.IptCoreDutyInfo;

/**
 * 内核的数据输出的封装
 */
public class CoreOutput {

    public static final int IN_CORE_THREAD = 0;
    public static final int IN_MAIN_THREAD = 1;
    /**
     * 封装Cand候选词的数据模型
     */
    final IptCandState mIptCandState;

    /**
     * 封装InputBar的数据模型
     */
    final InputState mInputState = InputState.create();
    /**
     * 封装List的数据模型
     */
    final IptListState mIptListState = IptListState.create();

    /**
     * 智能云词次cand的信息
     */
    final AICandState mAICandState = AICandState.crete();

    /**
     * 智能云词次cand的Icon的状态（是否处于Loading）
     */
    final AICandIconState mAICandIconState = AICandIconState.crete();

    /**
     * 智能云词卡片Loading状态的数据模型
     */
    final AIPadLoadingState mAIPadLoadingState = AIPadLoadingState.crete();

    /**
     * 智能云词卡片Loading状态的数据模型
     */
    final AIPadDataState mAIPadDataState = AIPadDataState.crete();

    /**
     * 智能云词纠错的半上屏状态的数据模型
     */
    final AICorrectInlineState mAICorrectInlineState = AICorrectInlineState.crete();

    /**
     * Sug的数据封装
     */
    final SugState mSugState = SugState.create();
    /**
     * Sug卡片的数据封装
     */
    final SugCardState mSugCardState = SugCardState.create();

    /**
     * 候选词详细信息的封装
     */
    final CandInfoState mCandInfoState = CandInfoState.create();

    /**
     * 联系人详细信息的封装
     */
    final ContactInfoState mContactInfoState = ContactInfoState.create();

    /**
     * 彩蛋数据封装
     */
    final EggInfoState mEggInfoState = EggInfoState.create();

    /**
     * 封装内核的Trace记录的数据模型
     */
    final CoreTraceState mCoreTraceState = CoreTraceState.create();

    /**
     * inline state
     */
    final InlineShowState mInlineShowState = InlineShowState.crete();

    /**
     *  触发词详细信息的封装
     */
    final TriggerWordsInfoState mTriggerWordInfoState = TriggerWordsInfoState.create();

    /**
     *  地图触发词详细信息的封装
     */
    final MapTriggerWordsInfoState mMapTriggerWordInfoState = MapTriggerWordsInfoState.create();

    /**
     * 联想词上的超会写入口
     */
    final AIIconInLXState aiIconInLXState = AIIconInLXState.crete();

    /**
     * RareCand state
     */
    final RareCandState mRareCandState;

    /**
     * sug 广告（穿山甲）
     */
    final SugAdState mSugAdState = SugAdState.create();

    /**
     * 云端意图信息的封装
     */
    final IntentState mIntentCandState = IntentState.crete();

    /**
     * 待切换到的padId
     */
    int mPadId = ImeCoreConsts.PAD_NONE;
    /**
     * 是否是外部驱动的面板切换
     */
    boolean mIsExternalLayoutSwitch;
    /**
     * 云输入白名单
     */
    int mSrvCloudVer = 0;
    /**
     * 当前内核数据输出对应的DutyInfo
     */
    IptCoreDutyInfo mDutyInfo;
    /**
     * SUG的选中位置
     */
    int mSugSelection;
    /**
     * SUG卡片的选中位置
     */
    int mSugCardSelection;

    /**
     * 返回给上传的actionKeyId 列表
     */
    int[] actionKeyIds;

    CoreOutput(int type) {
        mIptCandState = IptCandState.create(type);
        mRareCandState = RareCandState.create(type);
    }

    void copy(CoreOutput target) {
        mIptCandState.copy(target.mIptCandState);
        mInputState.copy(target.mInputState);
        mIptListState.copy(target.mIptListState);
        mSugState.copy(target.mSugState);
        mAICandState.copy(target.mAICandState);
        mAICandIconState.copy(target.mAICandIconState);
        mAIPadLoadingState.copy(target.mAIPadLoadingState);
        mAIPadDataState.copy(target.mAIPadDataState);
        mAICorrectInlineState.copy(target.mAICorrectInlineState);
        mSugCardState.copy(target.mSugCardState);
        mCandInfoState.copy(target.mCandInfoState);
        mContactInfoState.copy(target.mContactInfoState);
        mEggInfoState.copy(target.mEggInfoState);
        mCoreTraceState.copy(target.mCoreTraceState);
        mInlineShowState.copy(target.mInlineShowState);
        mPadId = target.mPadId;
        mIsExternalLayoutSwitch = target.mIsExternalLayoutSwitch;
        mSrvCloudVer = target.mSrvCloudVer;
        mSugSelection = target.mSugSelection;
        mSugCardSelection = target.mSugCardSelection;
        mDutyInfo = target.mDutyInfo.copy();
        mTriggerWordInfoState.copy(target.mTriggerWordInfoState);
        mMapTriggerWordInfoState.copy(target.mMapTriggerWordInfoState);
        mRareCandState.copy(target.mRareCandState);
        mSugAdState.copy(target.mSugAdState);
        mIntentCandState.copy(target.mIntentCandState);
        // copy action keys 到主线程
        if (target.actionKeyIds != null && target.actionKeyIds.length > 0) {
            int length = target.actionKeyIds.length;
            actionKeyIds = new int[length];
            System.arraycopy(target.actionKeyIds, 0, actionKeyIds, 0, length);
        }
        aiIconInLXState.copy(target.aiIconInLXState);
    }
}