package com.baidu.input.support;

import android.os.Handler;
import android.os.Looper;
import android.support.annotation.MainThread;

import com.baidu.input.support.precommit.IPreCommit;
import com.baidu.input.support.precommit.PreCommitFactory;
import com.baidu.iptcore.DutyCallback;
import com.baidu.iptcore.ImeCoreManager;
import com.baidu.iptcore.info.IptCoreCandInfo;
import com.baidu.iptcore.info.IptCoreDutyInfo;
import com.baidu.iptcore.info.IptIntentItem;
import com.baidu.iptcore.util.Config;
import com.baidu.iptcore.util.Logger;
import com.baidu.iptcore.util.MethodTracer;
import com.baidu.iptcore.util.PrintUtil;

import static com.baidu.input.support.CoreOutput.IN_CORE_THREAD;
import static com.baidu.input.support.CoreOutput.IN_MAIN_THREAD;

import org.json.JSONException;
import org.json.JSONObject;


/**
 * 默认的duty处理器。对内核DutyInfo做初步的封装和分发。<p>
 * <li>{@link InputDutyCallback}: 分发后的处理器</li>
 * 
 * Created by chendanfeng on 17/8/2.
 */
public class DefaultCoreDutyHandler implements DutyCallback {

    /**
     * Enter键的ascii值
     */
    private static final char ASCII_CODE_ENTER_1 = '\r';
    private static final char ASCII_CODE_ENTER_2 = '\n';

    /**
     * 部分事件触发的UI刷新需要延迟执行，定义这里的延迟时间
     */
    private static long uiUpdateDelayTime = 50;

    /**
     * 设置延迟时间
     */
    public static void setUiUpdateDelayTime(long delay) {
        DefaultCoreDutyHandler.uiUpdateDelayTime = delay;
    }

    /**
     * DutyInfo经过预处理后接受分发的Callback
     */
    private InputDutyCallback mDutyCallback;

    /**
     * 内核线程使用的CoreOutput
     */
    private final CoreOutput mCoreThreadCoreOutput = new CoreOutput(IN_CORE_THREAD);

    /**
     * 主线程Handler
     */
    private final Handler mMainThreadHandler = new Handler(Looper.getMainLooper());

    /**
     * 最近一次刷新Ui的runnable，内核线程读写
     */
    private PostUiRunnable mPostUiRunnable;

    public DefaultCoreDutyHandler(InputDutyCallback dutyCallback) {
        mDutyCallback = dutyCallback;
    }

    @Override
    public void onDutyInfo(IptCoreDutyInfo dutyInfo) {
        if (dutyInfo == null) {
            return;
        }
        mCoreThreadCoreOutput.mDutyInfo = dutyInfo;

        long a = 0;
        if (Config.enableLog()) {
            a = System.currentTimeMillis();
        }

        // 如果上一个任务是一个延迟任务，清除延迟任务。注意舍弃的任务的flashFlag需要保留，保证新的任务一定把所有状态都刷新
        long unhandledFlag = 0;
        int[] unhandledActionKeys = null;
        if (mPostUiRunnable != null && mPostUiRunnable.isDelayed) {
            if (mDutyCallback != null) {
                mDutyCallback.removeCoreUIPost();
            }
            mMainThreadHandler.removeCallbacks(mPostUiRunnable);
            unhandledFlag = mPostUiRunnable.unHandledFlag();
            unhandledActionKeys = mPostUiRunnable.unhandledActionKeyIds();
        }

        // 记录回调给上层的actionKeyIds
        int coreActionKeysLen = dutyInfo.getCoreActionKeyIdsLength();
        int[] actionKeyIds;
        if (unhandledActionKeys != null) {
            int unhandledLen = unhandledActionKeys.length;
            actionKeyIds = new int[coreActionKeysLen + unhandledLen];
            System.arraycopy(unhandledActionKeys, 0, actionKeyIds, 0, unhandledLen);
            dutyInfo.getCoreActionKeyIds(actionKeyIds, unhandledLen);
        } else {
            actionKeyIds = new int[coreActionKeysLen];
            dutyInfo.getCoreActionKeyIds(actionKeyIds, 0);
        }
        mCoreThreadCoreOutput.actionKeyIds = actionKeyIds;

        // 更新UI数据
        long flag = dutyInfo.flashFlag() | unhandledFlag;
        dutyInfo.setFlashFlag(flag);

        if ((flag & IptCoreDutyInfo.REFL_LAYOUT) > 0) {
            mCoreThreadCoreOutput.mPadId = ImeCoreManager.getPad().getPadId();
            mCoreThreadCoreOutput.mIsExternalLayoutSwitch = dutyInfo.isExternalLayoutSwitch();
        }
        if ((flag & IptCoreDutyInfo.REFL_SHOW) > 0) {
            mCoreThreadCoreOutput.mInputState.update(dutyInfo);
        }
        if ((flag & IptCoreDutyInfo.REFL_CAND) > 0) {
            mCoreThreadCoreOutput.mIptCandState.update(dutyInfo);
        }
        if ((flag & IptCoreDutyInfo.REFL_AI_CAND) > 0) {
            mCoreThreadCoreOutput.mAICandState.update(dutyInfo);
        }
        if ((flag & IptCoreDutyInfo.REFL_AI_ICON) > 0) {
            mCoreThreadCoreOutput.mAICandIconState.update(dutyInfo);
        }
        if ((flag & IptCoreDutyInfo.REFL_AI_PAD_LOADING) > 0) {
            mCoreThreadCoreOutput.mAIPadLoadingState.update(dutyInfo);
        }
        if ((flag & IptCoreDutyInfo.REFL_AI_PAD) > 0) {
            mCoreThreadCoreOutput.mAIPadDataState.update(dutyInfo);
        }

        if ((flag & IptCoreDutyInfo.REFL_AI_CORRECT_INLINE) > 0) {
            mCoreThreadCoreOutput.mAICorrectInlineState.update(dutyInfo);
        }

        if ((flag & IptCoreDutyInfo.REFL_SUG) > 0) {
            mCoreThreadCoreOutput.mSugState.update(dutyInfo);
        }
        if ((flag & IptCoreDutyInfo.REFL_LIST) > 0) {
            mCoreThreadCoreOutput.mIptListState.update(dutyInfo);
        }
        if ((flag & IptCoreDutyInfo.REFL_SUG_CARD) > 0) {
            mCoreThreadCoreOutput.mSugCardState.update(dutyInfo);
        }
        if ((flag & IptCoreDutyInfo.REFL_SUG_SELECTION) > 0) {
            mCoreThreadCoreOutput.mSugSelection = ImeCoreManager.getPad().getSugSelectedPosition();
        }
        if ((flag & IptCoreDutyInfo.REFL_SUG_CARD_SELECTION) > 0) {
            mCoreThreadCoreOutput.mSugCardSelection = ImeCoreManager.getPad().getSugCardSelectedPosition();
        }

        if ((flag & IptCoreDutyInfo.REFL_SRV_CLD_WHITE_VER) > 0) {
            mCoreThreadCoreOutput.mSrvCloudVer = ImeCoreManager.getPad().getSrvCloudWhiteVer();
        }
        if ((flag & IptCoreDutyInfo.REFL_CANDINFO) > 0) {
            mCoreThreadCoreOutput.mCandInfoState.update(dutyInfo);
        }

        if ((flag & IptCoreDutyInfo.REFL_CONTACT) > 0) {
            mCoreThreadCoreOutput.mContactInfoState.update(dutyInfo);
        }

        if ((flag & IptCoreDutyInfo.REFL_EGG) > 0) {
            mCoreThreadCoreOutput.mEggInfoState.update(dutyInfo);
        }

        if ((flag & IptCoreDutyInfo.REFL_IPTCORE_TRACE) > 0) {
            mCoreThreadCoreOutput.mCoreTraceState.update(dutyInfo);
        }

        // inline show 更新
        if ((flag & IptCoreDutyInfo.REFL_INLINE_SHOW) > 0) {
            mCoreThreadCoreOutput.mInlineShowState.update(dutyInfo);
        }
        // 生僻字cand更新
        if ((flag & IptCoreDutyInfo.REFL_RARE_CAND) > 0) {
            mCoreThreadCoreOutput.mRareCandState.update(dutyInfo);
        }

        if ((flag & IptCoreDutyInfo.REFL_COMMON_TRIGGER_WORD) > 0) {
            // 云端意图：意图推荐
            if (dutyInfo.isRefreshIntentionData()) {
                mCoreThreadCoreOutput.mIntentCandState.update(dutyInfo);
            } else if (dutyInfo.isTriggerMap()) {
                mCoreThreadCoreOutput.mMapTriggerWordInfoState.update(dutyInfo);
            } else {
                mCoreThreadCoreOutput.mTriggerWordInfoState.update(dutyInfo);
            }
        }

        // sug 广告 或者 意图app广告
        if (dutyInfo.isRefreshSugAd() || dutyInfo.isRefreshAppIntentionAd()) {
            mCoreThreadCoreOutput.mSugAdState.update(dutyInfo);
        }

        if ((flag & IptCoreDutyInfo.REFL_CLOUD_BUBBILE) > 0) {
            mCoreThreadCoreOutput.aiIconInLXState.update(dutyInfo);
        }

        if (Config.enableLog()) {
            String inputStr = mCoreThreadCoreOutput.mInputState.getInputString();
            IptCoreCandInfo firstCand = mCoreThreadCoreOutput.mIptCandState.getFirstCand();
            String firstWord = firstCand == null ? null : firstCand.uni();
            String padId = PrintUtil.padIdToString(mCoreThreadCoreOutput.mPadId);
            MethodTracer.recordDataMethod(System.currentTimeMillis() - a,
                    inputStr, firstWord, padId);
        }

        final CoreOutput mainThreadCoreOutput = new CoreOutput(IN_MAIN_THREAD);
        mainThreadCoreOutput.copy(mCoreThreadCoreOutput);

        // Post新的任务
        long delay = 0;
        if (dutyInfo.needDelayUiUpdate()) {
            delay = uiUpdateDelayTime;
        }
        if (mPostUiRunnable != null) {
            mPostUiRunnable.refreshCoreData(mainThreadCoreOutput);
        }
        mPostUiRunnable = new PostUiRunnable(mainThreadCoreOutput, delay > 0);
        if (mDutyCallback != null) {
            mDutyCallback.startCoreUIPost(delay);
        }
        mMainThreadHandler.postDelayed(mPostUiRunnable, delay);
        // 清理当前内核 duty 的actionKey数据
        mCoreThreadCoreOutput.mDutyInfo.clearCoreActionKeyIds();
    }

    @Override
    public void onHonorNotepadGetResponse(String response, int ecode) {
        if (mDutyCallback != null) {
            mDutyCallback.onHonorNotepadGetResponse(response, ecode);
        }
    }

    @MainThread
    private void setUIParams(CoreOutput coreOutput) {

        if (mDutyCallback != null) {
            if (mDutyCallback.interceptDuty(coreOutput.mDutyInfo)) {
                return;
            }
        }

        if (Config.enableLog()) {
            Logger.i("--> startDutyInfo: flashFlag=" + PrintUtil.flashFlagToString(coreOutput.mDutyInfo.flashFlag())
                    + ", action=" + PrintUtil.actionTypeToString(coreOutput.mDutyInfo.actionType()));
        }

        long flag = coreOutput.mDutyInfo.flashFlag();
        int actionType = coreOutput.mDutyInfo.actionType();

        // CoreTrace先于所有UI先刷新，因为目前CoreTrace需要获取删除前的input光标位置/删除前的框中内容
        if ((flag & IptCoreDutyInfo.REFL_IPTCORE_TRACE) > 0) {
            if (Config.enableLog()) {
                Logger.i("    refreshCoreTrace::" + coreOutput.mCoreTraceState);
            }
            if (mDutyCallback != null) {
                mDutyCallback.refreshCoreTrace(coreOutput.mCoreTraceState);
            }
        }

        // AI助聊 - 纠错的半上屏状态，要先于上屏的Action执行
        if ((flag & IptCoreDutyInfo.REFL_AI_CORRECT_INLINE) > 0) {
            if (Config.enableLog()) {
                Logger.i("    refreshAICorrectInline::" + coreOutput.mAICorrectInlineState);
            }
            if (mDutyCallback != null) {
                mDutyCallback.updateAICorrectInlineState(coreOutput.mAICorrectInlineState);
            }
        }

        // 获取预上屏策略逻辑
        IPreCommit preCommit = PreCommitFactory.getPreCommit(ImeCoreManager.getConfig().getPreCommitType());

        // 半上屏状态的更新
        // 如果有这个flag，需要先清除半上屏状态，接着完成上屏操作，再重新设置半上屏状态（如有）
        if ((flag & IptCoreDutyInfo.REFL_PRE_EXTRACT_SELECTION) > 0) {
            if (Config.enableLog()) {
                Logger.i("    refreshPreExtractSelection-before::"
                        + coreOutput.mDutyInfo.preExtractRangeBefore()
                        + "-" + coreOutput.mDutyInfo.preExtractRangeAfter()
                        + ", show=" + coreOutput.mDutyInfo.isPreExtractShow());
            }
            // finishComposing();
            preCommit.beforeAction(coreOutput.mDutyInfo, mDutyCallback);
        }

        // 处理动作
        switch (actionType) {
            case IptCoreDutyInfo.ACTTYPE_INSERT:
                submitCandWords(coreOutput.mDutyInfo.insertType(), coreOutput.mDutyInfo.insertText());
                break;
            case IptCoreDutyInfo.ACTTYPE_INSERT_CURSOR_BACKWARD:
                insertCursorBackward(coreOutput.mDutyInfo.insertText());
                break;
            case IptCoreDutyInfo.ACTTYPE_INSERT_AND_ENTER:
                insertAndEnter(coreOutput.mDutyInfo.insertType(), coreOutput.mDutyInfo.insertText());
                break;
            // 这两个action需要执行的行为是一样的，拆分出来是历史遗留的原因，可以去除
            case IptCoreDutyInfo.ACTTYPE_REPLACE_HANDWRITE:
            case IptCoreDutyInfo.ACTTYPE_REPLACE:
                // 判断是否是预上屏还是替换已经上屏的文案
                if ((flag & IptCoreDutyInfo.REFL_PRE_EXTRACT_SELECTION) > 0) {
                    preCommit.doAction(coreOutput.mDutyInfo, mDutyCallback);
                } else {
                    replaceCandWords(coreOutput.mDutyInfo.insertText(), coreOutput.mDutyInfo.replaceBefore(),
                            coreOutput.mDutyInfo.replaceAfter(), coreOutput.mDutyInfo.isReplaceKeepCursor());
                }
                break;
            case IptCoreDutyInfo.ACTTYPE_BACK:
                backspace();
                break;
            case IptCoreDutyInfo.ACTTYPE_LIST_DEFINE:
                startListDefine();
                break;
            case IptCoreDutyInfo.ACTTYPE_URL:
                jumpUrl(coreOutput.mDutyInfo.urlBuff(), coreOutput.mDutyInfo.insertText());
                break;
            case IptCoreDutyInfo.ACTTYPE_SUG_INSERT:
                actSugInsert(coreOutput.mDutyInfo.insertText());
                break;
            case IptCoreDutyInfo.ACTTYPE_SUG_CUR_APP_LINK:
                actSugCurrentAppLink(coreOutput.mDutyInfo.sugCmdBuff(), coreOutput.mDutyInfo.insertText());
                break;
            case IptCoreDutyInfo.ACTTYPE_SUG_CUR_APP_SEARCH:
                actSugCurrentAppSearch(coreOutput.mDutyInfo.sugCmdBuff(), coreOutput.mDutyInfo.insertText());
                break;
            case IptCoreDutyInfo.ACTTYPE_SUG_THIRDPARTY_APP_LINK:
                actSugThirdPartyAppLink(coreOutput.mDutyInfo.sugCmdBuff(), coreOutput.mDutyInfo.sugCmdAppBuff(),
                        coreOutput.mDutyInfo.sugData(),
                        coreOutput.mDutyInfo.insertText());
                break;
            case IptCoreDutyInfo.ACTTYPE_SUG_THIRDPARTY_APP_SEARCH:
                actSugThirdPartyAppSearch(coreOutput.mDutyInfo.sugCmdBuff(), coreOutput.mDutyInfo.sugCmdAppBuff(),
                        coreOutput.mDutyInfo.sugData(),
                        coreOutput.mDutyInfo.insertText());
                break;
            case IptCoreDutyInfo.ACTTYPE_DOWNLOAD:
                startDownload(coreOutput.mDutyInfo.sugCardTitle(), coreOutput.mDutyInfo.sugData(),
                        coreOutput.mDutyInfo.downloadBuff());
                break;
            case IptCoreDutyInfo.ACTTYPE_SUG_CUSTOM:
                actSugCustomLink(coreOutput.mDutyInfo.urlBuff(), coreOutput.mDutyInfo.insertText());
                break;
            case IptCoreDutyInfo.ACTTYPE_SUBMIT_PICTURE:
                actSubmitCloudPicture(coreOutput.mDutyInfo.urlBuff(),
                        coreOutput.mDutyInfo.insertText());
                break;
            case IptCoreDutyInfo.ACTTYPE_COMPOSING:
                composingText(coreOutput.mDutyInfo.insertText());
                break;
            case IptCoreDutyInfo.ACTTYPE_FINISH_AND_COMPOSING:
                finishAndComposingText(coreOutput.mDutyInfo.insertText());
                break;
            case IptCoreDutyInfo.ACTTYPE_FINISH_COMPOSING:
                finishComposing();
                break;
            default:
                break;
        }
        if (coreOutput.mDutyInfo.isOpenSchema()) {
            // 如果是本地触发词且有意图，需要兼容。原因是云意图暂不支持配置
            if (coreOutput.mDutyInfo.getTriggerBelongType() > IptIntentItem.TopLevelIntention.NONE.getValue()) {
                JSONObject extraInfo = new JSONObject();
                try {
                    extraInfo.put(IptIntentItem.JSON_KEY_BELONG_TYPE, coreOutput.mDutyInfo.getTriggerBelongType());
                    extraInfo.put(IptIntentItem.JSON_KEY_RECOMMENDATION_QUERY,
                            coreOutput.mDutyInfo.getRecommendationQuery());
                } catch (JSONException e) {
                    // do nothing
                }
                actOpenSchema(
                        coreOutput.mDutyInfo.getSchemaSource(),
                        coreOutput.mDutyInfo.schemaType(),
                        extraInfo.toString()
                );
            } else {
                actOpenSchema(
                        coreOutput.mDutyInfo.getSchemaSource(),
                        coreOutput.mDutyInfo.schemaType(),
                        coreOutput.mDutyInfo.schemaInfo()
                );
            }
        }

        boolean refreshKeyboard = (flag & IptCoreDutyInfo.REFL_LAYOUT) > 0;
        boolean refreshTip = (flag & IptCoreDutyInfo.REFL_TIP) > 0;
        if (refreshKeyboard && refreshTip) {
            int tipState = coreOutput.mDutyInfo.tips();
            if (mDutyCallback != null) {
                if (Config.enableLog()) {
                    Logger.i("    refreshKeyboardWithTip: padId=" + coreOutput.mPadId
                            + ", isExternalSwitch=" + coreOutput.mIsExternalLayoutSwitch
                            + ", tip=" + Integer.toHexString(tipState)
                            + "(" + PrintUtil.tipStateToString(tipState) + ")");
                }
                mDutyCallback.switchKeyboardWithTip(coreOutput.mPadId,
                        coreOutput.mIsExternalLayoutSwitch,
                        tipState);
            }
        } else if (refreshKeyboard) {
            if (mDutyCallback != null) {
                if (Config.enableLog()) {
                    Logger.i("    refreshKeyboard: padId=" + coreOutput.mPadId
                            + ", isExternalSwitch=" + coreOutput.mIsExternalLayoutSwitch);
                }
                mDutyCallback.switchKeyboard(coreOutput.mPadId, coreOutput.mIsExternalLayoutSwitch);
            }
        } else if (refreshTip) {
            int tipState = coreOutput.mDutyInfo.tips();
            if (Config.enableLog()) {
                Logger.i("    refreshTips: tip=" + Integer.toHexString(tipState) + "(" +
                        PrintUtil.tipStateToString(tipState) + ")");
            }
            if (mDutyCallback != null) {
                mDutyCallback.refreshTips(tipState);
            }
        }

        // List先于Cand刷新(符号更多面板的list选中态的定位问题）
        // AcgCand先于Cand刷新(上层目前有颜文字重排给Cand展示的逻辑）
        // Cloud先于Cand刷新(云输入需要插入到cand中)
        // Cand先于InputBar刷新。手写的半上屏结果需要体现在Input刷新逻辑中，因此isCandOn和firstCand都要先于input准备好。

        if ((flag & IptCoreDutyInfo.REFL_LIST) > 0) {
            if (Config.enableLog()) {
                Logger.i("    refreshList::" + coreOutput.mIptListState);
            }
            if (mDutyCallback != null) {
                mDutyCallback.updateList(coreOutput.mIptListState);
            }
        }

        boolean toRefreshFullCand = (flag & IptCoreDutyInfo.REFL_CAND) > 0;

        if ((flag & IptCoreDutyInfo.REFL_CAND) > 0) {
            if (Config.enableLog()) {
                Logger.i("    refreshCand::" + coreOutput.mIptCandState);
            }
            if (mDutyCallback != null) {
                mDutyCallback.updateCand(coreOutput.mIptCandState);
            }
        }

        if ((flag & IptCoreDutyInfo.REFL_SHOW) > 0) {
            if (Config.enableLog()) {
                Logger.i("    refreshInputBar::" + coreOutput.mInputState);
            }
            if (mDutyCallback != null) {
                mDutyCallback.updateInputBar(coreOutput.mInputState);
            }
        }

        if ((flag & IptCoreDutyInfo.REFL_TRACK) > 0) {
            if (Config.enableLog()) {
                Logger.i("    refreshTrack");
            }
            if (mDutyCallback != null) {
                mDutyCallback.refreshTrack();
            }
        }

        if ((flag & IptCoreDutyInfo.REFL_SUG) > 0) {
            if (Config.enableLog()) {
                Logger.i("    refreshSug::" + coreOutput.mSugState);
            }
            if (mDutyCallback != null) {
                mDutyCallback.updateSug(coreOutput.mSugState);
            }
        }

        if ((flag & IptCoreDutyInfo.REFL_AI_CAND) > 0) {
            if (Config.enableLog()) {
                Logger.i("    refreshAICand::" + coreOutput.mAICandState);
            }
            if (mDutyCallback != null) {
                mDutyCallback.updateAICandState(coreOutput.mAICandState);
            }
        }

        if ((flag & IptCoreDutyInfo.REFL_AI_ICON) > 0) {
            if (Config.enableLog()) {
                Logger.i("    refreshAIIcon::" + coreOutput.mAICandIconState);
            }
            if (mDutyCallback != null) {
                mDutyCallback.updateAICandIconState(coreOutput.mAICandIconState);
            }
        }

        // 需要保证数据先于Loading的Flag调用
        if ((flag & IptCoreDutyInfo.REFL_AI_PAD) > 0) {
            if (Config.enableLog()) {
                Logger.i("    refreshAIPad::" + coreOutput.mAIPadDataState);
            }
            if (mDutyCallback != null) {
                mDutyCallback.updateAIPadDataState(coreOutput.mAIPadDataState);
            }
        }

        if ((flag & IptCoreDutyInfo.REFL_AI_PAD_LOADING) > 0) {
            if (Config.enableLog()) {
                Logger.i("    refreshAIPadLoading::" + coreOutput.mAIPadLoadingState);
            }
            if (mDutyCallback != null) {
                mDutyCallback.updateAIPadLoadingState(coreOutput.mAIPadLoadingState);
            }
        }

        if ((flag & IptCoreDutyInfo.REFL_SRV_CLD_WHITE_VER) > 0) {
            if (Config.enableLog()) {
                Logger.i("    refreshSrvCloudWhiteVer::" + coreOutput.mSrvCloudVer);
            }
            if (mDutyCallback != null) {
                mDutyCallback.updateSrvCloudWhiteVer(coreOutput.mSrvCloudVer);
            }
        }

        if ((flag & IptCoreDutyInfo.REFL_SUG_CARD) > 0) {
            if (Config.enableLog()) {
                Logger.i("    refreshSugCard::" + coreOutput.mSugCardState);
            }
            if (mDutyCallback != null) {
                mDutyCallback.updateSugCard(coreOutput.mSugCardState);
            }
        }

        if ((flag & IptCoreDutyInfo.REFL_SUG_SELECTION) > 0) {
            if (Config.enableLog()) {
                Logger.i("    refreshSugSelection::" + coreOutput.mSugSelection);
            }
            if (mDutyCallback != null) {
                mDutyCallback.updateSugSelection(coreOutput.mSugSelection);
            }
        }

        if ((flag & IptCoreDutyInfo.REFL_SUG_CARD_SELECTION) > 0) {
            if (Config.enableLog()) {
                Logger.i("    refreshSugCardSelection::" + coreOutput.mSugCardSelection);
            }
            if (mDutyCallback != null) {
                mDutyCallback.updateSugCardSelection(coreOutput.mSugCardSelection);
            }
        }

        if ((flag & IptCoreDutyInfo.REFL_CANDINFO) > 0) {
            if (Config.enableLog()) {
                Logger.i("    refreshCandInfo::" + coreOutput.mCandInfoState);
            }
            if (mDutyCallback != null) {
                mDutyCallback.updateCandInfo(coreOutput.mCandInfoState);
            }
        }

        if ((flag & IptCoreDutyInfo.REFL_CONTACT) > 0) {
            if (Config.enableLog()) {
                Logger.i("    refreshContactInfo::" + coreOutput.mContactInfoState);
            }
            if (mDutyCallback != null) {
                mDutyCallback.updateContactInfo(coreOutput.mContactInfoState);
            }
        }

        if ((flag & IptCoreDutyInfo.REFL_EGG) > 0) {
            if (Config.enableLog()) {
                Logger.i("    refreshEgg::" + coreOutput.mEggInfoState);
            }
            if (mDutyCallback != null) {
                mDutyCallback.updateEggInfo(coreOutput.mEggInfoState);
            }
        }

        if ((flag & IptCoreDutyInfo.REFL_PRE_EXTRACT_SELECTION) > 0) {
            if (Config.enableLog()) {
                Logger.i("    refreshPreExtractSelection::"
                        + coreOutput.mDutyInfo.preExtractRangeBefore()
                        + "-" + coreOutput.mDutyInfo.preExtractRangeAfter()
                        + ", show=" + coreOutput.mDutyInfo.isPreExtractShow());
            }
            // if (mDutyCallback != null) {
            //     boolean isInsert =
            //             coreOutput.mDutyInfo.actionType() == IptCoreDutyInfo.ACTTYPE_REPLACE;
            //     mDutyCallback.updatePreSelection(coreOutput.mDutyInfo.preExtractRangeBefore(),
            //             coreOutput.mDutyInfo.preExtractRangeAfter(), isInsert);
            // }
            preCommit.afterAction(coreOutput.mDutyInfo, mDutyCallback);
        }

        if ((flag & IptCoreDutyInfo.REFL_INLINE_SHOW) > 0) {
            if (Config.enableLog()) {
                Logger.i("    refreshInlineShow::" + coreOutput.mInlineShowState);
            }
            if (mDutyCallback != null) {
                mDutyCallback.updateInlineShow(coreOutput.mInlineShowState);
            }
        }

        if ((flag & IptCoreDutyInfo.REFL_COMMON_TRIGGER_WORD) > 0) {
            if (Config.enableLog()) {
                Logger.i("    refreshTriggerWordInfo::" + coreOutput.mTriggerWordInfoState);
            }
            if (mDutyCallback != null) {
                if (coreOutput.mDutyInfo.isRefreshIntentionData() || coreOutput.mDutyInfo.isRefreshMapData()) {
                    mDutyCallback.updateIntentState(coreOutput.mIntentCandState, coreOutput.mDutyInfo.isRefreshMapData());
                } else {
                    mDutyCallback.updateTriggerWordInfo(coreOutput.mTriggerWordInfoState);
                }
            }
        }

        if ((flag & IptCoreDutyInfo.REFL_IDENTITY_NUM_BEFORE) > 0) {
            if (Config.enableLog()) {
                Logger.i("    onIdentityNumRecognized");
            }
            if (mDutyCallback != null) {
                mDutyCallback.onIdentityNumRecognized();
            }
        }

        if ((flag & IptCoreDutyInfo.REFL_HW_GESTURE) > 0) {
            if (Config.enableLog()) {
                Logger.i("    refreshHwGesture::" + coreOutput.mDutyInfo.hwGesture());
            }
            if (mDutyCallback != null) {
                mDutyCallback.updateHwGesture(coreOutput.mDutyInfo.hwGesture());
            }
        }

        if ((flag & IptCoreDutyInfo.REFL_RARE_CAND) > 0) {
            if (Config.enableLog()) {
                Logger.i("    refreshRareCand::" + coreOutput.mRareCandState);
            }
            if (mDutyCallback != null) {
                mDutyCallback.updateRareCandState(coreOutput.mRareCandState);
            }
        }

        // 刷新sug广告
        if (coreOutput.mDutyInfo.isRefreshSugAd() || coreOutput.mDutyInfo.isRefreshAppIntentionAd()) {
            if (Config.enableLog()) {
                Logger.i("    refreshCoreSugAd:: trigger:" + coreOutput.mSugAdState.getAdTrigger()
                        + ":" + coreOutput.mSugAdState);
            }
            if (mDutyCallback != null) {
                mDutyCallback.updateCoreSugAd(coreOutput.mSugAdState);
            }
        }

        if ((flag & IptCoreDutyInfo.REFL_CLOUD_BUBBILE) > 0) {
            if (Config.enableLog()) {
                Logger.i("    refreshCloudBubble::" + coreOutput.aiIconInLXState);
            }
            if (mDutyCallback != null) {
                mDutyCallback.updateAIIconInLianXState(coreOutput.aiIconInLXState);
            }
        }

        // 每次有dutyInfo都需要刷新softUI
        if (mDutyCallback != null) {
            mDutyCallback.updateKeyboardUI(coreOutput.mDutyInfo, coreOutput.actionKeyIds);
        }

        if (Config.enableLog()) {
            Logger.i("<-- endDutyInfo");
        }
    }

    /**
     * 替换英文的预上屏文本
     * @param insertText 实际上屏的内容
     */
    private void replaceCandWords(String insertText, int replaceBefore, int replaceAfter, boolean isReplaceKeepCursor) {
        if (Config.enableLog()) {
            Logger.i("    replaceCandWords:: " + insertText + ", replaceBefore=" + replaceBefore
                    + ", replaceAfter=" + replaceAfter + ", keep=" + isReplaceKeepCursor);
        }
        if (mDutyCallback != null) {
            mDutyCallback.replaceCandWords(insertText, replaceBefore, replaceAfter, isReplaceKeepCursor);
        }
    }

    /**
     * 处理退格消息
     */
    private void backspace() {
        if (Config.enableLog()) {
            Logger.i("    backspace");
        }
        if (mDutyCallback != null) {
            mDutyCallback.submitBackspaceKeyEvent();
        }
    }

    /**
     * 处理List自定义
     */
    private void startListDefine() {
        if (Config.enableLog()) {
            Logger.i("    startListDefine");
        }
        if (mDutyCallback != null) {
            mDutyCallback.startListDefine();
        }
    }

    /**
     * 对应InputEventHandler的submitCandWords，执行cand候选字的上屏操作
     *
     * @param insertType 需要上屏的候选词类型
     * @param insertText 需要上屏的文本
     */
    private void submitCandWords(int insertType, String insertText) {
        if (Config.enableLog()) {
            Logger.i("    submitCandWord:: type=" + insertType + ", text=" + insertText);
        }
        if (mDutyCallback != null) {
            if (isInsertEnterKey(insertText)) {
                mDutyCallback.submitEnterKeyEvent();
            } else {
                if (insertText != null) { // null值不需要提交，空字符串允许提交
                    mDutyCallback.submitText(insertType, insertText);
                }
            }
        }
    }

    private boolean isInsertEnterKey(String insertText) {
        if (insertText != null && insertText.length() == 1) {
            char ch = insertText.charAt(0);
            return ch == ASCII_CODE_ENTER_1 || ch == ASCII_CODE_ENTER_2;
        }
        return false;
    }

    /**
     * 执行cand候选字的上屏操作，同时执行回车
     *
     * @param insertType 需要上屏的候选词类型
     * @param insertText 需要上屏的文本
     */
    private void insertAndEnter(int insertType, String insertText) {
        if (Config.enableLog()) {
            Logger.i("    insertAndEnter:: type=" + insertType + ", text=" + insertText);
        }
        if (mDutyCallback != null) {
            mDutyCallback.insertAndEnter(insertType, insertText);
        }
    }

    private void jumpUrl(String url, String word) {
        if (Config.enableLog()) {
            Logger.i("    jumpUrl::" + url + ", word=" + word);
        }
        if (mDutyCallback != null) {
            mDutyCallback.jumpUrl(url, word);
        }
    }

    private void actSubmitCloudPicture(String url, String word) {
        if (Config.enableLog()) {
            Logger.i("    submitPicture::" + url + ", word=" + word);
        }
        if (mDutyCallback != null) {
            mDutyCallback.submitCloudPicture(url, word);
        }
    }

    private void actSugInsert(String word) {
        if (Config.enableLog()) {
            Logger.i("    actSugInsert::" + word);
        }
        if (mDutyCallback != null) {
            mDutyCallback.actSugInsert(word);
        }
    }

    private void actSugCurrentAppLink(String url, String word) {
        if (Config.enableLog()) {
            Logger.i("    actSugCurrentAppLink::" + url + "," + word);
        }
        if (mDutyCallback != null) {
            mDutyCallback.actSugCurrentAppLink(url, word);
        }
    }

    private void actSugCurrentAppSearch(String url, String word) {
        if (Config.enableLog()) {
            Logger.i("    actSugCurrentAppSearch::" + url + "," + word);
        }
        if (mDutyCallback != null) {
            mDutyCallback.actSugCurrentAppSearch(url, word);
        }
    }

    private void actSugThirdPartyAppLink(String cmd, String app, String data, String word) {
        if (Config.enableLog()) {
            Logger.i("    actSugThirdPartyAppLink::" + cmd + "," + app + "," + word
                    + "," + data);
        }
        if (mDutyCallback != null) {
            mDutyCallback.actSugThirdPartyAppLink(cmd, app, data, word);
        }
    }

    private void actSugThirdPartyAppSearch(String cmd, String app, String data, String word) {
        if (Config.enableLog()) {
            Logger.i("    actSugThirdPartyAppSearch::" + cmd + "," + app + "," + word
                    + "," + data);
        }
        if (mDutyCallback != null) {
            mDutyCallback.actSugThirdPartyAppSearch(cmd, app, data, word);
        }
    }

    private void startDownload(String title, String data, String url) {
        if (Config.enableLog()) {
            Logger.i("    downlowd::" + title + ", " + data + ", " + url);
        }
        if (mDutyCallback != null) {
            mDutyCallback.startDownload(title, data, url);
        }
    }

    private void actSugCustomLink(String url, String word) {
        if (Config.enableLog()) {
            Logger.i("    actSugCustomLink::" + url + ", " + word + ", " + word);
        }
        if (mDutyCallback != null) {
            mDutyCallback.actSugCustomLink(url, word);
        }
    }

    private void insertCursorBackward(String insertText) {
        if (Config.enableLog()) {
            Logger.i("    insertCursorBackward");
        }
        if (mDutyCallback != null) {
            mDutyCallback.insertCursorBackward(insertText);
        }
    }

    private void composingText(String text) {
        if (Config.enableLog()) {
            Logger.i("    composingText::" + text);
        }
        if (text != null) { // 允许提交空字符串
            if (mDutyCallback != null) {
                mDutyCallback.submitComposingText(text);
            }
        }
    }

    private void finishAndComposingText(String insertText) {
        if (Config.enableLog()) {
            Logger.i("finishAndComposingText::" + insertText);
        }
        if (insertText != null) { // 允许提交空字符串
            if (mDutyCallback != null) {
                mDutyCallback.finishAndComposingText(insertText);
            }
        }
    }

    private void finishComposing() {
        if (Config.enableLog()) {
            Logger.i("    finishComposing");
        }
        if (mDutyCallback != null) {
            mDutyCallback.finishComposingText();
        }
    }

    private void actOpenSchema(int schemaSource, String schemaType, String schemaInfo) {
        if (Config.enableLog()) {
            Logger.i("    actOpenSchema::"
                    + " schemaType: " + schemaType
                    + " schemaInfo: " + schemaInfo);
        }
        if (mDutyCallback != null) {
            mDutyCallback.actOpenSchema(schemaSource, schemaType, schemaInfo);
        }
    }

    /**
     * 内核线程更新到Ui的Runnable任务
     */
    private class PostUiRunnable implements Runnable {

        /** 用于主线程读取的数据 */
        private CoreOutput mainThreadCoreOutput;
        /** 是否是延迟任务 */
        private final boolean isDelayed;
        /** 待处理的刷新标志 */
        private volatile long unHandledFlag;

        /** 待处理的actionKeyIds标志，标志action key id列表是否还没有处理 */
        private volatile boolean unHandledActKeyIdsFlag;

        public PostUiRunnable(CoreOutput mainThreadCoreOutput, boolean isDelayed) {
            this.mainThreadCoreOutput = mainThreadCoreOutput;
            this.isDelayed = isDelayed;
            this.unHandledFlag = mainThreadCoreOutput.mDutyInfo.flashFlag();
            this.unHandledActKeyIdsFlag = true;
        }

        public void refreshCoreData(CoreOutput data) {
            // 任务执行前可以继续刷新batch输入数据
            if (unHandledActKeyIdsFlag && data.mDutyInfo.isBatchInput()
                    && this.mainThreadCoreOutput.mDutyInfo.isBatchInput()) {
                this.mainThreadCoreOutput = data;
            }
        }

        public long unHandledFlag() {
            return unHandledFlag;
        }

        /**
         * 处理还没有回调给上层的actionKeys
         */
        public int[] unhandledActionKeyIds() {
            CoreOutput main = mainThreadCoreOutput;
            // 如果已经处理了
            if (main == null || !unHandledActKeyIdsFlag) {
                return null;
            }
            int[] mainActionKeyIds = main.actionKeyIds;
            if (mainActionKeyIds == null || mainActionKeyIds.length == 0) {
                return null;
            }
            int length = mainActionKeyIds.length;
            int[] result = new int[length];
            // 从主线程中拷贝到内核线程
            System.arraycopy(mainActionKeyIds, 0, result, 0, length);
            return result;
        }

        @Override
        public void run() {
            unHandledFlag = 0; // 任务已经执行，清除刷新标志
            unHandledActKeyIdsFlag = false;
            setUIParams(mainThreadCoreOutput);
        }
    }
}
