package com.baidu.input.support;

import com.baidu.input.support.state.AICandIconState;
import com.baidu.input.support.state.AICandState;
import com.baidu.input.support.state.AICorrectInlineState;
import com.baidu.input.support.state.AIIconInLXState;
import com.baidu.input.support.state.AIPadDataState;
import com.baidu.input.support.state.AIPadLoadingState;
import com.baidu.input.support.state.CandInfoState;
import com.baidu.input.support.state.ContactInfoState;
import com.baidu.input.support.state.CoreTraceState;
import com.baidu.input.support.state.EggInfoState;
import com.baidu.input.support.state.InlineShowState;
import com.baidu.input.support.state.InputState;
import com.baidu.input.support.state.IntentState;
import com.baidu.input.support.state.IptCandState;
import com.baidu.input.support.state.IptListState;
import com.baidu.input.support.state.RareCandState;
import com.baidu.input.support.state.SugAdState;
import com.baidu.input.support.state.SugCardState;
import com.baidu.input.support.state.SugState;
import com.baidu.input.support.state.TriggerWordsInfoState;
import com.baidu.iptcore.info.IptCoreCandInfo;
import com.baidu.iptcore.info.IptCoreDutyInfo;
import com.baidu.input.support.state.MapTriggerWordsInfoState;

/**
 * 内核传递的DutyInfo的处理器分发的事件的回调类
 * 
 * Created by cdf on 17/8/3.
 */
public interface InputDutyCallback {

    /**
     * 是否拦截对应duty
     * @param dutyInfo 对应的duty
     * @return 返回true表示拦截了，不需要后续处理
     */
    boolean interceptDuty(IptCoreDutyInfo dutyInfo);

    /**
     * 更新候选条的显示
     *
     * @param iptCandState 候选字数据模型
     */
    void updateCand(IptCandState iptCandState);

    /**
     * 更新input区的展示
     *
     * @param inputState input区域状态
     */
    void updateInputBar(InputState inputState);

    /**
     * 更新list区域的显示
     *
     * @param iptListState list数据模型
     */
    void updateList(IptListState iptListState);

    /**
     * 切换面板同时改变tip状态
     * @param padId 面板id
     * @param isExternalSwitch 是否是外部驱动的面板切换
     * @param tipState tip状态
     */
    void switchKeyboardWithTip(int padId, boolean isExternalSwitch, int tipState);

    /**
     * 根据给定的padId切换面板
     * @param padId {@link com.baidu.iptcore.ImeCoreConsts#PAD_EN26}等
     * @param isExternalSwitch 是否是外部驱动的面板切换
     */
    void switchKeyboard(int padId, boolean isExternalSwitch);

    /**
     * 刷新tip状态
     * @param tipState tip状态
     */
    void refreshTips(int tipState);

    /**
     * 清除手写轨迹
     */
    void refreshTrack();

    /**
     * 提交退格事件
     */
    void submitBackspaceKeyEvent();

    /**
     * 提交enter事件
     */
    void submitEnterKeyEvent();

    /**
     * 处理list自定义
     */
    void startListDefine();

    /**
     * 上屏候选词
     * @param insertType 待上屏的候选词类型
     * @param insertText 待上屏的字符
     */
    void submitText(int insertType, String insertText);

    /**
     * 上屏候选词，并且执行回车
     * @param insertType 待上屏的候选词类型
     * @param insertText 待上屏的字符
     */
    void insertAndEnter(int insertType, String insertText);

    /**
     * 提交半上屏字符
     * @param text 待半上屏的字符
     */
    void submitComposingText(String text);

    /**
     * 清除半上屏状态
     */
    void finishComposingText();

    /**
     * 执行运营活动的动作
     * @param schemaSource 来源
     * @param schemaType    动作类型
     * @param schemaInfo    动作附加信息
     */
    void actOpenSchema(int schemaSource, String schemaType, String schemaInfo);

    /**
     * 结束半上屏并重新半上屏字符
     * @param insertText 半上屏的字符
     */
    void finishAndComposingText(String insertText);

    /**
     * 更新智能云词卡片的Loading状态
     */
    void updateAIPadLoadingState(AIPadLoadingState aiPadLoadingState);

    /**
     * 更新智能云词卡片数据
     */
    void updateAIPadDataState(AIPadDataState aiPadDataState);

    /**
     * 更新智能云词次cand的Loading状态
     */
    void updateAICandIconState(AICandIconState aiCandIconState);

    /**
     * 更新智能云词次cand的数据
     */
    void updateAICandState(AICandState aiCandState);

    /**
     * 更新智能云词纠错的半上屏状态
     */
    void updateAICorrectInlineState(AICorrectInlineState aiCorrectInlineState);

    /**
     * 更新Sug的状态
     */
    void updateSug(SugState sugState);

    /**
     * 更新sug卡片的状态
     */
    void updateSugCard(SugCardState sugCardState);

    /**
     * 更新候选词详细信息弹框
     * @param candInfoState 候选词详细信息的封装
     */
    void updateCandInfo(CandInfoState candInfoState);

    /**
     * 更新联系人信息弹框
     * @param contactInfoState 联系人详细信息的封装
     */
    void updateContactInfo(ContactInfoState contactInfoState);

    /**
     * 刷新云输入版本号
     * @param cloudVer 云白名单的版本号
     */
    void updateSrvCloudWhiteVer(int cloudVer);

    /**
     * 更新彩蛋信息
     * @param eggInfo 彩蛋信息
     */
    void updateEggInfo(EggInfoState eggInfo);

    /**
     * 更新预上屏范围
     * @param cursorBefore 光标前的长度
     * @param cursorAfter 光标后的长度
     * @param isInsert 是否是来自输入的预上屏输入
     */
    void updatePreSelection(int cursorBefore, int cursorAfter, boolean isInsert);

    /**
     * 刷新sug的选中位置
     */
    void updateSugSelection(int sugSelection);

    /**
     * 刷新sug卡片的选中位置
     */
    void updateSugCardSelection(int sugCardSelection);

    /**
     * 刷新面板的UI
     */
    void updateKeyboardUI();

    /**
     * 刷新面板的UI
     */
    default void updateKeyboardUI(IptCoreDutyInfo dutyInfo, int[] actionKeyIds) {
        updateKeyboardUI();
    }

    /**
     * 跳转URL
     * @param url 跳转链接
     * @param word 上屏文本
     */
    void jumpUrl(String url, String word);

    /**
     * 云输入图片上屏
     * @param url 图片本身的url
     * @param word 配置的文本
     */
    void submitCloudPicture(String url, String word);

    /**
     * 开始下载
     * @param title sug卡片的标题
     * @param data sug的数据
     * @param url 下载链接
     */
    void startDownload(String title, String data, String url);

    /**
     * 执行光标后退
     */
    void insertCursorBackward(String insertText);

    /**
     * 替换英文的预上屏文本
     * @param insertText 实际上屏的内容
     */
    void replaceCandWords(String insertText, int replaceBefore, int replaceAfter, boolean isReplaceKeepCursor);

    /**
     * sug动作：上屏
     * @param word 上屏文本
     */
    void actSugInsert(String word);

    /**
     * sug动作：在当前app打开链接
     * @param url 链接
     * @param word sug文本
     */
    void actSugCurrentAppLink(String url, String word);

    /**
     * sug动作：在当前app执行搜索
     * @param url 链接
     * @param word sug文本
     */
    void actSugCurrentAppSearch(String url, String word);

    /**
     * sug动作：第三方app打开链接
     * @param cmd 命令
     * @param app 第三方app
     * @param data 打开app的intent的参数
     * @param word 文本
     */
    void actSugThirdPartyAppLink(String cmd, String app, String data, String word);

    /**
     * 第三方app执行搜索
     * @param cmd 命令
     * @param app 第三方app
     * @param data 打开app的intent的参数
     * @param word 文本
     */
    void actSugThirdPartyAppSearch(String cmd, String app, String data, String word);

    /**
     * 执行SUG特殊动作的打开URL
     * @param url 链接
     * @param word sug文本
     */
    void actSugCustomLink(String url, String word);

    /**
     * 刷新内核轨迹数据，用于上层记录
     * @param traceState 轨迹数据内容
     */
    void refreshCoreTrace(CoreTraceState traceState);

    /**
     * 更新inlineShow (目前只有VIVO用到，没有改需求的可以空实现)
     * @param state state
     */
    void updateInlineShow(InlineShowState state);

    /**
     * 更新触发词卡片浮层
     * @param triggerWordInfoState 联系人详细信息的封装
     */
    void updateTriggerWordInfo(TriggerWordsInfoState triggerWordInfoState);

    /**
     * 展示地图触发词卡片浮层，
     * 已经废弃，不会再回调，会在 updateIntentState 中回调
     */
    @Deprecated
    void updateMapTriggerWordInfo(MapTriggerWordsInfoState triggerWordInfoState, boolean isRefreshMapData);

    /**
     * 内核抛到UI线程回调，用于线程切换耗时监听
     * @param delayMillis 延迟时间
     */
    default void startCoreUIPost(long delayMillis) {
    }

    /**
     * 内核取消UI线程回调，用于线程切换耗时监听
     */
    default void removeCoreUIPost() {
    }

    /**
     * 更新手写笔势
     * @param hwGesture 手写笔势
     * @see com.baidu.iptcore.ImeCoreConsts.HwGestureType#TYPE_NONE
     * @see com.baidu.iptcore.ImeCoreConsts.HwGestureType#TYPE_SPLIT
     * @see com.baidu.iptcore.ImeCoreConsts.HwGestureType#TYPE_CHOOSE
     * @see com.baidu.iptcore.ImeCoreConsts.HwGestureType#TYPE_DELETE
     */
    default void updateHwGesture(int hwGesture) {

    }

    /**
     * 更新生僻字 (目前只有OPPO用到，没有改需求的可以空实现)
     * @param state state
     */
    default void updateRareCandState(RareCandState state) {
    }
    /**
     * 更新内核sug广告
     * @param state state
     */
    default void updateCoreSugAd(SugAdState state) {
    }

    /**
     * 云端意图：更新意图推荐 (目前只有OPPO和主线用到，没有改需求的可以空实现)
     * @param state state
     */
    default void updateIntentState(IntentState state, boolean isRefreshMapData) {
    }

    /**
     * 在联想词上展示超会写icon
     */
    default void updateAIIconInLianXState(AIIconInLXState state) {

    }


    default void onHonorNotepadGetResponse(String response, int ecode) {

    }

    /**
     * 识别到身份证号码了
     */
    default void onIdentityNumRecognized() {

    }
}
