package com.baidu.input.support.precommit;

import android.support.annotation.IntDef;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;

import com.baidu.input.support.InputDutyCallback;
import com.baidu.iptcore.info.IptCoreDutyInfo;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;


/**
 * 预上屏策略接口
 */
public interface IPreCommit {

    @IntDef({TYPE_REPLACE_CAND_TEXT, TYPE_SET_COMPOSING_TEXT})
    @Retention(RetentionPolicy.SOURCE)
    @interface PreCommitType {
    }

    /**
     * 使用 replaceCandWord 的方式预上屏 (默认)
     * <p>
     * （就是会经历 finishComposing -> deleteSurroundingText -> commitText -> setComposingRegion）完成预上屏
     *
     * @see ReplaceCandWordsPreCommit
     */
    int TYPE_REPLACE_CAND_TEXT = 1;

    /**
     * 使用 setComposingText() 方式预上屏
     *
     * @see SetComposingTextPreCommit
     */
    int TYPE_SET_COMPOSING_TEXT = 2;

    /**
     * 预上屏action执行前
     *
     * @param dutyInfo 内核返回的数据
     * @param callback 回调
     */
    void beforeAction(@NonNull IptCoreDutyInfo dutyInfo, @Nullable InputDutyCallback callback);

    /**
     * 预上屏action执行时
     *
     * @param dutyInfo 内核返回的数据
     * @param callback 回调
     */
    void doAction(@NonNull IptCoreDutyInfo dutyInfo, @Nullable InputDutyCallback callback);

    /**
     * 预上屏action执行后
     *
     * @param dutyInfo 内核返回的数据
     * @param callback 回调
     */
    void afterAction(@NonNull IptCoreDutyInfo dutyInfo, @Nullable InputDutyCallback callback);

}
