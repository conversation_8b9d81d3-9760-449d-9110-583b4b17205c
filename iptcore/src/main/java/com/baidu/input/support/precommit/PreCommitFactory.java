package com.baidu.input.support.precommit;

import android.support.annotation.NonNull;

import java.util.HashMap;
import java.util.Map;

/**
 * 预上屏策略工厂类
 */
public class PreCommitFactory {

    private static final Map<Integer, IPreCommit> PRE_COMMIT_MAP = new HashMap<>();

    /**
     * 获取预上屏策略类
     *
     * @param type 预上屏策略类型
     * @return 预上屏策略类
     */
    @NonNull
    public static IPreCommit getPreCommit(@IPreCommit.PreCommitType int type) {
        IPreCommit preCommit = PRE_COMMIT_MAP.get(type);
        if (preCommit == null) {
            // noinspection SwitchStatementWithTooFewBranches
            switch (type) {
                case IPreCommit.TYPE_SET_COMPOSING_TEXT:
                    preCommit = new SetComposingTextPreCommit();
                    break;
                default:
                    preCommit = new ReplaceCandWordsPreCommit();
                    break;
            }
            PRE_COMMIT_MAP.put(type, preCommit);
        }
        return preCommit;
    }
}
