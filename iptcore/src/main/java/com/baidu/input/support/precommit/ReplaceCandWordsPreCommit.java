package com.baidu.input.support.precommit;

import android.support.annotation.NonNull;
import android.support.annotation.Nullable;

import com.baidu.input.support.InputDutyCallback;
import com.baidu.iptcore.info.IptCoreDutyInfo;
import com.baidu.iptcore.util.Config;
import com.baidu.iptcore.util.Logger;

/**
 * 使用 replaceCandWord 的方式预上屏 (默认方式)
 * <p>
 * （就是会经历 finishComposing -> deleteSurroundingText -> commitText -> setComposingRegion）完成预上屏
 */
public class ReplaceCandWordsPreCommit implements IPreCommit {
    @Override
    public void beforeAction(@NonNull IptCoreDutyInfo dutyInfo, @Nullable InputDutyCallback callback) {
        if (callback != null) {
            if (Config.enableLog()) {
                Logger.i("    finishComposing");
            }
            callback.finishComposingText();
        }
    }

    @Override
    public void doAction(@NonNull IptCoreDutyInfo dutyInfo, @Nullable InputDutyCallback callback) {
        replaceCandWords(dutyInfo.insertText(), dutyInfo.replaceBefore(),
                dutyInfo.replaceAfter(), dutyInfo.isReplaceKeepCursor(), callback);
    }

    @Override
    public void afterAction(@NonNull IptCoreDutyInfo dutyInfo, @Nullable InputDutyCallback callback) {
        boolean isInsert = dutyInfo.actionType() == IptCoreDutyInfo.ACTTYPE_REPLACE;
        if (callback != null) {
            callback.updatePreSelection(dutyInfo.preExtractRangeBefore(), dutyInfo.preExtractRangeAfter(), isInsert);
        }
    }


    /**
     * 替换英文的预上屏文本
     *
     * @param insertText 实际上屏的内容
     */
    private void replaceCandWords(String insertText, int replaceBefore, int replaceAfter,
                                  boolean isReplaceKeepCursor, InputDutyCallback callback) {
        if (Config.enableLog()) {
            Logger.i("    replaceCandWords:: " + insertText + ", replaceBefore=" + replaceBefore
                    + ", replaceAfter=" + replaceAfter + ", keep=" + isReplaceKeepCursor);
        }
        if (callback != null) {
            callback.replaceCandWords(insertText, replaceBefore, replaceAfter, isReplaceKeepCursor);
        }
    }
}
