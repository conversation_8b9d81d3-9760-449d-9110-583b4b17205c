package com.baidu.input.support.precommit;

import android.support.annotation.NonNull;
import android.support.annotation.Nullable;

import com.baidu.input.support.InputDutyCallback;
import com.baidu.iptcore.info.IptCoreDutyInfo;
import com.baidu.iptcore.util.Config;
import com.baidu.iptcore.util.Logger;


/**
 * 使用setComposingText()方法预上屏
 */
public class SetComposingTextPreCommit implements IPreCommit {
    @Override
    public void beforeAction(@NonNull IptCoreDutyInfo dutyInfo, @Nullable InputDutyCallback callback) {
        // 不做任何事情
    }

    @Override
    public void doAction(@NonNull IptCoreDutyInfo dutyInfo, @Nullable InputDutyCallback callback) {
        if (callback != null) {
            String text = dutyInfo.insertText();
            if (Config.enableLog()) {
                Logger.i("    composingText::" + text);
            }
            callback.submitComposingText(text == null ? "" : text);
        }
    }

    @Override
    public void afterAction(@NonNull IptCoreDutyInfo dutyInfo, @Nullable InputDutyCallback callback) {
        if (callback != null) {
            if (Config.enableLog()) {
                Logger.i("    finishComposing");
            }
            if (dutyInfo.preExtractRangeBefore() == 0 && dutyInfo.preExtractRangeAfter() == 0) {
                callback.finishComposingText();
            }
        }
    }
}
