package com.baidu.input.support.state;

import android.support.annotation.NonNull;

import com.baidu.iptcore.ImeCoreConsts;
import com.baidu.iptcore.ImeCoreManager;
import com.baidu.iptcore.info.IptCoreDutyInfo;

/**
 * 智能云词卡片Icon的State（是否处于Loading态等)
 */
public class AICandIconState implements IState {

    /**
     * ICON的状态，参考 {@link ImeCoreConsts.AIIconState}
     */
    private int mIconState = ImeCoreConsts.AIIconState.AI_ICON_NONE;

    private AICandIconState() {

    }

    public static AICandIconState crete() {
        return new AICandIconState();
    }

    @Override
    public void update(IptCoreDutyInfo dutyInfo) {
        mIconState = ImeCoreManager.getPad().getAICandIconState();
    }

    @Override
    public boolean isEmpty() {
        return false;
    }

    /**
     * 当前是否处于Loading的状态
     */
    public boolean isLoading() {
        return mIconState == ImeCoreConsts.AIIconState.AI_ICON_LOADING;
    }

    public void copy(AICandIconState target) {
        this.mIconState = target.mIconState;
    }

    @NonNull
    @Override
    public String toString() {
        return "AICandIconState{"
                + "IconState=" + mIconState
                + '}';
    }
}
