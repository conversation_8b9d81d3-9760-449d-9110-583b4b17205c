package com.baidu.input.support.state;

import android.support.annotation.NonNull;

import com.baidu.iptcore.ImeCoreManager;
import com.baidu.iptcore.info.IptCoreCandInfo;
import com.baidu.iptcore.info.IptCoreDutyInfo;

/**
 * 智能云词卡片数据的State
 */
public class AICandState implements IState {

    /**
     * 次cand的候选词
     */
    private IptCoreCandInfo mCandInfo = null;

    private AICandState() {

    }

    public static AICandState crete() {
        return new AICandState();
    }

    @Override
    public void update(IptCoreDutyInfo dutyInfo) {
        mCandInfo = ImeCoreManager.getPad().getAICand();
    }

    @Override
    public boolean isEmpty() {
        return mCandInfo == null;
    }

    /**
     * 次cand的候选词
     */
    public IptCoreCandInfo getCandInfo() {
        return mCandInfo;
    }

    public void copy(AICandState target) {
        this.mCandInfo = target.mCandInfo;
    }

    @NonNull
    @Override
    public String toString() {
        return "AICandState{"
                + "CandInfo=" + mCandInfo
                + '}';
    }
}
