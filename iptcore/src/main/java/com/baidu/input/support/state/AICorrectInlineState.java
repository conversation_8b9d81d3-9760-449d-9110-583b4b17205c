package com.baidu.input.support.state;

import com.baidu.iptcore.ImeCoreManager;
import com.baidu.iptcore.info.IptCoreCandInfo;
import com.baidu.iptcore.info.IptCoreDutyInfo;

/**
 * 智能云词刷新AI助聊的纠错的半上屏状态的state
 */
public class AICorrectInlineState implements IState {

    /**
     * 包含纠错信息的CandInfo对象
     */
    private IptCoreCandInfo mCandInfo = null;

    private AICorrectInlineState() {

    }

    public static AICorrectInlineState crete() {
        return new AICorrectInlineState();
    }


    public IptCoreCandInfo getCorrectInfo() {
        return mCandInfo;
    }

    public void copy(AICorrectInlineState target) {
        this.mCandInfo = target.mCandInfo;
    }

    @Override
    public void update(IptCoreDutyInfo dutyInfo) {
        mCandInfo = ImeCoreManager.getPad().getAICorrectInlineInfo();
    }

    @Override
    public boolean isEmpty() {
        return mCandInfo == null;
    }

    @Override
    public String toString() {
        return "AICorrectInlineState{" +
                "mCandInfo=" + mCandInfo +
                '}';
    }
}
