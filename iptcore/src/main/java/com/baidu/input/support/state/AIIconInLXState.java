package com.baidu.input.support.state;

import android.support.annotation.NonNull;

import com.baidu.iptcore.ImeCoreManager;
import com.baidu.iptcore.info.IptCoreDutyInfo;

/**
 * 联想词上的超会写入口的显示状态
 */
public class AIIconInLXState implements IState {

    /**
     * 联想词上的超会写入口是否需要展示
     */
    private boolean shouldShow = false;

    private AIIconInLXState() {

    }

    public static AIIconInLXState crete() {
        return new AIIconInLXState();
    }

    @Override
    public void update(IptCoreDutyInfo dutyInfo) {
        shouldShow = ImeCoreManager.getPad().getAIIconInLXNeedShow();
    }

    public boolean isShouldShow() {
        return shouldShow;
    }

    @Override
    public boolean isEmpty() {
        return false;
    }

    public void copy(AIIconInLXState target) {
        this.shouldShow = target.shouldShow;
    }

    @NonNull
    @Override
    public String toString() {
        return "AIIconInLXState{"
                + "shouldShow=" + shouldShow + '}';
    }
}
