package com.baidu.input.support.state;

import android.support.annotation.NonNull;

import com.baidu.iptcore.ImeCoreConsts;
import com.baidu.iptcore.ImeCoreManager;
import com.baidu.iptcore.info.IptCoreCandInfo;
import com.baidu.iptcore.info.IptCoreDutyInfo;

import java.util.ArrayList;

/**
 * 智能云词卡片数据（除Loading状态）外的封装
 */
public class AIPadDataState implements IState {

    /**
     * 智能云词Tab页的信息，用来判断当前处于哪个tab，包括是否需要执行自动打开的逻辑
     * {@link ImeCoreConsts.AIPadType}
     */
    private int mAITabType = ImeCoreConsts.AIPadType.AI_PAD_TAB_NONE;

    /**
     * 智能云词当前Tab的状态，是否符合发起条件，以及不发起的原因等等
     */
    private int mAIPadState = ImeCoreConsts.AIPadState.AI_PAD_STAT_NORMAL;

    /**
     * 智能云词发起请求时的原始文本(Ai纠错待修改文本, 字符表情的请求文本 等)
     */
    private String mOriginText = null;

    /**
     * AI Pad是否是自动打开的
     */
    private boolean mIsPadAutoOpen = false;

    /**
     * 智能云词面板显示的候选词的内容  /趣聊/帮写/校对
     */
    private final ArrayList<IptCoreCandInfo> mCandidateWord = new ArrayList<>();

    /**
     * 智能云词面板显示的候选词的个数
     */
    private int mCandidateCount = 0;

    private AIPadDataState() {

    }

    public static AIPadDataState crete() {
        return new AIPadDataState();
    }

    @Override
    public void update(IptCoreDutyInfo dutyInfo) {
        mAITabType = ImeCoreManager.getPad().getAIPadTab();
        mAIPadState = ImeCoreManager.getPad().getAIPadState();
        mOriginText = ImeCoreManager.getPad().getAIPadOriginText();
        mIsPadAutoOpen = ImeCoreManager.getPad().getAIPabIsAutoOpen();
        // 候选词相关
        mCandidateCount = ImeCoreManager.getPad().getAIPadCnt();
        mCandidateWord.clear();
        for (int i = 0; i < mCandidateCount; ++i) {
            mCandidateWord.add(ImeCoreManager.getPad().getAIPadItem(i));
        }
    }


    @Override
    public boolean isEmpty() {
        return mCandidateCount == 0;
    }

    /**
     * 智能云词Tab页的信息，用来判断当前处于哪个tab，包括是否需要执行自动打开的逻辑
     * {@link ImeCoreConsts.AIPadType}
     */
    public int getAITabType() {
        return mAITabType;
    }

    /**
     * 智能云词当前Tab的状态，是否符合发起条件，以及不发起的原因等等
     */
    public int getAIPadState() {
        return mAIPadState;
    }

    /**
     * 智能云词发起请求时的原始文本(Ai纠错待修改文本, 字符表情的请求文本 等)
     */
    public String getOriginText() {
        return mOriginText;
    }

    /**
     * 智能云词面板显示的候选词的内容  /趣聊/帮写/校对
     */
    public IptCoreCandInfo getCandidateAt(int idx) {
        return mCandidateWord.get(idx);
    }

    /**
     * 智能云词面板显示的候选词的个数
     */
    public int getCandidateCount() {
        return mCandidateCount;
    }

    /**
     * AI PAD是否是自动打开的。自动打开不记忆tab
     */
    public boolean isAutoOpen() {
        return mIsPadAutoOpen;
    }

    public void copy(AIPadDataState target) {
        this.mAITabType = target.mAITabType;
        this.mAIPadState = target.mAIPadState;
        this.mOriginText = target.mOriginText;
        this.mIsPadAutoOpen = target.mIsPadAutoOpen;

        this.mCandidateCount = target.mCandidateCount;
        this.mCandidateWord.clear();
        this.mCandidateWord.addAll(target.mCandidateWord);
    }

    @NonNull
    @Override
    public String toString() {
        return "AIPadDataState{"
                + "TabType=" + mAITabType
                + ", PadState=" + mAIPadState
                + ", OriginText=" + mOriginText
                + ", CandidateCount=" + mCandidateCount
                + ", isAutoOpen=" + mIsPadAutoOpen
                + '}';
    }

}
