package com.baidu.input.support.state;

import android.support.annotation.NonNull;

import com.baidu.iptcore.ImeCoreManager;
import com.baidu.iptcore.info.IptCoreDutyInfo;

/**
 * 智能云词卡片Loading状态的封装
 */
public class AIPadLoadingState implements IState {

    /**
     * 是否处于Loading的状态
     */
    private boolean isLoading = false;

    private AIPadLoadingState() {

    }

    public static AIPadLoadingState crete() {
        return new AIPadLoadingState();
    }

    @Override
    public void update(IptCoreDutyInfo dutyInfo) {
        isLoading = ImeCoreManager.getPad().getAIPadLoadingState();
    }

    public boolean isLoading() {
        return isLoading;
    }

    @Override
    public boolean isEmpty() {
        return false;
    }

    public void copy(AIPadLoadingState target) {
        this.isLoading = target.isLoading;
    }

    @NonNull
    @Override
    public String toString() {
        return "AIPadLoadingState{"
                + "isLoading=" + isLoading + '}';
    }
}
