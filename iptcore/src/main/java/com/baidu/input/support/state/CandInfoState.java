package com.baidu.input.support.state;

import java.util.Arrays;

import com.baidu.iptcore.ImeCoreManager;
import com.baidu.iptcore.info.IptCoreDutyInfo;

/**
 * Cand候选字长按或点击产生的详细信息弹框所需展示的内容
 *
 * Created by chendanfeng on 2018/5/8.
 */
public class CandInfoState implements IState {

    private int count;

    private String[] items;
    private byte[] popMenuInfo;

    public static CandInfoState create() {
        return new CandInfoState();
    }

    @Override
    public void update(IptCoreDutyInfo dutyInfo) {
        count = ImeCoreManager.getPad().getCandInfoCount();
        if (count > 0) {
            items = new String[count];
            for (int i = 0; i < count; i++) {
                items[i] = ImeCoreManager.getPad().getCandInfo(i);
            }
        } else {
            items = null;
        }
        popMenuInfo = dutyInfo.popMenuInfo();
    }

    /**
     * 获得候选字详细信息的条数
     */
    public int getCandInfoCount() {
        return count;
    }

    public byte[] getMenuInfo() {
        return popMenuInfo;
    }

    /**
     * 获得候选字详细信息的说明文案
     */
    public String getCandInfo(int index) {
        if (index >= 0 && index < count) {
            return items[index];
        } else {
            return null;
        }
    }

    /**
     * 获取候选词详细信息列表
     */
    public String[] getCandInfoItems() {
        return items;
    }

    @Override
    public boolean isEmpty() {
        return count == 0;
    }

    public void copy(CandInfoState target) {
        count = target.count;
        items = target.items == null ? null : Arrays.copyOf(target.items, target.items.length);
        popMenuInfo = target.popMenuInfo;
    }

    @Override
    public String toString() {
        return "CandInfoState{" + "count=" + count + ", items=" + Arrays.toString(items) + '}';
    }
}
