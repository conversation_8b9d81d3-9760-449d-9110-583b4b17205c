package com.baidu.input.support.state;

import java.util.Arrays;

import com.baidu.iptcore.ImeCoreManager;
import com.baidu.iptcore.info.IptContactItem;
import com.baidu.iptcore.info.IptCoreDutyInfo;

/**
 * 联系人详细信息的数据封装
 * 
 * Created by <PERSON><PERSON><PERSON><PERSON> on 18/5/10.
 */
public class ContactInfoState implements IState {

    private int count;
    private IptContactItem[] items;

    public static ContactInfoState create() {
        return new ContactInfoState();
    }

    @Override
    public void update(IptCoreDutyInfo dutyInfo) {
        count = ImeCoreManager.getPad().getContactCount();
        if (count > 0) {
            items = new IptContactItem[count];
            for (int i = 0; i < count; i++) {
                items[i] = ImeCoreManager.getPad().getContactItem(i);
            }
        } else {
            items = null;
        }
    }

    /**
     * 获得联系人详细信息的条数
     */
    public int getCount() {
        return count;
    }

    /**
     * 获得联系人详细信息的列表
     */
    public IptContactItem[] getItems() {
        return items;
    }

    /**
     * 获取指定位置的联系人详细信息条目
     * @param index 索引
     * @return 联系人详细信息条目，可能为null
     */
    public IptContactItem getContactItem(int index) {
        if (index >= 0 && index < count) {
            return items[index];
        } else {
            return null;
        }
    }

    @Override
    public boolean isEmpty() {
        return count == 0;
    }

    public void copy(ContactInfoState target) {
        count = target.count;
        items = target.items == null ? null : Arrays.copyOf(target.items, target.items.length);
    }

    @Override
    public String toString() {
        return "ContactInfoState{" + "count=" + count + ", items=" + Arrays.toString(items) + '}';
    }
}
