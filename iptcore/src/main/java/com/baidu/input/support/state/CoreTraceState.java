package com.baidu.input.support.state;

import com.baidu.iptcore.ImeCoreConsts;
import com.baidu.iptcore.IptCoreInterface;
import com.baidu.iptcore.info.IptCoreDutyInfo;

public class CoreTraceState implements IState {

    private int mTraceType = ImeCoreConsts.TRACE_TYPE_NONE;

    /**
     * zid等候选词的信息
     */
    private String mWordInfo;
    /**
     * 误触数据
     */
    private String mMisTouchInfo;
    /**
     * 整句上屏的内容
     */
    private String mZjInput;
    /**
     * 轨迹记录达到阈值后的导出路径
     */
    private String mTraceExportedPath;

    public static CoreTraceState create() {
        return new CoreTraceState();
    }

    /**
     * 设置轨迹刷新类型，需要在update之前调用
     */
    public void setTraceType(int traceType) {
        mTraceType = traceType;
    }

    /**
     * 获得轨迹类型
     */
    public int getTraceType() {
        return mTraceType;
    }

    /**
     * 获取zid等候选词信息
     */
    public String getWordInfo() {
        return mWordInfo;
    }

    /**
     * 获取误触数据
     */
    public String getMisTouchInfo() {
        return mMisTouchInfo;
    }

    /**
     * 获得整句预测上屏文本
     */
    public String getZjInput() {
        return mZjInput;
    }

    /**
     * 获得轨迹导出的路径
     */
    public String getTraceExportedPath() {
        return mTraceExportedPath;
    }

    @Override
    public void update(IptCoreDutyInfo dutyInfo) {
        mTraceType = dutyInfo.traceType();
        if ((mTraceType & ImeCoreConsts.TRACE_TYPE_ZJ_INPUT) != 0) {
            mZjInput = dutyInfo.insertText();
        }
        if ((mTraceType & ImeCoreConsts.TRACE_TYPE_LOG_FILE_WARNING) != 0) {
            mTraceExportedPath = IptCoreInterface.get().getTraceLog();
        }
        if ((mTraceType & ImeCoreConsts.TRACE_TYPE_WORD_INFO) != 0) {
            mWordInfo = IptCoreInterface.get().exportWordInfoForTrace();
        }
        if ((mTraceType & ImeCoreConsts.TRACE_TYPE_MIS_TOUCHINFO) != 0) {
            mMisTouchInfo = IptCoreInterface.get().exportMisTouchInfo();
        }
    }

    @Override
    public boolean isEmpty() {
        return mTraceType == ImeCoreConsts.TRACE_TYPE_NONE;
    }

    public void copy(CoreTraceState target) {
        mTraceType = target.mTraceType;
        mZjInput = target.mZjInput;
        mTraceExportedPath = target.mTraceExportedPath;
        mMisTouchInfo = target.mMisTouchInfo;
        mWordInfo = target.mWordInfo;
    }

    @Override
    public String toString() {
        return "{" +
                "traceType=" + mTraceType +
                ", wordInfo='" + mWordInfo + '\'' +
                ", misTouchInfo='" + mMisTouchInfo + '\'' +
                ", zjInput='" + mZjInput + '\'' +
                ", traceExportedPath='" + mTraceExportedPath + '\'' +
                '}';
    }
}
