package com.baidu.input.support.state;

import com.baidu.iptcore.ImeCoreManager;
import com.baidu.iptcore.info.IptCoreDutyInfo;

import java.util.Arrays;

/**
 * 彩蛋数据封装
 */
public class EggInfoState implements IState {

    private char[] eggInfo;

    public static EggInfoState create() {
        return new EggInfoState();
    }

    @Override
    public void update(IptCoreDutyInfo dutyInfo) {
        eggInfo = ImeCoreManager.getPad().getEgg();
    }

    /**
     * 获得彩蛋数据
     */
    public char[] eggInfo() {
        return eggInfo;
    }

    @Override
    public boolean isEmpty() {
        return eggInfo == null;
    }

    public void copy(EggInfoState eggInfoState) {
        eggInfo = eggInfoState.eggInfo == null ? null : Arrays.copyOf(eggInfoState.eggInfo,
                eggInfoState.eggInfo.length);
    }
}
