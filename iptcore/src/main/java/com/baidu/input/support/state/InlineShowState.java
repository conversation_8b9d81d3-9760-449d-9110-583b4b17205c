package com.baidu.input.support.state;

import com.baidu.iptcore.ImeCoreManager;
import com.baidu.iptcore.info.IptCoreDutyInfo;

/**
 * inline半上屏状态的state
 * 见vivo需求
 * https://console.cloud.baidu-int.com/devops/icafe/issue/OEMinput-5524/show
 */
public class InlineShowState implements IState {

    /**
     * inline信息
     */
    private String inline = null;

    private InlineShowState() {

    }

    public static InlineShowState crete() {
        return new InlineShowState();
    }

    public String getInline() {
        return inline;
    }

    public void copy(InlineShowState target) {
        this.inline = target.inline;
    }

    @Override
    public void update(IptCoreDutyInfo dutyInfo) {
        inline = ImeCoreManager.getPad().getInlineShow();
    }

    @Override
    public boolean isEmpty() {
        return inline == null || inline.isEmpty();
    }

    @Override
    public String toString() {
        return "InlineShowState{" +
                "inline=" + inline +
                '}';
    }
}
