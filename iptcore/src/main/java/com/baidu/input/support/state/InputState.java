/*
 * Copyright (C) 2017 Baidu, Inc. All Rights Reserved.
 */
package com.baidu.input.support.state;

import java.util.ArrayList;
import java.util.List;

import com.baidu.iptcore.ImeCoreConsts;
import com.baidu.iptcore.ImeCoreManager;
import com.baidu.iptcore.info.IptCoreDutyInfo;
import com.baidu.iptcore.info.IptCoreShowInfo;

import android.text.TextUtils;
import android.util.Pair;

/**
 * 封装inputbar的数据
 * Created by ch<PERSON><PERSON><PERSON> on 17/4/10.
 */
public class InputState implements IState {

    private String committedNoPopStr;
    private String committedPopStr;
    private String committedListStr;
    private String unmatchedInputStr;
    private String committedStr;
    private byte[] hotLetterInfos;

    /**
     * 获取一个新实例（create or obtain）
     */
    public static InputState create() {
        return new InputState();
    }
    
    /**
     * input区的span类型：已经提交的汉字
     */
    public static final int SPAN_TYPE_COMMIT_HZ = 0;
    /**
     * input区的span类型：已经提交的list
     */
    public static final int SPAN_TYPE_COMMIT_LIST = 1;
    /**
     * input区的span类型：剩余的普通input文本
     */
    public static final int SPAN_TYPE_UNCOMMIT_INPUT = 2;
    /**
     * input区的span类型：提交的汉字中可以回退的部分
     */
    public static final int SPAN_TYPE_POP_REGION = 3;
    /**
     * input区的显示文字
     */
    private final IptCoreShowInfo mInputShow = new IptCoreShowInfo();
    
    /**
     * 更新input的显示
     */
    @Override
    public void update(IptCoreDutyInfo dutyInfo) {
        mInputShow.reset();
        boolean success = ImeCoreManager.getPad().getInputShow(mInputShow);
        if (success) {
            splitInputInfo();
            getHotLettersInfoCore();
        }
    }

    private void getHotLettersInfoCore() {
        boolean isEmpty = TextUtils.isEmpty(mInputShow.uni());
        if (!isEmpty
                && ImeCoreManager.getConfig().getNeedPyMask()
                && ImeCoreManager.getPad().getCurrentPadId() == ImeCoreConsts.PAD_PY26) {
            hotLetterInfos = new byte[ImeCoreConsts.HOTLETTER_INFO_LENS];
            ImeCoreManager.getPad().getPyHotLetter(hotLetterInfos);
        } else {
            hotLetterInfos = null;
        }
    }

    @Deprecated
    public byte[] getHotLetter() {
        return hotLetterInfos;
    }

    /**
     * 获得input区域全部的展示文本
     * @return input区域全部展示文本
     */
    public String getInputString() {
        return mInputShow.uni();
    }

    /**
     * 获取input光标前的字符串
     */
    public String getInputStringBeforeCursor() {
        int cursorIdx = getCurrentCursorIdxInString();
        String input = getInputString();
        int len = input.length();
        if (TextUtils.isEmpty(input) || cursorIdx <= 0) {
            return "";
        } else if (cursorIdx == len) {
            return input;
        } else {
            cursorIdx = cursorIdx > len ? len : cursorIdx;
            return input.substring(0, cursorIdx);
        }
    }
    
    /**
     * 获得光标位置
     */
    public int getCursorIdx() {
        return mInputShow.cursorIdx();
    }

    /**
     * 获得当前光标在输入码中的位置
     */
    public int getCurrentCursorIdxInString() {
        return getCursorIdxInString(getCursorIdx());
    }

    /**
     * 获得指定光标在输入码中的位置
     */
    public int getCursorIdxInString(int cursorIdx) {
        int commitHzLength = getCommitHzLength();
        if (cursorIdx < 0) {
            return commitHzLength;
        }

        if (cursorIdx < mInputShow.cursorInfoLen()) {
            return mInputShow.cursorInfo()[cursorIdx] + commitHzLength;
        } else {
            return getInputLength();
        }
    }

    /**
     * 获得光标的最大位置
     */
    public int getMaxCursorIdx() {
        return mInputShow.cursorInfoLen() - 1;
    }

    /**
     * 获取输入码长度
     * @return 输入码长度
     */
    public int getInputLength() {
        String inputString = getInputString();
        return TextUtils.isEmpty(inputString) ? 0 : inputString.length();
    }

    public int getCommitHzLength() {
        return mInputShow.hzLen();
    }

    public String getCommitHz() {
        return committedStr;
    }

    public int getCommitListLength() {
        return mInputShow.listLen();
    }

    public String getCommitList() {
        return committedListStr;
    }

    public int getUnmatchedInputLength() {
        return mInputShow.inputLen();
    }

    public String getUnmatchedInput() {
        return unmatchedInputStr;
    }

    public int getPopBegan() {
        return mInputShow.popBegan();
    }

    public int getPopEnd() {
        return mInputShow.popEnd();
    }

    public byte[] getAutofixInfo() {
        return mInputShow.autofixInfo();
    }

    public int getAutofixInfoLen() {
        return mInputShow.autofixInfoLen();
    }

    @Override
    public String toString() {
        return "InputState{" + mInputShow + '}';
    }

    /**
     * 工具方法。将当前input区字符切分成不同的部分
     * @param inputState input区数据
     * @return 分好段的input区的字符串。每段是一个type:Integer和字符串组成的pair对。
     */
    public static List<Pair<Integer, String>> splitInputInfoString(InputState inputState) {
        if (inputState == null) {
            return null;
        }
        IptCoreShowInfo showInfo = inputState.mInputShow;
        List<Pair<Integer, String>> spannable = new ArrayList<>();
        String inputShow = inputState.getInputString();
        if (!TextUtils.isEmpty(inputShow)) {
            int nowIdx = 0;
            int len = showInfo.hzLen();
            if (len > 0) {

                int popBegin = showInfo.popBegan();
                int popEnd = showInfo.popEnd();
                if (popBegin >= nowIdx && popBegin < popEnd && popEnd == len) {
                    String committedNoPop = inputShow.substring(nowIdx, nowIdx + popBegin);
                    String popStr = inputShow.substring(nowIdx + popBegin, nowIdx + len);
                    if (!TextUtils.isEmpty(committedNoPop)) {
                        spannable.add(new Pair<>(SPAN_TYPE_COMMIT_HZ, committedNoPop));
                    }
                    spannable.add(new Pair<>(SPAN_TYPE_POP_REGION, popStr));
                } else {
                    String committed = inputShow.substring(nowIdx, nowIdx + len);
                    spannable.add(new Pair<>(SPAN_TYPE_COMMIT_HZ, committed));
                }
                nowIdx += len;
            }
            len = showInfo.listLen();
            if (len > 0) {
                String listStr = inputShow.substring(nowIdx, nowIdx + len);
                spannable.add(new Pair<>(SPAN_TYPE_COMMIT_LIST, listStr));
                nowIdx += len;
            }
            len = showInfo.inputLen();
            if (len > 0) {
                String inputStr = inputShow.substring(nowIdx, nowIdx + len);
                spannable.add(new Pair<>(SPAN_TYPE_UNCOMMIT_INPUT, inputStr));
            }
        }
        return spannable;
    }

    private void splitInputInfo() {
        clearSplitInputInfo();

        IptCoreShowInfo showInfo = mInputShow;
        String inputShow = getInputString();
        if (!TextUtils.isEmpty(inputShow)) {
            int nowIdx = 0;
            int len = showInfo.hzLen();
            if (len > 0) {

                int popBegin = showInfo.popBegan();
                int popEnd = showInfo.popEnd();
                if (popBegin >= nowIdx && popBegin < popEnd && popEnd == len) {
                    committedNoPopStr = inputShow.substring(nowIdx, nowIdx + popBegin);
                    committedPopStr = inputShow.substring(nowIdx + popBegin, nowIdx + len);
                    committedStr = inputShow.substring(nowIdx, nowIdx + len);
                } else {
                    committedNoPopStr = inputShow.substring(nowIdx, nowIdx + len);
                    committedPopStr = null;
                    committedStr = committedNoPopStr;
                }
                nowIdx += len;
            }
            len = showInfo.listLen();
            int endIndex = nowIdx + len;
            int length = inputShow.length();
            if (len > 0 && nowIdx < length && endIndex < length) {
                committedListStr = inputShow.substring(nowIdx, endIndex);
            } else {
                committedListStr = null;
            }
            len = showInfo.inputLen();
            endIndex = nowIdx + len;
            if (len > 0 && nowIdx < length && endIndex < length) {
                unmatchedInputStr = inputShow.substring(nowIdx, endIndex);
            } else {
                unmatchedInputStr = null;
            }
        }
    }

    private void clearSplitInputInfo() {
        committedNoPopStr = null;
        committedPopStr = null;
        committedListStr = null;
        unmatchedInputStr = null;
        committedStr = null;
    }

    @Override
    public boolean isEmpty() {
        return !TextUtils.isEmpty(mInputShow.uni());
    }

    public void copy(InputState target) {
        committedNoPopStr = target.committedNoPopStr;
        committedPopStr = target.committedPopStr;
        committedListStr = target.committedListStr;
        unmatchedInputStr = target.unmatchedInputStr;
        committedStr = target.committedStr;
        mInputShow.copy(target.mInputShow);
        hotLetterInfos = target.hotLetterInfos;
    }
}
