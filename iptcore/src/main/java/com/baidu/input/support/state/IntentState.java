package com.baidu.input.support.state;


import com.baidu.iptcore.ImeCoreManager;
import com.baidu.iptcore.info.IptCoreDutyInfo;
import com.baidu.iptcore.info.IptIntentItem;

import java.util.ArrayList;

/**
 * 意图推荐卡片的 State
 */
public class IntentState implements IState {


    /**
     * 意图卡片
     */
    private final ArrayList<IptIntentItem> mCandidateWord = new ArrayList<>();

    /**
     * 意图个数
     */
    private int mCandidateCount = 0;

    public static IntentState crete() {
        return new IntentState();
    }


    @Override
    public void update(IptCoreDutyInfo dutyInfo) {
        mCandidateCount = ImeCoreManager.getPad().getCloudIntentionCount();
        mCandidateWord.clear();
        for (int i = 0; i < mCandidateCount; ++i) {
            mCandidateWord.add(ImeCoreManager.getPad().getCloudIntentionItem(i));
        }
    }

    @Override
    public boolean isEmpty() {
        return mCandidateCount == 0;
    }

    public void copy(IntentState target) {
        this.mCandidateCount = target.mCandidateCount;
        this.mCandidateWord.clear();
        this.mCandidateWord.addAll(target.mCandidateWord);
    }

    public ArrayList<IptIntentItem> getCandidateWord() {
        return mCandidateWord;
    }

    public int getCandidateCount() {
        return mCandidateCount;
    }
}
