/*
 * Copyright (C) 2017 Baidu, Inc. All Rights Reserved.
 */
package com.baidu.input.support.state;

import com.baidu.input.support.CoreOutput;
import com.baidu.iptcore.ImeCoreManager;
import com.baidu.iptcore.IptCoreInterface;
import com.baidu.iptcore.info.IptCoreCandInfo;
import com.baidu.iptcore.info.IptCoreDutyInfo;

import android.support.annotation.Nullable;

/**
 * 封装Cand候选字的数据模型
 * Created by ch<PERSON><PERSON><PERSON> on 17/4/10.
 */
public class IptCandState implements IState {

    private static final int CACHED_NUM = 17;

    /**
     * 前面多少个候选词经NNRank模型排序
     */
    private static final int NNRANK_CHECK_CNT = 5;

    /**
     * 未使用NNrank，包括未下载模型，或者是模型未安装成功
     */
    public static final int NNRANK_NO = 0;
    /**
     * NNrank对候选词重排序了
     */
    public static final int NNRANK_SORTED = 1;
    /**
     * NNrank对候选词处理了但是没有重排序
     */
    public static final int NNRANK_UNSORTED = 2;

    /**
     * 内核线程使用的candState
     */
    private static IptCandState sCoreThreadCandState = new IptCandState();

    /**
     * 缓存0-CACHE_NUM个候选字的数组
     */
    private IptCoreCandInfo[] mCachedCandList = new IptCoreCandInfo[CACHED_NUM];
    /**
     * 缓存候选字的第0个对应的索引号
     */
    private int mCachedCandStartOffset = 0;
    /**
     * 首选cand单独保存
     */
    private IptCoreCandInfo mFirstCandInfo = null;
    /**
     * 当前的cand数量
     */
    private int mCandNum = 0;
    /**
     * 当前cand对应的nnrank状态
     */
    private NnrankInfo mNnRankInfo = new NnrankInfo();

    /**
     * 获取一个新实例
     */
    public static IptCandState create(int type) {
        if (type == CoreOutput.IN_CORE_THREAD) {
            return sCoreThreadCandState;
        }
        return new IptCandState();
    }

    /**
     * 更新Cand区域数据
     */
    @Override
    public void update(IptCoreDutyInfo dutyInfo) {
        mCandNum = ImeCoreManager.getPad().getCandCount();

        // 加载缓存的候选词数组
        loadCands( 0, CACHED_NUM);
        mFirstCandInfo = mCachedCandList[0];

        // nnrank
        checkNnrankState(dutyInfo);
    }

    private void checkNnrankState(IptCoreDutyInfo dutyInfo) {
        int nnrankState;
        boolean isLx = (dutyInfo.tips() & IptCoreDutyInfo.TIP_CN_LIAN) != 0;
        if (isLx || !ImeCoreManager.getLib().isNnrankerInstalled()) {
            nnrankState = NNRANK_NO;
        } else {
            nnrankState = NNRANK_UNSORTED;
            int cnt = Math.min(mCandNum, NNRANK_CHECK_CNT);
            for (int i = 0; i < cnt; i++) {
                IptCoreCandInfo cand = getCandAt(i);
                if (cand != null && cand.flag() == IptCoreCandInfo.CANDFLAG_NNRANK) {
                    nnrankState = NNRANK_SORTED;
                }
            }
        }

        int[] intArgs = new int[2];
        IptCoreInterface.get().getNnrankerInfo(intArgs);
        mNnRankInfo.set(nnrankState, intArgs[0], intArgs[1]);
    }

    /**
     * 返回当前候选字的总个数
     * @return 当前候选字总个数
     */
    public int getCandCount() {
        return mCandNum;
    }

    /**
     * 获取指定索引的Cand
     * @param index 候选词索引
     * @return 候选词信息
     */
    public IptCoreCandInfo getCandAt(int index) {
        if (index < 0 || index >= mCandNum) {
            return null;
        }
        if (index >= mCachedCandStartOffset && index < mCachedCandStartOffset + mCachedCandList.length) {
            int cacheIndex = index - mCachedCandStartOffset;
            return mCachedCandList[cacheIndex];
        } else {
            loadCands(index, CACHED_NUM);
            return mCachedCandList[0];
        }
    }

    /**
     * 获取当前首个首选词
     */
    public IptCoreCandInfo getFirstCand() {
        return mFirstCandInfo;
    }

    /**
     * 根据指定的offset，加载指定长度的候选字，存放到指定数组中
     * @param offset 结果数组要取的首个cand的偏移
     * @param length 取词长度（不能少于数组长度）
     */
    private void loadCands(int offset, int length) {

        if (length < 0 || offset < 0) {
            return;
        }
        int leftLen = getCandCount() - offset;
        length = Math.min(length, leftLen);
        if (length > 0) {
            ImeCoreManager.getPad().loadCandFromCore(offset, length, (result, offset1) -> {
                // run at CoreThread
                sCoreThreadCandState.setLoadedCand(result, offset1);
                if (IptCandState.this != sCoreThreadCandState) {
                    IptCandState.this.copy(sCoreThreadCandState);
                }
            });
        } else {
            setLoadedCand(null, 0);
        }
    }

    /**
     * 获取当前cand的nnrank 状态
     * @return {@link #NNRANK_NO}等
     */
    public int getNnRankState() {
        return mNnRankInfo.state;
    }

    /**
     * 获取当前NNrank调序后的cand数量
     */
    public int getNnRankCandNum() {
        return mNnRankInfo.candNum;
    }

    /**
     * 获取当前nnrank的耗时
     */
    public int getNnRankTime() {
        return mNnRankInfo.time;
    }

    /**
     * 将当前缓存的候选词，设置为新的结果
     * @param result 新的缓存候选词
     * @param offset 缓存候选词的首个对应的起始index
     */
    private void setLoadedCand(@Nullable IptCoreCandInfo[] result, int offset) {
        mCachedCandStartOffset = offset;

        int len = 0;
        if (result != null && result.length > 0) {
            len = Math.min(result.length, mCachedCandList.length);
            System.arraycopy(result, 0, mCachedCandList, 0, len);
        }

        // 剩余的部分置空
        if (len < mCachedCandList.length) {
            for (int i = len; i < mCachedCandList.length; i++) {
                mCachedCandList[i] = null;
            }
        }
    }

    @Override
    public boolean isEmpty() {
        return mCandNum == 0;
    }

    public void copy(IptCandState target) {
        mCandNum = target.mCandNum;
        mFirstCandInfo = target.mFirstCandInfo;
        mCachedCandStartOffset = target.mCachedCandStartOffset;
        mNnRankInfo.copy(target.mNnRankInfo);
        System.arraycopy(target.mCachedCandList, 0, mCachedCandList, 0, target.mCachedCandList.length);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("IptCandState{candNum=").append(mCandNum).append(", cands=[");
        int count = Math.min(5, mCandNum);
        for (int i = 0; i < count; i++) {
            sb.append(getCandAt(i));
            sb.append(",");
        }
        sb.append("]}");
        return sb.toString();
    }

    /**
     * NNrank的相关信息
     */
    private static class NnrankInfo {
        /**
         * 此次候选查询的nnrank状态：未启用/启用后未排序/发生排序
         */
        private int state = NNRANK_NO;
        /**
         * 发生排序的cand数量
         */
        private int candNum;
        /**
         * 排序的耗时
         */
        private int time;

        public void set(int state, int candNum, int time) {
            this.state = state;
            this.candNum = candNum;
            this.time = time;
        }

        public void copy(NnrankInfo target) {
            this.state = target.state;
            this.candNum = target.candNum;
            this.time = target.time;
        }
    }
}
