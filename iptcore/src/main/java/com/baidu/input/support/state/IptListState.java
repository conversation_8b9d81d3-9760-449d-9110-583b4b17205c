/*
 * Copyright (C) 2017 Baidu, Inc. All Rights Reserved.
 */
package com.baidu.input.support.state;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.baidu.iptcore.ImeCoreConsts;
import com.baidu.iptcore.ImeCoreManager;
import com.baidu.iptcore.info.IptCoreDutyInfo;
import com.baidu.iptcore.info.IptCoreListInfo;

/**
 * List的数据模型
 * Created by ch<PERSON><PERSON><PERSON> on 17/4/14.
 */
public class IptListState implements IState {

    /**
     * 是否有“自定义”项
     */
    private boolean hasEditCell;
    /**
     * 是否是默认导入的符号list
     */
    private boolean isDefaultSymList;
    /**
     * 当前选中的list的索引。如果没有，则是-1
     */
    private int listFilterIndex = -1;

    /**
     * 更多候选词面板需要显示的tabs
     */
    private byte[] mMoreCandTabs;

    /**
     * 获取一个新实例（create or obtain）
     */
    public static IptListState create() {
        return new IptListState();
    }
    
    /**
     * list区域数据的数量
     */
    private int mListCount;

    /**
     * list的item
     */
    private List<IptCoreListInfo> mListItems = new ArrayList<>();

    /**
     * list的筛选类型,{@link ImeCoreConsts#LIST_FILTER_TYPE_TYPE_NONE}
     */
    private int mListFilterType = -1;
    
    @Override
    public void update(IptCoreDutyInfo dutyInfo) {
        mListCount = ImeCoreManager.getPad().getListCount();
        mListItems.clear();
        for (int i = 0; i < mListCount; i++) {
            IptCoreListInfo listInfo = ImeCoreManager.getPad().getListItem(i);
            mListItems.add(listInfo);
        }

        // update list properties
        IptCoreListInfo info;
        boolean hasEditCell = false;
        boolean isDefList = false;
        int listFilterIndex = -1;
        for (int i = 0; i < mListCount; i++) {
            info = mListItems.get(i);
            if (info != null) {
                if ((info.listType() & ImeCoreConsts.CHECKED_LIST_ITEM) != 0) {
                    listFilterIndex = i;
                }

                if ((info.listType() & ImeCoreConsts.DEFINE_LIST_ITEM) != 0) {
                    hasEditCell = true;
                } else if ((info.listType() & ImeCoreConsts.SYM_LIST_ITEM) != 0) {
                    isDefList = true;
                }
            }
        }
        this.hasEditCell = hasEditCell;
        this.isDefaultSymList = isDefList;
        this.listFilterIndex = listFilterIndex;

        // 更新更多候选词面板中的tab数据
        if ((dutyInfo.flashFlag() & IptCoreDutyInfo.REFL_MORECAND_TAB) > 0) {
            this.mMoreCandTabs = ImeCoreManager.getPad().getTabs();
            mListFilterType = dutyInfo.listFilterType();
        } else {
            mListFilterType = -1;
        }
    }

    /**
     * 获得当前的list项的个数
     */
    public int getListCnt() {
        return mListCount;
    }

    /**
     * list列表中是否有“自定义”项
     */
    public boolean hasEditCell() {
        return hasEditCell;
    }

    /**
     * 当前是否展示的是默认符号列表
     */
    public boolean isDefaultSymList() {
        return isDefaultSymList;
    }

    /**
     * 获得当前list列表中，处于筛选态的索引
     * @return 如果有筛选态则返回索引，无筛选项返回-1
     */
    public int getListFilterIndex() {
        return listFilterIndex;
    }

    /**
     * 获取指定索引的list项信息
     */
    public IptCoreListInfo getListAt(int idx) {
        if (mListItems == null || idx < 0 || idx > mListItems.size()) {
            return null;
        }
        return mListItems.get(idx);
    }

    /**
     * 获取更多候选词面板的tabs
     */
    public byte[] getMoreCandTabs() {
        return mMoreCandTabs;
    }

    /**
     * 获取list筛选类型
     * @return
     */
    public int getListFilterType() {
        return mListFilterType;
    }

    @Override
    public boolean isEmpty() {
        return mListCount == 0;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("IptListState{[cnt=");
        sb.append(mListCount);
        sb.append(",isDef=").append(isDefaultSymList);
        sb.append(",lockIdx=").append(listFilterIndex);
        sb.append(",tabs=").append(Arrays.toString(mMoreCandTabs));
        sb.append("][");
        for (IptCoreListInfo item : mListItems) {
            sb.append(item);
            sb.append(",");
        }
        sb.append("]");
        sb.append(",filterType:" + mListFilterType);
        sb.append("]}");
        return sb.toString();
    }

    public void copy(IptListState target) {
        hasEditCell = target.hasEditCell;
        isDefaultSymList = target.isDefaultSymList;
        listFilterIndex = target.listFilterIndex;
        mListCount = target.mListCount;
        mListItems.clear();
        mListItems.addAll(target.mListItems);
        mMoreCandTabs = target.mMoreCandTabs;
        mListFilterType = target.mListFilterType;
    }
}
