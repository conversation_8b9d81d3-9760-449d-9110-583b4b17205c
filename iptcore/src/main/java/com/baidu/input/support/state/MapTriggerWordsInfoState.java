package com.baidu.input.support.state;

import com.baidu.iptcore.ImeCoreManager;
import com.baidu.iptcore.info.IptCoreDutyInfo;
import com.baidu.iptcore.info.IptMapTriggerWordItem;

import java.util.Arrays;

public class MapTriggerWordsInfoState implements IState {
    private int count;
    private IptMapTriggerWordItem[] items;

    public static MapTriggerWordsInfoState create() {
        return new MapTriggerWordsInfoState();
    }

    @Override
    public void update(IptCoreDutyInfo dutyInfo) {
        count = ImeCoreManager.getPad().getMapTriggerWordCount();
        if (count > 0) {
            items = new IptMapTriggerWordItem[count];
            for (int i = 0; i < count; i++) {
                items[i] = ImeCoreManager.getPad().getMapTriggerWordItemItem(i);
            }
        } else {
            items = null;
        }
    }

    /**
     * 获得卡片详细信息的条数
     */
    public int getCount() {
        return count;
    }

    /**
     * 获得卡片信息列表
     */
    public IptMapTriggerWordItem[] getItems() {
        return items;
    }

    /**
     * 获取指定位置的卡片详细信息
     * @param index 索引
     * @return 联系人详细信息条目，可能为null
     */
    public IptMapTriggerWordItem getTriggerWordItem(int index) {
        if (index >= 0 && index < count) {
            return items[index];
        } else {
            return null;
        }
    }

    @Override
    public boolean isEmpty() {
        return count == 0;
    }

    public void copy(MapTriggerWordsInfoState target) {
        count = target.count;
        items = target.items == null ? null : Arrays.copyOf(target.items, target.items.length);
    }

    @Override
    public String toString() {
        return "MapTriggerWordsInfoState{" + "count=" + count + ", items=" + Arrays.toString(items) + '}';
    }
}