/*
 * Copyright (C) 2017 Baidu, Inc. All Rights Reserved.
 */
package com.baidu.input.support.state;

import android.support.annotation.Nullable;

import com.baidu.input.support.CoreOutput;
import com.baidu.iptcore.ImeCoreManager;
import com.baidu.iptcore.info.IptCoreCandInfo;
import com.baidu.iptcore.info.IptCoreDutyInfo;

/**
 * 封装生僻字Cand候选字的数据模型
 */
public class RareCandState implements IState {

    private static final int CACHED_NUM = 8;

    /**
     * 内核线程使用的candState
     */
    private static RareCandState sCoreThreadCandState = new RareCandState();

    /**
     * 缓存0-CACHE_NUM个候选字的数组
     */
    private IptCoreCandInfo[] mCachedCandList = new IptCoreCandInfo[CACHED_NUM];
    /**
     * 缓存候选字的第0个对应的索引号
     */
    private int mCachedCandStartOffset = 0;
    /**
     * 当前的cand数量
     */
    private int mCandNum = 0;

    /**
     * 获取一个新实例
     */
    public static RareCandState create(int type) {
        if (type == CoreOutput.IN_CORE_THREAD) {
            return sCoreThreadCandState;
        }
        return new RareCandState();
    }

    /**
     * 更新Cand区域数据
     */
    @Override
    public void update(IptCoreDutyInfo dutyInfo) {
        mCandNum = ImeCoreManager.getPad().getRareCandCount();
        // 加载缓存的候选词数组
        loadCands(0, CACHED_NUM);
    }

    /**
     * 返回当前候选字的总个数
     *
     * @return 当前候选字总个数
     */
    public int getCandCount() {
        return mCandNum;
    }

    /**
     * 获取指定索引的Cand
     *
     * @param index 候选词索引
     * @return 候选词信息
     */
    public IptCoreCandInfo getCandAt(int index) {
        if (index < 0 || index >= mCandNum) {
            return null;
        }
        if (index >= mCachedCandStartOffset && index < mCachedCandStartOffset + mCachedCandList.length) {
            int cacheIndex = index - mCachedCandStartOffset;
            return mCachedCandList[cacheIndex];
        } else {
            loadCands(index, CACHED_NUM);
            return mCachedCandList[0];
        }
    }

    /**
     * 根据指定的offset，加载指定长度的候选字，存放到指定数组中
     *
     * @param offset 结果数组要取的首个cand的偏移
     * @param length 取词长度（不能少于数组长度）
     */
    private void loadCands(int offset, int length) {
        if (length < 0 || offset < 0) {
            return;
        }
        int leftLen = getCandCount() - offset;
        length = Math.min(length, leftLen);
        if (length > 0) {
            ImeCoreManager.getPad().loadRareCandFromCore(offset, length, (result, offset1) -> {
                // run at CoreThread
                sCoreThreadCandState.setLoadedCand(result, offset1);
                if (RareCandState.this != sCoreThreadCandState) {
                    RareCandState.this.copy(sCoreThreadCandState);
                }
            });
        } else {
            setLoadedCand(null, 0);
        }
    }

    /**
     * 将当前缓存的候选词，设置为新的结果
     *
     * @param result 新的缓存候选词
     * @param offset 缓存候选词的首个对应的起始index
     */
    private void setLoadedCand(@Nullable IptCoreCandInfo[] result, int offset) {
        mCachedCandStartOffset = offset;

        int len = 0;
        if (result != null && result.length > 0) {
            len = Math.min(result.length, mCachedCandList.length);
            System.arraycopy(result, 0, mCachedCandList, 0, len);
        }

        // 剩余的部分置空
        if (len < mCachedCandList.length) {
            for (int i = len; i < mCachedCandList.length; i++) {
                mCachedCandList[i] = null;
            }
        }
    }

    @Override
    public boolean isEmpty() {
        return mCandNum == 0;
    }

    public void copy(RareCandState target) {
        mCandNum = target.mCandNum;
        mCachedCandStartOffset = target.mCachedCandStartOffset;
        System.arraycopy(target.mCachedCandList, 0, mCachedCandList, 0, target.mCachedCandList.length);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("RareCandState{num=").append(mCandNum).append(", items=[");
        int count = Math.min(5, mCandNum);
        for (int i = 0; i < count; i++) {
            sb.append(getCandAt(i));
            sb.append(",");
        }
        sb.append("]}");
        return sb.toString();
    }
}
