package com.baidu.input.support.state;

import com.baidu.iptcore.ImeCoreManager;
import com.baidu.iptcore.info.IptCoreDutyInfo;
import com.baidu.iptcore.info.IptCoreSugAdInfo;

import java.util.ArrayList;
import java.util.List;

/**
 * Sug广告的数据封装
 * Created by lwt
 */
public class SugAdState implements IState {
    private AppAdTrigger adTrigger = AppAdTrigger.Sug;

    private int mSugAdCount;
    private final List<IptCoreSugAdInfo> mSugAdItems = new ArrayList<>();

    public static SugAdState create() {
        return new SugAdState();
    }
    
    @Override
    public void update(IptCoreDutyInfo dutyInfo) {
        if (dutyInfo.isRefreshAppIntentionAd()) {
            adTrigger = AppAdTrigger.Intention;
        } else {
            adTrigger = AppAdTrigger.Sug;
        }
        mSugAdCount = ImeCoreManager.getPad().getCoreSugAdCount(adTrigger);
        if (mSugAdCount > 0) {
            mSugAdItems.clear();
            for (int i = 0; i < mSugAdCount; i++) {
                mSugAdItems.add(ImeCoreManager.getPad().getCoreSugAdAt(i, adTrigger));
            }
        } else {
            mSugAdItems.clear();
        }
    }

    public AppAdTrigger getAdTrigger() {
        return adTrigger;
    }

    @Override
    public boolean isEmpty() {
        return mSugAdCount <= 0;
    }

    public void copy(SugAdState target) {
        mSugAdCount = target.mSugAdCount;
        mSugAdItems.clear();
        mSugAdItems.addAll(target.mSugAdItems);
        adTrigger = target.adTrigger;
    }

    public int getCount() {
        return mSugAdCount;
    }

    public IptCoreSugAdInfo getItem(int idx) {
        if (idx >= 0 && idx < mSugAdItems.size()) {
            return mSugAdItems.get(idx);
        }
        return null;
    }

    @Override
    public String toString() {
        return "SugAdState{" + "count=" + mSugAdCount + ", items=" + mSugAdItems + '}';
    }
}
