package com.baidu.input.support.state;

import java.util.ArrayList;
import java.util.List;

import com.baidu.iptcore.ImeCoreConsts;
import com.baidu.iptcore.ImeCoreManager;
import com.baidu.iptcore.info.IptCoreDutyInfo;
import com.baidu.iptcore.info.IptCoreSugCardInfo;

/**
 * Sug卡片的数据封装
 * Created by cdf on 18/4/16.
 */
public class SugCardState implements IState {

    private int mSugCardCount;
    private List<IptCoreSugCardInfo> mSugCardItems = new ArrayList<>();
    private int mSugCardSelectedPos = -1;
    private String mSugCardSourceMsg;
    private int mSugCardType;

    public static SugCardState create() {
        return new SugCardState();
    }
    
    @Override
    public void update(IptCoreDutyInfo dutyInfo) {
        mSugCardCount = ImeCoreManager.getPad().getSugCardCount();
        if (mSugCardCount > 0) {
            mSugCardSelectedPos = ImeCoreManager.getPad().getSugCardSelectedPosition();
            mSugCardItems.clear();
            for (int i = 0; i < mSugCardCount; i++) {
                mSugCardItems.add(ImeCoreManager.getPad().getSugCardAt(i));
            }
            mSugCardType = ImeCoreManager.getPad().getSugType();
            mSugCardSourceMsg = ImeCoreManager.getPad().getSugSourceMsg();
        } else {
            mSugCardSelectedPos = -1;
            if (mSugCardItems.size() > 0) {
                mSugCardItems.clear();
            }
            mSugCardType = ImeCoreConsts.SUG_CARD_TYPE_NONE;
            mSugCardSourceMsg = null;
        }
    }

    @Override
    public boolean isEmpty() {
        return mSugCardCount == 0;
    }

    public void copy(SugCardState target) {
        mSugCardCount = target.mSugCardCount;
        mSugCardSelectedPos = target.mSugCardSelectedPos;
        mSugCardItems.clear();
        mSugCardItems.addAll(target.mSugCardItems);
        mSugCardType = target.mSugCardType;
        mSugCardSourceMsg = target.mSugCardSourceMsg;
    }

    public int getCount() {
        return mSugCardCount;
    }

    public IptCoreSugCardInfo getItem(int idx) {
        if (idx >= 0 && idx < mSugCardItems.size()) {
            return mSugCardItems.get(idx);
        }
        return null;
    }

    public int getSelectedPosition() {
        return mSugCardSelectedPos;
    }

    public int getSugCardType() {
        return mSugCardType;
    }

    public String getSugSourceMsg() {
        return mSugCardSourceMsg;
    }

    @Override
    public String toString() {
        return "SugCardState{" + "count=" + mSugCardCount + ", items=" + mSugCardItems + '}';
    }
}
