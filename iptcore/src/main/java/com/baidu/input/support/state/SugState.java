package com.baidu.input.support.state;

import com.baidu.iptcore.ImeCoreConsts;
import com.baidu.iptcore.ImeCoreManager;
import com.baidu.iptcore.info.IptCoreCandInfo;
import com.baidu.iptcore.info.IptCoreDutyInfo;

import java.util.ArrayList;
import java.util.List;

/**
 * sug的数据封装
 * Created by cdf on 18/4/16.
 */
public class SugState implements IState {

    /** sug数量 */
    private int mSugCount;
    /** sug信息 */
    private List<IptCoreCandInfo> mSugItems = new ArrayList<>();
    /** sug的选中位置 */
    private int mSugSelectedPos = -1;
    /** sug的状态 */
    private int mSugState;
    /** sug的动作类型 */
    private int mSugActionType;
    /** sug的源id */
    private int mSugSourceId;

    /** sug的配置id */
    private int mSugId;

    public static SugState create() {
        return new SugState();
    }
    
    @Override
    public void update(IptCoreDutyInfo dutyInfo) {
        mSugCount = ImeCoreManager.getPad().getSugCount();
        if (mSugCount > 0) {
            mSugSelectedPos = ImeCoreManager.getPad().getSugSelectedPosition();
            mSugState = ImeCoreManager.getPad().getSugState();
            mSugActionType = ImeCoreManager.getPad().getSugActionType();
            mSugSourceId = ImeCoreManager.getPad().getSugSourceId();
            mSugId = ImeCoreManager.getPad().getSugId();
            mSugItems.clear();
            for (int i = 0; i < mSugCount; i++) {
                IptCoreCandInfo sug = ImeCoreManager.getPad().getSugAt(i);
                mSugItems.add(sug);
                IptCoreCandInfo.SugAdStyle sugAdStyle = sug.getSugAdStyle();
                if (sugAdStyle != null) {
                    sugAdStyle.adActionType = ImeCoreManager.getPad().getSugActionType(i);
                    sugAdStyle.globalId = ImeCoreManager.getPad().getSugAdGlobalId(i);
                }
            }
        } else {
            mSugSelectedPos = -1;
            mSugState = ImeCoreConsts.PAD_SUG_STATE_NONE;
            if (mSugItems.size() > 0) {
                mSugItems.clear();
            }
        }
    }

    public int getCount() {
        return mSugCount;
    }

    public IptCoreCandInfo getSugItem(int idx) {
        if (idx >= 0 && idx < mSugItems.size()) {
            return mSugItems.get(idx);
        }
        return null;
    }

    public int getSelectedPos() {
        return mSugSelectedPos;
    }

    public int getSugState() {
        return mSugState;
    }

    public int getSugSourceId() {
        return mSugSourceId;
    }

    public int getSugId() {
        return mSugId;
    }

    public int getSugActionType() {
        return mSugActionType;
    }

    @Override
    public boolean isEmpty() {
        return mSugCount == 0;
    }

    public void copy(SugState target) {
        mSugCount = target.mSugCount;
        mSugState = target.mSugState;
        mSugSelectedPos = target.mSugSelectedPos;
        mSugSourceId = target.mSugSourceId;
        mSugActionType = target.mSugActionType;
        mSugId = target.mSugId;
        mSugItems.clear();
        mSugItems.addAll(target.mSugItems);
    }

    @Override
    public String toString() {
        return "SugState{"
                + "cnt=" + mSugCount
                + ", sugId=" + mSugId
                + ", state=" + mSugState
                + ", selectPos=" + mSugSelectedPos
                + ", items=" + mSugItems
                + '}';
    }
}
