package com.baidu.input.support.state;

import com.baidu.iptcore.ImeCoreManager;
import com.baidu.iptcore.info.IptTriggerWordItem;
import com.baidu.iptcore.info.IptCoreDutyInfo;

import java.util.Arrays;

public class TriggerWordsInfoState implements IState {
    private int count;
    private IptTriggerWordItem[] items;
    private long id;

    public static TriggerWordsInfoState create() {
        return new TriggerWordsInfoState();
    }

    @Override
    public void update(IptCoreDutyInfo dutyInfo) {
        count = ImeCoreManager.getPad().getTriggerWordCount();
        if (count > 0) {
            id = ImeCoreManager.getPad().getTriggerWordItemsId();
            items = new IptTriggerWordItem[count];
            for (int i = 0; i < count; i++) {
                items[i] = ImeCoreManager.getPad().getTriggerWordItemItem(i);
            }
        } else {
            items = null;
        }
    }

    /**
     * 获得卡片详细信息的条数
     */
    public int getCount() {
        return count;
    }

    /**
     * 获得卡片信息列表
     */
    public IptTriggerWordItem[] getItems() {
        return items;
    }

    /**
     * 获得配置ID
     * @return
     */
    public long getId() {
        return id;
    }

    /**
     * 获取指定位置的卡片详细信息
     * @param index 索引
     * @return 联系人详细信息条目，可能为null
     */
    public IptTriggerWordItem getContactItem(int index) {
        if (index >= 0 && index < count) {
            return items[index];
        } else {
            return null;
        }
    }

    @Override
    public boolean isEmpty() {
        return count == 0;
    }

    public void copy(TriggerWordsInfoState target) {
        count = target.count;
        items = target.items == null ? null : Arrays.copyOf(target.items, target.items.length);
        id = target.id;
    }

    @Override
    public String toString() {
        return "BrandExposureInfoState{" + "count=" + count + ", items=" + Arrays.toString(items) + ", id=" + id + '}';
    }
}
