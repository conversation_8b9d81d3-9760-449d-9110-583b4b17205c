package com.baidu.iptcore;

/**
 * 设置接口的Key
 */
public class ConfigKey {

    /**
     * 不要给这个变量加上final
     * 这样做会导致其他组件在依赖内核组件时，预编译阶段把key替换为常量。
     */
    private static int keyStart = 0;

    /** boolean key */
    /** 手写半上屏开关 */
    public static final int HW_PRE_EXTRACT_MODE = keyStart;
    /** 自动纠错 */
    public static final int AUTOFIX = HW_PRE_EXTRACT_MODE + 1;
    /** 中英混输 */
    public static final int CNEN = AUTOFIX + 1;
    /** 繁体 */
    public static final int FANTI = CNEN + 1;
    /** 二次元 */
    public static final int ACGN = FANTI + 1;

    /** 联想预测 */
    public static final int CURSOR_LIAN = ACGN + 1;
    /** emoji候选 */
    public static final int EMOJI = CURSOR_LIAN + 1;
    /** emoji联想 */
    public static final int EMOJI_LIAN = EMOJI + 1;
    /** 双拼 */
    public static final int SHUANGPIN = EMOJI_LIAN + 1;
    /** 自动保存 */
    public static final int AUTOSAVE = SHUANGPIN + 1;

    /** 符号联想 */
    public static final int SYLIAN = AUTOSAVE + 1;
    /** 模糊输入 */
    public static final int MOHU = SYLIAN + 1;
    /** 更多候选里是否需要有list类型筛选 */
    public static final int ENABLE_PAD_MORE_TABS = MOHU + 1;
    /** 笔画字形优先开关状态 */
    public static final int BH_FIRST = ENABLE_PAD_MORE_TABS + 1;
    /** 快速输入 */
    public static final int FASTINPUT = BH_FIRST + 1;

    /** 五笔提示开关 */
    public static final int WBTIP = FASTINPUT + 1;
    /** 五笔拼音混输 */
    public static final int WBPY = WBTIP + 1;
    /** 五笔 自造词记忆开关 */
    public static final int WB_USE_USERDICT = WBPY + 1;
    /** 五笔 个性化短语开关 */
    public static final int WB_ONLY_STANDARD = WB_USE_USERDICT + 1;
    /** 五笔 智能调频开关 */
    public static final int WB_FREQ_ADJUST = WB_ONLY_STANDARD + 1;

    /** 五笔 Z键通配开关 */
    public static final int WB_Z_COMMON = WB_FREQ_ADJUST + 1;
    /** 个性短语开关 */
    public static final int PHRASE = WB_Z_COMMON + 1;
    /** 英文排序方式 */
    public static final int ENABLE_HW_LIST = PHRASE + 1;
    /** 隐私模式 */
    public static final int PRIVATE_MODE = ENABLE_HW_LIST + 1;

    /** 双拼输入转换开关 */
    public static final int SHUANGPIN_NOCVT = PRIVATE_MODE + 1;
    /** 空格上屏联想词选项状态  false-空格清空联想词  true-空格上屏联想词首选 */
    public static final int SPACE_LIAN = SHUANGPIN_NOCVT + 1;
    /** AI助聊的总开关是否打开。该变量会影响到次cand是否显示 */
    public static final int AI_CHAT_ENABLE = SPACE_LIAN + 1;
    /** 该场景下，次cand是否支持AI帮写 */
    public static final int AUTO_WRITE_CAND_ENABLE = AI_CHAT_ENABLE + 1;
    /** 该场景下，卡片是否支持AI帮写 */
    public static final int AUTO_WRITE_PAD_ENABLE = AUTO_WRITE_CAND_ENABLE + 1;

    /** 该场景（如购物场景）下，是否需要自动打开AI帮写的tab */
    public static final int AUTO_WRITE_AUTO_OPEN = AUTO_WRITE_PAD_ENABLE + 1;
    /** 该场景下，次cand是否支持AI校对 */
    public static final int TXT_ERROR_RECOVERY_CAND_ENABLE = AUTO_WRITE_AUTO_OPEN + 1;
    /** 该场景下，卡片是否支持AI校对 */
    public static final int TXT_ERROR_RECOVERY_PAD_ENABLE = TXT_ERROR_RECOVERY_CAND_ENABLE + 1;
    /** 该场景下，次cand是否支持AI趣聊 */
    public static final int FUNCHAT_CAND_ENABLE = TXT_ERROR_RECOVERY_PAD_ENABLE + 1;
    /** 该场景下，次cand是否支持AI趣聊 */

    public static final int FUNCHAT_PAD_ENABLE = FUNCHAT_CAND_ENABLE + 1;
    /** 该场景下，神句配图是否支持自动打开 */
    public static final int AI_PEITU_AUTO_OPEN = FUNCHAT_PAD_ENABLE + 1;
    /** 该场景输入前预测是否支持正负能量 */
    public static final int SENT_PRE_MODE_ENABLED = AI_PEITU_AUTO_OPEN + 1;
    /** 输入前整句开关，true表示开 */
    public static final int IS_SENT_PRE_ENABLED = SENT_PRE_MODE_ENABLED + 1;
    /** 输入中整句开关，true表示开 */
    public static final int IS_SENT_CLOUD_ENABLED = IS_SENT_PRE_ENABLED + 1;
    /** 是否保留输入后整句预测，保留的话，撰写和纠错内核不会发起了 */
    public static final int IS_SENT_HIDE_COMPOSE = IS_SENT_CLOUD_ENABLED + 1;

    /** 写轨迹到文件的开关 */
    public static final int TRACE_FLUSH = IS_SENT_HIDE_COMPOSE + 1;
    /** 是否开启收集误触信息 */
    public static final int COLLECT_MIS_INFO = TRACE_FLUSH + 1;
    /** 英文输入是否经过内核 */
    public static final int IS_ABC = COLLECT_MIS_INFO + 1;
    /** 简单云输入 */
    public static final int IS_EASYCLOUD = IS_ABC + 1;
    /** 个性化语音状态 */
    public static final int IS_VOICE_CORRECT = IS_EASYCLOUD + 1;

    /** 设置英语面板下是否句首自动大写 */
    public static final int AUTO_CAPITAL = IS_VOICE_CORRECT + 1;
    /** 设置SUG开关 */
    public static final int IS_SUGOPEN = AUTO_CAPITAL + 1;
    /** 硬键盘模式开关 */
    public static final int IS_HARD_KEYBOARD = IS_SUGOPEN + 1;
    /** 设置计算器可用 */
    public static final int ENABLE_CALC = IS_HARD_KEYBOARD + 1;
    /** 联想打点信息 */
    public static final int INPUT_ASSOCIATE_STATE = ENABLE_CALC + 1;

    /** 四码唯一时自动上屏 */
    public static final int WB_FOUR_CODE_AUTO_SEND_STATE = INPUT_ASSOCIATE_STATE + 1;
    /** 第五码将首选上屏 */
    public static final int WB_FIVE_CODE_SEND_FIRST_STATE = WB_FOUR_CODE_AUTO_SEND_STATE + 1;
    /** 次cand是否打开 */
    public static final int SUB_CAND_STATE = WB_FIVE_CODE_SEND_FIRST_STATE + 1;
    /** 数字联想是否打开 */
    public static final int NUM_LIAN = SUB_CAND_STATE + 1;
    /** 删除键联想 */
    public static final int BACKSPACE_LIAN = NUM_LIAN + 1;
    /** 是否启用人名模式。默认启用 */
    public static final int ENABLE_NAME_MODE = BACKSPACE_LIAN + 1;
    /** 英文纠错开关。纠错打开时，第二词为最优选 */
    public static final int EN_SECOND_IS_BEST = ENABLE_NAME_MODE + 1;
    /** 设置当前输入框是否具有no_suggest属性。参考OEMinput-2367第一条 */
    public static final int BOX_NO_SUGGEST = EN_SECOND_IS_BEST + 1;
    /** 设置英文面板下是否开启高级查词模式（光标查词、退格查词） */
    public static final int EN_ADVANCED_FIND_MODE = BOX_NO_SUGGEST + 1;
    /** 拼音面板shift状态变化时是否刷新面板 */
    public static final int REFRESH_KEYBOARD_WHEN_SHIFT_CHANGE_IN_CN = EN_ADVANCED_FIND_MODE + 1;
    /** 无输入码时是否忽略点击的分词键 */
    public static final int IGNORE_SPLIT_NO_INPUT = REFRESH_KEYBOARD_WHEN_SHIFT_CHANGE_IN_CN + 1;
    /** 中文面板有输入码时切换英文，是否上屏输入码 */
    public static final int COMMIT_INPUT_WHEN_SWITCH_LANGUAGE = IGNORE_SPLIT_NO_INPUT + 1;
    /** 无输入码时按shift+code，是否直接上屏大写字母 */
    public static final int COMMIT_ALPHA_WHEN_SHIFT_LONGDOWN = COMMIT_INPUT_WHEN_SWITCH_LANGUAGE + 1;
    /** inline输入码模式 */
    public static final int INLINE_SHOW = COMMIT_ALPHA_WHEN_SHIFT_LONGDOWN + 1;
    /** 手写未结束的情况下会屏蔽REFL_CAND和REFL_CAND刷新标记，屏蔽手写预上屏 */
    public static final int DISABLE_HW_CAND_BEFORE_FINISH = INLINE_SHOW + 1;
    /** 拼音面板shift状态变化时是否刷新面板 */
    public static final int REFRESH_PINYIN_SHIFT = DISABLE_HW_CAND_BEFORE_FINISH + 1;
    /** 五笔面板shift状态变化时是否刷新面板 */
    public static final int REFRESH_WUBI_SHIFT = REFRESH_PINYIN_SHIFT + 1;
    /** 设置当前是否是高情商 */
    public static final int SET_HIGH_EQ_ENABLE = REFRESH_WUBI_SHIFT + 1;
    /** 当前是否请求小模型 */
    public static final int REQUEST_MMODLE = SET_HIGH_EQ_ENABLE + 1;
    /** 当前是否展示次 Cand 最低优先级提示 */
    public static final int AICHAT_CONTENT_HINT = REQUEST_MMODLE + 1;
    /**是否将拼音九键分词修改为 1 ，默认为关闭状态 */
    public static final int TREAT_T9_SPLIT= AICHAT_CONTENT_HINT + 1;
    /**生僻字面板的生僻字带拼音（例如【犇(bēn)】）,关闭后不带拼音（例如【犇】）,默认是开启状态 */
    public static final int RARE_ZI_WITH_PINYIN= TREAT_T9_SPLIT + 1;
    /** 云端意图的开关（主线） */
    public static final int REQ_CLOUD_INTENT = RARE_ZI_WITH_PINYIN + 1;
    /** 是否是发送的场景 */
    public static final int SCENE_WITH_SEND_BOX = REQ_CLOUD_INTENT + 1;
    /** 地图云端意图的开关 */
    public static final int REQ_MAP_CLOUD_INTENT = SCENE_WITH_SEND_BOX + 1;
    /** Boolean 类型设置项的key的结束 */
    public static final int BOOL_KEY_END = REQ_MAP_CLOUD_INTENT;

    /** Int Key */
    /** 英文大小写 */
    public static final int ENCASE = BOOL_KEY_END + 1;
    /** 英文排序方式 */
    public static final int ENSORT = ENCASE + 1;
    /** 模糊输入选项 */
    public static final int MOHU_OPTION = ENSORT + 1;
    /** 个性短语位置 */
    public static final int PHRASE_POS = MOHU_OPTION + 1;
    /** 手写模式 */
    public static final int HW_TYPE = PHRASE_POS + 1;

    /** 手写识别速度 */
    public static final int HW_SPEED = HW_TYPE + 1;
    /** 键盘手写状态 */
    public static final int TRACK_TYPE = HW_SPEED + 1;
    /** 五笔编码方案 */
    public static final int WB_SCHEMA = TRACK_TYPE + 1;
    /** 联想类型 */
    public static final int LEGEND_MODE = WB_SCHEMA + 1;

    /** 手写联想类型 */
    public static final int HW_LEGEND_MODE = LEGEND_MODE + 1;
    /** 英文联想类型 */
    public static final int EN_LEGEND_MODE = HW_LEGEND_MODE + 1;
    /** 手写注音类型 */
    public static final int HW_TONE_MODE = EN_LEGEND_MODE + 1;
    /** 云输入类型 */
    public static final int CLOUD_INPUT_TYPE = HW_TONE_MODE + 1;
    /** 仓颉编码类型 */
    public static final int CJ_SCHEMA = CLOUD_INPUT_TYPE + 1;
    /** 全拼筛选状态 拼音/五笔 */
    public static final int QP_FILTER = CJ_SCHEMA + 1;

    /** 是否安装了手百 */
    public static final int EXIST_BM = QP_FILTER + 1;
    /** 手机宽度 */
    public static final int PHONE_WIDTH = EXIST_BM + 1;
    /** 手机高度 */
    public static final int PHONE_HEIGHT = PHONE_WIDTH + 1;
    /** 用于计算Icon大小的base width值 */
    public static final int ICON_SIZE = PHONE_HEIGHT + 1;
    /** 网络环境信息 */
    public static final int ENV_NET_TYPE = ICON_SIZE + 1;

    /** 屏幕方向 */
    public static final int ORIENTATION = ENV_NET_TYPE + 1;
    /** 框属性 */
    public static final int ENV_EDIT_TYPE = ORIENTATION + 1;
    /** 框属性 */
    public static final int BOX_ATTRI = ENV_EDIT_TYPE + 1;
    /** 回车键类型 */
    public static final int ENTER_KEY_TYPE = BOX_ATTRI + 1;
    /** 当前是聊天场景或购物场景 */
    public static final int AI_SCENE_TYPE = ENTER_KEY_TYPE + 1;

    /** 设置轨迹的模式，也即开关 */
    public static final int TRACE_MODE = AI_SCENE_TYPE + 1;
    /** 设置英文shift状态 */
    public static final int EN_SHIFT = TRACE_MODE + 1;
    /** 当前是聊天场景或购物场景 */
    public static final int CLOUD_DELAY_TIME = EN_SHIFT + 1;
    /** 设置计算器time时间; 单位ms(默认100ms) */
    public static final int CALC_TIME_INTERVAL = CLOUD_DELAY_TIME + 1;
    /** 内核log的等级 */
    public static final int DEBUG_LOG_LEVEL = CALC_TIME_INTERVAL + 1;

    /** 英语面板下点击按键是否自动添加空格 */
    public static final int SPACE_AUTO_INSERT = DEBUG_LOG_LEVEL + 1;
    /** 向内核设置生僻字历史词最大个数 */
    public static final int RECENT_RARE_CAND_LIMIT = SPACE_AUTO_INSERT + 1;
    /** 文心模型加载的状态（仅有get） */
    public static final int WENXIN_LOAD_STATE = RECENT_RARE_CAND_LIMIT + 1;
    /** 输入框hint文本的类型 */
    public static final int EDIT_HINT_TYPE = WENXIN_LOAD_STATE + 1;

    /**
     * 是否需要请求手写联想的结果
     * 在 is_hw_req_cloud 关闭的场景，仅看是否需要在手写时请求联想(OEM在云端触发词的时候使用)
     * 内核默认关闭，各平台根据需求开启，开启效果：可请求hw_req的联想
     * 这时，即使 is_hw_req_cloud 关闭，is_hw_req_cloud_lian开启仍需请求手写联想
     */
    public static final int IS_HANDWRITING_REQUEST_CLOUD_LAIN = EDIT_HINT_TYPE + 1;

    /** String Key */
    /** sug白名单数据 */
    public static final int SUG_WHITE_DATA = IS_HANDWRITING_REQUEST_CLOUD_LAIN + 1;
    /** 手机CUID */
    public static final int CUID = SUG_WHITE_DATA + 1;
    /** 手机CUID3 */
    public static final int CUID3 = CUID + 1;
    /** 手机OAID */
    public static final int OAID = CUID3 + 1;
    /** 手机型号 */
    public static final int MODEL = OAID + 1;

    /** ipt_version */
    public static final int IPT_VER = MODEL + 1;
    /** 渠道号 */
    public static final int CHANNEL = IPT_VER + 1;
    /** 平台号 */
    public static final int IPT_PLATFORM = CHANNEL + 1;
    /** APP环境信息:包名 */
    public static final int ENV_APP = IPT_PLATFORM + 1;
    /** 城市 */
    public static final int CITY = ENV_APP + 1;

    /** 当前皮肤 */
    public static final int THEME = CITY + 1;
    /** 框内HINT词 */
    public static final int EDIT_HINT = THEME + 1;
    /** 双拼方案 */
    public static final int SP_FILE = EDIT_HINT + 1;
    /** 内核云输入1.3协议和2.0协议切换 */
    public static final int CLOUD_VERSION = SP_FILE + 1;
    /** 手写是否请求云 */
    public static final int HW_REQ_CLOUD = CLOUD_VERSION + 1;
    /** 设置云输入发起等级 */
    public static final int CLOUD_REQUEST_LEVEL = HW_REQ_CLOUD + 1;
    /** 向内核设置用户画像信息(b64 加密过后的用户画像信息，内核会自行解密) */
    public static final int USER_PROFILE = CLOUD_REQUEST_LEVEL + 1;
    /** 设置地图触发词 */
    public static final int MAP_TRIGGER_WORDS = USER_PROFILE + 1;

    /**
     * 设置通用触发词
     */
    public static final int COMMON_TRIGGER_WORDS = MAP_TRIGGER_WORDS + 1;

    /**
     * 设置云端触发词
     */
    public static final int CLOUND_TRIGGER_WORDS = COMMON_TRIGGER_WORDS + 1;

    /**
     * android Id
     */
    public static final int ANDROID_ID = CLOUND_TRIGGER_WORDS + 1;
    /**
     * 手机厂商
     */
    public static final int PHONE_VENDOR = ANDROID_ID + 1;
    /**
     * 系统版本
     */
    public static final int OS_VERSION = PHONE_VENDOR + 1;

    /** 配置手写结束后是否强制全上屏，默认关闭 */
    public static final int HW_FORCE_INSERT = OS_VERSION + 1;
    /** 配置当前场景是否是全局手写，默认关闭 */
    public static final int GLOBAL_HW_MODE = HW_FORCE_INSERT + 1;
    /** 硬键盘配置是否在数字后转化句号符号 */
    public static final int HK_CONVERT_DOT_AFTER_NUMBER = GLOBAL_HW_MODE + 1;

    /** 设置是否启用高斯个性化模型 */
    public static final int GAUSS_CUSTOMIZE = HK_CONVERT_DOT_AFTER_NUMBER + 1;
    /** 用于表示拆字的位置是否需要移动到主cand上，默认是false */
    public static final int CHAIZI_RESULT_INSERT_MAINCAND = GAUSS_CUSTOMIZE + 1;
    /**
     * 设置云触发词在手写面板显示在第几位
     */
    public static final int HANDWRITING_LIAN_CLOUD_TRIGGER_INDEX = CHAIZI_RESULT_INSERT_MAINCAND + 1;
    /**
     * 荣耀特有的 OAID
     */
    public static final int HONOR_OAID = HANDWRITING_LIAN_CLOUD_TRIGGER_INDEX + 1;
    /** 是否屏蔽手写定时器产生的cand */
    public static final int DISABLE_HW_TIMER_CAND = HONOR_OAID + 1;

    /** 联系人联想功能，默认开启 */
    public static final int CONTACT_LIAN = DISABLE_HW_TIMER_CAND + 1;

    /** 高斯个性化模型落点收集与训练 */
    public static final int GAUSS_CUSTOMIZE_USER_POINTS_DISABLED = CONTACT_LIAN + 1;

    /**
     * 浏览器 UA
     */
    public static final int USER_AGENT = GAUSS_CUSTOMIZE_USER_POINTS_DISABLED + 1;
    /**
     * 运营商
     */
    public static final int MOBILE_CARRIER = USER_AGENT + 1;
    /**
     * 设备启动时间
     */
    public static final int DEVICE_START_SEC = MOBILE_CARRIER + 1;
    /**
     * 物理内存
     */
    public static final int PHYSICAL_MEM_BYTE = DEVICE_START_SEC + 1;
    /**
     * 硬盘大小
     */
    public static final int HARD_DISK_SIZE_BYTE = PHYSICAL_MEM_BYTE + 1;
    /**
     * 系统更新时间
     */
    public static final int SYSTEM_UPDATE_MICRO_SEC = HARD_DISK_SIZE_BYTE + 1;
    /**
     * 是否需要云端通用触发词请求神句配图
     */
    public static final int IS_NEED_CLOUD_TRIGGER_PEITU = SYSTEM_UPDATE_MICRO_SEC + 1;
    /**
     * emoji不强插触发词后，根据词频自主排序（现在仅荣耀开启，其他端关闭）
     */
    public static final int EMOJI_CAND_SORT_BYFREQ = IS_NEED_CLOUD_TRIGGER_PEITU + 1;

    /**
     * 个人信息联想开关开关 - 是否查询用户个人化信息，并给出联想候选，如手机号码，身份证号等 ///
     */
    public static final int PERSON_INFO_PERMISSION_STATUS = EMOJI_CAND_SORT_BYFREQ + 1;
    /**
     * 是否需要判断输入内容是否包含身份证号 默认关闭
     */
    public static final int IDENTITY_NUM_RECOGNIZE = PERSON_INFO_PERMISSION_STATUS + 1;
}
