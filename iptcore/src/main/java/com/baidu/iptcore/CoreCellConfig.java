package com.baidu.iptcore;

import com.baidu.iptcore.util.Ids;

import com.baidu.input.inputtrace.api.InputTracer;

import java.util.Arrays;

import com.baidu.iptcore.info.IptCellInfo;
import com.baidu.iptcore.info.IptIdmCellInfo;
import com.baidu.iptcore.info.IptKwdCellInfo;
import com.baidu.iptcore.info.IptPhraseGroup;
import com.baidu.iptcore.info.IptPhraseItem;
import com.baidu.iptcore.util.Config;
import com.baidu.iptcore.util.MethodTracer;

/**
 * 词库相关的设置
 * Created by ch<PERSON><PERSON><PERSON> on 18/2/3.
 */
public class CoreCellConfig {

    public int installCell(int key, String path) {
        InputTracer.i(Ids.installCell);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("installCell: " + key + "=" + path);
        }
        int result = IptCoreInterface.get().installCell(key, path);
        InputTracer.o(Ids.installCell);
        return result;
    }

    public int uninstallCell(int key, int id) {
        InputTracer.i(Ids.uninstallCell);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("uninstallCell: " + key + "=" + id);
        }
        int result = IptCoreInterface.get().uninstallCell(key, id);
        InputTracer.o(Ids.uninstallCell);
        return result;
    }

    public int getPhraseGroupCount() {
        InputTracer.i(Ids.getPhraseGroupCount);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("getPhraseGroupCount");
        }
        int result = IptCoreInterface.get().getPhraseGroupCount();
        InputTracer.o(Ids.getPhraseGroupCount);
        return result;
    }

    public IptPhraseGroup getPhraseGroup(int idx) {
        InputTracer.i(Ids.getPhraseGroup);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("getPhraseGroup: " + idx);
        }
        IptPhraseGroup group = new IptPhraseGroup();
        int ret = IptCoreInterface.get().getPhraseGroup(idx, group);
        if (ret >= 0) {
            InputTracer.o(Ids.getPhraseGroup);
            return group;
        } else {
            InputTracer.o(Ids.getPhraseGroup);
            return null;
        }
    }

    public int addPhraseGroup(String groupName) {
        InputTracer.i(Ids.addPhraseGroup);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("addPhraseGroup: " + groupName);
        }
        int result = IptCoreInterface.get().addPhraseGroup(groupName);
        InputTracer.o(Ids.addPhraseGroup);
        return result;
    }

    public int deletePhraseGroup(int idx) {
        InputTracer.i(Ids.deletePhraseGroup);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("deletePhraseGroup: " + idx);
        }
        int result = IptCoreInterface.get().deletePhraseGroup(idx);
        InputTracer.o(Ids.deletePhraseGroup);
        return result;
    }

    public int editPhraseGroup(int idx, String groupName) {
        InputTracer.i(Ids.editPhraseGroup);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("editPhraseGroup: " + idx + ", " + groupName);
        }
        int result = IptCoreInterface.get().editPhraseGroup(idx, groupName);
        InputTracer.o(Ids.editPhraseGroup);
        return result;
    }

    public int enablePhraseGroup(int idx, boolean isEnable) {
        InputTracer.i(Ids.enablePhraseGroup);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("enablePhraseGroup: " + idx + ", " + isEnable);
        }
        int result = IptCoreInterface.get().enablePhraseGroup(idx, isEnable);
        InputTracer.o(Ids.enablePhraseGroup);
        return result;
    }

    public int getPhraseItemCount(int groupId, String code) {
        InputTracer.i(Ids.getPhraseItemCount);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("getPhraseItemCount: " + groupId + ", " + code);
        }
        int result = IptCoreInterface.get().getPhraseItemCount(groupId, code);
        InputTracer.o(Ids.getPhraseItemCount);
        return result;
    }

    public IptPhraseItem getPhraseItem(int idx) {
        InputTracer.i(Ids.getPhraseItem);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("getPhraseItem: " + idx);
        }
        IptPhraseItem item = new IptPhraseItem();
        int ret = IptCoreInterface.get().getPhraseItem(idx, item);
        if (ret >= 0) {
            InputTracer.o(Ids.getPhraseItem);
            return item;
        } else {
            InputTracer.o(Ids.getPhraseItem);
            return null;
        }
    }

    public int addPhraseItem(String code, String word, int pos, int groupId) {
        InputTracer.i(Ids.addPhraseItem);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("addPhraseItem: " + code + ", " + word + ", " + pos + ", " + groupId);
        }
        int result = IptCoreInterface.get().addPhraseItem(code, word, pos, groupId);
        InputTracer.o(Ids.addPhraseItem);
        return result;
    }

    public int editPhraseItem(int idx, String code, String word, int pos) {
        InputTracer.i(Ids.editPhraseItem);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("editPhraseItem: " + idx + ", " + code + ", " + word + ", " + pos);
        }
        int result = IptCoreInterface.get().editPhraseItem(idx, code, word, pos);
        InputTracer.o(Ids.editPhraseItem);
        return result;
    }

    public int deletePhraseItem(int idx) {
        InputTracer.i(Ids.deletePhraseItem);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("deletePhraseItem: " + idx);
        }
        int result = IptCoreInterface.get().deletePhraseItem(idx);
        InputTracer.o(Ids.deletePhraseItem);
        return result;
    }
    
    public int importPhrase(String filename, boolean overWrite) {
        InputTracer.i(Ids.importPhrase);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("importPhrase: " + filename + ", " + overWrite);
        }
        int result = IptCoreInterface.get().importPhrase(filename, overWrite);
        InputTracer.o(Ids.importPhrase);
        return result;
    }

    public int exportPhrase(String filename, int groupId) {
        InputTracer.i(Ids.exportPhrase);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("exportPhrase: " + filename + ", " + groupId);
        }
        int result = IptCoreInterface.get().exportPhrase(filename, groupId);
        InputTracer.o(Ids.exportPhrase);
        return result;
    }

    public int reduceUsWord(int percent) {
        InputTracer.i(Ids.reduceUsWord);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("reduceUsWord: " + percent);
        }
        int result = IptCoreInterface.get().reduceUsWord(percent);
        InputTracer.o(Ids.reduceUsWord);
        return result;
    }

    public int getUsWordSize() {
        InputTracer.i(Ids.getUsWordSize);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("getUsWordSize");
        }
        int result = IptCoreInterface.get().getUsWordSize();
        InputTracer.o(Ids.getUsWordSize);
        return result;
    }

    public int importUsWord(String path) {
        InputTracer.i(Ids.importUsWord);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("importUsWord: " + path);
        }
        int result = IptCoreInterface.get().importUsWord(path);
        InputTracer.o(Ids.importUsWord);
        return result;
    }

    public int exportUsWord(String path) {
        InputTracer.i(Ids.exportUsWord);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("exportUsWord: " + path);
        }
        int result = IptCoreInterface.get().exportUsWord(path);
        InputTracer.o(Ids.exportUsWord);
        return result;
    }

    public int importUeWord(String path) {
        InputTracer.i(Ids.importUeWord);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("importUeWord: " + path);
        }
        int result = IptCoreInterface.get().importUeWord(path);
        InputTracer.o(Ids.importUeWord);
        return result;
    }

    public int exportUeWord(String path) {
        InputTracer.i(Ids.exportUeWord);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("exportUeWord: " + path);
        }
        int result = IptCoreInterface.get().exportUeWord(path);
        InputTracer.o(Ids.exportUeWord);
        return result;
    }

    public int exportRareUserWord(String path) {
        InputTracer.i(Ids.exportRareUserWord);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("exportRareUserWord: " + path);
        }
        int result = IptCoreInterface.get().exportRareUserWord(path);
        InputTracer.o(Ids.exportRareUserWord);
        return result;
    }

    public int getHwRareVersion() {
        InputTracer.i(Ids.getHwRareVersion);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("getHwRareVersion");
        }
        int result = IptCoreInterface.get().getHwRareVersion();
        InputTracer.o(Ids.getHwRareVersion);
        return result;
    }


    public int importAllUserWord(String path) {
        InputTracer.i(Ids.importAllUserWord);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("importAllUserWord: " + path);
        }
        int result = IptCoreInterface.get().importAllUserWord(path);
        InputTracer.o(Ids.importAllUserWord);
        return result;
    }

    public int exportAllUserWord(String path) {
        InputTracer.i(Ids.exportAllUserWord);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("exportAllUserWord: " + path);
        }
        int result = IptCoreInterface.get().exportAllUserWord(path);
        InputTracer.o(Ids.exportAllUserWord);
        return result;
    }

    public int getCellCount() {
        InputTracer.i(Ids.getCellCount);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("getCellCount");
        }
        int result = IptCoreInterface.get().getCellCount();
        InputTracer.o(Ids.getCellCount);
        return result;
    }

    public IptCellInfo getCellInfoByIndex(int idx) {
        InputTracer.i(Ids.getCellInfoByIndex);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("getCellInfoByIndex:" + idx);
        }
        IptCellInfo item = new IptCellInfo();
        int ret = IptCoreInterface.get().getCellInfoByIndex(idx, item);
        if (ret >= 0) {
            InputTracer.o(Ids.getCellInfoByIndex);
            return item;
        } else {
            InputTracer.o(Ids.getCellInfoByIndex);
            return null;
        }
    }

    public IptCellInfo getCellInfoByCellId(int cellId) {
        InputTracer.i(Ids.getCellInfoByCellId);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("getCellInfoByCellId");
        }
        IptCellInfo item = new IptCellInfo();
        int ret = IptCoreInterface.get().getCellInfoByCellId(cellId, item);
        if (ret >= 0) {
            InputTracer.o(Ids.getCellInfoByCellId);
            return item;
        } else {
            InputTracer.o(Ids.getCellInfoByCellId);
            return null;
        }
    }

    public boolean upateCellLocInfo(IptCellInfo info, int locType, int installTime) {
        InputTracer.i(Ids.upateCellLocInfo);
        if (info == null) {
            InputTracer.o(Ids.upateCellLocInfo);
            return false;
        }
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("upateCellLocInfo");
        }
        int ret1 = IptCoreInterface.get().setCellLocType(info.cellId(), locType);
        int ret2 = IptCoreInterface.get().setCellInstallTime(info.cellId(), installTime);
        InputTracer.o(Ids.upateCellLocInfo);
        return ret1 >= 0 && ret2 >= 0;
    }

    public int getPopWordCellId() {
        InputTracer.i(Ids.getPopWordCellId);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("getPopWordCellId");
        }
        int result = IptCoreInterface.get().getPopWordCellId();
        InputTracer.o(Ids.getPopWordCellId);
        return result;
    }

    public int getSyswordCellId() {
        InputTracer.i(Ids.getSyswordCellId);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("getSyswordCellId");
        }
        int result = IptCoreInterface.get().getSysWordCellId();
        InputTracer.o(Ids.getSyswordCellId);
        return result;
    }

    public int getCloudWhiteVer() {
        InputTracer.i(Ids.getCloudWhiteVer);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("getCloudWhiteVer");
        }
        int result = IptCoreInterface.get().getCloudWhiteVer();
        InputTracer.o(Ids.getCloudWhiteVer);
        return result;
    }

    public int enableCell(int cellId, boolean isEnable) {
        InputTracer.i(Ids.enableCell);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("enableCell:" + cellId + ", " + isEnable);
        }
        int result = IptCoreInterface.get().enableCell(cellId, isEnable);
        InputTracer.o(Ids.enableCell);
        return result;
    }

    public int getRecentCount(int days) {
        InputTracer.i(Ids.getRecentCount);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("getRecentCount:" + days);
        }
        int result = IptCoreInterface.get().getRecentCount(days);
        InputTracer.o(Ids.getRecentCount);
        return result;
    }
    
    public int getKwdCellCount() {
        InputTracer.i(Ids.getKwdCellCount);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("getKwdCellCount");
        }
        int result = IptCoreInterface.get().getKwdCellCount();
        InputTracer.o(Ids.getKwdCellCount);
        return result;
    }

    public IptKwdCellInfo getKwdCellInfoByIndex(int index) {
        InputTracer.i(Ids.getKwdCellInfoByIndex);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("getKwdCellInfoByIndex:" + index);
        }
        IptKwdCellInfo item = new IptKwdCellInfo();
        int ret = IptCoreInterface.get().getKwdCellInfoByIndex(index, item);
        if (ret >= 0) {
            InputTracer.o(Ids.getKwdCellInfoByIndex);
            return item;
        } else {
            InputTracer.o(Ids.getKwdCellInfoByIndex);
            return null;
        }
    }

    public IptKwdCellInfo getKwdCellInfoByCellId(int cellId) {
        InputTracer.i(Ids.getKwdCellInfoByCellId);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("getKwdCellInfoByCellId:" + cellId);
        }
        IptKwdCellInfo item = new IptKwdCellInfo();
        int ret = IptCoreInterface.get().getKwdCellInfoByCellId(cellId, item);
        if (ret >= 0) {
            InputTracer.o(Ids.getKwdCellInfoByCellId);
            return item;
        } else {
            InputTracer.o(Ids.getKwdCellInfoByCellId);
            return null;
        }
    }

    public int exportKwd(String filePath) {
        InputTracer.i(Ids.exportKwd);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("exportKwd: " + filePath);
        }
        int result = IptCoreInterface.get().exportKwd(filePath);
        InputTracer.o(Ids.exportKwd);
        return result;
    }

    public int getIdmCellCount() {
        InputTracer.i(Ids.getIdmCellCount);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("getIdmCellCount");
        }
        int result = IptCoreInterface.get().getIdmCellCount();
        InputTracer.o(Ids.getIdmCellCount);
        return result;
    }

    public IptIdmCellInfo getIdmCellInfoByIndex(int index) {
        InputTracer.i(Ids.getIdmCellInfoByIndex);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("getIdmCellInfoByIndex:" + index);
        }
        IptIdmCellInfo item = new IptIdmCellInfo();
        int ret = IptCoreInterface.get().getIdmCellInfoByIndex(index, item);
        if (ret >= 0) {
            InputTracer.o(Ids.getIdmCellInfoByIndex);
            return item;
        } else {
            InputTracer.o(Ids.getIdmCellInfoByIndex);
            return null;
        }
    }

    public byte[] hwEncodePoint(int candId) {
        InputTracer.i(Ids.hwEncodePoint);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("hwEncodePoint:id=" + candId);
        }
        byte[] result = IptCoreInterface.get().hwEncodePoint(candId);
        InputTracer.o(Ids.hwEncodePoint);
        return result;
    }

    @Deprecated
    public int contactReset() {
        return IptCoreInterface.get().contactReset();
    }

    @Deprecated
    public int contactAppendAttr(String attr) {
        return IptCoreInterface.get().contactAppendAttr(attr);
    }

    @Deprecated
    public int contactAppendValue(String name, String attr, String value) {
        return IptCoreInterface.get().contactAppendValue(name, attr, value);
    }

    public int contactBuildStart() {
        InputTracer.i(Ids.contactBuildStart);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("contactBuildStart");
        }
        int result = IptCoreInterface.get().contactBuildStart();
        InputTracer.o(Ids.contactBuildStart);
        return result;
    }

    public int contactBuildAddName(String name) {
        InputTracer.i(Ids.contactBuildAddName);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("contactBuildAddName: " + name);
        }
        int result = IptCoreInterface.get().contactBuildAddName(name);
        InputTracer.o(Ids.contactBuildAddName);
        return result;
    }

    public int contactBuildAddValue(String attr, String value) {
        InputTracer.i(Ids.contactBuildAddValue);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("contactBuildAddValue, attr=" + attr + ", value=" + value);
        }
        int result = IptCoreInterface.get().contactBuildAddValue(attr, value);
        InputTracer.o(Ids.contactBuildAddValue);
        return result;
    }

    public int contactBuildEnd(boolean isRetainBlack) {
        InputTracer.i(Ids.contactBuildEnd);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("contactBuildEnd, isRetainBlack=" + isRetainBlack);
        }
        int result = IptCoreInterface.get().contactBuildEnd(isRetainBlack);
        InputTracer.o(Ids.contactBuildEnd);
        return result;
    }

    public int contactBuildAddBlackName(String delName) {
        InputTracer.i(Ids.contactBuildAddBlackName);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("contactBuildAddBlackName: delName=" + delName);
        }
        int result = IptCoreInterface.get().contactBuildAddBlackName(delName);
        InputTracer.o(Ids.contactBuildAddBlackName);
        return result;
    }

    @Deprecated
    public int contactDelete(String name, int option) {
        return IptCoreInterface.get().contactDelete(name, option);
    }

    public String contactVoiceFind(String oriWord) {
        InputTracer.i(Ids.contactVoiceFind);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("contactVoiceFind");
        }
        String result = IptCoreInterface.get().contactVoiceFind(oriWord);
        InputTracer.o(Ids.contactVoiceFind);
        return result;
    }

    public String contactVoiceFindAddressbook(String oriWord) {
        InputTracer.i(Ids.contactVoiceFindAddressbook);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("contactVoiceFindAddressbook");
        }
        String result = IptCoreInterface.get().contactVoiceFindAddressbook(oriWord);
        InputTracer.o(Ids.contactVoiceFindAddressbook);
        return result;
    }

    @Deprecated
    public int oldCpExport(String inputFile, String outputFile) {
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("oldCpExport");
        }
        return IptCoreInterface.get().oldCpExport(inputFile, outputFile);
    }

    @Deprecated
    public int oldUeExport(String inputFile, String outputFile) {
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("oldUeExport");
        }
        return IptCoreInterface.get().oldUeExport(inputFile, outputFile);
    }

    public int importZyword(String fileName) {
        InputTracer.i(Ids.importZyword);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("importZyword");
        }
        int result = IptCoreInterface.get().importZyword(fileName);
        InputTracer.o(Ids.importZyword);
        return result;
    }

    public int exportZyword(String fileName) {
        InputTracer.i(Ids.exportZyword);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("exportZyword");
        }
        int result = IptCoreInterface.get().exportZyword(fileName);
        InputTracer.o(Ids.exportZyword);
        return result;
    }

    public int symImport(String fileName, boolean isOverwrite) {
        InputTracer.i(Ids.symImport);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("symImport");
        }
        int result = IptCoreInterface.get().symImport(fileName, isOverwrite);
        InputTracer.o(Ids.symImport);
        return result;
    }

    public int symExport(String fileName) {
        InputTracer.i(Ids.symExport);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("symExport");
        }
        int result = IptCoreInterface.get().symExport(fileName);
        InputTracer.o(Ids.symExport);
        return result;
    }

    public int userSymImport(String[] symData) {
        InputTracer.i(Ids.userSymImport);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("userSymImport: symData=" +
                    Arrays.toString(symData));
        }
        int result = IptCoreInterface.get().userSymImport(symData);
        InputTracer.o(Ids.userSymImport);
        return result;
    }

    public int sylianImport(String fileName, boolean isOverwrite) {
        InputTracer.i(Ids.sylianImport);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("sylianImport");
        }
        int result = IptCoreInterface.get().sylianImport(fileName, isOverwrite);
        InputTracer.o(Ids.sylianImport);
        return result;
    }

    public int vkwordImport(String fileName, boolean isOverwrite) {
        InputTracer.i(Ids.vkwordImport);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("vkwordImport");
        }
        int result = IptCoreInterface.get().vkwordImport(fileName, isOverwrite);
        InputTracer.o(Ids.vkwordImport);
        return result;
    }

    public int vkwordExport(String fileName) {
        InputTracer.i(Ids.vkwordExport);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("vkwordExport");
        }
        int result = IptCoreInterface.get().vkwordExport(fileName);
        InputTracer.o(Ids.vkwordExport);
        return result;
    }

    public int usrinfoImport(String fileName) {
        InputTracer.i(Ids.usrinfoImport);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("usrinfoImport");
        }
        int result = IptCoreInterface.get().usrinfoImport(fileName);
        InputTracer.o(Ids.usrinfoImport);
        return result;
    }

    public int usrinfoExport(String fileName) {
        InputTracer.i(Ids.usrinfoExport);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("usrinfoExport");
        }
        int result = IptCoreInterface.get().usrinfoExport(fileName);
        InputTracer.o(Ids.usrinfoExport);
        return result;
    }

    public int otherwordImport(String fileName, boolean isOverwrite) {
        InputTracer.i(Ids.otherwordImport);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("otherwordImport");
        }
        int result = IptCoreInterface.get().otherwordImport(fileName, isOverwrite);
        InputTracer.o(Ids.otherwordImport);
        return result;
    }

    public int getZhuyinHzVersion() {
        InputTracer.i(Ids.getZhuyinHzVersion);
        int ret = IptCoreInterface.get().getZhuyinHzVersion();
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("getZhuyinHzVersion " + ret);
        }
        InputTracer.o(Ids.getZhuyinHzVersion);
        return ret;
    }

    public int getZhuyinCzVersion() {
        InputTracer.i(Ids.getZhuyinCzVersion);
        int ret = IptCoreInterface.get().getZhuyinCzVersion();
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("getZhuyinCzVersion " + ret);
        }
        InputTracer.o(Ids.getZhuyinCzVersion);
        return ret;
    }

    public int getZhuyinCzInfo() {
        InputTracer.i(Ids.getZhuyinCzInfo);
        int ret = IptCoreInterface.get().getZhuyinCzInfo();
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("getZhuyinCzInfo " + ret);
        }
        InputTracer.o(Ids.getZhuyinCzInfo);
        return ret;
    }

    public int getCangjieVersion() {
        InputTracer.i(Ids.getCangjieVersion);
        int ret = IptCoreInterface.get().getCangjieVersion();
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("getCangjieVersion " + ret);
        }
        InputTracer.o(Ids.getCangjieVersion);
        return ret;
    }

    public int getHwVersion() {
        InputTracer.i(Ids.getHwVersion);
        int ret = IptCoreInterface.get().getHwVersion();
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("getHwVersion " + ret);
        }
        InputTracer.o(Ids.getHwVersion);
        return ret;
    }


    public int searchGetVersion() {
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("searchGetVersion");
        }
        return IptCoreInterface.get().searchGetVersion();
    }

    public int[] searchFind(String queryWord) {
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("searchFind: " + queryWord);
        }
        return IptCoreInterface.get().searchFind(queryWord);
    }

    public int adjustSylianRelation(String preStr, int preLen, String tailStr, int tailLen) {
        InputTracer.i(Ids.adjustSylianRelation);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("adjustSylianRelation:" + preStr + ", " + preLen
                    + ", " + tailStr + ", " + tailLen);
        }
        int result = IptCoreInterface.get().adjustSylianRelation(preStr, preLen, tailStr, tailLen);
        InputTracer.o(Ids.adjustSylianRelation);
        return result;
    }

    public char[] featureInfExtract(String originStr, int originLen, int infType) {
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("featureInfExtract");
        }
        return IptCoreInterface.get().featureInfExtract(originStr, originLen, infType);
    }

    public int candContextExport(String fileName) {
        InputTracer.i(Ids.candContextExport);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("candContextExport: " + fileName);
        }
        int result = IptCoreInterface.get().candContextExport(fileName);
        InputTracer.o(Ids.candContextExport);
        return result;
    }

    public String getFtByUni(String unicode) {
        InputTracer.i(Ids.getFtByUni);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("getFtByUni:" + unicode);
        }
        String result = IptCoreInterface.get().getFtByUni(unicode);
        InputTracer.o(Ids.getFtByUni);
        return result;
    }

    public int importOldUsFile(String oldCellFile, String oldUzFile) {
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("importOldUsFile: " + oldCellFile + ", " + oldUzFile);
        }
        return IptCoreInterface.get().importOldUsFile(oldCellFile, oldUzFile);
    }

    @Deprecated
    public int kwdGetSearchVersion() {
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("kwdGetSearchVersion");
        }
        return IptCoreInterface.get().kwdGetSearchVersion();
    }

    public int getAutoReplyVer() {
        InputTracer.i(Ids.getAutoReplyVer);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("getAutoReplyVer");
        }
        int result = IptCoreInterface.get().getAutoReplyVer();
        InputTracer.o(Ids.getAutoReplyVer);
        return result;
    }

    public int getSpMapSheng(byte keyChar, byte[] shengList) {
        InputTracer.i(Ids.getSpMapSheng);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("getSpMapSheng: " + keyChar);
        }
        int result = IptCoreInterface.get().getSpMapSheng(keyChar, shengList);
        InputTracer.o(Ids.getSpMapSheng);
        return result;
    }

    public int getSpMapYun(byte keyChar, byte[] yunList) {
        InputTracer.i(Ids.getSpMapYun);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("getSpMapYun:" + keyChar);
        }
        int result = IptCoreInterface.get().getSpMapYun(keyChar, yunList);
        InputTracer.o(Ids.getSpMapYun);
        return result;
    }

    public int importAppMap(String filePath) {
        InputTracer.i(Ids.importAppMap);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("importAppMap:" + filePath);
        }
        int result = IptCoreInterface.get().importAppMap(filePath);
        InputTracer.o(Ids.importAppMap);
        return result;
    }

    public int appMapGetVersion() {
        InputTracer.i(Ids.appMapGetVersion);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("appMapGetVersion");
        }
        int result = IptCoreInterface.get().appMapGetVersion();
        InputTracer.o(Ids.appMapGetVersion);
        return result;
    }

    public int czDownRefresh(String fileName) {
        InputTracer.i(Ids.czDownRefresh);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("czDownRefresh:" + fileName);
        }
        int result = IptCoreInterface.get().czDownRefresh(fileName);
        InputTracer.o(Ids.czDownRefresh);
        return result;
    }

    public int coreRefresh(int type, String fileName) {
        InputTracer.i(Ids.coreRefresh);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("coreRefresh:" + type + ", " + fileName);
        }
        int result = IptCoreInterface.get().coreRefresh(type);
        InputTracer.o(Ids.coreRefresh);
        return result;
    }

    public int coreUnload(int type) {
        InputTracer.i(Ids.coreUnload);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("coreUnload type:" + type);
        }
        int result = IptCoreInterface.get().coreUnload(type);
        InputTracer.o(Ids.coreUnload);
        return result;
    }

    public int nnrankerRefresh(String configFilePath, String wordFilePath,
                           String modelFilePath) {
        InputTracer.i(Ids.nnrankerRefresh);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod(
                    "nnrankerRefresh:" + configFilePath + ", " + wordFilePath + ", " + modelFilePath);
        }
        int result = IptCoreInterface.get().nnrankerRefresh(configFilePath, wordFilePath, modelFilePath);
        InputTracer.o(Ids.nnrankerRefresh);
        return result;
    }

    public int autoReplyRefresh(String fileName) {
        InputTracer.i(Ids.autoReplyRefresh);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("autoReplyRefresh: " + fileName);
        }
        int result = IptCoreInterface.get().autoReplyRefresh(fileName);
        InputTracer.o(Ids.autoReplyRefresh);
        return result;
    }

    @Deprecated
    public int createUswordManager(String searchWord, int type) {
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("createUswordManager: searchWord=" + searchWord + ", type=" + type);
        }
        IptCoreInterface.get().createUswordManager(searchWord, type);
        return 0;
    }

    public int destroyUswordManager() {
        InputTracer.i(Ids.destroyUswordManager);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("destroyUswordManager");
        }
        IptCoreInterface.get().destroyUswordManager();
        InputTracer.o(Ids.destroyUswordManager);
        return 0;
    }

    public int uswordGetCount() {
        InputTracer.i(Ids.uswordGetCount);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("uswordGetCount");
        }
        int result = IptCoreInterface.get().uswordGetCount();
        InputTracer.o(Ids.uswordGetCount);
        return result;
    }

    public String uswordGetStr(int idx) {
        InputTracer.i(Ids.uswordGetStr);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("uswordGetStr: idx=" + idx);
        }
        String result = IptCoreInterface.get().uswordGetStr(idx);
        InputTracer.o(Ids.uswordGetStr);
        return result;
    }

    public int uswordGetAction(int idx) {
        InputTracer.i(Ids.uswordGetAction);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("uswordGetAction: idx=" + idx);
        }
        int result = IptCoreInterface.get().uswordGetAction(idx);
        InputTracer.o(Ids.uswordGetAction);
        return result;
    }

    public int uswordDoAction(int idx) {
        InputTracer.i(Ids.uswordDoAction);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("uswordDoAction: idx=" + idx);
        }
        IptCoreInterface.get().uswordDoAction(idx);
        InputTracer.o(Ids.uswordDoAction);
        return 0;
    }

    public int uswordGetCnWordCount() {
        InputTracer.i(Ids.uswordGetCnWordCount);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("uswordGetCnWordCount");
        }
        int result = IptCoreInterface.get().uswordGetCnWordCount();
        InputTracer.o(Ids.uswordGetCnWordCount);
        return result;
    }

    public int keywordFindVoiceLian(String content, int[] emojiList, String[] emoticonList) {
        InputTracer.i(Ids.keywordFindVoiceLian);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("keywordFindVoiceLian:" + content);
        }
        int result = IptCoreInterface.get().keywordFindVoiceLian(content, emojiList, emoticonList);
        InputTracer.o(Ids.keywordFindVoiceLian);
        return result;
    }

    public char[] keywordFindVoiceEgg(String content) {
        InputTracer.i(Ids.keywordFindVoiceEgg);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("keywordFindVoiceEgg:" + content);
        }
        char[] result = IptCoreInterface.get().keywordFindVoiceEgg(content);
        InputTracer.o(Ids.keywordFindVoiceEgg);
        return result;
    }

    public int backupTraceLog(String filePath) {
        InputTracer.i(Ids.backupTraceLog);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("backupTraceLog: " + filePath);
        }
        int result = IptCoreInterface.get().backupTraceLog(filePath);
        InputTracer.o(Ids.backupTraceLog);
        return result;
    }

    public int blockCloudUnis(String content) {
        InputTracer.i(Ids.blockCloudUnis);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("blockCloudUnis: content=" + content);
        }
        int result = IptCoreInterface.get().blockCloudUnis(content);
        InputTracer.o(Ids.blockCloudUnis);
        return result;
    }

    public int resetCloudBlack() {
        InputTracer.i(Ids.resetCloudBlack);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("resetCloudBlack");
        }
        int result = IptCoreInterface.get().resetCloudBlack();
        InputTracer.o(Ids.resetCloudBlack);
        return result;
    }

    public int importOldDefWord(String filePath) {
        InputTracer.i(Ids.importOldDefWord);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("importOldDefWord:" + filePath);
        }
        int result = IptCoreInterface.get().importOldDefWord(filePath);
        InputTracer.o(Ids.importOldDefWord);
        return result;
    }

    public byte[] getContactNamesInPb() {
        InputTracer.i(Ids.getContactNamesInPb);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("getContactNamesInPb");
        }
        byte[] result = IptCoreInterface.get().getContactNamesInPb();
        InputTracer.o(Ids.getContactNamesInPb);
        return result;
    }

    public String exportInputAssociateInfo() {
        InputTracer.i(Ids.exportInputAssociateInfo);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("exportInputAssociateInfo");
        }
        String[] associateData = new String[1];
        IptCoreInterface.get().exportInputAssociateInfo(associateData);
        InputTracer.o(Ids.exportInputAssociateInfo);
        return associateData[0];
    }

    public String getKeyCountForMisTouch() {
        InputTracer.i(Ids.getKeyCountForMisTouch);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("getKeyCountForMisTouch");
        }
        String result = IptCoreInterface.get().getKeyCountForMisTouch();
        InputTracer.o(Ids.getKeyCountForMisTouch);
        return result;
    }

    public void iptDictRebuildFromOldUsr2(String outputDir, String immDir,
                                          String usr2Path, String vkwordPath) {
        InputTracer.i(Ids.iptDictRebuildFromOldUsr2);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("iptDictRebuildFromOldUsr2");
        }
        IptCoreInterface.get().iptDictRebuildFromOldUsr2(outputDir, immDir, usr2Path, vkwordPath);
        InputTracer.o(Ids.iptDictRebuildFromOldUsr2);
    }

    public void iptDictRebuildFromOldUe2(String outputDir, String immDir, String ue2Path,
                                         String learnDic1Path, String learnDic2Path) {
        InputTracer.i(Ids.iptDictRebuildFromOldUe2);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("iptDictRebuildFromOldUe2");
        }
        IptCoreInterface.get().iptDictRebuildFromOldUe2(outputDir, immDir, ue2Path,
                learnDic1Path, learnDic2Path);
        InputTracer.o(Ids.iptDictRebuildFromOldUe2);
    }

    public void iptDictRebuildFromOldKeyword(String outputDir, String path) {
        InputTracer.i(Ids.iptDictRebuildFromOldKeyword);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("iptDictRebuildFromOldKeyword");
        }
        IptCoreInterface.get().iptDictRebuildFromOldKeyword(outputDir, path);
        InputTracer.o(Ids.iptDictRebuildFromOldKeyword);
    }

    public void iptDictRebuildFromOldZyUsr(String outputDir, String immDir,
                                           String downDir, String path) {
        InputTracer.i(Ids.iptDictRebuildFromOldZyUsr);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("iptDictRebuildFromOldZyUsr");
        }
        IptCoreInterface.get().iptDictRebuildFromOldZyUsr(outputDir, immDir, downDir, path);
        InputTracer.o(Ids.iptDictRebuildFromOldZyUsr);
    }

    public void iptDictRebuildFromOldZy(String outputDir, String immDir,
                                        String hzZyPath, String czZyPath) {
        InputTracer.i(Ids.iptDictRebuildFromOldZy);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("iptDictRebuildFromOldZy");
        }
        IptCoreInterface.get().iptDictRebuildFromOldZy(outputDir, immDir, hzZyPath, czZyPath);
        InputTracer.o(Ids.iptDictRebuildFromOldZy);
    }

    public void iptDictRebuildFromOldCangjie(String outputDir,
                                             String path, boolean isQuick) {
        InputTracer.i(Ids.iptDictRebuildFromOldCangjie);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("iptDictRebuildFromOldCangjie");
        }
        IptCoreInterface.get().iptDictRebuildFromOldCangjie(outputDir, path, isQuick);
        InputTracer.o(Ids.iptDictRebuildFromOldCangjie);
    }


    /**
     * 该函数没有说明
     */
    public int dctCellInstallByBuffer(String value) {
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("dctCellInstallByBuffer:" + value);
        }
        return IptCoreInterface.get().dctCellInstallByBuffer(value);
    }

    /**
     * 该函数没有说明
     */
    public int dctCellWordInDict(String value) {
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("dctCellWordInDict:" + value);
        }
        return IptCoreInterface.get().dctCellWordInDict(value);
    }

    /**
     * 该函数没有说明
     */
    public String dctCellExportBuffer(int cellId) {
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("dctCellExportBuffer:" + cellId);
        }
        return IptCoreInterface.get().dctCellExportBuffer(cellId);
    }

    /**
     * 该函数没有说明
     */
    public int dctCellResetBuffer(int cellId) {
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("dctCellResetBuffer:" + cellId);
        }
        return IptCoreInterface.get().dctCellResetBuffer(cellId);
    }

    public int addPersonInfoFromLine(int index, String line) {
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("addPersonInfoFromLine:" + index + "," + line);
        }
        return IptCoreInterface.get().addPersonInfoFromLine(index, line);
    }

    public int startPersonInfoAddLine() {
        int ret = IptCoreInterface.get().startPersonInfoAddLine();
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("startPersonInfoAddLine, ret: " + ret);
        }
        return ret;
    }

    public int personInfoAddFinished() {
        int ret = IptCoreInterface.get().personInfoAddFinished();
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("personInfoAddFinished, ret: " + ret);
        }
        return ret;
    }

    public int resetPersonInfo() {
        int ret = IptCoreInterface.get().resetPersonInfo();
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("resetPersonInfo, ret: " + ret);
        }
        return ret;
    }

}
