package com.baidu.iptcore;

import android.support.annotation.NonNull;
import android.text.TextUtils;

import com.baidu.annotation.JavaConfig;
import com.baidu.input.support.precommit.IPreCommit;
import com.baidu.iptcore.util.Config;
import com.baidu.iptcore.util.CoreOnlineLog;
import com.baidu.iptcore.util.Logger;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

/**
 * 内核设置项的封装类
 * Created by ch<PERSON><PERSON><PERSON> on 18/3/18.
 */
public class CoreConfig {

    /**
     * 是否是英文的T9模式
     */
    private volatile boolean isEnT9Mode = true;
    /**
     * 是否需要拼音键盘明暗提示功能
     */
    private volatile boolean needPyMask = false;
    /**
     * 网络权限
     */
    private volatile boolean mNetPermission = true;

    @IPreCommit.PreCommitType
    private volatile int preCommitType = IPreCommit.TYPE_REPLACE_CAND_TEXT;

    /**
     * 内核日志需要的key和key的描述的映射
     * Int类型的Key不够直观，所以增加这个辅助进行日志。仅在需要打日志的情况下才会初始化
     */
    private Map<Integer, String> configKeyMap = null;

    /**
     * 设置一个Int类型的设置项给内核
     * @param key 设置项的key
     * @param value 设置项的值
     */
    public void setInt(int key, int value) {
        if (Config.enableLog()) {
            initCofigKeyMap();
            Logger.i("set: " + getConfigKey(key) + " = " + value);
        }
        if (!IptCoreInterface.get().isCoreOpened()) {
            return;
        }
        IptCoreInterface.get().setInt(key, value);
    }

    /**
     * 获取一个Int类型的设置项的值
     * @param key 设置项的key
     * @return 设置项的值
     */
    public int getInt(int key) {
        if (!IptCoreInterface.get().isCoreOpened()) {
            return 0;
        }
        return IptCoreInterface.get().getInt(key);
    }

    /**
     * 设置一个Boolean类型的设置项给内核
     * @param key 设置项的key
     * @param value 设置项的值
     */
    public void setBoolean(int key, boolean value) {
        if (Config.enableLog()) {
            initCofigKeyMap();
            Logger.i("set: " + getConfigKey(key) + " = " + value);
        }
        if (!IptCoreInterface.get().isCoreOpened()) {
            return;
        }
        IptCoreInterface.get().setBoolean(key, value);
    }

    /**
     * 获取一个Boolean类型的设置项
     * @param key 设置项的key
     * @return 设置项的值
     */
    public boolean getBoolean(int key) {
        if (!IptCoreInterface.get().isCoreOpened()) {
            return false;
        }
        return IptCoreInterface.get().getBoolean(key);
    }

    /**
     * 设置一个String类型的设置项给内核
     * @param key 设置项的key
     * @param value 设置项的值
     */
    public void setString(int key, String value) {
        if (Config.enableLog()) {
            initCofigKeyMap();
            Logger.i("set: " + getConfigKey(key) + " = " + value);
        }
        if (!IptCoreInterface.get().isCoreOpened()) {
            return;
        }
        IptCoreInterface.get().setString(key, value);
    }

    /**
     * 获取一个String类型的设置项
     * @param key 设置项的key
     * @return 设置项的值
     */
    public String getString(int key) {
        if (!IptCoreInterface.get().isCoreOpened()) {
            return "";
        }
        return IptCoreInterface.get().getString(key);
    }

    /**
     * 获取一个String类型的设置项
     * @param key 设置项的key
     * @return 设置项的值
     */
    public void setLong(int key, long value) {
        if (Config.enableLog()) {
            initCofigKeyMap();
            Logger.i("setLong: " + getConfigKey(key) + " = " + value);
        }
        if (!IptCoreInterface.get().isCoreOpened()) {
            return;
        }
        IptCoreInterface.get().setLong(key, value);
    }

    /**
     * Ai助聊json配置设定接口。内核会记忆Ai助聊配置最后一次设定信息
     * @param jsonStr 配置的json字符串(来自通知中心)
     * @param noticeKey 配置通知中心key
     */
    public void setAiWordsJson(String jsonStr, String noticeKey) {
        if (Config.enableLog() && !Config.isShortLogMode()) {
            Logger.i("setAiWordsJson:"
                    + " jsonStr: " + jsonStr
                    + " noticeKey: " + noticeKey);
        }
        if (!IptCoreInterface.get().isCoreOpened()) {
            return;
        }
        IptCoreInterface.get().setAiWordsJson(jsonStr, noticeKey);
    }

    /**
     * 设置轨迹记录的阈值，达到指定的阈值大小后，给客户端一个标志，客户端进行文件上传
     * @param base 首次进行阈值提示的大小（Byte）
     * @param increment 阈值提示的大小增量（Byte）
     */
    public void setTraceWarningSize(int base, int increment) {
        if (Config.enableLog() && !Config.isShortLogMode()) {
            Logger.i("setTraceWarningSize: base=" + base + ", increment=" + increment);
        }
        if (!IptCoreInterface.get().isCoreOpened()) {
            return;
        }
        IptCoreInterface.get().setTraceWarningSize(base, increment);
    }

    /**
     * 设置云输入地址
     */
    public void setCloudAddress(String cloudHttpHost, int cloudHttpPort,
                                String cloudUdpHost, int cloudUdpPort,
                                String sugHttpHost, int sugHttpPort,
                                String sugUdpHost, int sugUdpPort,
                                String nlpHttpHost, int nlpHttpPort,
                                boolean cloudUseUdp, boolean sugUseUdp) {
        if (Config.enableLog() && !Config.isShortLogMode()) {
            Logger.i(Logger.TAG, "setCloudAddress: cloudHttp=%s(%d)", cloudHttpHost, cloudHttpPort);
            Logger.i(Logger.TAG, "setCloudAddress: cloudUdp=%s(%d)", cloudUdpHost, cloudUdpPort);
            Logger.i(Logger.TAG, "setCloudAddress: sugHttp=%s(%d)", sugHttpHost, sugHttpPort);
            Logger.i(Logger.TAG, "setCloudAddress: sugUdp=%s(%d)", sugUdpHost, sugUdpPort);
            Logger.i(Logger.TAG, "setCloudAddress: nlpHttpHost=%s(%d)", nlpHttpHost, nlpHttpPort);
            Logger.i(Logger.TAG, "setCloudAddress: cloudUseUdp=" + cloudUseUdp
                    + ", sugUseUdp=" + sugUseUdp);
        }
        if (!IptCoreInterface.get().isCoreOpened()) {
            return;
        }
        String[] hosts = new String[] {cloudHttpHost, cloudUdpHost, sugHttpHost, sugUdpHost, nlpHttpHost};
        int[] ports = new int[] {cloudHttpPort, cloudUdpPort, sugHttpPort, sugUdpPort, nlpHttpPort};

        IptCoreInterface.get().setCloudAddress(hosts, ports, cloudUseUdp, sugUseUdp);
    }

    /**
     * 设置网络权限
     * @param hasNetPermission 是否有cta权限
     */
    public void setNetPermission(boolean hasNetPermission) {
        this.mNetPermission = hasNetPermission;
    }

    public boolean getNetPermission() {
        return mNetPermission;
    }

    /**
     * 获取当前是否是英文T9模式
     */
    @JavaConfig
    public final boolean getIsEnT9Mode() {
        return isEnT9Mode;
    }

    /**
     * 设置当前是否是英文T9模式
     */
    @JavaConfig
    public final void setIsEnT9Mode(boolean isT9) {
        isEnT9Mode = isT9;
    }

    /**
     * 获取当前是否需要拼音键盘明暗提示功能
     */
    @JavaConfig
    public final boolean getNeedPyMask() {
        return needPyMask;
    }

    /**
     * 设置当前为是否需要拼音键盘明暗提示功能
     */
    @JavaConfig
    public final void setNeedPyMask(boolean needMask) {
        needPyMask = needMask;
    }

    /**
     * 初始化日志记录模块
     */
    private void initCofigKeyMap() {
        if (configKeyMap == null) {
            synchronized (this) {
                if (configKeyMap == null) {
                    configKeyMap = getConfigKeyMap();
                }
            }
        }
    }

    /**
     * 初始化日志记录的Map
     */
    private Map<Integer, String> getConfigKeyMap() {
        if (!Config.enableLog()) {
            // 不打印日志的时候，不应该初始化config map。所以这里简单地返回null
            return null;
        }

        try {
            Field[] fields = ConfigKey.class.getFields();
            return new HashMap<Integer, String>() {
                {
                    for (Field field : fields) {
                        String name = field.getName();
                        int value = field.getInt(null);
                        this.put(value, name);
                    }
                }
            };
        } catch (Throwable e) {
            // do nothing
        }

        return new HashMap<>();
    }

    /**
     * 把Int类型的Key转换成容易识别的String
     */
    private Object getConfigKey(int key) {
        if (configKeyMap == null || Config.isShortLogMode()) {
            return key;
        }

        String keyString = configKeyMap.get(key);
        return TextUtils.isEmpty(keyString) ? key : keyString;
    }
    public void setPadSymExtAutoReturn(boolean enable) {
        if (Config.enableLog()) {
            Logger.v("setPadSymExtAutoReturn: " + enable);
        }
        if (!IptCoreInterface.get().isCoreOpened()) {
            return;
        }
        IptCoreInterface.get().setPadSymExtAutoReturn(enable);
    }

    /**
     * 设置用户当前所在场景组
     * @param groupIDs
     */
    public void setSceneGroupIDs(long[] groupIDs) {
        if (Config.enableLog()) {
            Logger.v("setSceneGroupIDs, size: " + groupIDs.length);
        }
        if (!IptCoreInterface.get().isCoreOpened()) {
            return;
        }
        IptCoreInterface.get().setSceneGroupIDs(groupIDs);
    }

    /**
     * 设置是否在Cand条上显示候选词类型
     * @param enable 是否显示候选词类型
     */
    public void setDisplayCandType(boolean enable) {
        if (Config.enableLog()) {
            Logger.v("setDisplayCandType: " + enable);
        }
        if (!IptCoreInterface.get().isCoreOpened()) {
            return;
        }
        IptCoreInterface.get().setDisplayCandType(enable);
    }

    public boolean getPadSymExtAutoReturn() {
        if (Config.enableLog()) {
            Logger.v("getPadSymExtAutoReturn");
        }
        if (!IptCoreInterface.get().isCoreOpened()) {
            return false;
        }
        return IptCoreInterface.get().getPadSymExtAutoReturn();
    }

    /**
     *
     * @return 是否在Cand条上显示候选词类型
     */
    public boolean getDisplayCandType() {
        if (Config.enableLog()) {
            Logger.v("getDisplayCandType");
        }
        if (!IptCoreInterface.get().isCoreOpened()) {
            return false;
        }
        return IptCoreInterface.get().getDisplayCandType();
    }

    /**
     * 获取预上屏的策略类型
     *
     * @return 预上屏的策略类型
     * @see IPreCommit#TYPE_REPLACE_CAND_TEXT
     * @see IPreCommit#TYPE_SET_COMPOSING_TEXT
     */
    @IPreCommit.PreCommitType
    public int getPreCommitType() {
        return preCommitType;
    }

    /**
     * 设置预上屏的策略类型
     *
     * @param preCommitType 类型
     * @see IPreCommit#TYPE_REPLACE_CAND_TEXT
     * @see IPreCommit#TYPE_SET_COMPOSING_TEXT
     */
    public void setPreCommitType(@IPreCommit.PreCommitType int preCommitType) {
        this.preCommitType = preCommitType;
    }

    /**
     * 设置上传数据地址
     */
    public void setUplDataAddress(String address, int port) {
        if (Config.enableLog()) {
            Logger.v("setUplDataAddress: " + address);
        }
        if (!IptCoreInterface.get().isCoreOpened()) {
            return;
        }
        IptCoreInterface.get().setUplDataAddress(address, port);
    }

    public void setOnlineLogLevel(@NonNull CoreOnlineLog logLevel) {
        if (Config.enableLog()) {
            Logger.v("setOnlineLogLevel: " + logLevel.getLogValue());
        }
        if (!IptCoreInterface.get().isCoreOpened()) {
            return;
        }
        IptCoreInterface.get().setOnlineLogLevel(logLevel.getLogValue());
    }

}
