package com.baidu.iptcore;

import com.baidu.iptcore.info.IptCoreCandInfo;
import com.baidu.iptcore.info.IptCoreDutyInfo;

/**
 * ImeCoreManager对外的duty回调接口
 * 
 * Created by ch<PERSON><PERSON><PERSON> on 17/8/10.
 */
public interface DutyCallback {
    
    /**
     * 收到duty消息的回调
     * @param dutyInfo 封装上层需要处理的事件
     */
    void onDutyInfo(IptCoreDutyInfo dutyInfo);

    void onHonorNotepadGetResponse(String response, int ecode);
}
