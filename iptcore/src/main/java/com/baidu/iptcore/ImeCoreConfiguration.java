package com.baidu.iptcore;

import android.content.Context;
import android.content.pm.PackageInfo;

import com.baidu.iptcore.interceptor.IInputInterceptor;
import com.baidu.iptcore.util.IDictionaryCopy;
import com.baidu.iptcore.util.IDictionaryInstaller;
import com.baidu.iptcore.util.LogAdapter;
import com.baidu.iptcore.util.MethodTraceLogAdapter;
import com.baidu.iptcore.util.NetworkLogAdapter;

import java.util.ArrayList;
import java.util.List;

/**
 * 对输入法内核的配置
 * 
 * Created by ch<PERSON><PERSON><PERSON> on 17/7/28.
 */
public class ImeCoreConfiguration {

    final String dictionaryPath;
    public final int logLovel;
    public final boolean enableLog;
    public final boolean traceOpen;
    public final boolean disableLogEncode;
    public final boolean enableNetworkLog;
    public final boolean enableProfiler;
    public final boolean enableCoreTaskStatistics;
    public LogAdapter logAdapter;
    public MethodTraceLogAdapter methodTraceLogAdapter;
    public NetworkLogAdapter networkLogAdapter;
    public boolean isTestUrl;
    public boolean isPureMode;
    public boolean shortLogMode;
    public int flavor;
    ImePlatformEnv platformEnv;
    public PackageInfo packageInfo;
    DutyCallback imeCoreCallback;
    Context context;
    List<IInputInterceptor> interceptors;
    IDictionaryInstaller dictionaryInstaller;
    IDictionaryCopy dictionaryCopy;

    /**
     * 构造器
     */
    private ImeCoreConfiguration(Builder builder) {
        dictionaryPath = builder.dictionaryPath;
        platformEnv = builder.platformEnv;
        imeCoreCallback = builder.imeCoreCallback;
        context = builder.context;
        logLovel = builder.logLevel;
        enableLog = builder.enableLog;
        disableLogEncode = builder.disableLogEncode;
        traceOpen = builder.traceOpen;
        enableNetworkLog = builder.enableNetworkLog;
        enableProfiler = builder.enableProfiler;
        interceptors = builder.interceptors;
        dictionaryInstaller = builder.dictionaryInstaller;
        dictionaryCopy = builder.dictionaryCopy;
        packageInfo = builder.packageInfo;
        isTestUrl = builder.isTestUrl;
        isPureMode = builder.isPureMode;
        shortLogMode = builder.shortLogMode;
        flavor = builder.flavor;
        logAdapter = builder.logAdapter;
        networkLogAdapter = builder.networkLogAdapter;
        methodTraceLogAdapter = builder.methodTraceLogAdapter;
        enableCoreTaskStatistics = builder.coreTaskStatistics;
    }

    /**
     * 生成器
     */
    public static class Builder {

        public boolean isPureMode;
        private boolean isTestUrl;
        private int flavor = -1;
        private String dictionaryPath;
        private int logLevel = ImeCoreConsts.LOG_LEVEL_ALL;
        private ImePlatformEnv platformEnv;
        private DutyCallback imeCoreCallback;
        private Context context;
        private boolean enableLog;
        private boolean disableLogEncode = true;
        public boolean traceOpen;
        private boolean enableNetworkLog;
        private boolean enableProfiler;
        private boolean shortLogMode;
        private LogAdapter logAdapter;
        private MethodTraceLogAdapter methodTraceLogAdapter;
        private NetworkLogAdapter networkLogAdapter;
        private List<IInputInterceptor> interceptors;
        private IDictionaryInstaller dictionaryInstaller;
        private IDictionaryCopy dictionaryCopy;
        private PackageInfo packageInfo;
        private boolean coreTaskStatistics;

        /**
         * 设置字典的路径
         */
        public Builder dictionaryPath(String dictionaryPath) {
            this.dictionaryPath = dictionaryPath;
            return this;
        }

        /**
         * 设置日志打印的等级。该值传递给内核，内核会避免字符串拼接等操作
         * {@link ImeCoreConsts#LOG_LEVEL_ALL}等
         */
        public Builder setLogLevel(int logLevel) {
            this.logLevel = logLevel;
            return this;
        }

        /**
         * 设置平台相关的回调
         */
        public Builder platformEnv(ImePlatformEnv platformEnv) {
            this.platformEnv = platformEnv;
            return this;
        }

        /**
         * 设置ImeCore的回调
         */
        public Builder imeCoreCallback(DutyCallback callback) {
            this.imeCoreCallback = callback;
            return this;
        }

        /**
         * 设置context
         */
        public Builder context(Context context) {
            this.context = context;
            return this;
        }

        /**
         * 设置是否打印日志
         */
        public Builder enableLog(boolean enableLog) {
            this.enableLog = enableLog;
            return this;
        }

        /**
         * 设置是否取消日志加密
         */
        public Builder disableLogEncode(boolean disable) {
            this.disableLogEncode = disable;
            return this;
        }

        /**
         * 设置是否开启内核接口耗时监控
         */
        public Builder openTrace(boolean enable) {
            this.traceOpen = enable;
            return this;
        }

        /**
         * 设置是否打印网络日志
         */
        public Builder enableNetworkLog(boolean enableLog) {
            this.enableNetworkLog = enableLog;
            return this;
        }

        /**
         * 设置是否开启profiler日志记录
         */
        public Builder enableProfiler(boolean enableProfiler) {
            this.enableProfiler = enableProfiler;
            return this;
        }

        /**
         * 日志打印适配器
         */
        public Builder logAdapter(LogAdapter logAdapter) {
            this.logAdapter = logAdapter;
            return this;
        }

        /**
         * 内核接口耗时信息打印适配器
         */
        public Builder methodTraceLogAdapter(MethodTraceLogAdapter logAdapter) {
            this.methodTraceLogAdapter = logAdapter;
            return this;
        }

        /**
         * 网络请求信息打印适配器
         */
        public Builder networkLogAdapter(NetworkLogAdapter logAdapter) {
            this.networkLogAdapter = logAdapter;
            return this;
        }

        /**
         * 添加输入的拦截器
         */
        public Builder addInterceptors(List<IInputInterceptor> interceptors) {
            if (interceptors != null) {
                if (this.interceptors == null) {
                    this.interceptors = new ArrayList<>();
                }
                this.interceptors.addAll(interceptors);
            }
            return this;
        }

        /**
         * 添加输入的拦截器
         */
        public Builder addInterceptor(IInputInterceptor interceptor) {
            if (interceptor != null) {
                if (this.interceptors == null) {
                    this.interceptors = new ArrayList<>();
                }
                this.interceptors.add(interceptor);
            }
            return this;
        }

        /**
         * 设置词典安装工具
         */
        public Builder dictionaryInstaller(IDictionaryInstaller installer) {
            this.dictionaryInstaller = installer;
            return this;
        }

        public Builder dictionaryCopy(IDictionaryCopy dictionaryCopy) {
            this.dictionaryCopy = dictionaryCopy;
            return this;
        }

        /**
         * 添加包信息用于签名校验
         */
        public Builder packageInfo(PackageInfo info) {
            this.packageInfo = info;
            return this;
        }

        /**
         * 是否是沙盒环境
         */
        public Builder isTestUrl(boolean isTestUrl) {
            this.isTestUrl = isTestUrl;
            return this;
        }

        /**
         * 是否是纯净模式
         */
        public Builder isPureMode(boolean isPureMode) {
            this.isPureMode = isPureMode;
            return this;
        }

        /**
         * 是否是精简日志模式
         */
        public Builder shortLogMode(boolean shortLogMode) {
            this.shortLogMode = shortLogMode;
            return this;
        }

        /**
         * 设置产品。参考{@link ImeCoreConsts.Flavor}
         */
        public Builder flavor(int flavor) {
            this.flavor = flavor;
            return this;
        }

        public Builder enableCoreTaskStatistics(boolean enable) {
            this.coreTaskStatistics = enable;
            return this;
        }

        public ImeCoreConfiguration build() {
            if (flavor < 0) {
                throw new IllegalArgumentException("You must set flavor first!");
            }
            return new ImeCoreConfiguration(this);
        }
    }

}
