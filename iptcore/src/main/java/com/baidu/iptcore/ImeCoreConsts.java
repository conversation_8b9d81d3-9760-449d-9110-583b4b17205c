package com.baidu.iptcore;

/**
 * 内核对外常量
 * 
 * Created by z<PERSON><PERSON><PERSON><PERSON> on 2017/8/3.
 */
public interface ImeCoreConsts extends ImeCoreInternalConsts  {
    /**
     * 内核文件的文件名,在libs下去除lib开头，去除so结尾的名字
     */
    String FILE_DATA_IPTCORE_FILE_NAME = "iptcore";

    /**
     * 预置的字典在assert下的路径名
     */
    String ASSERT_DIR_DICT_NAME = "dict";

    // 输入法内核文件的索引
    @Deprecated
    byte IPTDICT_INDEX_HZ = 0;
    byte IPTDICT_INDEX_UZ = 1;
    byte IPTDICT_INDEX_CELL = 2;
    byte IPTDICT_INDEX_FT = 3;
    byte IPTDICT_INDEX_BH = 4;
    byte IPTDICT_INDEX_UE = 5;
    byte IPTDICT_INDEX_SYMBIN = 6;
    byte IPTDICT_INDEX_SYMLBIN = 7;
    /** 个性短语对应的配置文件 phrase.bin */
    byte IPTDICT_INDEX_CP = 8;
    /** 联系人文件 */
    byte IPTDICT_INDEX_CATE = 9;
    byte IPTDICT_INDEX_DEF = 10;
    /** 五笔86模式 */
    byte IPTDICT_INDEX_WB86 = 11;
    /**  英文词库 en2.bin合并到英文词典en5.bin */
    @Deprecated
    byte IPTDICT_INDEX_EN = 12;
    byte IPTDICT_INDEX_GRAM = 13;
    /** 输入时会触发多媒体搜索表情的关键字 */
    byte IPTDICT_INDEX_KEYWORD = 14;
    /** 根据ttf过滤的文件 */
    byte IPTDICT_INDEX_TTFFILTER = 15;
    /** 手写自造次 */
    byte IPTDICT_INDEX_USERWORD_HW = 16;
    /** 火星文对应关系 */
    byte IPTDICT_INDEX_MARSWORD = 17;
    /** 其它特殊的对应关系 */
    byte IPTDICT_INDEX_OTHERWORD = 18;
    // added for zhuyin-cangjie IPTDICT_INDEX_CANGJIE -> IPTCORE_INDEX_CJ
    byte IPTDICT_INDEX_CANGJIE = 19;
    @Deprecated
    byte IPTDICT_INDEX_ZHUYIN_HZ = 20;
    @Deprecated
    byte IPTDICT_INDEX_ZHUYIN_CZ = 21;
    /** 拼音纠正文件 */
    @Deprecated
    byte IPTDICT_INDEX_CH_COR = 22;
    /** 盲打拼音模板 */
    @Deprecated
    byte IPTDICT_INDEX_KP = 23;
    /** 手写汉字模板文件 */
    byte IPTDICT_INDEX_WT = 24;
    /** 歇后语模板文件 */
    byte IPTDICT_INDEX_XHY = 25;
    /** 手写部首筛选模板文件 */
    byte IPTDICT_INDEX_WT_BS = 26;
    /** 场景化模板文件 */
    byte IPTDICT_INDEX_HZ_LABEL = 27;
    /** 颜文字id映射文件 */
    byte IPTDICT_INDEX_OT_IDMAP = 28;
    /** 云输入关键字文件 */
    byte IPTDICT_INDEX_CLOUD_KEYWORD = 29;
    /** 候选字上下文文件 */
    byte IPTDICT_INDEX_CAND_CONTEXT = 30;
    /** vkword文件 */
    byte IPTDICT_INDEX_VKWORD = 31;
    /** search文件 */
    byte IPTDICT_INDEX_SEARCH = 32;
    /** 汉字音调文件 */
    @Deprecated
    byte IPTDICT_INDEX_HZ_TONE = 33;
    /** 智能问答文件 */
    byte IPTDICT_INDEX_AUTOREPLY = 34;
    /** 注音自造词**/
    byte IPTDICT_INDEX_ZY_USER = 35;
    /** 所有用户数据备份 */
    byte IPTDICT_INDEX_US_BAK = 36;
    /** 系统词库 */
    byte IPTDICT_INDEX_CZ = 37;
    /** 动态热区条件概率文件 */
    @Deprecated
    byte IPTDICT_INDEX_LTP = 38;
    /** 个性化动态热区文件 */
    byte IPTDICT_INDEX_LTP_USR = 39;
    /** app名字和框属性对应场景ID和词库文件 */
    byte IPTDICT_INDEX_APP_MAP = 40;
    /** 省份城市 */
    byte IPTDICT_INDEX_PROV_CITY = 41;
    /** 用户的语音纠错文件 */
    byte IPTDICT_INDEX_VOICE_CORRECT = 42;
    /** 本地联想运营词 */
    byte IPTDICT_INDEX_SPECIAL = 43;
    /** 五笔98模式 */
    byte IPTDICT_INDEX_WB98 = 44;
    /** 仓颉速成模式 */
    byte IPTDICT_INDEX_CANGJIE_QUICK = 45;
    /** 中文自造词新 */
    byte IPTDICT_INDEX_USR3USER = 46;
    /** 新的语音语调文件 */
    @Deprecated
    byte IPTCORE_INDEX_HZCORRTONE = 47;
    /** 新的英文自造词文件 */
    byte IPTCORE_INDEX_ENUSER = 48;
    /** 新的防误触模型 */
    byte IPTCORE_INDEX_PY3TRANSPROB = 49;
    /** 用户符号文件 */
    byte IPTCORE_INDEX_SYM_USER = 50;
    /** 用户细胞词文件 */
    byte IPTCORE_INDEX_USR3CELL = 51;
    /** 注音字典文件 */
    byte IPTCORE_INDEX_ZY = 52;
    /**
     * 英文系统词库文件
     */
    byte IPTDICT_INDEX_EN_SYS = 53;
    /**
     * 纠错词库索引
     */
    byte IPTDICT_INDEX_CORRECTION = 54;

    /**
     * 内置敏感词
     */
    byte IPTDICT_INDEX_SENSITIVE = 55;
    /**
     * 内置汉字拼音纠错文件
     */
    byte IPTDICT_INDEX_PY_CORR_TONE = 56;
    /**
     * 盲人辅助朗读词典，重构后的，重构之前为cxt.bin
     */
    byte IPTDICT_INDEX_BLIND_ASSISTANCE = 57;

    /**
     * 手写证书文件 hw_lic7.bin
     */
    byte IPTDICT_INEDEX_HW_LICENSE = 58;
    /**
     * 内置的敏感词过滤词库, sens.bin
     */
    byte IPTDICT_INDEX_SENS = 59;
    /**
     * oppo内置的手写笔势识别模型， hw_gesture.bin
     */
    byte IPTDICT_INDEX_HW_GESTURE = 60;

    /** 内核文件长度 */
    byte IPTDICT_LENGTH = IPTDICT_INDEX_HW_GESTURE + 1;

    /** 汉字字库 hz2.bin合并到cz5.bin*/
    @Deprecated
    String IPTCORE_DICT_HZ = "hz2.bin"; // 客户端原来更名为Hz.bin
    /** 中文自造词 */
    String IPTCORE_DICT_UZ = "usr2.bin"; // 客户端原来更名为Uz2.bin
    /** 细胞词库 */
    String IPTCORE_DICT_CELL = "Cell.bin";
    /** 繁体字字库ft2.bin + fj2.bin -> fanti3.bin */
    String IPTCORE_DICT_FT = "fanti3.bin"; // 客户端原来更名为Ft.bin
    /** 笔画查询 */
    String IPTCORE_DICT_BH = "bh4.bin"; // 客户端原来更名为bh2.bin
    /** 英文自造词 */
    String IPTCORE_DICT_UE = "ue2.bin";
    /** 符号列表 sym2.bin -> ipt3sym_sys.bin*/
    String IPTCORE_DICT_SYM = "ipt3sym_sys.bin";
    /** 符号联想 */
    String IPTCORE_DICT_SYML = "sylian.bin"; // 客户端原来更名为syml.bin
    /** 个性短语 */
    String IPTCORE_DICT_PHRASE = "phrase3.bin"; // phrase.bin -> phas_phrase.bin -> phrase3.bin
    /** 联系人词库 */
    String IPTCORE_DICT_CONTACT = "ctat_contact.bin"; // 旧内核原来是contact.bin
    /** 自定义输入方案 */
    String IPTCORE_DICT_DEF = "def.bin";
    /** 五笔输入方案 wb2.bin -> wb3.bin -> wb486.bin */
    String IPTCORE_DICT_WB86 = "wb486.bin"; // 客户端原来更名为Wb.bin
    /** 英文词库 en2.bin合并到英文词典en5.bin*/
    @Deprecated
    String IPTCORE_DICT_EN = "en2.bin";
    /** 新内核的英文自造词文件 */
    String IPTCORE_DICT_ENUSER = "en_user.bin";
    /** 三维词库，文件太大，预装的话使用图片后缀名 */
    String IPTCORE_DICT_GRAM = "gram.bin";
    /** 关键字词库 */
    String IPTCORE_DICT_KEYWORD = "keyword.bin";
    /** 根据ttf过滤的文件 */
    String IPTCORE_DICT_TTFFILTER = "ttffilter.ini";
    /** 手写自造词词库 */
    String IPTCORE_DICT_UZW = "Uzw.bin";
    /** 火星文 */
    String IPTCORE_DICT_HUOXINGWEN = "huoxingwen.bin";
    /** 其他关键字对应关系 ，例如卖萌文 */
    String IPTCORE_DICT_OTHERWORD = "otherword.bin";
    /** 仓颉 */
    String IPTCORE_DICT_CANGJIE = "cj2dict.bin";
    /** 注音字典  cz_zy.bin + cz_zy.bin -> py3zhuyin.bin*/
    String IPTCORE_DICT_ZY = "py3zhuyin.bin";
    /** 注音 字库 */
    @Deprecated
    String IPTCORE_DICT_HZ_ZY = "hz_zy.bin";
    @Deprecated
    String IPTCORE_DICT_CZ_ZY = "cz_zy.bin";
    /** 拼音纠正文件 */
    String IPTCORE_DICT_CH_COR = "ch_cor2.bin";
    /** 盲打拼音模板 */
    String IPTCORE_DICT_KP = "kp.bin";
    /** 手写汉字模板文件 */
    // 客户端原来叫为iptwt_20151202.bin，对应内核的hzmdl_dnn.bin
    // 后改成hzmdl_rnn.bin, 5g版本内置手写模型文件 hzmdl_rnn.bin -> hw6.bin
    String IPTCORE_DICT_WT = "hw7.bin";

    /**
     * 手写证书文件 hw_lic7.bin
     */
    String IPTCORE_DICT_HW_LICENSE = "hw_lic7.bin";

    /**
     * 手写笔势识别
     */
    String IPTCORE_DICT_HW_GESTURE = "hw_gesture.bin";

    /**
     * 生僻字部首拼音拆字文件hanzi_pzi.bin (后下发的，这里只记录下词库文件名字)
     */
    String IPTCORE_DICT_HANZI_PZI = "hanzi_pzi.bin";

    /**
     * 生僻字手写文件（支持级别3的生僻字手写）(后下发的，这里只记录下文件词库名字)
     */
    String IPTCORE_DICT_HW_RARE = "hw_rare.bin";


    /**
     * 用户自造词：生僻字次 cand、生僻字自造、联想记录
     */
    String IPTCORE_DICT_RARE_USR = "rare_usr.bin";


    /** 歇后语模板文件 xhy.bin -> xhy2.bin */
    String IPTCORE_DICT_XHY = "xhy2.bin";
    /** 手写部首筛选模板文件 */
    String IPTCORE_DICT_WT_BS = "wt_bs.bin";
    /** 云输入关键字文件 */
    String IPTCORE_DICT_CLOUD_KEYWORD = "cloud_keyword.bin";
    /** 盲人辅助下载文件 */
    @Deprecated
    String IPTCORE_DICT_CXT = "cxt.bin";
    /** v-k文件 */
    String IPTCORE_DICT_VKWORD = "vkword.bin";
    /** search文件 */
    String IPTCORE_DICT_SEARCH = "search.bin";
    /** 汉字场景化文件 */
    @Deprecated
    String IPTCORE_DICT_HZ_LABEL = "hz_label.bin";
    /** 颜文字id映射文件 */
    String IPTCORE_DICT_IDMAP = "idmap.bin";
    /** 汉字音调文件 */
    String IPTCORE_DICT_HZ_TONE = "hz_tone.bin";
    /** 智能问答文件 */
    String IPTCORE_DICT_AUTOREPLY = "autoreply.bin";
    /** 注音自造词文件 */
    String IPTCORE_DICT_CH_ZY_USER = "ch_zy_usr.bin";
    /** 所有用户数据备份 */
    String IPTCORE_DICT_US_BAK = "us_bak.bin";
    /** 系统词库 cz3.bin -> cz5.bin*/
    String IPTCORE_DICT_CZ = "cz5.bin";
    /** 动态热区条件概率文件 */
    String IPTCORE_DICT_LTP = "ltp.bin";
    /** 用户误触个性化文件 */
    String IPTCORE_DICT_USR_TOUCH_FILE = "usr_touch_file.bin";
    /** app名字和框属性对应场景ID和词库文件 */
    String IPTCORE_DICT_APP_MAP = "app_map2.bin"; // 客户端原来更名为 app_map_file.bin
    /** 省份城市词库 */
    String IPTCORE_DICT_PROV_CITY = "prov_city.bin";
    /** 用户的语音纠错文件 */
    String IPTCORE_DICT_VOICE_CORRECT = "usr_voice_correct_file.bin";
    /** 本地联想运营词 */
    String IPTCORE_DICT_SPECIAL = "special.bin";
    /** 英文系统词库文件 en_sys.bin -> en5.bin*/
    String IPTCORE_DICT_EN_SYS = "en5.bin";
    /** 98五笔词库 wb298.bin -> wb398.bin -> wb498.bin */
    String IPTCORE_DICT_WB98 = "wb498.bin"; // 客户端原来更名为Wb98.bin
    /** 仓颉速成文件*/
    String IPTCORE_DICT_CANGJIE_QUICK = "cj2dict_quick.bin";
    /** 中文自造词新 */
    String IPTCORE_DICT_USR3USER = "usr3user.bin";
    /** 新的语音语调文件 */
    String IPTCORE_DICT_HZCORRTONE = "hz_corr_tone.bin";
    /** 新的防误触模型 */
    String IPTCORE_DICT_PY3TRANSPROB = "py3transprob.bin";
    /** 用户符号文件 */
    String IPTCORE_DICT_SYM_USER = "sym_usr.bin";
    /** 细胞词 */
    String IPTCORE_DICT_USER3CELL = "usr3cell.bin";
    /** 旧内核的个性短语文件 */
    String IPTCORE_DICT_PHRASE_OLD = "phrase.bin";

    /** 个性短语的plus文件 */
    String INSTALL_FILE_PHRASE_PLUS = "phrase_plus.ini";
    /** 快速输入的补丁文件 */
    String INSTALL_FILE_FASTINPUT = "fast_input.kwd";
    /** 直达号的补丁文件（废弃） */
    @Deprecated
    String INSTALL_FILE_ZHIDAHAO = "zdh.kwd";
    /** 快速输入的补丁文件 */
    String INSTALL_FILE_MULTIMEDIA = "media.kwd";
    /** 扩展表情的不定文件  */
    String INSTALL_FILE_EMOJI_EXTEND = "emojiextend.kwd";
    /** 二次元数据文件，NIJIGEN为二次元日语的发音写法 */
    String INSTALL_FILE_NIJIGEN = "nijigen.kwd";
    /** 语音emoji联想文件 */
    String INSTALL_FILE_VOICE_EMOJI = "emoji_voice.kwd";
    /** 语音颜文字联想文件 */
    String INSTALL_FILE_VOICE_YAN = "emoticon_voice.kwd";
    /** 表情的补丁文件 */
    String INSTALL_FILE_EMOJI = "emoji.kwd";
    /** 颜文字联想文件 */
    String INSTALL_FILE_YAN = "emoticon.kwd"; // 客户端原来更名为yan.kwd
    /** 颜文字联想文件 */
    String INSTALL_FILE_YAN_IDM = "idmap.idm"; // 客户端原来更名为yan.idm
    /** 分类词库：成语俗语 */
    @Deprecated
    String INSTALL_FILE_CELL1 = "1.bcd";
    /** 邮件文件 */
    @Deprecated
    String INSTALL_FILE_EMAIL = "email.txt"; // 客户端原来更名为 email
    /** 默认的26双拼方案文件 */
    String SYS_FILE_SP26 = "sp26.ini";
    /** 联想符号功能 */
    String BAYESIAN_SYLIAN = "bayesian_sylian.bin";
    /** 内核预置的后置词文件，本地黑白名单词典文件 */
    String BLACK_WHITE_DICT = "black_white_dict.bin";
    /** black_white_dict.bin的PB备份 */
    String BLACK_WHITE_LIST = "black_white_list.bin";
    /** 盲人辅助朗读词典，重构后的，重构之前为cxt.bin */
    String BLIND_ASSISTANCE = "blind_assistance.bin";
    /** 内核预置的云输入黑名单词典文件 */
    String CLOUD_BLACK_DICT = "cloud_black_dict.bin";
    /** cloud_black_dict.bin的PB备份 */
    String CLOUD_BLACK_LIST = "cloud_black_list.bin";
    /** 汉字音调拼音纠错文件 ch_cor2.bin -> hz_corr_tone.bin */
    String PY_CORR_TONE = "py3corr2_tone.bin";
    /** 人名模式 */
    String NAME_MODE = "name_mode.bin";
    /** 专门用于小米13以上，显示小米logo的快速输入词典 */
    String INSTALL_FILE_FASTINPUT_MI = "fast_input_miui13.kwd";

     /** 下发文件名常量。下发文件没有对应的IPTDICT_INDEX_XX序号，不会添加到{@link IptCoreInterface#iptFiles}数组中，恢复默认词库也不会清除 */
    /** 英文系统词库下发文件 en_sys_down.bin -> en5down.bin */
    String IPTCORE_DICT_EN_SYSDOWN = "en5down.bin";
    /** cz3down文件 -> cz5down */
    String IPTCORE_DICT_CZ_DOWN = "cz5down.bin";
    /** 下发的滑行输入模型文件，模型升级后变为slide2.bin */
    @Deprecated
    String IPTCORE_DICT_SLIDE_DOWN = "slidedown.bin";
    /** 下发的更新后的滑行输入模型文件 */
    String IPTCORE_DICT_SLIDE_2 = "slide2.bin";
    /** NNRank的config文件 */
    String IPTCORE_DICT_NNRANK_CONFIG = "nn_ranker_config.bin";
    /** NNRank的model文件 */
    String IPTCORE_DICT_NNRANK_MODEL = "nn_ranker_model.nb";
    /** NNRank的data文件 */
    String IPTCORE_DICT_NNRANK_DATA = "nn_ranker_word.data";
    /**
     * 5G版本注音自造词库
     */
    String IPTCORE_DICT_USR_ZY = "usr3zy.bin";
    /**
     * 后下发手写模型
     */
    String IPTCORE_DICT_HW_DOWN = "hw4down.bin";  // 后下发手写模型文件
    /**
     * 纠错词典
     */
    String IPTCORE_DICT_CORRECTION = "iec3dict.bin";
    /**
     * 内置敏感词词库
     */
    @Deprecated
    String IPTCORE_DICT_SENSITIVE = "cloud_black_filter.bin";
    /**
     * 后下发敏感词词库， 词库安装后生成cloud_black_filter.bin
     */
    String IPTCORE_DICT_SENSITIVE_DOWN = "sensitive_dict_down";
    /**
     * 内置的敏感词过滤词库，约65K
     */
    String IPTCORE_DICT_SENS = "sens.bin";

    /** 品牌词文件名 */
    String OTHER_WORD = "otherword.txt";

    /**
     * 文心的模型文件，后下发
     */
    String IPTCORE_DICT_WENXIN_MODEL = "celm.bin";

    /** 内核网络日志前缀 */
    String NET_LOG_PREFIX = "net_log:";

    String LOG_TAG = "iptcore";

    /**
     * 空文件的大小，小于或等于此文件的大小不是标准的联系人文件
     */
    int EMPTY_CONTACT_FILE_LENGH = 2192;

    /**
     * 最长的拼音码长度,对应于64个unicode
     */
    int MAX_SHOWINFO_LEN = 512;

    /**
     * 滑行输入模型最低支持的版本号
     */
    int SLIDE_MIN_COMPATIBLE_VERSION = 0x20000;

    // ///////////////////
    /** 英文大小写类型 */
    /**
     * 普通状态
     */
    public static final int CFG_ENCASE_NORMAL = 0;
    /**
     * 首字母大写状态
     */
    public static final int CFG_ENCASE_FIRST = 1;
    /**
     * 全部字母大写状态
     */
    public static final int CFG_ENCASE_ALL = 2;

    // ///////////////////
    /**
     * 完全关闭网址邮箱符号输入
     */
    public static final int CFG_URLEMAIL_OFF = 0;
    /**
     * 手动输入模式
     */
    public static final int CFG_URLEMAIL_MANUAL = 1;
    /**
     * 自动输入模式
     */
    public static final int CFG_URLEMAIL_AUTO = 2;

    // ///////////////////
    /** 英文排序类型 */
    /**
     * 按英文词的频率排序
     */
    public static final int CFG_ENSORT_BYFREQ = 0;
    /**
     * 按英文词的长度排序
     */
    public static final int CFG_ENSORT_BYLEN = 1;
    /**
     * 按英文词的字母顺序排序
     */
    public static final int CFG_ENSORT_BYABC = 2;

    /** 退格 */
    public static final int FKEY_BACK = 0xE000;
    /** 清除 */
    public static final int FKEY_CLEAN = FKEY_BACK + 1;
    /** 空格 */
    public static final int FKEY_SPACE = FKEY_CLEAN + 1;
    /** 回车 */
    public static final int FKEY_ENTER = FKEY_SPACE + 1;
    /** 返回 */
    public static final int FKEY_RETURN = FKEY_ENTER + 1;
    /** shift，切换至英文暂态大写 */
    public static final int FKEY_SHIFT = FKEY_RETURN + 1;
    /** Shift，切换至英文大写 */
    public static final int FKEY_SHIFT_CAPLOCK = FKEY_SHIFT + 1;
    /** 切换到更多候选 */
    public static final int FKEY_MORECAND = FKEY_SHIFT_CAPLOCK + 1;
    /** 分隔符 */
    public static final int FKEY_DELIMITER = FKEY_MORECAND + 1;
    /** 中文切换到英文 */
    public static final int FKEY_ABC = 0xE000 + 64;
    /** 切换到数字 */
    public static final int FKEY_123 = FKEY_ABC + 1;
    /** 切换到符号 */
    public static final int FKEY_SYM = FKEY_123 + 1;
    /** 地球键（ios） */
    public static final int FKEY_MENU = FKEY_SYM + 1;
    /** 英文切换到中文 */
    public static final int FKEY_CN = FKEY_MENU + 1;
    /** 符号面板下的锁定功能键 */
    public static final int FKEY_LOCK = FKEY_CN + 1;
    /** 符号面板上翻页 */
    public static final int FKEY_UP = FKEY_LOCK + 1;
    /** 符号面板下翻页 */
    public static final int FKEY_DOWN = FKEY_UP + 1;
    /** 英文面板联想开闭 */
    public static final int FKEY_EN_LX = FKEY_DOWN + 1;
    /** 单字/全部切换键 */
    public static final int FKEY_SINGLE = FKEY_EN_LX + 1;
    /** 安卓上的中英切换键 */
    public static final int FKEY_CNEN = FKEY_SINGLE + 1;
    /** IOS语音按键 */
    public static final int FKEY_VOICE = FKEY_CNEN + 1;
    /** 左移光标 */
    public static final int FKEY_MOVE_LEFT = FKEY_VOICE + 1;
    /** 右移光标 */
    public static final int FKEY_MOVE_RIGHT = FKEY_MOVE_LEFT + 1;
    /** 旋转屏幕 */
    public static final int FKEY_ROTATE = FKEY_MOVE_RIGHT + 1;
    /** 切换到仿照iOS系统的符号面板 */
    public static final int FKEY_SYM_EXT = FKEY_ROTATE + 1;

    /** 生僻字面板【部/写】切换键 */
    int FKEY_RARE_CHAIZI_HW = 0xE052;

    /** 游戏辅助键盘0键 */
    public static final int FKEY_GAME_0 =  0xE000 + 128;
    /** 游戏辅助键盘1键 */
    public static final int FKEY_GAME_1 = FKEY_GAME_0 + 1;
    /** 游戏辅助键盘2键 */
    public static final int FKEY_GAME_2 = FKEY_GAME_1 + 1;
    /** 游戏辅助键盘3键 */
    public static final int FKEY_GAME_3 = FKEY_GAME_2 + 1;
    /** 游戏辅助键盘4键 */
    public static final int FKEY_GAME_4 = FKEY_GAME_3 + 1;
    /** 游戏辅助键盘5键 */
    public static final int FKEY_GAME_5 = FKEY_GAME_4 + 1;
    /** 游戏辅助键盘6键 */
    public static final int FKEY_GAME_6 = FKEY_GAME_5 + 1;
    /** 游戏辅助键盘7键 */
    public static final int FKEY_GAME_7 = FKEY_GAME_6 + 1;
    /** 游戏辅助键盘8键 */
    public static final int FKEY_GAME_8 = FKEY_GAME_7 + 1;
    /** 游戏辅助键盘9键 */
    public static final int FKEY_GAME_9 = FKEY_GAME_8 + 1;
    /** 客户端自定义按键，上屏内容由客户端传入 */
    public static final int FKEY_DEFINED = 0xE000 + 192;
    /** 笔画按键：横 */
    public static final int FKEY_BH_1 = FKEY_DEFINED + 1;
    /** 笔画按键：竖 */
    public static final int FKEY_BH_2 = FKEY_DEFINED + 2;
    /** 笔画按键：撇 */
    public static final int FKEY_BH_3 = FKEY_DEFINED + 3;
    /** 笔画按键：捺 */
    public static final int FKEY_BH_4 = FKEY_DEFINED + 4;
    /** 笔画按键：折 */
    public static final int FKEY_BH_5 = FKEY_DEFINED + 5;
    /** 笔画按键：通配 */
    public static final int FKEY_BH_6 = FKEY_DEFINED + 6;
    /** 9键皮肤布局,0和空格键在一起的皮肤 */
    public static final int FKEY_SPACE_0= 0xE0C9;
    /** 功能键结束 */
    public static final int FKEY_CEILING = 0xF8FF;

    /** 上层添加的功能键: 长按时候的删除键 */
    public static final int FKEY_PLATFORM_BACK_HOLD = FKEY_CEILING + 1;

    /**
     * 功能键开始
     */
    public static final int FKEY_START = FKEY_BACK;
    /**
     * 功能键结束
     */
    public static final int FKEY_END = FKEY_PLATFORM_BACK_HOLD;


    // ///////////////////
    /**
     * cz[x]down.bin词库版本，如cz5down 版本号为5
     */
    public static final int CZ_DWON_VER_INDEX = 0;
    /**
     * 中文系统词库版本版本 对应提测邮件中内核版本号
     */
    public static final int CZ_SYS_VER_INDEX = 1;
    /**
     * 中文词库分类版本
     */
    public static final int CZ_CATE_VER_INDEX = 2;
    /**
     * 二元关系词库版本
     */
    public static final int CZ_GRAM_VER_INDEX = 3;
    /**
     * 词库字数
     */
    public static final int CZ_COUNT_INDEX = 4;

    // ///////////////////

    /**
     * 按键是否是功能键
     * @param keyId 按键id
     * @return 是否是功能键
     */
    static boolean isFunctionKey(int keyId) {
        return keyId >= FKEY_START && keyId <= FKEY_END;
    }

    /**
     * 是否是面板切换功能键
     * @param keyId 功能键KeyId
     */
    static boolean isPadSwitchKey(int keyId) {
        return keyId == FKEY_RETURN
                || keyId == FKEY_SHIFT
                || keyId == FKEY_SHIFT_CAPLOCK
                || keyId == FKEY_MORECAND
                || keyId == FKEY_ABC
                || keyId == FKEY_123
                || keyId == FKEY_SYM
                || keyId == FKEY_CN
                || keyId == FKEY_CNEN
                || keyId == FKEY_SYM_EXT
                || keyId == FKEY_RARE_CHAIZI_HW;
    }

    /** List选项类型中，大类类型的MASK值 */
    public static final int LIST_CATEGORY_MASK = 0x1f;
    /** List选项类型：不存在 */
    public static final int COMMEN_LIST_ITEM = 0;
    /** List选项类型：拼音 */
    public static final int PY_LIST_ITEM = (1 << 0);
    /** List选项类型：笔画 */
    public static final int BH_LIST_ITEM = (1 << 1);
    /** List选项类型：符号 */
    public static final int SYM_LIST_ITEM = (1 << 2);
    /** List选项类型：英文 */
    public static final int EN_LIST_ITEM = (1 << 3);
    /** List选项类型：符号面板分类 */
    public static final int SYM_CATEGORY_LIST_ITEM = (1 << 4);
    ////////////以上是列表大类，相互独立////////////////
    /** List选项类型：拼音list或者笔画list中的英文选项 */
    public static final int EN_MIX_LIST_ITEM = (1 << 5);
    /** List选项类型：自定义 */
    public static final int DEFINE_LIST_ITEM = (1 << 6);
    /** List选项类型：选中状态 */
    public static final int CHECKED_LIST_ITEM = (1 << 7);
    
    // ///////////////////////////////////////////
    /** 空面板(默认状态;无任何功能) */
    public static final int PAD_NONE = 0;
    /** 笔画 */
    public static final int PAD_BH = 1;
    /** 五笔9键 */
    public static final int PAD_WB9 = 2;
    /** 五笔26键 */
    public static final int PAD_WB26 = 3;
    /** 英文9键 */
    public static final int PAD_EN9 = 4;
    /** 英文26键 */
    public static final int PAD_EN26 = 6;
    /** 英文26大写，不再使用，上层只需要当做英文26即可 */
    @Deprecated
    public static final int PAD_EN26_S = 7;
    /** 数字9键输入 */
    public static final int PAD_123_T9 = 8;
    /** 数字26键输入 */
    public static final int PAD_123_26 = 9;
    /** 拼音9键 */
    public static final int PAD_PY9 = 10;
    /** 拼音26键 */
    public static final int PAD_PY26 = 11;
    /** 手写 */
    public static final int PAD_HW = 12;
    /** 符号界面 */
    public static final int PAD_SYM = 14;
    /** 注音面板 */
    public static final int PAD_ZY = 16;
    /** 仓颉面板 */
    public static final int PAD_CJ = 17;
    /** int PAD_EXTERNAL = 18,        ///<外部面板 */
    /**
     * <生僻字键盘（部首拼音拆字）
     */
    int PAD_RARE_CHAIZI = 19;

    /**
     * <生僻字键盘（手写）
     */
    int PAD_RARE_HW = 20;

    /** 速成输入法面板 */
    public static final int PAD_SC = 40;
    /** 拼音游戏键盘 */
    public static final int PAD_PY_GAME = 41;
    /** 五笔游戏键盘 */
    public static final int PAD_WB_GAME = 42;
    /** 笔画游戏键盘 */
    public static final int PAD_BH_GAME = 43;
    /** 手写游戏键盘 */
    public static final int PAD_HW_GAME = 44;
    /** 语音游戏键盘 */
    public static final int PAD_VOICE = 45;
    /** URL输入面板 */
    public static final int PAD_URL = 46;
    /** 更多候选面板 */
    public static final int PAD_MORE = 47;
    /** 特殊符号面板(Android仿iOS系统符号面板) */
    public static final int PAD_SYM_EXT = 48;

    /**
     * 是否是英文面板
     */
    static boolean isEnPad(int padId) {
        return padId == ImeCoreConsts.PAD_EN9
                || padId == ImeCoreConsts.PAD_EN26
                || padId == ImeCoreConsts.PAD_EN26_S;
    }

    static boolean isNumPad(int padId) {
        return padId == ImeCoreConsts.PAD_123_26
                || padId == ImeCoreConsts.PAD_123_T9;
    }

    /**
     * 是否是手写的面板
     */
    static boolean isHwPad(int padId) {
        return padId == ImeCoreConsts.PAD_HW;
    }

    // ////////////// 内核管理的面板端状态 ////////////////////////////////

    /**
     * 普通点击
     */
    public static final int INPUTTYPE_CLICK = 0;

    /**
     * 上划
     */
    public static final int INPUTTYPE_UP = 1;

    /**
     * 下划
     */
    public static final int INPUTTYPE_DOWN = 2;

    /**
     * 左划
     */
    public static final int INPUTTYPE_LEFT = 3;

    /**
     * 右划
     */
    public static final int INPUTTYPE_RIGHT = 4;

    /**
     * 长按选择
     */
    public static final int INPUTTYPE_LONG_PRESS = 5;
    /**
     * 长按按住不放（目前只针对shift键生效）
     */
    public static final int INPUTTYPE_LONG_DOWN = 6;
    /**
     * 长按松手（目前只针对shift键生效）
     */
    public static final int INPUTTYPE_LONG_UP = 7;
    /**
     * 点击按下, 用于云输入预取
     */
    public static final int INPUTTYPE_CLICK_DOWN = 8;
    /**
     * PC输入法可以一次性输入多个输入码
     */
    public static final int INPUTTYPE_CLICK_MULTI_CHAR = 9;
    /**
     * 精确点击
     */
    public static final int INPUTTYPE_EXACT_CLICK = 10;


    // ////////////// 内核管理的面板端状态 ////////////////////////////////
    
    /** 无状态 */
    public static final int STATETRACK_NONE = 0;
    /** 正在手写状态 */
    public static final int STATETRACK_HW = 1;
    /** 正在虚框手写状态 */
    public static final int STATETRACK_SYMHW = 2;
    /** 正在滑动输入状态 */
    public static final int STATETRACK_SLIDE = 3;
    
    /** 无轨迹 */
    public static final int CFGTRACK_NONE = 0; 
    /** 键盘手写 */
    public static final int CFGTRACK_HW = 1;
    /** 滑动输入 */
    public static final int CFGTRACK_SLIDE = 2;

    /**
     * 手写速度类型: 最慢
     */
    public static final int CFGHWSPEED_SLOWEST = 700;
    /**
     * 手写速度类型: 较慢
     */
    public static final int CFGHWSPEED_SLOWER = 600;
    /**
     * 手写速度类型: 正常
     */
    public static final int CFGHWSPEED_NORMAL = 500;
    /**
     * 手写速度类型: 较快
     */
    public static final int CFGHWSPEED_FASTER = 400;
    /**
     * 手写速度类型: 最快
     */
    public static final int CFGHWSPEED_FASTEST = 300;
    /**
     * 手写速度类型：智能速度
     */
    public static final int CFGHWSPEED_SMART = 999;

    /**
     * 手写模式：单字
     */
    int CFGHWTYPE_HZ = 1;
    /**
     * 手写模式：叠写
     */
    int CFGHWTYPE_REDUP = 2;
    /**
     * 手写模式：连写
     */
    int CFGHWTYPE_CONTINUATION = 4;
    /**
     * 手写模式：自由写
     */
    int CFGHWTYPE_FREE = CFGHWTYPE_HZ | CFGHWTYPE_REDUP | CFGHWTYPE_CONTINUATION;

    /**
     * 手写符号类型: HW_FIND_RANGE_NUM 数字
     */
    public static final int CFGSYMHWRNG_123 = 0x08;
    /**
     * 手写符号类型: HW_FIND_RANGE_EN_LOWER | HW_FIND_RANGE_EN_UPPER 英文
     */
    public static final int CFGSYMHWRNG_ABC = 0x30;
    /**
     * 手写符号类型:
     * HW_FIND_RANGE_PUN_COMMON | HW_FIND_RANGE_PUN_EXT | HW_FIND_RANGE_SYM_COMMON | HW_FIND_RANGE_SYM_EXT 标点,符号
     */
    public static final int CFGSYMHWRNG_PUN = 0x3C0;

    /**
     * 五笔86方案
     */
    public static final int WB_86_SCHEMA = 1;
    /**
     * 五笔98方案
     */
    public static final int WB_98_SCHEMA = 2;
    /**
     * 五笔自定义方案
     */
    public static final int WB_DEF_SCHEMA = 3;
    /**
     * 五笔新世纪方案
     */
    public static final int WB_NEW_SCHEMA = 4;

    /**
     * 普通仓颉方案
     */
    public static final int CJ_NORMAL_SCHEMA = 1;
    /**
     * 速成仓颉方案
     */
    public static final int CJ_QUICK_SCHEMA = 2;

    /**
     * 不联想
     */
    public static final int NO_LEGEND = 1;
    /**
     * 单次联想
     */
    public static final int LEGEND_ONCE = 2;
    /**
     * 多次联想
     */
    public static final int LEGEND_MORE = 3;
    
    /** list筛选：py筛选 */
    public static final int LIST_PY_FILTER = 1;
    /** list筛选：bh筛选 */
    public static final int LIST_BH_FILTER = 2;

    /** 更多候选词面板中，tab的筛选方式：无筛选 */
    int MORE_CAND_TAB_NONE_FILTER = 0;
    /** 更多候选词面板中，tab的筛选方式：py筛选 */
    int MORE_CAND_TAB_PY_FILTER = 1;
    /** 更多候选词面板中，tab的筛选方式：bh筛选 */
    int MORE_CAND_TAB_BH_FILTER = 2;
    /** 更多候选词面板中，tab的筛选方式：en筛选 */
    int MORE_CAND_TAB_EN_FILTER = 3;
    /** 更多候选词面板中，tab的筛选方式：emoji筛选 */
    int MORE_CAND_TAB_EMOJI_FILTER = 4;
    /** 更多候选词面板中，tab的筛选方式：人名筛选 */
    int MORE_CAND_TAB_NAME_FILTER = 5;

    /** 不开启云输入 */
    public static final int CLOUD_CLOSED = 255;
    /** 在3G/4G/WiFi下开启 */
    public static final int CLOUD_NON_2G = 2;
    /** 仅wifi下开启 */
    public static final int CLOUD_WIFI = 10;
    /** 所有网络下开启 */
    public static final int CLOUD_ALL = 1;
    /** 无论什么网络条件都开启 */
    public static final int CLOUD_ALWAYS = 0;

    /** 模糊音选项的数量 */
    public static final int CFG_MOHU_CH_NUM = 18;
    /** 模糊音选项：ch=c模糊选项 */
    public static final int CFG_MOHU_CH_C = 1; 
    /** 模糊音选项：sh=s模糊选项 */
    public static final int CFG_MOHU_SH_S = (1 << 1); 
    /** 模糊音选项：zh=z模糊选项 */
    public static final int CFG_MOHU_ZH_Z = (1 << 2);
    /** 模糊音选项：g=k模糊选项 */
    public static final int CFG_MOHU_G_K = (1 << 3);  
    /** 模糊音选项：h=f模糊选项 */
    public static final int CFG_MOHU_H_F = (1 << 4);  
    /** 模糊音选项：l=n模糊选项 */
    public static final int CFG_MOHU_L_N = (1 << 5);  
    /** 模糊音选项：l=r模糊选项 */
    public static final int CFG_MOHU_L_R = (1 << 6);  
    /** 模糊音选项：ang=an模糊选项 */
    public static final int CFG_MOHU_ANG_AN = (1 << 7);  
    /** 模糊音选项：eng=en模糊选项 */
    public static final int CFG_MOHU_ENG_EN = (1 << 8); 
    /** 模糊音选项：ing=in模糊选项 */
    public static final int CFG_MOHU_ING_IN = (1 << 9); 
    /** 模糊音选项：iang=ian模糊选项 */
    public static final int CFG_MOHU_IANG_IAN = (1 << 10);  
    /** 模糊音选项：uang=uan模糊选项 */
    public static final int CFG_MOHU_UANG_UAN = (1 << 11);
    /** 模糊音选项：ai=an模糊选项 */
    public static final int CFG_MOHU_AI_AN = (1 << 12);
    /** 模糊音选项：un=ong模糊选项 */
    public static final int CFG_MOHU_UN_ONG = (1 << 13);
    /** 模糊音选项：hui=fei模糊选项 */
    public static final int CFG_MOHU_HUI_FEI = (1 << 14);
    /** 模糊音选项：huang=wang模糊选项 */
    public static final int CFG_MOHU_HUANG_WANG = (1 << 15);
    /** 模糊音选项：feng=hong模糊选项 */
    public static final int CFG_MOHU_FENG_HONG = (1 << 16);
    /** 模糊音选项：hu=fu模糊选项 */
    public static final int CFG_MOHU_FU_HU = (1 << 17);

    // ////////////// 词库相关常量 ////////////////////////////////

    /** 自造词类型：汉字 */
    public static final int CONFIG_USERWORD_TYPE_USERWORD_CH1 = 1;
    /** 自造词类型：二字词 */
    public static final int CONFIG_USERWORD_USERWORD_CH2 = 2;
    /** 自造词类型：三字词 */
    public static final int CONFIG_USERWORD_USERWORD_CH3 = 3;
    /** 自造词类型：四字词 */
    public static final int CONFIG_USERWORD_USERWORD_CH4 = 4;
    /** 自造词类型：五字词 */
    public static final int CONFIG_USERWORD_USERWORD_CH5 = 5;
    /** 自造词类型：六字词 */
    public static final int CONFIG_USERWORD_USERWORD_CH6 = 6;
    /** 自造词类型：七字词 */
    public static final int CONFIG_USERWORD_USERWORD_CH7 = 7;
    /** 自造词类型：八字词 */
    public static final int CONFIG_USERWORD_USERWORD_CH8 = 8;
    /** 自造词类型：八字以上词 */
    public static final int CONFIG_USERWORD_USERWORD_MORE = 9;
    /** 自造词类型：英文词 */
    public static final int CONFIG_USERWORD_USERWORD_EN = 20;
    /** 自造词类型：所有中文词 */
    public static final int CONFIG_USERWORD_USERWORD_ALL = 30;
    /** 自造词类型：自造词查询选项枚举值个数 */
    public static final int CONFIG_USERWORD_USERWORD_LAST_SIGNIGIE = 31;

    /** 英文自造词 */
    public static final int SEARCHKEY_EN = 20;
    /** 中文自造词 */
    public static final int SEARCHKEY_ALL_CH = 30;

    /**
     * 删除某个词对应联系人的信息
     */
    public static final int CMD_CONTACT_DEL = 44;
    /**
     * 删除某个词对应联系人的信息并恢复默认词频
     */
    public static final int CMD_CONTACT_RESTORE_FREQ = 45;
    /**
     * 删除某个词对应联系人以及对应的自造词
     */
    public static final int CMD_CONTACT_DEL_ALL = 46;

    /** 网络类型：未知 */
    int NET_TYPE_UNKNOW = 0;
    /** 网络类型：2G */
    int NET_TYPE_2G = 1;
    /** 网络类型：3G */
    int NET_TYPE_3G = 2;
    /** 网络类型：4G */
    int NET_TYPE_4G = 3;
    /** 网络类型：4G以上 */
    int NET_TYPE_4_NG = 4;
    /** 网络类型：wifi */
    int NET_TYPE_WIFI = 10;
    /** 网络类型：结束标志位 */
    int NET_TYPE_END = 255;

    /** 用户词操作：恢复词频 */
    int USRWORD_ACTION_RESET = 0;
    /** 用户词操作：删除自造词 */
    int USRWORD_ACTION_DELETE = 1;

    /** 手写注音模式：生僻字注音 */
    int HW_TONEMODE_ODD = 0;
    /** 手写注音模式：全部注音 */
    int HW_TONEMODE_ALL = 1;

    /** 未知 */
    int SHOWINFO_TYPE_NONE = 0;
    /** 普通 */
    int SHOWINFO_TYPE_NORMAL = 1;
    /** 拼音，需要转换声调 */
    int SHOWINFO_TYPE_PINYIN = 2;
    /** 上层获取的剪切板数据 */
    int SHOWINFO_TYPE_USER_PASTE = 3;
    /** 上层获取的语音数据 */
    int SHOWINFO_TYPE_USER_AUDIO = 4;
    /** 上层获取的OCR数据 */
    int SHOWINFO_TYPE_USER_OCR = 5;

    /** 无纠错 */
    int AUTOFIX_TYPE_NONE = 0;
    /** 当前输入码输错 */
    int AUTOFIX_TYPE_INPUT = 0x1;
    /** 当前输入码前漏输了 */
    int AUTOFIX_TYPE_LESS = 0x2;
    /** 当前输入码是多输的 */
    int AUTOFIX_TYPE_MORE = 0x4;
    /** 当前输入码是交换的 */
    int AUTOFIX_TYPE_SWAP = 0x8;
    /** 当前输入码后漏输了 */
    int AUTOFIX_TYPE_LESS_BACK = 0x10;
    /** 当前输入码前后都漏输了 */
    int AUTOFIX_TYPE_LESS_PRE_BACK = AUTOFIX_TYPE_LESS | AUTOFIX_TYPE_LESS_BACK;

    /** SUG状态：普通sug */
    int PAD_SUG_STATE_NONE = 0;
    /** sug状态：起面板的sug */
    int PAD_SUG_STATE_STARTUP = 1;

    /**
     * SUG卡片类型：未知
     */
    int SUG_CARD_TYPE_NONE = 0;
    /**
     * SUG卡片类型：应用
     */
    int SUG_CARD_TYPE_APP = 1;
    /**
     * SUG卡片类型：音频
     */
    int SUG_CARD_TYPE_MUSIC = 2;
    /**
     * SUG卡片类型：视频
     */
    int SUG_CARD_TYPE_VIDEO = 3;
    /**
     * SUG卡片类型：图书
     */
    int SUG_CARD_TYPE_BOOK = 4;
    /**
     * SUG卡片类型：普通
     */
    int SUG_CARD_TYPE_NORMAL = 5;

    /**
     * 词库类型：cz3
     */
    int ECORE_FILE_TYPE_CFT_CZ3 = 0;
    /**
     * 词库类型：智能回复
     */
    int ECORE_FILE_TYPE_CFT_AUTOREPLY = 1;
    /**
     * 词库类型：英文系统词库
     */
    int ECORE_FILE_TYPE_CFT_EN_NEO = 2;
    /**
     * 词库类型：滑写词库
     */
    int ECORE_FILE_TYPE_CFT_SLIDE = 3;
    /**
     * 词库类型：手写词库
     */
    int ECORE_FILE_TYPE_CF_HANDWRITE = 4;
    /**
     * 词库类型：仓颉词库
     */
    int ECORE_FILE_TYPE_CF_CANGJIE = 5;
    /**
     * 词库类型：仓颉速成词库
     */
    int ECORE_FILE_TYPE_CF_CANGJIE_QUICK = 6;
    /**
     * 词库类型：注音词库
     */
    int ECORE_FILE_TYPE_CF_ZHUYIN = 7;
    /**
     * 分流模型
     */
    int ECORE_FILE_TYPE_FLOW_MODEL = 8;
    /** CFT_SENS = 9, //敏感词库*/
    /**
     * 生僻字部首拼音拆字词库，
     */
    int CFT_RARE_CHAIZI = 10;
    /**
     * 词库类型：文心大模型
     */
    int ECORE_FILE_TYPE_CFT_LOCAL_WENXIN = 11;

    /** 框属性：默认属性 */
    byte ATTRIBUTE_DEFAULT = 0;
    /** 框属性：搜索框属性 */
    byte ATTRIBUTE_SEARCH = 1;
    /** 框属性：网址框属性 */
    byte ATTRIBUTE_WEB = 2;
    /** 框属性：数字框属性 */
    byte ATTRIBUTE_DIGITAL = 3;
    /** 框属性：邮件框属性 */
    byte ATTRIBUTE_EMAIL = 4;
    /** 框属性：密码框属性 */
    byte ATTRIBUTE_PASSWORD = 5;
    /** 框属性：电话框属性 */
    byte ATTRIBUTE_DIAL = 6;
    /** 框属性：未知属性 */
    byte ATTRIBUTE_UNKNOWN = 7;

    /** 默认，IOS */
    byte RETURN_KEY_DEFAULT = 0;
    /** 前往，安卓，IOS */
    byte RETURN_KEY_GO = 1;
    /** 谷歌，IOS */
    byte RETURN_KEY_GOOGLE = 2;
    /** IOS */
    byte RETURN_KEY_JOIN = 3;
    /** 下一项，安卓，IOS */
    byte RETURN_KEY_NEXT = 4;
    /** IOS */
    byte RETURN_KEY_ROUTE = 5;
    /** 搜索，安卓，IOS */
    byte RETURN_KEY_SEARCH = 6;
    /** 发送，安卓，IOS */
    byte RETURN_KEY_SEND = 7;
    /** 雅虎，IOS */
    byte RETURN_KEY_YAHOO = 8;
    /** 完成，安卓，IOS */
    byte RETURN_KEY_DONE = 9;
    /** IOS */
    byte RETURN_KEY_EMERGENCY_CALL = 10;
    /** IOS */
    byte RETURN_KEY_CONTINUE = 11;
    /** 上一项，安卓 */
    byte RETURN_KEY_PREVIOUS = 12;
    /** 未指定，安卓 */
    byte RETURN_KEY_UNSPECIFIED = 13;
    /** 未指定，安卓 */
    byte RETURN_KEY_NONE = 14;

    /** Shift关闭 */
    int CFG_EN_SHIFT_OFF = 0;
    /** Shift打开 */
    int CFG_EN_SHIFT_ON = 1;
    /** Shift锁定 */
    int CFG_EN_SHIFT_LOCK = 2;

    /**
     * 起面板事件
     */
    int PAD_EVENT_UP = 1;
    /**
     * 收面板事件
     */
    int PAD_EVENT_DOWN = 2;
    /**
     * 端上调用，随系统事件，属于系统主动触发的特定行为，在这个行为中内核进行处理状态
     * 现与 PadEventClearAll 功能相似，但是对端上意义不同，PadEventClearAll属于端上主要调用触发的行为
     */
    int PAD_EVENT_RESTART = 3;
    /**
     * Android 起面板完成皮肤加载后,发这个事件,iOS不会有这个事件
     * Android 下处理撰写自动打开,输入前Cand活动等 需要在这个事件里面处理
     */
    int PAD_EVENT_UP_DONE = 4;
    /**
     * 端上会在内核不关心的事件中清空面板的所有内容，但是已经上屏的内容仍要上屏(如手写/英文)
     * 端上主要调用触发 ，内核将预上屏内容上屏并清空面板上所有内容
     */
    int PAD_EVENT_CLEAR_ALL = 5;
    /**
     * 面板展示的时候的事件，区别 PAD_EVENT_UP，该事件在 onWindowShown 的时候触发
     */
    int PAD_EVENT_SHOW = 6;

    /**
     * 无整句预测的类型
     */
    int CLOUDZJ_NONE = 0;
    /**
     * 普通的整句输入类型
     */
    int CLOUDZJ_NORMAL = 1;

    /**
     * 整句前序底纹词。
     * 含义是面板弹起且没有前序上屏输入时，展示的一种底纹提示语。
     */
    int CLOUDZJ_PRE_HINT = 2;

    /**
     * 整句前序本地词。
     * 含义是面板弹起且没有前序上屏输入时，手动点击底纹提示后展现的推荐词句，因为在本地词典因此叫本地词。
     */
    int CLOUDZJ_PRE_HINT_LOCAL = 3;

    /**
     * 整句前序底纹词加动画显示。
     * 含义是面板弹起且没有前序上屏输入时，展示的底纹语同时，具有的动画标识。
     */
    int CLOUDZJ_PRE_HINT_ANI = 4;

    /** 符号类别： 最近 */
    int SYM_CATE_RECENT = 0;
    /** 符号类别： 中文 */
    int SYM_CATE_CN = 1;
    /** 符号类别： 英文 */
    int SYM_CATE_EN = 2;
    /** 符号类别： 表情符 */
    int SYM_CATE_EMOJI = 3;
    /** 符号类别： 网络 */
    int SYM_CATE_NET = 4;
    /** 符号类别： 特殊 */
    int SYM_CATE_SPECIAL = 5;
    /** 符号类别： 数学 */
    int SYM_CATE_MATH = 6;
    /** 符号类别： 序号 */
    int SYM_CATE_NUMBER = 7;
    /** 符号类别： 希腊 */
    int SYM_CATE_RUSSIA = 8;
    /** 符号类别： 箭头 */
    int SYM_CATE_ARROW = 9;
    /** 符号类别： 平假名 */
    int SYM_CATE_PINGJIA = 10;
    /** 符号类别： 片假名 */
    int SYM_CATE_PIANJIA = 11;
    /** 符号类别： 注音 */
    int SYM_CATE_ZY = 12;
    /** 符号类别： 部首 */
    int SYM_CATE_BUSHOU = 13;
    /** 符号类别： 制表 */
    int SYM_CATE_TAB = 14;

    /**
     * 所有的符号左侧列表索引
     */
    int[] SYM_CATE_LIST = {
            SYM_CATE_RECENT,    // 最近
            SYM_CATE_CN,        // 中文
            SYM_CATE_EN,        // 英文
            SYM_CATE_EMOJI,     // 表情
            SYM_CATE_NET,       // 网络
            SYM_CATE_SPECIAL,   // 特殊
            SYM_CATE_MATH,      // 数学
            SYM_CATE_NUMBER,    // 序号
            SYM_CATE_RUSSIA,    // 希俄
            SYM_CATE_ARROW,     // 箭头
            SYM_CATE_PINGJIA,   // 平假名
            SYM_CATE_PIANJIA,   // 片假名
            SYM_CATE_ZY,        // 注音
            SYM_CATE_BUSHOU,    // 部首
            SYM_CATE_TAB,       // 制表
    };

    /** 轨迹模式：关闭 */
    int TRACE_MODE_OFF = 0;
    /** 轨迹模式：打开 */
    int TRACE_MODE_ON = 1;
    /** 轨迹模式：只有设置相关的trace被记录，输入相关的不被记录（可用在输入密码的时候） */
    int TRACE_MODE_ONLY_CONFIG = 2;

    /**
     * 按键明暗提示功能时，热键信息数量。
     */
    byte HOTLETTER_INFO_LENS = 27;

    /** 内核通知轨迹刷新类型：无 */
    int TRACE_TYPE_NONE = 0x00;
    /** 内核通知轨迹刷新类型：回删上屏文字 */
    int TRACE_TYPE_BACKSPACE_TEXT = 0x01;
    /** 内核通知轨迹刷新类型：回删输入码 */
    int TRACE_TYPE_BACKSPACE_INPUT = 0x02;
    /** 内核通知轨迹刷新类型：热区轨迹，上层配合调用export_mis_key_for_trace */
    @Deprecated
    int TRACE_TYPE_HOTSPOT = 0x04;
    /** 整句预测轨迹，从insert取出上屏词 */
    int TRACE_TYPE_ZJ_INPUT = 0x08;
    /** log文件大小超过预警值 */
    int TRACE_TYPE_LOG_FILE_WARNING = 0x10;
    /** 上屏词信息 */
    int TRACE_TYPE_WORD_INFO = 0x20;
    /** 误触信息收集 */
    int TRACE_TYPE_MIS_TOUCHINFO = 0x40;

    /** 打印所有log */
    int LOG_LEVEL_ALL = 0;
    /** 打印调试信息及该等级以上的Log */
    int LOG_LEVEL_DEBUG = 1;
    /** 打印警告信息及该等级以上的log */
    int LOG_LEVEL_WARNING = 2;
    /** 打印错误信息及该等级以上的log */
    int LOG_LEVEL_ERROR = 3;
    /** 不打印任何日志 */
    int LOG_LEVEL_NONE = 64;

    /** 点击候选条操作 */
    int CAND_CLICK = 0;
    /** 选择候选词操作 */
    int CAND_SELECT = 1;
    /** 长按候选条操作 */
    int CAND_LONGPRESS = 2;
    /** 点击次Cand操作 */
    int AI_CAND_CLICK = 3;
    /** 点击sug条操作 */
    int SUG_CLICK = 4;
    /** 点击最近生僻字操作 */
    int RARE_CAND_CLICK = 5;

    /** PC输入法对词条的黑名单处理 */
    int CAND_WORD_DELETE = 8;
    /** PC输入法添加固守操作 */
    int CAND_ADD_FIXTERM = 9;
    /** PC输入法取消固守操作 */
    int CAND_DELETE_FIXTERM = 10;
    /** PC输入法刷新候选项 */
    int CAND_REFRESH_FIND = 11;

    /** 方向，横屏 */
    int ORIENTATION_LANDSCAPE = 1;
    /** 方向，竖屏 */
    int ORIENTATION_PORTRAIT = 2;

    /**
     * list筛选类型：无筛选类型
     */
    byte LIST_FILTER_TYPE_TYPE_NONE = 0;

    /**
     * list筛选类型：拼音筛选
     */
    byte LIST_FILTER_TYPE_PY = 1;
    /**
     * list筛选类型：笔画筛选
     */
    byte LIST_FILTER_TYPE_BH = 2;
    /**
     * list筛选类型：英文筛选
     */
    byte LIST_FILTER_TYPE_EN = 3;
    /**
     * list筛选类型：emoji筛选
     */
    byte LIST_FILTER_TYPE_EMOJI = 4;
    /**
     * list筛选类型：人名筛选
     */
    byte LIST_FILTER_TYPE_NAME = 5;

    /**
     * 网络流通道的类型
     */
    interface StreamType {
        /**
         * 普通云输入请求
         */
        int STREAM_CLOUD = 0;
        /**
         * 云kv -- 预取（KV通道已删除）
         */
        int STREAM_KV = 1;
        /**
         * sug请求
         */
        int STREAM_SUG = 2;
        /**
         * AI助聊请求
         */
        int STREAM_AICHAT = 3;
        /**
         * 上报数据
         */
        int STREAM_UPLOAD_DATA = 4;
        /**
         * 上报数据
         */
        int STREAM_UPLOAD_DATA_9 = 5;
        /**
         * 不需要关心当前通道
         */
        int STREAM_TYPE_UNDEFINED = 255;
    }

    /**
     * Ai助聊次cand图标状态的描述
     */
    interface AIIconState {
        /**
         * 不显示AI_ICON(目前不存在这个状态)
         */
        byte AI_ICON_NONE = 0;

        /**
         * AI_ICON 的常规状态
         */
        byte AI_ICON_NORMAL = 1;

        /**
         * AI_ICON 处在loading状态
         */
        byte AI_ICON_LOADING = 2;
    }

    /**
     * Ai助聊面板Tab页状态
     */
    interface AIPadType {
        /**
         * 助聊面板处在关闭状态
         */
        byte AI_PAD_TAB_NONE = 0;

        /**
         * AI帮写(原名 NLP撰写)
         */
        byte AI_PAD_TAB_AI_COMPOSE = 2;

        /**
         * AI校对(原名 NLP纠错)
         */
        byte AI_PAD_TAB_AI_CORRECT = 3;

        /**
         * 神句配图
         */
        byte AI_PAD_TAB_SENT_GIF = 4;

        /**
         * 字符表情
         */
        byte AI_PAD_TAB_TEXT_EMOJI = 5;

        /**
         * 特技字
         */
        byte AI_PAD_TAB_SPECIAL_CHAR = 6;
    }

    /**
     *  告诉客户端,是否符合发起条件, 以及不发起的原因.
     */
    interface AIPadState {

        /**
         * 面板状态正常, 请获取内容并正常显示。这个标识意味着需要获取数据进行展示。如果没有数据，需要提示用户错误。
         */
        byte AI_PAD_STAT_NORMAL = 0;

        /**
         * 当前App不支持该功能
         */
        byte AI_PAD_STAT_APP_NOT_SUPPORT = 1;

        /**
         * 当前编辑框文本过少
         */
        byte AI_PAD_STAT_TEXT_TOO_SHORT = 2;

        /**
         * 当前编辑框文本过长
         */
        byte AI_PAD_STAT_TEXT_TOO_LONG = 3;

        /**
         * 网络错误, 导致功能不可用
         */
        byte AI_PAD_STAT_NET_ERROR = 4;

        /**
         * 正在请求信息。这个表示意味着正在请求数据。如果没有数据，也是正常情况。
         */
        byte AI_PAD_STAT_LOADING = 5;

        /**
         * 请求文本命中黑名单,不允许发起请求
         */
        byte AI_PAD_STAT_HIT_BLACK_LIST = 6;

        /**
         * 当前无请求文本
         */
        byte AI_PAD_STAT_TEXT_NO_TEXT = 7;
    }

    /**
     * Ai正负能量条状态
     */
    interface AIEnergyType {

        /**
         * 常规模式
         */
        int AI_ENERGY_NORMAL = 0;

        /**
         * 抑郁模式
         */
        int AI_ENERGY_NEG = 1;

        /**
         * 治愈模式
         */
        int AI_ENERGY_POS = 2;

        /**
         * 不显示正负能量条
         */
        int AI_ENERGY_NONE = 3;
    }

    /**
     * AiPad上的相关的点击操作
     */
    interface AIPadActType {
        /** 空,占位数字 */
        byte AI_PAD_ACT_NONE = 0;

        /** 客户端发起打断请求 */
        byte AI_PAD_ACT_DISTURB = 1;
        /** 点击 换一换 */
        byte AI_PAD_ACT_CLICK_REFRESH = 2;
        /** AiPad上点击 CandInfo, 允许同时多个(Ai校对) */
        byte AI_PAD_ACT_CLICK_CAND_ITEM = 3;

        /** 点击AiPad关闭按键 */
        byte AI_PAD_ACT_CLOSE = 32;
        /** 点击AiPad打开按键(会自动进入默认Tab/或上次的Tab)，不建议使用 */
        /** 打开AiPad--并切换到AI创作(原名 NLP撰写) */
        byte AI_PAD_ACT_OPEN_TAB_AI_COMPOSE = 35;
        /** 打开AiPad--并切换到AI校对(原名 NLP纠错) */
        byte AI_PAD_ACT_OPEN_TAB_AI_CORRECT = 39;
        /** 打开AiPad--并切换到神句配图 */
        byte AI_PAD_ACT_OPEN_TAB_AI_PEITU = 40;
        /** 打开AiPad--并切换到花漾文 */
        byte AI_PAD_ACT_OPEN_TAB_AI_TEXT_EMOJI = 41;
        /** 打开AiPad--并切换到特技字 */
        byte AI_PAD_ACT_OPEN_TAB_AI_SPECIAL_CHAR = 42;
        /** 打开AiPad--并切换到文心 */
        byte AI_PAD_ACT_OPEN_TAB_WENXIN = 43;
        /** 打开AiPad--并切换到高情商详情页 */
        byte AI_PAD_ACT_OPEN_TAB_HIGH_EQ = 44;
        /** 打开AiPad--并切换到小红书种草详情页 */
        byte AI_PAD_ACT_OPEN_TAB_XHS_PROMOTE_ESSAY = 45;
        /** 打开AiPad并切换到AI问问 */
        byte AI_PAD_ACT_OPEN_TAB_AI_WENWEN = 46;
        /** 打开AiPad并切换到恋爱大师*/
        byte AI_PAD_ACT_OPEN_TAB_LOVE_MASTER  = 47;
    }

    /**
     * AI趣聊的类型
     */
    interface AIPadFunchatType {
        /** RAP */
        byte AI_PAD_FUNCHAT_RAP = 0;
        /** 藏头诗 */
        byte AI_PAD_FUNCHAT_ACROSTIC = 1;
        /** 现代诗 */
        byte AI_PAD_FUNCHAT_MODERNPOERTY = 2;
    }

    /**
     * 客户端上屏文本的数据类型
     */
    interface CustomInputDataType {
        /** 客户端上屏了剪切板内容 */
        byte PASTE_DATA = 0;
        /** 客户端上屏了emoji */
        byte EMOJI_DATA = 1;
        /** 客户端上屏了语音文本(预上屏不发事件) */
        byte VOICE_INPUT_DATA = 3;
        /** 客户端上屏了花漾文（字符表情）的文本 */
        byte TEXT_EMOJI_INPUT_DATA = 4;
        /** 客户端上屏了特技字的文本 */
        byte SPECIAL_CHAR_INPUT_DATA = 5;
        /** 客户端上屏的翻译内容 */
        byte PC_VFMODE_DATA = 6;
        /** 在同一个app内发生了框切换但是没有面板事件,需要触发输入后 */
        byte EDITBOX_CHANGED_DATA = 7;
        /** 端上获取到搜索query时调用 */
        byte SEARCH_QUERY = 8;
        /** 客户端发送事件 */
        byte SEND_DATA = 9;
    }

    /**
     * AI趣聊中，rap的类型
     */
    interface AIFunChatRapType {
        /** 当前不是AI趣聊的RAP类型 */
        byte AI_FUNCHAT_RAP_NONE = 0;
        /** RAP - 单押 */
        byte AI_FUNCHAT_RAP_SINGLE = 1;
        /** RAP - 双押 */
        byte AI_FUNCHAT_RAP_DOUBLE = 2;
        /** RAP - 三押 */
        byte AI_FUNCHAT_RAP_TRIPLE = 3;
    }

    /**
     * AI助聊中，撰写的场景
     */
    interface AiSceneType {
        /** 助聊-聊天场景 */
        int AI_CHAT_SCENE = 0;
        /** 助聊-购物场景 */
        int AI_SHOP_SCENE = 1;
        /** 助聊-发布模型 */
        int AI_POST_SCENE = 2;
    }

    /**
     * AI助聊中，语料的来源。用于反馈页面
     */
    interface ComposeSrc {
        /** 来源于NLP服务器返回的结果 */
        int COMPOSE_SRC_NLP = 0;
        /** 来源于白名单 */
        int COMPOSE_SRC_WHITELIST = 1;
    }

    interface AiCandNotifyType {
        /**
         * 通知次Cand展示AI造字字体生成
         */
        int  AI_TYPE_COMMON_HIGHEST_PRIORITY = 0;
    }

    /**
     * 用来告知内核当前的产品线
     */
    interface Flavor {
        int MAINLINE = 0;
        int OEM_HUAWEI = 1;
        int OEM_HONOR = 2;
        int OEM_OPPO = 3;
        int OEM_VIVO = 4;
        int OEM_XIAOMI = 5;
    }

    interface AutoSpaceType {
        /** 不自动追加空格 */
        int AST_OFF = 0;

        /** 自动后追空格 */
        int AST_APPEND = 1;

        /** 自动前追空格 */
        int AST_PREPEND = 2;
    }

    interface ECoreZhuyinCZType {
        /** 无注音词典  - 非注音用户等 */
        int ZHUYIN_DICT_NONE = 0;
        /** 注音词典内容是简体 */
        int ZHUYIN_DICT_JT = 1;
        /** 注音词典内容是繁体 */
        int ZHUYIN_DICT_FT = 2;
    }

    /**
     * 手写笔势类型
     */
    interface HwGestureType {
        /**
         * 无效笔势
         */
        int TYPE_NONE = 0;
        /**
         * 拆分笔势
         */
        int TYPE_SPLIT = 1;
        /**
         * 选中笔势
         */
        int TYPE_CHOOSE = 2;
        /**
         * 删除笔势
         */
        int TYPE_DELETE = 3;
        /**
         * 换行笔势
         */
        int TYPE_BREAK = 4;
        /**
         * 插入笔势：『正v』，下方插入
         */
        int TYPE_INSERT_BELOW = 5;
        /**
         * 插入笔势：『倒v』，上方插入
         */
        int TYPE_INSERT_ABOVE = 6;
    }

    /**
     * 模型加载的结果
     */
    interface HwLoadType {
        /** 未加载模型 */
        int HWLOADTYPE_NONE = 0;
        /** 加载模型失败 */
        int HWLOADTYPE_FAIL = 1;
        /** 加载path.imm_hw6()的模型 */
        int HWLOADTYPE_IMM_HW = 2;

        /**
         * 加载移动端分流模型
         */
        int LOADTYPE_IMM_FLOW = 3;
        /**
         * load分流模型失败
         */
        int LOADTYPE_IMM_FLOW_FAIL = 4;
        /** 加载移动端文心模型 */
        int LOADTYPE_IMM_WENXIN = 5;
        /** load文心模型失败 */
        int LOADTYPE_IMM_WENXIN_FAIL = 6;
    }

    interface XhsEditorType {
        /** 默认 */
        int XHS_EDITOR_TYPE_NONE = 0;
        /** 小红书正文 */
        int XHS_CONTENT_EDITOR = 1;
        /** 小红书标题 */
        int XHS_TITLE_EDITOR = 2;
    }

    interface EditHintType {
        /** 无意义值 */
        int HINT_TYPE_NONE = 0;
        /** 抖音普通搜索框 */
        int HINT_TYPE_SPANNABLE_STRING = 1;
        /** 抖音电商搜索框 */
        int HINT_TYPE_STRING = 2;
    }


    /**
     * 网盟sug广告的action type
     */
    interface EventWmSugActionType {
        /**
         * 点击了网盟SUG广告，loading页面后调用了网盟的SDK进行click, 但不知道是直投包还是渠道包还是促活
         */
        int WM_SUG_APP_AD_CLICK_UNK = 0;
        /**
         * 点击了网盟SUG广告，loading页面后调用了网盟的SDK进行click,直投包
         */
        int WM_SUG_APP_AD_CLICK_ZHITOU = 1;
        /**
         * 点击了网盟SUG广告，loading页面后调用了网盟的SDK进行click,渠道包
         */
        int WM_SUG_APP_AD_CLICK_CHANNEL = 2;
        /**
         * 点击了网盟SUG广告，loading页面后调用了网盟的SDK进行click,促活包
         */
        int WM_SUG_APP_AD_CLICK_ACT = 3;
        /**
         * 点击了网盟SUG广告，loading页面后没有调用了网盟的SDK进行click
         */
        int WM_SUG_APP_NM_CLICK = 4;
        /**
         * 必须传入idx，在sugclick前传入，表示对应的sug，网盟SUG获取到广告了
         */
        int WM_SUG_APP_SDK_RECV = 100;
        /**
         * 必须传入idx，在sugclick前传入，表示对应的sug，网盟SUG请求了广告
         */
        int WM_SUG_APP_SDK_SEND = 101;
    }

    interface CloudIntentionCardActionType {
        /**
         * 无效操作
         */
        int ACTION_NONE = 0;
        /**
         * 查看
         */
        int ACTION_VIEW = 1;
        /**
         * 分享
         */
        int ACTION_SHARE = 2;

        /**
         * 下载应用
         */
        int ACTION_DOWNLOAD = 3;

        /**
         * 打开应用
         */
        int ACTION_OPEN = 4;
    }

    interface MobileCarrier {
        /**
         * 未知
         */
        int UNKNOWN = 0;
        /**
         * 移动
         */
        int Mobile = 1;
        /**
         * 联通
         */
        int Unicom = 2;
        /**
         * 电信
         */
        int Telecom = 3;
    }
}
