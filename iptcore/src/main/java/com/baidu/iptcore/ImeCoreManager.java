/*
 * Copyright (C) 2017 Baidu, Inc. All Rights Reserved.
 */
package com.baidu.iptcore;

import com.baidu.iptcore.util.Ids;

import com.baidu.input.inputtrace.api.InputTracer;

import android.content.pm.PackageInfo;
import android.os.Looper;

import com.baidu.annotation.CoreThread;
import com.baidu.base.network.NetworkTracer;
import com.baidu.input.perf.trace.NativeTraceHandler;
import com.baidu.iptcore.async.AsyncCellConfig;
import com.baidu.iptcore.async.AsyncCoreConfig;
import com.baidu.iptcore.async.AsyncEnvCallback;
import com.baidu.iptcore.async.AsyncImeLib;
import com.baidu.iptcore.async.AsyncImePad;
import com.baidu.iptcore.async.CoreThreadEngine;
import com.baidu.iptcore.interceptor.EnInlineInterceptor;
import com.baidu.iptcore.interceptor.IInputInterceptor;
import com.baidu.iptcore.interceptor.InterceptImePad;
import com.baidu.iptcore.util.Config;
import com.baidu.iptcore.util.DictUtils;
import com.baidu.iptcore.util.LoggablePlatformEnv;
import com.baidu.iptcore.util.Logger;
import com.baidu.iptcore.util.MethodTracer;
import com.baidu.iptcore.util.profiler.DefaultTraceAnalyzer;
import com.baidu.iptcore.util.profiler.DiskLogHandler;

import java.util.ArrayList;
import java.util.List;

/**
 * 输入法新内核的封装类
 * Created by cdf on 17/3/30.
 */
public class ImeCoreManager {

    /**
     * 单例
     */
    private static volatile ImeCoreManager sInstance;
    /**
     * 内核设置项的处理类
     */
    private CoreConfig mImeCoreConfig;
    /**
     * 含异步或拦截封装能力的面板操作的管理类
     */
    private InterceptImePad mImePad;
    /**
     * 词库设置的管理类
     */
    private CoreCellConfig mImeCoreCellConfig;
    /**
     * InputLib管理类
     */
    private ImeLib mImeLib;

    /**
     * 是否启用异步输入
     */
    private boolean mAsyncMode = true;
    /**
     * 异步输入引擎
     */
    private CoreThreadEngine mCoreThreadEngine;

    /**
     * 获取ImeCoremanager的实例。必须保证create被调用并且没有destroy
     *
     * @return ImeCoreManager实例
     */
    static ImeCoreManager get() {
        if (sInstance == null) {
            synchronized (ImeCoreManager.class) {
                if (sInstance == null) {
                    sInstance = new ImeCoreManager();
                }
            }
        }
        return sInstance;
    }

    /**
     * 构造器
     */
    private ImeCoreManager() {
        InputTracer.i(Ids.ImeCoreManager);
        if (mAsyncMode) {
            mCoreThreadEngine = new CoreThreadEngine();
        }

        if (mAsyncMode) {
            mImeCoreConfig = new AsyncCoreConfig(mCoreThreadEngine);
        } else {
            mImeCoreConfig = new CoreConfig();
        }
        if (mAsyncMode) {
            mImePad = new AsyncImePad(mCoreThreadEngine);
        } else {
            mImePad = new InterceptImePad();
        }
        if (mAsyncMode) {
            mImeCoreCellConfig = new AsyncCellConfig(mCoreThreadEngine);
        } else {
            mImeCoreCellConfig = new CoreCellConfig();
        }
        if (mAsyncMode) {
            mImeLib = new AsyncImeLib(mCoreThreadEngine);
        } else {
            mImeLib = new ImeLib();
        }
        InputTracer.o(Ids.ImeCoreManager);
    }

    /**
     * 创建和初始化内核
     *
     * @param configuration 对内核的配置
     */
    public static void open(ImeCoreConfiguration configuration) {
        InputTracer.i(Ids.open);
        get().openCore(configuration);
        InputTracer.o(Ids.open);
    }

    /**
     * 刷新内核
     * @param configuration 内核配置
     */
    public static void refresh(ImeCoreConfiguration configuration) {
        InputTracer.i(Ids.refresh);
        get().refreshCore(configuration);
        InputTracer.o(Ids.refresh);
    }

    private void refreshCore(ImeCoreConfiguration configuration) {
        InputTracer.i(Ids.refreshCore);
        if (mAsyncMode) {
            mCoreThreadEngine.executeLifecycleTask(() -> refreshCoreInternal(configuration));
        } else {
            refreshCoreInternal(configuration);
        }
        InputTracer.o(Ids.refreshCore);
    }

    @CoreThread
    private boolean refreshCoreInternal(ImeCoreConfiguration configuration) {
        InputTracer.i(Ids.refreshCoreInternal);
        closeCoreInternal();
        boolean result = openCoreInternal(configuration);
        InputTracer.o(Ids.refreshCoreInternal);
        return result;
    }

    private void initImePad(ImeCoreConfiguration configuration) {
        InputTracer.i(Ids.initImePad);
        // init interceptors
        List<IInputInterceptor> interceptors = new ArrayList<>();
        EnInlineInterceptor enInlineInterceptor = new EnInlineInterceptor();
        enInlineInterceptor.setCallback(configuration.imeCoreCallback);
        interceptors.add(enInlineInterceptor);
        if (configuration.interceptors != null) {
            interceptors.addAll(configuration.interceptors);
        }

        // init ImePad
        mImePad.init(configuration.imeCoreCallback, interceptors);
        if (mImePad instanceof AsyncImePad
                && configuration.platformEnv instanceof AsyncEnvCallback) {
            ((AsyncImePad) mImePad).setAsyncEnvCallback(
                    (AsyncEnvCallback) configuration.platformEnv);
        }
        InputTracer.o(Ids.initImePad);
    }

    /**
     * 初始化内核
     */
    private void openCore(ImeCoreConfiguration configuration) {
        InputTracer.i(Ids.openCore);
        if (Config.enableLog()) {
            Logger.w("open core");
        }

        beforeOpenCore(configuration);

        if (mAsyncMode) {
            mCoreThreadEngine.executeLifecycleTask(() -> openCoreInternal(configuration));
        } else {
            openCoreInternal(configuration);
        }
        InputTracer.o(Ids.openCore);
    }

    @CoreThread
    private boolean openCoreInternal(ImeCoreConfiguration configuration) {

        InputTracer.i(Ids.openCoreInternal);
        if (Config.enableLog()) {
            MethodTracer.startCoreMethod();
            MethodTracer.recordCoreMethod("openCore, copy dict");
        }

        DictUtils.copyDictionary(configuration.context, configuration.dictionaryPath,
                configuration.dictionaryInstaller, configuration.dictionaryCopy);

        if (Config.enableLog()) {
            MethodTracer.stopCoreMethod();
            MethodTracer.startCoreMethod();
            MethodTracer.recordCoreMethod("openCore");
        }

        // 开始监控
        if (BuildConfig.ENABLE_NATIVE_TRACE) {
            initNativeTrace();
        }

        initImePad(configuration);

        String dictDirectory = configuration.dictionaryPath;
        ImePlatformEnv platformEnv = configuration.platformEnv;
        PackageInfo packageInfo = configuration.packageInfo;
        int flavor = configuration.flavor;

        if (Config.enableLog()) {
            platformEnv = new LoggablePlatformEnv(platformEnv);
        }

        boolean success = IptCoreInterface.get().openCore(configuration.context, dictDirectory, packageInfo, flavor);
        IptCoreInterface.get().setCallbacks(platformEnv, mImePad);

        // 设置内核的日志等级
        ImeCoreManager.getConfig().setInt(ConfigKey.DEBUG_LOG_LEVEL, Config.getLogLevel());

        if (Config.enableLog()) {
            MethodTracer.stopCoreMethod();
        }
        enableCoreThreadStatistics(configuration.enableCoreTaskStatistics);
        InputTracer.o(Ids.openCoreInternal);
        return success;
    }

    private void initNativeTrace() {
        InputTracer.i(Ids.initNativeTrace);
        NativeTraceHandler.Config config = new NativeTraceHandler.ConfigBuilder()
                .enableNativeSymReport(true)
                .threasHold(10)
                .build();
        boolean success = NativeTraceHandler.getInstance().init(
                config, new DefaultTraceAnalyzer());
        if (success) {
            NativeTraceHandler.getInstance().startPerf();
        }
        InputTracer.o(Ids.initNativeTrace);
    }

    /**
     * 打开内核前的操作
     */
    private void beforeOpenCore(ImeCoreConfiguration configuration) {
        InputTracer.i(Ids.beforeOpenCore);
        Logger.init(configuration.logAdapter);
        Logger.setLogOpen(configuration.enableLog);
        Logger.disableLogEncode(configuration.disableLogEncode);
        if (configuration.enableProfiler) {
            DiskLogHandler.getInstance().initDiskLog(configuration.context);
        }
        MethodTracer.init(configuration.context,
                configuration.traceOpen,
                configuration.enableProfiler,
                configuration.methodTraceLogAdapter,
                mAsyncMode ? mCoreThreadEngine.getLooper() : Looper.getMainLooper());
        NetworkTracer.init(configuration.enableNetworkLog, configuration.networkLogAdapter);
        Config.init(configuration);
        InputTracer.o(Ids.beforeOpenCore);
    }

    /**
     * 关闭内核
     */
    public static void close() {
        InputTracer.i(Ids.close);
        get().closeCore();
        InputTracer.o(Ids.close);
    }

    private void closeCore() {
        InputTracer.i(Ids.closeCore);
        if (Config.enableLog()) {
            Logger.w("close core");
        }
        if (mAsyncMode) {
            mCoreThreadEngine.executeLifecycleTask(this::closeCoreInternal);
        } else {
            closeCoreInternal();
        }
        InputTracer.o(Ids.closeCore);
    }

    @CoreThread
    private boolean closeCoreInternal() {
        InputTracer.i(Ids.closeCoreInternal);
        if (Config.enableLog()) {
            MethodTracer.startCoreMethod();
            MethodTracer.recordCoreMethod("closeCore");
        }
        boolean ret = IptCoreInterface.get().closeCore();

        if (Config.enableLog()) {
            MethodTracer.stopCoreMethod();
        }
        InputTracer.o(Ids.closeCoreInternal);
        return ret;
    }

    /**
     * 获取设置接口对象
     */
    public static ImePad getPad() {
        InputTracer.i(Ids.getPad);
        ImePad result = get().mImePad;
        InputTracer.o(Ids.getPad);
        return result;
    }

    /**
     * 获取设置接口对象
     */
    public static CoreConfig getConfig() {
        InputTracer.i(Ids.getConfig);
        CoreConfig result = get().mImeCoreConfig;
        InputTracer.o(Ids.getConfig);
        return result;
    }

    /**
     * 获取ConfigPad对象
     */
    public static CoreCellConfig getCellConfig() {
        InputTracer.i(Ids.getCellConfig);
        CoreCellConfig result = get().mImeCoreCellConfig;
        InputTracer.o(Ids.getCellConfig);
        return result;
    }

    /**
     * 获得InputLib对象
     */
    public static ImeLib getLib() {
        InputTracer.i(Ids.getLib);
        ImeLib result = get().mImeLib;
        InputTracer.o(Ids.getLib);
        return result;
    }

    /**
     * 小游戏获取操作code，和内核无关
     */
    public static String getProtCode() {
        InputTracer.i(Ids.getProtCode);
        String result = IptCoreInterface.get().getProtCode();
        InputTracer.o(Ids.getProtCode);
        return result;
    }

    /**
     * 校验文件md5，和内核无关
     */
    public static void checkFileMD5(String name, byte[] digest) {
        InputTracer.i(Ids.checkFileMD5);
        IptCoreInterface.get().checkFileMD5(name, digest);
        InputTracer.o(Ids.checkFileMD5);
    }


    boolean checkCoreThreadBusy(long threshold) {
        final CoreThreadEngine engine = mCoreThreadEngine;
        return engine != null && engine.isThreadBusy(threshold);
    }



    private void enableCoreThreadStatistics(boolean enable) {
        final CoreThreadEngine engine = mCoreThreadEngine;
        if (engine != null) {
            engine.enableThreadStatistics(enable);
        }
    }


    /**
     * 判断内核线程是否繁忙
     * @param threshold 设定的阈值
     * @return true/false
     */
    public static boolean isCoreThreadBusy(long threshold) {
        if (!IptCoreInterface.get().isCoreOpened()) {
            return true;
        }
        return get().checkCoreThreadBusy(threshold);
    }

}

