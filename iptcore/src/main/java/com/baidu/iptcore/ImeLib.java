package com.baidu.iptcore;

import com.baidu.iptcore.util.Ids;

import com.baidu.input.inputtrace.api.InputTracer;

public class ImeLib {

    /**
     * 获得内核版本号
     */
    public int getCoreVersion() {
        InputTracer.i(Ids.getCoreVersion);
        int result = IptCoreInterface.get().getCoreVersion();
        InputTracer.o(Ids.getCoreVersion);
        return result;
    }

    /**
     * 获取内核版本号字符串
     * @return
     */
    public String getCoreVersionStr() {
        InputTracer.i(Ids.getCoreVersionStr);
        String result = IptCoreInterface.get().getCoreVersionStr();
        InputTracer.o(Ids.getCoreVersionStr);
        return result;
    }

    /**
     * 获取cz3词库中的三维词库版本号
     */
    public int getCz3DictGramVersion() {
        InputTracer.i(Ids.getCz3DictGramVersion);
        int result = IptCoreInterface.get().getCz3DictGramVersion();
        InputTracer.o(Ids.getCz3DictGramVersion);
        return result;
    }

    /**
     * 获取cz3词库中的系统词库版本号
     */
    public int getCz3DictSysVersion() {
        InputTracer.i(Ids.getCz3DictSysVersion);
        int result = IptCoreInterface.get().getCz3DictSysVersion();
        InputTracer.o(Ids.getCz3DictSysVersion);
        return result;
    }

    /**
     * 获取cz3词库中的类别版本号
     */
    public int getCz3DictCateVersion() {
        InputTracer.i(Ids.getCz3DictCateVersion);
        int result = IptCoreInterface.get().getCz3DictCateVersion();
        InputTracer.o(Ids.getCz3DictCateVersion);
        return result;
    }

    /**
     * 获取 cz5down 词库加载状态
     * @return
     */
    public int getCz5DownStatus() {
        InputTracer.i(Ids.getCz5DownStatus);
        int result = IptCoreInterface.get().getCz5DownStatus();
        InputTracer.o(Ids.getCz5DownStatus);
        return result;
    }

    /**
     * 获取滑行输入模型版本号
     */
    public int getSlideDictVersion() {
        InputTracer.i(Ids.getSlideDictVersion);
        int result = IptCoreInterface.get().getSlideDictVersion();
        InputTracer.o(Ids.getSlideDictVersion);
        return result;
    }

    /**
     * NNrank模型是否已经安装
     */
    public boolean isNnrankerInstalled() {
        InputTracer.i(Ids.isNnrankerInstalled);
        boolean result = IptCoreInterface.get().isNnrankerInstalled();
        InputTracer.o(Ids.isNnrankerInstalled);
        return result;
    }

    /**
     * 获取英文词库的版本
     */
    public int getEnSysDictVersion() {
        InputTracer.i(Ids.getEnSysDictVersion);
        int result = IptCoreInterface.get().getEnSysDictVersion();
        InputTracer.o(Ids.getEnSysDictVersion);
        return result;
    }

    /**
     * 未打开内核时获取词库版本号
     * int[] index对应关系如下：
     * @see ImeCoreConsts#CZ_DWON_VER_INDEX
     * @see ImeCoreConsts#CZ_SYS_VER_INDEX
     * @see ImeCoreConsts#CZ_CATE_VER_INDEX
     * @see ImeCoreConsts#CZ_GRAM_VER_INDEX
     * @see ImeCoreConsts#CZ_COUNT_INDEX
     * @return
     */
    public int[] getUnloadedCzVersion(String dictDir) {
        InputTracer.i(Ids.getUnloadedCzVersion);
        int[] result = IptCoreInterface.get().getUnloadedCzVersion(dictDir);
        InputTracer.o(Ids.getUnloadedCzVersion);
        return result;
    }

    /**
     * 导出内核记录的轨迹，返回值为导出的文件名
     */
    public String getTraceLog() {
        InputTracer.i(Ids.getTraceLog);
        String result = IptCoreInterface.get().getTraceLog();
        InputTracer.o(Ids.getTraceLog);
        return result;
    }

    /**
     * 端上处理完了内核轨迹文件，通知内核删除内核轨迹文件
     */
    public void resetTraceLog() {
        InputTracer.i(Ids.resetTraceLog);
        IptCoreInterface.get().resetTraceLog();
        InputTracer.o(Ids.resetTraceLog);
    }

    /**
     * cz5down 等词库增量下发
     */
    public int applyPatch(int type, String patchPath, String targetMd5
            , String originalFilePath) {
        InputTracer.i(Ids.applyPatch);
        int result = IptCoreInterface.get().applyPatch(type, patchPath, targetMd5, originalFilePath);
        InputTracer.o(Ids.applyPatch);
        return result;
    }


    /**
     * 获取文心的so的主版本号（不需要打开内核）
     * = 0 表示so是空，>0 表示版本号
     * 32位返回0表示不支持
     */
    public int getMlmSoVersion() {
        InputTracer.i(Ids.getMlmSoVersion);
        int result = IptCoreInterface.get().getMlmSoVersion();
        InputTracer.o(Ids.getMlmSoVersion);
        return result;
    }

    /**
     * 获取文心的词库的版本号（不需要打开内核）
     *  * -1表示so版本不兼容无法获取(32 位也返回 -1)
     *  * = 0 表示文件是空，>0 表示版本号
     */
    public int getMlmDictVersion(String path) {
        InputTracer.i(Ids.getMlmDictVersion);
        int result = IptCoreInterface.get().getMlmDictVersion(path);
        InputTracer.o(Ids.getMlmDictVersion);
        return result;
    }
}

