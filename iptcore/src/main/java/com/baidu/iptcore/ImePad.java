package com.baidu.iptcore;

import com.baidu.iptcore.util.Ids;

import com.baidu.input.inputtrace.api.InputTracer;

import android.content.Context;
import android.support.annotation.IntRange;
import android.graphics.Point;
import android.graphics.Rect;
import android.support.annotation.Nullable;
import android.support.annotation.WorkerThread;

import com.baidu.input.common.utils.CollectionUtil;
import com.baidu.input.support.state.AppAdTrigger;
import com.baidu.iptcore.info.IptAiHwRes;
import com.baidu.iptcore.info.IptCoreSugAdAction;
import com.baidu.iptcore.info.IptCoreSugAdInfo;
import com.baidu.iptcore.info.IptIntentItem;
import com.baidu.iptcore.info.IptMapTriggerWordItem;
import com.baidu.iptcore.info.IptTriggerWordItem;
import com.baidu.iptcore.info.IptContactItem;
import com.baidu.iptcore.info.IptCoreCandInfo;
import com.baidu.iptcore.info.IptCoreDutyInfo;
import com.baidu.iptcore.info.IptCoreListInfo;
import com.baidu.iptcore.info.IptCoreShowInfo;
import com.baidu.iptcore.info.IptCoreSugCardInfo;
import com.baidu.iptcore.util.Config;
import com.baidu.iptcore.util.MethodTracer;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 面板操作的封装类
 *
 * <AUTHOR>
 */
public class ImePad implements IptCoreInterface.IptPrivateDutyCallback {

    private DutyCallback mCallback = null;
    /**
     * 当前面板id
     */
    private volatile int mPadId = ImeCoreConsts.PAD_NONE;

    /**
     * 从内核取出的DutyInfo容器，内核线程读写
     */
    private final IptCoreDutyInfo mDutyInfo = new IptCoreDutyInfo();

    /**
     * 当前面板的TIP状态
     */
    private volatile int tipStates;

    protected final void setCallback(DutyCallback iptCoreInterfaceCallback) {
        InputTracer.i(Ids.setCallback);
        this.mCallback = iptCoreInterfaceCallback;
        InputTracer.o(Ids.setCallback);
    }

    @Override
    public void onPrivateDutyInfo(IptCoreDutyInfo dutyInfo) {
        InputTracer.i(Ids.onPrivateDutyInfo);
        dutyEntry(dutyInfo);
        InputTracer.o(Ids.onPrivateDutyInfo);
    }

    @Override
    public void onPrivateAssociationInfo(String response, int ecode) {
        if (mCallback != null) {
            mCallback.onHonorNotepadGetResponse(response, ecode);
        }
    }

    private void updateCurrentPadId(IptCoreDutyInfo dutyInfo) {
        InputTracer.i(Ids.updateCurrentPadId);
        if (dutyInfo != null) {
            long flag = dutyInfo.flashFlag();
            if ((flag & IptCoreDutyInfo.REFL_LAYOUT) > 0) {
                mPadId = IptCoreInterface.get().getPadId();
            }
        }
        InputTracer.o(Ids.updateCurrentPadId);
    }

    public void dutyEntry(IptCoreDutyInfo dutyInfo) {
        InputTracer.i(Ids.dutyEntry);
        updateTipState(dutyInfo);
        updateCurrentPadId(dutyInfo);
        if (mCallback != null) {
            mCallback.onDutyInfo(dutyInfo);
        }
        InputTracer.o(Ids.dutyEntry);
    }

    /**
     * 更新当前的tip状态
     * @param dutyInfo 内核返回的dutyInfo
     */
    private void updateTipState(IptCoreDutyInfo dutyInfo) {
        InputTracer.i(Ids.updateTipState);
        if (dutyInfo != null) {
            long flag = dutyInfo.flashFlag();
            if ((flag & IptCoreDutyInfo.REFL_TIP) > 0) {
                tipStates = dutyInfo.tips();
            }
        }
        InputTracer.o(Ids.updateTipState);
    }

    public void keymapClean() {
        InputTracer.i(Ids.keymapClean);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("keymapClean");
        }
        IptCoreInterface.get().keymapClean();
        InputTracer.o(Ids.keymapClean);
    }

    public void keymapAddChar(int id, char ch, int level) {
        InputTracer.i(Ids.keymapAddChar);
        if (Config.enableLog() && !Config.isShortLogMode()) {
            MethodTracer.recordCoreMethod("keymapAddChar: id:" + id + ", ch:" + ch + ", level:" + level);
        }
        IptCoreInterface.get().keymapAddChar(id, ch, level);
        InputTracer.o(Ids.keymapAddChar);
    }

    /**
     * AIPad（卡片）相关的点击操作
     */
    public void actAIPadClick(int type) {
        InputTracer.i(Ids.actAIPadClick);
        this.actAIPadClick(type, null);
        InputTracer.o(Ids.actAIPadClick);
    }

    /**
     * AIPad（卡片）相关的点击操作
     */
    public void actAIPadClick(int type, short[] clickList) {
        InputTracer.i(Ids.actAIPadClick1);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("actAIPadClick: type=" + type
                    + ", clickList=" + Arrays.toString(clickList));
        }
        IptCoreInterface.get().actAIPadClick(type, clickList);
        InputTracer.o(Ids.actAIPadClick1);
    }

    /**
     * 客户端上屏事件
     * @param type          {@link ImeCoreConsts.CustomInputDataType}
     * @param customData    上屏的具体文本
     */
    public void actCustomInputAction(int type, String customData) {
        InputTracer.i(Ids.actCustomInputAction);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("actCustomInputAction: type=" + type
                    + ", customData=" + customData);
        }
        IptCoreInterface.get().actCustomInputAction(type, customData);
        InputTracer.o(Ids.actCustomInputAction);
    }


    public void actCandAction(int candType, int candIndex) {
        InputTracer.i(Ids.actCandAction);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("actCandAction:"
                    + "candType=" + candType
                    + ", candIndex=" + candIndex);
        }
        IptCoreInterface.get().actCandAction(candType, candIndex);
        InputTracer.o(Ids.actCandAction);
    }

    public void actKeyClicked(int keyId, int inputType) {
        InputTracer.i(Ids.actKeyClicked);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("actKeyClicked: keyId=" + keyId + ", inputType=" + inputType);
        }
        IptCoreDutyInfo dutyInfo = actKeyClickInternal(keyId, 0, 0, inputType, null);
        dutyEntry(dutyInfo);
        InputTracer.o(Ids.actKeyClicked);
    }

    /**
     * 设置shift状态
     *
     * @param shiftId
     */
    public void actChangeShift(int shiftId) {
        InputTracer.i(Ids.actChangeShift);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("actChangeShift: id=" + shiftId);
        }
        int ret = IptCoreInterface.get().actChangeShift(shiftId, mDutyInfo);
        IptCoreDutyInfo dutyInfo = ret < 0 ? null : mDutyInfo;
        dutyEntry(dutyInfo);

        InputTracer.o(Ids.actChangeShift);
    }

    public void actKeyClicked(int keyId, int inputType, String uni) {
        InputTracer.i(Ids.actKeyClicked1);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("actKeyClicked: keyId=" + keyId + ", inputType=" + inputType
                    + ", uni=" + uni);
        }
        IptCoreDutyInfo dutyInfo = actKeyClickInternal(keyId, 0, 0, inputType, uni);
        dutyEntry(dutyInfo);
        InputTracer.o(Ids.actKeyClicked1);
    }

    public void actKeyClicked(int keyId, int x, int y, int inputType, String uni) {
        InputTracer.i(Ids.actKeyClicked2);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("actKeyClicked: keyId=" + keyId
                    + ", x=" + x + ", y=" + y
                    + ", inputType=" + inputType
                    + ", uni=" + uni);
        }
        IptCoreDutyInfo dutyInfo = actKeyClickInternal(keyId, x, y, inputType, uni);
        dutyEntry(dutyInfo);
        InputTracer.o(Ids.actKeyClicked2);
    }

    public void actKeyClicked(int keyId, int x, int y, byte power, byte area,
                              int inputType, String uni, boolean isMulti) {
        InputTracer.i(Ids.actKeyClicked3);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("actKeyClicked: keyId=" + keyId
                    + ", x=" + x + ", y=" + y
                    + ", power=" + power + ", area=" + area
                    + ", inputType=" + inputType
                    + ", uni=" + uni
                    + ", isMulti=" + isMulti);
        }
        IptCoreDutyInfo dutyInfo = actKeyClickInternal(keyId, x, y, power, area, inputType, uni);
        dutyEntry(dutyInfo);
        InputTracer.o(Ids.actKeyClicked3);
    }

    public void actCandClicked(int candIdx) {
        InputTracer.i(Ids.actCandClicked);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("actCandClicked: candIdx=" + candIdx);
        }
        int ret = IptCoreInterface.get().actCandClick(candIdx, mDutyInfo);
        IptCoreDutyInfo dutyInfo = ret < 0 ? null : mDutyInfo;
        dutyEntry(dutyInfo);
        InputTracer.o(Ids.actCandClicked);
    }

    public void actRareCandClicked(int candIdx) {
        InputTracer.i(Ids.actRareCandClicked);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("actRareCandClicked: candIdx=" + candIdx);
        }
        int ret = IptCoreInterface.get().actRareCandClick(candIdx, mDutyInfo);
        IptCoreDutyInfo dutyInfo = ret < 0 ? null : mDutyInfo;
        dutyEntry(dutyInfo);
        InputTracer.o(Ids.actRareCandClicked);
    }

    public void actCandSelect(int candIdx) {
        InputTracer.i(Ids.actCandSelect);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("actCandSelect: candIdx=" + candIdx);
        }
        int ret = IptCoreInterface.get().actCandSelect(candIdx, mDutyInfo);
        IptCoreDutyInfo dutyInfo = ret < 0 ? null : mDutyInfo;
        dutyEntry(dutyInfo);
        InputTracer.o(Ids.actCandSelect);
    }

    public void actCandLongPress(int candIdx) {
        InputTracer.i(Ids.actCandLongPress);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("actCandLongPress: candIdx=" + candIdx);
        }
        int ret = IptCoreInterface.get().actCandLongPress(candIdx, mDutyInfo);
        IptCoreDutyInfo dutyInfo = ret < 0 ? null : mDutyInfo;
        dutyEntry(dutyInfo);
        InputTracer.o(Ids.actCandLongPress);
    }

    public void actListClicked(int listIdx) {
        InputTracer.i(Ids.actListClicked);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("actListClicked: listIdx=" + listIdx);
        }
        int ret = IptCoreInterface.get().actListClick(listIdx, mDutyInfo);
        IptCoreDutyInfo dutyInfo = ret < 0 ? null : mDutyInfo;
        dutyEntry(dutyInfo);
        InputTracer.o(Ids.actListClicked);
    }

    public void actTabClicked(int qpFilter) {
        InputTracer.i(Ids.actTabClicked);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("actTabClicked: qpFilter=" + qpFilter);
        }
        int ret = IptCoreInterface.get().actTabClick(qpFilter, mDutyInfo);
        IptCoreDutyInfo dutyInfo = ret < 0 ? null : mDutyInfo;
        dutyEntry(dutyInfo);
        InputTracer.o(Ids.actTabClicked);
    }

    public byte[] getTabs() {
        InputTracer.i(Ids.getTabs);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("getTabs");
        }
        byte[] result = IptCoreInterface.get().getTabs();
        InputTracer.o(Ids.getTabs);
        return result;
    }

    public void actCandInfoClick(int index) {
        InputTracer.i(Ids.actCandInfoClick);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("actCandInfoClick: index=" + index);
        }
        int ret = IptCoreInterface.get().actCandInfoClick(index, mDutyInfo);
        IptCoreDutyInfo dutyInfo = ret < 0 ? null : mDutyInfo;
        dutyEntry(dutyInfo);
        InputTracer.o(Ids.actCandInfoClick);
    }

    public void actCandInfoCancel() {
        InputTracer.i(Ids.actCandInfoCancel);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("actCandInfoCancel");
        }
        int ret = IptCoreInterface.get().actCandInfoCancel(mDutyInfo);
        IptCoreDutyInfo dutyInfo = ret < 0 ? null : mDutyInfo;
        dutyEntry(dutyInfo);
        InputTracer.o(Ids.actCandInfoCancel);
    }

    public void actContactInsert() {
        InputTracer.i(Ids.actContactInsert);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("actContactInsert");
        }
        int ret = IptCoreInterface.get().actContactInsert(mDutyInfo);
        IptCoreDutyInfo dutyInfo = ret < 0 ? null : mDutyInfo;
        dutyEntry(dutyInfo);
        InputTracer.o(Ids.actContactInsert);
    }

    public void actContactCancel() {
        InputTracer.i(Ids.actContactCancel);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("actContactCancel");
        }
        int ret = IptCoreInterface.get().actContactCancel(mDutyInfo);
        IptCoreDutyInfo dutyInfo = ret < 0 ? null : mDutyInfo;
        dutyEntry(dutyInfo);
        InputTracer.o(Ids.actContactCancel);
    }

    public void actContactInfoSelect(int contactIdx, int itemIdx, boolean isSelect) {
        InputTracer.i(Ids.actContactInfoSelect);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("actContactInfoSelect: contactIdx=" + contactIdx
                    + ", itemIdx=" + itemIdx + ", isSelect=" + isSelect);
        }
        IptCoreInterface.get().actContactInfoSelect(contactIdx, itemIdx, isSelect);
        InputTracer.o(Ids.actContactInfoSelect);
    }

    public void actInputCursor(int cursorIdx) {
        InputTracer.i(Ids.actInputCursor);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("actInputCursor: cursorIdx=" + cursorIdx);
        }
        int ret = IptCoreInterface.get().actInputCursor(cursorIdx, mDutyInfo);
        IptCoreDutyInfo dutyInfo = ret < 0 ? null : mDutyInfo;
        dutyEntry(dutyInfo);
        InputTracer.o(Ids.actInputCursor);
    }

    public void actInputCursorLeft() {
        InputTracer.i(Ids.actInputCursorLeft);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("actInputCursorLeft");
        }
        IptCoreInterface.get().actInputCursorLeft();
        InputTracer.o(Ids.actInputCursorLeft);
    }

    public void actInputCursorRight() {
        InputTracer.i(Ids.actInputCursorRight);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("actInputCursorRight");
        }
        IptCoreInterface.get().actInputCursorRight();
        InputTracer.o(Ids.actInputCursorRight);
    }

    public void actInputPop() {
        InputTracer.i(Ids.actInputPop);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("actInputPop");
        }
        int ret = IptCoreInterface.get().actInputPop(mDutyInfo);
        IptCoreDutyInfo dutyInfo = ret < 0 ? null : mDutyInfo;
        dutyEntry(dutyInfo);
        InputTracer.o(Ids.actInputPop);
    }

    public void actEditCursorChange() {
        InputTracer.i(Ids.actEditCursorChange);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("actEditCursorChange");
        }
        int ret = IptCoreInterface.get().actEditCursorChange(mDutyInfo);
        IptCoreDutyInfo dutyInfo = ret < 0 ? null : mDutyInfo;
        dutyEntry(dutyInfo);
        InputTracer.o(Ids.actEditCursorChange);
    }

    public void actTrackStart(int x, int y, long time) {
        InputTracer.i(Ids.actTrackStart);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("actTrackStart: x=" + x + ", y=" + y);
        }
        int ret = IptCoreInterface.get().actTrackStartXY((short) x, (short) y, false, mDutyInfo, time);
        IptCoreDutyInfo dutyInfo = ret < 0 ? null : mDutyInfo;
        dutyEntry(dutyInfo);
        InputTracer.o(Ids.actTrackStart);
    }

    public void actTrackMove(int x, int y, long time) {
        InputTracer.i(Ids.actTrackMove);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("actTrackMove: x=" + x + ", y=" + y);
        }
        int ret = IptCoreInterface.get().actTrackMoveXY((short) x, (short) y, mDutyInfo, time);
        IptCoreDutyInfo dutyInfo = ret < 0 ? null : mDutyInfo;
        dutyEntry(dutyInfo);
        InputTracer.o(Ids.actTrackMove);
    }

    public void actTrackEnd(int x, int y, long time) {
        InputTracer.i(Ids.actTrackEnd);
        actTrackEnd(x, y, time, false);
        InputTracer.o(Ids.actTrackEnd);
    }

    public void actTrackEnd(int x, int y, long time, boolean isGesture) {
        InputTracer.i(Ids.actTrackEnd1);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("actTrackEnd: x=" + x + ", y=" + y);
        }
        int ret = IptCoreInterface.get().actTrackEnd((short) x, (short) y, mDutyInfo, time, isGesture);
        IptCoreDutyInfo dutyInfo = ret < 0 ? null : mDutyInfo;
        dutyEntry(dutyInfo);
        InputTracer.o(Ids.actTrackEnd1);
    }

    public void actSugClick(int index) {
        InputTracer.i(Ids.actSugClick);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("actSugClick: index=" + index);
        }
        int ret = IptCoreInterface.get().actSugClick(index, mDutyInfo);
        IptCoreDutyInfo dutyInfo = ret < 0 ? null : mDutyInfo;
        dutyEntry(dutyInfo);
        InputTracer.o(Ids.actSugClick);
    }

    public void actSugCardClick(int index, boolean isInsert) {
        InputTracer.i(Ids.actSugCardClick);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("actSugCardClick: index=" + index + ", isInsert=" + isInsert);
        }
        int ret = IptCoreInterface.get().actSugCardClick(index, isInsert, mDutyInfo);
        IptCoreDutyInfo dutyInfo = ret < 0 ? null : mDutyInfo;
        dutyEntry(dutyInfo);
        InputTracer.o(Ids.actSugCardClick);
    }

    public void actSugCardSelect(int sugCardIdx) {
        InputTracer.i(Ids.actSugCardSelect);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("actSugCardSelect: index=" + sugCardIdx);
        }
        int ret = IptCoreInterface.get().actSugCardSelect(sugCardIdx, mDutyInfo);
        IptCoreDutyInfo dutyInfo = ret < 0 ? null : mDutyInfo;
        dutyEntry(dutyInfo);
        InputTracer.o(Ids.actSugCardSelect);
    }

    public void importSymList(String[] listItems, String[] listValues) {
        InputTracer.i(Ids.importSymList);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("importSymList: NAMES=" + Arrays.toString(listItems)
                    + ", VALUES=" + Arrays.toString(listValues));
        }
        int ret = IptCoreInterface.get().importSymList(listItems, listValues, mDutyInfo);
        IptCoreDutyInfo dutyInfo = ret < 0 ? null : mDutyInfo;
        dutyEntry(dutyInfo);
        InputTracer.o(Ids.importSymList);
    }

    /**
     * 设置符号面板的lock状态
     * @param symCateList 需要设置的符号项的列表
     * @param symLockList 和符号项对应的lock状态
     */
    public void padSetLock(int[] symCateList, int[] symLockList) {
        InputTracer.i(Ids.padSetLock);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("padSetLock: SYMS=" + Arrays.toString(symCateList)
                    + ", LOCKS=" + Arrays.toString(symLockList));
        }
        IptCoreInterface.get().padSetLock(symCateList, symLockList);
        InputTracer.o(Ids.padSetLock);
    }

    /**
     * 获取符号面板的lock状态
     * @param symCateList 待获取的符号项的列表
     * @param symLockList 和符号项对应的lock状态
     * @return 需要补返回值，保证外部等待内核线程执行完毕获取结果
     */
    public int padGetLock(int[] symCateList, int[] symLockList) {
        InputTracer.i(Ids.padGetLock);
        IptCoreInterface.get().padGetLock(symCateList, symLockList);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("padGetLock: SYMS=" + Arrays.toString(symCateList)
                    + ", LOCKS=" + Arrays.toString(symLockList));
        }
        InputTracer.o(Ids.padGetLock);
        return 0;
    }

    /**
     * 符号面板导入符号，更多符号面板重新排序
     * @param symList 导入符号
     * @param length 导入符号数量
     */
    public void padSetSymFilter(char[][] symList, int length) {
        InputTracer.i(Ids.padSetSymFilter);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("padSetSymFilter: symsLen=" + length);
        }
        IptCoreInterface.get().padSetSymFilter(symList, length);
        InputTracer.o(Ids.padSetSymFilter);
    }

    public void actShiftLongDown() {
        InputTracer.i(Ids.actShiftLongDown);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("actShiftLongDown");
        }
        IptCoreInterface.get().actShiftLongDown();
        InputTracer.o(Ids.actShiftLongDown);
    }

    public void actShiftLongUp() {
        InputTracer.i(Ids.actShiftLongUp);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("actShiftLongUp");
        }
        IptCoreInterface.get().actShiftLongUp();
        InputTracer.o(Ids.actShiftLongUp);
    }

    public String actCorrectVoiceData(String orgResult) {
        InputTracer.i(Ids.actCorrectVoiceData);
        String result = IptCoreInterface.get().actCorrectVoiceData(orgResult);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("actCorrectVoiceData: oriResult=" + orgResult + ", result=" + result);
        }
        InputTracer.o(Ids.actCorrectVoiceData);
        return result;
    }

    public int actCorrectVoiceSend() {
        InputTracer.i(Ids.actCorrectVoiceSend);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("actCorrectVoiceSend");
        }
        int result = IptCoreInterface.get().actCorrectVoiceSend();
        InputTracer.o(Ids.actCorrectVoiceSend);
        return result;
    }

    public int actCheckClip(char[] uniArray, int uniLen) {
        InputTracer.i(Ids.actCheckClip);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("actCheckClip");
        }
        int result = IptCoreInterface.get().actCheckClip(uniArray, uniLen);
        InputTracer.o(Ids.actCheckClip);
        return result;
    }

    public int actAdjustEmojiRelation(int emojiValue, int cellId) {
        InputTracer.i(Ids.actAdjustEmojiRelation);
        int result = IptCoreInterface.get().actAdjustEmojiRelation(emojiValue, cellId);
        InputTracer.o(Ids.actAdjustEmojiRelation);
        return result;
    }

    /**
     * 发起面板事件
     *
     * @param padEvent 面板事件
     */
    public void sendPadEvent(final int padEvent, final boolean restart) {
        InputTracer.i(Ids.sendPadEvent);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("sendPadEvent: " + padEvent);
        }
        IptCoreInterface.get().sendPadEvent(padEvent, restart);
        InputTracer.o(Ids.sendPadEvent);
    }

    /**
     * 切换面板
     *
     * @param corePadId 内核的面板id
     */
    public void switchKeyboard(final int corePadId) {
        InputTracer.i(Ids.switchKeyboard);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("switchKeyboard: " + corePadId);
        }
        int ret = IptCoreInterface.get().padSwitch(corePadId, mDutyInfo);
        IptCoreDutyInfo dutyInfo = ret < 0 ? null : mDutyInfo;
        dutyEntry(dutyInfo);

        mPadId = corePadId;
        InputTracer.o(Ids.switchKeyboard);
    }

    public int getCurrentPadId() {
        InputTracer.i(Ids.getCurrentPadId);
        InputTracer.o(Ids.getCurrentPadId);
        return mPadId;
    }

    public int getPadId() {
        InputTracer.i(Ids.getPadId);
        int result = IptCoreInterface.get().getPadId();
        InputTracer.o(Ids.getPadId);
        return result;
    }

    /**
     * 中英功能键切换面板
     *
     * @param padId 指定的面板id
     */
    public void actCnEnKey(final int padId) {
        InputTracer.i(Ids.actCnEnKey);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("actCnEnKey: " + padId);
        }
        int ret = IptCoreInterface.get().actCnEnKey(padId, mDutyInfo);
        IptCoreDutyInfo dutyInfo = ret < 0 ? null : mDutyInfo;
        dutyEntry(dutyInfo);

        InputTracer.o(Ids.actCnEnKey);
    }
    /**
     * 生僻字面板【部/写】切换键（效果与按键事件传入FKEY_RARE_CHAIZI_HW相同）
     *
     * @param padId 指定的面板id
     */
    public void actRareChaiZiHwKey(final int padId) {
        InputTracer.i(Ids.actRareChaiZiHwKey);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("actRareChaiZiHwKey: " + padId);
        }
        int ret = IptCoreInterface.get().actRareChaiZiHwKey(padId, mDutyInfo);
        IptCoreDutyInfo dutyInfo = ret < 0 ? null : mDutyInfo;
        dutyEntry(dutyInfo);

        InputTracer.o(Ids.actRareChaiZiHwKey);
    }

    /**
     * 设置默认的面板id
     *
     * @param padId 指定的面板id
     */
    public void setDefaultPad(final int padId) {
        InputTracer.i(Ids.setDefaultPad);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("setDefaultPad: " + padId);
        }
        IptCoreInterface.get().setDefaultPad(padId);
        InputTracer.o(Ids.setDefaultPad);
    }

    public boolean getStateValue(int stateIndex) {
        InputTracer.i(Ids.getStateValue);
        boolean result = getTipState(stateIndex);
        InputTracer.o(Ids.getStateValue);
        return result;
    }

    public String getHwPinyinStr(char unicode, boolean isAll) {
        InputTracer.i(Ids.getHwPinyinStr);
        String result = IptCoreInterface.get().getHwPinyin(unicode, isAll ? 1 : 0);
        InputTracer.o(Ids.getHwPinyinStr);
        return result;
    }

    public int getCandCount() {
        InputTracer.i(Ids.getCandCount);
        int result = IptCoreInterface.get().getCandCount();
        InputTracer.o(Ids.getCandCount);
        return result;
    }

    /**
     * 获取智能云词卡片的Loading状态
     */
    public boolean getAIPadLoadingState() {
        InputTracer.i(Ids.getAIPadLoadingState);
        boolean result = IptCoreInterface.get().getAIPadLoadingState();
        InputTracer.o(Ids.getAIPadLoadingState);
        return result;
    }

    /**
     * 获取智能云词卡片的tab
     */
    public int getAIPadTab() {
        InputTracer.i(Ids.getAIPadTab);
        int ret = IptCoreInterface.get().getAIPadTab();
        InputTracer.o(Ids.getAIPadTab);
        return ret >= 0 ? ret : ImeCoreConsts.AIPadType.AI_PAD_TAB_NONE;
    }

    /**
     * 获取智能云词卡片的状态
     */
    public int getAIPadState() {
        InputTracer.i(Ids.getAIPadState);
        int ret = IptCoreInterface.get().getAIPadState();
        InputTracer.o(Ids.getAIPadState);
        return ret >= 0 ? ret : ImeCoreConsts.AIPadState.AI_PAD_STAT_APP_NOT_SUPPORT;
    }

    /**
     * 获取智能云词卡片的候选词的数目
     */
    public int getAIPadCnt() {
        InputTracer.i(Ids.getAIPadCnt);
        int ret = IptCoreInterface.get().getAIPadCnt();
        InputTracer.o(Ids.getAIPadCnt);
        return ret >= 0 ? ret : 0;
    }

    /**
     * 获取智能云词指定index的候选词
     */
    public IptCoreCandInfo getAIPadItem(int index) {
        InputTracer.i(Ids.getAIPadItem);
        IptCoreCandInfo candInfo = new IptCoreCandInfo();
        int ret = IptCoreInterface.get().getAIPadItem(index, candInfo);
        InputTracer.o(Ids.getAIPadItem);
        return ret >= 0 ? candInfo : null;
    }

    /**
     * 获取智能云词发起请求的原始词
     */
    public String getAIPadOriginText() {
        InputTracer.i(Ids.getAIPadOriginText);
        String result = IptCoreInterface.get().getAIPadOriginText();
        InputTracer.o(Ids.getAIPadOriginText);
        return result;
    }

    /**
     * 获取AI Pad是否是自动打开
     */
    public boolean getAIPabIsAutoOpen() {
        InputTracer.i(Ids.getAIPabIsAutoOpen);
        boolean result = IptCoreInterface.get().getAIPabIsAutoOpen();
        InputTracer.o(Ids.getAIPabIsAutoOpen);
        return result;
    }

    public IptCoreCandInfo getAICand() {
        InputTracer.i(Ids.getAICand);
        IptCoreCandInfo candInfo = new IptCoreCandInfo();
        int ret = IptCoreInterface.get().getAICand(candInfo);
        InputTracer.o(Ids.getAICand);
        return ret >= 0 ? candInfo : null;
    }

    public int getAICandIconState() {
        InputTracer.i(Ids.getAICandIconState);
        int result = IptCoreInterface.get().getAICandIconState();
        InputTracer.o(Ids.getAICandIconState);
        return result;
    }

    /**
     * 获得AI纠错的半上屏状态
     */
    public IptCoreCandInfo getAICorrectInlineInfo() {
        InputTracer.i(Ids.getAICorrectInlineInfo);
        IptCoreCandInfo candInfo = new IptCoreCandInfo();
        final int ret = IptCoreInterface.get().getAICorrectInlineInfo(candInfo);
        InputTracer.o(Ids.getAICorrectInlineInfo);
        return ret >= 0 ? candInfo : null;
    }

    /**
     * 检测该词中是否包含敏感词。如果包含，就不应该发起神句配图等网络请求
     */
    public boolean checkHitBlackList(String content) {
        InputTracer.i(Ids.checkHitBlackList);
        boolean result = IptCoreInterface.get().checkHitBlackList(content);
        InputTracer.o(Ids.checkHitBlackList);
        return result;
    }

    /**
     * 加密AI卡片请求数据
     *
     * @param checkId    校验id
     * @param inContent  需要加密的原始数据
     * @return 加密结果
     */
    public byte[] encryptionAICardRequestData(int checkId, byte[] inContent) {
        InputTracer.i(Ids.encryptionAICardRequestData);
        byte[] result = IptCoreInterface.get().encryptionAICardRequestData(checkId, inContent);
        InputTracer.o(Ids.encryptionAICardRequestData);
        return result;
    }

    /**
     * 解密AI卡片返回数据
     * @param checkId 校验id
     * @param inContent 解密后的数据
     * @return 解密结果
     */
    public byte[] decryptAICardResponseData(int checkId, byte[] inContent) {
        InputTracer.i(Ids.decryptAICardResponseData);
        byte[] result = IptCoreInterface.get().decryptAICardResponseData(checkId, inContent);
        InputTracer.o(Ids.decryptAICardResponseData);
        return result;
    }

    public IptCoreCandInfo getCandItem(int index) {
        InputTracer.i(Ids.getCandItem);
        IptCoreCandInfo candInfo = new IptCoreCandInfo();
        int ret = IptCoreInterface.get().getCandItem(index, candInfo);
        if (ret < 0) {
            candInfo = null;
        }
        InputTracer.o(Ids.getCandItem);
        return candInfo;
    }

    public List<IptCoreCandInfo> getAiCandItems() {
        InputTracer.i(Ids.getAiCandItems);
        List<IptCoreCandInfo> result = new ArrayList<>();
        int count = IptCoreInterface.get().getAiCandItemCount();
        for (int i = 0; i < count; i++) {
            IptCoreCandInfo candInfo = new IptCoreCandInfo();
            int ret = IptCoreInterface.get().getAiCandItem(i, candInfo);
            if (ret >= 0) {
                result.add(candInfo);
            }
        }
        InputTracer.o(Ids.getAiCandItems);
        return result;
    }

    /**
     * 批量加载候选词
     * @param start 起始索引
     * @param length 加载长度
     * @return 实际加载的长度（返回值是用于保证发起线程等待结果返回）
     */
    public int loadCandFromCore(int start, int length, ICandLoadCallback callback) {
        InputTracer.i(Ids.loadCandFromCore);
        if (start < 0 || length <= 0) {
            if (callback != null) {
                callback.onResult(null, 0);
            }
            InputTracer.o(Ids.loadCandFromCore);
            return 0;
        }
        IptCoreCandInfo[] resultArray = new IptCoreCandInfo[length];
        for (int i = 0; i < length; i++) {
            resultArray[i] = getCandItem(start + i);
        }
        if (callback != null) {
            callback.onResult(resultArray, start);
        }
        InputTracer.o(Ids.loadCandFromCore);
        return length;
    }

    public int getRareCandCount() {
        InputTracer.i(Ids.getRareCandCount);
        int result = IptCoreInterface.get().getRareCandCount();
        InputTracer.o(Ids.getRareCandCount);
        return result;
    }

    public IptCoreCandInfo getRareCandItem(int index) {
        InputTracer.i(Ids.getRareCandItem);
        IptCoreCandInfo candInfo = new IptCoreCandInfo();
        int ret = IptCoreInterface.get().getRareCandItem(index, candInfo);
        if (ret < 0) {
            candInfo = null;
        }
        InputTracer.o(Ids.getRareCandItem);
        return candInfo;
    }

    /**
     * 批量加载候选词
     * @param start 起始索引
     * @param length 加载长度
     * @return 实际加载的长度（返回值是用于保证发起线程等待结果返回）
     */
    public int loadRareCandFromCore(int start, int length, ICandLoadCallback callback) {
        InputTracer.i(Ids.loadRareCandFromCore);
        if (start < 0 || length <= 0) {
            if (callback != null) {
                callback.onResult(null, 0);
            }
            InputTracer.o(Ids.loadRareCandFromCore);
            return 0;
        }
        IptCoreCandInfo[] resultArray = new IptCoreCandInfo[length];
        for (int i = 0; i < length; i++) {
            resultArray[i] = getRareCandItem(start + i);
        }
        if (callback != null) {
            callback.onResult(resultArray, start);
        }
        InputTracer.o(Ids.loadRareCandFromCore);
        return length;
    }

    @Deprecated
    public IptCoreShowInfo getInputShow() {
        InputTracer.i(Ids.getInputShow);
        IptCoreShowInfo showInfo = new IptCoreShowInfo();
        int ret = IptCoreInterface.get().getInputShow(showInfo, showInfo.cursorInfo(),
                showInfo.autofixInfo());
        boolean success = ret >= 0;
        InputTracer.o(Ids.getInputShow);
        return success ? showInfo : null;
    }

    public boolean getInputShow(IptCoreShowInfo outputShowInfo) {
        InputTracer.i(Ids.getInputShow1);
        int ret = IptCoreInterface.get().getInputShow(outputShowInfo, outputShowInfo.cursorInfo(),
                outputShowInfo.autofixInfo());
        InputTracer.o(Ids.getInputShow1);
        return ret >= 0;
    }

    public int getListCount() {
        InputTracer.i(Ids.getListCount);
        int result = IptCoreInterface.get().getListCount();
        InputTracer.o(Ids.getListCount);
        return result;
    }

    public IptCoreListInfo getListItem(int index) {
        InputTracer.i(Ids.getListItem);
        IptCoreListInfo info = new IptCoreListInfo();
        int ret = IptCoreInterface.get().getListItem(index, info);
        if (ret >= 0) {
            InputTracer.o(Ids.getListItem);
            return info;
        } else {
            InputTracer.o(Ids.getListItem);
            return null;
        }
    }

    public int getSugCount() {
        InputTracer.i(Ids.getSugCount);
        int result = IptCoreInterface.get().getSugCount();
        InputTracer.o(Ids.getSugCount);
        return result;
    }

    /**
     * 获取sug广告超时时间
     */
    public int getSugAdTimeoutMs() {
        int result = IptCoreInterface.get().getSugAdTimeoutMs();
        return result;
    }

    public IptCoreCandInfo getSugAt(int index) {
        InputTracer.i(Ids.getSugAt);
        IptCoreCandInfo candInfo = new IptCoreCandInfo();
        int ret = IptCoreInterface.get().getSugItem(index, candInfo);
        if (ret < 0) {
            candInfo = null;
        }
        InputTracer.o(Ids.getSugAt);
        return candInfo;
    }

    public int getSugCardCount() {
        InputTracer.i(Ids.getSugCardCount);
        int result = IptCoreInterface.get().getSugCardCount();
        InputTracer.o(Ids.getSugCardCount);
        return result;
    }

    public IptCoreSugCardInfo getSugCardAt(int index) {
        InputTracer.i(Ids.getSugCardAt);
        IptCoreSugCardInfo sugCardInfo = new IptCoreSugCardInfo();
        int ret = IptCoreInterface.get().getSugCardItem(index, sugCardInfo);
        if (ret < 0) {
            sugCardInfo = null;
        }
        InputTracer.o(Ids.getSugCardAt);
        return sugCardInfo;
    }

    public int getSugSelectedPosition() {
        InputTracer.i(Ids.getSugSelectedPosition);
        int result = IptCoreInterface.get().getSugSelect();
        InputTracer.o(Ids.getSugSelectedPosition);
        return result;
    }

    public int getSugCardSelectedPosition() {
        InputTracer.i(Ids.getSugCardSelectedPosition);
        int result = IptCoreInterface.get().getSugCardSelect();
        InputTracer.o(Ids.getSugCardSelectedPosition);
        return result;
    }

    public int getSugState() {
        InputTracer.i(Ids.getSugState);
        int result = IptCoreInterface.get().getSugState();
        InputTracer.o(Ids.getSugState);
        return result;
    }

    @Deprecated
    public int getPyHotLetter(byte[] alphas) {
        return IptCoreInterface.get().getPyHotLetter(alphas);
    }

    public void actCurSugClose() {
        InputTracer.i(Ids.actCurSugClose);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("actCurSugClose");
        }
        IptCoreInterface.get().actCurSugClose();
        InputTracer.o(Ids.actCurSugClose);
    }

    public void userTraceStartPadKeyLayout() {
        InputTracer.i(Ids.userTraceStartPadKeyLayout);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("userTraceStartPadKeyLayout");
        }
        IptCoreInterface.get().userTraceStartPadKeyLayout();
        InputTracer.o(Ids.userTraceStartPadKeyLayout);
    }

    public void userTraceFinishPadKeyLayout() {
        InputTracer.i(Ids.userTraceFinishPadKeyLayout);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("userTraceFinishPadKeyLayout");
        }
        IptCoreInterface.get().userTraceFinishPadKeyLayout();
        InputTracer.o(Ids.userTraceFinishPadKeyLayout);
    }

    public void userTraceWrite(int action, int container, byte[] content) {
        InputTracer.i(Ids.userTraceWrite);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("userTraceWrite: action=" + action + ", container:" + container);
        }
        IptCoreInterface.get().userTraceWrite(action, container, content);
        InputTracer.o(Ids.userTraceWrite);
    }

    public void userVoiceInputLog(String content) {
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("userVoiceInputLog");
        }
        IptCoreInterface.get().userVoiceInputLog(content);
    }

    public int getCandInfoCount() {
        InputTracer.i(Ids.getCandInfoCount);
        int result = IptCoreInterface.get().getCandInfoCount();
        InputTracer.o(Ids.getCandInfoCount);
        return result;
    }

    public String getCandInfo(int index) {
        InputTracer.i(Ids.getCandInfo);
        String result = IptCoreInterface.get().getCandInfo(index);
        InputTracer.o(Ids.getCandInfo);
        return result;
    }

    public int getContactCount() {
        InputTracer.i(Ids.getContactCount);
        int result = IptCoreInterface.get().getContactCount();
        InputTracer.o(Ids.getContactCount);
        return result;
    }

    public IptContactItem getContactItem(int index) {
        InputTracer.i(Ids.getContactItem);
        IptContactItem item = new IptContactItem();
        int ret = IptCoreInterface.get().getContactItem(index, item);
        if (ret >= 0) {
            return item;
        } else {
            InputTracer.o(Ids.getTriggerWordItemItem);
            return null;
        }
    }

    public int getTriggerWordCount() {
        return IptCoreInterface.get().getTriggerWordCount();
    }

    public IptTriggerWordItem getTriggerWordItemItem(int index) {
        IptTriggerWordItem item = new IptTriggerWordItem();
        int ret = IptCoreInterface.get().getTriggerWordItem(index, item);
        if (ret >= 0) {
            return item;
        } else {
            return null;
        }
    }

    public int getMapTriggerWordCount() {
        return IptCoreInterface.get().getMapTriggerWordCount();
    }

    public IptMapTriggerWordItem getMapTriggerWordItemItem(int index) {
        IptMapTriggerWordItem item = new IptMapTriggerWordItem();
        int ret = IptCoreInterface.get().getMapTriggerWordItem(index, item);
        if (ret >= 0) {
            return item;
        } else {
            return null;
        }
    }

    public long getTriggerWordItemsId() {
        return IptCoreInterface.get().getTriggerWordItemsId();
    }

    public void sendLocationEvent(float latitude, float longitude) {
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("sendLocationEvent: latitude=" + latitude + ",longitude=" + longitude);
        }
        IptCoreInterface.get().sendLocationEvent(latitude, longitude);
    }

    public String getCandContext(int idx) {
        InputTracer.i(Ids.getCandContext);
        String result = IptCoreInterface.get().getCandContext(idx);
        InputTracer.o(Ids.getCandContext);
        return result;
    }

    public int getHwSmartDelayTime() {
        InputTracer.i(Ids.getHwSmartDelayTime);
        int result = IptCoreInterface.get().getHwSmartDelayTime();
        InputTracer.o(Ids.getHwSmartDelayTime);
        return result;
    }

    public char[] getEgg() {
        InputTracer.i(Ids.getEgg);
        char[] result = IptCoreInterface.get().getEgg();
        InputTracer.o(Ids.getEgg);
        return result;
    }

    public int getSrvCloudWhiteVer() {
        InputTracer.i(Ids.getSrvCloudWhiteVer);
        int result = IptCoreInterface.get().getSrvCloudWhiteVer();
        InputTracer.o(Ids.getSrvCloudWhiteVer);
        return result;
    }

    public int getSugType() {
        InputTracer.i(Ids.getSugType);
        int result = IptCoreInterface.get().getSugType();
        InputTracer.o(Ids.getSugType);
        return result;
    }

    public int getSugActionType() {
        InputTracer.i(Ids.getSugActionType);
        int result = IptCoreInterface.get().getSugActionType();
        InputTracer.o(Ids.getSugActionType);
        return result;
    }

    public int getSugActionType(int index) {
        return IptCoreInterface.get().getSugActionTypeByIndex(index);
    }

    public String getSugAdGlobalId(int index) {
        return IptCoreInterface.get().getSugAdGlobalId(index);
    }

    public int getSugSourceId() {
        InputTracer.i(Ids.getSugSourceId);
        int result = IptCoreInterface.get().getSugSourceId();
        InputTracer.o(Ids.getSugSourceId);
        return result;
    }

    public int getSugId() {
        return IptCoreInterface.get().getSugId();
    }

    public String getSugSourceMsg() {
        InputTracer.i(Ids.getSugSourceMsg);
        String result = IptCoreInterface.get().getSugSourceMsg();
        InputTracer.o(Ids.getSugSourceMsg);
        return result;
    }

    public void setPadLayout(int[] rect, boolean isSplit) {
        InputTracer.i(Ids.setPadLayout);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("setPadLayout: ["
                    + rect[0] + "," + rect[1] + "-" + rect[2] + "," + rect[3] + "], isSplit:" + isSplit);
        }
        IptCoreInterface.get().setPadLayout(rect, isSplit);
        InputTracer.o(Ids.setPadLayout);
    }

    public void setPadKeyPos(byte keyId, int[] viewRect, int[] touchRect) {
        InputTracer.i(Ids.setPadKeyPos);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("setPadKeyPos: " + keyId + ", vrect=["
                    + viewRect[0] + "," + viewRect[1] + "-" + viewRect[2] + "," + viewRect[3]
                    + "], trect=["
                    + touchRect[0] + "," + touchRect[1] + "-" + touchRect[2] + "," + touchRect[3]
                    + "]");
        }
        IptCoreInterface.get().setPadKeyPos(keyId, viewRect, touchRect);
        InputTracer.o(Ids.setPadKeyPos);
    }

    public int getTouchedKey(int x, int y, long timeoutMillis) {
        InputTracer.i(Ids.getTouchedKey);
        int coreKeyChar = IptCoreInterface.get().getTouchedKey(x, y);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("getTouchedKey: " + x + ", " + y + ", get:" + coreKeyChar);
        }
        InputTracer.o(Ids.getTouchedKey);
        return coreKeyChar;
    }



    private boolean getTipState(int stateIndex) {
        InputTracer.i(Ids.getTipState);
        boolean result = (tipStates & stateIndex) != 0;
        InputTracer.o(Ids.getTipState);
        return result;
    }

    public int getTipStates() {
        InputTracer.i(Ids.getTipStates);
        InputTracer.o(Ids.getTipStates);
        return tipStates;
    }

    @Deprecated
    public IptCoreDutyInfo actKeyClickInternal(int keyId, int inputType, String uni) {
        InputTracer.i(Ids.actKeyClickInternal);
        int ret = IptCoreInterface.get().actKeyClick(keyId, inputType, uni, mDutyInfo);
        mDutyInfo.addCoreActionKeyId(keyId);
        InputTracer.o(Ids.actKeyClickInternal);
        return ret < 0 ? null : mDutyInfo;
    }

    public IptCoreDutyInfo actKeyClickInternal(int keyId, int x, int y, int inputType, String uni) {
        InputTracer.i(Ids.actKeyClickInternal1);
        int ret = IptCoreInterface.get().actKeyClickXY(keyId, x, y, inputType, uni, mDutyInfo);
        mDutyInfo.addCoreActionKeyId(keyId);
        InputTracer.o(Ids.actKeyClickInternal1);
        return ret < 0 ? null : mDutyInfo;
    }

    public IptCoreDutyInfo actKeyClickInternal(int keyId, int x, int y, byte power,
                                               byte area, int inputType, String uni) {
        InputTracer.i(Ids.actKeyClickInternal2);
        int ret = IptCoreInterface.get().actKeyClickTouchInfo(keyId, x, y, power, area,
                inputType, uni, mDutyInfo);
        mDutyInfo.addCoreActionKeyId(keyId);
        InputTracer.o(Ids.actKeyClickInternal2);
        return ret < 0 ? null : mDutyInfo;
    }

    public interface ICandLoadCallback {
        void onResult(IptCoreCandInfo[] result, int offset);
    }

    /**
     * 清理所有可写词库：细胞词、(中/英)自造词、联系人词、注音用户自造词
     * @return 恢复成功返回0，失败返回-1
     */
    public int writableDictReset() {
        InputTracer.i(Ids.writableDictReset);
        int ret = IptCoreInterface.get().writableDictReset();
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("writableDictReset : " + ret);
        }
        InputTracer.o(Ids.writableDictReset);
        return ret;
    }

    /**
     * 执行内核的callback
     * @param tag       内核给出的callback的tag
     * @param callBack  内核给出的callback地址
     */
    public void execCallback(int tag, long callBack) {
        InputTracer.i(Ids.execCallback);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("execCallback, tag:" + tag + " callBack: " + callBack);
        }
        IptCoreInterface.get().execCallback(tag, callBack);
        InputTracer.o(Ids.execCallback);
    }

    public void actAiFontGeneratedNotify(int type, String showContent, String scheme
            , String insertContent, long displayTime) {
        InputTracer.i(Ids.actAiFontGeneratedNotify);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod(
                    "actAiFontGeneratedNotify, type:" + type + " showContent: "
                            + showContent + " scheme: " + scheme + " displayTime: " + displayTime
                            + " insertContent: " + insertContent);
        }
        IptCoreInterface.get().actAiFontGeneratedNotify(type, showContent, scheme
                , insertContent, displayTime);
        InputTracer.o(Ids.actAiFontGeneratedNotify);
    }

    @WorkerThread
    public int getIntentStyle(String content) {
        InputTracer.i(Ids.getIntentStyle);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod(
                    "getIntentStyle, content:" + content);
        }
        int result = IptCoreInterface.get().getIntentStyle(content);
        InputTracer.o(Ids.getIntentStyle);
        return result;
    }

    public int aiFontRecoPoint(String character, int[] points) {
        InputTracer.i(Ids.aiFontRecoPoint);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod(
                    "aiFontRecoPoint, character:"
                            + character + " points: " + points.length);
        }
        int point = 0;
        IptAiHwRes iptAiHwRes = new IptAiHwRes();
        IptCoreInterface.get().aiFontRecoPoint(iptAiHwRes, 10, points);
        List<String> characters = iptAiHwRes.getCharacters();
        if (!CollectionUtil.isEmpty(characters)) {
            int count = characters.size();
            int index = characters.indexOf(character);
            if (-1 == index) {
                point = 0;
            } else {
                point = Math.round(100 - index * (100f / count));
            }
        }
        InputTracer.o(Ids.aiFontRecoPoint);
        return point;
    }

    public boolean loadAiFontHwModel(Context context, String path, String licensePath) {
        InputTracer.i(Ids.loadAiFontHwModel);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("loadAiFontHwModel, path:" + path);
        }
        boolean result = IptCoreInterface.get().loadAiFontHwModel(context, path, licensePath);
        InputTracer.o(Ids.loadAiFontHwModel);
        return result;
    }

    public void unLoadAiFontHwModel() {
        InputTracer.i(Ids.unLoadAiFontHwModel);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("unLoadAiFontHwModel");
        }
        IptCoreInterface.get().unLoadAiFontHwModel();
        InputTracer.o(Ids.unLoadAiFontHwModel);
    }

    public int aiFontHwModelVersion() {
        InputTracer.i(Ids.aiFontHwModelVersion);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("aiFontHwModelVersion");
        }
        int result = IptCoreInterface.get().aiFontHwModelVersion();
        InputTracer.o(Ids.aiFontHwModelVersion);
        return result;
    }

    public void actToolbarClick() {
        InputTracer.i(Ids.actToolbarClick);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("actToolbarClick");
        }
        IptCoreInterface.get().actToolbarClick();
        InputTracer.o(Ids.actToolbarClick);
    }

    @Nullable
    public String getInlineShow() {
        InputTracer.i(Ids.getInlineShow);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("getInlineShow");
        }
        String result = IptCoreInterface.get().getInlineShow();
        InputTracer.o(Ids.getInlineShow);
        return result;
    }

    public boolean enableGaussTrain(boolean forcible) {
        InputTracer.i(Ids.enableGaussTrain);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod(
                    "enableGaussTrans, forcible:" + forcible);
        }
        boolean result = IptCoreInterface.get().trainGaussUser(forcible);
        InputTracer.o(Ids.enableGaussTrain);
        return result;
    }

    public void setCloudIntention(int type, boolean isOn) {
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("setCloudIntention");
        }
        IptCoreInterface.get().setCloudIntention(type, isOn);
    }

    public void setContactPermissionStatus(boolean isOn) {
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("setContactPermissionStatus");
        }
        IptCoreInterface.get().setContactPermissionStatus(isOn);
    }

    /**
     * 获取云端意图卡片的数量
     */
    public int getCloudIntentionCount() {
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("getCloudIntentionCount");
        }
        return IptCoreInterface.get().getCloudIntentionCount();
    }

    /**
     * 获取云端意图卡片的数量
     */
    public int getCloudIntention() {
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("getCloudIntention");
        }
        return IptCoreInterface.get().getCloudIntention();
    }

    /**
     * 获取云端意图卡片的数据
     */
    public IptIntentItem getCloudIntentionItem(int index) {
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("getCloudIntentionItem");
        }

        IptIntentItem iptIntentItem = new IptIntentItem();
        int ret = IptCoreInterface.get().getCloudIntentionItem(index, iptIntentItem);
        if (ret >= 0) {
            return iptIntentItem;
        }
        return null;
    }

    public void sendWmSugAdAction(int index, int sugActionType) {
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("sendWmSugClickAction");
        }
        IptCoreInterface.get().sendWmSugAdAction(index, sugActionType);
    }

    public void sendCloudIntentionCardAction(int firstLevel, int secondLevel, String title, int actionType) {
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("sendCloudIntentionCardAction, firstLevel="
                    + firstLevel + ", secondLevel=" + secondLevel + ", title=" + title
                    + ", actionType=" + actionType);
        }
        IptCoreInterface.get().sendCloudIntentionCardAction(firstLevel, secondLevel, title, actionType);
    }

    public void loadCoreSugAd(AppAdTrigger trigger) {
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("loadCoreSugAd");
        }
        IptCoreInterface.get().loadCoreSugAd(trigger.pos);
    }

    public int getCoreSugAdCount(AppAdTrigger trigger) {
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("getCoreSugAdCount");
        }
        return IptCoreInterface.get().getCoreSugAdCount(trigger.pos);
    }

    public IptCoreSugAdInfo getCoreSugAdAt(int index, AppAdTrigger trigger) {
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("getCoreSugAdAt");
        }
        IptCoreSugAdInfo coreSugInfo = new IptCoreSugAdInfo();
        int ret = IptCoreInterface.get().getCoreSugAdAt(index, coreSugInfo, trigger.pos);
        if (ret < 0) {
            coreSugInfo = null;
        }
        return coreSugInfo;
    }

    public void coreSugAdTimeout(int time, AppAdTrigger trigger) {
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("coreSugAdTimeout: " + time);
        }
        IptCoreInterface.get().coreSugAdTimeout(time, trigger.pos);
    }

    public void coreSugAdWin(int index, AppAdTrigger trigger) {
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("coreSugAdWin");
        }
        IptCoreInterface.get().coreSugAdWin(index, trigger.pos);
    }

    public void coreSugAdFail(int index, int reason, long price, AppAdTrigger trigger) {
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("coreSugAdFail");
        }
        IptCoreInterface.get().coreSugAdFail(index, reason, price, trigger.pos);
    }

    public void coreSugAdShow(int index, Rect rect, AppAdTrigger trigger) {
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("coreSugAdFail");
        }
        int[] points = new int[]{rect.left, rect.top, rect.right, rect.bottom};
        IptCoreInterface.get().coreSugAdShow(index, points, trigger.pos);
    }

    public IptCoreSugAdAction coreSugAdClick(int index, AppAdTrigger trigger, Rect rect, Point downPoint, Point upPoint) {
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("coreSugAdClick");
        }
        IptCoreSugAdAction coreSugAdAction = new IptCoreSugAdAction();

        int[] bounds = new int[]{rect.left, rect.top, rect.right, rect.bottom};
        int[] clickPoints = new int[]{downPoint.x, downPoint.y, upPoint.x, upPoint.y};
        int ret = IptCoreInterface.get().coreSugAdClick(index, coreSugAdAction, trigger.pos, bounds, clickPoints);
        if (ret < 0) {
            coreSugAdAction = null;
        }
        return coreSugAdAction;
    }

    public void coreSugAdDownloadStart(int index, AppAdTrigger trigger) {
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("coreSugAdDownloadStart");
        }
        IptCoreInterface.get().coreSugAdDownloadStart(index, trigger.pos);
    }

    public void coreSugAdDownloadFinish(int index, AppAdTrigger trigger) {
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("coreSugAdDownloadFinish");
        }
        IptCoreInterface.get().coreSugAdDownloadFinish(index, trigger.pos);
    }

    public void coreSugAdInstallFinish(int index, AppAdTrigger trigger) {
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("coreSugAdInstallFinish");
        }
        IptCoreInterface.get().coreSugAdInstallFinish(index, trigger.pos);
    }

    public void coreSugAdOpenUrlApp(int index, AppAdTrigger trigger) {
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("coreSugAdOpenUrlApp");
        }
        IptCoreInterface.get().coreSugAdOpenUrlApp(index, trigger.pos);
    }

    public void coreSugAdOpenFallbackUrl(int index, AppAdTrigger trigger) {
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("coreSugAdOpenFallbackUrl");
        }
        IptCoreInterface.get().coreSugAdOpenFallbackUrl(index, trigger.pos);
    }

    public void coreSugAdDplSuccess(int index, AppAdTrigger trigger) {
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("coreSugAdDplSuccess");
        }
        IptCoreInterface.get().coreSugAdDplSuccess(index, trigger.pos);
    }

    public void coreSugAdDplFailed(int index, AppAdTrigger trigger) {
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("coreSugAdDplFailed");
        }
        IptCoreInterface.get().coreSugAdDplFailed(index, trigger.pos);
    }

    /**
     * 获取在联想词上的超会写icon是否需要展示
     * @return
     */
    public boolean getAIIconInLXNeedShow() {
        return IptCoreInterface.get().getAiIconInLXNeedShow();
    }

    public boolean isMatchSugWhiteData() {
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("isMatchSugWhiteData");
        }
        return IptCoreInterface.get().isMatchSugWhiteData();
    }

    public int getEncryptVersion() {
        InputTracer.i(Ids.getEncryptVersion);
        int result = IptCoreInterface.get().getEncryptVersion();
        InputTracer.o(Ids.getEncryptVersion);
        return result;
    }

    public int getEncryptB64Version() {
        InputTracer.i(Ids.getEncryptB64Version);
        int result = IptCoreInterface.get().getEncryptB64Version();
        InputTracer.o(Ids.getEncryptB64Version);
        return result;
    }

    public byte[] b64Encode(byte[] input) {
        InputTracer.i(Ids.b64Encode);
        byte[] result = IptCoreInterface.get().b64Encode(input);
        InputTracer.o(Ids.b64Encode);
        return result;
    }

    public byte[] b64Decode(byte[] input) {
        InputTracer.i(Ids.b64Decode);
        byte[] result = IptCoreInterface.get().b64Decode(input);
        InputTracer.o(Ids.b64Decode);
        return result;
    }

    public byte[] aesEncrypt(byte[] input) {
        InputTracer.i(Ids.aesEncrypt);
        byte[] result = IptCoreInterface.get().aesEncrypt(input);
        InputTracer.o(Ids.aesEncrypt);
        return result;
    }

    public byte[] aesEncryptV2(byte[] input) {
        InputTracer.i(Ids.aesEncryptV2);
        byte[] result = IptCoreInterface.get().aesEncryptV2(input);
        InputTracer.o(Ids.aesEncryptV2);
        return result;
    }

    public byte[] aesDecrypt(byte[] input) {
        InputTracer.i(Ids.aesDecrypt);
        byte[] result = IptCoreInterface.get().aesDecrypt(input);
        InputTracer.o(Ids.aesDecrypt);
        return result;
    }

    public byte[] aesB64Encrypt(byte[] input) {
        InputTracer.i(Ids.aesB64Encrypt);
        byte[] result = IptCoreInterface.get().aesB64Encrypt(input);
        InputTracer.o(Ids.aesB64Encrypt);
        return result;
    }

    public byte[] aesB64EncryptV2(byte[] input) {
        InputTracer.i(Ids.aesB64EncryptV2);
        byte[] result = IptCoreInterface.get().aesB64EncryptV2(input);
        InputTracer.o(Ids.aesB64EncryptV2);
        return result;
    }

    public byte[] aesB64Decrypt(byte[] input) {
        InputTracer.i(Ids.aesB64Decrypt);
        byte[] result = IptCoreInterface.get().aesB64Decrypt(input);
        InputTracer.o(Ids.aesB64Decrypt);
        return result;
    }

    public byte[] rsaEncrypt(byte[] input) {
        InputTracer.i(Ids.rsaEncrypt);
        byte[] result = IptCoreInterface.get().rsaEncrypt(input);
        InputTracer.o(Ids.rsaEncrypt);
        return result;
    }

    public byte[] rsaDecrypt(byte[] input) {
        InputTracer.i(Ids.rsaDecrypt);
        byte[] result = IptCoreInterface.get().rsaDecrypt(input);
        InputTracer.o(Ids.rsaDecrypt);
        return result;
    }

    public int doHonorNotepadAssociationQuery(String input, @IntRange(from = 0, to = 3) int mode, long requestId, int limitCount) {
        int ret = IptCoreInterface.get().doHonorIntelligentRequest(input, mode, requestId, limitCount);
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("doHonorNotepadAssociationQuery#input:" + input + ", mode:" + mode + ", request_id:"
                    + requestId + ", limitCount:" + limitCount + ", ret:" + ret);
        }
        return ret;
    }

    /**
     * dctSetNameByBuff
     *
     * @param app app名称
     * @param ts  时间戳
     * @param isNew 是否是全新安装
     * @return 返回值表示是否请求已经成功发起
     */
    public void dctSetNameByBuff(String app, long ts, boolean isNew) {
        if (Config.enableLog()) {
            MethodTracer.recordCoreMethod("dctSetNameByBuff" + app + ":" + ts + "," + isNew);
        }
        IptCoreInterface.get().dctSetNameByBuff(app, ts, isNew);
    }

}
