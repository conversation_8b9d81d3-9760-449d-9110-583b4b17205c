package com.baidu.iptcore;

import android.support.annotation.Keep;

import com.baidu.iptcore.info.IptAppMsgInfo;

/**
 * 需要提供给内核的平台环境接口
 *
 * Created by chend<PERSON>feng on 17/7/4.
 */
@Keep
public interface ImePlatformEnv {

    /**
     * 查找指定的app列表是否安装
     * @param appList 待检查的app信息列表，包括包名和version等信息
     * @return 列表中，首个已经安装且满足信息的应用的序号
     */
    int findApp(IptAppMsgInfo[] appList);

    /**
     * 请求客户端准备好 Ai助聊/cand活动/陪伴等显示所需要的资源(图片等)
     * @param urlList   请求的url列表
     * @param callback  客户端准备好资源后的回调指针,如果准备资源过程中,内核被析构,请放弃回调
     * @param tag       客户端准备好图片资源后,的回调参数
     */
    boolean requestUrlResource(String[] urlList, long callback, int tag);

    /**
     * 是否允许自动打开AiPad(Ai助聊卡片)
     * @param tabType 内核期望打开的tabtype
     * @return ture 表示允许自动打开
     */
    boolean isAllowAiPadAutoOpen(int tabType);

    /**
     * 是否能够显示智能云词的活动入口
     * @return true 表示允许显示
     */
    boolean isAllowCloudCampaignShow();

    /**
     * 是否能够给展示云输入广告
     * @return
     */
    boolean isAllowCloudAdShow();

    /**
     * 次 Cand 是否在 idle 状态展示高情商提示
     * @return
     */
    boolean isAllowMinorCandHighEQShow();

    /**
     * AI助聊次 Cand 活动是否展示
     * @param schemaType：活动对应的schema类型
     * @param schemaInfo：活动对应的schema信息
     * @return
     */
    boolean isCloudCampaignFinalyShow(String schemaType, String schemaInfo, long userVipCondition, long vipExpireDay, long vipExpireDayTo);

    /**
     * 通用触发词 根据会员类型判断是否需要显示
     * @param validPeople：服务端下发的会员匹配规则
     * @param belongType：所属的类型。参考{@link com.baidu.iptcore.info.IptIntentItem.TopLevelIntention}
     * @return true标示可以显示
     */
    boolean isCommonTriggerFinallyShow(String validPeople, int belongType);

    /**
     * 获得光标前指定长度的文本
     *
     * @param maxLength 内核接受的最长字符串长度
     * @return 光标前文本
     */
    String getEditBeforeCursor(int maxLength);

    /**
     * 获得光标后指定长度的文本
     *
     * @param maxLength 内核接受的最长字符串长度
     * @return 光标后文本
     */
    String getEditAfterCursor(int maxLength);

    /**
     * 获得当前框内是否有选中态
     *
     * @param maxLength 内核接受的最长字符串长度
     * @return 选中文本, 由于 maxLength 的存在，实际应用场景中会存在选中超过 maxLength 的情况，内核约定我们需要从0开始截取maxLength个长度的字符串
     */
    String getEditSelection(int maxLength);

    /**
     * @return 获取用户多轮输入内容,数据内容透传到服务器端用于模型识别意图
     */
    String getEditTextDialogue(int maxLength);

    /**
     * 获取用户当前代际灵感值，其中 -1 标示端上无法获取用户当前能量值，0 标示用户能量值为 0
     * @return
     */
    int getEnergyValue();

    /**
     * 面板切换前的回调
     * @param fromPadId 从哪个面板切换过来
     * @param toPadId 切换到的面板
     */
    void willEnterPad(int fromPadId, int toPadId);

    /**
     * 面板切换后的回调
     * @param fromPadId 从哪个面板切换过来
     * @param toPadId 切换到的面板
     */
    void didEnterPad(int fromPadId, int toPadId);


    /**
     * 是否安装了某个app
     * @param packageName：包名
     */
    boolean hasInstallApp(String packageName);

}
