/*
 * Copyright (C) 2017 Baidu, Inc. All Rights Reserved.
 */
package com.baidu.iptcore;

import com.baidu.iptcore.info.IptCoreDutyInfo;
import com.baidu.iptcore.util.CoreMethodCostRecorder;

import android.os.Handler;
import android.os.Handler.Callback;
import android.os.Looper;
import android.os.Message;
import android.support.annotation.Keep;

/**
 * 线程管理相关的回调
 *
 */
@Keep
public class IptCoreEnv implements Callback {

    /**
     * 主线程Handler
     */
    private Handler mHandler;

    IptCoreEnv() {
        mHandler = new Handler(Looper.myLooper(), this);
    }

    /**
     * Call From Core
     */
    @Keep
    public void runInMain(int tag, long arg1) {
        Message message = new Message();
        message.what = tag;
        message.obj = arg1;
        mHandler.sendMessageAtFrontOfQueue(message);
    }

    /**
     * Call From Core
     */
    @Keep
    public void runDelay(int millisec, int tag, long arg1) {

        Message message = new Message();
        message.what = tag;
        message.obj = arg1;
        mHandler.sendMessageDelayed(message, millisec);
    }

    /**
     * Call From Core
     */
    @Keep
    public void cancelRun(int tag) {
        mHandler.removeMessages(tag);
    }

    /**
     * 从java层获取一个dutyInfo实例
     */
    @Keep
    public Object getDutyInfo() {
        return new IptCoreDutyInfo();
    }

    /**
     * Call From Core
     */
    @Keep
    public void onDutyInfo(Object dutyInfo) {
        if (dutyInfo instanceof IptCoreDutyInfo) {
            IptCoreInterface.get().privateDutyInfoCallbackCore((IptCoreDutyInfo) dutyInfo);
        }
    }

    /**
     * Call From Core
     */
    @Keep
    public void onCoreMethodStart(int coreMethod, String msg) {
        CoreMethodCostRecorder.onCoreMethodStart(coreMethod, msg);
    }

    /**
     * Call From Core
     */
    @Keep
    public void onCoreMethodEnd(int coreMethod, String msg) {
        CoreMethodCostRecorder.onCoreMethodEnd(coreMethod, msg);
    }

    /**
     * Call From Core
     */
    @Keep
    public void onCoreInnerCost(int coreMethod, int cost) {
        CoreMethodCostRecorder.onCoreInnerCost(coreMethod, cost);
    }

    /**
     * Call From Core
     */
    @Keep
    public void onHonorNotepadGetResponse(String response, int ecode) {
        IptCoreInterface.get().privateAssociationInfoCallbackCore(response, ecode);
    }

    @Override
    public boolean handleMessage(Message msg) {
        IptCoreInterface.get().execCallback(msg.what, (Long) msg.obj);
        return false;
    }
}
