package com.baidu.iptcore;

import android.content.Context;
import android.content.pm.PackageInfo;
import android.support.annotation.IntRange;
import android.support.annotation.Keep;
import android.support.annotation.MainThread;
import android.support.annotation.WorkerThread;

import com.baidu.annotation.CoreThread;
import com.baidu.iptcore.dependency.AbsBuiltinIptFilesProvider;
import com.baidu.iptcore.dependency.BuiltinIptFilesComponent;
import com.baidu.iptcore.info.IptAiHwRes;
import com.baidu.iptcore.info.IptCoreSugAdAction;
import com.baidu.iptcore.info.IptCoreSugAdInfo;
import com.baidu.iptcore.info.IptIntentItem;
import com.baidu.iptcore.info.IptMapTriggerWordItem;
import com.baidu.iptcore.info.IptTriggerWordItem;
import com.baidu.iptcore.info.IptCellInfo;
import com.baidu.iptcore.info.IptContactItem;
import com.baidu.iptcore.info.IptCoreCandInfo;
import com.baidu.iptcore.info.IptCoreDutyInfo;
import com.baidu.iptcore.info.IptCoreListInfo;
import com.baidu.iptcore.info.IptCoreShowInfo;
import com.baidu.iptcore.info.IptCoreSugCardInfo;
import com.baidu.iptcore.info.IptIdmCellInfo;
import com.baidu.iptcore.info.IptKwdCellInfo;
import com.baidu.iptcore.info.IptPhraseGroup;
import com.baidu.iptcore.info.IptPhraseItem;
import com.baidu.iptcore.net.IptCloudStream;
import com.baidu.iptcore.util.Config;
import com.baidu.iptcore.util.MethodTracer;

/**
 * 内核所有的native接口
 */
@Keep
public class IptCoreInterface {

    /**
     * 内核的dutyCallback
     */
    public interface IptPrivateDutyCallback {
        /**
         * 执行内核传递的任务
         * @param dutyInfo 内核返回的duty
         */
        void onPrivateDutyInfo(IptCoreDutyInfo dutyInfo);
        /**
         * 执行内核传递的任务
         * @param response 内核返回的纠正信息
         */
        void onPrivateAssociationInfo(String response, int ecode);
    }

    /**
     * 输入法内核文件列表
     */
    public static String[] iptFiles;

    /**
     * 默认的输入法内置内核文件列表。
     * 保存需要拷贝的内核文件的最小集合即可。用于词典安装时，从Asset下读取词典列表有误时，替代的最小词典集合
     */
    public static String[] defBuiltinIptFiles;

    static {
        iptFiles = new String[ImeCoreConsts.IPTDICT_LENGTH];

        iptFiles[ImeCoreConsts.IPTDICT_INDEX_HZ] = ImeCoreConsts.IPTCORE_DICT_HZ;
        iptFiles[ImeCoreConsts.IPTDICT_INDEX_UZ] = ImeCoreConsts.IPTCORE_DICT_UZ;
        iptFiles[ImeCoreConsts.IPTDICT_INDEX_CELL] = ImeCoreConsts.IPTCORE_DICT_CELL;
        iptFiles[ImeCoreConsts.IPTDICT_INDEX_FT] = ImeCoreConsts.IPTCORE_DICT_FT;
        iptFiles[ImeCoreConsts.IPTDICT_INDEX_BH] = ImeCoreConsts.IPTCORE_DICT_BH;

        iptFiles[ImeCoreConsts.IPTDICT_INDEX_UE] = ImeCoreConsts.IPTCORE_DICT_UE;
        iptFiles[ImeCoreConsts.IPTDICT_INDEX_SYMBIN] = ImeCoreConsts.IPTCORE_DICT_SYM;
        iptFiles[ImeCoreConsts.IPTDICT_INDEX_SYMLBIN] = ImeCoreConsts.IPTCORE_DICT_SYML;
        iptFiles[ImeCoreConsts.IPTDICT_INDEX_CP] = ImeCoreConsts.IPTCORE_DICT_PHRASE;
        iptFiles[ImeCoreConsts.IPTDICT_INDEX_CATE] = ImeCoreConsts.IPTCORE_DICT_CONTACT;

        iptFiles[ImeCoreConsts.IPTDICT_INDEX_DEF] = ImeCoreConsts.IPTCORE_DICT_DEF;
        iptFiles[ImeCoreConsts.IPTDICT_INDEX_WB86] = ImeCoreConsts.IPTCORE_DICT_WB86;
        iptFiles[ImeCoreConsts.IPTDICT_INDEX_EN] = ImeCoreConsts.IPTCORE_DICT_EN;
        iptFiles[ImeCoreConsts.IPTDICT_INDEX_GRAM] = ImeCoreConsts.IPTCORE_DICT_GRAM;
        iptFiles[ImeCoreConsts.IPTDICT_INDEX_KEYWORD] = ImeCoreConsts.IPTCORE_DICT_KEYWORD;

        iptFiles[ImeCoreConsts.IPTDICT_INDEX_TTFFILTER] = ImeCoreConsts.IPTCORE_DICT_TTFFILTER;
        iptFiles[ImeCoreConsts.IPTDICT_INDEX_USERWORD_HW] = ImeCoreConsts.IPTCORE_DICT_UZW;
        iptFiles[ImeCoreConsts.IPTDICT_INDEX_MARSWORD] = ImeCoreConsts.IPTCORE_DICT_HUOXINGWEN;
        iptFiles[ImeCoreConsts.IPTDICT_INDEX_OTHERWORD] = ImeCoreConsts.IPTCORE_DICT_OTHERWORD;
        iptFiles[ImeCoreConsts.IPTDICT_INDEX_CANGJIE] = ImeCoreConsts.IPTCORE_DICT_CANGJIE;

        iptFiles[ImeCoreConsts.IPTDICT_INDEX_ZHUYIN_HZ] = ImeCoreConsts.IPTCORE_DICT_HZ_ZY;
        iptFiles[ImeCoreConsts.IPTDICT_INDEX_ZHUYIN_CZ] = ImeCoreConsts.IPTCORE_DICT_CZ_ZY;
        iptFiles[ImeCoreConsts.IPTDICT_INDEX_CH_COR] = ImeCoreConsts.IPTCORE_DICT_CH_COR;
        iptFiles[ImeCoreConsts.IPTDICT_INDEX_KP] = ImeCoreConsts.IPTCORE_DICT_KP;
        iptFiles[ImeCoreConsts.IPTDICT_INDEX_WT] = ImeCoreConsts.IPTCORE_DICT_WT;

        iptFiles[ImeCoreConsts.IPTDICT_INDEX_XHY] = ImeCoreConsts.IPTCORE_DICT_XHY;
        iptFiles[ImeCoreConsts.IPTDICT_INDEX_WT_BS] = ImeCoreConsts.IPTCORE_DICT_WT_BS;
        iptFiles[ImeCoreConsts.IPTDICT_INDEX_HZ_LABEL] = ImeCoreConsts.IPTCORE_DICT_HZ_LABEL;
        iptFiles[ImeCoreConsts.IPTDICT_INDEX_OT_IDMAP] = ImeCoreConsts.IPTCORE_DICT_IDMAP;
        iptFiles[ImeCoreConsts.IPTDICT_INDEX_CLOUD_KEYWORD] = ImeCoreConsts.IPTCORE_DICT_CLOUD_KEYWORD;

        iptFiles[ImeCoreConsts.IPTDICT_INDEX_CAND_CONTEXT] = ImeCoreConsts.IPTCORE_DICT_CXT;
        iptFiles[ImeCoreConsts.IPTDICT_INDEX_VKWORD] = ImeCoreConsts.IPTCORE_DICT_VKWORD;
        iptFiles[ImeCoreConsts.IPTDICT_INDEX_SEARCH] = ImeCoreConsts.IPTCORE_DICT_SEARCH;
        iptFiles[ImeCoreConsts.IPTDICT_INDEX_HZ_TONE] = ImeCoreConsts.IPTCORE_DICT_HZ_TONE;
        iptFiles[ImeCoreConsts.IPTDICT_INDEX_AUTOREPLY] = ImeCoreConsts.IPTCORE_DICT_AUTOREPLY;

        iptFiles[ImeCoreConsts.IPTDICT_INDEX_ZY_USER] = ImeCoreConsts.IPTCORE_DICT_CH_ZY_USER;
        iptFiles[ImeCoreConsts.IPTDICT_INDEX_US_BAK] = ImeCoreConsts.IPTCORE_DICT_US_BAK;
        iptFiles[ImeCoreConsts.IPTDICT_INDEX_CZ] = ImeCoreConsts.IPTCORE_DICT_CZ;
        iptFiles[ImeCoreConsts.IPTDICT_INDEX_LTP] = ImeCoreConsts.IPTCORE_DICT_LTP;
        iptFiles[ImeCoreConsts.IPTDICT_INDEX_LTP_USR] = ImeCoreConsts.IPTCORE_DICT_USR_TOUCH_FILE;

        iptFiles[ImeCoreConsts.IPTDICT_INDEX_APP_MAP] = ImeCoreConsts.IPTCORE_DICT_APP_MAP;
        iptFiles[ImeCoreConsts.IPTDICT_INDEX_PROV_CITY] = ImeCoreConsts.IPTCORE_DICT_PROV_CITY;
        iptFiles[ImeCoreConsts.IPTDICT_INDEX_VOICE_CORRECT] = ImeCoreConsts.IPTCORE_DICT_VOICE_CORRECT;
        iptFiles[ImeCoreConsts.IPTDICT_INDEX_SPECIAL] = ImeCoreConsts.IPTCORE_DICT_SPECIAL;
        iptFiles[ImeCoreConsts.IPTDICT_INDEX_WB98] = ImeCoreConsts.IPTCORE_DICT_WB98;

        iptFiles[ImeCoreConsts.IPTDICT_INDEX_CANGJIE_QUICK] = ImeCoreConsts.IPTCORE_DICT_CANGJIE_QUICK;
        iptFiles[ImeCoreConsts.IPTDICT_INDEX_USR3USER] = ImeCoreConsts.IPTCORE_DICT_USR3USER;
        iptFiles[ImeCoreConsts.IPTCORE_INDEX_HZCORRTONE] = ImeCoreConsts.IPTCORE_DICT_HZCORRTONE;
        iptFiles[ImeCoreConsts.IPTCORE_INDEX_ENUSER] = ImeCoreConsts.IPTCORE_DICT_ENUSER;
        iptFiles[ImeCoreConsts.IPTCORE_INDEX_PY3TRANSPROB] = ImeCoreConsts.IPTCORE_DICT_PY3TRANSPROB;
        iptFiles[ImeCoreConsts.IPTCORE_INDEX_SYM_USER] = ImeCoreConsts.IPTCORE_DICT_SYM_USER;
        iptFiles[ImeCoreConsts.IPTCORE_INDEX_USR3CELL] = ImeCoreConsts.IPTCORE_DICT_USER3CELL;
        iptFiles[ImeCoreConsts.IPTCORE_INDEX_ZY] = ImeCoreConsts.IPTCORE_DICT_ZY;
        iptFiles[ImeCoreConsts.IPTDICT_INDEX_EN_SYS] = ImeCoreConsts.IPTCORE_DICT_EN_SYS;
        iptFiles[ImeCoreConsts.IPTDICT_INDEX_CORRECTION] = ImeCoreConsts.IPTCORE_DICT_CORRECTION;
        iptFiles[ImeCoreConsts.IPTDICT_INDEX_SENSITIVE] = ImeCoreConsts.IPTCORE_DICT_SENSITIVE;
        iptFiles[ImeCoreConsts.IPTDICT_INDEX_PY_CORR_TONE] = ImeCoreConsts.PY_CORR_TONE;
        iptFiles[ImeCoreConsts.IPTDICT_INDEX_BLIND_ASSISTANCE] = ImeCoreConsts.BLIND_ASSISTANCE;
        iptFiles[ImeCoreConsts.IPTDICT_INEDEX_HW_LICENSE] = ImeCoreConsts.IPTCORE_DICT_HW_LICENSE;
        iptFiles[ImeCoreConsts.IPTDICT_INDEX_SENS] = ImeCoreConsts.IPTCORE_DICT_SENS;
        iptFiles[ImeCoreConsts.IPTDICT_INDEX_HW_GESTURE] = ImeCoreConsts.IPTCORE_DICT_HW_GESTURE;

        AbsBuiltinIptFilesProvider builtinFilesProvider = BuiltinIptFilesComponent.of().getBuiltinFilesProvider();
        defBuiltinIptFiles = builtinFilesProvider.getAllBuiltinIptFiles().toArray(new String[]{});
    }

    /**
     * 内核dutyInfo的回调处理对象
     */
    private IptPrivateDutyCallback mCallback = null;

    /**
     * Android平台的线程管理实现
     */
    private IptCoreEnv mCoreEnv;

    /**
     * Android平台的网络实现
     */
    private IptCoreNetMan mCoreNetMan;

    /**
     * 内核是否打开
     */
    private volatile boolean mIsCoreOpened = false;

    /** 单例 */
    private static volatile IptCoreInterface sInstance;

    /**
     * 获取ImeCoremanager的实例。必须保证create被调用并且没有destroy
     * @return ImeCoreManager实例
     */
    public static IptCoreInterface get() {
        if (sInstance == null) {
            synchronized (IptCoreInterface.class) {
                if (sInstance == null) {
                    sInstance = new IptCoreInterface();
                }
            }
        }
        return sInstance;
    }

    synchronized boolean openCore(Context context, String dictDir, PackageInfo packageInfo, int flavor) {
        mCoreEnv = new IptCoreEnv();
        mCoreNetMan = new IptCoreNetMan();
        boolean success = open(context, mCoreEnv, mCoreNetMan, dictDir, packageInfo, flavor) >= 0;
        mIsCoreOpened = true;
        return success;
    }

    synchronized boolean closeCore() {
        mIsCoreOpened = false;
        mCallback = null;
        return close() >= 0;
    }

    void setCallbacks(ImePlatformEnv imePlatformEnv, IptPrivateDutyCallback callback) {
        mCallback = callback;
        setImeServiceCallback(imePlatformEnv);
    }

    public boolean isCoreOpened() {
        return mIsCoreOpened;
    }

    // /////////////////////////////////////////////////////////////
    synchronized native int open(Context context, IptCoreEnv ardEnv, IptCoreNetMan netMan,
                                 String dictDir, PackageInfo packageInfo, int flavor);

    synchronized native int close();

    synchronized native int getCoreVersion();
    synchronized native String getCoreVersionStr();

    synchronized native int getEnSysDictVersion();

    synchronized native int getCz3DictGramVersion();
    synchronized native int getCz3DictSysVersion();
    synchronized native int getCz3DictCateVersion();
    synchronized native int getCz5DownStatus();
    synchronized native int getSlideDictVersion();
    synchronized native boolean isNnrankerInstalled();
    synchronized native int[] getUnloadedCzVersion(String dictDir);

    /**
     * 导出内核记录的轨迹，返回值为导出的文件名
     */
    public synchronized native String getTraceLog();

    /**
     * 端上处理完了内核轨迹文件，通知内核删除内核轨迹文件
     */
    synchronized native void resetTraceLog();

    /**
     * 备份轨迹文件供上层使用。
     * @param filePath — 备份文件到哪里。绝对路径。
     * @return 返回值=0表示执行成功; 返回值<0表示执行失败
     */
    synchronized native int backupTraceLog(String filePath);

    synchronized native void sendPadEvent(int padEvent, boolean restart);
    synchronized native int padSwitch(int padId, IptCoreDutyInfo dutyInfo);

    synchronized native int actCnEnKey(int padId, IptCoreDutyInfo dutyInfo);
    synchronized native int actRareChaiZiHwKey(int padId, IptCoreDutyInfo dutyInfo);
    synchronized native void setDefaultPad(int padId);

    // //////////////////////////////////////////////////////////////
    synchronized native int actKeyClick(int keyId, int inputType, String uni, IptCoreDutyInfo dutyInfo);

    synchronized native int actKeyClickXY(int keyId, int x, int y, int inputType, String uni,
                                        IptCoreDutyInfo dutyInfo);

    synchronized native void actAIPadClick(int type, short[] clickList);

    synchronized native void actCustomInputAction(int type, String userData);

    synchronized native void actCandAction(int candType, int candIndex);

    synchronized native int actKeyClickTouchInfo(int keyId, int x, int y, byte power,
                                                 byte area, int inputType, String uni,
                                                 IptCoreDutyInfo dutyInfo);

    synchronized native int actCandClick(int candIdx, IptCoreDutyInfo dutyInfo);

    synchronized native int actRareCandClick(int candIdx, IptCoreDutyInfo dutyInfo);

    synchronized native int actCandSelect(int candIdx, IptCoreDutyInfo dutyInfo);

    synchronized native int actCandLongPress(int candIdx, IptCoreDutyInfo dutyInfo);

    synchronized native int actSugClick(int sugIdx, IptCoreDutyInfo dutyInfo);

    synchronized native int actSugCardClick(int sugcardIdx, boolean isInsert, IptCoreDutyInfo dutyInfo);

    synchronized native int actSugCardSelect(int sugcardIdx, IptCoreDutyInfo dutyInfo);

    synchronized native int actListClick(int listIdx, IptCoreDutyInfo dutyInfo);

    synchronized native int actTabClick(int qpFilter, IptCoreDutyInfo dutyInfo);

    synchronized native byte[] getTabs();

    /**
     * 弹出candinfo信息栏选择
     * @param index candinfo的索引
     * @return -1表示失败，0表示成功
     */
    synchronized native int actCandInfoClick(int index, IptCoreDutyInfo dutyInfo);

    /**
     * 取消candinfo信息栏
     * @return -1表示失败，0表示成功
     */
    synchronized native int actCandInfoCancel(IptCoreDutyInfo dutyInfo);

    /**
     * 插入选择的联系人信息
     * @return -1表示失败，0表示成功
     */
    synchronized native int actContactInsert(IptCoreDutyInfo dutyInfo);

    /**
     * 关闭联系人信息窗口
     * @return -1表示失败，0表示成功
     */
    synchronized native int actContactCancel(IptCoreDutyInfo dutyInfo);

    /**
     * 设置指定的ContactItem为选中/非选中状态
     * @param contactIdx 昵称所对应的具体联系人索引
     * @param itemIdx 具体联系人下的contactItem索引
     * @param isSelect 是否选中
     * @return 成功返回0，失败返回-1
     */
    synchronized native int actContactInfoSelect(int contactIdx, int itemIdx, boolean isSelect);

    /**
     * 开始手写
     * @param point 坐标点数组。(x,y)为一个坐标点
     * @param isSymHw 是否是虚框手写
     * @param dutyInfo 待返回的任务信息
     * @return 成功返回0，<0表示失败
     */
    synchronized native int actTrackStart(short[] point, boolean isSymHw, IptCoreDutyInfo dutyInfo, long time);

    /**
     * 开始手写
     * @param pointX 起始点的x坐标
     * @param pointY 起始点的y坐标
     * @param isSymHw 是否是虚框手写
     * @param dutyInfo 任务信息
     * @return 成功返回0，<0表示失败
     */
    synchronized native int actTrackStartXY(short pointX, short pointY, boolean isSymHw,
                                            IptCoreDutyInfo dutyInfo, long time);

    /**
     * 手写点移动
     * @param point 坐标点数组。(x,y)为一个坐标点
     * @param dutyInfo 待返回的任务信息
     * @return 成功返回0，<0表示失败
     */
    synchronized native int actTrackMove(short[] point, IptCoreDutyInfo dutyInfo, long time);

    /**
     * 手写点移动
     * @param pointX 当前点的x坐标
     * @param pointY 当前点的y坐标
     * @param dutyInfo 待返回的任务信息
     * @return 成功返回0，<0表示失败
     */
    synchronized native int actTrackMoveXY(short pointX, short pointY, IptCoreDutyInfo dutyInfo, long time);

    /**
     * 结束一条手写轨迹
     * @param pointX 结束点的x坐标
     * @param pointY 结束点的y坐标
     * @param dutyInfo 待返回的任务信息
     * @return 成功返回0，<0表示失败
     */
    synchronized native int actTrackEnd(short pointX, short pointY, IptCoreDutyInfo dutyInfo,
                                        long time, boolean isGesture);

    /**
     * 设置input光标位置
     * @param cursorIdx 光标位置
     * @param dutyInfo 待返回的任务信息
     * @return 成功返回0，<0表示失败
     */
    synchronized native int actInputCursor(int cursorIdx, IptCoreDutyInfo dutyInfo);

    /**
     * 发送input光标左移事件
     */
    synchronized native void actInputCursorLeft();

    /**
     * 发送input光标右移事件
     */
    synchronized native void actInputCursorRight();

    synchronized native int actInputPop(IptCoreDutyInfo dutyInfo);

    synchronized native int actEditCursorChange(IptCoreDutyInfo dutyInfo);

    /**
     * 导入初始List列表内容（作为默认的符号候选）
     * @param listItems 初始的列表内容，String类型
     * @return 成功返回0，<0表示失败
     */
    synchronized native int importSymList(String[] listItems, String[] listNames,
                                          IptCoreDutyInfo dutyInfo);

    /**
     * 设置符号面板的lock状态
     * @param symCateList 需要设置的符号项的列表
     * @param symLockList 和符号项对应的lock状态
     */
    synchronized native void padSetLock(int[] symCateList, int[] symLockList);

    /**
     * 获取符号面板的lock状态
     * @param symCateList 待获取的符号项的列表
     * @param symLockList 和符号项对应的lock状态
     */
    synchronized native void padGetLock(int[] symCateList, int[] symLockList);

    /**
     * 符号面板导入符号，更多符号面板重新排序
     * @param symList 导入符号
     * @param length 导入符号数量
     */
    synchronized native void padSetSymFilter(char[][] symList, int length);

    /**
     * Shift长按按下
     */
    synchronized native int actShiftLongDown();

    /**
     * Shift长按抬起
     */
    synchronized native int actShiftLongUp();

    /**
     * 使用内核优化语音识别结果
     * @param orgResult 原始语音识别结果
     * @return 内核的优化识别结果，如果没有优化，返回null
     */
    synchronized native String actCorrectVoiceData(String orgResult);

    /**
     * 客户端接受语音识别结果
     */
    synchronized native int actCorrectVoiceSend();

    /**
     * 设置剪切板更新的内容
     *
     * @param uniArray 最近的剪切板内容
     * @param uniLen   最近的剪切板内容长度
     * @return 是否需要保存剪切板的内容，返回值0不需要，1需要
     */
    synchronized native int actCheckClip(char[] uniArray, int uniLen);

    synchronized native int actAdjustEmojiRelation(int emojiValue, int cellId);

    // ////////////////////////////////////////////////////////////
    synchronized native int getInputShow(IptCoreShowInfo showInfo, byte[] cursorInfo, byte[] autofixInfo);

    synchronized native int getCandCount();

    @CoreThread
    public synchronized native int getListCount();

    synchronized native int getCandItem(int candIdx, IptCoreCandInfo candInfo);
    synchronized native int getAiCandItem(int candIdx, IptCoreCandInfo candInfo);
    synchronized native int getAiCandItemCount();

    synchronized native int getRareCandCount();

    synchronized native int getRareCandItem(int candIdx, IptCoreCandInfo candInfo);


    synchronized native int getSugCount();

    /**
     * sug广告超时时间
     */
    synchronized native int getSugAdTimeoutMs();

    synchronized native int getSugItem(int sugIdx, IptCoreCandInfo candInfo);

    synchronized native int getSugSelect();

    synchronized native int getSugCardCount();

    synchronized native int getSugCardItem(int sugCardIdx, IptCoreSugCardInfo sugCardInfo);

    synchronized native int getSugCardSelect();

    synchronized native int getCandInfoCount();

    synchronized native String getCandInfo(int idx);

    synchronized native int getContactCount();

    synchronized native int getContactItem(int index, IptContactItem contactItem);

    synchronized native int getTriggerWordCount();

    synchronized native int getTriggerWordItem(int index, IptTriggerWordItem item);

    synchronized native int getMapTriggerWordCount();

    synchronized native int getMapTriggerWordItem(int index, IptMapTriggerWordItem item);

    synchronized native long getTriggerWordItemsId();

    synchronized native void sendLocationEvent(float latitude, float longitude);

    synchronized native int getPadId();

    synchronized native boolean getAIPadLoadingState();

    synchronized native int getAIPadTab();
    synchronized native int getAIPadState();
    synchronized native int getAIPadCnt();
    synchronized native int getAIPadItem(int index, IptCoreCandInfo candInfo);
    synchronized native String getAIPadOriginText();
    synchronized native boolean getAIPabIsAutoOpen();
    synchronized native int getAICand(IptCoreCandInfo candInfo);
    synchronized native int getAICandIconState();
    synchronized native int getAICorrectInlineInfo(IptCoreCandInfo candInfo);
    synchronized native boolean checkHitBlackList(String content);
    synchronized native byte[] encryptionAICardRequestData(int checkId, byte[] inContent);
    synchronized native byte[] decryptAICardResponseData(int checkId, byte[] inContent);

    @CoreThread
    public synchronized native int getListItem(int idx, IptCoreListInfo info);

    // /////////////////////////////////////////////////////////////
    synchronized native int getHwSmartDelayTime();

    synchronized native int stateGetTrackType();

    synchronized native boolean stateGetIsAcceptTrack();

    synchronized native String getCandContext(int idx);

    synchronized native char[] getEgg();
    synchronized native int getSrvCloudWhiteVer();

    synchronized native int getSugType();
    synchronized native int getSugActionType();

    /**
     * 获取某个sug的actionType
     */
    synchronized native int getSugActionTypeByIndex(int index);

    /**
     * 获取某个sug广告的globalid
     */
    synchronized native String getSugAdGlobalId(int index);

    /**
     * sug 的id
     */
    synchronized native int getSugId();

    synchronized native int getSugSourceId();
    synchronized native String getSugSourceMsg();

    /**
     * 为智能云词添加黑名单
     * @param content 黑名单字符串
     */
    synchronized native int blockCloudUnis(String content);

    /**
     * 清空智能云词添加黑名单
     */
    synchronized native int resetCloudBlack();

    synchronized native void setPadLayout(int[] rect, boolean isSplit);
    synchronized native void setPadKeyPos(byte keyId, int[] viewRect, int[] touchRect);

    @MainThread
    synchronized native int getTouchedKey(int x, int y);

    synchronized native int getSugState();

    /**
     * 获得拼音面板当前输入情况下各个字母的等级
     *
     * @param alphas 字母的hot等级
     * @return 成功返回0，否则返回-1
     */
    @Deprecated
    synchronized native int getPyHotLetter(byte[] alphas);

    synchronized native void actCurSugClose();

    /**
     * layout设置开始接口
     */
    synchronized native void userTraceStartPadKeyLayout();

    /**
     * layout设置完成接口
     */
    synchronized native void userTraceFinishPadKeyLayout();

    /**
     * 传入user trace
     * @param action    : 行为
     * @param container : 容器
     * @param content   : 用户轨迹数据
     */
    synchronized native void userTraceWrite(int action, int container, byte[] content);

    /**
     * 语音上屏日志记录
     *
     * @param content : 字符串
     */
    synchronized native void userVoiceInputLog(String content);

    // /////////////////////////////////////////////////////////////
    synchronized native int setImeServiceCallback(ImePlatformEnv imeService);

    /**
     * 执行内核的callback
     * @param tag 内核给出的callback的tag
     * @param arg0 内核给出的callback地址
     * @return
     */
    synchronized native int execCallback(int tag, long arg0);

    /**
     * 网络连接数据返回后通知内核的回调
     * @param stream 产生数据的stream
     * @param callbackPtr 内核给出的callback指针
     * @param errorCode 错误码
     * @param responseBody 返回数据
     */
    synchronized native int onNetReceive(IptCloudStream stream, long callbackPtr, int errorCode,
                                       byte[] responseBody);

    // ///////////////////////// 设置区域 //////////////////////////

    /**
     * 设置一个Int类型的设置项给内核
     * @param key 设置项的key
     * @param value 设置项的值
     */
    synchronized native void setInt(int key, int value);

    /**
     * 获取一个Int类型的设置项
     * @param key 设置项的key
     * @return 设置项的值
     */
    synchronized native int getInt(int key);

    /**
     * 设置一个Boolean类型的设置项给内核
     * @param key 设置项的key
     * @param value 设置项的值
     */
    synchronized native void setBoolean(int key, boolean value);

    /**
     * 获取一个Boolean类型的设置项
     * @param key 设置项的key
     * @return 设置项的值
     */
    synchronized native boolean getBoolean(int key);

    /**
     * 设置一个String类型的设置项给内核
     * @param key 设置项的key
     * @param value 设置项的值
     */
    synchronized native void setString(int key, String value);

    /**
     * 获取一个String类型的设置项
     * @param key 设置项的key
     * @return 设置项的值
     */
    synchronized native String getString(int key);

    /**
     * 获取一个Long类型的设置项
     * @param key 设置项的key
     * @return 设置项的值
     */
    synchronized native void setLong(int key, long value);

    /**
     * 安装一个词库
     * @param key 词库的key
     * @param path 词库的路径
     */
    synchronized native int installCell(int key, String path);

    /**
     * 卸载一个词库
     * @param key 词库的key
     * @param id 词库的id，对一些词库该值无意义
     */
    synchronized native int uninstallCell(int key, int id);

    /**
     * Ai助聊json配置设定接口。内核会记忆Ai助聊配置最后一次设定信息
     * @param jsonStr 配置的json字符串(来自通知中心)
     * @param noticeKey 配置通知中心key
     */
    synchronized native void setAiWordsJson(String jsonStr, String noticeKey);

    /**
     * 设置轨迹记录的阈值，达到指定的阈值大小后，给客户端一个标志，客户端进行文件上传
     * @param base 首次进行阈值提示的大小（Byte）
     * @param increment 阈值提示的大小增量（Byte）
     */
    synchronized native void setTraceWarningSize(int base, int increment);

    synchronized native void keymapClean();
    synchronized native void keymapAddChar(int id, char ch, int level);

    synchronized native void setCloudAddress(String[] hosts, int[] ports, boolean cloudUseUdp, boolean sugUseUdp);


    // /////////////////// 需要session的设置项 ////////////////////////////////////////
    synchronized native int getPhraseGroupCount();
    synchronized native int getPhraseGroup(int idx, IptPhraseGroup phraseGroup);
    synchronized native int addPhraseGroup(String groupName);
    synchronized native int deletePhraseGroup(int idx);
    synchronized native int editPhraseGroup(int idx, String groupName);

    synchronized native int enablePhraseGroup(int idx, boolean isEnable);
    synchronized native int getPhraseItemCount(int groupId, String code);
    synchronized native int getPhraseItem(int idx, IptPhraseItem phraseItem);
    synchronized native int addPhraseItem(String code, String word, int pos, int groupId);
    synchronized native int editPhraseItem(int idx, String code, String word, int pos);
    synchronized native int deletePhraseItem(int idx);
    synchronized native int importPhrase(String fileName, boolean overwrite);
    synchronized native int exportPhrase(String fileName, int groupId);

    synchronized native int reduceUsWord(int percent);
    synchronized native int getUsWordSize();
    synchronized native int importUsWord(String path);
    synchronized native int exportUsWord(String path);
    synchronized native int importUeWord(String path);
    synchronized native int exportUeWord(String path);

    /**
     * 导出生僻字用户词库的明文
     * @param path 路径
     */
    synchronized native int exportRareUserWord(String path);

    /**
     * Int32 cfg_get_hw_rare_version(); //获取生僻字手写版本号
     */
    synchronized native int getHwRareVersion();

    synchronized native int getCellCount();
    synchronized native int getCellInfoByIndex(int idx, IptCellInfo cellInfo);
    synchronized native int getCellInfoByCellId(int cellId, IptCellInfo cellInfo);
    synchronized native int setCellLocType(int cellId, int locType);
    synchronized native int setCellInstallTime(int cellId, int installTime);
    synchronized native int getPopWordCellId();
    synchronized native int getSysWordCellId();
//    synchronized native int getSyswordVer();
    synchronized native int getCloudWhiteVer();
    synchronized native int enableCell(int cellId, boolean isEnable);
    synchronized native int getRecentCount(int days);

    synchronized native int getKwdCellCount();
    synchronized native int getKwdCellInfoByIndex(int index, IptKwdCellInfo kwdInfo);
    synchronized native int getKwdCellInfoByCellId(int cellId, IptKwdCellInfo kwdInfo);
    synchronized native int exportKwd(String filePath);
    synchronized native int getIdmCellCount();
    synchronized native int getIdmCellInfoByIndex(int index, IptIdmCellInfo idmInfo);

    /**
     * 手写数据压缩
     *
     * @param candId 候选字idx
     * @return 手写压缩数据
     */
    synchronized native byte[] hwEncodePoint(int candId);

    /**
     * 手写查询拼音
     * @param unicode — 要注音的字对应的unicode编码
     * @param isall   — 是否给所有字注音(0-只标注生僻字， 1-标注所有字)
     * @return 若查询成功, 返回unicode编码对应汉字的注音(结构为: "yin,pin"); 若查询失败, 返回NULL
     */
    synchronized native String getHwPinyin(char unicode, int isall);

    /**
     * 清空所有联系人
     * @return 清空成功返回0; 失败返回-1
     */
    @Deprecated
    synchronized native int contactReset();

    /**
     * 添加属性(必须先定义属性)
     * @param attr 联系人的属性名
     * @return 若添加成功, 返回分配到的属性id号; 若添加失败, 返回-1
     */
    @Deprecated
    synchronized native int contactAppendAttr(String attr);

    /**
     * 添加联系人信息
     * @param name — 联系人姓名
     * @param attr — 联系人的属性名
     * @param value — 联系人的属性值
     * @return 若添加成功, 返回0; 若添加失败, 返回-1
     */
    @Deprecated
    synchronized native int contactAppendValue(String name, String attr, String value);

    synchronized native int contactBuildStart();
    synchronized native int contactBuildAddName(String name);
    synchronized native int contactBuildAddValue(String attr, String value);
    synchronized native int contactBuildEnd(boolean isRetainBlack);
    synchronized native int contactBuildAddBlackName(String blackName);

    /**
     * 删除对应联系人
     * @param name — 联系人姓名
     * @param option — 删除类型    CMD_CONTACT_DEL  CMD_CONTACT_RESTORE_FREQ  CMD_CONTACT_DEL_ALL
     * @return 若删除成功, 返回0; 若删除失败, 返回-1;未找到要删除的项目，返回-2
     */
    @Deprecated
    synchronized native int contactDelete(String name, int option);

    synchronized native String contactVoiceFind(String oriWord);
    synchronized native String contactVoiceFindAddressbook(String oriWord);
    @Deprecated
    synchronized native int oldCpExport(String inputFile, String outputFile);
    @Deprecated
    synchronized native int oldUeExport(String inputFile, String outputFile);
    synchronized native int importZyword(String fileName);
    synchronized native int exportZyword(String fileName);
    synchronized native int symImport(String fileName, boolean isOverwrite);
    synchronized native int symExport(String fileName);
    synchronized native int userSymImport(String[] symData);
    synchronized native int otherwordImport(String fileName, boolean isOverwrite);

    synchronized native int sylianImport(String fileName, boolean isOverwrite);
    synchronized native int vkwordImport(String fileName, boolean isOverwrite);
    synchronized native int vkwordExport(String fileName);
    synchronized native int usrinfoImport(String fileName);
    synchronized native int usrinfoExport(String fileName);
    synchronized native int getZhuyinHzVersion();
    synchronized native int getZhuyinCzVersion();
    synchronized native int getZhuyinCzInfo();
    synchronized native int getCangjieVersion();
    synchronized native int getHwVersion();
    @Deprecated
    synchronized native int searchGetVersion();
    @Deprecated
    synchronized native int[] searchFind(String queryWord);
    synchronized native int adjustSylianRelation(String preStr, int preLen, String tailStr, int tailLen);
    @Deprecated
    synchronized native char[] featureInfExtract(String originStr, int originLen, int infType);
    synchronized native int candContextExport(String fileName);
    synchronized native String getFtByUni(String unicode);
    @Deprecated
    synchronized native int importOldUsFile(String oldCellFile, String oldUzFile);
    synchronized native int kwdGetSearchVersion();
    synchronized native int getAutoReplyVer();

    synchronized native int importAppMap(String filePath);
    synchronized native int appMapGetVersion();

    /**
     * 获取误触数据，获取失败返回空
     */
    @CoreThread
    public synchronized native String exportMisTouchInfo();

    /**
     * 获取落点数，与上面的误触导出接口配套使用，获取失败返回空
     */
    @CoreThread
    public synchronized native String getKeyCountForMisTouch();

    /**
     * 获取联想数据
     */
    @CoreThread
    public synchronized native void exportInputAssociateInfo(String[] associateData);

    /**
     * 导出这次commit的word_info用于trace
     */
    @CoreThread
    public synchronized native String exportWordInfoForTrace();

    synchronized native int czDownRefresh(String fileName);

    /**
     * 刷新接口已废弃fileName，把词库文件放置统一目录下刷新，目录在openCore传入内核
     * @param type
     * @return
     */
    synchronized native int coreRefresh(int type);

    /**
     * 内核卸载词库
     * @param type
     * @return
     */
    synchronized native int coreUnload(int type);

    synchronized native int nnrankerRefresh(String configFilePath, String wordFilePath,
                                            String modelFilePath);

    @CoreThread
    public synchronized native void getNnrankerInfo(int[] intArgs);

    synchronized native int autoReplyRefresh(String fileName);
    @Deprecated
    synchronized native void createUswordManager(String searchWord, int type);
    synchronized native void destroyUswordManager();
    synchronized native int uswordGetCount();
    synchronized native String uswordGetStr(int idx);
    synchronized native int uswordGetAction(int idx);
    synchronized native void uswordDoAction(int idx);
    synchronized native int uswordGetCnWordCount();
    synchronized native int keywordFindVoiceLian(String content, int[] emojiList,
                                                 String[] emoticonList);
    synchronized native char[] keywordFindVoiceEgg(String content);
    synchronized native int importAllUserWord(String path);
    synchronized native int exportAllUserWord(String path);
    synchronized native int importOldDefWord(String filePath);


    /**
     * 获取双拼声母映射
     *
     * @param keyChar 按键字符
     * @param shengList [in/out] 可分配一个64字节的buffer
     * @return 返回shengList的实际个数
     */
    synchronized native int getSpMapSheng(byte keyChar, byte[] shengList);
    /**
     * 获取双拼韵母映射
     *
     * @param keyChar 按键字符
     * @param yunList [in/out] 可分配一个128个字节的buffer
     * @return 返回yunList的实际个数
     */
    synchronized native int getSpMapYun(byte keyChar, byte[] yunList);

    /**
     * 获得保护的key。目前用于小游戏的scretkey的提供，复用内核的签名校验方法将scretKey保护起来
     */
    native String getProtCode();

    /**
     * check 文件的MD5值
     *
     * @param name   文件的路径
     * @param digest MD5值 ， 32个字节
     */
    native void checkFileMD5(String name, byte[] digest);

    void privateDutyInfoCallbackCore(IptCoreDutyInfo dutyInfo) {
        if (mCallback != null) {
            mCallback.onPrivateDutyInfo(dutyInfo);
        }
    }

    void privateAssociationInfoCallbackCore(String response, int ecode) {
        if (mCallback != null) {
            if (Config.enableLog()) {
                MethodTracer.startCoreMethod();
            }
            mCallback.onPrivateAssociationInfo(response, ecode);
            if (Config.enableLog()) {
                MethodTracer.recordCoreMethod("onHonorNotepadGetResponse#response:" + response + ",code:" + ecode);
                MethodTracer.stopCoreMethod();
            }
        }
    }

    /**
     * 清理所有可写词库：细胞词、(中/英)自造词、联系人词、注音用户自造词
     * @return 恢复成功返回0，失败返回-1
     */
    native int writableDictReset();
    /**
     * 设置shift状态
     * */
    native int actChangeShift(int shiftId, IptCoreDutyInfo dutyInfo);

    /**
     * ai font生成，通知ai助聊显示生成通知
     * @param type  {@link com.baidu.iptcore.ImeCoreConsts.AiCandNotifyType}
     * @param showContent   ai助聊显示内容
     * @param scheme    跳转scheme
     * @param displayTime 通知展示时长，单位 ms，负数表示一直展示直到被其他状态顶掉
     * @param insertContent 点击后上屏的内容（主：展示内容和点击上屏的内容可能不同）
     */
    synchronized native void actAiFontGeneratedNotify(int type, String showContent, String scheme
            , String insertContent, long displayTime);

    /**
     * 根据手写轨迹获取匹配字符
     * @param iptAiHwRes {@link IptAiHwRes}
     * @param maxNum 允许返回的最大字符数
     * @param points 手写轨迹
     */
    native int aiFontRecoPoint(IptAiHwRes iptAiHwRes, int maxNum, int[] points);

    /**
     * 加载ai font手写模型
     * @param path path
     */
    synchronized native boolean loadAiFontHwModel(Context context, String path, String licensePath);

    /**
     * 卸载ai font手写模型
     */
    synchronized native void unLoadAiFontHwModel();

    /**
     * 获取ai font手写模型版本号
     * @return version
     */
    native int aiFontHwModelVersion();

    /**
     * 点击工具栏
     */
    synchronized native void actToolbarClick();

    /**
     * 获取文心的so的主版本号
     */
    synchronized native int getMlmSoVersion();

    /**
     * 获取文心的词库的版本号
     */
    synchronized native int getMlmDictVersion(String path);

    /**
     *
     * @param type 类型
     * @param patchPath 补丁路径
     * @param targetMd5 应用补丁生成的文件的 md5，固定长度 32
     * @param originalFilePath 原始文件路径，对于内核已知的可写目录的词典文件，originalFilePath 传空
     * @return
     */
    synchronized native int applyPatch(int type, String patchPath, String targetMd5
            , String originalFilePath);

    /**
     * 加解密库的版本
     */
    synchronized native int getEncryptVersion();

    /**
     * 加解密库中Base64的版本
     */
    synchronized native int getEncryptB64Version();

    /**
     * B64加密
     */
    synchronized native byte[] b64Encode(byte[] input);

    /**
     * B64解密
     */
    synchronized native byte[] b64Decode(byte[] input);

    /**
     * AES加密
     */
    synchronized native byte[] aesEncrypt(byte[] input);

    /**
     * AES V2加密（客户端不支持V2的解密）
     */
    synchronized native byte[] aesEncryptV2(byte[] input);

    /**
     * AES解密。只能解密第一种方式
     */
    synchronized native byte[] aesDecrypt(byte[] input);

    /**
     * AES+B64加密
     */
    synchronized native byte[] aesB64Encrypt(byte[] input);

    /**
     * AES+B64 V2加密（客户端不支持V2的解密）
     */
    synchronized native byte[] aesB64EncryptV2(byte[] input);

    /**
     * AES+B64解密。只能解密第一种方式
     */
    synchronized native byte[] aesB64Decrypt(byte[] input);

    /**
     * rsa公钥加密
     */
    synchronized native byte[] rsaEncrypt(byte[] input);

    /**
     * rsa公钥解密
     */
    synchronized native byte[] rsaDecrypt(byte[] input);


    /**
     * 设置符号26键是否需要自动返回普通26键
     */
    synchronized native void setPadSymExtAutoReturn(boolean enable);

    /**
     * 设置用户当前所在场景组
     * @param groupIDs
     */
    synchronized native void setSceneGroupIDs(long[] groupIDs);

    /**
     * 获取符号26键自动返回状态
     */
    synchronized native boolean getPadSymExtAutoReturn();

    /**
     * 设置sug是否开启调试
     */
    synchronized native void setDisplayCandType(boolean enable);

    /**
     * 获取sug是否开启调试
     * @return
     */
    synchronized native boolean getDisplayCandType();

    /**
     * 获取内核通讯录的名单，PB格式
     */
    synchronized native byte[] getContactNamesInPb();

    /**
     * 获取inline输入码
     */
    synchronized native String getInlineShow();

    /**
     * 旧版中文自造词文件升级覆盖新版文件
     */
    synchronized native void iptDictRebuildFromOldUsr2(String outputDir, String immDir,
                                                       String usr2Path, String vkwordPath);

    /**
     * 旧版英文自造词文件升级覆盖新版文件
     */
    synchronized native void iptDictRebuildFromOldUe2(String outputDir, String immDir, String ue2Path,
                                                      String learnDic1Path, String learnDic2Path);
    /**
     * 旧版keyword文件升级覆盖新版文件
     */
    synchronized native void iptDictRebuildFromOldKeyword(String outputDir, String path);

    /**
     * 旧版注音自造词文件升级覆盖新版文件
     */
    synchronized native void iptDictRebuildFromOldZyUsr(String outputDir, String immDir,
                                                        String downDir, String path);
    /**
     * 旧版注音系统文件转换成新版文件
     */
    synchronized native void iptDictRebuildFromOldZy(String outputDir, String immDir,
                                                        String hzZyPath, String czZyPath);

    /**
     * 旧版仓颉文件升级覆盖新版文件
     */
    synchronized native void iptDictRebuildFromOldCangjie(String outputDir,
                                                          String path, boolean isQuick);

    /**
     * 获取某段文字的意图类型
     * 注：需要异步调用
     */
    @WorkerThread
    synchronized native int getIntentStyle(String content);


    /**
     * 网盟sug广告事件通知内核
     * @param sugActionType 点击之后跳转类型
     */
    synchronized native void sendWmSugAdAction(int index, int sugActionType);

    /**
     * 意图卡片事件通知内核
     * @param firstLevel 一级意图类型
     * @param secondLevel 二级意图类型
     * @param title 卡片标题
     * @param action 事件类型
     */
    synchronized native void sendCloudIntentionCardAction(int firstLevel, int secondLevel, String title, int action);

    /**
     * 加载sug广告（内核穿山甲广告）
     */
    synchronized native void loadCoreSugAd(int pos);

    /**
     * 获取sug广告个数（内核穿山甲广告）
     */
    synchronized native int getCoreSugAdCount(int pos);

    synchronized native int getCoreSugAdAt(int index, IptCoreSugAdInfo adInfo, int pos);

    /**
     * 告诉内核sug广告获取超时了
     */
    synchronized native int coreSugAdTimeout(int time, int pos);
    synchronized native int coreSugAdWin(int index, int pos);
    synchronized native int coreSugAdFail(int index, int reason, long price, int pos);
    synchronized native int coreSugAdShow(int index, int[] rect, int pos);
    synchronized native int coreSugAdClick(int index, IptCoreSugAdAction adAction, int pos, int[] rect, int[] clickPoints);
    synchronized native int coreSugAdDownloadStart(int index, int pos);
    synchronized native int coreSugAdDownloadFinish(int index, int pos);
    synchronized native int coreSugAdInstallFinish(int index, int pos);
    synchronized native int coreSugAdOpenUrlApp(int index, int pos);
    synchronized native int coreSugAdOpenFallbackUrl(int index, int pos);
    synchronized native int coreSugAdDplSuccess(int index, int pos);
    synchronized native int coreSugAdDplFailed(int index, int pos);



    /**
     * 云端意图开关 - 是否请求联系人/天气/日历/汇率
     */
    synchronized native void setCloudIntention(int type, boolean isOn);

    /**
     * 云端意图 - 获取云端意图开关状态。
     */
    synchronized native int getCloudIntention();


    /**
     * 云端意图 - 联系人意图：
     *
     * @param isOn true 读取联系人词典的内容判断意图。false，借用人名模式判断是否有联系人意图引导用户开启权限
     */
    synchronized native void setContactPermissionStatus(boolean isOn);

    /**
     * 获取云端意图卡片的数量
     */
    synchronized native int getCloudIntentionCount();

    /**
     * 获取云端意图卡片的数据
     */
    synchronized native int getCloudIntentionItem(int index, IptIntentItem item);

    /**
     * 训练高斯个性化模型
     *  端上最好保持为false,false时具体策略内核会自己确定
     * @param forcible — 是否强制训练
     * @return 是否进行了训练
     */
    synchronized native boolean trainGaussUser(boolean forcible);

    /**
     * 获取在联想词上的超会写icon是否需要展示
     * @return
     */
    synchronized native boolean getAiIconInLXNeedShow();

    /**
     * 该函数没有说明
     */
    synchronized native int dctCellInstallByBuffer(String value);
    /**
     * 该函数没有说明
     */
    synchronized native int dctCellWordInDict(String value);
    /**
     * 该函数没有说明
     */
    synchronized native String dctCellExportBuffer(int cellId);
    /**
     * 该函数没有说明
     */
    synchronized native int dctCellResetBuffer(int cellId);
    /**
     * 该函数没有说明
     */
    synchronized native void setUplDataAddress(String address, int port);
    /**
     * sug白名单数据是否匹配
     */
    synchronized native boolean isMatchSugWhiteData();


    /**
     * 荣耀笔记发起云请求
     * @param input 表示输入string的内容
     * @param findMode 表示查询方式。0：无实际含义,占位；1：拼音联想结果；2：纠错功能；3：拼音+纠错
     * @param requestId 表示当前请求string的唯一标识
     * @param limitCount 表示对结果数量的限制
     * @return 返回值表示是否请求已经成功发起
     */
    synchronized native int doHonorIntelligentRequest(String input, int findMode, long requestId, int limitCount);

    /**
     * dctSetNameByBuff
     *
     * @param app app名称
     * @param ts  时间戳
     * @return 返回值表示是否请求已经成功发起
     */
    synchronized native int dctSetNameByBuff(String app, long ts, boolean isNew);

    /**
     * 设置是否需要开启内核线上log，默认不开启
     * @param level
     *      0: 不开启（默认：CORE_ONLINE_LOG_NONE）；
     *      1：开启所有日志 （CORE_ONLINE_LOG_ALL）；
     *      2：内核关键逻辑-耗时（CORE_ONLINE_LOG_TIMECOST）
     * @return
     */
    synchronized native void setOnlineLogLevel(@IntRange(from = 0, to = 2) int level);

    /**
     * 智能表单：导入个人化信息 逐条导入-开始标记，内核开启文件写指针, 并关闭查词功能
     */
    synchronized native int startPersonInfoAddLine();

    /**
     * 智能表单：导入个人化信息 逐条导入 导入结束调用 cfg_usrinfo_import_finished
     * @param index 用户信息展示联想的最短索引长度，当索引长度大于 0 时，认为是联想候选，
     * @param line 个人信息字符串（邮箱，手机号等）
     * @return 返回值：<0 导入出错，=0 导入成功 ///
     */
    synchronized native int addPersonInfoFromLine(int index, String line);
    /**
     * 智能表单：导入个人化信息结束标志
     * @return 返回值：<0 结束出错，=0 结束成功
     */
    synchronized native int personInfoAddFinished();
    /**
     *
     * 智能表单：个人信息联想功能关闭，调用该接口，清空用户个人信息数据
     */
    synchronized native int resetPersonInfo();
}