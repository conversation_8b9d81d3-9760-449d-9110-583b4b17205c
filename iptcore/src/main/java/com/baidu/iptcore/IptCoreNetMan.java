package com.baidu.iptcore;

import static com.baidu.iptcore.ImeCoreConsts.LOG_TAG;
import static com.baidu.iptcore.ImeCoreConsts.NET_LOG_PREFIX;

import android.os.Handler;
import android.os.Looper;
import android.support.annotation.Keep;

import com.baidu.iptcore.net.IptCloudStream;
import com.baidu.iptcore.net.IptCloudStreamCallback;
import com.baidu.iptcore.util.Config;
import com.baidu.iptcore.util.Logger;

/**
 * 内核云输入所需的网络实现类
 * 
 * Created by chend<PERSON><PERSON> on 18/9/11.
 */
@Keep
public class IptCoreNetMan {

    /**
     * 主线程Handler
     */
    private final Handler coreThreadHandler;

    public IptCoreNetMan() {
        coreThreadHandler = new Handler(Looper.myLooper());
    }

    /**
     * 创建网络流
     * @param url 需要请求的url
     * @param port 需要请求的端口
     * @param protocol 需要请求的协议
     * @param timeOut 超时时间,单位毫秒
     * @param maxRecvLen 允许返回的最大长度，主要是为了http协议拼接body时，防止数据过大
     * @param callbackPtr 网络回调通知网络数据已经返回的Callback的地址
     * @return 网络流实例
     */
    @Keep
    public IptCloudStream streamCreate(String url, int port, int protocol, int timeOut, int maxRecvLen,
                                       long callbackPtr) {
        IptCloudStream stream = new IptCloudStream(url, port, protocol, timeOut, maxRecvLen, 
                new CloudStreamCallback(callbackPtr));
        if (!Config.isShortLogMode()) {
            Logger.i("iptcore",
                    "streamCreate:: url=%s, port=%d, protocol=%d, timeOut=%d, maxRecvLen=%d, callbackPtr=%d, stream=%s",
                    url, port, protocol, timeOut, maxRecvLen, callbackPtr, stream);
        }
        return stream;
    }

    /**
     * 网络流发起请求。一个网络流会多次发起请求，并且会存在在上一个请求没有返回前，再次发起请求
     * @param stream 网络流对象
     * @param streamType 流的类型
     * @param sendData 发送的消息,HTTP请求时,仅仅为http body,http头需要有Content-Length
     * @param streamLen 发送消息的长度
     * @return >=0 发送成功, < 0表示发送失败
     */
    @Keep
    public int streamSend(IptCloudStream stream, int streamType,
                          byte[] sendData, int streamLen) {
        boolean netPermission = ImeCoreManager.getConfig().getNetPermission();
        if (!Config.isShortLogMode()) {
            Logger.i(LOG_TAG + "_" + NET_LOG_PREFIX,
                    "streamSend:: streamType=%d, streamLen=%d, stream=%s, netPermission=%b",
                    streamType, streamLen, stream, netPermission);
        }

        if (netPermission && stream != null) {
            return stream.send(streamType, sendData, streamLen);
        }
        return -1;
    }

    /**
     * 关闭网络流
     * @param stream 网络流
     **/
    @Keep
    public void streamClose(IptCloudStream stream) {
        if (!Config.isShortLogMode()) {
            Logger.i("iptcore", "streamClose:: stream=%s", stream);
        }
        if (stream != null) {
            stream.close();
        }
    }

    /**
     * 网络流的回调对象
     */
    private class CloudStreamCallback implements IptCloudStreamCallback {

        /**
         * 一次网络请求对应的回调接口在native层的地址
         */
        private final long callbackPtr;

        CloudStreamCallback(long callbackPtr) {
            this.callbackPtr = callbackPtr;
        }

        @Override
        public void onResponse(final IptCloudStream stream, final int errorCode, final byte[] responseBody) {
            coreThreadHandler.post(new Runnable() {
                @Override
                public void run() {
                    if (!Config.isShortLogMode()) {
                        Logger.i(LOG_TAG + "_" + NET_LOG_PREFIX,
                                "stream callback: %s, errorCode = %d"
                                , stream, errorCode);
                    }

                    IptCoreInterface.get().onNetReceive(stream, callbackPtr, errorCode, responseBody);
                }
            });
        }
    }
}
