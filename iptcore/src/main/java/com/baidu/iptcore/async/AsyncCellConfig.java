package com.baidu.iptcore.async;

import com.baidu.annotation.CoreThread;
import com.baidu.iptcore.CoreCellConfig;
import com.baidu.iptcore.info.IptCellInfo;
import com.baidu.iptcore.info.IptIdmCellInfo;
import com.baidu.iptcore.info.IptKwdCellInfo;
import com.baidu.iptcore.info.IptPhraseGroup;
import com.baidu.iptcore.info.IptPhraseItem;

@CoreThread
public class AsyncCellConfig extends CoreCellConfig {

    /**
     * 内核线程引擎
     */
    private final CoreThreadEngine mCoreThreadEngine;

    public AsyncCellConfig(CoreThreadEngine coreThreadEngine) {
        mCoreThreadEngine = coreThreadEngine;
    }

    @Override
    public int installCell(int key, String path) {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.installCell(key, path));
        return result == null ? -1 : result;
    }

    @Override
    public int uninstallCell(int key, int id) {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.uninstallCell(key, id));
        return result == null ? -1 : result;
    }

    @Override
    public int exportAllUserWord(String path) {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.exportAllUserWord(path));
        return result == null ? -1 : result;
    }

    @Override
    public int importAllUserWord(String path) {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.importAllUserWord(path));
        return result == null ? -1 : result;
    }

    @Override
    public int getPhraseGroupCount() {
        Integer result = mCoreThreadEngine.executeFutureTask(super::getPhraseGroupCount);
        return result == null ? 0 : result;
    }

    @Override
    public IptPhraseGroup getPhraseGroup(int idx) {
        return mCoreThreadEngine.executeFutureTask(() -> super.getPhraseGroup(idx));
    }

    @Override
    public int addPhraseGroup(String groupName) {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.addPhraseGroup(groupName));
        return result == null ? -1 : result;
    }

    @Override
    public int deletePhraseGroup(int idx) {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.deletePhraseGroup(idx));
        return result == null ? -1 : result;
    }

    @Override
    public int editPhraseGroup(int idx, String groupName) {
        Integer result =
                mCoreThreadEngine.executeFutureTask(() -> super.editPhraseGroup(idx, groupName));
        return result == null ? -1 : result;
    }

    @Override
    public int enablePhraseGroup(int idx, boolean isEnable) {
        Integer result =
                mCoreThreadEngine.executeFutureTask(() -> super.enablePhraseGroup(idx, isEnable));
        return result == null ? -1 : result;
    }

    @Override
    public int getPhraseItemCount(int groupId, String code) {
        Integer result =
                mCoreThreadEngine.executeFutureTask(() -> super.getPhraseItemCount(groupId, code));
        return result == null ? 0 : result;
    }

    @Override
    public IptPhraseItem getPhraseItem(int idx) {
        return mCoreThreadEngine.executeFutureTask(() -> super.getPhraseItem(idx));
    }

    @Override
    public int addPhraseItem(String code, String word, int pos, int groupId) {
        Integer result = mCoreThreadEngine.executeFutureTask(
                () -> super.addPhraseItem(code, word, pos, groupId));
        return result == null ? -1 : result;
    }

    @Override
    public int editPhraseItem(int idx, String code, String word, int pos) {
        Integer result = mCoreThreadEngine.executeFutureTask(
                () -> super.editPhraseItem(idx, code, word, pos));
        return result == null ? -1 : result;
    }

    @Override
    public int deletePhraseItem(int idx) {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.deletePhraseItem(idx));
        return result == null ? -1 : result;
    }

    @Override
    public int importPhrase(String filename, boolean overWrite) {
        Integer result = mCoreThreadEngine.executeFutureTask(
                () -> super.importPhrase(filename, overWrite));
        return result == null ? -1 : result;
    }

    @Override
    public int exportPhrase(String filename, int groupId) {
        Integer result = mCoreThreadEngine.executeFutureTask(
                () -> super.exportPhrase(filename, groupId));
        return result == null ? -1 : result;
    }

    @Override
    public int reduceUsWord(int percent) {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.reduceUsWord(percent));
        return result == null ? -1 : result;
    }

    @Override
    public int getUsWordSize() {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.getUsWordSize());
        return result == null ? 0 : result;
    }

    @Override
    public int importUsWord(String path) {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.importUsWord(path));
        return result == null ? -1 : result;
    }

    @Override
    public int exportUsWord(String path) {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.exportUsWord(path));
        return result == null ? -1 : result;
    }

    @Override
    public int importUeWord(String path) {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.importUeWord(path));
        return result == null ? -1 : result;
    }

    @Override
    public int exportUeWord(String path) {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.exportUeWord(path));
        return result == null ? -1 : result;
    }

    @Override
    public int exportRareUserWord(String path) {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.exportRareUserWord(path));
        return result == null ? -1 : result;
    }

    @Override
    public int getHwRareVersion() {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.getHwRareVersion());
        return result == null ? 0 : result;
    }

    @Override
    public int getCellCount() {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.getCellCount());
        return result == null ? 0 : result;
    }

    @Override
    public IptCellInfo getCellInfoByIndex(int idx) {
        return mCoreThreadEngine.executeFutureTask(() -> super.getCellInfoByIndex(idx));
    }

    @Override
    public IptCellInfo getCellInfoByCellId(int cellId) {
        return mCoreThreadEngine.executeFutureTask(() -> super.getCellInfoByCellId(cellId));
    }

    @Override
    public boolean upateCellLocInfo(IptCellInfo info, int locType, int installTime) {
        if (info == null) {
            return false;
        }
        IptCellInfo newInfo = info.copy();
        Boolean result = mCoreThreadEngine.executeFutureTask(
                () -> super.upateCellLocInfo(newInfo, locType, installTime));
        return result == null ? false : result;
    }

    @Override
    public int getPopWordCellId() {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.getPopWordCellId());
        return result == null ? -1 : result;
    }

    @Override
    public int getSyswordCellId() {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.getSyswordCellId());
        return result == null ? -1 : result;
    }

    @Override
    public int getCloudWhiteVer() {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.getCloudWhiteVer());
        return result == null ? 0 : result;
    }

    @Override
    public int enableCell(int cellId, boolean isEnable) {
        Integer result =
                mCoreThreadEngine.executeFutureTask(() -> super.enableCell(cellId, isEnable));
        return result == null ? -1 : result;
    }

    @Override
    public int getRecentCount(int days) {
        Integer result =
                mCoreThreadEngine.executeFutureTask(() -> super.getRecentCount(days));
        return result == null ? -1 : result;
    }

    @Override
    public int getKwdCellCount() {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.getKwdCellCount());
        return result == null ? -1 : result;
    }

    @Override
    public IptKwdCellInfo getKwdCellInfoByIndex(int index) {
        return mCoreThreadEngine.executeFutureTask(() -> super.getKwdCellInfoByIndex(index));
    }

    @Override
    public IptKwdCellInfo getKwdCellInfoByCellId(int cellId) {
        return mCoreThreadEngine.executeFutureTask(() -> super.getKwdCellInfoByCellId(cellId));
    }

    @Override
    public int exportKwd(String filePath) {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.exportKwd(filePath));
        return result == null ? -1 : result;
    }

    @Override
    public int getIdmCellCount() {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.getIdmCellCount());
        return result == null ? -1 : result;
    }

    @Override
    public IptIdmCellInfo getIdmCellInfoByIndex(int index) {
        return mCoreThreadEngine.executeFutureTask(() -> super.getIdmCellInfoByIndex(index));
    }

    @Override
    public byte[] hwEncodePoint(int candId) {
        return mCoreThreadEngine.executeFutureTask(() -> super.hwEncodePoint(candId));
    }

    @Override
    public int contactReset() {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.contactReset());
        return result == null ? -1 : result;
    }

    @Override
    public int contactAppendAttr(String attr) {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.contactAppendAttr(attr));
        return result == null ? -1 : result;
    }

    @Override
    public int contactAppendValue(String name, String attr, String value) {
        Integer result = mCoreThreadEngine.executeFutureTask(
                () -> super.contactAppendValue(name, attr, value));
        return result == null ? -1 : result;
    }

    @Override
    public int contactBuildStart() {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.contactBuildStart());
        return result == null ? -1 : result;
    }

    @Override
    public int contactBuildAddName(String name) {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.contactBuildAddName(name));
        return result == null ? -1 : result;
    }

    @Override
    public int contactBuildAddValue(String attr, String value) {
        Integer result =
                mCoreThreadEngine.executeFutureTask(() -> super.contactBuildAddValue(attr, value));
        return result == null ? -1 : result;
    }

    @Override
    public int contactBuildEnd(boolean isRetainBlack) {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.contactBuildEnd(isRetainBlack));
        return result == null ? -1 : result;
    }

    @Override
    public int contactBuildAddBlackName(String delName) {
        Integer result =
                mCoreThreadEngine.executeFutureTask(() -> super.contactBuildAddBlackName(delName));
        return result == null ? -1 : result;
    }

    @Override
    public int contactDelete(String name, int option) {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.contactDelete(name, option));
        return result == null ? -1 : result;
    }

    @Override
    public String contactVoiceFind(String oriWord) {
        return mCoreThreadEngine.executeFutureTask(() -> super.contactVoiceFind(oriWord));
    }

    @Override
    public String contactVoiceFindAddressbook(String oriWord) {
        return mCoreThreadEngine.executeFutureTask(() -> super.contactVoiceFindAddressbook(oriWord));
    }

    @Override
    public int oldCpExport(String inputFile, String outputFile) {
        Integer result = mCoreThreadEngine.executeFutureTask(
                () -> super.oldCpExport(inputFile, outputFile));
        return result == null ? -1 : result;
    }

    @Override
    public int oldUeExport(String inputFile, String outputFile) {
        Integer result = mCoreThreadEngine.executeFutureTask(
                () -> super.oldUeExport(inputFile, outputFile));
        return result == null ? -1 : result;
    }

    @Override
    public int importZyword(String fileName) {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.importZyword(fileName));
        return result == null ? -1 : result;
    }

    @Override
    public int exportZyword(String fileName) {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.exportZyword(fileName));
        return result == null ? -1 : result;
    }

    @Override
    public int symImport(String fileName, boolean isOverwrite) {
        Integer result = mCoreThreadEngine.executeFutureTask(
                () -> super.symImport(fileName, isOverwrite));
        return result == null ? -1 : result;
    }

    @Override
    public int symExport(String fileName) {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.symExport(fileName));
        return result == null ? -1 : result;
    }

    @Override
    public int userSymImport(String[] symData) {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.userSymImport(symData));
        return result == null ? -1 : result;
    }

    @Override
    public int sylianImport(String fileName, boolean isOverwrite) {
        Integer result = mCoreThreadEngine.executeFutureTask(
                () -> super.sylianImport(fileName, isOverwrite));
        return result == null ? -1 : result;
    }

    @Override
    public int vkwordImport(String fileName, boolean isOverwrite) {
        Integer result = mCoreThreadEngine.executeFutureTask(
                () -> super.vkwordImport(fileName, isOverwrite));
        return result == null ? -1 : result;
    }

    @Override
    public int vkwordExport(String fileName) {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.vkwordExport(fileName));
        return result == null ? -1 : result;
    }

    @Override
    public int usrinfoImport(String fileName) {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.usrinfoImport(fileName));
        return result == null ? -1 : result;
    }

    @Override
    public int usrinfoExport(String fileName) {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.usrinfoExport(fileName));
        return result == null ? -1 : result;
    }

    @Override
    public int otherwordImport(String fileName, boolean isOverwrite) {
        Integer result = mCoreThreadEngine.executeFutureTask(
                () -> super.otherwordImport(fileName, isOverwrite));
        return result == null ? -1 : result;
    }

    @Override
    public int getZhuyinHzVersion() {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.getZhuyinHzVersion());
        return result == null ? 0 : result;
    }

    @Override
    public int getZhuyinCzVersion() {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.getZhuyinCzVersion());
        return result == null ? 0 : result;
    }

    @Override
    public int getZhuyinCzInfo() {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.getZhuyinCzInfo());
        return result == null ? 0 : result;
    }

    @Override
    public int getCangjieVersion() {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.getCangjieVersion());
        return result == null ? 0 : result;
    }

    @Override
    public int getHwVersion() {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.getHwVersion());
        return result == null ? 0 : result;
    }

    @Override
    public int searchGetVersion() {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.searchGetVersion());
        return result == null ? 0 : result;
    }

    @Override
    public int[] searchFind(String queryWord) {
        return mCoreThreadEngine.executeFutureTask(() -> super.searchFind(queryWord));
    }

    @Override
    public int adjustSylianRelation(String preStr, int preLen, String tailStr, int tailLen) {
        Integer result = mCoreThreadEngine.executeFutureTask(
                () -> super.adjustSylianRelation(preStr, preLen, tailStr, tailLen));
        return result == null ? -1 : result;
    }

    @Override
    public char[] featureInfExtract(String originStr, int originLen, int infType) {
        return mCoreThreadEngine.executeFutureTask(
                () -> super.featureInfExtract(originStr, originLen, infType));
    }

    @Override
    public int candContextExport(String fileName) {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.candContextExport(fileName));
        return result == null ? -1 : result;
    }

    @Override
    public String getFtByUni(String unicode) {
        return mCoreThreadEngine.executeFutureTask(() -> super.getFtByUni(unicode));
    }

    @Override
    @Deprecated
    public int importOldUsFile(String oldCellFile, String oldUzFile) {
        Integer result =
                mCoreThreadEngine.executeFutureTask(() -> super.importOldUsFile(oldCellFile, oldUzFile));
        return result == null ? -1 : result;
    }

    @Override
    public int kwdGetSearchVersion() {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.kwdGetSearchVersion());
        return result == null ? 0 : result;
    }

    @Override
    public int getAutoReplyVer() {
//        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.getAutoReplyVer());
//        return result == null ? 0 : result;
        return 0;
    }

    @Override
    public int getSpMapSheng(byte keyChar, byte[] shengList) {
        Integer result = mCoreThreadEngine.executeFutureTask(
                () -> super.getSpMapSheng(keyChar, shengList));
        return result == null ? -1 : result;
    }

    @Override
    public int getSpMapYun(byte keyChar, byte[] yunList) {
        Integer result = mCoreThreadEngine.executeFutureTask(
                () -> super.getSpMapYun(keyChar, yunList));
        return result == null ? -1 : result;
    }

    @Override
    public int importAppMap(String filePath) {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.importAppMap(filePath));
        return result == null ? -1 : result;
    }

    @Override
    public int appMapGetVersion() {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.appMapGetVersion());
        return result == null ? 0 : result;
    }

    @Override
    public int czDownRefresh(String fileName) {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.czDownRefresh(fileName));
        return result == null ? -1 : result;
    }

    @Override
    public int coreRefresh(int type, String fileName) {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.coreRefresh(type, fileName));
        return result == null ? -1 : result;
    }

    public int coreUnload(int type) {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.coreUnload(type));
        return result == null ? -1 : result;
    }

    @Override
    public int nnrankerRefresh(String configFilePath, String wordFilePath,
                               String modelFilePath) {
        Integer result = mCoreThreadEngine.executeFutureTask(
                () -> super.nnrankerRefresh(configFilePath, wordFilePath, modelFilePath));
        return result == null ? -1 : result;
    }

    @Override
    public int autoReplyRefresh(String fileName) {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.autoReplyRefresh(fileName));
        return result == null ? -1 : result;
    }

    @Override
    public int createUswordManager(String searchWord, int type) {
        Integer result = mCoreThreadEngine.executeFutureTask(
                () -> super.createUswordManager(searchWord, type));
        return result == null ? -1 : result;
    }

    @Override
    public int destroyUswordManager() {
        Integer result = mCoreThreadEngine.executeFutureTask(super::destroyUswordManager);
        return result == null ? -1 : result;
    }

    @Override
    public int uswordGetCount() {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.uswordGetCount());
        return result == null ? -1 : result;
    }

    @Override
    public String uswordGetStr(int idx) {
        return mCoreThreadEngine.executeFutureTask(() -> super.uswordGetStr(idx));
    }

    @Override
    public int uswordGetAction(int idx) {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.uswordGetAction(idx));
        return result == null ? -1 : result;
    }

    @Override
    public int uswordDoAction(int idx) {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.uswordDoAction(idx));
        return result == null ? -1 : result;
    }

    @Override
    public int uswordGetCnWordCount() {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.uswordGetCnWordCount());
        return result == null ? 0 : result;
    }

    @Override
    public int keywordFindVoiceLian(String content, int[] emojiList, String[] emoticonList) {
        Integer result =
                mCoreThreadEngine.executeFutureTask(
                        () -> super.keywordFindVoiceLian(content, emojiList, emoticonList));
        return result == null ? -1 : result;
    }

    @Override
    public char[] keywordFindVoiceEgg(String content) {
        return mCoreThreadEngine.executeFutureTask(() -> super.keywordFindVoiceEgg(content));
    }

    @Override
    public int backupTraceLog(String filePath) {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.backupTraceLog(filePath));
        return result == null ? -1 : result;
    }

    @Override
    public int blockCloudUnis(String content) {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.blockCloudUnis(content));
        return result == null ? -1 : result;
    }

    @Override
    public int resetCloudBlack() {
        Integer result = mCoreThreadEngine.executeFutureTask(super::resetCloudBlack);
        return result == null ? -1 : result;
    }

    @Override
    public int importOldDefWord(String filePath) {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.importOldDefWord(filePath));
        return result == null ? -1 : result;
    }

    @Override
    public byte[] getContactNamesInPb() {
        return mCoreThreadEngine.executeFutureTask(() -> super.getContactNamesInPb());
    }

    public String exportInputAssociateInfo() {
        return mCoreThreadEngine.executeFutureTask(() -> super.exportInputAssociateInfo());
    }

    public String getKeyCountForMisTouch() {
        return mCoreThreadEngine.executeFutureTask(() -> super.getKeyCountForMisTouch());
    }

    /**
     * 该函数没有说明
     */
    @Override
    public int dctCellInstallByBuffer(String value) {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.dctCellInstallByBuffer(value));
        return result == null ? -1 : result;
    }

    /**
     * 该函数没有说明
     */
    @Override
    public int dctCellWordInDict(String value) {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.dctCellWordInDict(value));
        return result == null ? -1 : result;
    }

    /**
     * 该函数没有说明
     */
    @Override
    public String dctCellExportBuffer(int cellId) {
        return mCoreThreadEngine.executeFutureTask(() -> super.dctCellExportBuffer(cellId));
    }

    /**
     * 该函数没有说明
     */
    @Override
    public int dctCellResetBuffer(int cellId) {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.dctCellResetBuffer(cellId));
        return result == null ? -1 : result;
    }

    @Override
    public int addPersonInfoFromLine(int index, String line) {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.addPersonInfoFromLine(index, line));
        return result == null ? -1 : result;
    }

    @Override
    public int startPersonInfoAddLine() {
        Integer result = mCoreThreadEngine.executeFutureTask(super::startPersonInfoAddLine);
        return result == null ? -1 : result;
    }

    @Override
    public int personInfoAddFinished() {
        Integer result = mCoreThreadEngine.executeFutureTask(super::personInfoAddFinished);
        return result == null ? -1 : result;
    }

    @Override
    public int resetPersonInfo() {
        Integer result = mCoreThreadEngine.executeFutureTask(super::resetPersonInfo);
        return result == null ? -1 : result;
    }
}
