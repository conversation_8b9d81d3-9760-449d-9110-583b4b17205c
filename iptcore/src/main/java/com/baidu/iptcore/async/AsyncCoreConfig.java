package com.baidu.iptcore.async;

import android.support.annotation.NonNull;

import com.baidu.iptcore.CoreConfig;
import com.baidu.iptcore.util.CoreOnlineLog;

/**
 * 设置类接口的线程切换管理
 */
public class AsyncCoreConfig extends CoreConfig {

    /**
     * 内核异步引擎
     */
    private final CoreThreadEngine mCoreThreadEngine;

    public AsyncCoreConfig(CoreThreadEngine engine) {
        mCoreThreadEngine = engine;
    }

    @Override
    public void setInt(int key, int value) {
        mCoreThreadEngine.executeTask(() -> super.setInt(key, value));
    }

    @Override
    public int getInt(int key) {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.getInt(key));
        return result == null ? 0 : result;
    }

    @Override
    public void setBoolean(int key, boolean value) {
        mCoreThreadEngine.executeTask(() -> super.setBoolean(key, value));
    }

    @Override
    public boolean getBoolean(int key) {
        Boolean result = mCoreThreadEngine.executeFutureTask(() -> super.getBoolean(key));
        return result == null ? false : result;
    }

    @Override
    public void setLong(int key, long value) {
        mCoreThreadEngine.executeTask(() -> super.setLong(key, value));
    }

    @Override
    public void setString(int key, String value) {
        mCoreThreadEngine.executeTask(() -> super.setString(key, value));
    }

    @Override
    public String getString(int key) {
        return mCoreThreadEngine.executeFutureTask(() -> super.getString(key));
    }

    @Override
    public void setAiWordsJson(String jsonStr, String noticeKey) {
        mCoreThreadEngine.executeTask(() -> super.setAiWordsJson(jsonStr, noticeKey));
    }

    @Override
    public void setTraceWarningSize(int base, int increment) {
        mCoreThreadEngine.executeTask(() -> super.setTraceWarningSize(base, increment));
    }

    @Override
    public void setCloudAddress(String cloudHttpHost, int cloudHttpPort,
                                String cloudUdpHost, int cloudUdpPort,
                                String sugHttpHost, int sugHttpPort,
                                String sugUdpHost, int sugUdpPort,
                                String nlpHttpHost, int nlpHttpPort,
                                boolean cloudUseUdp, boolean sugUseUdp) {
        mCoreThreadEngine.executeTask(() ->
                super.setCloudAddress(cloudHttpHost, cloudHttpPort, cloudUdpHost, cloudUdpPort,
                        sugHttpHost, sugHttpPort, sugUdpHost, sugUdpPort,
                        nlpHttpHost, nlpHttpPort,
                        cloudUseUdp, sugUseUdp));
    }

    @Override
    public void setDisplayCandType(boolean enable) {
        mCoreThreadEngine.executeTask(() -> super.setDisplayCandType(enable));
    }

    @Override
    public boolean getDisplayCandType() {
        return mCoreThreadEngine.executeFutureTask(() -> super.getDisplayCandType());
    }

    @Override
    public void setSceneGroupIDs(long[] groupIDs) {
        mCoreThreadEngine.executeTask(() -> super.setSceneGroupIDs(groupIDs));
    }

    @Override
    public void setUplDataAddress(String address, int port) {
        mCoreThreadEngine.executeTask(() -> super.setUplDataAddress(address, port));
    }

    @Override
    public void setOnlineLogLevel(@NonNull CoreOnlineLog logLevel) {
        mCoreThreadEngine.executeTask(() -> super.setOnlineLogLevel(logLevel));
    }
}
