package com.baidu.iptcore.async;

import com.baidu.iptcore.ImePlatformEnv;

/**
 * 异步输入模式的平台环境信息Callback
 */
public interface AsyncEnvCallback extends ImePlatformEnv {

    /**
     * 主线程同步coreInput回调
     */
    void syncCoreInputMainThread();

    /**
     * 内核线程同步coreInput回调
     */
    void syncCoreInputCoreThread();

    /**
     * 监听内核线程启动时间
     */
    default void startCoreThreadPost() {
    }

    /**
     * 内核线程开始执行时间点
     */
    default void startCoreTask() {
    }

    /**
     * 内核线程结束执行时间点
     */
    default void stopCoreTask(String methodName) {
    }
}
