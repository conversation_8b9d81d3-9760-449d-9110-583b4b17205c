package com.baidu.iptcore.async;

import com.baidu.annotation.CoreThread;
import com.baidu.iptcore.ImeLib;

@CoreThread
public class AsyncImeLib extends ImeLib {

    /**
     * 内核线程引擎
     */
    private final CoreThreadEngine mCoreThreadEngine;

    public AsyncImeLib(CoreThreadEngine coreThreadEngine) {
        mCoreThreadEngine = coreThreadEngine;
    }

    @Override
    public int getCoreVersion() {
        Integer result = mCoreThreadEngine.executeFutureTask(super::getCoreVersion);
        return result == null ? 0 : result;
    }

    @Override
    public String getCoreVersionStr() {
        String result = mCoreThreadEngine.executeFutureTask(super::getCoreVersionStr);
        return result == null ? "" : result;
    }

    @Override
    public int applyPatch(int type, String patchPath, String targetMd5, String originalFilePath) {
        Integer result =  mCoreThreadEngine.executeFutureTask(
                () -> super.applyPatch(type, patchPath, targetMd5, originalFilePath));
        return result == null ? 0 : result;
    }

    @Override
    public int getCz3DictGramVersion() {
        Integer result = mCoreThreadEngine.executeFutureTask(super::getCz3DictGramVersion);
        return result == null ? 0 : result;
    }

    @Override
    public int getCz3DictSysVersion() {
        Integer result = mCoreThreadEngine.executeFutureTask(super::getCz3DictSysVersion);
        return result == null ? 0 : result;
    }

    @Override
    public int getCz3DictCateVersion() {
        Integer result = mCoreThreadEngine.executeFutureTask(super::getCz3DictCateVersion);
        return result == null ? 0 : result;
    }

    @Override
    public int getCz5DownStatus() {
        Integer result = mCoreThreadEngine.executeFutureTask(super::getCz5DownStatus);
        return result == null ? 0 : result;
    }

    @Override
    public int getSlideDictVersion() {
        Integer result = mCoreThreadEngine.executeFutureTask(super::getSlideDictVersion);
        return result == null ? 0 : result;
    }

    @Override
    public boolean isNnrankerInstalled() {
        return mCoreThreadEngine.executeFutureTask(super::isNnrankerInstalled);
    }

    @Override
    public int getEnSysDictVersion() {
        Integer result = mCoreThreadEngine.executeFutureTask(super::getEnSysDictVersion);
        return result == null ? 0 : result;
    }

    @Override
    public int[] getUnloadedCzVersion(String dictDir) {
        return mCoreThreadEngine.executeFutureTask(() -> super.getUnloadedCzVersion(dictDir));
    }

    @Override
    public String getTraceLog() {
        return mCoreThreadEngine.executeFutureTask(super::getTraceLog);
    }

    @Override
    public void resetTraceLog() {
        mCoreThreadEngine.executeTask(super::resetTraceLog);
    }
}
