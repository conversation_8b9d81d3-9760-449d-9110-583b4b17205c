package com.baidu.iptcore.async;

import android.content.Context;
import android.graphics.Point;
import android.graphics.Rect;
import android.support.annotation.Nullable;

import com.baidu.annotation.CoreThread;
import com.baidu.input.support.state.AppAdTrigger;
import com.baidu.iptcore.ImeCoreConsts;
import com.baidu.iptcore.info.IptCoreSugAdAction;
import com.baidu.iptcore.info.IptCoreSugAdInfo;
import com.baidu.iptcore.info.IptTriggerWordItem;
import com.baidu.iptcore.info.IptContactItem;
import com.baidu.iptcore.info.IptCoreCandInfo;
import com.baidu.iptcore.info.IptCoreListInfo;
import com.baidu.iptcore.info.IptCoreShowInfo;
import com.baidu.iptcore.info.IptCoreSugCardInfo;
import com.baidu.iptcore.interceptor.InterceptImePad;

import java.util.Arrays;
import java.util.List;

/**
 * 异步化输入操作
 * <p>
 * Created by ch<PERSON><PERSON><PERSON> on 2019/2/17.
 */
@CoreThread
public class AsyncImePad extends InterceptImePad {

    /**
     * 内核线程引擎
     */
    private final CoreThreadEngine mCoreThreadEngine;

    /**
     * 异步输入的环境信息callback
     */
    private AsyncEnvCallback mEnvCallback;

    /**
     * 当前面板是否展现。主线程读写。
     * 起收面板的流程：switchPad(PY)->sendPadEvent(UP)->sendPadEvent(DOWN)->swtichPad(NONE)
     * 这个变量用于标记，除了起面板和收面板的两个switchKeyboard之外，在pad展示期间的面板切换，都需要是阻塞式
     */
    private boolean mIsPadShown;

    public AsyncImePad(CoreThreadEngine engine) {
        mCoreThreadEngine = engine;
        mCoreThreadEngine.setInnerImePad(this);
    }

    public void setAsyncEnvCallback(AsyncEnvCallback envCallback) {
        mEnvCallback = envCallback;
        mCoreThreadEngine.setAsyncEnvCallback(envCallback);
    }

    /**
     * 主线程同步coreInput回调
     */
    private void syncCoreInputMainThread() {
        if (mEnvCallback != null) {
            mEnvCallback.syncCoreInputMainThread();
        }
    }

    /**
     * 内核线程同步coreInput回调
     */
    private void syncCoreInputCoreThread() {
        if (mEnvCallback != null) {
            mEnvCallback.syncCoreInputCoreThread();
        }
    }

    @Override
    public void keymapClean() {
        mCoreThreadEngine.executeTask(() -> super.keymapClean());
    }

    @Override
    public void keymapAddChar(int id, char ch, int level) {
        mCoreThreadEngine.executeTask(() -> super.keymapAddChar(id, ch, level));
    }

    @Override
    public void actAIPadClick(int type) {
        this.actAIPadClick(type, null);
    }

    /**
     * AIPad（卡片）相关的点击操作
     * @param type 内核定义的点击行为： {@link ImeCoreConsts.AIPadActType}。
     * @param clickList 目前只有校对可能存在多个item。其余情况传入null即可
     */
    public void actAIPadClick(int type, short[] clickList) {
        syncCoreInputMainThread();
        mCoreThreadEngine.executeTask(() -> {
            syncCoreInputCoreThread();
            super.actAIPadClick(type, clickList);
        });
    }

    /**
     * 客户端上屏事件
     * @param type          {@link ImeCoreConsts.CustomInputDataType}
     * @param customData    上屏的具体文本
     */
    @Override
    public void actCustomInputAction(int type, String customData) {
        syncCoreInputMainThread();
        mCoreThreadEngine.executeTask(() -> {
            syncCoreInputCoreThread();
            super.actCustomInputAction(type, customData);
        });
    }

    public void actCandAction(int candType, int candIndex) {
        syncCoreInputMainThread();
        mCoreThreadEngine.executeTask(() -> {
            syncCoreInputCoreThread();
            super.actCandAction(candType, candIndex);
        });
    }

    @Override
    public void actKeyClicked(int keyId, int inputType) {
        if (ImeCoreConsts.isPadSwitchKey(keyId)) {
            // 面板切换相关的功能键，阻塞式调用
            syncCoreInputMainThread();
            mCoreThreadEngine.executeFutureTask(() -> {
                syncCoreInputCoreThread();
                super.actKeyClicked(keyId, inputType);
                return 0;
            });
        } else {
            actKeyClicked(keyId, inputType, null);
        }
    }

    @Override
    public void actChangeShift(int shiftId) {
        syncCoreInputMainThread();
        mCoreThreadEngine.executeTask(() -> {
            syncCoreInputCoreThread();
            super.actChangeShift(shiftId);
        });
    }

    @Override
    public void actKeyClicked(int keyId, int inputType, String uni) {
        syncCoreInputMainThread();
        mCoreThreadEngine.submitKeyAction(new KeyAction(keyId, inputType, uni));
    }

    @Override
    public void actKeyClicked(int keyId, int x, int y, int inputType, String uni) {
        syncCoreInputMainThread();
        mCoreThreadEngine.submitKeyAction(new KeyAction(keyId, x, y, inputType, uni));
    }

    @Override
    public void actKeyClicked(int keyId, int x, int y, byte power, byte area,
                              int inputType, String uni, boolean isMulti) {
        syncCoreInputMainThread();
        mCoreThreadEngine.submitKeyAction(new KeyAction(keyId, x, y, power, area, inputType, uni, isMulti));
    }

    @Override
    public void actCandClicked(final int candIdx) {
        syncCoreInputMainThread();
        mCoreThreadEngine.executeTask(() -> {
            syncCoreInputCoreThread();
            super.actCandClicked(candIdx);
        });
    }

    @Override
    public void actRareCandClicked(final int candIdx) {
        syncCoreInputMainThread();
        mCoreThreadEngine.executeTask(() -> {
            syncCoreInputCoreThread();
            super.actRareCandClicked(candIdx);
        });
    }

    @Override
    public void actCandSelect(final int candIdx) {
        syncCoreInputMainThread();
        mCoreThreadEngine.executeTask(() -> {
            syncCoreInputCoreThread();
            super.actCandSelect(candIdx);
        });
    }

    @Override
    public void actCandLongPress(final int candIdx) {
        syncCoreInputMainThread();
        mCoreThreadEngine.executeTask(() -> {
            syncCoreInputCoreThread();
            super.actCandLongPress(candIdx);
        });
    }

    @Override
    public void actListClicked(final int listIdx) {
        syncCoreInputMainThread();
        mCoreThreadEngine.executeTask(() -> {
            syncCoreInputCoreThread();
            super.actListClicked(listIdx);
        });

    }

    @Override
    public void actTabClicked(final int qpFilter) {
        syncCoreInputMainThread();
        mCoreThreadEngine.executeTask(() -> {
            syncCoreInputCoreThread();
            super.actTabClicked(qpFilter);
        });
    }

    @Override
    public byte[] getTabs() {
        return mCoreThreadEngine.executeFutureTask(super::getTabs);
    }

    @Override
    public void actCandInfoClick(final int index) {
        syncCoreInputMainThread();
        mCoreThreadEngine.executeTask(() -> {
            syncCoreInputCoreThread();
            super.actCandInfoClick(index);
        });

    }

    @Override
    public void actCandInfoCancel() {
        syncCoreInputMainThread();
        mCoreThreadEngine.executeTask(() -> {
            syncCoreInputCoreThread();
            super.actCandInfoCancel();
        });

    }

    @Override
    public void actContactInsert() {
        syncCoreInputMainThread();
        mCoreThreadEngine.executeTask(() -> {
            syncCoreInputCoreThread();
            super.actContactInsert();
        });

    }

    @Override
    public void actContactCancel() {
        syncCoreInputMainThread();
        mCoreThreadEngine.executeTask(() -> {
            syncCoreInputCoreThread();
            super.actContactCancel();
        });

    }

    @Override
    public void actContactInfoSelect(final int contactIdx, final int itemIdx,
                                     final boolean isSelect) {
        syncCoreInputMainThread();
        mCoreThreadEngine.executeTask(() -> {
            syncCoreInputCoreThread();
            super.actContactInfoSelect(contactIdx, itemIdx, isSelect);
        });

    }

    @Override
    public void actInputCursor(final int cursorIdx) {
        syncCoreInputMainThread();
        mCoreThreadEngine.executeTask(() -> {
            syncCoreInputCoreThread();
            super.actInputCursor(cursorIdx);
        });

    }

    @Override
    public void actInputCursorLeft() {
        syncCoreInputMainThread();
        mCoreThreadEngine.executeTask(() -> {
            syncCoreInputCoreThread();
            super.actInputCursorLeft();
        });
    }

    @Override
    public void actInputCursorRight() {
        syncCoreInputMainThread();
        mCoreThreadEngine.executeTask(() -> {
            syncCoreInputCoreThread();
            super.actInputCursorRight();
        });
    }

    @Override
    public void actInputPop() {
        syncCoreInputMainThread();
        mCoreThreadEngine.executeTask(() -> {
            syncCoreInputCoreThread();
            super.actInputPop();
        });
    }

    @Override
    public void actEditCursorChange() {
        syncCoreInputMainThread();
        mCoreThreadEngine.executeTask(() -> {
            syncCoreInputCoreThread();
            super.actEditCursorChange();
        });
    }

    @Override
    public void actTrackStart(int x, int y, final long time) {
        syncCoreInputMainThread();
        mCoreThreadEngine.executeTask(() -> {
            syncCoreInputCoreThread();
            super.actTrackStart(x, y, time);
        });
    }

    @Override
    public void actTrackMove(final int x, final int y, final long time) {
        syncCoreInputMainThread();
        mCoreThreadEngine.executeTask(() -> {
            syncCoreInputCoreThread();
            super.actTrackMove(x, y, time);
        });

    }

    @Override
    public void actTrackEnd(final int x, final int y, final long time) {
        actTrackEnd(x, y, time, false);
    }

    @Override
    public void actTrackEnd(final int x, final int y, final long time, boolean isGesture) {
        syncCoreInputMainThread();
        mCoreThreadEngine.executeTask(() -> {
            syncCoreInputCoreThread();
            super.actTrackEnd(x, y, time, isGesture);
        });

    }

    @Override
    public void actSugClick(final int index) {
        syncCoreInputMainThread();
        mCoreThreadEngine.executeTask(() -> {
            syncCoreInputCoreThread();
            super.actSugClick(index);
        });
    }

    @Override
    public void actSugCardClick(final int index, final boolean isInsert) {
        syncCoreInputMainThread();
        mCoreThreadEngine.executeTask(() -> {
            syncCoreInputCoreThread();
            super.actSugCardClick(index, isInsert);
        });
    }

    @Override
    public void actSugCardSelect(final int sugCardIdx) {
        syncCoreInputMainThread();
        mCoreThreadEngine.executeTask(() -> {
            syncCoreInputCoreThread();
            super.actSugCardSelect(sugCardIdx);
        });
    }

    @Override
    public void sendPadEvent(int padEvent, boolean restart) {
        if (padEvent == ImeCoreConsts.PAD_EVENT_UP) {
            mIsPadShown = true;
        } else if (padEvent == ImeCoreConsts.PAD_EVENT_DOWN) {
            mIsPadShown = false;
        }

        syncCoreInputMainThread();
        mCoreThreadEngine.executeTask(() -> {
            syncCoreInputCoreThread();
            super.sendPadEvent(padEvent, restart);
        });
    }

    @Override
    public void switchKeyboard(final int corePadId) {
        if (mIsPadShown) {
            // 面板展示期间的面板切换，阻塞式调用。mIsPadShown标志本身排除了起面板和收面板的情况
            syncCoreInputMainThread();
            mCoreThreadEngine.executeFutureTask(() -> {
                syncCoreInputCoreThread();
                super.switchKeyboard(corePadId);
                return 0;
            });
        } else {
            syncCoreInputMainThread();
            mCoreThreadEngine.executeTask(() -> {
                syncCoreInputCoreThread();
                super.switchKeyboard(corePadId);
            });
        }
    }

    @Override
    public void importSymList(final String[] listItems, final String[] listValues) {
        mCoreThreadEngine.executeTask(() -> super.importSymList(listItems, listValues));
    }

    @Override
    public void padSetLock(int[] symCateList, int[] symLockList) {
        mCoreThreadEngine.executeTask(() -> super.padSetLock(symCateList, symLockList));
    }

    @Override
    public int padGetLock(int[] symCateList, int[] symLockList) {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.padGetLock(symCateList,
                symLockList));
        return result == null ? -1 : result;
    }

    @Override
    public void padSetSymFilter(char[][] symList, int length) {
        mCoreThreadEngine.executeTask(() -> super.padSetSymFilter(symList, length));
    }

    @Override
    public void actShiftLongDown() {
        syncCoreInputMainThread();
        mCoreThreadEngine.executeTask(() -> {
            syncCoreInputCoreThread();
            super.actShiftLongDown();
        });
    }

    @Override
    public void actShiftLongUp() {
        syncCoreInputMainThread();
        mCoreThreadEngine.executeTask(() -> {
            syncCoreInputCoreThread();
            super.actShiftLongUp();
        });
    }

    @Override
    public String actCorrectVoiceData(final String orgResult) {
        syncCoreInputMainThread();
        return mCoreThreadEngine.executeFutureTask(
                () -> {
                    syncCoreInputCoreThread();
                    return super.actCorrectVoiceData(orgResult);
                });
    }

    @Override
    public int actCheckClip(char[] uniArray, int uniLen) {
        Integer result = mCoreThreadEngine.executeFutureTask(
                () -> super.actCheckClip(uniArray, uniLen));
        return result == null ? 0 : result;
    }

    @Override
    public int actCorrectVoiceSend() {
        syncCoreInputMainThread();
        Integer result = mCoreThreadEngine.executeFutureTask(
                () -> {
                    syncCoreInputCoreThread();
                    return super.actCorrectVoiceSend();
                });
        return result == null ? -1 : result;
    }

    @Override
    public int actAdjustEmojiRelation(int emojiValue, int cellId) {
        Integer result = mCoreThreadEngine.executeFutureTask(
                () -> super.actAdjustEmojiRelation(emojiValue, cellId));
        return result == null ? -1 : result;
    }

    @Override
    public int getCurrentPadId() {
        // currentPadId是上层的一个缓存，不需要进入内核线程执行
        return super.getCurrentPadId();
    }

    @Override
    public int getPadId() {
        Integer result = mCoreThreadEngine.executeFutureTask(
                () -> super.getPadId());
        return result == null ? ImeCoreConsts.PAD_NONE : result;
    }

    @Override
    public void actCnEnKey(int padId) {
        syncCoreInputMainThread();
        mCoreThreadEngine.executeTask(() -> {
            syncCoreInputCoreThread();
            super.actCnEnKey(padId);
        });
    }

    @Override
    public void actRareChaiZiHwKey(int padId) {
        syncCoreInputMainThread();
        mCoreThreadEngine.executeTask(() -> {
            syncCoreInputCoreThread();
            super.actRareChaiZiHwKey(padId);
        });
    }

    @Override
    public void setDefaultPad(int padId) {
        mCoreThreadEngine.executeTask(() -> super.setDefaultPad(padId));
    }

    @Override
    public String getHwPinyinStr(char unicode, boolean isAll) {
        return mCoreThreadEngine.executeFutureTask(
                () -> super.getHwPinyinStr(unicode, isAll));
    }

    @Override
    public boolean getStateValue(int stateIndex) {
        // TipState是上层的一个缓存，不需要进入内核线程执行
        return super.getStateValue(stateIndex);
    }

    @Override
    public int getCandCount() {
        Integer result = mCoreThreadEngine.executeFutureTask(
                () -> super.getCandCount());
        return result == null ? 0 : result;
    }

    @Override
    public IptCoreCandInfo getCandItem(int index) {
        return mCoreThreadEngine.executeFutureTask(
                () -> super.getCandItem(index));
    }

    @Override
    public List<IptCoreCandInfo> getAiCandItems() {
        return mCoreThreadEngine.executeFutureTask(
                super::getAiCandItems);
    }

    @Override
    public int loadCandFromCore(int start, int length, ICandLoadCallback callback) {
        Integer res = mCoreThreadEngine.executeFutureTask(
                () -> super.loadCandFromCore(start, length, callback));
        return res == null ? 0 : res;
    }

    @Override
    public IptCoreCandInfo getRareCandItem(int index) {
        return mCoreThreadEngine.executeFutureTask(() -> super.getRareCandItem(index));
    }
    @Override
    public int loadRareCandFromCore(int start, int length, ICandLoadCallback callback) {
        Integer res = mCoreThreadEngine.executeFutureTask(() -> super.loadRareCandFromCore(start, length, callback));
        return res == null ? 0 : res;
    }

    @Override
    public int getRareCandCount() {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.getRareCandCount());
        return result == null ? 0 : result;
    }

    @Deprecated
    @Override
    public IptCoreShowInfo getInputShow() {
        return mCoreThreadEngine.executeFutureTask(() -> super.getInputShow());
    }

    @Override
    public boolean getInputShow(IptCoreShowInfo outputShowInfo) {
        Boolean success = mCoreThreadEngine.executeFutureTask(
                () -> super.getInputShow(outputShowInfo));
        return success == null ? false : success;
    }

    @Override
    public int getListCount() {
        Integer result = mCoreThreadEngine.executeFutureTask(
                () -> super.getListCount());
        return result == null ? 0 : result;
    }

    @Override
    public IptCoreListInfo getListItem(int index) {
        return mCoreThreadEngine.executeFutureTask(
                () -> super.getListItem(index));
    }

    @Override
    public int getSugCount() {
        Integer result = mCoreThreadEngine.executeFutureTask(
                () -> super.getSugCount());
        return result == null ? 0 : result;
    }

    @Override
    public int getSugAdTimeoutMs() {
        Integer result = mCoreThreadEngine.executeFutureTask(super::getSugAdTimeoutMs);
        return result == null ? 0 : result;
    }

    @Override
    public IptCoreCandInfo getSugAt(int index) {
        return mCoreThreadEngine.executeFutureTask(
                () -> super.getSugAt(index));
    }

    @Override
    public int getSugCardCount() {
        Integer result = mCoreThreadEngine.executeFutureTask(
                () -> super.getSugCardCount());
        return result == null ? 0 : result;
    }

    @Override
    public IptCoreSugCardInfo getSugCardAt(int index) {
        return mCoreThreadEngine.executeFutureTask(
                () -> super.getSugCardAt(index));
    }

    @Override
    public int getCandInfoCount() {
        Integer result = mCoreThreadEngine.executeFutureTask(
                () -> super.getCandInfoCount());
        return result == null ? 0 : result;
    }

    @Override
    public String getCandInfo(int index) {
        return mCoreThreadEngine.executeFutureTask(
                () -> super.getCandInfo(index));
    }

    @Override
    public int getContactCount() {
        Integer result = mCoreThreadEngine.executeFutureTask(
                () -> super.getContactCount());
        return result == null ? 0 : result;
    }

    @Override
    public int getSugSelectedPosition() {
        Integer result = mCoreThreadEngine.executeFutureTask(
                () -> super.getSugSelectedPosition());
        return result == null ? -1 : result;
    }

    @Override
    public int getSugCardSelectedPosition() {
        Integer result = mCoreThreadEngine.executeFutureTask(
                () -> super.getSugCardSelectedPosition());
        return result == null ? -1 : result;
    }

    @Override
    public int getSugState() {
        Integer result = mCoreThreadEngine.executeFutureTask(
                () -> super.getSugState());
        return result == null ? ImeCoreConsts.PAD_SUG_STATE_NONE : result;
    }

    @Override
    public int getPyHotLetter(byte[] alphas) {
        Integer result = mCoreThreadEngine.executeFutureTask(
                () -> super.getPyHotLetter(alphas));
        return result == null ? -1 : result;
    }

    @Override
    public void actCurSugClose() {
        syncCoreInputMainThread();
        mCoreThreadEngine.executeTask(() -> {
            syncCoreInputCoreThread();
            super.actCurSugClose();
        });
    }

    @Override
    public void userTraceStartPadKeyLayout() {
        mCoreThreadEngine.executeTask(() -> super.userTraceStartPadKeyLayout());
    }

    @Override
    public void userTraceFinishPadKeyLayout() {
        mCoreThreadEngine.executeTask(() -> super.userTraceFinishPadKeyLayout());
    }

    @Override
    public void userTraceWrite(int action, int container, byte[] content) {
        mCoreThreadEngine.executeTask(() -> super.userTraceWrite(action, container, content));
    }

    @Override
    public void userVoiceInputLog(String content) {
        mCoreThreadEngine.executeTask(() -> super.userVoiceInputLog(content));
    }

    @Override
    public IptContactItem getContactItem(int index) {
        return mCoreThreadEngine.executeFutureTask(
                () -> super.getContactItem(index));
    }

    @Override
    public int getTriggerWordCount() {
        Integer result = mCoreThreadEngine.executeFutureTask(
                () -> super.getTriggerWordCount());
        return result == null ? 0 : result;
    }
    @Override
    public IptTriggerWordItem getTriggerWordItemItem(int index) {
        return mCoreThreadEngine.executeFutureTask(
                () -> super.getTriggerWordItemItem(index));
    }

    @Override
    public long getTriggerWordItemsId() {
        Long result = mCoreThreadEngine.executeFutureTask(
                () -> super.getTriggerWordItemsId());
        return result == null ? 0 : result;
    }

    @Override
    public void sendLocationEvent(float latitude, float longitude) {
        mCoreThreadEngine.executeTask(() -> {super.sendLocationEvent(latitude, longitude);});
    }

    @Override
    public String getCandContext(int idx) {
        return mCoreThreadEngine.executeFutureTask(
                () -> super.getCandContext(idx));
    }

    @Override
    public int getHwSmartDelayTime() {
        Integer res = mCoreThreadEngine.executeFutureTask(
                super::getHwSmartDelayTime);
        return res == null ? 0 : res;
    }

    @Override
    public char[] getEgg() {
        return mCoreThreadEngine.executeFutureTask(
                () -> super.getEgg());
    }

    @Override
    public int getSrvCloudWhiteVer() {
        Integer result = mCoreThreadEngine.executeFutureTask(
                () -> super.getSrvCloudWhiteVer());
        return result == null ? 0 : result;
    }

    @Override
    public int getSugType() {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.getSugType());
        return result == null ? ImeCoreConsts.SUG_CARD_TYPE_NONE : result;
    }

    @Override
    public int getSugActionType() {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.getSugActionType());
        return result == null ? 0 : result;
    }

    @Override
    public int getSugActionType(int index) {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.getSugActionType(index));
        return result == null ? 0 : result;
    }

    @Override
    public String getSugAdGlobalId(int index) {
        return mCoreThreadEngine.executeFutureTask(() -> super.getSugAdGlobalId(index));
    }

    @Override
    public int getSugSourceId() {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.getSugSourceId());
        return result == null ? 0 : result;
    }

    @Override
    public int getSugId() {
        Integer result = mCoreThreadEngine.executeFutureTask(() -> super.getSugId());
        return result == null ? 0 : result;
    }

    @Override
    public String getSugSourceMsg() {
        return mCoreThreadEngine.executeFutureTask(() -> super.getSugSourceMsg());
    }

    @Override
    public void setPadLayout(int[] rect, boolean isSplit) {
        int[] rectCopy = Arrays.copyOf(rect, rect.length);
        mCoreThreadEngine.executeTask(() -> super.setPadLayout(rectCopy, isSplit));
    }

    @Override
    public int getTouchedKey(int x, int y, long timeoutMillis) {
        Integer result = mCoreThreadEngine.executeFutureTask(timeoutMillis, () -> super.getTouchedKey(x, y, timeoutMillis));
        return result == null ? 0 : result;
    }

    @Override
    public void setPadKeyPos(byte keyId, int[] viewRect, int[] touchRect) {
        int[] viewRectCopy = Arrays.copyOf(viewRect, viewRect.length);
        int[] touchRectCopy = Arrays.copyOf(touchRect, touchRect.length);
        mCoreThreadEngine.executeTask(() -> super.setPadKeyPos(keyId, viewRectCopy, touchRectCopy));
    }

    @Override
    public void execCallback(int tag, long callBack) {
        mCoreThreadEngine.executeTask(() -> {
            super.execCallback(tag, callBack);
        });
    }

    @Override
    public void actAiFontGeneratedNotify(int type, String showContent, String scheme
            , String insertContent, long displayTime) {
        mCoreThreadEngine.executeTask(() -> super.actAiFontGeneratedNotify(
                type, showContent, scheme, insertContent, displayTime));
    }

    @Override
    public boolean loadAiFontHwModel(Context context, String path, String licensePath) {
        Boolean res = mCoreThreadEngine.executeFutureTask(() -> super.loadAiFontHwModel(context, path, licensePath));
        return res != null && res;
    }

    @Override
    public void unLoadAiFontHwModel() {
        mCoreThreadEngine.executeTask(super::unLoadAiFontHwModel);
    }

    @Override
    public int aiFontHwModelVersion() {
        return super.aiFontHwModelVersion();
    }

    @Override
    public int getIntentStyle(String content) {
        Integer res = mCoreThreadEngine.executeFutureTask(() -> super.getIntentStyle(content));
        return res == null ? 0 : res;
    }

    @Override
    public int aiFontRecoPoint(String character, int[] points) {
        Integer res = mCoreThreadEngine.executeFutureTask(() -> super.aiFontRecoPoint(character, points));
        return res == null ? 0 : res;
    }

    @Override
    public void actToolbarClick() {
        mCoreThreadEngine.executeTask(() -> {
            super.actToolbarClick();
        });
    }

    @Nullable
    @Override
    public String getInlineShow() {
        return mCoreThreadEngine.executeFutureTask(super::getInlineShow);
    }

    @Override
    public void sendWmSugAdAction(int index, int sugActionType) {
        mCoreThreadEngine.executeTask(() -> super.sendWmSugAdAction(index, sugActionType));
    }

    @Override
    public void sendCloudIntentionCardAction(int firstLevel, int secondLevel, String title, int actionType) {
        mCoreThreadEngine.executeTask(() -> super.sendCloudIntentionCardAction(firstLevel, secondLevel, title, actionType));
    }

    @Override
    public void loadCoreSugAd(AppAdTrigger trigger) {
        mCoreThreadEngine.executeTask(() -> super.loadCoreSugAd(trigger));
    }

    @Override
    public int getCoreSugAdCount(AppAdTrigger trigger) {
        Integer res = mCoreThreadEngine.executeFutureTask(() -> super.getCoreSugAdCount(trigger));
        return res == null ? 0 : res;
    }

    @Override
    public IptCoreSugAdInfo getCoreSugAdAt(int index, AppAdTrigger trigger) {
        return mCoreThreadEngine.executeFutureTask(() -> super.getCoreSugAdAt(index, trigger));
    }

    @Override
    public IptCoreSugAdAction coreSugAdClick(int index, AppAdTrigger trigger, Rect rect, Point downPoint, Point upPoint) {
        return mCoreThreadEngine.executeFutureTask(() -> super.coreSugAdClick(index, trigger, rect, downPoint, upPoint));
    }

    @Override
    public void coreSugAdShow(int index, Rect rect, AppAdTrigger trigger) {
        mCoreThreadEngine.executeTask(() -> super.coreSugAdShow(index, rect, trigger));
    }

    @Override
    public void coreSugAdTimeout(int time, AppAdTrigger trigger) {
        mCoreThreadEngine.executeTask(() -> super.coreSugAdTimeout(time, trigger));
    }

    @Override
    public void coreSugAdWin(int index, AppAdTrigger trigger) {
        mCoreThreadEngine.executeTask(() -> super.coreSugAdWin(index, trigger));
    }

    @Override
    public void coreSugAdFail(int index, int reason, long price, AppAdTrigger trigger) {
        mCoreThreadEngine.executeTask(() -> super.coreSugAdFail(index, reason, price, trigger));
    }

    @Override
    public void coreSugAdDownloadStart(int index, AppAdTrigger trigger) {
        mCoreThreadEngine.executeTask(() -> super.coreSugAdDownloadStart(index, trigger));
    }


    @Override
    public void coreSugAdDownloadFinish(int index, AppAdTrigger trigger) {
        mCoreThreadEngine.executeTask(() -> super.coreSugAdDownloadFinish(index, trigger));
    }

    @Override
    public void coreSugAdInstallFinish(int index, AppAdTrigger trigger) {
        mCoreThreadEngine.executeTask(() -> super.coreSugAdInstallFinish(index, trigger));
    }

    @Override
    public void coreSugAdOpenUrlApp(int index, AppAdTrigger trigger) {
        mCoreThreadEngine.executeTask(() -> super.coreSugAdOpenUrlApp(index, trigger));
    }

    @Override
    public void coreSugAdOpenFallbackUrl(int index, AppAdTrigger trigger) {
        mCoreThreadEngine.executeTask(() -> super.coreSugAdOpenFallbackUrl(index, trigger));
    }

    @Override
    public void coreSugAdDplSuccess(int index, AppAdTrigger trigger) {
        mCoreThreadEngine.executeTask(() -> super.coreSugAdDplSuccess(index, trigger));
    }

    @Override
    public void coreSugAdDplFailed(int index, AppAdTrigger trigger) {
        mCoreThreadEngine.executeTask(() -> super.coreSugAdDplFailed(index, trigger));
    }


    @Override
    public boolean enableGaussTrain(boolean forcible) {
        Boolean res = mCoreThreadEngine.executeFutureTask(() -> super.enableGaussTrain(forcible));
        return res != null && res;
    }

    @Override
    public boolean isMatchSugWhiteData() {
        Boolean res = mCoreThreadEngine.executeFutureTask(super::isMatchSugWhiteData);
        return res != null && res;
    }

    @Override
    public int getEncryptVersion() {
        Integer res = mCoreThreadEngine.executeFutureTask(() -> super.getEncryptVersion());
        return res == null ? 0 : res;
    }

    @Override
    public int getEncryptB64Version() {
        Integer res = mCoreThreadEngine.executeFutureTask(() -> super.getEncryptB64Version());
        return res == null ? 0 : res;
    }

    @Override
    public byte[] b64Encode(byte[] input) {
        return mCoreThreadEngine.executeFutureTask(() -> super.b64Encode(input));
    }

    @Override
    public byte[] b64Decode(byte[] input) {
        return mCoreThreadEngine.executeFutureTask(() -> super.b64Decode(input));
    }

    @Override
    public byte[] aesEncrypt(byte[] input) {
        return mCoreThreadEngine.executeFutureTask(() -> super.aesEncrypt(input));
    }

    @Override
    public byte[] aesEncryptV2(byte[] input) {
        return mCoreThreadEngine.executeFutureTask(() -> super.aesEncryptV2(input));
    }

    @Override
    public byte[] aesDecrypt(byte[] input) {
        return mCoreThreadEngine.executeFutureTask(() -> super.aesDecrypt(input));
    }

    @Override
    public byte[] aesB64Encrypt(byte[] input) {
        return mCoreThreadEngine.executeFutureTask(() -> super.aesB64Encrypt(input));
    }

    @Override
    public byte[] aesB64EncryptV2(byte[] input) {
        return mCoreThreadEngine.executeFutureTask(() -> super.aesB64EncryptV2(input));
    }

    @Override
    public byte[] aesB64Decrypt(byte[] input) {
        return mCoreThreadEngine.executeFutureTask(() -> super.aesB64Decrypt(input));
    }

    @Override
    public byte[] rsaEncrypt(byte[] input) {
        return mCoreThreadEngine.executeFutureTask(() -> super.rsaEncrypt(input));
    }

    @Override
    public byte[] rsaDecrypt(byte[] input) {
        return mCoreThreadEngine.executeFutureTask(() -> super.rsaDecrypt(input));
    }

    @Override
    public byte[] encryptionAICardRequestData(int checkId, byte[] inContent) {
        return mCoreThreadEngine.executeFutureTask(
                () -> super.encryptionAICardRequestData(checkId, inContent)
        );
    }

    @Override
    public byte[] decryptAICardResponseData(int checkId, byte[] inContent) {
        return mCoreThreadEngine.executeFutureTask(
                () -> super.decryptAICardResponseData(checkId, inContent)
        );
    }

    @Override
    public int doHonorNotepadAssociationQuery(String input, int mode, long requestId, int limitCount) {
        Integer res = mCoreThreadEngine.executeFutureTask(
                () -> super.doHonorNotepadAssociationQuery(input, mode, requestId, limitCount));
        return res == null ? 0 : res;
    }
    @Override
    public void dctSetNameByBuff(String app, long ts, boolean isNew) {
        mCoreThreadEngine.executeTask(() -> super.dctSetNameByBuff(app, ts, isNew));
    }
}
