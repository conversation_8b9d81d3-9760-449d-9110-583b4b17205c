package com.baidu.iptcore.async;

import android.os.HandlerThread;
import android.os.Looper;
import android.os.SystemClock;
import android.util.Printer;

/**
 * 内核线程.
 *
 * <AUTHOR>
 * @since 2025/3/31
 */
class CoreThread extends HandlerThread implements Printer, ICoreTaskStatisticsHandler {

    /**
     * 基于日志 log  tag 来判断消息是否开始
     */
    private static final String MESSAGE_START_TAG = ">>>>> Dispatching to";

    private static final String MESSAGE_END_TAG = "<<<<< Finished to";

    private static final long MESSAGE_INVALID_MILLS = 0;

    /**
     * 内核事件开始的时间戳
     */
    private volatile long taskStartMillis = MESSAGE_INVALID_MILLS;

    /**
     * 任务准备执行的时间戳。对应 Handler#sendMessageAtTime
     */
    private volatile long taskPrepare2StartMills = MESSAGE_INVALID_MILLS;

    /**
     * 是否启用监听。默认
     */
    private boolean isEnabled = false;


    public CoreThread(String name) {
        super(name);
    }

    public CoreThread(String name, int priority) {
        super(name, priority);
    }

    @Override
    protected void onLooperPrepared() {
        super.onLooperPrepared();
        if (isEnabled) {
            getLooper().setMessageLogging(this);
        }
    }

    @Override
    public void println(String x) {
        if (x == null) {
            return;
        }
        if (x.startsWith(MESSAGE_START_TAG)) {
            onCoreTaskStart();
        } else if (x.startsWith(MESSAGE_END_TAG)) {
            onCoreTaskEnd();
        }
    }

    /**
     * 内核任务开始
     */
    private void onCoreTaskStart() {
        taskStartMillis = SystemClock.elapsedRealtime();
        taskPrepare2StartMills = MESSAGE_INVALID_MILLS;
    }

    /**
     * 内核任务结束
     */
    private void onCoreTaskEnd() {
        taskStartMillis = MESSAGE_INVALID_MILLS;
        taskPrepare2StartMills = MESSAGE_INVALID_MILLS;
    }

    @Override
    public void enable(boolean enable) {
        Looper looper = getLooper();
        isEnabled = enable;
        if (looper != null) {
            if (enable) {
                looper.setMessageLogging(this);
            } else {
                looper.setMessageLogging(null);
                reset();
            }
        }
    }

    @Override
    public boolean isCoreThreadBusy(long threshold) {
        return isTaskTakeTooMuch(threshold);
    }

    @Override
    public void updatePendingTaskMills() {
        // 此时通过其他线程发送了一个 Message 到内核内核线程
        if (taskPrepare2StartMills == MESSAGE_INVALID_MILLS) {
            taskPrepare2StartMills = SystemClock.elapsedRealtime();
        }
    }

    /**
     * 当前任务是否已经执行一段时间了。
     * 主要用于探测需要主线程和内核线程交互的场景如（防误触方法）
     * 简单加一个策略，如果内核线程本身在执行耗时较久的方法，那就不要执行这些方法了
     *
     * @param threshold 设置的阈值
     * @return true 超过了阈值。
     */
    public boolean isTaskTakeTooMuch(long threshold) {
        if (!isEnabled) {
            return false;
        }
        long now = SystemClock.elapsedRealtime();
        // case1: 内核线程长时间得不到调度
        if (isTaskTakeTooMuch(now, taskPrepare2StartMills, threshold)) {
            return true;
        }
        // case2：内核线程正在执行任务，已经耗时很久了
        return isTaskTakeTooMuch(now, taskStartMillis, threshold);
    }


    /**
     * 检测下当前任务是否耗时太久了
     *
     * @param now       当前CPU 时间
     * @param compared  任务开始时
     * @param threshold 阈值
     * @return 是否为耗时较久的任务
     */
    private boolean isTaskTakeTooMuch(long now, long compared, long threshold) {
        if (compared <= MESSAGE_INVALID_MILLS || threshold <= MESSAGE_INVALID_MILLS) {
            return false;
        }

        return now - compared >= threshold;
    }

    private void reset() {
        taskPrepare2StartMills = MESSAGE_INVALID_MILLS;
        taskStartMillis = MESSAGE_INVALID_MILLS;
    }
}
