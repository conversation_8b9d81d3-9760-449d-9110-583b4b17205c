package com.baidu.iptcore.async;

import android.os.Handler;
import android.os.Looper;
import android.os.Process;

import com.baidu.input.inputtrace.api.InputTracer;
import com.baidu.iptcore.BuildConfig;
import com.baidu.iptcore.ImeCoreConsts;
import com.baidu.iptcore.ImePad;
import com.baidu.iptcore.IptCoreInterface;
import com.baidu.iptcore.info.IptCoreDutyInfo;
import com.baidu.iptcore.util.Config;
import com.baidu.iptcore.util.Ids;
import com.baidu.iptcore.util.Logger;
import com.baidu.iptcore.util.MethodTracer;
import com.baidu.iptcore.util.TimeStatisticsUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * 内核线程的引擎
 *
 * Created by chendenfeng on 2019/2/16.
 */
public class CoreThreadEngine {
    /**
     * 按键模拟延时
     */
    private static long keyCost;

    /**
     * 设置按键模拟延时
     */
    public static void setKeyCost(long keyCost) {
        CoreThreadEngine.keyCost = keyCost;
    }

    /**
     * 内核线程的Handler
     */
    private final Handler mHandler;
    /**
     * 待执行的KeyAction列表
     */
    private final List<KeyAction> mKeyActionList = new ArrayList<>();
    /**
     * 是否有KeyAction在列表中等待执行
     */
    private volatile boolean mPendingInputTask;
    /**
     * 当前正在执行的KeyAcion列表，仅内核线程读写
     */
    private final List<KeyAction> mExecutingKeyActionList = new ArrayList<>();

    private ImePad mImePad;

    private AsyncEnvCallback mEnvCallback;

    private final ICoreTaskStatisticsHandler asyncTaskCostCallback;

    public CoreThreadEngine() {
        CoreThread mHandlerThread = new CoreThread("CoreThread", Process.THREAD_PRIORITY_FOREGROUND);
        mHandlerThread.start();
        asyncTaskCostCallback = mHandlerThread;
        mHandler = new CoreThreadHandler(mHandlerThread.getLooper(), asyncTaskCostCallback);
        // 记录内核线程的id
        mHandler.post(() -> InputTracer.coreThreadId(Thread.currentThread().getId()));
    }

    /**
     * 设置内部的imePad的实现
     * @param imePad imePad的实现
     */
    void setInnerImePad(ImePad imePad) {
        mImePad = imePad;
    }

    void setAsyncEnvCallback(AsyncEnvCallback envCallback) {
        mEnvCallback = envCallback;
    }

    /**
     * 提交一个按键点击事件
     * @param keyAction 按键事件
     */
    void submitKeyAction(KeyAction keyAction) {
        synchronized (mKeyActionList) {
            mKeyActionList.add(keyAction);

            if (!mPendingInputTask) {
                mPendingInputTask = true;
                InputTask inputTask = new InputTask();
                executeTask(inputTask);
            }
        }
    }

    private boolean isBatchInputKey(KeyAction keyAction) {
        return !ImeCoreConsts.isFunctionKey(keyAction.keyId);
    }

    /**
     * 在内核线程提交一个任务
     * @param r 任务
     */
    public void executeTask(Runnable r) {
        // 刚开始，内核暂时没有打开，非输入任务，直接关闭
        if (!IptCoreInterface.get().isCoreOpened() && !(r instanceof InputTask)) {
            return;
        }

        if (Looper.myLooper() == mHandler.getLooper()) {
            r.run();
        } else {
            Runnable task = r;
            if (MethodTracer.isTraceOpen()) {
                task = () -> {
                    MethodTracer.startCoreMethod();
                    // 执行任务
                    InputTracer.i(Ids.coreTaskRun);
                    if (mEnvCallback != null) {
                        mEnvCallback.startCoreTask();
                    }
                    r.run();
                    if (mEnvCallback != null) {
                        mEnvCallback.stopCoreTask(MethodTracer.getNowMethodName());
                    }
                    // 结束任务
                    InputTracer.o(Ids.coreTaskRun);
                    MethodTracer.stopCoreMethod();
                };
            }
            if (mEnvCallback != null) {
                mEnvCallback.startCoreThreadPost();
            }
            mHandler.post(task);
        }
    }

    /**
     * 在内核线程提交一个带返回值的任务
     * @param task 带返回值的任务
     */
    <T> T executeFutureTask(final CoreTask<T> task) {
        if (!IptCoreInterface.get().isCoreOpened()) {
            return null;
        }

        return executeFutureTaskInternal(task);
    }

    /**
     * 在内核线程提交一个带返回值的任务
     * @param task 带返回值的任务
     * @param timeoutMillis 超时时间，单位：毫秒
     */
    <T> T executeFutureTask(long timeoutMillis, final CoreTask<T> task) {
        if (!IptCoreInterface.get().isCoreOpened()) {
            return null;
        }
        return executeFutureTaskInternal(timeoutMillis, task);
    }

    private <T> T executeFutureTaskInternal(CoreTask<T> task) {
        return executeFutureTaskInternal(0, task);
    }

    private <T> T executeFutureTaskInternal(long timeoutMillis, CoreTask<T> task) {
        if (Looper.myLooper() == mHandler.getLooper()) {
            return task.get();
        } else {
            final CoreFuture<T> future = new CoreFuture<T>();
            Runnable r = () -> {
                MethodTracer.startCoreMethod();
                // 执行任务
                InputTracer.i(Ids.coreFutureTaskRun);
                if (mEnvCallback != null) {
                    mEnvCallback.startCoreTask();
                }
                T result = task.get();
                if (mEnvCallback != null) {
                    mEnvCallback.stopCoreTask(MethodTracer.getNowMethodName());
                }
                InputTracer.o(Ids.coreFutureTaskRun);
                MethodTracer.stopCoreMethod();
                future.setResult(result);
                synchronized (future) {
                    future.notifyAll();
                }
            };

            T result;
            synchronized (future) {
                if (mEnvCallback != null) {
                    mEnvCallback.startCoreThreadPost();
                }
                mHandler.post(r);

                // 主线程在等待内核线程的返回结果
                InputTracer.i(Ids.mainWaitForCore);
                try {
                    if (timeoutMillis <= 0) {
                        future.wait();
                    } else {
                        future.wait(timeoutMillis);
                    }
                } catch (InterruptedException e) {
                    if (BuildConfig.DEBUG) {
                        Logger.printStackTrace(e);
                    }
                    future.setResult(null);
                }
                InputTracer.o(Ids.mainWaitForCore);
                result = future.getResult();
            }
            return result;
        }
    }

    public <T> T executeLifecycleTask(final CoreTask<T> task) {
        return executeFutureTaskInternal(task);
    }

    public Looper getLooper() {
        return mHandler.getLooper();
    }

    public boolean isThreadBusy(long threshold) {
        final ICoreTaskStatisticsHandler callback = asyncTaskCostCallback;
        return callback != null && callback.isCoreThreadBusy(threshold);
    }

    public void enableThreadStatistics(boolean enable) {
        final ICoreTaskStatisticsHandler callback = asyncTaskCostCallback;
        if (callback != null) {
            callback.enable(enable);
        }
    }

    /**
     * 一次按键输入任务
     */
    private class InputTask implements Runnable {
        private long mLastDutyTime = 0L;
        @Override
        public void run() {
            synchronized (mKeyActionList) {
                mExecutingKeyActionList.clear();
                mExecutingKeyActionList.addAll(mKeyActionList);
                mKeyActionList.clear();
                mPendingInputTask = false;
            }

            // 当内核未打开，将输入任务停止
            if (!IptCoreInterface.get().isCoreOpened()) {
                return;
            }

            if (mEnvCallback != null) {
                mEnvCallback.syncCoreInputCoreThread();
            }

            // 先判断是否是可以BatchInput的按键，
            // 如果是，则添加到列表保证快速点击时，多个按键可以合并查询后一次刷新
            // 如果不是，则正常一个按键执行一次

            MethodTracer.startCoreMethod();
            TimeStatisticsUtil.startTrace();

            // 执行速度慢时，累积的batch按键中，过滤掉可以跳过不执行的按键
            filterDiscardableBatchKeys(mExecutingKeyActionList);

            IptCoreDutyInfo dutyInfo = null;
            IptCoreDutyInfo validDutyInfo = null;
            mLastDutyTime = System.currentTimeMillis();
            for (KeyAction keyAction : mExecutingKeyActionList) {

                if (isBatchInputKey(keyAction)) {

                    // 含有AI纠错半上屏的Flag不可以丢弃
                    if (dutyInfo != null && (dutyInfo.flashFlag() & IptCoreDutyInfo.REFL_AI_CORRECT_INLINE) > 0) {
                        mImePad.dutyEntry(dutyInfo);
                        validDutyInfo = null;
                        TimeStatisticsUtil.endAndRestart();
                        mLastDutyTime = System.currentTimeMillis();
                    }

                    // 保证至少100毫秒更新一次UI
                    if (validDutyInfo != null && System.currentTimeMillis() - mLastDutyTime > 100) {
                        mImePad.dutyEntry(validDutyInfo);
                        validDutyInfo = null;
                        mLastDutyTime = System.currentTimeMillis();
                    }

                    dutyInfo = actKeyClick(keyAction);
                    if (dutyInfo != null) {
                        validDutyInfo = dutyInfo;
                        boolean isAICorrectInline = (dutyInfo.flashFlag() & IptCoreDutyInfo.REFL_AI_CORRECT_INLINE) > 0;
                        validDutyInfo.setBatchInput(!isAICorrectInline);
                    }

                } else {
                    // 提交上一次按键duty
                    if (validDutyInfo != null) {
                        mImePad.dutyEntry(validDutyInfo);
                        TimeStatisticsUtil.endAndRestart();
                    }
                    // 执行本次按键duty并置空
                    dutyInfo = actKeyClick(keyAction);
                    mImePad.dutyEntry(dutyInfo);
                    dutyInfo = null;
                    validDutyInfo = null;
                    mLastDutyTime = System.currentTimeMillis();
                }
            }

            if (validDutyInfo != null) {
                mImePad.dutyEntry(validDutyInfo);
            }

            TimeStatisticsUtil.endTrace();

            MethodTracer.stopCoreMethod();
        }

        /**
         * 当执行速度较慢时，如果累积了多个按键事件，可以过滤掉一些可以丢弃的按键事件
         */
        private void filterDiscardableBatchKeys(List<KeyAction> executingKeyActions) {

            // 长按删除时，如果一次累积多个删除事件，可以丢弃多余的，只保留一个。
            // 用于保证长按速率也随着执行速率变慢，避免累积太多UI刷新事件
            int size = executingKeyActions.size();
            boolean hasBackHoldKey = false;
            KeyAction keyAction;
            for (int i = size - 1; i >= 0; i--) {
                keyAction = executingKeyActions.get(i);
                if (keyAction.keyId == ImeCoreConsts.FKEY_PLATFORM_BACK_HOLD) {
                    if (hasBackHoldKey) {
                        executingKeyActions.remove(i);
                    } else {
                        hasBackHoldKey = true;
                    }
                }
            }
        }

        private IptCoreDutyInfo actKeyClick(KeyAction keyAction) {
            long a = 0;
            if (Config.enableLog()) {
                MethodTracer.recordCoreMethod("actKeyClicked_CoreThread: keyId="
                        + keyAction.keyId + ", inputType=" + keyAction.inputType
                        + ", uni=" + keyAction.uni
                        + (keyAction.isKp ? ", x=" + keyAction.x + ", y=" + keyAction.y : "")
                        + (keyAction.hasTouchInfo ?
                                   ", power=" + keyAction.power + ", area=" + keyAction.area : "")
                );
                a = System.currentTimeMillis();
            }

            IptCoreDutyInfo dutyInfo;

            int keyId = keyAction.keyId;
            if (keyId == ImeCoreConsts.FKEY_PLATFORM_BACK_HOLD) {
                keyId = ImeCoreConsts.FKEY_BACK;
            }

            if (keyAction.isKp) {
                if (keyAction.hasTouchInfo) {
                    dutyInfo = mImePad.actKeyClickInternal(keyId,
                            keyAction.x, keyAction.y,
                            keyAction.power, keyAction.area,
                            keyAction.inputType, keyAction.uni);
                } else {
                    dutyInfo = mImePad.actKeyClickInternal(keyId,
                            keyAction.x, keyAction.y, keyAction.inputType,
                            keyAction.uni);
                }
            } else {
                dutyInfo = mImePad.actKeyClickInternal(keyId, keyAction.x, keyAction.y, keyAction.inputType,
                        keyAction.uni);
            }

            if (Config.enableLog()) {
                MethodTracer.recordKeyClickMethod(System.currentTimeMillis() - a);
            }

            if (keyCost > 0) {
                try {
                    Thread.sleep(keyCost);
                } catch (InterruptedException e) {
                    Logger.printStackTrace(e);
                }
            }

            if (dutyInfo != null && keyAction.isMulti) {
                dutyInfo.setDelayUiUpdate();
            }

            return dutyInfo;
        }
    }

    /**
     * 内核线程的一个带返回值的task的封装
     * @param <T> 返回值类型
     */
    public interface CoreTask<T> {
        T get();
    }

    /**
     * 内核一个异步带返回值的Future的封装
     * @param <T> 返回值类型
     */
    public static class CoreFuture<T> {
        private T data;

        public void setResult(T data) {
            this.data = data;
        }

        public T getResult() {
            return data;
        }
    }
}
