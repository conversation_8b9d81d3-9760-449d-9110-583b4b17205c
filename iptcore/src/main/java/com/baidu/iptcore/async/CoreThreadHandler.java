package com.baidu.iptcore.async;

import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;

/**
 * 处理内核线程的 Handler.
 *
 * <AUTHOR>
 * @since 2025/3/31
 */
public class CoreThread<PERSON>and<PERSON> extends Handler {

    private final ICoreTaskStatisticsHandler taskCostCallback;

    public CoreThreadHandler(ICoreTaskStatisticsHandler taskCostCallback) {
        this.taskCostCallback = taskCostCallback;
    }

    public CoreThreadHandler(@Nullable Callback callback, ICoreTaskStatisticsHandler taskCostCallback) {
        super(callback);
        this.taskCostCallback = taskCostCallback;
    }

    public CoreThreadHandler(@NonNull Looper looper, ICoreTaskStatisticsHandler taskCostCallback) {
        super(looper);
        this.taskCostCallback = taskCostCallback;
    }

    public CoreThreadHandler(@NonNull Looper looper, @Nullable Callback callback, ICoreTaskStatisticsHandler taskCostCallback) {
        super(looper, callback);
        this.taskCostCallback = taskCostCallback;
    }

    @Override
    public boolean sendMessageAtTime(@NonNull Message msg, long uptimeMillis) {
        final ICoreTaskStatisticsHandler callback = taskCostCallback;
        if (callback != null) {
            callback.updatePendingTaskMills();
        }
        return super.sendMessageAtTime(msg, uptimeMillis);
    }
}
