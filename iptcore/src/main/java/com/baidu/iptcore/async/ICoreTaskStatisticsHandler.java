package com.baidu.iptcore.async;

/**
 * 内核线程任务处理器
 *
 * <AUTHOR>
 * @since 2025/3/31
 */
public interface ICoreTaskStatisticsHandler {


    /**
     * 是否启用任务监听
     * @param enable
     */
    void enable(boolean enable);

    /**
     * 内核线程是否繁忙。主要用于主线程直接同内核线程交互的场景，防止出现太长时间的主线程空等任务的 case
     * @param threshold 设定的阈值
     * @return true/false
     */
    boolean isCoreThreadBusy(long threshold);

    /**
     * 更新下发送消息时的时间，用于检查处理内核线程长时间得不到 CPU 时间片的场景
     */
    void updatePendingTaskMills();
}
