package com.baidu.iptcore.async;

/**
 * 按键点击动作
 * Created by cdf on 2019/2/16.
 */
public class KeyAction {
    /** 按键id */
    public final int keyId;
    /** 按键输入方式 */
    public final int inputType;
    /** 按键id是FKEY_DEFINE的时候，对应的输入字符串 */
    public final String uni;
    /** 按键点击坐标 */
    public final int x;
    /** 按键点击坐标 */
    public final int y;
    /** 按键是否是纠错按键 */
    public final boolean isKp;
    /** 按键点击压力 */
    public final byte power;
    /** 按键点击面积 */
    public final byte area;
    /** 是否具有按键压力和面积的触摸信息 */
    public final boolean hasTouchInfo;
    /** 是否是多点情况下的按键点击 */
    public final boolean isMulti;

    public KeyAction(int keyId, int inputType, String uni) {
        this(keyId, 0, 0, false, (byte) 0, (byte) 0, false, inputType, uni, false);
    }

    public KeyAction(int keyId, int x, int y, int inputType, String uni) {
        this(keyId, x, y, true, (byte) 0, (byte) 0, false, inputType, uni, false);
    }

    public KeyAction(int keyId, int x, int y, byte power, byte area,
                     int inputType, String uni, boolean isMulti) {
        this(keyId, x, y, true, power, area, true, inputType, uni, isMulti);
    }

    private KeyAction(int keyId, int x, int y, boolean isKp,
                      byte power, byte area, boolean hasTouchInfo,
                      int inputType, String uni, boolean isMulti) {
        this.keyId = keyId;
        this.inputType = inputType;
        this.uni = uni;
        this.x = x;
        this.y = y;
        this.isKp = isKp;
        this.power = power;
        this.area = area;
        this.hasTouchInfo = hasTouchInfo;
        this.isMulti = isMulti;
    }
}
