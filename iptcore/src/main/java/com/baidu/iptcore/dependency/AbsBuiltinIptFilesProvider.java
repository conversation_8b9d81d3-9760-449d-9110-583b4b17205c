package com.baidu.iptcore.dependency;

import com.baidu.input.common.utils.CollectionUtil;
import com.baidu.iptcore.ImeCoreConsts;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public abstract class AbsBuiltinIptFilesProvider {

    /**
     * 默认的输入法内置内核文件列表。
     * 保存需要拷贝的内核文件的最小集合即可。用于词典安装时，从Asset下读取词典列表有误时，替代的最小词典集合
     */
    private static String[] defBuiltinIptFiles = new String[] {
        ImeCoreConsts.IPTCORE_DICT_APP_MAP, // app_map2.bin
                ImeCoreConsts.IPTCORE_DICT_BH, // bh4.bin
                ImeCoreConsts.IPTCORE_DICT_CZ, // cz5.bin
                ImeCoreConsts.INSTALL_FILE_EMOJI, // emoji.kwd
                ImeCoreConsts.INSTALL_FILE_VOICE_EMOJI, // emoji_voice.kwd
                ImeCoreConsts.INSTALL_FILE_EMOJI_EXTEND, // emojiextend.kwd
                ImeCoreConsts.INSTALL_FILE_YAN, // emoticon.kwd
                ImeCoreConsts.INSTALL_FILE_VOICE_YAN, // emoticon_voice.kwd
                ImeCoreConsts.IPTCORE_DICT_EN_SYS, // en5.bin
                ImeCoreConsts.INSTALL_FILE_FASTINPUT, // fast_input.kwd
                ImeCoreConsts.IPTCORE_DICT_FT, // fanti3.bin
                ImeCoreConsts.IPTCORE_DICT_WT, // hzmdl_rnn.bin -> hw6.bin
                ImeCoreConsts.INSTALL_FILE_YAN_IDM, // idmap.idm
                ImeCoreConsts.INSTALL_FILE_NIJIGEN, // nijigen.kwd
                ImeCoreConsts.IPTCORE_DICT_PHRASE_OLD, // phrase.bin
                ImeCoreConsts.INSTALL_FILE_PHRASE_PLUS, // phrase_plus.ini
                ImeCoreConsts.IPTCORE_DICT_PY3TRANSPROB, // py3transprob.bin
                ImeCoreConsts.SYS_FILE_SP26, // sp26.ini
                ImeCoreConsts.IPTCORE_DICT_SYML, // sylian.bin
                ImeCoreConsts.IPTCORE_DICT_SYM, // ipt3sym_sys.bin
                ImeCoreConsts.IPTCORE_DICT_WB86, // wb3.bin
                ImeCoreConsts.IPTCORE_DICT_WB98, // wb398.bin
                ImeCoreConsts.IPTCORE_DICT_XHY, // xhy2.bin
                ImeCoreConsts.IPTCORE_DICT_CORRECTION, // iec3dict.bin
                ImeCoreConsts.BAYESIAN_SYLIAN,  // bayesian_sylian.bin
                ImeCoreConsts.BLACK_WHITE_DICT, // black_white_dict.bin
                ImeCoreConsts.BLACK_WHITE_LIST, // black_white_list.bin
                ImeCoreConsts.BLIND_ASSISTANCE, // blind_assistance.bin
                ImeCoreConsts.CLOUD_BLACK_DICT, // cloud_black_dict.bin
                ImeCoreConsts.CLOUD_BLACK_LIST, // cloud_black_list.bin
                ImeCoreConsts.NAME_MODE,    // name_mode.bin
                ImeCoreConsts.PY_CORR_TONE, // py3corr2_tone.bin
                ImeCoreConsts.IPTCORE_DICT_PY3TRANSPROB,    // py3transprob.bin
                ImeCoreConsts.IPTCORE_DICT_HW_LICENSE,    // hw_lic7.bin
                ImeCoreConsts.IPTCORE_DICT_SENS,    // sens.bin
                ImeCoreConsts.IPTCORE_DICT_TTFFILTER  // ttffilter.ini
    };

    /**
     * 公共的内置词库文件
     */
    public final List<String> getPublicBuiltinIptFiles() {
        return Arrays.asList(defBuiltinIptFiles);
    }

    /**
     * 该Flavor下独有的内置词库文件，如华为/荣耀内置的注音仓颉
     */
    public abstract List<String> getFlavorBuiltinIptFiles();

    /**
     * 获取所有的内置文件列表
     */
    public final List<String> getAllBuiltinIptFiles() {
        return new ArrayList<String>() {
            {
                addAll(getPublicBuiltinIptFiles());

                List<String> flavorBuiltIptFiles = getFlavorBuiltinIptFiles();
                if (!CollectionUtil.isEmpty(flavorBuiltIptFiles)) {
                    addAll(getFlavorBuiltinIptFiles());
                }
            }
        };
    }
}
