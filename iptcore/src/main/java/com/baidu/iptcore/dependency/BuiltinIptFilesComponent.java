package com.baidu.iptcore.dependency;

import com.baidu.pyramid.annotation.Inject;
import com.baidu.pyramid.annotation.component.Component;
import com.baidu.pyramid.annotation.component.Holder;

/**
 * 内置词库的依赖需求方
 */
@Component
public class BuiltinIptFilesComponent {

    private static final BuiltinIptFilesComponent INSTANCE = new BuiltinIptFilesComponent();

    @Inject
    private Holder<AbsBuiltinIptFilesProvider> builtinIptFilesProviderHolder;

    // 为了依赖注入，不能为private
    BuiltinIptFilesComponent() {

    }

    public static BuiltinIptFilesComponent of() {
        return INSTANCE;
    }

    public AbsBuiltinIptFilesProvider getBuiltinFilesProvider() {
        return builtinIptFilesProviderHolder.get();
    }
}
