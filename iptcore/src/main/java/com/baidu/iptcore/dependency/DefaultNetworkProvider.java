package com.baidu.iptcore.dependency;

import com.baidu.pyramid.annotation.Service;
import com.baidu.pyramid.annotation.Singleton;

import java.util.concurrent.TimeUnit;

import okhttp3.OkHttpClient;

/**
 * DefaultNetworkProvider Introduce.
 *
 * <AUTHOR>
 * @since 2025/2/13
 */
@Service
@Singleton
public class DefaultNetworkProvider implements IImeNetworkProvider {
    @Override
    public OkHttpClient provideOkHttpClient(long timeMillis) {

        return new OkHttpClient.Builder()
                .connectTimeout(timeMillis, TimeUnit.MILLISECONDS)
                .build();
    }
}
