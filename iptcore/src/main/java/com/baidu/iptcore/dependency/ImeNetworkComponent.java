package com.baidu.iptcore.dependency;

import com.baidu.pyramid.annotation.Inject;
import com.baidu.pyramid.annotation.component.Component;
import com.baidu.pyramid.annotation.component.ListHolder;

import java.util.List;

/**
 * 内置词库的依赖需求方
 */
@Component
public class ImeNetworkComponent {

    private static final ImeNetworkComponent INSTANCE = new ImeNetworkComponent();

    @Inject
    private ListHolder<IImeNetworkProvider> networkProviders;

    // 为了依赖注入，不能为private
    ImeNetworkComponent() {

    }

    public static ImeNetworkComponent of() {
        return INSTANCE;
    }

    public IImeNetworkProvider getNetworkProvider() {
        List<IImeNetworkProvider> list = networkProviders.getList();
        if (list.size() > 1) {
            for (IImeNetworkProvider provider : list) {
                if (provider instanceof DefaultNetworkProvider) {
                    continue;
                }
                return provider;
            }
        } else if (!list.isEmpty()){
            return list.get(0);
        }
        return null;
    }
}
