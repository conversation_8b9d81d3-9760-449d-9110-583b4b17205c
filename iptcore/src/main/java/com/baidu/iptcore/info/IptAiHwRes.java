package com.baidu.iptcore.info;

import android.support.annotation.Keep;
import android.support.annotation.Nullable;

import com.baidu.input.common.utils.CollectionUtil;

import java.util.ArrayList;
import java.util.List;


/**
 * created by yanglingyun<PERSON> on 8/5/21
 */
@Keep
public class IptAiHwRes {

    private List<AiHwResItem> aiHwResItems;
    private List<String> characters;

    @Keep
    public void setData(int count, String[] characters, short[] reserved, int[] pr) {
        if (count <= 0 || characters == null || reserved == null || pr == null
                || characters.length < count || reserved.length < count || pr.length < count) {
            return;
        }
        aiHwResItems = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            aiHwResItems.add(new AiHwResItem(characters[i], reserved[i], pr[i]));
        }
    }

    @Nullable
    public List<String> getCharacters() {
        if (!CollectionUtil.isEmpty(aiHwResItems)) {
            if (null == characters) {
                characters = new ArrayList<>();
            }
            characters.clear();
            for (int i = 0; i < aiHwResItems.size(); i++) {
                characters.add(i, aiHwResItems.get(i).character);
            }
        }
        return characters;
    }

    public static class AiHwResItem {

        private String character;
        private short reserved;
        private int pr;

        public AiHwResItem(String character, short reserved, int pr) {
            this.character = character;
            this.reserved = reserved;
            this.pr = pr;
        }
    }
}
