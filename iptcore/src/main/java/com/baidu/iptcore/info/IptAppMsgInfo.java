package com.baidu.iptcore.info;

import java.util.Arrays;

import android.support.annotation.Keep;

/**
 * 检测指定app是否安装的包信息
 *
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/8/26.
 */
@Keep
public class IptAppMsgInfo {

    private String packageName;
    private int[] minVersion;
    private int[] maxVersion;
    private int versionNum;

    @Keep
    public void setPackageName(String packageName){
        this.packageName = packageName;
    }

    @Keep
    public void setVersion(int[] minVer, int[] maxVer, int verNum) {
        this.minVersion = minVer;
        this.maxVersion = maxVer;
        this.versionNum = verNum;
    }

    public String packageName() {
        return packageName;
    }

    public int[] minVersion() {
        return minVersion;
    }

    public int[] maxVersion() {
        return maxVersion;
    }

    public int versionNum() {
        return versionNum;
    }

    @Override
    public String toString() {
        return "appmsg{" + packageName +
                ", " + Arrays.toString(minVersion) +
                "-" + Arrays.toString(maxVersion) +
                ", num=" + versionNum +
                '}';
    }
}
