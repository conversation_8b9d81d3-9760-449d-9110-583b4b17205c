package com.baidu.iptcore.info;

import android.support.annotation.Keep;

/**
 * 细胞词库相关信息
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/2/7.
 */
@Keep
public class IptCellInfo {

    /** 词库类型：默认 */
    private static final int CELL_TYPE_DEFAULT = 0;
    /** 词库类型：历史位置，含当前地 */
    public static final int CELL_TYPE_HISTORY_LOC = 1;
    /** 词库类型：常驻地 */
    public static final int CELL_TYPE_RESIDENCE = 2;

    /**
     * 细胞词库可见,{@link #isHide}
     */
    public static final int CELL_VISIBAL = 0;
    /**
     * 细胞词库不可见,{@link #isHide}
     */
    public static final int CELL_INVISIBAL = 1;
    
    /** cell_id编号 */
    private int cellId;
    /** server_Id编号 */
    private int serverId;
    /** 词数量 */
    private int ciCount;
    /** 词库是否打开 */
    private boolean isOpen;
    /** 安装包类型, 0代表覆盖安装, 1代表升级安装 */
    private int dataType;
    /** 升级后版本号 */
    private int innerVer;
    /** 升级前版本号 */
    private int innerVerFrom;
    /** 版本号1 */
    private int ver1;
    /** 版本号2 */
    private int ver2;
    /** 版本号3 */
    private int ver3;
    /** 类型1 */
    private int type1;
    /** 类型2 */
    private int type2;
    /** 类型3 */
    private int type3;
    /** 词库名 */
    private String name;
    /** 作者 */
    private String author;
    /** 关键词 */
    private String keyword;
    /** 词库信息 */
    private String info;
    
    /** 服务端维护的词库类型。0：普通类型，1：所在地；2：常驻地 */
    public int serverType;
    /** 服务端维护的词库更新时间 */
    public int serverTime;
    /** 是否隐藏,{@link #CELL_INVISIBAL},{@link #CELL_VISIBAL}*/
    public int isHide;

    @Keep
    public void setData(int cellId, int serverId, int ciCount, boolean isOpen, int dataType,
                        int innerVer, int innerVerFrom, int ver1,
                        int ver2, int ver3, int type1, int type2, int type3,
                        int locType, int isHide, int installTime) {
        this.cellId = cellId;
        this.serverId = serverId;
        this.ciCount = ciCount;
        this.isOpen = isOpen;
        this.dataType = dataType;
        this.innerVer = innerVer;
        this.innerVerFrom = innerVerFrom;
        this.ver1 = ver1;
        this.ver2 = ver2;
        this.ver3 = ver3;
        this.type1 = type1;
        this.type2 = type2;
        this.type3 = type3;
        this.serverType = locType;
        this.serverTime = installTime;
        this.isHide = isHide;
    }

    @Keep
    public void setData(String name, String author, String keyword, String info) {
        this.name = name;
        this.author = author;
        this.keyword = keyword;
        this.info = info;
    }

    public int cellId() {
        return cellId;
    }

    public int serverId() {
        return serverId;
    }

    public int ciCount() {
        return ciCount;
    }

    public boolean isOpen() {
        return isOpen;
    }

    public int dataType() {
        return dataType;
    }

    public int innerVer() {
        return innerVer;
    }

    public int innerVerFrom() {
        return innerVerFrom;
    }

    public int ver1() {
        return ver1;
    }

    public int ver2() {
        return ver2;
    }

    public int ver3() {
        return ver3;
    }

    public int type1() {
        return type1;
    }

    public int type2() {
        return type2;
    }

    public int type3() {
        return type3;
    }

    public String name() {
        return name;
    }

    public String author() {
        return author;
    }

    public String keyword() {
        return keyword;
    }

    public String info() {
        return info;
    }

    public void setCellId(int cellId) {
        this.cellId = cellId;
    }

    public void setServerId(int serverId) {
        this.serverId = serverId;
    }

    public void setCiCount(int ciCount) {
        this.ciCount = ciCount;
    }

    public void setOpen(boolean open) {
        isOpen = open;
    }

    public void setDataType(int dataType) {
        this.dataType = dataType;
    }

    public void setInnerVer(int innerVer) {
        this.innerVer = innerVer;
    }

    public void setInnerVerFrom(int innerVerFrom) {
        this.innerVerFrom = innerVerFrom;
    }

    public void setVer1(int ver1) {
        this.ver1 = ver1;
    }

    public void setVer2(int ver2) {
        this.ver2 = ver2;
    }

    public void setVer3(int ver3) {
        this.ver3 = ver3;
    }

    public void setType1(int type1) {
        this.type1 = type1;
    }

    public void setType2(int type2) {
        this.type2 = type2;
    }

    public void setType3(int type3) {
        this.type3 = type3;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setAuthor(String author) {
        this.author = author;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public void setInfo(String info) {
        this.info = info;
    }

    /**
     * 是否是静默下载的地理词库
     * @return true: 是静默下载的地理词库
     */
    public boolean isAutoDownloadGeo() {
        return serverType == CELL_TYPE_HISTORY_LOC || serverType == CELL_TYPE_RESIDENCE;
    }

    public IptCellInfo copy() {
        IptCellInfo newInfo = new IptCellInfo();
        newInfo.cellId = cellId;
        newInfo.serverId = serverId;
        newInfo.ciCount = ciCount;
        newInfo.isOpen = isOpen;
        newInfo.dataType = dataType;
        newInfo.innerVer = innerVer;
        newInfo.innerVerFrom = innerVerFrom;
        newInfo.ver1 = ver1;
        newInfo.ver2 = ver2;
        newInfo.ver3 = ver3;
        newInfo.type1 = type1;
        newInfo.type2 = type2;
        newInfo.type3 = type3;
        newInfo.name = name;
        newInfo.author = author;
        newInfo.keyword = keyword;
        newInfo.info = info;
        return newInfo;
    }
}
