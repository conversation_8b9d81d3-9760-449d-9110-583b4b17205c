package com.baidu.iptcore.info;

import java.util.Arrays;

import android.support.annotation.Keep;

/**
 * 联系人信息的封装
 * 
 * Created by <PERSON><PERSON><PERSON><PERSON> on 18/5/9.
 */
@Keep
public class IptContactItem {
    
    /** 联系人的全名 */
    private String fullName;
    
    /** 属性数量 */
    private int attrCnt;
    
    /** 所有属性的名称 */
    private String[] attrNames;
    
    /** 所有属性的值 */
    private String[] attrValues;

    @Keep
    public void setData(String fullName, int attrCnt, String[] attrNames, String[] attrValues) {
        this.fullName = fullName;
        this.attrCnt = attrCnt;
        this.attrNames = attrNames;
        this.attrValues = attrValues;
    }

    public String getFullName() {
        return fullName;
    }

    public int getAttrCnt() {
        return attrCnt;
    }

    public String getAttrName(int index) {
        if (index >= 0 && index < attrCnt) {
            return attrNames[index];
        } else {
            return null;
        }
    }

    public String getAttrValue(int index) {
        if (index >= 0 && index < attrCnt) {
            return attrValues[index];
        } else {
            return null;
        }
    }

    @Override
    public String toString() {
        return "IptContactItem{" + "fullName='" + fullName + '\'' + ", attrCnt=" + attrCnt 
                + ", attrNames=" + Arrays.toString(attrNames) + ", attrValues=" + Arrays.toString(attrValues) + '}';
    }
}
