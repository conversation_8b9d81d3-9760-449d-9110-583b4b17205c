/*
 * Copyright (C) 2017 Baidu, Inc. All Rights Reserved.
 */
package com.baidu.iptcore.info;

import android.graphics.Color;
import android.graphics.Rect;
import android.support.annotation.Keep;
import android.support.annotation.Nullable;

import com.baidu.iptcore.ImeCoreConsts;
import com.baidu.iptcore.util.ColorUtils;

/**
 * 内核对于Cand条候选词的信息封装
 */
@Keep
public class IptCoreCandInfo {
    // /////////////////////候选词类型区域////////////////////////

    public static final byte CANDTYPE_NONE = 0; // 无类型
    public static final byte CANDTYPE_NORMAL = 1; // 普通候选,正常展示汉字即可
    public static final byte CANDTYPE_CLOUD_ARROW = 2; // 云动画箭头
    public static final byte CANDTYPE_SERVICE = 3; // 云服务结果
    public static final byte CANDTYPE_FAST_INPUT = 4; // 快速输入
    public static final byte CANDTYPE_MEDIA_POS = 5;   ///< 多媒体-定位
    public static final byte CANDTYPE_MEDIA_PIC = 6;   ///< 多媒体-图片
    public static final byte CANDTYPE_MEDIA_SOUND = 7; ///< 多媒体-声音
    public static final byte CANDTYPE_MEDIA_WRITE = 8; ///< 多媒体-笔记
    public static final byte CANDTYPE_MEDIA_PEN = 9;   ///< 多媒体-涂鸦
    public static final byte CANDTYPE_ZHIDAHAO = 10; // 直达号
    public static final byte CANDTYPE_OP = 11; // 运营词
    public static final byte CANDTYPE_XIEHOUYU = 12; // 歇后语
    public static final byte CANDTYPE_EMOJI = 13; // 表情
    public static final byte CANDTYPE_EMOTICON = 14; // 颜文字
    public static final byte CANDTYPE_EMOJI_LIAN = 15; // 表情联想
    public static final byte CANDTYPE_EMOTICON_LIAN = 16; // 颜文字联想
    public static final byte CANDTYPE_NIJIGEN = 17; // 二次元输入
    public static final byte CANDTYPE_CONTACT_LIAN = 18; // 联系人联想
    public static final byte CANDTYPE_USER_PASTE = 19; // 上层获取的剪切板数据
    public static final byte CANDTYPE_USER_AUDIO = 20; // 上层获取的语音数据
    public static final byte CANDTYPE_USER_OCR = 21; // 上层获取的OCR数据
    public static final byte CANDTYPE_VOICE_EGG = 22; // 语音联想彩蛋
    public static final byte CANDTYPE_CLOUD_LIAN = 23; // 云输入联想
    public static final byte CANDTYPE_CLOUD_FORCAST_LIAN = 24; // 云输入通过预测得到的联想
    public static final byte CANDTYPE_AUTO_REPLY = 25; // 智能回复
    public static final byte CANDTYPE_TOP_PROMOTION = 26; // PC平台6号推荐位数据
    public static final byte CANDTYPE_CLOUD_ZJ_FORCAST = 27; // 云输入整句预测结果
    public static final byte CANDTYPE_CLOUD_PREFETCH = 28; // 云输入预取
    public static final byte CANDTYPE_SMART_SEARCH = 29; // 云输入智能搜索
    public static final byte CANDTYPE_FIXTERM = 30; // 固守词库标识
    public static final byte CANDTYPE_VMODE = 31; // V模式的标识
    public static final byte CANDTYPE_CLOUD_EMOJI = 32; // 云输入表情
    public static final byte CANDTYPE_FROM_LIST = 33; // 自定义list里面的点击上屏
    public static final byte CANDTYPE_ZJ_PRE_HINT_LOCAL = 34; // 整句前序本地词
    public static final byte CANDTYPE_SERVICE_LIAN = 35; // 云输入联想服务
    public static final byte CANDTYPE_NLP_FORCAST = 36; // 输入后整句结果
    public static final byte CANDTYPE_NLP_COMPOOSE = 37; // 智能云词撰写
    public static final byte CANDTYPE_NLP_CORRECTION = 38; // 智能云词纠错
    public static final byte CANDTYPE_CHAIZI = 39; // 智能云词生僻字
    public static final byte CANDTYPE_CALC = 40;  // 计算器结果
    public static final byte CANDTYPE_CALC_EQUATION = 41; // 计算器等式
    public static final byte CANDTYPE_CLOUD_CAMPAIGN = 42;  // 智能云词活动
    /** AI趣聊-RAP */
    public static final byte CANDTYPE_NLP_FUNCHAT_RAP = 43;
    /** AI趣聊-藏头诗 */
    public static final byte CANDTYPE_NLP_FUNCHAT_ACROSTIC = 44;
    /** AI趣聊-现代诗 */
    public static final byte CANDTYPE_NLP_FUNCHAT_MODERNPOERTY = 45;
    /** 次cand上的AI校对入口 */
    public static final byte CANDTYPE_AI_CORRECT_ENTRY = 46;
    /** 次Cand上的神句配图开关 */
    public static final byte CANDTYPE_AIPEITU_PREFERENCE = 47;
    /** 次Cand上购物评价的开关 */
    public static final byte CANDTYPE_SHOP_SCENE_PREFERENCE = 48;
    public static final byte CANDTYPE_AI_HINT = 49;
    /** 撰写的购物场景（仅在卡片中） */
    public static final byte CANDTYPE_NLP_COMPOOSE_SHOP = 50;
    /**
     * 云输入占位符，直接出现在候选条，点击无效果
     */
    public static final byte CANDTYPE_CLOUD_PLACEHOLDER = 51;
    /**
     * 云输入结果直接出现在候选条
     */
    public static final byte CANDTYPE_CLOUD_INLINE = 52;

    /**
     * 次cand通知，由端上设置，内核在次cand最高优展示,内容和逻辑端上控制
     */
    public static final byte CANDTYPE_AI_COMMON_HIGTEST_PRIORITY = 54;

    /**
     * Ai撰写-发布模型
     */
    public static final byte CANDTYPE_NLP_COMPOOSE_POST = 55;

    /**
     * 人名模式通知提示
     */
    public static final byte CANDTYPE_NAME_MODE = 56;
    /**
     * emoji倍数上屏
     */
    public static final byte CANDTYPE_EMOJI_LIAN_MULTI = 57;
    /**
     * 次cand上花漾文、特技字 收键盘开关
     */
    public static final byte CANDTYPE_AI_KEYBOARD_CLOSE_SWTICH = 58;
    /**
     * 计算器超长提示
     */
    public static final byte CANDTYPE_CALC_MAX_LENGTH = 59;
    /**
     * AI 智能联想撤销提示
     */
    public static final byte CANDTYPE_CANCEL_AICAND_INSERT = 64;
    /**
     * AI智能联想小模型
     */
    public static final byte CANDTYPE_NLP_MMODEL = 65;
    /**
     * 小红书正文提示『一键生成👉』
     */
    public static final byte CANDTYPE_AICAND_CONTENT_HINT = 66;
    /**
     * 高情商意图模型
     */
    public static final byte CANDTYPE_AICAND_INTENTION = 67;
    /**
     * 次 Cand Idle 状态高情商提示
     */
    public static final byte CANDTYPE_AICAND_HIGHEQ_NOTIFY = 68;

    /**
     * 最近常用的生僻字
     */
    public static final byte CANDTYPE_RECENT_RARE_ZI = 69;

    /**
     * 通用触发词
     */
    public static final byte CANDTYPE_COMMON_TRIGGER_WORD = 72;

    /**
     * 地图触发词
     */
    public static final byte CANDTYPE_MAP_TRIGGER_WORD = 73;

    /**
     * 云端触发词
     */
    public static final byte CANDTYPE_CLOUD_TRIGGER_WORDS = 74;
    /**
     * 云端意图(OEM)
     */
    public static final byte CANDTYPE_CLOUD_INTENTION = 75;
    /**
     * 常用短语功能
     */
    public static final byte CANDTYPE_COMMON_QUOTE = 76;
    /**
     * 智能表单功能 - 个人化信息联想
     */
    public static final byte CANDTYPE_USRINFO_LIAN = 77;

    // /////////////////////云服务类型区域////////////////////////
    public static final int SERVICE_TYPE_NONE = 0; // 无类型
    public static final int SERVICE_TYPE_HOLDER = 1; // 留空占位
    public static final int SERVICE_TYPE_CANDIDATE = 2; // candidate type 候选类型，此类型会让正常的云输入结果填充
    public static final int SERVICE_TYPE_TEXT = 3; // 普通文本（文字彩蛋）
    public static final int SERVICE_TYPE_MOVIE = 4; // 电影资源
    public static final int SERVICE_TYPE_SMILIES = 5; // 表情符号
    public static final int SERVICE_TYPE_EMOTICONS = 6; // 颜文字
    public static final int SERVICE_TYPE_IMAGE = 7; // 图片
    public static final int SERVICE_TYPE_MAGIC_TEXT = 8; // ios特技字体
    public static final int SERVICE_TYPE_SUG = 9; // sug
    /** 自定义sug */
    public static final int SERVICE_TYPE_DIY_SUG = 10;
    /** Sug广告 */
    public static final int SERVICE_TYPE_SUG_AD = 11;
    /** 网盟Sug广告 */
    public static final int SERVICE_TYPE_SUG_AD_WANGMENG_APP = 12;

    public static final int SERVICE_TYPE_AI_REPLY = 101; // 智能回复
    public static final int SERVICE_TYPE_AI_INTENT = 102; // 智能回复-意图理解
    public static final int SERVICE_TYPE_AI_EMOJI = 103; // 智能回复-emoji表情
    public static final int SERVICE_TYPE_AI_QUERY_KEY = 104; // 智能回复-问题
    public static final int SERVICE_TYPE_JSON = 201; // JSON类型数据：唯一结果等


    public static final int CANDFLAG_NONE = 0;          // 无角标
    public static final int CANDFLAG_CONTACT = 0x1;     // 联系人角标
    public static final int CANDFLAG_CLOUD_CACHE = 0x2; // 云缓存角标
    public static final int CANDFLAG_CLOUD = 0x4; // 云角标
    public static final int CANDFLAG_TRIANGLE = 0x8;  // 三角形角标(以前的逐词和整句)
    public static final int CANDFLAG_CORRECT = 0x10; // 读音纠错
    public static final int CANDFLAG_HW = 1 << 5; // 手写
    public static final int CANDFLAG_UNDER_LINE = 1 << 6; // 下划线
    public static final int CANDFLAG_CLOUD_CACHE_REPLACE = 1 << 7; // 云缓存替换首选
    public static final int CANDFLAG_NNRANK = 1 << 8; // nnrank重排結果
    /**
     * 纠错候选标记
     */
    public static final int CANDFLAG_IEC = 1 << 9;
    /**
     * 本地逐词结果
     */
    public static final int CANDFLAG_LOCAL_WORDBYWORD_PRED = 1 << 10;
    /**
     * cloud_inline云角标
     */
    public static final int CANDFLAG_CLOUD_INLINE = 1 << 11;
    /**
     * 文心模型结果
     */
    public static final int CANDFLAG_CLOUD_WENXIN = 1 << 12;
    /**
     * 云端ngram
     */
    public static final int CANDFLAG_CLOUD_NGRAM = 1 << 13;
    /**
     * 普通kv
     */
    public static final int CANDFLAG_CLOUD_NORMAL_KV = 1 << 14;
    /**
     * 热词kv
     */
    public static final int CANDFLAG_CLOUD_HOT_KV = 1 << 15;
    /**
     * 云逐词
     */
    public static final int CANDFLAG_CLOUD_WORDBYWORD_PRED = 1 << 16;
    /**
     * 云预测词（向后预测若干个输入码）
     */
    public static final int CANDFLAG_CLOUD_PREDICT = 1 << 17;
    /**
     * 移动端短句预测（例如输入：kanzhek，次cand直接出：看着看着）
     */
    public static final int CANDFLAG_CLOUD_ZJ_SHORT_PREDICT = 1 << 18;
    /**
     * 云词中的文心结果被本地词去重，该标志打在本地词头上
     */
    public static final int CANDFLAG_WENXIN_DUP_REMOVED = 1 << 19;
    /**
     * 非文心云词被本地去重，该标志打在本地词头上
     */
    public static final int CANDFLAG_NONWENXIN_DUP_REMOVED = 1 << 20;
    /**
     * 云结果带有替换标签（有的云结果不一定能真正替换本地词）
     */
    public static final int CANDFLAG_CLOUD_WITH_REPLACE_TAG = 1 << 21;
    /**
     * 云端场景化kv
     */
    public static final int CANDFLAG_CLOUD_SCENE_KV = 1 << 22;
    /**
     * 本地一元整句
     */
    public static final int CANDFLAG_ONE_GRAM_ZJ = 1 << 23;
    /**
     * 自造词
     */
    public static final int CANDFLAG_USR = 1 << 24;

    /**
     * 通用触发词
     */
    public static final int CANDFLAG_COMMON_TRIGGER = 1 << 25;

    /**
     * 地图触发词
     */
    public static final int CANDFLAG_MAP_TRIGGER = 1 << 26;


    /**
     * 灵感语录-常用短语
     */
    public static final int CANDFLAG_COMMON_QUOTE = 1 << 27;

    /**
     * 智能表单-个人化信息联想
     */
    public static final int CANDFLAG_USRINFO_LIAN = 1 << 28;

    private int mCandType = 0;
    private int mServiceType = 0;

    private Rect mImgRect = new Rect(0, 0, 0, 0);

    private String mUni = null;
    private String mImgUrl = null;
    private String mServiceExtra = null;

    /**
     * 目前通用触发词使用：主 cand 图标是否刷色
     */
    private boolean isNeedLinkIconColor = false;

    /**
     * 目前通用触发词使用：触发词配置 id
     */
    private long triggerItemId = 0;

    /**
     * 云端触发词命中的tab id
     */
    private int cloudTriggerTabId = 0;

    /**
     * 云端触发词 show type
     */
    private int cloudTriggerShowType = 0;

    /**
     * 云端触发词的触发词
     */
    private String cloudTriggerWord = "";

    /**
     * 通用触发词是否属于云端意图。>=1则属于
     */
    private int triggerBelongType = 0;

    /**
     * 用于客户端 云端触发词类型，根据意图绘图等功能
     */
    private int cloudTriggerIntentId = -1;

    /** 候选项标记 */
    private int mFlag = CANDFLAG_NONE;

    /**
     * 候选词是否是联想词
     */
    private boolean mIsLian;

    /**
     * 是否可以打开sug卡片
     */
    private boolean mIsContainsSugCard;

    /**
     * 整句预测match的长度
     */
    private int mBlueLen;
    /**
     * 唯一结果等的服务信息
     */
    private String mServiceContent;
    /**
     * cand对应的拼音
     */
    private String mPinyin;
    /**
     * 纠错拼音
     */
    private CorrectPinyinInfo[] mCorrectPinyin;

    /**
     * AI助聊中纠错信息
     */
    private AICorrectInfo[] mAICorrectInfo;

    /**
     * 云输入uid
     */
    private String mUid;

    /**
     * 次cand活动入口的图片的url
     */
    private String mCloudImageUrl = null;

    /**
     * 次cand活动入口的图片的MD5
     */
    private String mCloudImageMD5 = null;

    /**
     * 活动入口点击行为的类型
     */
    private String mCloudResourceClickType = null;

    /**
     * 活动入口点击行为的具体参数
     */
    private String mCloudResourceClickInfo = null;

    /**
     * 活动入口的文本
     * 对于卡片上的类型，文本和{@link IptCoreCandInfo#uni()}不一致。
     * 对于次cand的类型，文本和{@link IptCoreCandInfo#uni()}一致
     */
    private String mCloudText = null;

    /**
     * emojiQuery信息
     */
    private String mEmojiQueryText = null;

    /**
     * 正负能量
     */
    private int mEnergyMode = 0;

    /**
     * 次cand活动入口的样式
     */
    private int mCloudStyle = 0;

    /**
     * 来源信息
     */
    private int mComposeSrc = 0;

    /**
     * AI助聊 Rap的类型
     */
    private int mRapType = ImeCoreConsts.AIFunChatRapType.AI_FUNCHAT_RAP_NONE;

    /**
     * 意图类型
     */
    private int mIntentStyle = 0;

    /**
     * 意图模型来源
     */
    private int mIntentionSrc = 0;

    /**
     * 通用触发词的样式
     */
    private CommonTriggerWordStyle commonTriggerWordStyle;

    /**
     * 主线使用：顶层意图
     */
    private IptIntentItem.TopLevelIntention mTopLevelIntention;

    /**
     * 主线使用：二级意图
     */
    private IptIntentItem.SecondLevelIntention mSecondLevelIntention;

    /**
     * 主线使用: 意图请求id，用于数据回传
     */
    private String mIntentionRequestId;

    /**
     * sug广告样式（除了广告文案外，所有广告元素均用SugAdStyle表现）
     */
    private SugAdStyle mSugAdStyle;

    /**
     * 网盟Sug广告info
     */
    private WmSugAdInfo wmSugAdInfo;

    public IptCoreCandInfo() {

    }

    public int candType() {
        return mCandType;
    }

    public int serviceType() {
        return mServiceType;
    }

    /**
     * 获取候选词标记
     */
    public int flag() {
        return mFlag;
    }

    public String uni() {
        return mUni;
    }

    public String serviceExtra() {
        return mServiceExtra;
    }

    public String imgUrl() {
        return mImgUrl;
    }

    public boolean isNeedLinkIconColor() {
        return isNeedLinkIconColor;
    }

    public long getTriggerItemId() {
        return triggerItemId;
    }

    public int getCloudTriggerTabId() {
        return cloudTriggerTabId;
    }

    public int getCloudTriggerShowType() {
        return cloudTriggerShowType;
    }

    public Rect imgRect() {
        return mImgRect;
    }

    public boolean isLian() {
        return mIsLian;
    }

    public boolean isContainsSugCard() {
        return mIsContainsSugCard;
    }

    public int blueLen() {
        return mBlueLen;
    }

    public String serviceContent() {
        return mServiceContent;
    }

    public String pinyin() {
        return mPinyin;
    }

    public CorrectPinyinInfo[] correctPinyin() {
        return mCorrectPinyin;
    }

    public String uid() {
        return mUid;
    }

    @Keep
    public void setData(int candType, int serviceType, int flag, int left,
                        int top, int right, int bottom, int isLian, int isContainsSugCard,
                        int blueLen, int rapType, int energyType, int composeSrc, int cloudStyle,
                        int intentStyle, int intentionSrc,  int intentTopLevel, int intentSecondLevel) {
        mCandType = candType;
        mServiceType = serviceType;
        mFlag = flag;
        mImgRect.left = left;
        mImgRect.top = top;
        mImgRect.right = right;
        mImgRect.bottom = bottom;
        mIsLian = isLian > 0;
        mIsContainsSugCard = isContainsSugCard > 0;
        mBlueLen = blueLen;
        mEnergyMode = energyType;
        mCloudStyle = cloudStyle;
        mComposeSrc = composeSrc;
        mRapType = rapType;
        mIntentStyle = intentStyle;
        mIntentionSrc = intentionSrc;
        mTopLevelIntention = IptIntentItem.TopLevelIntention.fromInt(intentTopLevel);
        mSecondLevelIntention = IptIntentItem.SecondLevelIntention.fromInt(intentSecondLevel);
    }


    @Keep
    public void setData(String[] data) {
        mUni = data[0];
        mImgUrl = data[1];
        mServiceContent = data[2];
        mPinyin = data[3];
        mCloudImageMD5 = data[4];
        mCloudImageUrl = data[5];
        mCloudResourceClickType = data[6];
        mCloudResourceClickInfo = data[7];
        mCloudText = data[8];
        mEmojiQueryText = data[9];
        mServiceExtra = data[10];
    }

    @Keep
    public void setData(boolean linkIconColor) {
        isNeedLinkIconColor = linkIconColor;
    }

    @Keep
    public void setData(long itemId) {
        triggerItemId = itemId;
    }

    @Keep
    public void setCloudTriggerTabId(int tabId) {
        cloudTriggerTabId = tabId;
    }

    @Keep
    public void setCloudTriggerShowType(int type) {
        cloudTriggerShowType = type;
    }


    @Keep
    public void setCorrectPinyin(int cnt, int[] position, String[] pinyin) {
        if (cnt <= 0 || position == null || pinyin == null
                || position.length < cnt || pinyin.length < cnt) {
            return;
        }
        mCorrectPinyin = new CorrectPinyinInfo[cnt];
        for (int i = 0; i < cnt; i++) {
            mCorrectPinyin[i] = new CorrectPinyinInfo(position[i], pinyin[i]);
        }
    }

    @Keep
    public void setAICorrectInfo(int cnt, int[] beginIdx, int[] endIdx, String[] originText, String[] correctText) {
        if (cnt <= 0 || beginIdx == null || beginIdx.length < cnt
                || endIdx == null || endIdx.length < cnt
                || originText == null || originText.length < cnt
                || correctText == null || correctText.length < cnt) {
            mAICorrectInfo = null;
            return;
        }

        mAICorrectInfo = new AICorrectInfo[cnt];
        for (int i = 0; i < cnt; ++i) {
            mAICorrectInfo[i] = new AICorrectInfo(beginIdx[i], endIdx[i], originText[i], correctText[i]);
        }
    }

    @Keep
    public void setSugAdStyle(int adMark, int bgColor, int tipType, String tipImgUrl, String tipText,
                              int tipTextColor, int tipBgColor, String tipIconUrl, int tipIconColor) {
        // 内核设置的颜色为 RGB ，这里增加透明度（不透明）
        if (mSugAdStyle == null) {
            mSugAdStyle = new SugAdStyle();
            mSugAdStyle.bgImg = mImgUrl;
        }
        mSugAdStyle.hasAdTag = adMark > 0;
        mSugAdStyle.bgColor = ColorUtils.rgba2argb(bgColor);
        mSugAdStyle.setTipStyle(tipType, tipImgUrl, tipText, ColorUtils.rgba2argb(tipTextColor),
                ColorUtils.rgba2argb(tipBgColor), tipIconUrl, ColorUtils.rgba2argb(tipIconColor));
    }

    public SugAdStyle getSugAdStyle() {
        return mSugAdStyle;
    }

    @Keep
    public void setUid(byte[] uid) {
        if (uid != null && uid.length > 0) {
            try {
                mUid = new String(uid);
            } catch (Exception e) {
                // do nothing
            }
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        IptCoreCandInfo candInfo = (IptCoreCandInfo) o;

        if (mCandType != candInfo.mCandType) {
            return false;
        }
        if (mServiceType != candInfo.mServiceType) {
            return false;
        }
        if (mFlag != candInfo.mFlag) {
            return false;
        }
        return mUni != null ? mUni.equals(candInfo.mUni) : candInfo.mUni == null;
    }

    @Override
    public int hashCode() {
        int result = mCandType;
        result = 31 * result + mServiceType;
        result = 31 * result + (mUni != null ? mUni.hashCode() : 0);
        result = 31 * result + mFlag;
        return result;
    }

    @Override
    public String toString() {
        return "IptCoreCandInfo{" + "mCandType=" + mCandType + ", mCandFlag=" + mFlag
                + ", mServiceType=" + mServiceType + ", mUni='" + mUni + '\'' + '}';
    }

    /**
     * AI助聊中，纠错信息的个数
     */
    public int getAICorrectCnt() {
        return mAICorrectInfo == null ? 0 : mAICorrectInfo.length;
    }

    /**
     * AI助聊中，获得每个纠错的信息
     */
    public AICorrectInfo getAICorrectAt(int idx) {
        return mAICorrectInfo[idx];
    }

    public String cloudImageUrl() {
        return mCloudImageUrl;
    }

    public String cloudImageMD5() {
        return mCloudImageMD5;
    }


    public int cloudStyle() {
        return mCloudStyle;
    }

    public String getCloudResourceClickType() {
        return mCloudResourceClickType;
    }

    public String getCloudResourceClickInfo() {
        return mCloudResourceClickInfo;
    }

    public String getCloudText() {
        return mCloudText;
    }

    public String getEmojiQueryTextText() {
        return mEmojiQueryText;
    }

    /**
     * 撰写的能量，{@link ImeCoreConsts.AIEnergyType}
     */
    public int energyMode() {
        return mEnergyMode;
    }

    /**
     * NLP的来源信息，{@link ImeCoreConsts.ComposeSrc}
     */
    public int composeSrc() {
        return mComposeSrc;
    }

    /**
     * rap的类型，{@link ImeCoreConsts.AIFunChatRapType}
     */
    public int getRapType() {
        return mRapType;
    }

    /**
     * 获取意图类型
     * @return
     */
    public int getIntentStyle() {
        return mIntentStyle;
    }

    /**
     * 获取意图模型来源
     * @return
     */
    public int getIntentionSrc() {
        return mIntentionSrc;
    }

    public String getCloudTriggerWord() {
        return cloudTriggerWord;
    }

    @Keep
    public void setCloudTriggerWord(String triggerWord) {
        this.cloudTriggerWord = triggerWord;
    }

    @Keep
    public void setTriggerBelongType(int triggerBelongType) {
        this.triggerBelongType = triggerBelongType;
    }

    public IptIntentItem.TopLevelIntention getTriggerBelongType() {
        return IptIntentItem.TopLevelIntention.fromInt(triggerBelongType);
    }

    public int getCloudTriggerIntentId() {
        return cloudTriggerIntentId;
    }

    @Keep
    public void setCloudTriggerIntentId(int intentId) {
        this.cloudTriggerIntentId = intentId;
    }

    public IptIntentItem.TopLevelIntention getTopLevelIntention() {
        return mTopLevelIntention;
    }

    public IptIntentItem.SecondLevelIntention getSecondLevelIntention() {
        return mSecondLevelIntention;
    }

    public String getIntentionRequestId() {
        return mIntentionRequestId;
    }

    @Keep
    public void setIntentionRequestId(String intentionRequestId) {
        this.mIntentionRequestId = intentionRequestId;
    }

    /**
     * 通用触发词样式
     */
    @Keep
    public void setCommonTriggerWordStyle(String bgImg, String bgClr1, String bgClr2, String wordClr,
                                          int bgStyle, int wordStyle, int showAd, int triggerType, String triggerWord) {
        int bgColor1 = ColorUtils.coreColorStr2ColorInt(bgClr1);
        int bgColor2 = ColorUtils.coreColorStr2ColorInt(bgClr2);
        int wordColor = ColorUtils.coreColorStr2ColorInt(wordClr);
        commonTriggerWordStyle = new CommonTriggerWordStyle(bgImg, bgColor1, bgColor2, wordColor,
                bgStyle, wordStyle, showAd > 0, triggerType, triggerWord);
    }
    public CommonTriggerWordStyle getCommonTriggerWordStyle() {
        return commonTriggerWordStyle;
    }

    public static class CommonTriggerWordStyle {
        /**
         * 背景图片
         */
        public final String bgImg;
        /**
         * 背景颜色1
         */
        public final int bgColor1;
        /**
         * 背景颜色2
         */
        public final int bgColor2;
        /**
         * 文字颜色
         */
        public final int wordColor;

        /**
         * 背景样式
         */
        public final int bgStyle;
        /**
         * 文字样式
         */
        public final int wordStyle;
        /**
         * 是否显示广告
         */
        public final boolean showAd;
        /**
         * 触发词类型
         */
        public final int triggerType;

        /**
         * 触发词
         */
        public final String triggerWord;

        /**
         * 触发词类型
         */
        public static final int TRIGGER_TYPE_NONE = -1;
        /**
         * 候选触发
         */
        public static final int TRIGGER_TYPE_CAND = 0;
        /**
         * 联想触发
         */
        public static final int TRIGGER_TYPE_LX = 1;

        /**
         * 无背景
         */
        public static final int BG_STYLE_NONE = 0;

        /**
         * 背景为图片
         */
        public static final int BG_STYLE_IMG = 1;

        /**
         * 背景为颜色
         */
        public static final int BG_STYLE_COLOR = 2;

        /**
         * 文字样式为空
         */
        public static final int WORD_STYLE_NONE = 0;

        /**
         * 文字样式为颜色
         */
        public static final int WORD_STYLE_COLOR = 1;

        public CommonTriggerWordStyle(String bgImg, int bgColor1, int bgColor2, int wordColor, int bgStyle,
                                      int wordStyle, boolean showAd, int triggerType, String triggerWord) {
            this.bgImg = bgImg;
            this.bgColor1 = bgColor1;
            this.bgColor2 = bgColor2;
            this.wordColor = wordColor;
            this.bgStyle = bgStyle;
            this.wordStyle = wordStyle;
            this.showAd = showAd;
            this.triggerType = triggerType;
            this.triggerWord = triggerWord;
        }

        @Override
        public final CommonTriggerWordStyle clone() {
            return new CommonTriggerWordStyle(
                    this.bgImg,
                    this.bgColor1,
                    this.bgColor2,
                    this.wordColor,
                    this.bgStyle,
                    this.wordStyle,
                    this.showAd,
                    this.triggerType,
                    this.triggerWord
            );
        }

        @Override
        public String toString() {
            return "CommonTriggerWordStyle{" +
                    "bgImg='" + bgImg + '\'' +
                    ", bgColor1=0x" + Integer.toHexString(bgColor1) +
                    ", bgColor2=0x" + Integer.toHexString(bgColor2) +
                    ", wordColor=0x" + Integer.toHexString(wordColor) +
                    ", bgStyle=" + bgStyle +
                    ", wordStyle=" + wordStyle +
                    ", showAd=" + showAd +
                    ", triggerWordType=" + triggerType +
                    ", triggerWord='" + triggerWord + '\'' +
                    '}';
        }
    }

    /**
     * 纠错的拼音封装
     */
    public static class CorrectPinyinInfo {
        /**
         * 纠错拼音在cand中的文字的位置
         */
        public final int position;
        /**
         * 纠错的拼音
         */
        public final String pinyin;

        CorrectPinyinInfo(int position, String pinyin) {
            this.position = position;
            this.pinyin = pinyin;
        }
    }

    /**
     * AI助聊中，纠错信息的封装
     */
    public static class AICorrectInfo {
        /** 纠错在原始文本中的起始位置（含） */
        public final int beginIdx;

        /** 纠错在原始文本中的结束位置（含） */
        public final int endIdx;

        /** 被纠错词对应的原始文本 */
        public final String originText;

        /** 需要把被修改词替换掉的词，如果为空，则表示把被纠错词删除 */
        public final String correctText;

        public AICorrectInfo(int beginIdx, int endIdx, String originText, String correctText) {
            this.beginIdx = beginIdx;
            this.endIdx = endIdx;
            this.originText = originText;
            this.correctText = correctText;
        }

        @Override
        public String toString() {
            return "AICorrectInfo{" +
                    "beginIdx=" + beginIdx +
                    ", endIdx=" + endIdx +
                    ", originText='" + originText + '\'' +
                    ", correctText='" + correctText + '\'' +
                    '}';
        }
    }

    /** sug 广告 */

    /**
     * 无图标
     */
    public static final int SUG_AD_ICON_TYPE_NONE = 0;
    /**
     * 文字+背景+icon
     */
    public static final int SUG_AD_ICON_TYPE_ICON_WENZI = 1;
    /**
     * 纯图片
     */
    public static final int SUG_AD_ICON_TYPE_ICON_IMAGE = 2;

    /**
     * Sug广告的样式
     */
    public static class SugAdStyle {
        /**
         * sug 广告全局id
         */
        public String globalId;
        /**
         * sug广告背景颜色
         */
        public int bgColor;

        /**
         * sug广告背景图片
         */
        public String bgImg;

        /**
         * 是否有广告标签
         */
        public boolean hasAdTag = true;
        /**
         * 是否有广告标签
         */
        public int adActionType;

        /**
         * sug广告左侧小tip的类型
         */
        public int tipType;

        /**
         * 小tip的类型为纯图片时的URL
         */
        public String tipImgUrl;

        /**
         * 小tip的类型为文字类型时的文字
         */
        public String tipText;

        /**
         * 小tip的类型为文字类型时的文字颜色
         */
        public int tipTextColor;
        /**
         * 小tip的类型为文字类型时的小tip背景颜色
         */
        public int tipBgColor;

        /**
         * 小tip的类型为文字类型时的小图标的url
         */
        public String tipIconUrl;

        /**
         * 小tip的类型为文字类型时的小图标的刷色
         */
        public int tipIconColor;

        /**
         * 设置Tip（小Icon的样式）
         */
        public void setTipStyle(int tipType, String tipImgUrl, String tipText,
                          int tipTextColor, int tipBgColor, String tipIconUrl, int tipIconColor) {
            this.tipType = tipType;
            this.tipImgUrl = tipImgUrl;
            this.tipText = tipText;
            this.tipTextColor = tipTextColor;
            this.tipBgColor = tipBgColor;
            this.tipIconUrl = tipIconUrl;
            this.tipIconColor = tipIconColor;
        }

        @Override
        public String toString() {
            return "SugAdStyle{" +
                    "id='" + globalId + '\'' +
                    ", bgColor=" + formatColor(bgColor) +
                    ", bgImg='" + bgImg + '\'' +
                    ", adTag=" + hasAdTag +
                    ", action=" + adActionType +
                    ", tipType=" + tipType +
                    ", tipImg='" + tipImgUrl + '\'' +
                    ", tipText='" + tipText + '\'' +
                    ", tipTextColor=" + formatColor(tipTextColor) +
                    ", tipBgColor=" + formatColor(tipBgColor) +
                    ", tipIcon='" + tipIconUrl + '\'' +
                    ", tipIconColor=" + formatColor(tipIconColor) +
                    '}';
        }

        private static String formatColor(int c) {
            return "#" + Integer.toHexString(c) + "|" + Color.red(c) + "," + Color.green(c) + "," + Color.blue(c) +
                    "," + (Color.alpha(c) / 256f);
        }
    }

    @Keep
    public void setWmSugAdInfo(String placeId, String appPkg,
                               boolean allowZhitou, boolean allowChannel, boolean allowAct,
                               boolean isLaxin, boolean isAct, boolean showAd, boolean isFromWm, boolean isFromCsj) {
        wmSugAdInfo = new WmSugAdInfo(placeId, appPkg, allowZhitou, allowChannel, allowAct, isLaxin,
                isAct, showAd, isFromWm, isFromCsj);
    }

    /**
     * 获取网盟sug广告信息
     */
    public WmSugAdInfo getWmSugAdInfo() {
        return wmSugAdInfo;
    }

    /**
     * 网盟sug广告信息
     */
    public static class WmSugAdInfo {
        /**
         * 广告位ID
         */
        public final String placeId;
        /**
         * app包名
         */
        public final String appPackage;
        /**
         * 网盟SUG是否允许直投包
         */
        public final boolean allowZhitou;

        /**
         * 网盟SUG是否允许渠道包
         */
        public final boolean allowChannel;

        /**
         * 网盟SUG是否允许促活
         */
        public final boolean allowAct;

        /**
         * 判断该结果广告是否展示为拉新
         */
        public final boolean isLaxin;

        /**
         * 判断该结果广告是否展示为促活
         */
        public final boolean isAct;
        /**
         * 是否展示为广告
         */
        public final boolean showAd;

        /**
         * 是否为网盟广告
         */
        public final boolean isFromWm;

        /**
         * 是否为穿山甲广告
         */
        public final boolean isFromCsj;

        /**
         * query词（如果是SUG的时候可能为空，因为sug不会这个字段赋值，直接用的sug词）
         */
        @Nullable
        public String query;

        /**
         * 广告是否加载完成
         */
        public boolean isAdLoaded = false;

        public WmSugAdInfo(String placeId, String appPackage,
                           boolean allowZhitou, boolean allowChannel, boolean allowAct,
                           boolean isLaxin, boolean isAct, boolean showAd, boolean isFromWm, boolean isFromCsj) {
            this.placeId = placeId;
            this.appPackage = appPackage;
            this.allowZhitou = allowZhitou;
            this.allowChannel = allowChannel;
            this.allowAct = allowAct;
            this.isLaxin = isLaxin;
            this.isAct = isAct;
            this.showAd = showAd;
            this.isFromWm = isFromWm;
            this.isFromCsj = isFromCsj;
        }

        @Override
        public String toString() {
            return "WmSugAdInfo{" +
                    "placeId='" + placeId + '\'' +
                    ", allowZhitou=" + allowZhitou +
                    ", allowChannel=" + allowChannel +
                    ", allowAct=" + allowAct +
                    ", isLaxin=" + isLaxin +
                    ", isAct=" + isAct +
                    ", showAd=" + showAd +
                    ", isAdLoaded=" + isAdLoaded +
                    '}';
        }
    }

}
