/*
 * Copyright (C) 2017 Baidu, Inc. All Rights Reserved.
 */
package com.baidu.iptcore.info;

import android.support.annotation.Keep;

import com.baidu.annotation.CoreThread;
import com.baidu.iptcore.util.PrintUtil;

import java.util.Arrays;
/**
 * 内核DutyInfo
 */
@Keep
public class IptCoreDutyInfo {

    /** 无行为 */
    public static final byte ACTTYPE_NONE = 0;
    /** 上屏 */
    public static final byte ACTTYPE_INSERT = 1;
    /** 删除 */
    public static final byte ACTTYPE_BACK = 2;
    /** 跳转链接 */
    public static final byte ACTTYPE_URL = 3;
    /** sug 直接上屏(单独列出来) */
    public static final byte ACTTYPE_SUG_INSERT = 5;
    /** sug当前app跳转链接 */
    public static final byte ACTTYPE_SUG_CUR_APP_LINK = 6;
    /** sug当前app搜索 */
    public static final byte ACTTYPE_SUG_CUR_APP_SEARCH = 7;
    /** sug第三方app跳转链接 */
    public static final byte ACTTYPE_SUG_THIRDPARTY_APP_LINK = 8;
    /** sug第三方app搜索 */
    public static final byte ACTTYPE_SUG_THIRDPARTY_APP_SEARCH = 9;
    /** 下载 */
    public static final byte ACTTYPE_DOWNLOAD = 10;
    /** sug  特殊动作,sug里边加广告 */
    public static final byte ACTTYPE_SUG_CUSTOM = 11;
    /** 地球键 */
    public static final byte ACTTYPE_EARTH = 12;
    /** 上翻键 */
    public static final byte ACTTYPE_UP = 13;
    /** 下翻键 */
    public static final byte ACTTYPE_DOWN = 14;
    /** 上屏后光标回退一格，用于成对符号上屏 */
    public static final byte ACTTYPE_INSERT_CURSOR_BACKWARD = 15;
    /** 符号列表的自定义 */
    public static final byte ACTTYPE_LIST_DEFINE = 16;
    /** 语音按键 */
    public static final byte ACTTYPE_VOICE = 17;
    /** 左移光标 */
    public static final byte ACTTYPE_MOVE_LEFT = 18;
    /** 右移光标 */
    public static final byte ACTTYPE_MOVE_RIGHT = 19;
    /** 插入句号 */
    public static final byte ACTTYPE_BACK_INSERT_DOT = 20;
    /** 替换预上屏文本, get_replace_range */
    public static final byte ACTTYPE_REPLACE = 21;
    /** 云输入上屏图片 */
    public static final byte ACTTYPE_SUBMIT_PICTURE = 22;
    /** 上屏文字后, 再发送一个回车 */
    public static final byte ACTTYPE_INSERT_AND_ENTER = 23;
    /** 手写半上屏, 需要和ACTTYPE_REPLACE区分是因为Android手写半上屏自己实现的, 无需响应 */
    public static final byte ACTTYPE_REPLACE_HANDWRITE = 24;
    /** 上屏换行(iOS会先插入\n\a, 再把\a删除, 以防止插入\n触发发送操作) */
    public static final byte ACTTYPE_INSERT_LINE_FEED = 25;
    /** 内核定义的ACTTYPE END */
    public static final byte ACTTYPE_CORE_END = ACTTYPE_INSERT_LINE_FEED;

    /** 半上屏 */
    public static final int ACTTYPE_COMPOSING = ACTTYPE_CORE_END + 1;
    /** 结束半上屏 */
    public static final int ACTTYPE_FINISH_COMPOSING = ACTTYPE_CORE_END + 2;
    /** 结束半上屏，并且重新半上屏 */
    public static final int ACTTYPE_FINISH_AND_COMPOSING = ACTTYPE_FINISH_COMPOSING + 2;

    /** 刷新InputShow */
    public static final long REFL_SHOW = (1L << 1);
    /** 刷新Cand区 */
    public static final long REFL_CAND = (1L << 2);
    /** 刷新List区 */
    public static final long REFL_LIST = (1L << 3);
    /** 刷新Key区 */
    public static final long REFL_KEY = (1L << 4);
    /** 刷新轨迹区 */
    public static final long REFL_TRACK = (1L << 5);
    /** 刷新sug区 */
    public static final long REFL_SUG = (1L << 6);
    /** 刷新sug卡片区 */
    public static final long REFL_SUG_CARD = (1L << 7);
    /** 刷新Ai助聊图标状态(目前只包含是否Loading的状态) */
    public static final long REFL_AI_ICON = (1L << 8);
    /** 刷新键盘布局 */
    public static final long REFL_LAYOUT = (1L << 9);
    /** 刷新TIP区域 */
    public static final long REFL_TIP = (1L << 10);
    /** 刷新CandInfo区 */
    public static final long REFL_CANDINFO = (1L << 11);
    /** 刷新contact区 */
    public static final long REFL_CONTACT = (1L << 12);
    /** 刷新服务端云输入白名单版本号 */
    public static final long REFL_SRV_CLD_WHITE_VER = (1L << 13);
    /** 刷新彩蛋区域 */
    public static final long REFL_EGG = (1L << 14);
    /** 刷新次Cand内容 */
    public static final long REFL_AI_CAND = (1L << 15);
    /** 刷新PC输入法6号位 */
    public static final long REFL_TOP_PROMOTION = (1L << 16);
    /** 刷新Ai校对, 错误文本预上屏提示信息（仅Android） */
    public static final long REFL_AI_CORRECT_INLINE = (1L << 17);
    /** 刷新Ai助理面板内容 */
    public static final long REFL_AI_PAD = (1L << 18);
    /** 刷新sug选择的区域 */
    public static final long REFL_SUG_SELECTION = (1L << 19);
    /** 刷新sug card选择的区域 */
    public static final long REFL_SUG_CARD_SELECTION = (1L << 20);
    /** 刷新预上屏范围 */
    public static final long REFL_PRE_EXTRACT_SELECTION = (1L << 21);
    /** 刷新轨迹 */
    public static final long REFL_IPTCORE_TRACE = (1L << 22);
    /** 刷新Tab（更多候选词面板中） */
    public static final long REFL_MORECAND_TAB = (1L << 23);
    /** 是否刷新超会写联想气泡，目前仅华为支持 */
    public static final long REFL_CLOUD_BUBBILE = (1L << 26);
    /** 刷新inline输入码 */
    public static final long REFL_INLINE_SHOW = (1L << 27);
    /** 刷新Ai助聊面板是否Loading的状态 */
    public static final long REFL_AI_PAD_LOADING = (1L << 28);
    /** 更新手写笔势 */
    public static final long REFL_HW_GESTURE = (1L << 29);
    /** 刷新最近生僻字 */
    public static final long REFL_RARE_CAND = (1L << 30);
    /** 刷新通用触发词 */
    public static final long REFL_COMMON_TRIGGER_WORD = (1L << 31);
    /** 输入内容是身份证号 */
    public static final long REFL_IDENTITY_NUM_BEFORE = (1L << 32);

    // ///////////////////// TIP区域 ////////////////////////
    public static final int TIP_NUM = 15;
    /**
     * 英文首字母大写
     */
    public static final int TIP_CAPITAL_FIRST = 1;
    /**
     * 英文锁定大写
     */
    public static final int TIP_CAPITAL_ALL = 1 << 1;
    /**
     * 英文联想状态
     */
    public static final int TIP_EN_ABC = 1 << 2;
    /**
     * 有输入码状态
     */
    public static final int TIP_HAS_INPUT = 1 << 3;
    /**
     * 中文更多候选字单字状态
     */
    public static final int TIP_MORE_SINGLE = 1 << 4;
    /**
     * 符号面板锁定状态
     */
    public static final int TIP_SYM_LOCK = 1 << 5;
    /**
     * 当前是否是中文联想状态
     */
    public static final int TIP_CN_LIAN = 1 << 6;
    /**
     * 当前是否是中文下的临时英文输入状态
     */
    public static final int TIP_CN_SHIFT = 1 << 7;
    /**
     * 输入框是聊天框并且当前无候选字和输入码
     */
    public static final int TIP_CHAT_NOINPUT = 1 << 8;
    /**
     * 当前是速成仓颉状态
     */
    public static final int TIP_CANGJIE_SC = 1 << 9;
    /**
     * 移动光标产生的联想
     */
    public static final int TIP_CURSOR_ASSOCIATE = (1 << 10);
    /**
     * 移动光标产生的联想-计算器联想
     */
    public static final int TIP_CURSOR_ASSOCIATE_CALC = (1 << 11);
    /**
     * PC端vf模式
     */
    public static final int TIP_PC_VF_MODE = (1 << 12);
    /**
     * 英文输入码已满
     */
    public static final int TIP_EN_INPUT_FULL = (1 << 13);
    /**
     * 英文第二词如果是最优选则通知上层刷新tip并自动candselect这个词
     */
    public static final int TIP_EN_SECOND_IS_BEST = (1 << 14);


    /**
     * 行为类别
     */
    private int mActionType = ACTTYPE_NONE;

    /**
     * 刷新标记
     */
    private long mFlashFlag = 0;

    /**
     * 内核的各种状态
     */
    private int mTipStates = 0;

    /**
     * 上屏内容的类型
     */
    private int mInsertType = IptCoreCandInfo.CANDTYPE_NONE;
    /**
     * 上屏内容
     */
    private String mInsertBuff = null;
    /**
     * URL内容
     */
    private String mUrlBuff = null;
    /**
     * SUG指令
     */
    private String mSugCmdBuff = null;
    /**
     * SUG APP指令
     */
    private String mSugCmdAppBuff = null;
    /**
     * 下载内容
     */
    private String mDownloadBuff = null;

    /**
     * 当actionType为REPLACE时，替换的光标前面的内容
     */
    private int mReplaceBefore;
    /**
     * 当actionType为REPLACE时，替换的光标后面的内容
     */
    private int mReplaceAfter;
    /**
     * 预上屏范围:光标前长度
     */
    private int mPreExtractRangeBefore;
    /**
     * 预上屏范围:光标后长度
     */
    private int mPreExtractRangeAfter;
    /**
     * 预上屏范围是否需要显示
     */
    private boolean mPreExtractShow;
    /**
     * 当actionType为REPLACE时，替换后的光标是否变化
     */
    private boolean mIsReplaceKeepCursor;

    /**
     * SUGk卡片的标题
     */
    private String mSugCardTitle;
    /**
     * SUG数据
     */
    private String mSugData;
    /**
     * 点击次cand广告后的动作类型
     */
    private String mSchemaType;
    /**
     * 点击次cand广告后的动作的info
     */
    private String mSchemaInfo;
    /**
     * 是否是外部驱动的面板切换
     */
    private boolean mIsExternalLayoutSwitch;
    /**
     * 轨迹类型
     */
    private int mTraceType;

    /**
     * list筛选类型：
     * NONE_FILTER = 0, 无筛选
     * PY_FILTER = 1, 拼音筛选
     * BH_FILTER = 2, 笔画筛选
     * N_FILTER = 3, 英文筛选
     * EMOJI_FILTER = 4, 表情筛选
     */
    private int mListFilterType;
    /**
     * 是否打开云运营活动schema
     */
    private boolean mIsOpenSchema;

    /**
     * 获取触发词是否是打开地图卡片
     */
    private boolean mIsTriggerMap;

    /*
     * 是否是刷新地图触发词的duty
     */
    private boolean mIsRefreshMapData;

    /**
     * scheme 跳转的来源
     */
    private int mSchemaSource;

    /**
     * 手写笔势
     *
     * @see com.baidu.iptcore.ImeCoreConsts.HwGestureType#TYPE_NONE
     * @see com.baidu.iptcore.ImeCoreConsts.HwGestureType#TYPE_SPLIT
     * @see com.baidu.iptcore.ImeCoreConsts.HwGestureType#TYPE_CHOOSE
     * @see com.baidu.iptcore.ImeCoreConsts.HwGestureType#TYPE_DELETE
     */
    private int mHwGesture;

    /**
     * 网盟SUG
     */
    private boolean isWmSugApp;

    /**
     * 网盟SUG是否允许直投包
     */
    private boolean isWmSugAllowZhitou;

    /**
     * 网盟SUG是否允许渠道包
     */
    private boolean isWmSugAllowChannel;

    /**
     * 网盟SUG是否允许促活
     */
    private boolean isWmSugAllowAct;

    /**
     * 网盟SUG返回跳离APP是否显示提示弹窗
     */
    private boolean isWmSugShowDialogWhenLeave;

    /**
     * 网盟SUG app包名
     */
    private String wmSugAppPackage;

    /**
     * 是否是刷新sug广告
     */
    private boolean refreshSugAd;
    /**
     * 是否是刷新app意图广告
     */
    private boolean refreshAppIntentionAd;
    /**
     * SUG 是否来自网盟
     */
    private boolean sugFromWm;
    /**
     * SUG 是否来自穿山甲
     */
    private boolean sugFromCsj;

    /**
     * 本地通用触发词的query，用于打开智能推荐卡片时的查询词
     */
    private String recommendationQuery;


    /**
     * 需要存放在dutyInfo里面的Ui参数，用于Java层(非内核层)在内核线程进行一些赋值后，传递给dutyInfo处理器
     * 典型的使用例如，上层需要识别某一个dutyInfo是来自哪个action时，就可以添加在Ui参数里面
     */
    private final UiParam mUiParam = new UiParam();
    private byte[] mPopMenu;

    /**
     * 是否是刷新云端意图的duty
     */
    private boolean mIsRefreshIntentionData;

    /**
     * 通用触发词是否属于云端意图。>=1则属于
     */
    private int triggerBelongType = 0;
    /**
     * actionKeyId 列表，最多记录20个, 按键比较快的时候，duty会丢弃，但是按键要记录下来
     * 只会在内核线程中操作，外部获取时copy出去
     */
    private static final int[] CORE_ACTION_KEY_IDS = new int[20];
    /**
     * 当前记录到的actionKey的index
     */
    private static int actionKeyIndex = 0;

    // ////////////////////////////
    public IptCoreDutyInfo() {

    }

    public int actionType() {
        return mActionType;
    }

    public long flashFlag() {
        return mFlashFlag;
    }

    public int insertType() {
        return mInsertType;
    }

    public String insertText() {
        return mInsertBuff;
    }

    public String urlBuff() {
        return mUrlBuff;
    }

    public String sugCmdBuff() {
        return mSugCmdBuff;
    }

    public String sugCmdAppBuff() {
        return mSugCmdAppBuff;
    }

    public String downloadBuff() {
        return mDownloadBuff;
    }

    public int replaceBefore() {
        return mReplaceBefore;
    }

    public int replaceAfter() {
        return mReplaceAfter;
    }

    public int preExtractRangeBefore() {
        return mPreExtractRangeBefore;
    }

    public int preExtractRangeAfter() {
        return mPreExtractRangeAfter;
    }

    public boolean isPreExtractShow() {
        return mPreExtractShow;
    }

    public boolean isReplaceKeepCursor() {
        return mIsReplaceKeepCursor;
    }

    public boolean isExternalLayoutSwitch() {
        return mIsExternalLayoutSwitch;
    }

    public int traceType() {
        return mTraceType;
    }


    /**
     * list筛选的类型，具体类型值见{@link #mListFilterType}中注释
     *
     * @return
     */
    public int listFilterType() {
        return mListFilterType;
    }

    /**
     * 是否打开云运营活动schema
     */
    public boolean isOpenSchema() {
        return mIsOpenSchema;
    }

    public boolean isRefreshIntentionData() {
        return mIsRefreshIntentionData;
    }

    /**
     * 获取触发词是否是打开地图卡片
     */
    public boolean isTriggerMap() {
        return mIsTriggerMap;
    }

    /**
     * 是否是刷新地图触发词的duty
     */
    public boolean isRefreshMapData() {
        return mIsRefreshMapData;
    }

    /**
     * 获取scheme跳转的来源
     */
    public int getSchemaSource() {
        return mSchemaSource;
    }

    public int tips() {
        return mTipStates;
    }

    public String sugCardTitle() {
        return mSugCardTitle;
    }

    public String sugData() {
        return mSugData;
    }

    public String schemaType() {
        return mSchemaType;
    }

    public String schemaInfo() {
        return mSchemaInfo;
    }

    public byte[] popMenuInfo() {
        return mPopMenu;
    }

    public int hwGesture() {
        return mHwGesture;
    }

    public boolean isWmSugApp() {
        return isWmSugApp;
    }

    public boolean isWmSugAllowZhitou() {
        return isWmSugAllowZhitou;
    }

    public boolean isWmSugAllowChannel() {
        return isWmSugAllowChannel;
    }

    public boolean isWmSugAllowAct() {
        return isWmSugAllowAct;
    }

    public boolean isWmSugShowDialogWhenLeave() {
        return isWmSugShowDialogWhenLeave;
    }

    public String getWmSugAppPackage() {
        return wmSugAppPackage;
    }

    public boolean isRefreshSugAd() {
        return refreshSugAd;
    }

    public boolean isRefreshAppIntentionAd() {
        return refreshAppIntentionAd;
    }

    public boolean isSugFromCsj() {
        return sugFromCsj;
    }

    public boolean isSugFromWm() {
        return sugFromWm;
    }

    public void setActionType(int actionType) {
        mActionType = actionType;
    }

    /**
     * 仅内核使用
     */
    @CoreThread
    public int getCoreActionKeyIdsLength() {
        return actionKeyIndex;
    }
    /**
     * 仅内核使用
     */
    @CoreThread
    public void getCoreActionKeyIds(int[] output, int pos) {
        // copy action keys 到主线程
        int size = actionKeyIndex;
        if (size > 0) {
            int len = pos + size <= output.length ? size : output.length - pos;
            System.arraycopy(CORE_ACTION_KEY_IDS, 0, output, pos, len);
        }
    }

    @CoreThread
    public void addCoreActionKeyId(int actionKeyId) {
        // 保存的数据已经超过限制了，则不保存
        if (actionKeyIndex >= CORE_ACTION_KEY_IDS.length) {
            return;
        }
        CORE_ACTION_KEY_IDS[actionKeyIndex++] = actionKeyId;
    }

    @CoreThread
    public void clearCoreActionKeyIds() {
        actionKeyIndex = 0;
        Arrays.fill(CORE_ACTION_KEY_IDS, 0);
    }

    public void setInsertText(String insertText) {
        mInsertBuff = insertText;
    }

    public void setFlashFlag(long flashFlag) {
        mFlashFlag = flashFlag;
    }

    public void setTipState(int tipState) {
        mTipStates = tipState;
    }

    public String getRecommendationQuery() {
        return recommendationQuery;
    }

    public int getTriggerBelongType() {
        return triggerBelongType;
    }


    @Keep
    public void setData(long[] data) {
        mActionType = (int) data[0];
        mFlashFlag = data[1];
        mTipStates = (int) data[2];
        mInsertType = (int) data[3];
        mReplaceBefore = (int) data[4];
        mReplaceAfter = (int) data[5];
        mPreExtractRangeBefore = (int) data[6];
        mPreExtractRangeAfter = (int) data[7];
        mPreExtractShow = data[8] > 0;
        mIsReplaceKeepCursor = (data[9] > 0);
        mIsExternalLayoutSwitch = (data[10] > 0);
        mTraceType = (int) data[11];
        mListFilterType = (int) data[12];
        mIsOpenSchema = (data[13] > 0);
        mHwGesture = (int) data[14];
        mIsTriggerMap = (data[15] > 0);
        mIsRefreshMapData = (data[16] > 0);
        mSchemaSource = (int) data[17];
        isWmSugApp = (data[18] > 0);
        isWmSugAllowZhitou = (data[19] > 0);
        isWmSugAllowChannel = (data[20] > 0);
        isWmSugAllowAct = (data[21] > 0);
        isWmSugShowDialogWhenLeave = (data[22] > 0);
        refreshSugAd = (data[23] > 0);
        sugFromWm = (data[24] > 0);
        sugFromCsj = (data[25] > 0);
        mIsRefreshIntentionData = (data[26] > 0);
        triggerBelongType = (int) data[27];
        refreshAppIntentionAd = data[28] > 0;

        // 内核回调填充dutyInfo时，表示一个新的dutyInfo实例的填充，uiParam恢复到默认
        mUiParam.reset();
    }

    @Keep
    public void setData(String[] data) {
        mInsertBuff = data[0];
        mUrlBuff = data[1];
        mSugCmdBuff = data[2];
        mSugCmdAppBuff = data[3];
        mDownloadBuff = data[4];
        mSugCardTitle = data[5];
        mSugData = data[6];
        mSchemaType = data[7];
        mSchemaInfo = data[8];
        wmSugAppPackage = data[9];
        recommendationQuery = data[10];

        // 内核回调填充dutyInfo时，表示一个新的dutyInfo实例的填充，uiParam恢复到默认
        mUiParam.reset();
    }

    @Keep
    public void setData(byte[] data) {
        mPopMenu = data;
    }

    public IptCoreDutyInfo copy() {
        IptCoreDutyInfo dutyInfoCopy = new IptCoreDutyInfo();
        dutyInfoCopy.mActionType = mActionType;
        dutyInfoCopy.mFlashFlag = mFlashFlag;
        dutyInfoCopy.mTipStates = mTipStates;
        dutyInfoCopy.mInsertType = mInsertType;
        dutyInfoCopy.mInsertBuff = mInsertBuff;
        dutyInfoCopy.mUrlBuff = mUrlBuff;
        dutyInfoCopy.mSugCmdBuff = mSugCmdBuff;
        dutyInfoCopy.mSugCmdAppBuff = mSugCmdAppBuff;
        dutyInfoCopy.mDownloadBuff = mDownloadBuff;
        dutyInfoCopy.mReplaceBefore = mReplaceBefore;
        dutyInfoCopy.mReplaceAfter = mReplaceAfter;
        dutyInfoCopy.mPreExtractRangeBefore = mPreExtractRangeBefore;
        dutyInfoCopy.mPreExtractRangeAfter = mPreExtractRangeAfter;
        dutyInfoCopy.mPreExtractShow = mPreExtractShow;
        dutyInfoCopy.mSugCardTitle = mSugCardTitle;
        dutyInfoCopy.mSugData = mSugData;
        dutyInfoCopy.mIsOpenSchema = mIsOpenSchema;
        dutyInfoCopy.mIsRefreshIntentionData = mIsRefreshIntentionData;
        dutyInfoCopy.mSchemaInfo = mSchemaInfo;
        dutyInfoCopy.mSchemaType = mSchemaType;
        dutyInfoCopy.mIsTriggerMap = mIsTriggerMap;
        dutyInfoCopy.mIsRefreshMapData = mIsRefreshMapData;
        dutyInfoCopy.mSchemaSource = mSchemaSource;
        dutyInfoCopy.mPopMenu = mPopMenu;
        dutyInfoCopy.mIsExternalLayoutSwitch = mIsExternalLayoutSwitch;
        dutyInfoCopy.mHwGesture = mHwGesture;
        dutyInfoCopy.isWmSugApp = isWmSugApp;
        dutyInfoCopy.isWmSugAllowZhitou = isWmSugAllowZhitou;
        dutyInfoCopy.isWmSugAllowChannel = isWmSugAllowChannel;
        dutyInfoCopy.isWmSugAllowAct = isWmSugAllowAct;
        dutyInfoCopy.isWmSugShowDialogWhenLeave = isWmSugShowDialogWhenLeave;
        dutyInfoCopy.wmSugAppPackage = wmSugAppPackage;
        dutyInfoCopy.refreshSugAd = refreshSugAd;
        dutyInfoCopy.refreshAppIntentionAd = refreshAppIntentionAd;
        dutyInfoCopy.sugFromWm = sugFromWm;
        dutyInfoCopy.sugFromCsj = sugFromCsj;
        dutyInfoCopy.triggerBelongType = triggerBelongType;
        dutyInfoCopy.recommendationQuery = recommendationQuery;
        dutyInfoCopy.mUiParam.copy(mUiParam);
        return dutyInfoCopy;
    }

    @Override
    public String toString() {

        return "IptCoreDutyInfo{" + "mActionType=" + PrintUtil.actionTypeToString(mActionType) + ", mFlashFlag="
                + PrintUtil.flashFlagToString(mFlashFlag)
                + ", " + "mInsertBuff='"
                + mInsertBuff + '\'' + '}';
    }

    /**
     * 设置当前duty对应的Ui刷新，是否可以延迟执行
     */
    public void setDelayUiUpdate() {
        mUiParam.needDelayUiUpdate = true;
    }

    /**
     * 当前duty对应的Ui刷新，是否可以延迟执行
     */
    public boolean needDelayUiUpdate() {
        // 多点中间的任务才可delay/drop
        // 没有actionType的任务才可以delay/drop
        // flashFlag限制在一定范围内的任务才可以delay/drop
        return mUiParam.needDelayUiUpdate
                && actionType() == IptCoreDutyInfo.ACTTYPE_NONE
                && (flashFlag() & ~(REFL_SHOW | REFL_CAND | REFL_LIST | REFL_KEY
                | REFL_AI_CAND | REFL_SUG | REFL_SUG_CARD | REFL_TIP | REFL_AI_ICON | REFL_RARE_CAND)) == 0;
    }

    public void setBatchInput(boolean batchInput) {
        mUiParam.isBatchInput = batchInput;
    }

    public boolean isBatchInput() {
        return mUiParam.isBatchInput;
    }

    /**
     * Ui参数封装，参考{@link #mUiParam}的解释
     */
    private static class UiParam {

        public boolean isBatchInput = false;
        /**
         * 当前duty对应的Ui刷新，是否可以延迟执行
         */
        private boolean needDelayUiUpdate = false;

        public void reset() {
            needDelayUiUpdate = false;
            isBatchInput = false;
        }

        public void copy(UiParam uiParam) {
            this.needDelayUiUpdate = uiParam.needDelayUiUpdate;
            this.isBatchInput = uiParam.isBatchInput;
        }
    }
}
