package com.baidu.iptcore.info;

import android.support.annotation.Keep;

/**
 * List区域信息
 * Created by <PERSON><PERSON><PERSON><PERSON> on 18/5/4.
 */
@Keep
public class IptCoreListInfo {
    
    /** list项的显示字符串 */
    private String uni;
    
    /**
     *  list项的类型。
     *  @see com.baidu.iptcore.ImeCoreConsts#BH_LIST_ITEM
     */
    private int listType;
    
    @Keep
    public void setData(String uni, int listType) {
        this.uni = uni;
        this.listType = listType;
    }

    /**
     * 获取list项显示的字符
     */
    public String uni() {
        return uni;
    }

    /**
     * 获取list项的类型
     * @return 类型。{@link com.baidu.iptcore.ImeCoreConsts#BH_LIST_ITEM}等
     */
    public int listType() {
        return listType;
    }

    @Override
    public String toString() {
        return "IptCoreListInfo{" + uni + ',' + listType + '}';
    }
}
