/*
 * Copyright (C) 2017 Baidu, Inc. All Rights Reserved.
 */
package com.baidu.iptcore.info;

import java.util.Arrays;

import android.support.annotation.Keep;
import android.support.annotation.NonNull;

import com.baidu.iptcore.ImeCoreConsts;

/**
 * 内核封装的input区域的数据结构
 */
@Keep
public class IptCoreShowInfo {

    /** 
     * input区展示的文本 
     */
    private String mUni = null;
    /**
     * 已经提交的候选字的数量
     */
    private int mHzLen = 0;
    /**
     * 已经提交的list数量
     */
    private int mListLen = 0;
    /**
     * 剩余的input区域的数量
     */
    private int mInputLen = 0;
    /**
     * 可撤回区域的起始位置
     */
    private int mPopBegan = 0;
    /**
     * 可撤回区域的终止位置
     */
    private int mPopEnd = 0;
    /**
     * Input区的光标信息
     */
    private byte[] mCursorInfo = new byte[ImeCoreConsts.MAX_SHOWINFO_LEN];
    /**
     * Input区的光标信息的有效长度
     */
    private int mCursorInfoLen = 0;
    /**
     * 光标位置。是以输入码为单位的位置，不是在input字符串中的绝对位置
     */
    private int mCursorIdx = -1;
    /**
     * 所有输入码的显示范围对应的纠错信息，长度为list_len + input_len
     */
    private byte[] mInputAutofixInfo = new byte[ImeCoreConsts.MAX_SHOWINFO_LEN];
    /**
     * showInfo的类型
     */
    private int mShowInfoType = ImeCoreConsts.SHOWINFO_TYPE_NONE;

    public IptCoreShowInfo() {

    }

    /**
     * input区需要显示的字符
     */
    public String uni() {
        return mUni;
    }

    /**
     * 已经提交的候选字的数量
     */
    public int hzLen() {
        return mHzLen;
    }

    /**
     * 已经提交的list的长度
     */
    public int listLen() {
        return mListLen;
    }

    /**
     * 剩余的input的长度
     */
    public int inputLen() {
        return mInputLen;
    }

    /**
     * 待回退的部分的起始索引
     */
    public int popBegan() {
        return mPopBegan;
    }

    /**
     * 待回退部分的终止索引
     */
    public int popEnd() {
        return mPopEnd;
    }

    /**
     * 所有输入码的显示范围对应的纠错信息
     */
    @NonNull
    public byte[] cursorInfo() {
        return mCursorInfo;
    }

    /**
     * 光标位置在input字符串中的位置信息的有效长度
     */
    public int cursorInfoLen() {
        return mCursorInfoLen;
    }

    /**
     * 光标位置。是以输入码为单位的位置，不是在input字符串中的绝对位置
     * @return 未设置可能为-1
     */
    public int cursorIdx() {
        return mCursorIdx;
    }

    /**
     * 输入码显示范围对应的纠错信息
     */
    @NonNull
    public byte[] autofixInfo() {
        return mInputAutofixInfo;
    }

    /**
     * 输入码显示范围对应的纠错信息的有效长度
     */
    public int autofixInfoLen() {
        return listLen() + inputLen();
    }

    @Keep
    @Deprecated
    public void setData(String uni, int hzLen, int listLen, int inputLen,
            int popBegan, int popEnd, int cursorInfoLen, int cursorIdx, int showInfoType) {
        mUni = uni;
        mHzLen = hzLen;
        mListLen = listLen;
        mInputLen = inputLen;
        mPopBegan = popBegan;
        mPopEnd = popEnd;
        mCursorInfoLen = cursorInfoLen;
        mCursorIdx = cursorIdx;
        mShowInfoType = showInfoType;
    }

    @Keep
    public void setData(int[] data) {
        mHzLen = data[0];
        mListLen = data[1];
        mInputLen = data[2];
        mPopBegan = data[3];
        mPopEnd = data[4];
        mCursorInfoLen = data[5];
        mCursorIdx = data[6];
        mShowInfoType = data[7];
    }

    @Keep
    public void setData(String[] data) {
        mUni = data[0];
    }

    public void reset() {
        mUni = null;
        mHzLen = 0;
        mListLen = 0;
        mInputLen = 0;
        mPopBegan = 0;
        mPopEnd = 0;
        Arrays.fill(mCursorInfo, (byte) 0);
        mCursorInfoLen = 0;
        mCursorIdx = -1;
        Arrays.fill(mInputAutofixInfo, (byte) 0);
        mShowInfoType = ImeCoreConsts.SHOWINFO_TYPE_NONE;
    }

    public void copy(IptCoreShowInfo target) {
        mUni = target.mUni;
        mHzLen = target.mHzLen;
        mListLen = target.mListLen;
        mInputLen = target.mInputLen;
        mPopBegan = target.mPopBegan;
        mPopEnd = target.mPopEnd;
        System.arraycopy(target.mCursorInfo, 0, mCursorInfo, 0, target.mCursorInfo.length);
        mCursorInfoLen = target.mCursorInfoLen;
        mCursorIdx = target.mCursorIdx;
        System.arraycopy(target.mInputAutofixInfo, 0, mInputAutofixInfo, 0, target.mInputAutofixInfo.length);
        mShowInfoType = target.mShowInfoType;
    }

    @Override
    public String toString() {
        return "IptCoreShowInfo{" +
                "mUni='" + mUni + '\'' +
                ", mHzLen=" + mHzLen +
                ", mListLen=" + mListLen +
                ", mInputLen=" + mInputLen +
                ", mPopBegan=" + mPopBegan +
                ", mPopEnd=" + mPopEnd +
                ", mCursorInfoLen=" + mCursorInfoLen +
                ", mCursorIdx=" + mCursorIdx +
                '}';
    }
}
