package com.baidu.iptcore.info;

import android.support.annotation.Keep;

/**
 * SUG穿山甲广告action
 */
@Keep
public class IptCoreSugAdAction {

    /**
     *  /// 无效
     */
    public static final int ACTION_NONE = 0;
    /**
     *  /// 促活，deeplink
     */
    public static final int ACTION_ACT = 1;
    /**
     *  /// 拉新，直投
     */
    public static final int ACTION_ZHITOU = 2;
    /**
     *  /// 拉新，渠道
     */
    public static final int ACTION_CHANNEL = 3;

    /**
     * 类型
     */
    private int type = ACTION_NONE;
    /**
     * 6要素 icon
     */
    private String url;

    @Keep
    public void setType(int type) {
        this.type = type;
    }

    @Keep
    public void setUrl(String url) {
        this.url = url;
    }

    public int getType() {
        return type;
    }

    public String getUrl() {
        return url;
    }

    @Override
    public String toString() {
        return "IptCoreSugAdAction{" +
                "type=" + type +
                ", url='" + url + '\'' +
                '}';
    }
}
