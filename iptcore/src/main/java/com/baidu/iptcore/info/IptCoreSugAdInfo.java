package com.baidu.iptcore.info;

import android.support.annotation.Keep;

/**
 * SUG穿山甲广告信息
 */
@Keep
public class IptCoreSugAdInfo {

    /**
     * 1 价格低于网盟（需要传竞价成功者的price，单位分）
     */
    public static final int REASON_PRICE_LOW = 1;
    /**
     * 2 未满足促活>直投>渠道的规则
     */
    public static final int REASON_NOT_ACT = 2;
    /**
     * 广告类型
     */
    public static final int TYPE_NONE = 0;
    /**
     * 穿山甲
     */
    public static final int TYPE_CSJ = 1;
    /**
     * 兜底数据，目前仅用在应用意图卡片 POS_APP_INTENTION_CARD
     */
    public static final int TYPE_MAX = 255;
    /**
     * 类型
     */
    private int type = TYPE_NONE;
    /**
     * 竞价
     */
    private long price = 0;

    /**
     * SUG是否允许促活
     */
    private boolean allowAct;
    /**
     * SUG是否允许直投包
     */
    private boolean allowZhitou;

    /**
     * SUG是否允许渠道包
     */
    private boolean allowChannel;

    /**
     * 名称
     */
    private String appName;
    /**
     * 6要素 icon
     */
    private String icon;
    /**
     * 6要素 版本号
     */
    private String appVersion;
    /**
     * 6要素 开发者名称
     */
    private String developerName;
    /**
     * 6要素 描述地址
     */
    private String descUrl;
    /**
     * 6要素 隐私协议地址
     */
    private String privacyPolicyUrl;
    /**
     * 6要素 权限地址
     */
    private String permissionsUrl;

    @Keep
    public void setPrice(long data) {
        price = data;
    }

    @Keep
    public void setData(int[] data) {
        type = data[0];
        allowAct = data[1] > 0;
        allowZhitou = data[2] > 0;
        allowChannel = data[3] > 0;
    }

    @Keep
    public void setData(String[] data) {
       appName = data[0];
       icon = data[1];
       appVersion = data[2];
       developerName = data[3];
       descUrl = data[4];
       privacyPolicyUrl = data[5];
       permissionsUrl = data[6];
    }

    public int getType() {
        return type;
    }

    public long getPrice() {
        return price;
    }

    public boolean isAllowAct() {
        return allowAct;
    }

    public boolean isAllowZhitou() {
        return allowZhitou;
    }

    public boolean isAllowChannel() {
        return allowChannel;
    }

    public String getAppName() {
        return appName;
    }

    public String getIcon() {
        return icon;
    }

    public String getAppVersion() {
        return appVersion;
    }

    public String getDeveloperName() {
        return developerName;
    }

    public String getDescUrl() {
        return descUrl;
    }

    public String getPrivacyPolicyUrl() {
        return privacyPolicyUrl;
    }

    public String getPermissionsUrl() {
        return permissionsUrl;
    }

    /**
     * 是否是兜底广告数据
     */
    public boolean isFallbackData() {
        return type == TYPE_MAX;
    }

    @Override
    public String toString() {
        return "IptCoreSugAdInfo{" +
                "type=" + type +
                ", price=" + price +
                ", allowAct=" + allowAct +
                ", allowZhitou=" + allowZhitou +
                ", allowChannel=" + allowChannel +
                ", appName='" + appName + '\'' +
                ", icon='" + icon + '\'' +
                ", appVersion='" + appVersion + '\'' +
                ", developerName='" + developerName + '\'' +
                ", descUrl='" + descUrl + '\'' +
                ", privacyPolicyUrl='" + privacyPolicyUrl + '\'' +
                ", permissionsUrl='" + permissionsUrl + '\'' +
                '}';
    }
}
