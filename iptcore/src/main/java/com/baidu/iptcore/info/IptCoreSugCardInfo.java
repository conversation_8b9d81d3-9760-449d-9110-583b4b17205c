/*
 * Copyright (C) 2017 Baidu, Inc. All Rights Reserved.
 */
package com.baidu.iptcore.info;

import android.support.annotation.Keep;

/**
 * SUG卡片信息
 */
@Keep
public class IptCoreSugCardInfo {
    // ///////////////////状态类型区域////////////////////////
    public static final byte STATE_NONE = 0; // 无状态
    public static final byte STATE_LOADING = 1; // 正在载入
    public static final byte STATE_LOAD_FAIL = 2; // 载入失败
    public static final byte STATE_LOAD_SUCCESS = 3; // 载入成功

    // ///////////////////卡片类型区域////////////////////////
    public static final byte TYPE_NONE = 0; // 未知
    public static final byte TYPE_APP = 1; // 应用
    public static final byte TYPE_MUSIC = 2; // 音频
    public static final byte TYPE_VIDEO = 3; // 视频
    public static final byte TYPE_BOOK = 4; // 图书
    public static final byte TYPE_NORMAL = 5; // 普通

    private int mState = STATE_NONE;
    private int mType = TYPE_NONE;
    private String mKey = null;
    private String mTitle = null;
    private String mContent1 = null;
    private String mContent2 = null;
    private String mContent3 = null;
    private String mImgUrl = null;
    private String mIconUrl = null;

    public int state() {
        return mState;
    }

    public int type() {
        return mType;
    }

    public String key() {
        return mKey;
    }

    public String title() {
        return mTitle;
    }

    public String content1() {
        return mContent1;
    }

    public String content2() {
        return mContent2;
    }

    public String content3() {
        return mContent3;
    }

    public String imgUrl() {
        return mImgUrl;
    }

    public String iconUrl() {
        return mIconUrl;
    }

    @Keep
    public void setData(int[] data) {
        mState = data[0];
        mType = data[1];
    }

    @Keep
    public void setData(String[] data) {
        mKey = data[0];
        mTitle = data[1];
        mContent1 = data[2];
        mContent2 = data[3];
        mContent3 = data[4];
        mImgUrl = data[5];
        mIconUrl = data[6];
    }

    @Override
    public String toString() {
        return "IptCoreSugCardInfo{" + "mState=" + mState + ", mType=" + mType + ", mKey='" + mKey + '\'' + ", mTitle='"
                + mTitle + '\'' + '}';
    }
}
