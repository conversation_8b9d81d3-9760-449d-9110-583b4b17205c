package com.baidu.iptcore.info;

import android.support.annotation.Keep;

/**
 * idm头信息类
 * Created by <PERSON><PERSON><PERSON><PERSON> on 18/2/11.
 */
@Keep
public class IptIdmCellInfo {
    /** cell_id编号 */
    private int cellId;
    /** server_Id编号 */
    private int serverId;
    /** 词数量 */
    private int count;
    /** 词库是否打开 */
    private boolean isOpen;
    /** 安装包类型, 0代表覆盖安装, 1代表升级安装 */
    private int dataType;
    /** 升级后版本号 */
    private int innerVer;
    /** 升级前版本号 */
    private int innerVerFrom;
    /** 版本号 */
    private int ver;
    /** 类型 */
    private int type;

    @Keep
    public void setData(int cellId, int serverId, int count, boolean isOpen, int dataType,
                        int innerVer, int innerVerFrom, int ver, int type) {
        this.cellId = cellId;
        this.serverId = serverId;
        this.count = count;
        this.isOpen = isOpen;
        this.dataType = dataType;
        this.innerVer = innerVer;
        this.innerVerFrom = innerVerFrom;
        this.ver = ver;
        this.type = type;
    }

    public int cellId() {
        return cellId;
    }

    public int serverId() {
        return serverId;
    }

    public int count() {
        return count;
    }

    public boolean isOpen() {
        return isOpen;
    }

    public int dataType() {
        return dataType;
    }

    public int innerVer() {
        return innerVer;
    }

    public int innerVerFrom() {
        return innerVerFrom;
    }

    public int ver() {
        return ver;
    }

    public int type() {
        return type;
    }

    public void setCellId(int cellId) {
        this.cellId = cellId;
    }

    public void setServerId(int serverId) {
        this.serverId = serverId;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public void setOpen(boolean open) {
        isOpen = open;
    }

    public void setDataType(int dataType) {
        this.dataType = dataType;
    }

    public void setInnerVer(int innerVer) {
        this.innerVer = innerVer;
    }

    public void setInnerVerFrom(int innerVerFrom) {
        this.innerVerFrom = innerVerFrom;
    }

    public void setVer(int ver) {
        this.ver = ver;
    }

    public void setType(int type) {
        this.type = type;
    }
}
