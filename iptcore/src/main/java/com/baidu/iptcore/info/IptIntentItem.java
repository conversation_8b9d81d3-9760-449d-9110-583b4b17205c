package com.baidu.iptcore.info;

import android.support.annotation.Keep;

@Keep
public class IptIntentItem {

    /** 一级意图的value */
    public static final String JSON_KEY_BELONG_TYPE = "key_belong_type";
    /** 触发词 */
    public static final String JSON_KEY_RECOMMENDATION_QUERY = "key_recommendation_query";

    /**
     * 意图类型
     */
    private CloudIntentionType intentionType;

    /**
     * 天气意图时间
     */
    private long timeval;

    /**
     * 天气意图的城市
     */
    private String city;

    /**
     * 日历意图：节假日
     */
    private String festival;

    /**
     * 用于进行搜索的文本
     */
    private String queryUni;

    /**
     * 主线使用：顶层意图
     */
    private TopLevelIntention topLevelIntention;
    /**
     * 主线使用：二级意图
     */
    private SecondLevelIntention secondLevelIntention;

    /**
     * 当是app意图的时候的app信息
     */
    private IptCoreCandInfo.WmSugAdInfo appInfo;

    /**
     * 当前是地图意图时候的地图信息
     */
    private IptMapTriggerWordItem mapInfo;

    @Keep
    public void setData(int type, long timeval, String city, String festival, String queryUni, int topLevel, int secondLevel) {
        this.intentionType = CloudIntentionType.fromInt(type);
        this.timeval = timeval;
        this.city = city;
        this.festival = festival;
        this.queryUni = queryUni;
        if (appInfo != null) {
            appInfo.query = queryUni;
        }
        this.topLevelIntention = TopLevelIntention.fromInt(topLevel);
        this.secondLevelIntention = SecondLevelIntention.fromInt(secondLevel);
    }

    @Keep
    public void setAppInfo(String placeId, String appPkg,
                           boolean allowZhitou, boolean allowChannel, boolean allowAct,
                           boolean isLaxin, boolean isAct, boolean showAd, boolean isFromWm, boolean isFromCsj) {
        appInfo = new IptCoreCandInfo.WmSugAdInfo(
                placeId, appPkg, allowZhitou, allowChannel, allowAct, isLaxin, isAct, showAd, isFromWm, isFromCsj
        );
        appInfo.query = queryUni;
    }

    @Keep
    public void setMapInfo(String addr, String name, String uid, String viewUrl, String shareUrl) {
        mapInfo = new IptMapTriggerWordItem();
        mapInfo.setData(addr, name, uid, viewUrl, shareUrl);
    }

    public IptCoreCandInfo.WmSugAdInfo getAppInfo() {
        return appInfo;
    }

    public IptMapTriggerWordItem getMapInfo() {
        return mapInfo;
    }

    public CloudIntentionType getIntentionType() {
        return intentionType;
    }

    public void setIntentionType(CloudIntentionType intentionType) {
        this.intentionType = intentionType;
    }

    public long getTimeval() {
        return timeval;
    }

    public void setTimeval(long timeval) {
        this.timeval = timeval;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getFestival() {
        return festival;
    }

    public void setFestival(String festival) {
        this.festival = festival;
    }

    public String getQueryUni() {
        return queryUni;
    }

    public TopLevelIntention getTopLevelIntention() {
        return topLevelIntention;
    }

    public SecondLevelIntention getSecondLevelIntention() {
        return secondLevelIntention;
    }


    @Override
    public String toString() {
        return "IptIntentItem{" +
                "intentionType=" + intentionType +
                ", timeval=" + timeval +
                ", city='" + city + '\'' +
                ", festival='" + festival + '\'' +
                ", showUnit='" + queryUni + '\'' +
                '}';
    }

    @Keep
    public enum CloudIntentionType {
        NONE(0),
        CALCULATOR(1), // 计算器
        CALENDAR(2), // 日历
        WEATHER(3),  // 天气
        CONTACT(4); //  联系人

        private int value;

        CloudIntentionType(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }

        @Keep
        public static CloudIntentionType fromInt(int value) {
            for (CloudIntentionType type : CloudIntentionType.values()) {
                if (type.value == value) {
                    return type;
                }
            }
            return CloudIntentionType.NONE;
        }
    }

    @Keep
    public enum TopLevelIntention {
        NONE(0),
        CloudIntentionFirBaiduBaike(1), // 百度百科 ///
        CloudIntentionFirLifeServices(2), // 生活服务 //
        CloudIntentionFirNovel(3), // 小说 ///
        CloudIntentionFirMovie(4),  // 电影 ///
        CloudIntentionFirMusic(5),  // 音乐 ///
        CloudIntentionFirGame(6),  // 游戏 ///
        CloudIntentionFirExpression(7),  // 表情 ///
        CloudIntentionFirApp(8);  // APP ///

        private int value;

        TopLevelIntention(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }

        @Keep
        public static TopLevelIntention fromInt(int value) {
            for (TopLevelIntention type : TopLevelIntention.values()) {
                if (type.value == value) {
                    return type;
                }
            }
            return TopLevelIntention.NONE;
        }
    }

    @Keep
    public enum SecondLevelIntention {
        NONE(0),
        CloudIntentionSecBaiduBaike(1), // 百度百科 ///(对应一级:CloudIntentionFirBaiduBaike)
        CloudIntentionSecMap(2), // 地图服务 //(对应一级:CloudIntentionFirLifeServices)
        CloudIntentionSecWeather(3), // 天气卡片 ///(对应一级:CloudIntentionFirLifeServices)
        CloudIntentionSecRate(4),  // 汇率卡片 ///(对应一级:CloudIntentionFirLifeServices)
        CloudIntentionSecLottery(5),  // 彩票卡片 ///(对应一级:CloudIntentionFirLifeServices)
        CloudIntentionSecTakeout(6),  // 外卖卡片 ///(对应一级:CloudIntentionFirLifeServices)
        CloudIntentionSecStar(7),  // 星座卡片 ///(对应一级:CloudIntentionFirLifeServices)
        CloudIntentionSecZodiacSign(8),  // 生肖卡片 ///(对应一级:CloudIntentionFirLifeServices)
        CloudIntentionSecNovel(9),  // 小说卡片 ///(对应一级:CloudIntentionFirNovel)
        CloudIntentionSecMovie(10),  // 电影卡片 ///(对应一级:CloudIntentionFirMusic)
        CloudIntentionSecMusic(11),  // 音乐卡片 ///(对应一级:CloudIntentionFirMusic)
        CloudIntentionSecGame(12),  // 游戏(待定，一期不需要) ///(对应一级:CloudIntentionFirGame)
        CloudIntentionSecExpression(13),  // 表情 ///(对应一级:CloudIntentionFirExpression)
        CloudIntentionSecApp(14);  // APP ///(对应一级:CloudIntentionFirApp)

        private int value;

        SecondLevelIntention(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }

        @Keep
        public static SecondLevelIntention fromInt(int value) {
            for (SecondLevelIntention type : SecondLevelIntention.values()) {
                if (type.value == value) {
                    return type;
                }
            }
            return SecondLevelIntention.NONE;
        }
    }

}