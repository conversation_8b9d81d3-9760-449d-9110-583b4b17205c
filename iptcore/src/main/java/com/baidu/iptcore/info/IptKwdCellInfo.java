package com.baidu.iptcore.info;

import android.support.annotation.Keep;

/**
 * 关键词头信息类
 * Created by <PERSON><PERSON><PERSON><PERSON> on 18/2/11.
 */
@Keep
public class IptKwdCellInfo {
    /** cell_id编号 */
    private int cellId;
    /** server_Id编号 */
    private int serverId;
    /** 词数量 */
    private int count;
    /** 词库是否打开 */
    private boolean isOpen;
    /** 安装包类型, 0代表覆盖安装, 1代表升级安装 */
    private int dataType;
    /** 升级后版本号 */
    private int innerVer;
    /** 升级前版本号 */
    private int innerVerFrom;
    /** 版本号1 */
    private int ver1;
    /** 版本号2 */
    private int ver2;
    /** 版本号3 */
    private int ver3;
    /** 类型1 */
    private int type1;
    /** 类型2 */
    private int type2;
    /** 类型3 */
    private int type3;
    /** 类型4 */
    private int type4;

    @Keep
    public void setData(int cellId, int serverId, int count, boolean isOpen, int dataType,
                        int innerVer, int innerVerFrom, int ver1,
                        int ver2, int ver3, int type1, int type2, int type3, int type4) {
        this.cellId = cellId;
        this.serverId = serverId;
        this.count = count;
        this.isOpen = isOpen;
        this.dataType = dataType;
        this.innerVer = innerVer;
        this.innerVerFrom = innerVerFrom;
        this.ver1 = ver1;
        this.ver2 = ver2;
        this.ver3 = ver3;
        this.type1 = type1;
        this.type2 = type2;
        this.type3 = type3;
        this.type4 = type4;
    }

    public int cellId() {
        return cellId;
    }

    public int serverId() {
        return serverId;
    }

    public int count() {
        return count;
    }

    public boolean isOpen() {
        return isOpen;
    }

    public int dataType() {
        return dataType;
    }

    public int innerVer() {
        return innerVer;
    }

    public int innerVerFrom() {
        return innerVerFrom;
    }

    public int ver1() {
        return ver1;
    }

    public int ver2() {
        return ver2;
    }

    public int ver3() {
        return ver3;
    }

    public int type1() {
        return type1;
    }

    public int type2() {
        return type2;
    }

    public int type3() {
        return type3;
    }

    public int type4() {
        return type4;
    }

    public void setCellId(int cellId) {
        this.cellId = cellId;
    }

    public void setServerId(int serverId) {
        this.serverId = serverId;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public void setOpen(boolean open) {
        isOpen = open;
    }

    public void setDataType(int dataType) {
        this.dataType = dataType;
    }

    public void setInnerVer(int innerVer) {
        this.innerVer = innerVer;
    }

    public void setInnerVerFrom(int innerVerFrom) {
        this.innerVerFrom = innerVerFrom;
    }

    public void setVer1(int ver1) {
        this.ver1 = ver1;
    }

    public void setVer2(int ver2) {
        this.ver2 = ver2;
    }

    public void setVer3(int ver3) {
        this.ver3 = ver3;
    }

    public void setType1(int type1) {
        this.type1 = type1;
    }

    public void setType2(int type2) {
        this.type2 = type2;
    }

    public void setType3(int type3) {
        this.type3 = type3;
    }

    public void setType4(int type4) {
        this.type4 = type4;
    }
}
