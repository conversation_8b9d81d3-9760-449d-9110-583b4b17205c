package com.baidu.iptcore.info;

import android.support.annotation.Keep;
@Keep
public class IptMapTriggerWordItem {

    /**
     * 地址
     */
    private String addr;

    /**
     * 名称
     */
    private String name;

    /**
     * uid
     */
    private String uid;

    /**
     * 查看地图url
     */
    private String viewUrl;

    /**
     * 分享url
     */
    private String shareUrl;

    @Keep
    public void setData(String addr, String name, String uid, String viewUrl, String shareUrl) {
        this.addr = addr;
        this.name = name;
        this.uid = uid;
        this.viewUrl = viewUrl;
        this.shareUrl = shareUrl;
    }

    public String getAddr() {
        return addr;
    }

    public String getName() {
        return name;
    }

    public String getUid() {
        return uid;
    }

    public String getViewUrl() {
        return viewUrl;
    }

    public String getShareUrl() {
        return shareUrl;
    }

    @Override
    public String toString() {
        return "IptMapTriggerWordItem{" +
                ", addr='" + addr + '\'' +
                ", name='" + name + '\'' +
                ", uid='" + uid + '\'' +
                ", viewUrl='" + viewUrl + '\'' +
                ", shareUrl='" + shareUrl +
                '}';
    }
}