package com.baidu.iptcore.info;

import android.support.annotation.Keep;

/**
 * 个性短语分组信息
 * Created by <PERSON><PERSON><PERSON><PERSON> on 18/2/2.
 */
@Keep
public class IptPhraseGroup {
    private int groupId;
    private int itemCnt;
    private boolean isEnabled;
    private String name;
    
    @Keep
    public void setData(int groupId, int itemCnt, boolean isOpen, String name) {
        this.groupId = groupId;
        this.itemCnt = itemCnt;
        this.isEnabled = isOpen;
        this.name = name;
    }

    public boolean isEnabled() {
        return isEnabled;
    }

    public int groupId() {
        return groupId;
    }

    public int itemCnt() {
        return itemCnt;
    }

    public String name() {
        return name;
    }

    public void setGroupId(int groupId) {
        this.groupId = groupId;
    }

    public void setItemCnt(int itemCnt) {
        this.itemCnt = itemCnt;
    }

    public void setEnabled(boolean enabled) {
        isEnabled = enabled;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String toString() {
        return "IptPhraseGroup{" + "groupId=" + groupId + ", itemCnt=" + itemCnt 
                + ", isEnabled=" + isEnabled + ", name='" + name + '\'' + '}';
    }
}
