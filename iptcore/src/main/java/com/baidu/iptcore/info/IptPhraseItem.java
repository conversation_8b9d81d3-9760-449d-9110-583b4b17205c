package com.baidu.iptcore.info;

import android.support.annotation.Keep;

/**
 * 个性短语Item信息
 * Created by <PERSON><PERSON><PERSON><PERSON> on 18/2/3.
 */
@Keep
public class IptPhraseItem {
    private int groupId;
    private int pos;
    private String code;
    private String word;
    
    @Keep
    public void setData(int groupId, int pos, String code, String word) {
        this.groupId = groupId;
        this.pos = pos;
        this.code = code;
        this.word = word;
    }

    public int groupId() {
        return groupId;
    }

    public int pos() {
        return pos;
    }

    public String code() {
        return code;
    }

    public String word() {
        return word;
    }

    public void setGroupId(int groupId) {
        this.groupId = groupId;
    }

    public void setPos(int pos) {
        this.pos = pos;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setWord(String word) {
        this.word = word;
    }

    @Override
    public String toString() {
        return "IptPhraseItem{" + "groupId=" + groupId + ", pos=" + pos + ", code='" 
                + code + '\'' + ", word='" + word + '\'' + '}';
    }
}
