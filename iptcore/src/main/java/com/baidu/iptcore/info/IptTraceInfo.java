package com.baidu.iptcore.info;

import android.support.annotation.Keep;

@Keep
public class IptTraceInfo {
    /**
     * traceType : 0
     * para : 3
     * type : 1
     */

    private int act;
    private String txt;
    private String pre;
    private String suf;

    public int getAct() {
        return act;
    }

    public void setAct(int act) {
        this.act = act;
    }

    public String getTxt() {
        return txt;
    }

    public void setTxt(String txt) {
        this.txt = txt;
    }

    public String getPre() {
        return pre;
    }

    public void setPre(String pre) {
        this.pre = pre;
    }

    public String getSuf() {
        return suf;
    }

    public void setSuf(String suf) {
        this.suf = suf;
    }

    @Override
    public String toString() {
        return "{" +
                "act=" + act +
                ", txt='" + txt + '\'' +
                ", pre='" + pre + '\'' +
                ", suf='" + suf + '\'' +
                '}';
    }
}
