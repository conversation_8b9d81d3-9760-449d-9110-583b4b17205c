package com.baidu.iptcore.info;

import android.support.annotation.Keep;

/**
 * 品牌词联想信息
 */
@Keep
public class IptTriggerWordItem {


    /**
     * 卡片标题
     */
    private String cardTitle;

    /**
     * 卡片大图标
     */
    private String cardIcon;

    /**
     * 卡片描述
     */
    private String cardDesc;

    /**
     * 跳转入口按钮显示文案
     */
    private String jumpTxt;

    /**
     * 跳转入口按钮
     */
    private String jumpIcon;

    /**
     * 跳转按钮的url
     */
    private String jumpUrl;

    /**
     * 是否显示发送入口
     */
    private boolean sendButton;

    @Keep
    public void setData(boolean sendButton, String cardTitle, String cardIcon, String cardDesc,
                        String jumpTxt, String jumpIcon, String jumpUrl) {
        this.cardTitle = cardTitle;
        this.cardIcon = cardIcon;
        this.cardDesc = cardDesc;
        this.jumpTxt = jumpTxt;
        this.jumpIcon = jumpIcon;
        this.jumpUrl = jumpUrl;
        this.sendButton = sendButton;
    }

    @Keep
    public void setData(boolean sendButton) {
        this.sendButton = sendButton;
    }

    public String getCardTitle() {
        return cardTitle;
    }

    public String getCardIcon() {
        return cardIcon;
    }

    public String getCardDesc() {
        return cardDesc;
    }

    public String getJumpTxt() {
        return jumpTxt;
    }

    public String getJumpIcon() {
        return jumpIcon;
    }

    public String getJumpUrl() {
        return jumpUrl;
    }

    public boolean isSendButton() {
        return sendButton;
    }

    @Override
    public String toString() {
        return "IptTriggerWordItem{" +
                ", cardTitle='" + cardTitle + '\'' +
                ", cardIcon='" + cardIcon + '\'' +
                ", cardDesc='" + cardDesc + '\'' +
                ", jumpTxt='" + jumpTxt + '\'' +
                ", jumpIcon='" + jumpIcon + '\'' +
                ", jumpUrl='" + jumpUrl + '\'' +
                ", sendButton=" + sendButton +
                '}';
    }
}
