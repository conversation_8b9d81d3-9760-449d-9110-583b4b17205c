package com.baidu.iptcore.interceptor;

import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.util.SparseArray;

import com.baidu.iptcore.DutyCallback;
import com.baidu.iptcore.ImeCoreConsts;
import com.baidu.iptcore.ImeCoreManager;
import com.baidu.iptcore.info.IptCoreDutyInfo;

import static com.baidu.iptcore.ConfigKey.ENCASE;

/**
 * 英文面板9键输入时的轮换半上屏拦截器。
 * 
 * Created by ch<PERSON><PERSON><PERSON> on 17/8/9.
 */
public class EnInlineInterceptor implements Handler.Callback, IInputInterceptor {

    /**
     * composing的delay时长
     */
    private static final long DELAY_COMPOSING = 1500;
    /**
     * composing字符提交的消息
     */
    private static final int MSG_COMMIT_INLINE_CHAR = 0;

    /**
     * 主线程Handler
     */
    private Handler mHandler;

    /**
     * 当前处于inline的按键idx，取值1-9，0表示没有当前的inlineKey
     */
    private int mCurrInlineKeyIdx;
    /**
     * 当前inline字符在轮换字符数组的索引
     */
    private int mInlineCharIdx;
    /**
     * 延迟finishComposing的回调对象
     */
    private DutyCallback mCallback;
    /**
     * 需要轮换的数据
     */
    private SparseArray<String[]> mSparseArray = new SparseArray<>();

    /**
     * 是否是en9输入
     */
    private boolean mIsEn9Input;

    /**
     * 大写状态
     */
    private int mEncaseState = ImeCoreConsts.CFG_ENCASE_NORMAL;
    /**
     * 大写暂态是否已经有大写字符上屏了
     */
    private boolean mIsShiftCapUsed;

    public EnInlineInterceptor() {
        initSparseArray();
    }

    private void initSparseArray() {
        mSparseArray.put(1, new String[] {"@", "...", "/", "1"});
        mSparseArray.put(2, new String[] {"a", "b", "c", "2"});
        mSparseArray.put(3, new String[] {"d", "e", "f", "3"});
        mSparseArray.put(4, new String[] {"g", "h", "i", "4"});
        mSparseArray.put(5, new String[] {"j", "k", "l", "5"});
        mSparseArray.put(6, new String[] {"m", "n", "o", "6"});
        mSparseArray.put(7, new String[] {"p", "q", "r", "s", "7"});
        mSparseArray.put(8, new String[] {"t", "u", "v", "8"});
        mSparseArray.put(9, new String[] {"w", "x", "y", "z", "9"});
    }

    @Override
    public void onFinishPadInput(int padId) {
        if (mIsEn9Input) {
            mIsEn9Input = false;
            resetInlineState();
            removeCallbacks();
        }
    }

    @Override
    public void onStartPadInput(int corePadId) {
        if (corePadId == ImeCoreConsts.PAD_EN9) {
            mIsEn9Input = true;
            mEncaseState = ImeCoreManager.getConfig().getInt(ENCASE);
        } else {
            mIsEn9Input = false;
        }
    }

    /**
     * 拦截按键点击
     * @param keyId 按键id
     * @return 是否拦截
     */
    @Override
    public IptCoreDutyInfo onBeforeKeyClicked(int keyId, int inputType) {
        if (mIsEn9Input && ImeCoreManager.getConfig().getIsEnT9Mode()
                && !ImeCoreManager.getPad().getStateValue(IptCoreDutyInfo.TIP_EN_ABC)
                && inputType == ImeCoreConsts.INPUTTYPE_CLICK) {
            int keyIdx = mapToT9KeyIdx(keyId);
            if (keyIdx >= 1 && keyIdx <= 9) {
                return processInline(keyIdx);
            }
            // [BAIDUINPUTBUG-111694] 问题，这里应该把 mIsShiftCapUsed 置为 false，但是并不能完全解决问题，暂不处理
            // 后续专门安排处理enT9的问题
            // mIsShiftCapUsed = false;
            if (keyId == ImeCoreConsts.FKEY_SHIFT
                    || keyId == ImeCoreConsts.FKEY_SHIFT_CAPLOCK) {
                return processShiftKey();
            } else if (keyId == ImeCoreConsts.FKEY_SPACE) {
                if (mCurrInlineKeyIdx > 0) {
                    resetInlineState();
                    return getIptCoreDutyInfo(IptCoreDutyInfo.ACTTYPE_FINISH_COMPOSING,
                            null, false);
                }
            }
        }
        return null;
    }

    private int mapToT9KeyIdx(int keyId) {
        switch (keyId) {
            case '\'':
                return 1;
            case 'B':
                return 2;
            case 'E':
                return 3;
            case 'H':
                return 4;
            case 'K':
                return 5;
            case 'N':
                return 6;
            case 'Q':
                return 7;
            case 'U':
                return 8;
            case 'X':
                return 9;
            default:
                return 0;
        }
    }

    @Override
    public void onAfterKeyClicked(int keyId, int inputType, IptCoreDutyInfo dutyInfo) {
        if (mIsEn9Input && ImeCoreManager.getConfig().getIsEnT9Mode()) {
            if (keyId == ImeCoreConsts.FKEY_EN_LX) {
                int tips = dutyInfo.tips();
                if ((tips & IptCoreDutyInfo.TIP_CAPITAL_FIRST) != 0) {
                    mIsShiftCapUsed = false;
                }

                mEncaseState = ImeCoreManager.getConfig().getInt(ENCASE);
            }
        }
    }

    @Override
    public void onBeforeDutyInfo(IptCoreDutyInfo dutyInfo) {
        if (dutyInfo == null) {
            return;
        }
        if (mIsEn9Input && ImeCoreManager.getConfig().getIsEnT9Mode()
                && !ImeCoreManager.getPad().getStateValue(IptCoreDutyInfo.TIP_EN_ABC)) {
            mapEnTipState(dutyInfo, dutyInfo.tips());
        }
    }

    private IptCoreDutyInfo processShiftKey() {
        if (mEncaseState == ImeCoreConsts.CFG_ENCASE_FIRST) {
            mEncaseState = ImeCoreConsts.CFG_ENCASE_ALL;
        } else if (mEncaseState == ImeCoreConsts.CFG_ENCASE_NORMAL) {
            mEncaseState = ImeCoreConsts.CFG_ENCASE_FIRST;
            mIsShiftCapUsed = false;
        } else {
            mEncaseState = ImeCoreConsts.CFG_ENCASE_NORMAL;
        }
        ImeCoreManager.getConfig().setInt(ENCASE, mEncaseState);
        return getIptCoreDutyInfo(IptCoreDutyInfo.ACTTYPE_NONE, null, true);
    }

    private IptCoreDutyInfo processInline(int keyIdx) {
        if (keyIdx != mCurrInlineKeyIdx) {

            String[] inlineChars = getInlineChars(keyIdx);
            if (inlineChars != null && inlineChars.length > 0) {
                mCurrInlineKeyIdx = keyIdx;
                mInlineCharIdx = 0;
                boolean toRefreshShift = checkRefreshShift(true);
                String inlineChar = getSubmitText(inlineChars, mInlineCharIdx);
                if (!TextUtils.isEmpty(inlineChar)) {
                    startDelayedCommitTask();
                    return getIptCoreDutyInfo(IptCoreDutyInfo.ACTTYPE_FINISH_AND_COMPOSING, inlineChar, toRefreshShift);
                }
            }
        } else {
            String[] inlineChars = getInlineChars(keyIdx);
            if (inlineChars != null && inlineChars.length > 0) {
                mInlineCharIdx = (mInlineCharIdx + 1) % inlineChars.length;

                boolean toRefreshShift = checkRefreshShift(false);
                String inlineChar = getSubmitText(inlineChars, mInlineCharIdx);
                if (!TextUtils.isEmpty(inlineChar)) {
                    startDelayedCommitTask();
                    return getIptCoreDutyInfo(IptCoreDutyInfo.ACTTYPE_COMPOSING, inlineChar, toRefreshShift);
                }
            }
        }
        return null;

    }

    private boolean checkRefreshShift(boolean isNewKey) {
        boolean toRefreshShift = false;
        if (mEncaseState == ImeCoreConsts.CFG_ENCASE_FIRST) {
            if (isNewKey) {
                if (mIsShiftCapUsed) {
                    toRefreshShift = true;
                    mEncaseState = ImeCoreConsts.CFG_ENCASE_NORMAL;
                    ImeCoreManager.getConfig().setInt(ENCASE, mEncaseState);
                } else {
                    mIsShiftCapUsed = true;
                }
            }
        }
        return toRefreshShift;
    }

    private String getSubmitText(String[] inlineChars, int idx) {
        String text = inlineChars[idx];
        if (text.length() == 1 && mEncaseState != ImeCoreConsts.CFG_ENCASE_NORMAL) {
            char ch = text.charAt(0);
            if (ch >= 'a' && ch <= 'z') {
                ch -= ('a' - 'A');
            }
            text = String.valueOf(ch);
        }
        return text;
    }

    private IptCoreDutyInfo getIptCoreDutyInfo(int actionType, String insertText, boolean refreshShiftState) {
        IptCoreDutyInfo dutyInfo = new IptCoreDutyInfo();
        dutyInfo.setActionType(actionType);
        dutyInfo.setInsertText(insertText);
        if (refreshShiftState) {

            dutyInfo.setFlashFlag(IptCoreDutyInfo.REFL_TIP | IptCoreDutyInfo.REFL_LAYOUT);
            int tipState = ImeCoreManager.getPad().getTipStates();
            mapEnTipState(dutyInfo, tipState);
        }
        return dutyInfo;
    }

    /**
     * 根据内部状态修改DutyInfo的tipState
     */
    private void mapEnTipState(IptCoreDutyInfo dutyInfo, int originTipState) {
        originTipState &= (~IptCoreDutyInfo.TIP_CAPITAL_FIRST);
        originTipState &= (~IptCoreDutyInfo.TIP_CAPITAL_ALL);
        if (mEncaseState == ImeCoreConsts.CFG_ENCASE_FIRST) {
            originTipState |= IptCoreDutyInfo.TIP_CAPITAL_FIRST;
        } else if (mEncaseState == ImeCoreConsts.CFG_ENCASE_ALL) {
            originTipState |= IptCoreDutyInfo.TIP_CAPITAL_ALL;
        }
        dutyInfo.setTipState(originTipState);
    }

    private void startDelayedCommitTask() {
        initComposingRunnable();
        mHandler.removeMessages(MSG_COMMIT_INLINE_CHAR);
        Message msg = Message.obtain();
        msg.what = MSG_COMMIT_INLINE_CHAR;
        mHandler.sendMessageDelayed(msg, DELAY_COMPOSING);
    }

    private String[] getInlineChars(int keyIdx) {
        return mSparseArray.get(keyIdx);
    }

    private void initComposingRunnable() {
        if (mHandler == null) {
            mHandler = new Handler(this);
        }
    }

    public void setCallback(DutyCallback callback) {
        mCallback = callback;
    }

    @Override
    public boolean handleMessage(Message msg) {
        switch (msg.what) {
            case MSG_COMMIT_INLINE_CHAR:
                resetInlineState();
                boolean refreshShift = checkRefreshShift(true);
                IptCoreDutyInfo dutyInfo =
                        getIptCoreDutyInfo(IptCoreDutyInfo.ACTTYPE_FINISH_COMPOSING, null, refreshShift);
                if (mCallback != null) {
                    mCallback.onDutyInfo(dutyInfo);
                }
                break;
            default:
                break;
        }
        return true;
    }

    private void resetInlineState() {
        mCurrInlineKeyIdx = 0;
        mInlineCharIdx = 0;
    }

    public void removeCallbacks() {
        if (mHandler != null) {
            mHandler.removeMessages(MSG_COMMIT_INLINE_CHAR);
        }
    }
}
