package com.baidu.iptcore.interceptor;

import com.baidu.iptcore.info.IptCoreDutyInfo;

import android.view.inputmethod.EditorInfo;

/**
 * 输入操作的拦截器
 * Created by cdf on 2019/2/19.
 */
public interface IInputInterceptor {

    /**
     * 拦截按键点击操作
     * @param keyId 按键id
     * @param inputType 输入方式，点击、上滑等
     * @return 需要执行的DutyInfo。为null表示不拦截
     */
    IptCoreDutyInfo onBeforeKeyClicked(int keyId, int inputType);

    /**
     * 按键点击之后的操作
     * @param keyId 按键id
     * @param inputType 输入方式，点击、上滑等
     * @param dutyInfo 内核返回的dutyInfo
     */
    void onAfterKeyClicked(int keyId, int inputType, IptCoreDutyInfo dutyInfo);

    /**
     * 退出一个面板输入
     * @param padId 面板id
     */
    void onFinishPadInput(int padId);

    /**
     * 进入一个面板输入
     * @param corePadId 面板id
     */
    void onStartPadInput(int corePadId);

    /**
     * 内核dutyInfo前处理
     */
    void onBeforeDutyInfo(IptCoreDutyInfo dutyInfo);
}
