package com.baidu.iptcore.interceptor;

import java.util.List;

import com.baidu.iptcore.DutyCallback;
import com.baidu.iptcore.ImeCoreConsts;
import com.baidu.iptcore.ImePad;
import com.baidu.iptcore.info.IptCoreDutyInfo;

/**
 * 提供拦截器功能的ImePad
 * <p>
 * Created by ch<PERSON><PERSON>feng on 2019/2/19.
 */
public class InterceptImePad extends ImePad {

    /**
     * 拦截器列表
     */
    private List<IInputInterceptor> mInterceptors;

    public final void init(DutyCallback imeCoreCallback,
                           List<IInputInterceptor> interceptors) {
        setCallback(imeCoreCallback);
        mInterceptors = interceptors;
    }

    @Override
    public void switchKeyboard(int corePadId) {
        // 英文面板存在tip变化，不做pad不等的过滤
        boolean needSwitch = ImeCoreConsts.isEnPad(corePadId)
                || ImeCoreConsts.isHwPad(corePadId) || ImeCoreConsts.isNumPad(corePadId)
                || (super.getCurrentPadId() != corePadId);
        if (needSwitch) {
            if (mInterceptors != null) {
                for (IInputInterceptor interceptor : mInterceptors) {
                    interceptor.onFinishPadInput(super.getCurrentPadId());
                }
            }
            super.switchKeyboard(corePadId);

            if (mInterceptors != null) {
                for (IInputInterceptor interceptor : mInterceptors) {
                    interceptor.onStartPadInput(corePadId);
                }
            }
        }
    }

    @Override
    public void actCnEnKey(int padId) {
        if (mInterceptors != null) {
            for (IInputInterceptor interceptor : mInterceptors) {
                interceptor.onFinishPadInput(super.getCurrentPadId());
            }
        }
        super.actCnEnKey(padId);
        if (mInterceptors != null) {
            for (IInputInterceptor interceptor : mInterceptors) {
                interceptor.onStartPadInput(padId);
            }
        }
    }

    @Override
    public final IptCoreDutyInfo actKeyClickInternal(int keyId, int x, int y, int inputType, String uni) {
        if (mInterceptors != null) {
            for (IInputInterceptor interceptor : mInterceptors) {
                IptCoreDutyInfo dutyInfo = interceptor.onBeforeKeyClicked(keyId, inputType);
                if (dutyInfo != null) {
                    return dutyInfo;
                }
            }
        }
        IptCoreDutyInfo dutyInfo = super.actKeyClickInternal(keyId, x, y, inputType, uni);

        if (mInterceptors != null) {
            for (IInputInterceptor interceptor : mInterceptors) {
                interceptor.onAfterKeyClicked(keyId, inputType, dutyInfo);
            }
        }
        return dutyInfo;
    }

    public final void dutyEntry(IptCoreDutyInfo dutyInfo) {
        if (mInterceptors != null) {
            for (IInputInterceptor interceptor : mInterceptors) {
                interceptor.onBeforeDutyInfo(dutyInfo);
            }
        }
        super.dutyEntry(dutyInfo);
    }
}
