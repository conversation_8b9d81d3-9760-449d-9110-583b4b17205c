package com.baidu.iptcore.net;

import android.support.annotation.Keep;
import android.support.annotation.NonNull;

import com.baidu.base.network.DatagramSocketFactory;
import com.baidu.base.network.INetCallback;
import com.baidu.base.network.http.HttpSender;
import com.baidu.base.network.udp.UdpSender3;
import com.baidu.iptcore.util.Config;
import com.baidu.iptcore.util.Logger;

/**
 * 内核云输入的一个网络流实例。对应一个网络请求。
 *
 * Created by ch<PERSON><PERSON><PERSON> on 18/9/11.
 */
@Keep
public class IptCloudStream {

    /** 云输入协议：http post请求 */
    public static final int PROTOCOL_HTTP_POST = 0;
    /** 云输入协议：http udp请求 */
    public static final int PROTOCOL_UDP = 1;

    /** 请求的url */
    private final String url;
    /** 请求的端口 */
    private final int port;
    /** 请求的协议：http/udp */
    private final int protocol;
    /** 超时时间 */
    private final int timeOut;
    /** 允许返回的最大长度，主要是为了http协议拼接body时，防止数据过大 */
    private final int maxRecvLen;
    /** 请求返回后的callback */
    private final IptCloudStreamCallback callback;
    /** UDP的socket工厂 */
    private final DatagramSocketFactory datagramSocketFactory;

    /** 是否有效。内核析构后，stream会变得无效 */
    private volatile boolean isValid = true;

    /**
     * 构造器
     * @param url 需要请求的url
     * @param port 需要请求的端口
     * @param protocol 需要请求的协议
     * @param timeOut 超时时间,单位毫秒
     * @param maxRecvLen 允许返回的最大长度，主要是为了http协议拼接body时，防止数据过大
     * @param callback 网络回调通知网络数据已经返回的Callback
     */
    public IptCloudStream(String url, int port, int protocol, int timeOut, int maxRecvLen, 
                          IptCloudStreamCallback callback) {
        this.url = url;
        this.port = port;
        this.protocol = protocol;
        this.timeOut = timeOut;
        this.maxRecvLen = maxRecvLen;
        this.callback = callback;
        this.datagramSocketFactory = new DatagramSocketFactory(maxRecvLen, buildUdpCallback());
    }

    /**
     * 发起网络请求
     * @param sendData 发送的消息,HTTP请求时,仅仅为http body,http头需要有Content-Length
     * @param streamLen 发送消息的长度
     * @return >=0 发送成功, < 0表示发送失败
     */
    public int send(int streamType, byte[] sendData, int streamLen) {

        boolean isUdp = (protocol == PROTOCOL_UDP);

        // 纯净模式
        if (Config.isPureMode()) {
            return 0;
        }
        if (isUdp) {
            UdpSender3.sendData(datagramSocketFactory, streamType, url, port, sendData);
        } else {
            INetCallback netCallback = buildHttpCallback(System.currentTimeMillis());
            HttpSender sender = new HttpSender(netCallback, url, streamType, sendData, timeOut);
            sender.startLink();
        }
        return 0;
    }

    /**
     * 构建HTTP网络请求的Callback
     */
    @NonNull
    private INetCallback buildHttpCallback(long startTime) {
        return new INetCallback() {
            @Override
            public void onResponse(byte[] responseBody) {
                Logger.i("iptcore", "stream onResponse(http):: errorCode=0"
                        + ", responseDataLen=" + (responseBody == null ? 0 : responseBody.length)
                        + ", cost=" + (System.currentTimeMillis() - startTime));
                if (callback != null && isValid) {
                    callback.onResponse(IptCloudStream.this, 0, responseBody);
                }
            }

            @Override
            public void onFail(int errorCode, String errorMsg) {
                Logger.i("iptcore", "stream onFail(http):: errorCode=" + errorCode
                        + ", cost=" + (System.currentTimeMillis() - startTime)
                        + ", errorMsg=" + errorMsg);
                if (callback != null && isValid) {
                    callback.onResponse(IptCloudStream.this, -1, null);
                }
            }
        };
    }

    /**
     * 构建UDP网络请求的Callback
     */
    @NonNull
    private INetCallback buildUdpCallback() {
        return new INetCallback() {
            @Override
            public void onResponse(byte[] responseBody) {
                Logger.i("iptcore", "stream onResponse(udp):: errorCode=0"
                        + ", responseDataLen=" + (responseBody == null ? 0 : responseBody.length));
                if (callback != null && isValid) {
                    callback.onResponse(IptCloudStream.this, 0, responseBody);
                }
            }

            @Override
            public void onFail(int errorCode, String errorMsg) {
                Logger.i("iptcore", "stream onFail(udp):: errorCode=" + errorCode
                        + ", errorMsg=" + errorMsg);
                if (callback != null && isValid) {
                    callback.onResponse(IptCloudStream.this, -1, null);
                }
            }
        };
    }

    /**
     * 网络流关闭
     */
    public void close() {
        isValid = false;
    }

    @Override
    public String toString() {
        return "IptCloudStream{" +
                "url='" + url + '\'' +
                ", port=" + port +
                ", protocol=" + protocol +
                ", timeOut=" + timeOut +
                ", maxRecvLen=" + maxRecvLen +
                ", isValid=" + isValid +
                '}';
    }
}
