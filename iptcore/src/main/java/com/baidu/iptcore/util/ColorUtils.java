package com.baidu.iptcore.util;

import android.graphics.Color;
import android.text.TextUtils;

/**
 * 颜色工具类
 */
public class ColorUtils {

    /**
     * 将内核的字符串颜色转换为int
     */
    public static int coreColorStr2ColorInt(String colorStr) {
        if (colorStr == null || TextUtils.isEmpty(colorStr)) {
            return Color.TRANSPARENT;
        }
        if (colorStr.startsWith("#")) {
            colorStr = colorStr.substring(1);
        } else if (colorStr.startsWith("0x")) {
            colorStr = colorStr.substring(2);
        }
        if (colorStr.length() < 6) {
            return Color.TRANSPARENT;
        }
        try {
            int r = Integer.parseInt(colorStr.substring(0, 2), 16);
            int g = Integer.parseInt(colorStr.substring(2, 4), 16);
            int b = Integer.parseInt(colorStr.substring(4, 6), 16);
            int a = 0xFF;
            if (colorStr.length() == 8) {
                a = Integer.parseInt(colorStr.substring(6, 8), 16);
            }
            return Color.argb(a, r, g, b);
        } catch (NumberFormatException e) {
            return Color.TRANSPARENT;
        }
    }

    /**
     * 将内核的rgba颜色转换argb颜色
     */
    public static int rgba2argb(int color) {
        int r = color >>> 24;
        int g = (color >> 16) & 0xFF;
        int b = (color >> 8) & 0xFF;
        int a = color & 0xFF;
        return Color.argb(a, r, g, b);
    }
}
