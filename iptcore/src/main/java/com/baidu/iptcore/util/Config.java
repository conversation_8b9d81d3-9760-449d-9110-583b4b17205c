package com.baidu.iptcore.util;

import com.baidu.iptcore.ImeCoreConfiguration;

/**
 * 模块配置管理
 * Created by <PERSON><PERSON><PERSON><PERSON> on 18/2/28.
 */
public class Config {

    /**
     * 是否使能log打印
     */
    private static boolean sEnableLog;
    /**
     * Log打印的等级
     */
    private static int sLogLevel;
    /**
     * 是否是沙盒环境
     */
    private static boolean sIsTestUrl;

    /**
     * 是否是纯净模式
     */
    private static boolean sPureMode;

    /**
     * 精简日志模式
     */
    private static boolean shortLogMode;


    /**
     * 根据外部配置初始化模块配置
     * @param configuration 外部的配置项
     */
    public static void init(ImeCoreConfiguration configuration) {
        sEnableLog = configuration.enableLog;
        sIsTestUrl = configuration.isTestUrl;
        sLogLevel = configuration.logLovel;
        sPureMode = configuration.isPureMode;
        shortLogMode = configuration.shortLogMode;
    }

    /**
     * 是否使能log打印
     */
    public static boolean enableLog() {
        return sEnableLog;
    }

    /**
     * 获得设置给内核的日志打印等级
     */
    public static int getLogLevel() {
        return sLogLevel;
    }

    public static boolean isTestUrl() {
        return sIsTestUrl;
    }

    public static boolean isPureMode() {
        return sPureMode;
    }

    public static boolean isShortLogMode() {
        return shortLogMode;
    }
}
