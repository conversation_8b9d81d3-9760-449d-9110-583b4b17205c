package com.baidu.iptcore.util;

import android.support.annotation.AnyThread;

import com.baidu.annotation.CoreThread;
import com.baidu.input.inputtrace.api.InputTracer;

import java.util.Arrays;

/**
 * 内核方法耗时数据记录
 */
public class CoreMethodCostRecorder {
    /**
     * 默认记录15次方法调用耗时
     */
    private static final long[] CORE_INNER_METHOD_COSTS = new long[15];

    /**
     * index
     */
    private static int coreCostIndex = 0;

    /**
     * 默认记录20次方法调用耗时
     */
    private static final String[] CORE_METHOD_INFOS = new String[20];

    /**
     * index
     */
    private static int coreInfoIndex = 0;

    /**
     * Call From Core 内核线程调用
     */
    @CoreThread
    public static void onCoreMethodStart(int coreMethod, String msg) {
        // 新的按键开始
        if (coreMethod == 0) {
            clearCoreInnerCost();
        }
        if (msg != null) {
            synchronized (CORE_METHOD_INFOS) {
                if (coreInfoIndex >= CORE_METHOD_INFOS.length) {
                    coreInfoIndex = 0;
                }
                CORE_METHOD_INFOS[coreInfoIndex++] = msg;
            }
        }
        InputTracer.i(Ids.coreNativeStart + coreMethod);
        // Log.e("wentaoli", "onCoreMethodStart: " + coreMethod + "," + msg);
    }

    /**
     * Call From Core
     */
    @CoreThread
    public static void onCoreMethodEnd(int coreMethod, String msg) {
        InputTracer.o(Ids.coreNativeStart + coreMethod);
        // Log.e("wentaoli", "onCoreMethodEnd: " + coreMethod + "," + msg);
    }

    /**
     * Call From Core
     */
    @CoreThread
    public static void onCoreInnerCost(int coreMethod, int cost) {
        long info = combineInts2Long(coreMethod, cost);
        synchronized (CORE_INNER_METHOD_COSTS) {
            if (coreCostIndex >= CORE_INNER_METHOD_COSTS.length) {
                coreCostIndex = 0;
            }
            CORE_INNER_METHOD_COSTS[coreCostIndex++] = info;
        }
        // Log.e("wentaoli", "onCoreInnerCost: " + coreMethod + "," + cost + " =" + Long.toHexString(info));
    }

    @CoreThread
    private static void clearCoreInnerCost() {
        synchronized (CORE_INNER_METHOD_COSTS) {
            coreCostIndex = 0;
            Arrays.fill(CORE_INNER_METHOD_COSTS, 0);
        }
        synchronized (CORE_METHOD_INFOS) {
            coreInfoIndex = 0;
            Arrays.fill(CORE_METHOD_INFOS, null);
        }
    }

    /**
     * 获取最近一次按键操作内核内部方法调用耗时
     */
    @AnyThread
    public static long[] getCoreInnerMethodCosts() {
        return Arrays.copyOf(CORE_INNER_METHOD_COSTS, coreCostIndex);
    }

    /**
     * 获取最近一次按键操作内核内部方法调用信息
     */
    @AnyThread
    public static String[] getCoreMethodInfos() {
        return Arrays.copyOf(CORE_METHOD_INFOS, coreInfoIndex);
    }

    private static long combineInts2Long(int high, int low) {
        return ((long) high << 32) | (low & 0xFFFFFFFFL);
    }

}
