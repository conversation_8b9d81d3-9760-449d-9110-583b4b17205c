package com.baidu.iptcore.util;

import java.io.Closeable;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Arrays;

import com.baidu.iptcore.ImeCoreConsts;
import com.baidu.iptcore.IptCoreInterface;

import android.content.Context;
import android.text.TextUtils;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.util.Log;

/**
 * 词典安装的工具类
 *
 * Created by cdf on 17/7/28.
 */
public class DictUtils {

    /**
     * 拷贝词库文件到指定路径
     * @param context context实例
     * @param dictDirPath 字典路径
     */
    public static void copyDictionary(Context context, @NonNull String dictDirPath,
                                         @Nullable IDictionaryInstaller dictInstaller, IDictionaryCopy dictionaryCopy) {
        if (dictInstaller != null) {
            dictInstaller.beforeInstallDictionary();
        }

        dictDirPath = addFileSeperatorAtLast(dictDirPath);
        File dir = new File(dictDirPath);
        if (!dir.exists()) {
            boolean success = dir.mkdirs();
            if (!success) {
                if (dictInstaller != null) {
                    dictInstaller.recordInstallError(context, "mkdirs fail");
                }
                return;
            }
        }

        // 用于日志收集，目前怀疑getAsset().list()接口会产生错误
        String[] builtinDictList = IptCoreInterface.defBuiltinIptFiles;
        String[] dictList = null;
        try {
            dictList = context.getAssets().list(ImeCoreConsts.ASSERT_DIR_DICT_NAME);
        } catch (IOException e) {
            Logger.printStackTrace(e);
            if (dictInstaller != null) {
                dictInstaller.recordInstallError(context, Log.getStackTraceString(e));
            }
        }
        if (dictList == null || dictList.length < builtinDictList.length
                || !checkContains(dictList, "cz5.bin")) {
            if (dictInstaller != null) {
                dictInstaller.recordInstallError(context,
                        "assets list dicts fail: " + Arrays.toString(dictList));
            }

            // 如果Asset.list出错，使用默认的内置列表替代动态获取的列表
            // todo
            dictList = builtinDictList;
        }
        for (String dictName : dictList) {
            if (TextUtils.isEmpty(dictName)) {
                continue;
            }
            String assertPath = ImeCoreConsts.ASSERT_DIR_DICT_NAME + File.separator + dictName;
            String filePath = dictDirPath + dictName;

            try {
                if (dictInstaller == null) {
                    installDictionary(context, assertPath, filePath, dictionaryCopy);
                } else {
                    dictInstaller.installDictionary(context, dictName, assertPath, filePath);
                }
            } catch (Exception e) {
                if (dictInstaller != null) {
                    dictInstaller.recordInstallError(context, Log.getStackTraceString(e));
                }
            }
        }

        if (dictInstaller != null) {
            dictInstaller.afterInstallDictionary();
        }
    }

    private static boolean checkContains(@NonNull String[] dictList, @NonNull String dictName) {
        for (String dict : dictList) {
            if (dictName.equals(dict)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 安装词典
     * @param context 上下文
     * @param assetsPath assets下的路径
     * @param filePath 目标路径
     */
    private static void installDictionary(Context context, String assetsPath,
                                          String filePath, IDictionaryCopy dictionaryCopy) {
        File file = new File(filePath);
        if (file.exists()) {
            return;
        }

        InputStream is = null;
        OutputStream os = null;
        byte[] buffer = new byte[1 << 6];
        try {
            is = sourceAssetsFile(context, assetsPath);
            os = new FileOutputStream(filePath);
            int size;
            while ((size = is.read(buffer)) > 0) {
                os.write(buffer, 0, size);
            }
        } catch (Exception e) {
            Logger.printStackTrace(e);
            if (null != dictionaryCopy) {
                dictionaryCopy.copyDictionaryError(context, filePath, assetsPath, e.getMessage());
            }
        } finally {
            closeQuietly(is);
            closeQuietly(os);
        }
    }

    /**
     * 如果路径最后没有文件分隔符，则添加一个
     */
    private static String addFileSeperatorAtLast(@NonNull String path) {
        if (!path.endsWith(File.separator)) {
            path = path + File.separator;
        }
        return path;
    }

    /**
     * 读取assets下的文件
     * @param context 上下文
     * @param fileName assets路径
     * @return 文件输入流，发生异常返回null
     */
    private static InputStream sourceAssetsFile(Context context, String fileName) {
        InputStream in = null;
        try {
            in = context.getAssets().open(fileName);
        } catch (IOException e) {
            Logger.printStackTrace(e);
            closeQuietly(in);
        }
        return in;
    }

    /**
     * 关闭文件流
     * @param closeable 文件流
     */
    private static void closeQuietly(Closeable closeable) {
        try {
            if (closeable != null) {
                closeable.close();
            }
        } catch (IOException e) {
            Logger.printStackTrace(e);
        }
    }

}
