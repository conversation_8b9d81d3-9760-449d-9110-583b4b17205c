package com.baidu.iptcore.util;

import android.content.Context;

/**
 * 词典安装工具
 */
public interface IDictionaryInstaller {

    /**
     * 安装词典前的操作
     */
    void beforeInstallDictionary();

    /**
     * 安装一个词典文件
     * @param context 上下文
     * @param dictFileName 词典名称
     * @param assetsPath 词典在asset下的路径
     * @param filePath 词典的目标路径
     */
    void installDictionary(Context context, String dictFileName, String assetsPath, String filePath);

    /**
     * 安装词典后的操作
     */
    void afterInstallDictionary();

    /**
     * 词典安装异常的回调，用于记录错误
     * @param context 上下文
     * @param message 错误信息
     */
    void recordInstallError(Context context, String message);
}
