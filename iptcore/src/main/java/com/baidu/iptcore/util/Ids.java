package com.baidu.iptcore.util;

import android.support.annotation.Keep;

import com.baidu.input.inputtrace.api.MethodIds;

@Keep
public interface Ids {
    /** @see com.baidu.iptcore.async.CoreThreadEngine#executeFutureTaskInternal  */
    int mainWaitForCore = MethodIds.CORE_START + 1;

    /** @see com.baidu.iptcore.async.CoreThreadEngine#executeTask  */
    int coreTaskRun = MethodIds.CORE_START + 2;

    /** @see com.baidu.iptcore.async.CoreThreadEngine#executeFutureTaskInternal  */
    int coreFutureTaskRun = MethodIds.CORE_START + 3;

    /** @see com.baidu.iptcore.CoreCellConfig#installCell */
    int installCell = MethodIds.CORE_START + 100;
    /** @see com.baidu.iptcore.CoreCellConfig#uninstallCell */
    int uninstallCell = MethodIds.CORE_START + 101;
    /** @see com.baidu.iptcore.CoreCellConfig#getPhraseGroupCount */
    int getPhraseGroupCount = MethodIds.CORE_START + 102;
    /** @see com.baidu.iptcore.CoreCellConfig#getPhraseGroup */
    int getPhraseGroup = MethodIds.CORE_START + 103;
    /** @see com.baidu.iptcore.CoreCellConfig#addPhraseGroup */
    int addPhraseGroup = MethodIds.CORE_START + 104;
    /** @see com.baidu.iptcore.CoreCellConfig#deletePhraseGroup */
    int deletePhraseGroup = MethodIds.CORE_START + 105;
    /** @see com.baidu.iptcore.CoreCellConfig#editPhraseGroup */
    int editPhraseGroup = MethodIds.CORE_START + 106;
    /** @see com.baidu.iptcore.CoreCellConfig#enablePhraseGroup */
    int enablePhraseGroup = MethodIds.CORE_START + 107;
    /** @see com.baidu.iptcore.CoreCellConfig#getPhraseItemCount */
    int getPhraseItemCount = MethodIds.CORE_START + 108;
    /** @see com.baidu.iptcore.CoreCellConfig#getPhraseItem */
    int getPhraseItem = MethodIds.CORE_START + 109;
    /** @see com.baidu.iptcore.CoreCellConfig#addPhraseItem */
    int addPhraseItem = MethodIds.CORE_START + 110;
    /** @see com.baidu.iptcore.CoreCellConfig#editPhraseItem */
    int editPhraseItem = MethodIds.CORE_START + 111;
    /** @see com.baidu.iptcore.CoreCellConfig#deletePhraseItem */
    int deletePhraseItem = MethodIds.CORE_START + 112;
    /** @see com.baidu.iptcore.CoreCellConfig#importPhrase */
    int importPhrase = MethodIds.CORE_START + 113;
    /** @see com.baidu.iptcore.CoreCellConfig#exportPhrase */
    int exportPhrase = MethodIds.CORE_START + 114;
    /** @see com.baidu.iptcore.CoreCellConfig#reduceUsWord */
    int reduceUsWord = MethodIds.CORE_START + 115;
    /** @see com.baidu.iptcore.CoreCellConfig#getUsWordSize */
    int getUsWordSize = MethodIds.CORE_START + 116;
    /** @see com.baidu.iptcore.CoreCellConfig#importUsWord */
    int importUsWord = MethodIds.CORE_START + 117;
    /** @see com.baidu.iptcore.CoreCellConfig#exportUsWord */
    int exportUsWord = MethodIds.CORE_START + 118;
    /** @see com.baidu.iptcore.CoreCellConfig#importUeWord */
    int importUeWord = MethodIds.CORE_START + 119;
    /** @see com.baidu.iptcore.CoreCellConfig#exportUeWord */
    int exportUeWord = MethodIds.CORE_START + 120;
    /** @see com.baidu.iptcore.CoreCellConfig#exportRareUserWord */
    int exportRareUserWord = MethodIds.CORE_START + 121;
    /** @see com.baidu.iptcore.CoreCellConfig#getHwRareVersion */
    int getHwRareVersion = MethodIds.CORE_START + 122;
    /** @see com.baidu.iptcore.CoreCellConfig#importAllUserWord */
    int importAllUserWord = MethodIds.CORE_START + 123;
    /** @see com.baidu.iptcore.CoreCellConfig#exportAllUserWord */
    int exportAllUserWord = MethodIds.CORE_START + 124;
    /** @see com.baidu.iptcore.CoreCellConfig#getCellCount */
    int getCellCount = MethodIds.CORE_START + 125;
    /** @see com.baidu.iptcore.CoreCellConfig#getCellInfoByIndex */
    int getCellInfoByIndex = MethodIds.CORE_START + 126;
    /** @see com.baidu.iptcore.CoreCellConfig#getCellInfoByCellId */
    int getCellInfoByCellId = MethodIds.CORE_START + 127;
    /** @see com.baidu.iptcore.CoreCellConfig#upateCellLocInfo */
    int upateCellLocInfo = MethodIds.CORE_START + 128;
    /** @see com.baidu.iptcore.CoreCellConfig#getPopWordCellId */
    int getPopWordCellId = MethodIds.CORE_START + 129;
    /** @see com.baidu.iptcore.CoreCellConfig#getSyswordCellId */
    int getSyswordCellId = MethodIds.CORE_START + 130;
    /** @see com.baidu.iptcore.CoreCellConfig#getCloudWhiteVer */
    int getCloudWhiteVer = MethodIds.CORE_START + 131;
    /** @see com.baidu.iptcore.CoreCellConfig#enableCell */
    int enableCell = MethodIds.CORE_START + 132;
    /** @see com.baidu.iptcore.CoreCellConfig#getRecentCount */
    int getRecentCount = MethodIds.CORE_START + 133;
    /** @see com.baidu.iptcore.CoreCellConfig#getKwdCellCount */
    int getKwdCellCount = MethodIds.CORE_START + 134;
    /** @see com.baidu.iptcore.CoreCellConfig#getKwdCellInfoByIndex */
    int getKwdCellInfoByIndex = MethodIds.CORE_START + 135;
    /** @see com.baidu.iptcore.CoreCellConfig#getKwdCellInfoByCellId */
    int getKwdCellInfoByCellId = MethodIds.CORE_START + 136;
    /** @see com.baidu.iptcore.CoreCellConfig#exportKwd */
    int exportKwd = MethodIds.CORE_START + 137;
    /** @see com.baidu.iptcore.CoreCellConfig#getIdmCellCount */
    int getIdmCellCount = MethodIds.CORE_START + 138;
    /** @see com.baidu.iptcore.CoreCellConfig#getIdmCellInfoByIndex */
    int getIdmCellInfoByIndex = MethodIds.CORE_START + 139;
    /** @see com.baidu.iptcore.CoreCellConfig#hwEncodePoint */
    int hwEncodePoint = MethodIds.CORE_START + 140;
    /** @see com.baidu.iptcore.CoreCellConfig#contactReset */
    int contactReset = MethodIds.CORE_START + 141;
    /** @see com.baidu.iptcore.CoreCellConfig#contactAppendAttr */
    int contactAppendAttr = MethodIds.CORE_START + 142;
    /** @see com.baidu.iptcore.CoreCellConfig#contactAppendValue */
    int contactAppendValue = MethodIds.CORE_START + 143;
    /** @see com.baidu.iptcore.CoreCellConfig#contactBuildStart */
    int contactBuildStart = MethodIds.CORE_START + 144;
    /** @see com.baidu.iptcore.CoreCellConfig#contactBuildAddName */
    int contactBuildAddName = MethodIds.CORE_START + 145;
    /** @see com.baidu.iptcore.CoreCellConfig#contactBuildAddValue */
    int contactBuildAddValue = MethodIds.CORE_START + 146;
    /** @see com.baidu.iptcore.CoreCellConfig#contactBuildEnd */
    int contactBuildEnd = MethodIds.CORE_START + 147;
    /** @see com.baidu.iptcore.CoreCellConfig#contactBuildAddBlackName */
    int contactBuildAddBlackName = MethodIds.CORE_START + 148;
    /** @see com.baidu.iptcore.CoreCellConfig#contactDelete */
    int contactDelete = MethodIds.CORE_START + 149;
    /** @see com.baidu.iptcore.CoreCellConfig#contactVoiceFind */
    int contactVoiceFind = MethodIds.CORE_START + 150;
    /** @see com.baidu.iptcore.CoreCellConfig#contactVoiceFindAddressbook */
    int contactVoiceFindAddressbook = MethodIds.CORE_START + 151;
    /** @see com.baidu.iptcore.CoreCellConfig#oldCpExport */
    int oldCpExport = MethodIds.CORE_START + 152;
    /** @see com.baidu.iptcore.CoreCellConfig#oldUeExport */
    int oldUeExport = MethodIds.CORE_START + 153;
    /** @see com.baidu.iptcore.CoreCellConfig#importZyword */
    int importZyword = MethodIds.CORE_START + 154;
    /** @see com.baidu.iptcore.CoreCellConfig#exportZyword */
    int exportZyword = MethodIds.CORE_START + 155;
    /** @see com.baidu.iptcore.CoreCellConfig#symImport */
    int symImport = MethodIds.CORE_START + 156;
    /** @see com.baidu.iptcore.CoreCellConfig#symExport */
    int symExport = MethodIds.CORE_START + 157;
    /** @see com.baidu.iptcore.CoreCellConfig#userSymImport */
    int userSymImport = MethodIds.CORE_START + 158;
    /** @see com.baidu.iptcore.CoreCellConfig#sylianImport */
    int sylianImport = MethodIds.CORE_START + 159;
    /** @see com.baidu.iptcore.CoreCellConfig#vkwordImport */
    int vkwordImport = MethodIds.CORE_START + 160;
    /** @see com.baidu.iptcore.CoreCellConfig#vkwordExport */
    int vkwordExport = MethodIds.CORE_START + 161;
    /** @see com.baidu.iptcore.CoreCellConfig#usrinfoImport */
    int usrinfoImport = MethodIds.CORE_START + 162;
    /** @see com.baidu.iptcore.CoreCellConfig#usrinfoExport */
    int usrinfoExport = MethodIds.CORE_START + 163;
    /** @see com.baidu.iptcore.CoreCellConfig#otherwordImport */
    int otherwordImport = MethodIds.CORE_START + 164;
    /** @see com.baidu.iptcore.CoreCellConfig#getZhuyinHzVersion */
    int getZhuyinHzVersion = MethodIds.CORE_START + 165;
    /** @see com.baidu.iptcore.CoreCellConfig#getZhuyinCzVersion */
    int getZhuyinCzVersion = MethodIds.CORE_START + 166;
    /** @see com.baidu.iptcore.CoreCellConfig#getZhuyinCzInfo */
    int getZhuyinCzInfo = MethodIds.CORE_START + 167;
    /** @see com.baidu.iptcore.CoreCellConfig#getCangjieVersion */
    int getCangjieVersion = MethodIds.CORE_START + 168;
    /** @see com.baidu.iptcore.CoreCellConfig#getHwVersion */
    int getHwVersion = MethodIds.CORE_START + 169;
    /** @see com.baidu.iptcore.CoreCellConfig#searchGetVersion */
    int searchGetVersion = MethodIds.CORE_START + 170;
    /** @see com.baidu.iptcore.CoreCellConfig#searchFind */
    int searchFind = MethodIds.CORE_START + 171;
    /** @see com.baidu.iptcore.CoreCellConfig#adjustSylianRelation */
    int adjustSylianRelation = MethodIds.CORE_START + 172;
    /** @see com.baidu.iptcore.CoreCellConfig#featureInfExtract */
    int featureInfExtract = MethodIds.CORE_START + 173;
    /** @see com.baidu.iptcore.CoreCellConfig#candContextExport */
    int candContextExport = MethodIds.CORE_START + 174;
    /** @see com.baidu.iptcore.CoreCellConfig#getFtByUni */
    int getFtByUni = MethodIds.CORE_START + 175;
    /** @see com.baidu.iptcore.CoreCellConfig#importOldUsFile */
    int importOldUsFile = MethodIds.CORE_START + 176;
    /** @see com.baidu.iptcore.CoreCellConfig#kwdGetSearchVersion */
    int kwdGetSearchVersion = MethodIds.CORE_START + 177;
    /** @see com.baidu.iptcore.CoreCellConfig#getAutoReplyVer */
    int getAutoReplyVer = MethodIds.CORE_START + 178;
    /** @see com.baidu.iptcore.CoreCellConfig#getSpMapSheng */
    int getSpMapSheng = MethodIds.CORE_START + 179;
    /** @see com.baidu.iptcore.CoreCellConfig#getSpMapYun */
    int getSpMapYun = MethodIds.CORE_START + 180;
    /** @see com.baidu.iptcore.CoreCellConfig#importAppMap */
    int importAppMap = MethodIds.CORE_START + 181;
    /** @see com.baidu.iptcore.CoreCellConfig#appMapGetVersion */
    int appMapGetVersion = MethodIds.CORE_START + 182;
    /** @see com.baidu.iptcore.CoreCellConfig#czDownRefresh */
    int czDownRefresh = MethodIds.CORE_START + 183;
    /** @see com.baidu.iptcore.CoreCellConfig#coreRefresh */
    int coreRefresh = MethodIds.CORE_START + 184;
    /** @see com.baidu.iptcore.CoreCellConfig#coreUnload */
    int coreUnload = MethodIds.CORE_START + 185;
    /** @see com.baidu.iptcore.CoreCellConfig#nnrankerRefresh */
    int nnrankerRefresh = MethodIds.CORE_START + 186;
    /** @see com.baidu.iptcore.CoreCellConfig#autoReplyRefresh */
    int autoReplyRefresh = MethodIds.CORE_START + 187;
    /** @see com.baidu.iptcore.CoreCellConfig#createUswordManager */
    int createUswordManager = MethodIds.CORE_START + 188;
    /** @see com.baidu.iptcore.CoreCellConfig#destroyUswordManager */
    int destroyUswordManager = MethodIds.CORE_START + 189;
    /** @see com.baidu.iptcore.CoreCellConfig#uswordGetCount */
    int uswordGetCount = MethodIds.CORE_START + 190;
    /** @see com.baidu.iptcore.CoreCellConfig#uswordGetStr */
    int uswordGetStr = MethodIds.CORE_START + 191;
    /** @see com.baidu.iptcore.CoreCellConfig#uswordGetAction */
    int uswordGetAction = MethodIds.CORE_START + 192;
    /** @see com.baidu.iptcore.CoreCellConfig#uswordDoAction */
    int uswordDoAction = MethodIds.CORE_START + 193;
    /** @see com.baidu.iptcore.CoreCellConfig#uswordGetCnWordCount */
    int uswordGetCnWordCount = MethodIds.CORE_START + 194;
    /** @see com.baidu.iptcore.CoreCellConfig#keywordFindVoiceLian */
    int keywordFindVoiceLian = MethodIds.CORE_START + 195;
    /** @see com.baidu.iptcore.CoreCellConfig#keywordFindVoiceEgg */
    int keywordFindVoiceEgg = MethodIds.CORE_START + 196;
    /** @see com.baidu.iptcore.CoreCellConfig#backupTraceLog */
    int backupTraceLog = MethodIds.CORE_START + 197;
    /** @see com.baidu.iptcore.CoreCellConfig#blockCloudUnis */
    int blockCloudUnis = MethodIds.CORE_START + 198;
    /** @see com.baidu.iptcore.CoreCellConfig#resetCloudBlack */
    int resetCloudBlack = MethodIds.CORE_START + 199;
    /** @see com.baidu.iptcore.CoreCellConfig#importOldDefWord */
    int importOldDefWord = MethodIds.CORE_START + 200;
    /** @see com.baidu.iptcore.CoreCellConfig#getContactNamesInPb */
    int getContactNamesInPb = MethodIds.CORE_START + 201;
    /** @see com.baidu.iptcore.CoreCellConfig#exportInputAssociateInfo */
    int exportInputAssociateInfo = MethodIds.CORE_START + 202;
    /** @see com.baidu.iptcore.CoreCellConfig#getKeyCountForMisTouch */
    int getKeyCountForMisTouch = MethodIds.CORE_START + 203;
    /** @see com.baidu.iptcore.CoreCellConfig#iptDictRebuildFromOldUsr2 */
    int iptDictRebuildFromOldUsr2 = MethodIds.CORE_START + 204;
    /** @see com.baidu.iptcore.CoreCellConfig#iptDictRebuildFromOldUe2 */
    int iptDictRebuildFromOldUe2 = MethodIds.CORE_START + 205;
    /** @see com.baidu.iptcore.CoreCellConfig#iptDictRebuildFromOldKeyword */
    int iptDictRebuildFromOldKeyword = MethodIds.CORE_START + 206;
    /** @see com.baidu.iptcore.CoreCellConfig#iptDictRebuildFromOldZyUsr */
    int iptDictRebuildFromOldZyUsr = MethodIds.CORE_START + 207;
    /** @see com.baidu.iptcore.CoreCellConfig#iptDictRebuildFromOldZy */
    int iptDictRebuildFromOldZy = MethodIds.CORE_START + 208;
    /** @see com.baidu.iptcore.CoreCellConfig#iptDictRebuildFromOldCangjie */
    int iptDictRebuildFromOldCangjie = MethodIds.CORE_START + 209;
    /** @see com.baidu.iptcore.CoreCellConfig#dctCellInstallByBuffer */
    int dctCellInstallByBuffer = MethodIds.CORE_START + 210;
    /** @see com.baidu.iptcore.CoreCellConfig#dctCellWordInDict */
    int dctCellWordInDict = MethodIds.CORE_START + 211;
    /** @see com.baidu.iptcore.CoreCellConfig#dctCellExportBuffer */
    int dctCellExportBuffer = MethodIds.CORE_START + 212;
    /** @see com.baidu.iptcore.CoreCellConfig#dctCellResetBuffer */
    int dctCellResetBuffer = MethodIds.CORE_START + 213;
    /** @see com.baidu.iptcore.ImeLib#getCoreVersion */
    int getCoreVersion = MethodIds.CORE_START + 214;
    /** @see com.baidu.iptcore.ImeLib#getCoreVersionStr */
    int getCoreVersionStr = MethodIds.CORE_START + 215;
    /** @see com.baidu.iptcore.ImeLib#getCz3DictGramVersion */
    int getCz3DictGramVersion = MethodIds.CORE_START + 216;
    /** @see com.baidu.iptcore.ImeLib#getCz3DictSysVersion */
    int getCz3DictSysVersion = MethodIds.CORE_START + 217;
    /** @see com.baidu.iptcore.ImeLib#getCz3DictCateVersion */
    int getCz3DictCateVersion = MethodIds.CORE_START + 218;
    /** @see com.baidu.iptcore.ImeLib#getCz5DownStatus */
    int getCz5DownStatus = MethodIds.CORE_START + 219;
    /** @see com.baidu.iptcore.ImeLib#getSlideDictVersion */
    int getSlideDictVersion = MethodIds.CORE_START + 220;
    /** @see com.baidu.iptcore.ImeLib#isNnrankerInstalled */
    int isNnrankerInstalled = MethodIds.CORE_START + 221;
    /** @see com.baidu.iptcore.ImeLib#getEnSysDictVersion */
    int getEnSysDictVersion = MethodIds.CORE_START + 222;
    /** @see com.baidu.iptcore.ImeLib#getUnloadedCzVersion */
    int getUnloadedCzVersion = MethodIds.CORE_START + 223;
    /** @see com.baidu.iptcore.ImeLib#getTraceLog */
    int getTraceLog = MethodIds.CORE_START + 224;
    /** @see com.baidu.iptcore.ImeLib#resetTraceLog */
    int resetTraceLog = MethodIds.CORE_START + 225;
    /** @see com.baidu.iptcore.ImeLib#applyPatch */
    int applyPatch = MethodIds.CORE_START + 226;
    /** @see com.baidu.iptcore.ImeLib#getMlmSoVersion */
    int getMlmSoVersion = MethodIds.CORE_START + 227;
    /** @see com.baidu.iptcore.ImeLib#getMlmDictVersion */
    int getMlmDictVersion = MethodIds.CORE_START + 228;
    /** @see com.baidu.iptcore.ImePad#IptCoreDutyInfo */
    int IptCoreDutyInfo = MethodIds.CORE_START + 229;
    /** @see com.baidu.iptcore.ImePad#onPrivateDutyInfo */
    int onPrivateDutyInfo = MethodIds.CORE_START + 230;
    /** @see com.baidu.iptcore.ImePad#updateCurrentPadId */
    int updateCurrentPadId = MethodIds.CORE_START + 231;
    /** @see com.baidu.iptcore.ImePad#dutyEntry */
    int dutyEntry = MethodIds.CORE_START + 232;
    /** @see com.baidu.iptcore.ImePad#updateTipState */
    int updateTipState = MethodIds.CORE_START + 233;
    /** @see com.baidu.iptcore.ImePad#keymapClean */
    int keymapClean = MethodIds.CORE_START + 234;
    /** @see com.baidu.iptcore.ImePad#keymapAddChar */
    int keymapAddChar = MethodIds.CORE_START + 235;
    /** @see com.baidu.iptcore.ImePad#actAIPadClick */
    int actAIPadClick = MethodIds.CORE_START + 236;
    /** @see com.baidu.iptcore.ImePad#actAIPadClick1 */
    int actAIPadClick1 = MethodIds.CORE_START + 237;
    /** @see com.baidu.iptcore.ImePad#actCustomInputAction */
    int actCustomInputAction = MethodIds.CORE_START + 238;
    /** @see com.baidu.iptcore.ImePad#actCandAction */
    int actCandAction = MethodIds.CORE_START + 239;
    /** @see com.baidu.iptcore.ImePad#actKeyClicked */
    int actKeyClicked = MethodIds.CORE_START + 240;
    /** @see com.baidu.iptcore.ImePad#actChangeShift */
    int actChangeShift = MethodIds.CORE_START + 241;
    /** @see com.baidu.iptcore.ImePad#actKeyClicked1 */
    int actKeyClicked1 = MethodIds.CORE_START + 242;
    /** @see com.baidu.iptcore.ImePad#actKeyClicked2 */
    int actKeyClicked2 = MethodIds.CORE_START + 243;
    /** @see com.baidu.iptcore.ImePad#actKeyClicked3 */
    int actKeyClicked3 = MethodIds.CORE_START + 244;
    /** @see com.baidu.iptcore.ImePad#actCandClicked */
    int actCandClicked = MethodIds.CORE_START + 245;
    /** @see com.baidu.iptcore.ImePad#actRareCandClicked */
    int actRareCandClicked = MethodIds.CORE_START + 246;
    /** @see com.baidu.iptcore.ImePad#actCandSelect */
    int actCandSelect = MethodIds.CORE_START + 247;
    /** @see com.baidu.iptcore.ImePad#actCandLongPress */
    int actCandLongPress = MethodIds.CORE_START + 248;
    /** @see com.baidu.iptcore.ImePad#actListClicked */
    int actListClicked = MethodIds.CORE_START + 249;
    /** @see com.baidu.iptcore.ImePad#actTabClicked */
    int actTabClicked = MethodIds.CORE_START + 250;
    /** @see com.baidu.iptcore.ImePad#getTabs */
    int getTabs = MethodIds.CORE_START + 251;
    /** @see com.baidu.iptcore.ImePad#actCandInfoClick */
    int actCandInfoClick = MethodIds.CORE_START + 252;
    /** @see com.baidu.iptcore.ImePad#actCandInfoCancel */
    int actCandInfoCancel = MethodIds.CORE_START + 253;
    /** @see com.baidu.iptcore.ImePad#actContactInsert */
    int actContactInsert = MethodIds.CORE_START + 254;
    /** @see com.baidu.iptcore.ImePad#actContactCancel */
    int actContactCancel = MethodIds.CORE_START + 255;
    /** @see com.baidu.iptcore.ImePad#actContactInfoSelect */
    int actContactInfoSelect = MethodIds.CORE_START + 256;
    /** @see com.baidu.iptcore.ImePad#actInputCursor */
    int actInputCursor = MethodIds.CORE_START + 257;
    /** @see com.baidu.iptcore.ImePad#actInputCursorLeft */
    int actInputCursorLeft = MethodIds.CORE_START + 258;
    /** @see com.baidu.iptcore.ImePad#actInputCursorRight */
    int actInputCursorRight = MethodIds.CORE_START + 259;
    /** @see com.baidu.iptcore.ImePad#actInputPop */
    int actInputPop = MethodIds.CORE_START + 260;
    /** @see com.baidu.iptcore.ImePad#actEditCursorChange */
    int actEditCursorChange = MethodIds.CORE_START + 261;
    /** @see com.baidu.iptcore.ImePad#actTrackStart */
    int actTrackStart = MethodIds.CORE_START + 262;
    /** @see com.baidu.iptcore.ImePad#actTrackMove */
    int actTrackMove = MethodIds.CORE_START + 263;
    /** @see com.baidu.iptcore.ImePad#actTrackEnd */
    int actTrackEnd = MethodIds.CORE_START + 264;
    /** @see com.baidu.iptcore.ImePad#actTrackEnd1 */
    int actTrackEnd1 = MethodIds.CORE_START + 265;
    /** @see com.baidu.iptcore.ImePad#actSugClick */
    int actSugClick = MethodIds.CORE_START + 266;
    /** @see com.baidu.iptcore.ImePad#actSugCardClick */
    int actSugCardClick = MethodIds.CORE_START + 267;
    /** @see com.baidu.iptcore.ImePad#actSugCardSelect */
    int actSugCardSelect = MethodIds.CORE_START + 268;
    /** @see com.baidu.iptcore.ImePad#importSymList */
    int importSymList = MethodIds.CORE_START + 269;
    /** @see com.baidu.iptcore.ImePad#padSetLock */
    int padSetLock = MethodIds.CORE_START + 270;
    /** @see com.baidu.iptcore.ImePad#padGetLock */
    int padGetLock = MethodIds.CORE_START + 271;
    /** @see com.baidu.iptcore.ImePad#padSetSymFilter */
    int padSetSymFilter = MethodIds.CORE_START + 272;
    /** @see com.baidu.iptcore.ImePad#actShiftLongDown */
    int actShiftLongDown = MethodIds.CORE_START + 273;
    /** @see com.baidu.iptcore.ImePad#actShiftLongUp */
    int actShiftLongUp = MethodIds.CORE_START + 274;
    /** @see com.baidu.iptcore.ImePad#actCorrectVoiceData */
    int actCorrectVoiceData = MethodIds.CORE_START + 275;
    /** @see com.baidu.iptcore.ImePad#actCorrectVoiceSend */
    int actCorrectVoiceSend = MethodIds.CORE_START + 276;
    /** @see com.baidu.iptcore.ImePad#actCheckClip */
    int actCheckClip = MethodIds.CORE_START + 277;
    /** @see com.baidu.iptcore.ImePad#actAdjustEmojiRelation */
    int actAdjustEmojiRelation = MethodIds.CORE_START + 278;
    /** @see com.baidu.iptcore.ImePad#sendPadEvent */
    int sendPadEvent = MethodIds.CORE_START + 279;
    /** @see com.baidu.iptcore.ImePad#switchKeyboard */
    int switchKeyboard = MethodIds.CORE_START + 280;
    /** @see com.baidu.iptcore.ImePad#getCurrentPadId */
    int getCurrentPadId = MethodIds.CORE_START + 281;
    /** @see com.baidu.iptcore.ImePad#getPadId */
    int getPadId = MethodIds.CORE_START + 282;
    /** @see com.baidu.iptcore.ImePad#actCnEnKey */
    int actCnEnKey = MethodIds.CORE_START + 283;
    /** @see com.baidu.iptcore.ImePad#actRareChaiZiHwKey */
    int actRareChaiZiHwKey = MethodIds.CORE_START + 284;
    /** @see com.baidu.iptcore.ImePad#setDefaultPad */
    int setDefaultPad = MethodIds.CORE_START + 285;
    /** @see com.baidu.iptcore.ImePad#getStateValue */
    int getStateValue = MethodIds.CORE_START + 286;
    /** @see com.baidu.iptcore.ImePad#getHwPinyinStr */
    int getHwPinyinStr = MethodIds.CORE_START + 287;
    /** @see com.baidu.iptcore.ImePad#getCandCount */
    int getCandCount = MethodIds.CORE_START + 288;
    /** @see com.baidu.iptcore.ImePad#getAIPadLoadingState */
    int getAIPadLoadingState = MethodIds.CORE_START + 289;
    /** @see com.baidu.iptcore.ImePad#getAIIconInLXNeedShow */
    int getAIIconInLXNeedShow = MethodIds.CORE_START + 290;
    /** @see com.baidu.iptcore.ImePad#getAIPadTab */
    int getAIPadTab = MethodIds.CORE_START + 291;
    /** @see com.baidu.iptcore.ImePad#getAIPadState */
    int getAIPadState = MethodIds.CORE_START + 292;
    /** @see com.baidu.iptcore.ImePad#getAIPadCnt */
    int getAIPadCnt = MethodIds.CORE_START + 293;
    /** @see com.baidu.iptcore.ImePad#getAIPadItem */
    int getAIPadItem = MethodIds.CORE_START + 294;
    /** @see com.baidu.iptcore.ImePad#getAIPadOriginText */
    int getAIPadOriginText = MethodIds.CORE_START + 295;
    /** @see com.baidu.iptcore.ImePad#getAIPabIsAutoOpen */
    int getAIPabIsAutoOpen = MethodIds.CORE_START + 296;
    /** @see com.baidu.iptcore.ImePad#getAICand */
    int getAICand = MethodIds.CORE_START + 297;
    /** @see com.baidu.iptcore.ImePad#getAICandIconState */
    int getAICandIconState = MethodIds.CORE_START + 298;
    /** @see com.baidu.iptcore.ImePad#getAICorrectInlineInfo */
    int getAICorrectInlineInfo = MethodIds.CORE_START + 299;
    /** @see com.baidu.iptcore.ImePad#checkHitBlackList */
    int checkHitBlackList = MethodIds.CORE_START + 300;
    /** @see com.baidu.iptcore.ImePad#encryptionAICardRequestData */
    int encryptionAICardRequestData = MethodIds.CORE_START + 301;
    /** @see com.baidu.iptcore.ImePad#decryptAICardResponseData */
    int decryptAICardResponseData = MethodIds.CORE_START + 302;
    /** @see com.baidu.iptcore.ImePad#getCandItem */
    int getCandItem = MethodIds.CORE_START + 303;
    /** @see com.baidu.iptcore.ImePad#getAiCandItems */
    int getAiCandItems = MethodIds.CORE_START + 304;
    /** @see com.baidu.iptcore.ImePad#loadCandFromCore */
    int loadCandFromCore = MethodIds.CORE_START + 305;
    /** @see com.baidu.iptcore.ImePad#getRareCandCount */
    int getRareCandCount = MethodIds.CORE_START + 306;
    /** @see com.baidu.iptcore.ImePad#getRareCandItem */
    int getRareCandItem = MethodIds.CORE_START + 307;
    /** @see com.baidu.iptcore.ImePad#loadRareCandFromCore */
    int loadRareCandFromCore = MethodIds.CORE_START + 308;
    /** @see com.baidu.iptcore.ImePad#getInputShow */
    int getInputShow = MethodIds.CORE_START + 309;
    /** @see com.baidu.iptcore.ImePad#getInputShow1 */
    int getInputShow1 = MethodIds.CORE_START + 310;
    /** @see com.baidu.iptcore.ImePad#getListCount */
    int getListCount = MethodIds.CORE_START + 311;
    /** @see com.baidu.iptcore.ImePad#getListItem */
    int getListItem = MethodIds.CORE_START + 312;
    /** @see com.baidu.iptcore.ImePad#getSugCount */
    int getSugCount = MethodIds.CORE_START + 313;
    /** @see com.baidu.iptcore.ImePad#getSugAt */
    int getSugAt = MethodIds.CORE_START + 314;
    /** @see com.baidu.iptcore.ImePad#getSugCardCount */
    int getSugCardCount = MethodIds.CORE_START + 315;
    /** @see com.baidu.iptcore.ImePad#getSugCardAt */
    int getSugCardAt = MethodIds.CORE_START + 316;
    /** @see com.baidu.iptcore.ImePad#getSugSelectedPosition */
    int getSugSelectedPosition = MethodIds.CORE_START + 317;
    /** @see com.baidu.iptcore.ImePad#getSugCardSelectedPosition */
    int getSugCardSelectedPosition = MethodIds.CORE_START + 318;
    /** @see com.baidu.iptcore.ImePad#getSugState */
    int getSugState = MethodIds.CORE_START + 319;
    /** @see com.baidu.iptcore.ImePad#getPyHotLetter */
    int getPyHotLetter = MethodIds.CORE_START + 320;
    /** @see com.baidu.iptcore.ImePad#actCurSugClose */
    int actCurSugClose = MethodIds.CORE_START + 321;
    /** @see com.baidu.iptcore.ImePad#userTraceStartPadKeyLayout */
    int userTraceStartPadKeyLayout = MethodIds.CORE_START + 322;
    /** @see com.baidu.iptcore.ImePad#userTraceFinishPadKeyLayout */
    int userTraceFinishPadKeyLayout = MethodIds.CORE_START + 323;
    /** @see com.baidu.iptcore.ImePad#userTraceWrite */
    int userTraceWrite = MethodIds.CORE_START + 324;
    /** @see com.baidu.iptcore.ImePad#userVoiceInputLog */
    int userVoiceInputLog = MethodIds.CORE_START + 325;
    /** @see com.baidu.iptcore.ImePad#getCandInfoCount */
    int getCandInfoCount = MethodIds.CORE_START + 326;
    /** @see com.baidu.iptcore.ImePad#getCandInfo */
    int getCandInfo = MethodIds.CORE_START + 327;
    /** @see com.baidu.iptcore.ImePad#getContactCount */
    int getContactCount = MethodIds.CORE_START + 328;
    /** @see com.baidu.iptcore.ImePad#getContactItem */
    int getContactItem = MethodIds.CORE_START + 329;
    /** @see com.baidu.iptcore.ImePad#getTriggerWordCount */
    int getTriggerWordCount = MethodIds.CORE_START + 330;
    /** @see com.baidu.iptcore.ImePad#getTriggerWordItemItem */
    int getTriggerWordItemItem = MethodIds.CORE_START + 331;
    /** @see com.baidu.iptcore.ImePad#getMapTriggerWordCount */
    int getMapTriggerWordCount = MethodIds.CORE_START + 332;
    /** @see com.baidu.iptcore.ImePad#getMapTriggerWordItemItem */
    int getMapTriggerWordItemItem = MethodIds.CORE_START + 333;
    /** @see com.baidu.iptcore.ImePad#getTriggerWordItemsId */
    int getTriggerWordItemsId = MethodIds.CORE_START + 334;
    /** @see com.baidu.iptcore.ImePad#sendLocationEvent */
    int sendLocationEvent = MethodIds.CORE_START + 335;
    /** @see com.baidu.iptcore.ImePad#getCandContext */
    int getCandContext = MethodIds.CORE_START + 336;
    /** @see com.baidu.iptcore.ImePad#getHwSmartDelayTime */
    int getHwSmartDelayTime = MethodIds.CORE_START + 337;
    /** @see com.baidu.iptcore.ImePad#getEgg */
    int getEgg = MethodIds.CORE_START + 338;
    /** @see com.baidu.iptcore.ImePad#getSrvCloudWhiteVer */
    int getSrvCloudWhiteVer = MethodIds.CORE_START + 339;
    /** @see com.baidu.iptcore.ImePad#getSugType */
    int getSugType = MethodIds.CORE_START + 340;
    /** @see com.baidu.iptcore.ImePad#getSugActionType */
    int getSugActionType = MethodIds.CORE_START + 341;
    /** @see com.baidu.iptcore.ImePad#getSugSourceId */
    int getSugSourceId = MethodIds.CORE_START + 342;
    /** @see com.baidu.iptcore.ImePad#getSugSourceMsg */
    int getSugSourceMsg = MethodIds.CORE_START + 343;
    /** @see com.baidu.iptcore.ImePad#setPadLayout */
    int setPadLayout = MethodIds.CORE_START + 344;
    /** @see com.baidu.iptcore.ImePad#setPadKeyPos */
    int setPadKeyPos = MethodIds.CORE_START + 345;
    /** @see com.baidu.iptcore.ImePad#getTouchedKey */
    int getTouchedKey = MethodIds.CORE_START + 346;
    /** @see com.baidu.iptcore.ImePad#getTipState */
    int getTipState = MethodIds.CORE_START + 347;
    /** @see com.baidu.iptcore.ImePad#getTipStates */
    int getTipStates = MethodIds.CORE_START + 348;
    /** @see com.baidu.iptcore.ImePad#actKeyClickInternal */
    int actKeyClickInternal = MethodIds.CORE_START + 349;
    /** @see com.baidu.iptcore.ImePad#actKeyClickInternal1 */
    int actKeyClickInternal1 = MethodIds.CORE_START + 350;
    /** @see com.baidu.iptcore.ImePad#actKeyClickInternal2 */
    int actKeyClickInternal2 = MethodIds.CORE_START + 351;
    /** @see com.baidu.iptcore.ImePad#writableDictReset */
    int writableDictReset = MethodIds.CORE_START + 352;
    /** @see com.baidu.iptcore.ImePad#execCallback */
    int execCallback = MethodIds.CORE_START + 353;
    /** @see com.baidu.iptcore.ImePad#actAiFontGeneratedNotify */
    int actAiFontGeneratedNotify = MethodIds.CORE_START + 354;
    /** @see com.baidu.iptcore.ImePad#getIntentStyle */
    int getIntentStyle = MethodIds.CORE_START + 355;
    /** @see com.baidu.iptcore.ImePad#aiFontRecoPoint */
    int aiFontRecoPoint = MethodIds.CORE_START + 356;
    /** @see com.baidu.iptcore.ImePad#loadAiFontHwModel */
    int loadAiFontHwModel = MethodIds.CORE_START + 357;
    /** @see com.baidu.iptcore.ImePad#unLoadAiFontHwModel */
    int unLoadAiFontHwModel = MethodIds.CORE_START + 358;
    /** @see com.baidu.iptcore.ImePad#aiFontHwModelVersion */
    int aiFontHwModelVersion = MethodIds.CORE_START + 359;
    /** @see com.baidu.iptcore.ImePad#actToolbarClick */
    int actToolbarClick = MethodIds.CORE_START + 360;
    /** @see com.baidu.iptcore.ImePad#getInlineShow */
    int getInlineShow = MethodIds.CORE_START + 361;
    /** @see com.baidu.iptcore.ImePad#enableGaussTrain */
    int enableGaussTrain = MethodIds.CORE_START + 362;
    /** @see com.baidu.iptcore.ImePad#getEncryptVersion */
    int getEncryptVersion = MethodIds.CORE_START + 363;
    /** @see com.baidu.iptcore.ImePad#getEncryptB64Version */
    int getEncryptB64Version = MethodIds.CORE_START + 364;
    /** @see com.baidu.iptcore.ImePad#b64Encode */
    int b64Encode = MethodIds.CORE_START + 365;
    /** @see com.baidu.iptcore.ImePad#b64Decode */
    int b64Decode = MethodIds.CORE_START + 366;
    /** @see com.baidu.iptcore.ImePad#aesEncrypt */
    int aesEncrypt = MethodIds.CORE_START + 367;
    /** @see com.baidu.iptcore.ImePad#aesEncryptV2 */
    int aesEncryptV2 = MethodIds.CORE_START + 368;
    /** @see com.baidu.iptcore.ImePad#aesDecrypt */
    int aesDecrypt = MethodIds.CORE_START + 369;
    /** @see com.baidu.iptcore.ImePad#aesB64Encrypt */
    int aesB64Encrypt = MethodIds.CORE_START + 370;
    /** @see com.baidu.iptcore.ImePad#aesB64EncryptV2 */
    int aesB64EncryptV2 = MethodIds.CORE_START + 371;
    /** @see com.baidu.iptcore.ImePad#aesB64Decrypt */
    int aesB64Decrypt = MethodIds.CORE_START + 372;
    /** @see com.baidu.iptcore.ImePad#rsaEncrypt */
    int rsaEncrypt = MethodIds.CORE_START + 373;
    /** @see com.baidu.iptcore.ImePad#rsaDecrypt */
    int rsaDecrypt = MethodIds.CORE_START + 374;
    /** @see com.baidu.iptcore.ImeCoreManager#ImeCoreManager */
    int ImeCoreManager = MethodIds.CORE_START + 375;
    /** @see com.baidu.iptcore.ImeCoreManager#open */
    int open = MethodIds.CORE_START + 376;
    /** @see com.baidu.iptcore.ImeCoreManager#refresh */
    int refresh = MethodIds.CORE_START + 377;
    /** @see com.baidu.iptcore.ImeCoreManager#refreshCore */
    int refreshCore = MethodIds.CORE_START + 378;
    /** @see com.baidu.iptcore.ImeCoreManager#refreshCoreInternal */
    int refreshCoreInternal = MethodIds.CORE_START + 379;
    /** @see com.baidu.iptcore.ImeCoreManager#initImePad */
    int initImePad = MethodIds.CORE_START + 380;
    /** @see com.baidu.iptcore.ImeCoreManager#openCore */
    int openCore = MethodIds.CORE_START + 381;
    /** @see com.baidu.iptcore.ImeCoreManager#openCoreInternal */
    int openCoreInternal = MethodIds.CORE_START + 382;
    /** @see com.baidu.iptcore.ImeCoreManager#initNativeTrace */
    int initNativeTrace = MethodIds.CORE_START + 383;
    /** @see com.baidu.iptcore.ImeCoreManager#beforeOpenCore */
    int beforeOpenCore = MethodIds.CORE_START + 384;
    /** @see com.baidu.iptcore.ImeCoreManager#close */
    int close = MethodIds.CORE_START + 385;
    /** @see com.baidu.iptcore.ImeCoreManager#closeCore */
    int closeCore = MethodIds.CORE_START + 386;
    /** @see com.baidu.iptcore.ImeCoreManager#closeCoreInternal */
    int closeCoreInternal = MethodIds.CORE_START + 387;
    /** @see com.baidu.iptcore.ImeCoreManager#getPad */
    int getPad = MethodIds.CORE_START + 388;
    /** @see com.baidu.iptcore.ImeCoreManager#getConfig */
    int getConfig = MethodIds.CORE_START + 389;
    /** @see com.baidu.iptcore.ImeCoreManager#getCellConfig */
    int getCellConfig = MethodIds.CORE_START + 390;
    /** @see com.baidu.iptcore.ImeCoreManager#getLib */
    int getLib = MethodIds.CORE_START + 391;
    /** @see com.baidu.iptcore.ImeCoreManager#getProtCode */
    int getProtCode = MethodIds.CORE_START + 392;
    /** @see com.baidu.iptcore.ImeCoreManager#checkFileMD5 */
    int checkFileMD5 = MethodIds.CORE_START + 393;
    /** @see com.baidu.iptcore.ImePad#setCallback */
    int setCallback = MethodIds.CORE_START + 394;
    /**  actKeyClicked from CoreThread */
    int actKeyClicked_CoreThread = MethodIds.CORE_START + 395;
    /** @see com.baidu.iptcore.CoreNative#coreNativeStart */
    int coreNativeStart = MethodIds.CORE_START + 2048;

}
