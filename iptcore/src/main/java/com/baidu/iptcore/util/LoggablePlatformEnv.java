package com.baidu.iptcore.util;

import android.support.annotation.NonNull;

import com.baidu.iptcore.ImePlatformEnv;
import com.baidu.iptcore.info.IptAppMsgInfo;

import java.util.Arrays;

public class LoggablePlatformEnv implements ImePlatformEnv {

    private final ImePlatformEnv platformEnv;

    public LoggablePlatformEnv(@NonNull ImePlatformEnv platformEnv) {
        this.platformEnv = platformEnv;
    }

    @Override
    public int findApp(IptAppMsgInfo[] appList) {
        long a = System.currentTimeMillis();
        int ret = platformEnv.findApp(appList);
        MethodTracer.recordEnvCallbackMethod(
                "findApp, list=" + Arrays.toString(appList) + ", " + "ret=" + ret,
                (System.currentTimeMillis() - a));
        return ret;
    }

    @Override
    public boolean requestUrlResource(String[] urlList, long callback, int tag) {
        long a = System.currentTimeMillis();
        boolean ret = platformEnv.requestUrlResource(urlList, callback, tag);
        MethodTracer.recordEnvCallbackMethod(
                "requestUrlResource, list=" + Arrays.toString(urlList)
                        + ", callback=" + callback
                        + ", tag=" + tag
                        + ", ret=" + ret,
                (System.currentTimeMillis() - a));
        return ret;
    }

    @Override
    public boolean isAllowAiPadAutoOpen(int tabType) {
        long a = System.currentTimeMillis();
        boolean ret = platformEnv.isAllowAiPadAutoOpen(tabType);
        MethodTracer.recordEnvCallbackMethod(
                "isAllowAiPadAutoOpen, tabType=" + tabType,
                (System.currentTimeMillis() - a));
        return ret;
    }

    @Override
    public boolean isAllowCloudCampaignShow() {
        long a = System.currentTimeMillis();
        boolean ret = platformEnv.isAllowCloudCampaignShow();
        MethodTracer.recordEnvCallbackMethod(
                "isAllowCloudCampaignShow", (System.currentTimeMillis() - a));
        return ret;
    }

    @Override
    public boolean isAllowCloudAdShow() {
        long a = System.currentTimeMillis();
        boolean ret = platformEnv.isAllowCloudAdShow();
        MethodTracer.recordEnvCallbackMethod(
                "isAllowCloudAdShow", (System.currentTimeMillis() - a));
        return ret;
    }

    @Override
    public boolean isAllowMinorCandHighEQShow() {
        long a = System.currentTimeMillis();
        boolean ret = platformEnv.isAllowMinorCandHighEQShow();
        MethodTracer.recordEnvCallbackMethod(
                "isAllowMinorCandHighEQShow", (System.currentTimeMillis() - a));
        return ret;
    }

    @Override
    public boolean isCloudCampaignFinalyShow(String schemaType, String schemaInfo, long userVipCondition, long vipExpireDay, long vipExpireDayTo) {
        long a = System.currentTimeMillis();
        boolean ret = platformEnv.isCloudCampaignFinalyShow(schemaType, schemaInfo, userVipCondition, vipExpireDay, vipExpireDayTo);
        MethodTracer.recordEnvCallbackMethod(
                "isCloudCampaignFinalyShow", (System.currentTimeMillis() - a));
        return ret;
    }

    @Override
    public boolean isCommonTriggerFinallyShow(String validPeople, int belongType) {
        long a = System.currentTimeMillis();
        boolean ret = platformEnv.isCommonTriggerFinallyShow(validPeople, belongType);
        MethodTracer.recordEnvCallbackMethod(
                "isCommonTriggerFinallyShow", (System.currentTimeMillis() - a));
        return ret;
    }

    @Override
    public String getEditBeforeCursor(int maxLength) {
        long a = System.currentTimeMillis();
        String text = platformEnv.getEditBeforeCursor(maxLength);
        MethodTracer.recordEnvCallbackMethod(
                "getEditBeforeCursor(" + maxLength + "): " + text,
                (System.currentTimeMillis() - a));
        return text;
    }

    @Override
    public String getEditAfterCursor(int maxLength) {
        long a = System.currentTimeMillis();
        String text = platformEnv.getEditAfterCursor(maxLength);
        MethodTracer.recordEnvCallbackMethod(
                "getEditAfterCursor(" + maxLength + "): " + text,
                (System.currentTimeMillis() - a));
        return text;
    }

    @Override
    public String getEditSelection(int maxLength) {
        long a = System.currentTimeMillis();
        String sel = platformEnv.getEditSelection(maxLength);
        MethodTracer.recordEnvCallbackMethod(
                "getEditSelection: " + sel,
                (System.currentTimeMillis() - a));
        return sel;
    }

    @Override
    public String getEditTextDialogue(int maxLength) {
        long a = System.currentTimeMillis();
        String sel = platformEnv.getEditTextDialogue(maxLength);
        MethodTracer.recordEnvCallbackMethod(
                "getEditTextDialogue: " + sel,
                (System.currentTimeMillis() - a));
        return sel;
    }

    @Override
    public int getEnergyValue() {
        long a = System.currentTimeMillis();
        int value = platformEnv.getEnergyValue();
        MethodTracer.recordEnvCallbackMethod(
                "getEnergyValue: " + value,
                (System.currentTimeMillis() - a));
        return value;
    }

    @Override
    public void willEnterPad(int fromPadId, int toPadId) {
        long a = System.currentTimeMillis();
        platformEnv.willEnterPad(fromPadId, toPadId);
        MethodTracer.recordEnvCallbackMethod(
                "willEnterPad, from=" + fromPadId + ", to=" + toPadId,
                (System.currentTimeMillis() - a));
    }

    @Override
    public void didEnterPad(int fromPadId, int toPadId) {
        long a = System.currentTimeMillis();
        platformEnv.didEnterPad(fromPadId, toPadId);
        MethodTracer.recordEnvCallbackMethod(
                "didEnterPad, from=" + fromPadId + ", to=" + toPadId,
                (System.currentTimeMillis() - a));
    }


    @Override
    public boolean hasInstallApp(String packageName) {
        long a = System.currentTimeMillis();
        boolean ret = platformEnv.hasInstallApp(packageName);
        MethodTracer.recordEnvCallbackMethod("hasInstallApp", (System.currentTimeMillis() - a));
        return ret;
    }
}
