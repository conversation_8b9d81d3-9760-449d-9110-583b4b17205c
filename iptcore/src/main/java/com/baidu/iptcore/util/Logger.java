package com.baidu.iptcore.util;

import android.support.annotation.Keep;
import android.util.Log;

import com.baidu.util.Base64Encoder;

/**
 * 简单的Log打印。
 * Created by cdf on 17/8/1.
 */

public class Logger {
    
    public static final String TAG = "iptcore";

    /**
     * 取消log信息加密
     */
    public static boolean isLogEncodedDisable;

    /**
     * 是否开启log
     */
    private static boolean logOpen;
    /**
     * 打印适配
     */
    private static LogAdapter printer;

    public static void setLogOpen(boolean enableLog) {
        logOpen = enableLog;
    }

    public static void disableLogEncode(boolean disable) {
        isLogEncodedDisable = disable;
    }

    public static void init(LogAdapter logAdapter) {
        if (logAdapter == null) {
            logAdapter = new AndroidLogAdapter();
        }
        printer = logAdapter;
    }

    public static void v(String message) {
        if (!logOpen) {
            return;
        }
        printer.v(TAG, encode(message));
    }

    public static void v(String tag, String msg, Object... args) {
        if (!logOpen) {
            return;
        }
        printer.v(tag, encode(String.format(msg, args)));
    }

    public static void d(String message) {
        if (!logOpen) {
            return;
        }
        printer.d(TAG, encode(message));
    }

    public static void d(String tag, String message, Object... args) {
        if (!logOpen) {
            return;
        }
        printer.d(tag, encode(String.format(message, args)));
    }

    public static void i(String message) {
        if (!logOpen) {
            return;
        }
        printer.i(TAG, encode(message));
    }

    public static void i(String tag, String message, Object... args) {
        if (!logOpen) {
            return;
        }
        printer.i(tag, encode(String.format(message, args)));
    }

    // WARN

    public static void w(String message) {
        if (!logOpen) {
            return;
        }
        printer.w(TAG, encode(message));
    }

    public static void w(String tag, String message, Object... args) {
        if (!logOpen) {
            return;
        }
        printer.w(tag, encode(String.format(message, args)));
    }

    public static void e(String message) {
        if (!logOpen) {
            return;
        }
        printer.e(TAG, encode(message));
    }

    public static void e(String tag, String message, Object... args) {
        if (!logOpen) {
            return;
        }
        printer.e(tag, encode(String.format(message, args)));
    }

    public static void printStackTrace(Throwable e) {
        if (!logOpen) {
            return;
        }
        e(Log.getStackTraceString(e));
    }

    @Keep
    public static void onJniLogWrite(String message) {
        if (!logOpen) {
            return;
        }
        printer.onJniLogWrite(TAG, message);
    }

    private static String encode(String message) {
        if (!isLogEncodedDisable && message != null) {
            message = Base64Encoder.B64Encode(message, "UTF-8");
        }
        return message;
    }


    private static class AndroidLogAdapter implements LogAdapter {
        @Override
        public void v(String tag, String message) {
            Log.v(tag, message);
        }

        @Override
        public void d(String tag, String message) {
            Log.d(tag, message);
        }

        @Override
        public void i(String tag, String message) {
            Log.i(tag, message);
        }

        @Override
        public void w(String tag, String message) {
            Log.w(tag, message);
        }

        @Override
        public void e(String tag, String message) {
            Log.e(tag, message);
        }

        @Override
        public void onJniLogWrite(String tag, String message) {
            Log.i(tag, message);
        }
    }
}
