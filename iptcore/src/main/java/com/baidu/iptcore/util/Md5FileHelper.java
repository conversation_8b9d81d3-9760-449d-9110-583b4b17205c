package com.baidu.iptcore.util;

import android.support.annotation.Keep;

import com.baidu.util.Md5Utils;

import java.io.File;

/**
 * 用于获取文件 md5 值的内核帮助类
 */
@Keep
public class Md5FileHelper {
    /**
     * 获取文件的小写 md5 值
     * 注意：此方法被内核 cz5down 后下发功能调用，若此方法修改则需要修改内核 cz5down 后下发 jni 层代码
     * todo 此方法后期考虑移动到 imebase 的 Md5Utils 中
     * @param filePath
     * @return
     */
    @Keep
    public static String md5(String filePath) {
        return Md5Utils.md5String(
                Md5Utils.md5Data(new File(filePath))
        ).toLowerCase();
    }
}
