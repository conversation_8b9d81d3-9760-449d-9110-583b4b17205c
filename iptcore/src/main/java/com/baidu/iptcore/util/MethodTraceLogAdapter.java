package com.baidu.iptcore.util;

/**
 * 内核接口耗时日志打印适配器
 */
public interface MethodTraceLogAdapter {

    /**
     * 进入内核接口
     *
     * @param message   内核接口信息
     * @param startTime 开始时间
     */
    void enterCoreMethod(String message, long startTime);

    /**
     * 退出内核接口
     *
     * @param message      内核接口信息
     * @param cost         耗时
     * @param endTime      接口结束的时间
     * @param nowStateInfo 当前状态信息
     */
    void exitCoreMethod(String message, long cost, long endTime, ExtraInfo nowStateInfo);

    /**
     * 附加信息
     */
    class ExtraInfo {
        /**
         * 当前面板
         */
        private String padId;
        /**
         * 当前的input内容
         */
        private String inputStr;
        /**
         * 首个候选词
         */
        private String firstWord;

        /**
         * 当前的接口中，内核回调的耗时
         */
        private long nowEnvCost = 0;
        /**
         * 当前接口中，数据组装的耗时
         */
        private long nowDataCost = 0;
        /**
         * 当前接口中，按键点击的耗时
         */
        private long nowKeyCost = 0;

        public void reset() {
            nowEnvCost = 0;
            nowKeyCost = 0;
            nowDataCost = 0;
            inputStr = null;
            firstWord = null;
        }

        public void appendEnvCost(long cost) {
            nowEnvCost += cost;
        }

        public void appendDataCost(long cost) {
            nowDataCost += cost;
        }

        public void appendKeyClickCost(long cost) {
            nowKeyCost += cost;
        }

        public void setInputStr(String inputStr) {
            this.inputStr = inputStr;
        }

        public void setFirstWord(String firstWord) {
            this.firstWord = firstWord;
        }

        public void setPadId(String padId) {
            this.padId = padId;
        }

        public String inputStr() {
            return inputStr;
        }

        public String firstWord() {
            return firstWord;
        }

        public long envCost() {
            return nowEnvCost;
        }

        public long dataCost() {
            return nowDataCost;
        }

        public long keyCost() {
            return nowKeyCost;
        }

        public String padId() {
            return padId;
        }
    }
}
