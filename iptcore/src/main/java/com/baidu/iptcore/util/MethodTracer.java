package com.baidu.iptcore.util;

import android.content.Context;
import android.os.Looper;
import android.os.Trace;
import android.text.TextUtils;

import com.baidu.iptcore.util.profiler.DefaultTraceProfilerLogAdapter;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 内核接口耗时监控
 */
public class MethodTracer {

    /**
     * 是否允许SysTrace通过标签分析耗时
     */
    private static final boolean ENABLE_TRACING = false;
    /**
     * 耗时监控是否打开
     */
    private static boolean isTraceOpen = false;

    /**
     * 当前内核接口起始时间
     */
    private static long startTime = 0;

    /**
     * 当前内核接口名称
     */
    private static String nowMethodName = null;

    /**
     * 当前接口产生的数据信息
     */
    private static MethodTraceLogAdapter.ExtraInfo nowStateInfo = new MethodTraceLogAdapter.ExtraInfo();

    /**
     * 方法耗时打印
     */
    private static List<MethodTraceLogAdapter> printers = new ArrayList<>();

    /**
     * 方法名转 ids
     */
    private static final Map<String, String> IDS_MAPPINGS = new HashMap<>();

    private static final long IC_GET_TIMEOUT = 50;

    public static void init(Context context, boolean traceOpen, boolean profilerOpen,
                            MethodTraceLogAdapter logAdapter, Looper coreThreadLooper) {
        isTraceOpen = traceOpen;
        printers.clear();
        if (profilerOpen) {
            printers.add(new DefaultTraceProfilerLogAdapter(context, coreThreadLooper));
        }
        if (logAdapter == null) {
            logAdapter = new DefaultLogAdapter();
        }
        printers.add(logAdapter);
        initIdsMapping();
    }

    private static void initIdsMapping() {
        Field[] fields = Ids.class.getDeclaredFields();
        for (Field value : fields) {
            try {
                Object obj = value.get(null);
                if (obj != null) {
                    IDS_MAPPINGS.put(value.getName(), obj.toString());
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 内核接口开始执行
     */
    public static void startCoreMethod() {
        if (!isTraceOpen) {
            return;
        }
        startTime = System.currentTimeMillis();
        nowMethodName = null;
        nowStateInfo.reset();

//        NativeTraceHandler.getInstance().reset();
    }

    /**
     * 记录内核接口名称和参数
     */
    public static void recordCoreMethod(String methodName) {
        if (startTime > 0) {
            methodName = shortenMethodName(methodName);
            nowMethodName = methodName;
            dispatchEnterCoreMethod(methodName, startTime);

            if (ENABLE_TRACING) {
                if (!TextUtils.isEmpty(nowMethodName)) {
                    Trace.beginSection(nowMethodName);
                }
            }
        }
    }

    private static String shortenMethodName(String methodName) {
        if (Config.isShortLogMode()) {
            String prefix = methodName;
            if (methodName.contains(":")) {
                prefix = methodName.substring(0, methodName.indexOf(":"));
            }
            String id = IDS_MAPPINGS.get(prefix);
            if (id != null) {
                methodName = methodName.replace(prefix, id);
            }
        }
        return methodName;
    }

    public static String getNowMethodName() {
        return nowMethodName;
    }

    static void recordEnvCallbackMethod(String message, long cost) {
        if (!isTraceOpen) {
            return;
        }
        if (!Config.isShortLogMode() || cost > IC_GET_TIMEOUT) {
            Logger.i(message + "(cost: " + cost + ")");
        }
        nowStateInfo.appendEnvCost(cost);
    }

    /**
     * 记录数据组装耗时
     */
    public static void recordDataMethod(long cost, String... stateInfo) {
        nowStateInfo.appendDataCost(cost);

        if (stateInfo != null && stateInfo.length >= 3) {
            nowStateInfo.setInputStr(stateInfo[0]);
            nowStateInfo.setFirstWord(stateInfo[1]);
            nowStateInfo.setPadId(stateInfo[2]);
        }
    }

    /**
     * 记录按键输入耗时
     */
    public static void recordKeyClickMethod(long cost) {
        nowStateInfo.appendKeyClickCost(cost);
    }

    /**
     * 内核接口结束执行
     */
    public static void stopCoreMethod() {
        if (!isTraceOpen) {
            return;
        }
        if (startTime > 0 && !TextUtils.isEmpty(nowMethodName)) {
            long endTime = System.currentTimeMillis();
            long cost = (endTime - startTime);
            dispatchExitCoreMethod(nowMethodName , cost, endTime, nowStateInfo);

            if (ENABLE_TRACING) {
                Trace.endSection();
            }
        }
        startTime = 0;
        nowMethodName = null;
        nowStateInfo.reset();
    }

    /**
     * 接口耗时是否开启记录
     */
    public static boolean isTraceOpen() {
        return isTraceOpen;
    }

    private static void dispatchEnterCoreMethod(String methodName, long startTime) {
        for (MethodTraceLogAdapter printer : printers) {
            printer.enterCoreMethod(methodName, startTime);
        }
    }

    private static void dispatchExitCoreMethod(String nowMethodName, long cost, long endTime,
                                               MethodTraceLogAdapter.ExtraInfo nowStateInfo) {
        for (MethodTraceLogAdapter printer : printers) {
            printer.exitCoreMethod(nowMethodName, cost, endTime, nowStateInfo);
        }
    }

    private static class DefaultLogAdapter implements MethodTraceLogAdapter {
        @Override
        public void enterCoreMethod(String methodName, long startTime) {
            Logger.i(methodName);
        }

        @Override
        public void exitCoreMethod(String message, long cost, long endTime,
                                   ExtraInfo nowStateInfo) {
            // do nothing
        }
    }
}
