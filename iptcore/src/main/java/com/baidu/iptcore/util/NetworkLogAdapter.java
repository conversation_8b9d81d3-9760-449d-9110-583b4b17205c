package com.baidu.iptcore.util;

import java.io.IOException;

/**
 * 内核网络信息的日志打印
 */
public interface NetworkLogAdapter {

    /**
     * 记录http失败信息
     * @param url 链接
     * @param e 异常信息
     */
    void logHttpFail(String url, IOException e);

    /**
     * 记录http返回信息
     * @param url 链接
     * @param code 返回code
     * @param cost 耗时
     */
    void logHttpResponse(String url, int code, long cost);

    /**
     * 记录udp返回信息
     * @param cloudInputType 云输入类型
     */
    void logUdpResponse(int cloudInputType);

    /**
     * 记录udp失败信息
     * @param streamType 云输入类型
     * @param e 异常
     */
    void logUdpFail(int streamType, Throwable e, byte[] sendData, byte[] receivedData);
}
