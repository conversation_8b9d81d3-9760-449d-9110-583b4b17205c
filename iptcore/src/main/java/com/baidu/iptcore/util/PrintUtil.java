package com.baidu.iptcore.util;

import com.baidu.iptcore.ImeCoreConsts;
import com.baidu.iptcore.info.IptCoreDutyInfo;

import static com.baidu.iptcore.info.IptCoreDutyInfo.ACTTYPE_BACK;
import static com.baidu.iptcore.info.IptCoreDutyInfo.ACTTYPE_COMPOSING;
import static com.baidu.iptcore.info.IptCoreDutyInfo.ACTTYPE_DOWNLOAD;
import static com.baidu.iptcore.info.IptCoreDutyInfo.ACTTYPE_FINISH_AND_COMPOSING;
import static com.baidu.iptcore.info.IptCoreDutyInfo.ACTTYPE_FINISH_COMPOSING;
import static com.baidu.iptcore.info.IptCoreDutyInfo.ACTTYPE_INSERT;
import static com.baidu.iptcore.info.IptCoreDutyInfo.ACTTYPE_INSERT_AND_ENTER;
import static com.baidu.iptcore.info.IptCoreDutyInfo.ACTTYPE_INSERT_CURSOR_BACKWARD;
import static com.baidu.iptcore.info.IptCoreDutyInfo.ACTTYPE_NONE;
import static com.baidu.iptcore.info.IptCoreDutyInfo.ACTTYPE_REPLACE;
import static com.baidu.iptcore.info.IptCoreDutyInfo.ACTTYPE_REPLACE_HANDWRITE;
import static com.baidu.iptcore.info.IptCoreDutyInfo.ACTTYPE_SUG_CUR_APP_LINK;
import static com.baidu.iptcore.info.IptCoreDutyInfo.ACTTYPE_SUG_CUR_APP_SEARCH;
import static com.baidu.iptcore.info.IptCoreDutyInfo.ACTTYPE_SUG_INSERT;
import static com.baidu.iptcore.info.IptCoreDutyInfo.ACTTYPE_SUG_THIRDPARTY_APP_LINK;
import static com.baidu.iptcore.info.IptCoreDutyInfo.ACTTYPE_SUG_THIRDPARTY_APP_SEARCH;
import static com.baidu.iptcore.info.IptCoreDutyInfo.ACTTYPE_URL;

import android.text.TextUtils;
import android.util.SparseArray;

/**
 * 打印信息的帮助类
 * Created by chendanfeng on 18/2/8.
 */
public class PrintUtil {

    private static final long[] FLASH_FLAGS = {
            IptCoreDutyInfo.REFL_SHOW, IptCoreDutyInfo.REFL_CAND, IptCoreDutyInfo.REFL_LIST,
            IptCoreDutyInfo.REFL_KEY, IptCoreDutyInfo.REFL_TRACK, IptCoreDutyInfo.REFL_SUG,
            IptCoreDutyInfo.REFL_SUG_CARD, IptCoreDutyInfo.REFL_LAYOUT,
            IptCoreDutyInfo.REFL_TIP, IptCoreDutyInfo.REFL_CANDINFO,
            IptCoreDutyInfo.REFL_CONTACT, IptCoreDutyInfo.REFL_SRV_CLD_WHITE_VER,
            IptCoreDutyInfo.REFL_EGG, IptCoreDutyInfo.REFL_TOP_PROMOTION, IptCoreDutyInfo.REFL_SUG_SELECTION,
            IptCoreDutyInfo.REFL_SUG_CARD_SELECTION, IptCoreDutyInfo.REFL_PRE_EXTRACT_SELECTION,
            IptCoreDutyInfo.REFL_IPTCORE_TRACE, IptCoreDutyInfo.REFL_MORECAND_TAB,
            IptCoreDutyInfo.REFL_AI_CAND, IptCoreDutyInfo.REFL_AI_ICON,
            IptCoreDutyInfo.REFL_AI_PAD, IptCoreDutyInfo.REFL_AI_PAD_LOADING,
            IptCoreDutyInfo.REFL_AI_CORRECT_INLINE,
            IptCoreDutyInfo.REFL_HW_GESTURE,
            IptCoreDutyInfo.REFL_RARE_CAND,
            IptCoreDutyInfo.REFL_COMMON_TRIGGER_WORD,
            IptCoreDutyInfo.REFL_IDENTITY_NUM_BEFORE,
    };
    private static final String[] FLASH_FLAG_STRINGS = {
            "SHOW", "CAND", "LIST",
            "KEY", "TRACK", "SUG",
            "SUG_CARD", "LAYOUT",
            "TIP", "CANDINFO",
            "CONTACT", "CLOUD_WHITE_VER",
            "EGG", "TOP_PROMOTION", "SUG_SELECTION",
            "SUG_CARD_SELECTION", "PREEXTRACT_SELECTION",
            "REFL_IPTCORE_TRACE", "REFL_MORECAND_TAB",
            "REFL_AI_CAND", "REFL_AI_ICON",
            "REFL_AD_PAD", "REFL_AI_PAD_LOADING",
            "REFL_AI_CORRECT_INLINE_STATE",
            "REFL_HW_GESTURE",
            "REFL_RARE_CAND",
            "REFL_COMMON_TRIGGER_WORD",
            "REFL_IDENTITY_NUM_BEFORE",
    };
    private static final SparseArray<String> PAD_ID_MAPPING;

    static {
        PAD_ID_MAPPING = new SparseArray<>();
        PAD_ID_MAPPING.put(ImeCoreConsts.PAD_NONE, "none");
        PAD_ID_MAPPING.put(ImeCoreConsts.PAD_BH, "BH");
        PAD_ID_MAPPING.put(ImeCoreConsts.PAD_WB9, "WB9");
        PAD_ID_MAPPING.put(ImeCoreConsts.PAD_WB26, "WB26");
        PAD_ID_MAPPING.put(ImeCoreConsts.PAD_EN9, "EN9");
        PAD_ID_MAPPING.put(ImeCoreConsts.PAD_EN26, "EN26");
        PAD_ID_MAPPING.put(ImeCoreConsts.PAD_123_T9, "NUM9");
        PAD_ID_MAPPING.put(ImeCoreConsts.PAD_123_26, "NUM26");
        PAD_ID_MAPPING.put(ImeCoreConsts.PAD_PY9, "PY9");
        PAD_ID_MAPPING.put(ImeCoreConsts.PAD_PY26, "PY26");
        PAD_ID_MAPPING.put(ImeCoreConsts.PAD_HW, "HW");
        PAD_ID_MAPPING.put(ImeCoreConsts.PAD_SYM, "SYM");
        PAD_ID_MAPPING.put(ImeCoreConsts.PAD_ZY, "ZY");
        PAD_ID_MAPPING.put(ImeCoreConsts.PAD_CJ, "CJ");
        PAD_ID_MAPPING.put(ImeCoreConsts.PAD_SC, "CJSC");
        PAD_ID_MAPPING.put(ImeCoreConsts.PAD_PY_GAME, "PYGAME");
        PAD_ID_MAPPING.put(ImeCoreConsts.PAD_WB_GAME, "WBGAME");
        PAD_ID_MAPPING.put(ImeCoreConsts.PAD_BH_GAME, "BHGAME");
        PAD_ID_MAPPING.put(ImeCoreConsts.PAD_HW_GAME, "HWGAME");
        PAD_ID_MAPPING.put(ImeCoreConsts.PAD_VOICE, "VOICE");
        PAD_ID_MAPPING.put(ImeCoreConsts.PAD_URL, "URL");
        PAD_ID_MAPPING.put(ImeCoreConsts.PAD_MORE, "MORE");
        PAD_ID_MAPPING.put(ImeCoreConsts.PAD_SYM_EXT, "SYM_EXT");
        PAD_ID_MAPPING.put(ImeCoreConsts.PAD_RARE_HW, "RARE_HW");
        PAD_ID_MAPPING.put(ImeCoreConsts.PAD_RARE_CHAIZI, "PAD_RARE_CHAIZI");
    }

    public static String flashFlagToString(long flashFlag) {
        StringBuilder sb = new StringBuilder("REFL_");
        if (flashFlag != 0) {
            for (int i = 0; i < FLASH_FLAGS.length; i++) {
                if ((flashFlag & FLASH_FLAGS[i]) != 0) {
                    sb.append(FLASH_FLAG_STRINGS[i]).append("|");
                }
            }
        } else {
            sb.append("NONE");
        }
        return sb.toString();
    }

    public static String actionTypeToString(int actionType) {
        String actionStr;
        switch (actionType) {
            case ACTTYPE_NONE:
                actionStr = "NONE";
                break;
            case ACTTYPE_INSERT:
                actionStr = "INSERT";
                break;
            case ACTTYPE_BACK:
                actionStr = "BACK";
                break;
            case ACTTYPE_URL:
                actionStr = "URL";
                break;
            case ACTTYPE_SUG_INSERT:
                actionStr = "SUG_INSERT";
                break;
            case ACTTYPE_SUG_CUR_APP_LINK:
                actionStr = "SUG_CUR_APP_LINK";
                break;
            case ACTTYPE_SUG_CUR_APP_SEARCH:
                actionStr = "SUG_CUR_APP_SEARCH";
                break;
            case ACTTYPE_SUG_THIRDPARTY_APP_LINK:
                actionStr = "SUG_THIRDPARTY_APP_LINK";
                break;
            case ACTTYPE_SUG_THIRDPARTY_APP_SEARCH:
                actionStr = "SUG_THIRDPARTY_APP_SEARCH";
                break;
            case ACTTYPE_DOWNLOAD:
                actionStr = "DOWNLOAD";
                break;
            case ACTTYPE_INSERT_CURSOR_BACKWARD:
                actionStr = "CURSOR_BACKWARD";
                break;
            case ACTTYPE_REPLACE:
                actionStr = "REPLACE";
                break;
            case ACTTYPE_COMPOSING:
                actionStr = "COMPOSING";
                break;
            case ACTTYPE_FINISH_COMPOSING:
                actionStr = "FINISH_COMPOSING";
                break;
            case ACTTYPE_FINISH_AND_COMPOSING:
                actionStr = "FINISH_AND_COMPOSING";
                break;
            case ACTTYPE_INSERT_AND_ENTER:
                actionStr = "INSERT_AND_ENTER";
                break;
            case ACTTYPE_REPLACE_HANDWRITE:
                actionStr = "REPLACE_HANDWRITE";
                break;
            default:
                actionStr = actionType + "";
                break;
        }
        return actionStr;
    }

    public static String tipStateToString(int tipState) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < IptCoreDutyInfo.TIP_NUM; i++) {
            boolean stateValue = (tipState & (1 << i)) != 0;
            if (stateValue) {
                sb.append('1').append('|');
            } else {
                sb.append('0').append('|');
            }
        }
        return sb.toString();
    }

    public static String padIdToString(int padId) {
        String name = PAD_ID_MAPPING.get(padId);
        if (TextUtils.isEmpty(name)) {
            return String.valueOf(padId);
        } else {
            return name + "_" + padId;
        }
    }
}
