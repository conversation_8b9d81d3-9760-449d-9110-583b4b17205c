package com.baidu.iptcore.util;

import java.util.Arrays;

/**
 * 临时性的线上内核耗时统计方案，后续会移除或重构
 */
@Deprecated
public class TimeStatisticsUtil {

    /**
     * 耗时分布统计的区间，分别为：0-33ms，34-66ms，67-99ms，100ms及以上
     * 若一秒有30帧，则每帧时间为33ms。超过99ms，意味着本次事件处理至少丢失3帧，会有明显的卡顿感
     */
    private static final int[] STATISTICAL_INTERVAL = new int[]{33, 66, 99, Integer.MAX_VALUE};

    /**
     * 耗时分布统计。
     */
    private static final int[] TIME_COST_STATISTICS = new int[STATISTICAL_INTERVAL.length];

    /**
     * 使能开关
     */
    private static boolean enable = false;

    /**
     * trace启动的时间
     */
    private static long startTime = 0L;

    /**
     * 卡开/关闭数据收集
     */
    public static void enableTrace(boolean enable) {
        TimeStatisticsUtil.enable = enable;
    }

    public static boolean isEnable() {
        return enable;
    }

    /**
     * 开始trace
     */
    public static void startTrace() {
        if (!enable) {
            return;
        }
        startTime = System.currentTimeMillis();
    }

    /**
     * 结束trace
     */
    public static void endTrace() {
        if (!enable) {
            return;
        }
        addStatistics(System.currentTimeMillis() - startTime);
    }

    /**
     * 结束trace，并立刻开始下一次trace
     */
    public static void endAndRestart() {
        if (!enable) {
            return;
        }
        long currentTime = System.currentTimeMillis();
        addStatistics(currentTime - startTime);
        startTime = currentTime;
    }

    /**
     * 导出统计数据
     */
    public static int[] exportAndClear() {
        if (!enable) {
            return null;
        }

        // 简单统计。这里我们不考虑多线程导致的脏数据的问题
        int[] ret = new int[TIME_COST_STATISTICS.length];
        System.arraycopy(TIME_COST_STATISTICS, 0, ret, 0, TIME_COST_STATISTICS.length);
        Arrays.fill(TIME_COST_STATISTICS, 0);
        return ret;
    }

    /**
     * 添加一条启动数据
     */
    private static void addStatistics(long cost) {
        int index = 0;
        while (index < STATISTICAL_INTERVAL.length && cost > STATISTICAL_INTERVAL[index]) {
            ++index;
        }
        // 防止极端情况下发生数组越界的情况
        if (index < TIME_COST_STATISTICS.length) {
            TIME_COST_STATISTICS[index]++;
        }
    }

}
