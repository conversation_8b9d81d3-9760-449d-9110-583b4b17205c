package com.baidu.iptcore.util.profiler;

import com.baidu.input.perf.trace.CallingPoint;
import com.baidu.input.perf.trace.NativeTraceAnalyzer;
import com.baidu.iptcore.util.Logger;

import org.json.JSONException;
import org.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class DefaultTraceAnalyzer implements NativeTraceAnalyzer {

    private final Date nowDate = new Date();
    private final SimpleDateFormat simpleDateFormat
            = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.US);

    @Override
    public void report(List<CallingPoint> result, boolean hasSymInfo) {
        if (result.size() <= 0) {
            return;
        }
        long minTime = result.get(0).time;
        long maxTime = result.get(result.size() - 1).time;
        StringBuilder sb = new StringBuilder();
        for (CallingPoint p : result) {
            p.time -= minTime;
            sb.append(p.toString());
        }
        nowDate.setTime(System.currentTimeMillis());
        String timeStr = simpleDateFormat.format(nowDate);
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("time", timeStr);
            jsonObject.put("type", "NATIVE_TRACE");
            jsonObject.put("cost", "" + (maxTime - minTime));
            jsonObject.put("stack", sb.toString());
        } catch (JSONException e) {
            Logger.printStackTrace(e);
        }
        DiskLogHandler.getInstance().log(0, "", jsonObject.toString() + "\n");

        if (hasSymInfo) {
            sb = new StringBuilder();
            for (CallingPoint p : result) {
                sb.append(p.toDetailString()).append(';');
            }
            jsonObject = new JSONObject();
            try {
                jsonObject.put("time", timeStr);
                jsonObject.put("type", "NATIVE_TRACE_DETAIL");
                jsonObject.put("stack", sb.toString());
            } catch (JSONException e) {
                Logger.printStackTrace(e);
            }
            DiskLogHandler.getInstance().log(0, "", jsonObject.toString() + "\n");
        }
    }
}
