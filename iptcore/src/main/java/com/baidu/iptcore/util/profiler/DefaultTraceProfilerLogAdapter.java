package com.baidu.iptcore.util.profiler;

import android.content.Context;
import android.os.Looper;

import com.baidu.iptcore.util.MethodTraceLogAdapter;

import org.json.JSONException;
import org.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class DefaultTraceProfilerLogAdapter implements MethodTraceLogAdapter {

    private static volatile Looper coreThreadLooper;
    private final Date nowDate = new Date();
    private final SimpleDateFormat simpleDateFormat
            = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.US);

    public DefaultTraceProfilerLogAdapter(Context context, Looper coreThreadLooper) {
        DefaultTraceProfilerLogAdapter.coreThreadLooper = coreThreadLooper;
    }

    @Override
    public void enterCoreMethod(String methodName, long startTime) {
        // do nothing
    }

    @Override
    public void exitCoreMethod(String message, long cost, long endTime, ExtraInfo extra) {
        if (coreThreadLooper != null && Looper.myLooper() != coreThreadLooper) {
            return;
        }

        // 1. log core method
        if (filterCoreMethod(message)) {
            JSONObject jsonObject = new JSONObject();
            try {
                nowDate.setTime(endTime);
                jsonObject.put("time", simpleDateFormat.format(nowDate));
                jsonObject.put("action", message);
                jsonObject.put("cost", cost);
                String costDetail = "(env:" + extra.envCost()
                        + ", data:" + extra.dataCost() + ", key:" + extra.keyCost() + ")";
                jsonObject.put("costDetail", costDetail);
                jsonObject.put("pad", extra.padId());
                jsonObject.put("code", extra.inputStr());
                jsonObject.put("firstWord", extra.firstWord());
            } catch (JSONException ignore) {
            }
            DiskLogHandler.getInstance().log(0, "", jsonObject.toString() + "\n");
        }

        // 2. check flush
        if (isFlushMessage(message)) {
            DiskLogHandler.getInstance().flush();
        }
    }

    /**
     * 确定是否是flush message
     */
    private boolean isFlushMessage(String message) {
        return message.contains("closeCore")
                || message.contains("sendPadEvent");
    }

    /**
     * 过滤出关心的内核接口
     */
    private boolean filterCoreMethod(String message) {
        return message.contains("Core")
                || message.contains("actKeyClick")
                || message.contains("sendPadEvent")
                || message.contains("switchKeyboard");
    }
}
