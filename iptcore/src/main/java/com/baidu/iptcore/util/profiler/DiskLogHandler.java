package com.baidu.iptcore.util.profiler;

import android.content.Context;
import android.os.Handler;
import android.os.HandlerThread;

import java.io.File;

public class DiskLogHandler {

    /** 500K averages to a 4000 lines per file */
    private static final int MAX_BYTES = 500 * 1024;

    /**
     * 单例实例
     */
    private static volatile DiskLogHandler instance = null;

    private DiskLogStrategy logStrategy;

    private static volatile boolean initialized;

    /**
     * 单例
     */
    public static DiskLogHandler getInstance() {
        if (null == instance) {
            synchronized (DiskLogHandler.class) {
                if (null == instance) {
                    instance = new DiskLogHandler();
                }
            }
        }
        return instance;
    }

    public void initDiskLog(Context context) {
        if (!initialized) {
            synchronized (DiskLogHandler.class) {
                if (!initialized) {
                    initDiskLogInternal(context);
                    initialized = true;
                }
            }
        }
    }

    private void initDiskLogInternal(Context context) {
        if (logStrategy == null) {
            String diskPath =
                    context.getApplicationContext().getExternalFilesDir("").getAbsolutePath();
            String folder = diskPath + File.separatorChar + "iptcoreLogger";
            HandlerThread ht = new HandlerThread("IptCoreProfilerLogger");
            ht.start();
            Handler handler = new DiskLogStrategy.WriteHandler(ht.getLooper(),
                    folder, MAX_BYTES, false);
            logStrategy = new DiskLogStrategy(handler);
        }
    }

    public void log(int level, String tag, String message) {
        if (logStrategy != null) {
            logStrategy.log(level, tag, message);
        }
    }

    public void flush() {
        if (logStrategy != null) {
            logStrategy.flush();
        }
    }
}
