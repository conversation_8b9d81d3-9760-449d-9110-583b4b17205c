package com.baidu.iptcore.util.profiler;

import android.os.Handler;
import android.os.Looper;
import android.os.Message;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;

/**
 * 磁盘日志记录。
 * 1) 大小限制功能，文件超出指定大小时自动新建文件写入；
 * 2) 可配置是否及时写入
 */
class DiskLogStrategy implements LogStrategy {

    private static final int MSG_ADD_LOG = 0;
    private static final int MSG_FLUSH = 1;

    private final Handler handler;

    public DiskLogStrategy(Handler handler) {
        this.handler = handler;
    }

    @Override
    public void log(int level, String tag, String message) {
        // do nothing on the calling thread, simply pass the tag/msg to the background thread
        handler.sendMessage(handler.obtainMessage(MSG_ADD_LOG, message));
    }

    @Override
    public void flush() {
        handler.sendMessage(handler.obtainMessage(MSG_FLUSH));
    }

    static class WriteHandler extends Handler {

        private final String folder;
        private final int maxFileSize;
        private final boolean autoFlush;

        private FileWriter fileWriter;
        private File logFile;

        WriteHandler(Looper looper, String folder, int maxFileSize) {
            this(looper, folder, maxFileSize, true);
        }

        WriteHandler(Looper looper, String folder, int maxFileSize, boolean autoFlush) {
            super(looper);
            this.folder = folder;
            this.maxFileSize = maxFileSize;
            this.autoFlush = autoFlush;
        }

        @Override
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case MSG_FLUSH:
                    flushLog();
                    break;
                case MSG_ADD_LOG:
                    appendLog((String) msg.obj);
                    if (autoFlush || checkSizeFull()) {
                        flushLog();
                    }
                    break;
                default:
                    break;
            }

        }

        private void flushLog() {
            if (fileWriter != null) {
                try {
                    fileWriter.flush();
                    fileWriter.close();
                } catch (IOException ignored) {
                }

                fileWriter = null;
                logFile = null;
            }
        }

        private void appendLog(String content) {

            try {
                if (fileWriter == null) {
                    logFile = getLogFile(folder, "logs");
                    fileWriter = new FileWriter(logFile, true);
                }
                writeLog(fileWriter, content);
            } catch (IOException e) {
                if (fileWriter != null) {
                    try {
                        fileWriter.flush();
                        fileWriter.close();
                    } catch (IOException ignored) {
                    }

                    fileWriter = null;
                    logFile = null;
                }
            }
        }

        private void writeLog(FileWriter fileWriter, String content) throws IOException {
            fileWriter.append(content);
        }

        private boolean checkSizeFull() {
            return logFile != null && logFile.length() > maxFileSize;
        }

        private File getLogFile(String folderName, String fileName) {

            File folder = new File(folderName);
            if (!folder.exists()) {
                folder.mkdirs();
            }

            int newFileCount = 0;
            File newFile;
            File existingFile = null;

            newFile = new File(folder, String.format("%s_%s.txt", fileName, newFileCount));
            while (newFile.exists()) {
                existingFile = newFile;
                newFileCount++;
                newFile = new File(folder, String.format("%s_%s.txt", fileName, newFileCount));
            }

            if (existingFile != null) {
                if (existingFile.length() >= maxFileSize) {
                    flushLog();
                    return newFile;
                }
                return existingFile;
            }

            return newFile;
        }
    }
}