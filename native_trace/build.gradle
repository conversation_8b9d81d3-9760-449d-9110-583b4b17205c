apply plugin: 'com.android.library'

def useCmake = false

android {
    compileSdkVersion rootProject.ext.compileSdkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion

    compileOptions {
        sourceCompatibility rootProject.ext.sourceCompatibilityVersion
        targetCompatibility rootProject.ext.targetCompatibilityVersion
    }

    defaultConfig {
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles 'consumer-rules.pro'

        if (useCmake) {
            externalNativeBuild {
                cmake {
                    cppFlags "-std=c++14"
                }
            }
            ndk {
                abiFilters "armeabi-v7a", "arm64-v8a"

            }
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    if (useCmake) {
        externalNativeBuild {
            cmake {
                path "CMakeLists.txt"
            }
        }
    }

}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation "androidx.appcompat:appcompat:${APPCOMPAT_VERSION}"
}

task makeJar(type: Copy) {
    delete 'build/native_trace.jar'
    from('build/intermediates/packaged-classes/release/')
    into('build/')
    include('classes.jar')
    rename('classes.jar', 'native_trace.jar')
}

makeJar.dependsOn(build)
