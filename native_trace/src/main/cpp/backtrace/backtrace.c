//
// Created by <PERSON>,<PERSON><PERSON>
//
#include "backtrace.h"
#include <pthread.h>
#include <dlfcn.h>
#include <jni.h>

#define TAG    "trace_perf"

/** 全局的trace_perf的Callback */
jobject g_trace_callback = NULL;

/** Java层的缓存数据结构 */
jbyteArray g_byte_array = NULL;

/** 全局的jvm */
JavaVM *g_jvm = NULL;

/** 上报到java层的methodId */
jmethodID g_report_method_id = NULL;

/** 上报给Java时，symbol名称的最大长度，超过时截断 */
#define MAX_SYMNAME_LEN 255

/**
 * const char * 转jstring
 */
jstring utf_to_str(JNIEnv *env, const char *utf, uint32_t utf_len) {
    if (utf == NULL || utf_len == 0) {
        return NULL;
    }

    uint32_t len = 0;
    while (utf[len] != 0 && len < utf_len) {
        len++;
    }

    if (utf[len] == 0) {
        return (*env)->NewStringUTF(env, utf);
    } else {
        char utf_copy[len + 1];
        memcpy(utf_copy, utf, len);
        utf_copy[len] = 0;
        return (*env)->NewStringUTF(env, utf_copy);
    }
}

/**
 * 上报到java层
 */
void reportToJava() {

    if (g_jvm == NULL || g_trace_callback == NULL || g_report_method_id == NULL) {
        return;
    }

    unsigned int array_len = 0;
    unsigned int max_len = 0;
    jbyte * data = (jbyte *) get_trace_data(&array_len, &max_len);

    if (array_len == 0) {
        return;
    }

    JNIEnv *env = NULL;
    if ((*g_jvm)->AttachCurrentThread(g_jvm, &env, NULL) == JNI_OK) {
        (*env)->SetByteArrayRegion(env, g_byte_array, 0, array_len, data);
        (*env)->CallVoidMethod(env, g_trace_callback, g_report_method_id,
                               g_byte_array, (jint) array_len, (jint) max_len);
    }
}

int __attribute__((no_instrument_function)) backtrace_get_dlinfo(void *caller_point,
                                                                 Dl_info *dlinfo) {
    void *ip = caller_point;
    if (!dladdr(ip, dlinfo)) {
        return JNI_ERR;
    } else {
        return JNI_OK;
    }
}

void __attribute__((no_instrument_function)) function_enter(void *function, void *call) {

#ifdef ENABLE_LOGCAT
    Dl_info func_dlinfo;
    Dl_info caller_dlinfo;
    int ret1 = backtrace_get_dlinfo(function, &func_dlinfo);
    int ret2 = backtrace_get_dlinfo(call, &caller_dlinfo);

    if (ret1 != JNI_OK || ret2 != JNI_OK) {
        return;
    }

    LOGD("[thread] backtrace enter [%x] %s (%x-%x) --- [%x] %s (%x-%x)",
         function - func_dlinfo.dli_fbase, func_dlinfo.dli_sname, function, func_dlinfo.dli_fbase,
         call - caller_dlinfo.dli_fbase, caller_dlinfo.dli_sname, call, caller_dlinfo.dli_fbase);
#endif

    if (is_trace_started()) {
        trace_perf_enter(function);
    }
}

void __attribute__((no_instrument_function)) function_exit(void *function, void *call) {

#ifdef ENABLE_LOGCAT
    Dl_info func_dlinfo;
    Dl_info caller_dlinfo;
    int ret1 = backtrace_get_dlinfo(function, &func_dlinfo);
    int ret2 = backtrace_get_dlinfo(call, &caller_dlinfo);

    if (ret1 != JNI_OK || ret2 != JNI_OK) {
        return;
    }

    LOGD("[thread] backtrace exit [%x] %s (%x-%x) --- [%x] %s (%x-%x)",
         function - func_dlinfo.dli_fbase, func_dlinfo.dli_sname, function, func_dlinfo.dli_fbase,
         call - caller_dlinfo.dli_fbase, caller_dlinfo.dli_sname, call, caller_dlinfo.dli_fbase);
#endif

    if (is_trace_started()) {
        trace_perf_exit(function);

        if (is_need_report()) {
            reportToJava();
        }
    }
}

JNIEXPORT jboolean JNICALL __attribute__((no_instrument_function))
Java_com_baidu_input_perf_trace_NativeTraceHandler_setTraceCallback(
        JNIEnv *env, jobject object,
        jobject callback, jbyteArray buffer) {

    // 全局只允许设置一次callback，重复设置无效
    if (g_jvm != NULL || g_trace_callback != NULL || g_byte_array != NULL) {
        return JNI_FALSE;
    }

    // 保存全局jvm和callback
    (*env)->GetJavaVM(env, &g_jvm);
    g_trace_callback = (*env)->NewGlobalRef(env, callback);
    g_byte_array = (*env)->NewGlobalRef(env, buffer);

    // 获取上报到java层的反射方法id
    jclass callback_class = (*env)->GetObjectClass(env, callback);
    g_report_method_id = (*env)->GetMethodID(env, callback_class,
                                             "report",
                                             "([BII)V");
    return JNI_TRUE;
}

JNIEXPORT void JNICALL __attribute__((no_instrument_function))
Java_com_baidu_input_perf_trace_NativeTraceHandler_reset(JNIEnv *env, jobject thiz) {
    if (is_trace_started()) {
        trace_perf_reset();
    }
}

JNIEXPORT void JNICALL __attribute__((no_instrument_function))
Java_com_baidu_input_perf_trace_NativeTraceHandler_startPerf(JNIEnv *env, jobject thiz) {
     start_perf();
}

JNIEXPORT void JNICALL __attribute__((no_instrument_function))
Java_com_baidu_input_perf_trace_NativeTraceHandler_getStackTraceInfo(
        JNIEnv *env, jobject thiz,
        jintArray jfunctions, jintArray jbases, jobjectArray jsyms) {

    jint length = (*env)->GetArrayLength(env, jfunctions);

    jboolean isCopy = JNI_FALSE;
    jint *functions = (*env)->GetIntArrayElements(env, jfunctions, &isCopy);
    jint *bases = (*env)->GetIntArrayElements(env, jbases, &isCopy);

    for (jint i = 0; i < length; i++) {
        uint32_t func_addr = (uint32_t) functions[i];
        Dl_info func_dlinfo;
        int ret = backtrace_get_dlinfo((void *) func_addr, &func_dlinfo);
        if (ret != JNI_OK) {
            continue;
        }
        bases[i] = (jint) func_dlinfo.dli_fbase;
        jstring sym = utf_to_str(env, func_dlinfo.dli_sname, MAX_SYMNAME_LEN);
        (*env)->SetObjectArrayElement(env, jsyms, i, sym);
    }
    (*env)->ReleaseIntArrayElements(env, jfunctions, functions, JNI_ABORT);
    (*env)->ReleaseIntArrayElements(env, jbases, bases, 0);
}