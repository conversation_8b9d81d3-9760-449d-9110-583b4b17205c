//
// Created by <PERSON>,<PERSON><PERSON>
//

#ifndef NATIVEHOOK_BACKTRACE_H
#define NATIVEHOOK_BACKTRACE_H

#include "native_trace_perf.h"
#include <jni.h>
#include "jni_def.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * 方法进入的hook
 */
void __attribute__((no_instrument_function)) function_enter(void *function, void *call);

/**
 * 方法退出的hook
 */
void __attribute__((no_instrument_function)) function_exit(void *function, void *call);

/**
 * 设置Trace的Callback
 * @param callback Java层接收分析上报的callback
 * @param buffer Java层的缓存数组
 * @return 是否设置成功
 */
JNIEXPORT jboolean JNICALL __attribute__((no_instrument_function))
Java_com_baidu_input_perf_trace_NativeTraceHandler_setTraceCallback(
        JNIEnv *env, jobject object, jobject callback, jbyteArray buffer);

/**
 * Java层在某些必要的点，重置trace_perf的信息，保证可以重新记录
 */
JNIEXPORT void JNICALL __attribute__((no_instrument_function))
Java_com_baidu_input_perf_trace_NativeTraceHandler_reset(
        JNIEnv *env, jobject object);

/**
 * 启动监测，需要在待观测线程中启动
 */
JNIEXPORT void JNICALL __attribute__((no_instrument_function))
Java_com_baidu_input_perf_trace_NativeTraceHandler_startPerf(
        JNIEnv *env, jobject object);

/**
 * 上报时获取更详细的堆栈信息
 * @param functions 待获取的function列表
 * @param bases 基地址信息ouput
 * @param syms 符号信息ouput
 */
JNIEXPORT void JNICALL __attribute__((no_instrument_function))
Java_com_baidu_input_perf_trace_NativeTraceHandler_getStackTraceInfo(
        JNIEnv *env, jobject thiz,
        jintArray functions, jintArray bases, jobjectArray syms);

#ifdef __cplusplus
}
#endif

#endif //NATIVEHOOK_BACKTRACE_H
