#include <android/log.h>
#include <pthread.h>
#include <jni.h>
#include <unistd.h>
#include <stdlib.h>
#include <stdbool.h>
#include "native_trace_perf.h"

#define TAG    "trace_perf"

/** 定义方法的进入常量 */
#define CALLING_ACTION_IN 0
/** 定义方法的退出常量 */
#define CALLING_ACTION_OUT 1

/** 上报给Java时，记录的最大数据长度 */
#define TRACE_PERF_DATA_LEN 4000

/**
 * 一个方法的进入或退出点信息的数据结构
 */
struct CallingPoint {
    uint8_t action;
    uint32_t function;
    uint32_t deep;
    uint32_t time_sec;
    uint32_t time_usec;
};

/** 当前时间的数据结构 */
struct timeval g_now;

/** 当前的时间(ms) */
volatile uint32_t current_time_sec = 0;
volatile uint32_t current_time_usec = 0;

/** 时间更新线程的标志 */
int flag = 1;

/** 保存的监测线程的id */
pthread_t core_thread_tid = 0;

/** 是否启动的trace_perf */
int trace_perf_started = 0;

/** 是否出错 */
bool has_error = false;

/** 记录方法进入和退出的buffer数据 */
struct CallingPoint *trace_perf_data;
/** 当前待写入的数据在buffer中的索引 */
int32_t perf_data_index = 0;
/** 当前方法深度 */
int32_t perf_data_deep = 0;

/**
 * 获得当前的系统时间(ms)
 */
void get_current_systime() {
    gettimeofday(&g_now, NULL);
    current_time_sec = g_now.tv_sec;
    current_time_usec = g_now.tv_usec;
}

/**
 * 时间更新线程
 */
void *run_time_update(void *args) {
    while (flag) {
        usleep(5000);
        get_current_systime();
    }
}

/**
 * 判断是否是入口函数
 */
bool __attribute__((no_instrument_function)) is_entry_method() {
    return perf_data_deep == 0;
}

/**
 * trace_perf是否启动
 */
bool __attribute__((no_instrument_function)) is_trace_started() {
    return trace_perf_started;
}

/**
 * 当前是否需要上报
 */
bool __attribute__((no_instrument_function)) is_need_report() {
    return is_entry_method();
}

/**
 * 启动perf
 */
void __attribute__((no_instrument_function)) start_perf() {

    // 初始化当前时间
    get_current_systime();

    // 准备缓存数据
    trace_perf_data = malloc(sizeof(struct CallingPoint) * TRACE_PERF_DATA_LEN);
    memset(trace_perf_data, 0, sizeof(struct CallingPoint) * (TRACE_PERF_DATA_LEN));
    perf_data_index = 0;
    perf_data_deep = 0;
    has_error = false;

    // 启动时间更新线程
    pthread_t time_update_tid;
    pthread_create(&time_update_tid, NULL, run_time_update, NULL);

    // 初始化内核线程id
    core_thread_tid = pthread_self();

    trace_perf_started = 1;
}

/**
 * 重置当前的信息
 */
void __attribute__((no_instrument_function)) trace_perf_reset() {
    perf_data_index = 0;
    perf_data_deep = 0;
    has_error = false;
}

/**
 * 方法进入的hook
 * @param func_addr 当前func地址
 * @param func_base_addr 当前func的base地址
 * @param func_sname 当前func的symname
 */
void __attribute__((no_instrument_function))
trace_perf_enter(void *func_addr) {

    if (pthread_self() != core_thread_tid) {
        LOGD("function %x is called from other thread: %ld, %ld", func_addr, pthread_self(),
             core_thread_tid);
        return;
    }

    if (has_error) {
        return;
    }

    if (is_entry_method()) {
        memset(trace_perf_data, 0, sizeof(struct CallingPoint) * (TRACE_PERF_DATA_LEN));
        perf_data_index = 0;
    }

    perf_data_deep++;
    if (perf_data_deep == INT32_MAX) {
        has_error = true;
        return;
    }

    if (perf_data_index < TRACE_PERF_DATA_LEN) {
        struct CallingPoint *curr_perf_data_ptr = trace_perf_data + perf_data_index;
        curr_perf_data_ptr->action = CALLING_ACTION_IN;
        curr_perf_data_ptr->function = (uint32_t) func_addr;
        curr_perf_data_ptr->time_sec = current_time_sec;
        curr_perf_data_ptr->time_usec = current_time_usec;
        curr_perf_data_ptr->deep = perf_data_deep;

        perf_data_index++;
    }
}

/**
 * 方法退出的hook
 * @param func_addr 当前func地址
 * @param func_base_addr 当前func的base地址
 * @param func_sname 当前func的symname
 */
void __attribute__((no_instrument_function))
trace_perf_exit(void *func_addr) {
    if (pthread_self() != core_thread_tid) {
        LOGD("function %x is called from other thread", func_addr);
        return;
    }

    if (has_error) {
        return;
    }

    if (perf_data_deep <= 0) {
        has_error = true;
        return;
    }

    // 碰撞去重
    if (perf_data_index > 0) {
        struct CallingPoint *prev_perf_data_ptr = trace_perf_data + (perf_data_index - 1);
        if (prev_perf_data_ptr->action == CALLING_ACTION_IN
            && prev_perf_data_ptr->function == (uint32_t) func_addr
            && prev_perf_data_ptr->deep == perf_data_deep
            && prev_perf_data_ptr->time_sec == current_time_sec
            && prev_perf_data_ptr->time_usec == current_time_usec) {
            perf_data_index--;
            perf_data_deep--;
            return;
        }
    }

    // 记录当前exit
    if (perf_data_index < TRACE_PERF_DATA_LEN) {
        struct CallingPoint *curr_perf_data_ptr = trace_perf_data + perf_data_index;
        curr_perf_data_ptr->action = CALLING_ACTION_OUT;
        curr_perf_data_ptr->function = (uint32_t) func_addr;
        curr_perf_data_ptr->deep = perf_data_deep;
        curr_perf_data_ptr->time_sec = current_time_sec;
        curr_perf_data_ptr->time_usec = current_time_usec;

        perf_data_index++;
        perf_data_deep--;
    }
}

/**
 * 获取这一段时间记录的trace数据
 * @param trace_data_len 当前记录的数据长度
 * @param trace_data_max_len 当前记录的数据最大长度
 */
struct CallingPoint *__attribute__((no_instrument_function))
get_trace_data(unsigned int *trace_data_len, unsigned int *trace_data_max_len) {
    (*trace_data_max_len) = sizeof(struct CallingPoint) * (TRACE_PERF_DATA_LEN);
    (*trace_data_len) = sizeof(struct CallingPoint) * (perf_data_index);
    struct CallingPoint *p = trace_perf_data;

    LOGD("get_trace_data: maxLen=%d, currLen=%d, first: action=%d, function=%p, deep=%d, time=%ld, %ld, sizeof=%d",
         *trace_data_max_len, *trace_data_len,
         p->action, p->function, p->deep, p->time_sec, p->time_usec,
         sizeof(struct CallingPoint));

    return trace_perf_data;
}