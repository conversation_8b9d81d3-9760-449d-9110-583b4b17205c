//
// Created by Cdf on 2020-10-04.
//

#ifndef NATIVE_TRACE_PERF_H
#define NATIVE_TRACE_PERF_H

#include <jni.h>
#include <stdbool.h>
#include "jni_def.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
* 启动perf
*/
void __attribute__((no_instrument_function)) start_perf();

/**
 * trace_perf是否启动
 */
bool __attribute__((no_instrument_function)) is_trace_started();

/**
 * 重置当前的信息
 */
void __attribute__((no_instrument_function)) trace_perf_reset();

void __attribute__((no_instrument_function))
trace_perf_enter(void *func_addr);

/**
 * 方法进入的hook
 * @param func_addr 当前func地址
 * @param func_base_addr 当前func的base地址
 * @param func_sname 当前func的symname
 */
void __attribute__((no_instrument_function))
trace_perf_exit(void *func_addr);

/**
 * 当前是否需要上报
 */
bool __attribute__((no_instrument_function)) is_need_report();

/**
 * 获取这一段时间记录的trace数据
 * @param trace_data_len 当前记录的数据长度
 * @param trace_data_max_len 当前记录的数据最大长度
 */
struct CallingPoint *__attribute__((no_instrument_function))
get_trace_data(unsigned int *trace_data_len, unsigned int *trace_data_max_len);

#ifdef __cplusplus
}
#endif

#endif //NATIVE_TRACE_PERF_H