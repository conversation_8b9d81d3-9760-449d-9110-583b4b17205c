package com.baidu.input.perf.trace;

public class CallingPoint {

    public static final int NATIVE_SIZE = 20;

    public int deep;
    byte action;
    int function;
    int base;
    public long time;
    String symName;

    @Override
    public String toString() {
        return action + "," + deep + "," + time + "," + (function - base) + ';';
    }

    public String toDetailString() {
        return "CallingPoint(" +
                "action=" + action +
                ", deep=" + deep +
                ", time=" + time +
                ", symName='" + symName + '\'' +
                ", func=" + Long.toHexString(function - base) +
                ", function=" + Long.toHexString(function) +
                ", base=" + Long.toHexString(base) +
                ')';
    }
}
