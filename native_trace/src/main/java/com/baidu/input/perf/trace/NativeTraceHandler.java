package com.baidu.input.perf.trace;

import androidx.collection.ArrayMap;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * NativeTrace的Handler
 */
public class NativeTraceHandler {

    private static final int DATA_LEN = 4000;

    /**
     * 单例实例
     */
    private static volatile NativeTraceHandler instance = null;

    /**
     * 和JNI进行数据交换的buffer大小
     */
    private final byte[] buffer = new byte[CallingPoint.NATIVE_SIZE * DATA_LEN];

    /**
     * 配置信息
     */
    private Config config = new Config(false, 0);

    /**
     * 单例
     */
    public static NativeTraceHandler getInstance() {
        if (null == instance) {
            synchronized (NativeTraceHandler.class) {
                if (null == instance) {
                    instance = new NativeTraceHandler();
                }
            }
        }
        return instance;
    }

    private NativeTraceHandler() {
        System.loadLibrary("xh_backtrace");
    }

    /**
     * 设置NativeTrace的分析器
     */
    public boolean init(Config config, NativeTraceAnalyzer callback) {
        boolean success = setTraceCallback(new DefaultNativeTraceProcessor(config, callback), buffer);
        if (success) {
            this.config = config;
        }
        return success;
    }

    /**
     * 给内核设置nativetrace的callback
     */
    private native boolean setTraceCallback(NativeTraceCallback callback, byte[] buffer);

    /**
     * 暴露给外部，在合适的时机重置内核记录的堆栈
     */
    public native void reset();

    /**
     * 开启监控（需要在你要监控的线程中调用）
     */
    public native void startPerf();

    /**
     * 获取堆栈的详细信息
     * @param functions 待查找的方法地址
     * @param bases 每个方法对应的base地址
     * @param syms 每个方法对应的符号
     */
    public native void getStackTraceInfo(int[] functions, int[] bases, String[] syms);

    public static class Config {

        private final boolean enableNativeSymReport;
        private final int thresHold;

        public Config(boolean enableNativeSymReport, int thresHold) {
            this.enableNativeSymReport = enableNativeSymReport;
            this.thresHold = thresHold;
        }
    }

    public static class ConfigBuilder {
        private boolean enableNativeSymReport;
        private int thresHold;

        public ConfigBuilder enableNativeSymReport(boolean enable) {
            this.enableNativeSymReport = enable;
            return this;
        }

        public ConfigBuilder threasHold(int timeMillis) {
            this.thresHold = timeMillis;
            return this;
        }

        public Config build() {
            return new Config(enableNativeSymReport, thresHold);
        }
    }

    /**
     * 默认的NativeTrace预处理器
     */
    private static class DefaultNativeTraceProcessor implements NativeTraceCallback {

        private final NativeTraceAnalyzer callback;
        private final Config config;

        public DefaultNativeTraceProcessor(Config config, NativeTraceAnalyzer callback) {
            this.config = config;
            this.callback = callback;
        }

        @Override
        public void report(byte[] data, int dataLen, int maxDataLen) {

            int callingPointLen = dataLen / CallingPoint.NATIVE_SIZE;
            List<CallingPoint> result = new ArrayList<>();
            CallingPoint callingPoint;
            for (int i = 0; i < callingPointLen; i++) {
                callingPoint = new CallingPoint();
                int startIndex = i * CallingPoint.NATIVE_SIZE;
                callingPoint.action = data[startIndex];
                callingPoint.function = byteToInt(data, startIndex + 4);
                callingPoint.deep = byteToInt(data, startIndex + 8);
                int sec = byteToInt(data, startIndex + 12);
                int usec = byteToInt(data, startIndex + 16);
                callingPoint.time = sec * 1000L + usec / 1000;
                result.add(callingPoint);
            }

            // checkThresHold
            if (callingPointLen <= 0) {
                return;
            }
            long minTime = result.get(0).time;
            long maxTime = result.get(result.size() - 1).time;
            long cost = maxTime - minTime;
            if (cost < 0 || cost < config.thresHold) {
                return;
            }

            // 获取数据信息
            if (config.enableNativeSymReport) {
                getBaseAndSymInfo(result);
            }

            if (callback != null) {
                callback.report(result, config.enableNativeSymReport);
            }
        }

        private void getBaseAndSymInfo(List<CallingPoint> result) {
            Map<Integer, Integer> baseMap = new ArrayMap<>();
            Map<Integer, String> symMap = new ArrayMap<>();

            ArrayList<Integer> functionList = new ArrayList<>();
            for (CallingPoint point : result) {
                if (!functionList.contains(point.function)) {
                    functionList.add(point.function);
                }
            }
            if (functionList.size() > 0) {
                int size = functionList.size();
                int[] functions = new int[size];
                for (int i = 0; i < size; i++) {
                    functions[i] = functionList.get(i);
                }
                int[] bases = new int[size];
                String[] syms = new String[size];
                NativeTraceHandler.getInstance().getStackTraceInfo(functions, bases, syms);
                for (int i = 0; i < size; i++) {
                    baseMap.put(functions[i], bases[i]);
                    symMap.put(functions[i], syms[i]);
                }
            }

            for (CallingPoint point : result) {
                Integer base = baseMap.get(point.function);
                String sym = symMap.get(point.function);
                point.base = base == null ? 0 : base;
                point.symName = sym;
            }
        }

        private int byteToInt(byte[] data, int startIndex) {
            return ByteBuffer.wrap(data, startIndex, 4)
                    .order(ByteOrder.LITTLE_ENDIAN).getInt();
        }

        private long byteToLong(byte[] data, int startIndex) {
            return ByteBuffer.wrap(data, startIndex, 8)
                    .order(ByteOrder.LITTLE_ENDIAN).getLong();
        }

    }
}
