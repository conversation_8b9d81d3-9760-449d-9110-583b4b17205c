import java.io.File

/**
 * <pre>
 * **这个文件是为了自动生成输入卡顿相关的代码而增加的。**
 * **使用的时候请在 Android studio 中打开内核组件项目，并将该文件拷贝到 AS 的 【scratches and consoles】中运行**
 * **注意修改脚本中的如下行**
 * ```kotlin
 * // 注意修改为自己电脑上的绝对目录
 * val projectDir = "/Users/<USER>/sourcecode/baidu/inputmethod/iptandroiddemo"
 * ```
 * </pre>
 */

data class MethodInfo(val name: String, val clazz: String)

// 注意修改为自己电脑上的绝对目录
val projectDir = "/Users/<USER>/sourcecode/baidu/inputmethod/iptandroiddemo"
val dir = "${projectDir}/iptcore/src/main/java"
val ids = File(dir, "com/baidu/iptcore/util/Ids.java")
val cellConfig = File(dir, "com/baidu/iptcore/CoreCellConfig.java")
val lib = File(dir, "com/baidu/iptcore/ImeLib.java")
val pad = File(dir, "com/baidu/iptcore/ImePad.java")
val manager = File(dir, "com/baidu/iptcore/ImeCoreManager.java")

val files = arrayListOf(cellConfig, lib, pad, manager)

val methodIds = mutableListOf<MethodInfo>()

val writeMethods = mutableListOf<String>()

// ****** 生成方法id *******
generateIds()
// 生成内核的mapping
generateMethodMapping()
// ****** 生成方法出入口 *******
fillIOs()

fun fillIOs() {
    files.forEach {
        autoFillMethodIO(it)
    }
}

fun autoFillMethodIO(file: File) {
    var hasMethodStart = false
    var oriName: String? = null
    var returnType: String? = null
    var idsName: String? = null
    var leftCount = 0
    var rightCount = 0
    var writeIn = false
    var writeOut = false

    val filelines = file.readText().lines().toMutableList()
    if (filelines.size == 0) {
        return
    }
    val packageLine = (filelines.indexOfFirst {
        it.trim().startsWith("package com.")
    } + 1).coerceAtLeast(1)
    if (!filelines.contains("import com.baidu.input.inputtrace.api.InputTracer;")) {
        filelines[packageLine] =
            "\nimport com.baidu.input.inputtrace.api.InputTracer;\n${filelines[packageLine]}"
    }
    if (!filelines.contains("import com.baidu.iptcore.util.Ids;")) {
        filelines[packageLine] = "\nimport com.baidu.iptcore.util.Ids;\n${filelines[packageLine]}"
    }
    var lineIdx = -1
    file.forEachLine { line ->
        lineIdx++
        // 类申明的行
        val isClassDeclare = line.trim().contains(" class ${file.nameWithoutExtension} ")
        if (!isClassDeclare && line.trim().isNotEmpty()) {
            if (line.contains("{")) {
                leftCount++
            }
            if (line.contains("}")) {
                rightCount++
            }
            val isMethodEnd = rightCount == leftCount && leftCount > 0
            // 是方法开始行
            if (isMethodDeclare(line)) {
                oriName = getMethodName(line)
                returnType = getMethodReturn(line)
                // 开始行
                if (!hasMethodStart && line.contains("{") && line.contains(")")) {
                    hasMethodStart = true
                }
            } else if (!hasMethodStart && line.contains("{") && line.contains(")")) {
                // 开始行
                hasMethodStart = true
            } else if (hasMethodStart) {
                var prefix = ""
                var content = line
                var postfix = ""
                val space = countSpaces(line)
                if (!writeIn) {
                    // 内容第一行
                    oriName?.let {
                        if (!line.contains("InputTracer")) {
                            idsName = it
                            var index = 1
                            while (writeMethods.contains(idsName!!)) {
                                idsName = "$oriName$index"
                                index++
                            }
                            writeMethods.add(idsName!!)
                            prefix = "${countSpaces(space)}InputTracer.i(Ids.${idsName});\n"
                            writeIn = true
                        } else {
                            // 方法第一行就是写入了的，说明已经是处理过了的方法了
                            writeIn = true
                            writeOut = true
                        }
                    }
                }
                // 写入了 in
                if (writeIn && idsName != null) {
                    // 有return语句的可能有多个out
                    if (line.contains("return")) {
                        if (line.contains(")") && line.contains("(")) {
                            val replace = line.replace("return ", "$returnType result = ")
                            content =
                                "$replace\n${countSpaces(space)}InputTracer.o(Ids.${idsName});\n"
                            postfix = "${countSpaces(space)}return result;"
                            writeOut = true
                        } else {
                            prefix += "${countSpaces(space)}InputTracer.o(Ids.${idsName});\n"
                            writeOut = true
                        }
                    } else {
                        // 写入了in，但是没有写入out
                        if (writeIn && !writeOut && isMethodEnd) {
                            prefix += "${countSpaces(space)}    InputTracer.o(Ids.${idsName});\n"
                            // postfix = "${countSpaces(space)}}"
                            writeOut = true
                        }
                    }
                }
                filelines[lineIdx] = prefix + content + postfix
            }
            if (isMethodEnd) {
                hasMethodStart = false
                writeIn = false
                writeOut = false
                oriName = null
                returnType = null
                idsName = null
                leftCount = 0
                rightCount = 0
            }
        }
    }
    val result = File(file.parentFile, file.nameWithoutExtension + ".java")
    result.writeText("")
    filelines.forEachIndexed { index, s ->
        result.appendText(s)
        if (index != filelines.size - 1) {
            result.appendText("\n")
        }
    }
//    filelines.forEach {
//        println(it)
//    }
}

fun generateIds() {
    files.forEach { file ->
        val filename =
            file.absolutePath.split("/com/baidu/")[1].replace(".java", "").replace("/", ".")
        val clazz = "com.baidu.$filename"
        file.forEachLine { line ->
            // 是方法开始行
            if (isMethodDeclare(line)) {
                // 获取方法名
                val name = getMethodName(line)
                name?.let { n ->
                    var method = n
                    var index = 1
                    while (methodIds.find { it.name == method } != null) {
                        method = "$name$index"
                        index++
                    }
                    methodIds.add(MethodInfo(method, clazz))
                }
            }
        }
    }

    val exist = ids.exists()
    val contentLines = mutableListOf<String>()
    if (!exist) {
        ids.writeText(
            "package com.baidu.iptcore.util;\n\n"
                    + "import com.baidu.input.inputtrace.api.MethodIds;\n\n"
                    + "public interface Ids {\n"
        )
        methodIds.forEachIndexed { index, s ->
            ids.appendText("    /** @see ${s.clazz}#${s.name} */\n")
            ids.appendText("    int ${s.name} = MethodIds.CORE_START + ${index + 100};\n")
        }
        ids.appendText("}\n")
    } else {
        contentLines.addAll(ids.readLines())
        var lastNum = 100
        var lastIndex = -1
        contentLines.forEachIndexed { index, s ->
            val tag = "MethodIds.CORE_START"
            if (s.contains(tag)) {
                val split = s.substringAfter(tag).replace(";", "").split(" ")
                split.forEach {
                    val num = if (it.contains("0x")) {
                        it.toIntOrNull(16)
                    } else {
                        it.toIntOrNull()
                    }
                    if (num != null && num != 0xFFF) {
                        lastIndex = index
                        lastNum = num
                    }
                }
            }
        }
        if (lastIndex <= 0) {
            lastIndex = contentLines.indexOfLast { it.trim() == "}" }
        } else {
            lastIndex += 1
        }

        val content = contentLines[lastIndex]
        var prefix = ""
        var insert = lastNum + 1
        methodIds.forEachIndexed { _, s ->
            val find = contentLines.find {
                it.contains(s.name) && it.contains("MethodIds.CORE_START") && it.contains("=")
            }
            if (find == null) {
                prefix += "    /** @see ${s.clazz}#${s.name} */\n"
                prefix += "    int ${s.name} = MethodIds.CORE_START + ${insert++};\n"
            }
        }
        contentLines[lastIndex] = prefix + content
        ids.writeText("")
        contentLines.forEach {
            ids.appendText("$it\n")
        }
    }
}

fun generateMethodMapping() {
    val delimiter = "MethodIds.CORE_START"

    val list = mutableListOf<Triple<String, String, Int>>()
    ids.forEachLine { line ->
        if (line.contains(delimiter) && line.contains("=")) {
            val str = line.substringAfter(delimiter)
            val num = str.replace("+", "").replace(";", "").trim().toIntOrNull()
            if (num != null) {
                val name = line.substringBefore(delimiter).replace("=", "").trim().split(" ").last()
                val method = methodIds.find { name == it.name }
                if (method != null) {
                    list.add(Triple(method.clazz, name, num))
                } else {
                    list.add(Triple("iptcore", name, num))
                }
            }
        }
    }
    val dirs = "$projectDir/build"
    File(dirs).mkdirs()
    val mapping = File(dirs, "methodMapping.txt")
    mapping.writeText("")
    list.sortedBy { it.third }.forEach {
        mapping.appendText("${it.third},1,${it.first} ${it.second} ()V\n")
    }
}


/**
 * 方法声明的行
 */
fun isMethodDeclare(line: String): Boolean {
    val s = line.trim()
    val start = s.startsWith("public ") || s.startsWith("private ") || s.startsWith("protected ")
    val hasMethod = line.contains("(")
    return start && hasMethod
}

/**
 * 构造方法声明的行
 */
fun isConstructMethodDeclare(line: String): Boolean {
    val hasConstruct = line.substringBefore("(").split(" ").size == 2
    return isMethodDeclare(line) && hasConstruct
}

/**
 * 静态方法声明的行
 */
fun isStaticMethodDeclare(line: String): Boolean {
    val static = line.trim().startsWith(" static ")
    return isMethodDeclare(line) && static
}

fun getMethodName(line: String): String? {
    val infos = line.trim().split(" ")
    infos.forEach { str ->
        if (str.contains("(")) {
            return str.substringBefore("(")
        }
    }
    return null
}

fun getMethodReturn(line: String): String? {
    val infos = line.trim().split(" ")
    infos.forEachIndexed { index, str ->
        if (str.contains("(")) {
            return infos[index - 1].trim()
        }
    }
    return null
}

fun countSpaces(line: String): Int {
    var count = 0
    for (char in line) {
        if (char == ' ') {
            count++
        } else {
            break
        }
    }
    return count
}

fun countSpaces(count: Int): String {
    var str = ""
    for (char in 0 until count) {
        str += " "
    }
    return str
}
