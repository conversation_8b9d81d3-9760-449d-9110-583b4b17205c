# !/usr/bin/env python
# -*- coding:utf-8 -*-
"""
Native Trace的解析脚本

@Author: cdf
@File  : NativeTraceParser.py
"""
import subprocess


class CallingPoint(object):
    """
    NativeTrace输出数据中，代表一个方法的I/O的数据结构
    """

    def __init__(self, action, deep, cost, func):
        self.action = action
        self.deep = deep
        self.cost = cost
        self.func = func
        self.stack_line = None

    def __str__(self):
        return "{%d, %d, %d, %d}" % (
            self.action, self.deep, self.cost, self.func)


def parse_native_stack_item(str):
    """
    解析一个native trace项
    """
    result = str.split(",")
    if len(result) < 4:
        return None

    try:
        action = int(result[0])
        deep = int(result[1])
        cost = int(result[2])
        func = int(result[3])
        if (action == 0 or action == 1) and deep > 0 and cost >= 0 and func > 0:
            return CallingPoint(action, deep, cost, func)
        else:
            return None
    except IOError:
        return None


def check_stack_items_valid(stack_items):
    """
    检查取到的堆栈信息是否完整
    :param stack_items: 堆栈信息
    :return: 是否完整
    """
    return True


def is_out_calling_point(callingPoint):
    """
    是否是方法退出节点
    """
    return callingPoint.action == 1


def filter_out_calling_point(stack_items):
    """
    过滤出表征方法退出的CallingPoint
    :param stack_items: 堆栈项
    :return: 过滤出来的CallingPoint
    """
    result = []
    for calling_point in stack_items:
        if is_out_calling_point(calling_point):
            result.append(calling_point)
    return result


def print_one_vertical_stack(stack):
    """
    打印一个纵向Stack
    :param stack: 纵向堆栈
    :return: 纵向堆栈
    """
    vertical_stack = []
    for calling_point in stack:
        vertical_stack.append(
            "func:%s (%dms) (%d)" % (calling_point.stack_line, calling_point.cost, calling_point.deep))
    return vertical_stack


def run_flame_graph(file_name):
    """
    生成火焰图
    :param file_name: 待生成的堆栈名称
    """
    flame_dir = "./flamegraph/FlameGraph-master"
    subprocess.call("%s/stackcollapse.pl %s > %s" % (flame_dir, file_name + ".txt", file_name + ".folded"), shell=True)
    subprocess.call("%s/flamegraph.pl %s > %s" % (flame_dir, file_name + ".folded", file_name + ".svg"), shell=True)


def retrace_lines(stack_items):
    """
    解析堆栈里面的符号和行号
    """

    addrs = ""
    for item in stack_items:
        addrs += format(item.func, 'x')
        addrs += " "

    cmd = "sh ./addr2line/run.sh \"%s\"" % addrs
    print(cmd)
    output = subprocess.Popen([cmd], stdout=subprocess.PIPE, shell=True).communicate()
    if output and len(output) >= 1 and output[0]:
        all_str = str(output[0]).strip()
        if all_str[0] == 'b' and all_str[1] == '\'' and all_str[-1] == '\'':
            all_str = all_str[2:-1]
        stack_lines = all_str.split("\\n")
        for i in range(0, len(stack_lines)):
            stack_line = stack_lines[i]
            if "/source" in stack_line:
                index = stack_line.index("/source")
                if index >= 0:
                    stack_lines[i] = stack_line[index:]

        if len(stack_lines) >= len(stack_items):
            for i in range(0, len(stack_items)):
                stack_items[i].stack_line = stack_lines[i]


def parse(stack, line_number, res_dir):
    """解析一段native trace信息
    :param line_number: 指定stack在原始数据的行号
    :param stack: native trace抓取的stack信息
    """
    result = stack.split(";")
    stack_items = []
    for item in result:
        stack_item = parse_native_stack_item(item)
        if stack_item:
            stack_items.append(stack_item)

    if len(stack_items) <= 0 or len(stack_items) % 2 != 0:
        return

    if not check_stack_items_valid(stack_items):
        return

    stack_items = filter_out_calling_point(stack_items)

    retrace_lines(stack_items)

    for calling_point in stack_items:
        print(calling_point)

    length = len(stack_items)
    flags = [False] * length
    max_deep = 0
    vertical_stacks = []

    for i in range(0, length):
        max_deep = max(stack_items[i].deep, max_deep)

        if flags[i]:
            continue

        stack = [stack_items[i]]
        flags[i] = True
        now_deep = stack_items[i].deep
        if now_deep > 1:
            for j in range(i + 1, len(stack_items)):
                if stack_items[j].deep == (now_deep - 1):
                    stack.append(stack_items[j])
                    flags[j] = True
                    now_deep -= 1
        vertical_stacks.append(print_one_vertical_stack(stack))

    with open(res_dir + "native_output_" + str(line_number) + ".txt", 'w') as f:
        for item in vertical_stacks:
            length = len(item)
            if length <= (max_deep + 1):
                for i in range(0, max_deep + 1 - length):
                    f.write("\n")
                for s in item:
                    f.write(s)
                    f.write("\n")
            f.write("1\n")

    run_flame_graph(res_dir + "native_output_" + str(line_number))
