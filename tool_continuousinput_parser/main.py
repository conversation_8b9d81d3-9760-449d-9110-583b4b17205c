#!/usr/bin/env python
# -*- coding:utf-8 -*-
"""
解析连续输入性能测试结果的脚本
1. pull文件到PC
2. 解析测试结果(生成一个report文件和一张统计图表)

@Author: cdf
@File  : main.py
"""

import os
import subprocess
import sys

import numpy as np
import matplotlib.pyplot as plt

from enum import Enum
import json

from NativeTraceParser import parse


def help():
    """
    帮助功能
    :return:
    """
    print('''==** 脚本使用方法 **==
需要传递2个参数，第一个参数是当前的动作，第二个参数是待分析的输入方式名称，第三个参数是device名称(adb devices获取一下)，第四个参数是PC的工作路径，示例如下：
1. python main.py pull [py26|py9|en26] 62b84fae ./res/ # 拉取手机端的数据
2. python main.py ana [py26|py9|en26] 62b84fae ./res/ # 分析指定类型的数据
3. python main.py ana_plot [py26|py9|en26] 62b84fae ./res/ # 分析指定类型数据，同时展示图表
    ''')


class CMD(Enum):
    """脚本命令枚举定义，Pull命令和Analyse命令"""
    UNKNOWN = 0
    PULL = 1
    ANALYSE = 2
    ANALYSE_AND_PLOT = 3


class TYPE(Enum):
    """待分析的数据类型的枚举定义"""
    UNKNOWN = 0
    PY26 = 1
    PY9 = 2
    EN26 = 3


class ProfilerOutputItem:
    """待分析的文件的每行对应的一次接口输出信息对象

    time: 接口结束的时间戳
    action: 接口动作(名称)
    cost: 耗时(ms)
    costDetail: 耗时的内部辅助信息
    pad: 面板信息
    code: 当前接口完成后对应的输入码
    firstWord: 当前接口完成后对应的首选词信息

    """

    def __init__(self, time, action, cost, cost_detail, pad, code, first_word):
        self.time = time
        self.action = action
        self.cost = cost
        self.costDetail = cost_detail
        self.pad = pad
        self.code = code
        self.firstWord = first_word

    def __str__(self):
        return "{time:%s, action:%s, cost=%d, costDetail=%s, pad=%s, code=%s, firstWord=%s}" % (
            self.time, self.action, self.cost, self.costDetail, self.pad, self.code, self.firstWord)

    def valid_code_len(self):
        """获得有效的输入码长度(去掉分词符的长度)，例如输入women，则code展示为wo'men，有效长度是5"""
        if self.code:
            valid_code = self.code.replace("'", '')
            return len(valid_code)
        return 0


def get_cmd(input_cmd):
    """根据终端输入的参数字符串，解析出当前待执行的脚本命令"""
    if input_cmd:
        if input_cmd.strip().lower() == "pull":
            return CMD.PULL
        elif input_cmd.strip().lower() == "ana":
            return CMD.ANALYSE
        elif input_cmd.strip().lower() == "ana_plot":
            return CMD.ANALYSE_AND_PLOT
    return CMD.UNKNOWN


def get_type(input_type):
    """根据终端输入的参数字符串，解析出当前待分析的类型"""
    if input_type:
        for type in (TYPE.PY9, TYPE.PY26, TYPE.EN26):
            if input_type.strip().lower() == base_name(type):
                return type
    return TYPE.UNKNOWN


def pull(analyse_type, device_id, res_dir):
    """从手机拉取log信息"""
    log_path = "/sdcard/Android/data/com.baidu.input/files/iptcoreLogger/logs_0.txt"
    pull_cmd = "adb -s %s pull %s %s" \
               % (device_id, log_path, get_analyse_output_filename(res_dir, analyse_type))
    del_cmd = "adb shell rm %s" % (log_path)
    subprocess.call(pull_cmd, shell=True)
    subprocess.call(del_cmd, shell=True)


def base_name(analyse_type):
    """根据当前待分析的类型，获取对应的base名称"""
    if analyse_type == TYPE.PY26:
        return "py26"
    elif analyse_type == TYPE.EN26:
        return "en26"
    elif analyse_type == TYPE.PY9:
        return "py9"
    else:
        return "unknown"


def get_analyse_output_filename(res_dir, analyse_type):
    """根据当前待分析的类型，获取连续输入后的输出文件名称，即待分析的文件名称"""
    return res_dir + "continuous_" + base_name(analyse_type) + "_output.txt"


def get_analyse_report_filename(res_dir, analyse_type):
    """根据当前待分析的类型，获取分析后产生的report文件名"""
    return res_dir + "continuous_" + base_name(analyse_type) + "_report.md"


def get_analyse_png_filename(res_dir, analyse_type):
    """根据当前待分析的类型，获取分析后产生的图表文件名"""
    return res_dir + "continuous_" + base_name(analyse_type) + "_main_plot.png"


def filter_valid_continuous_output_item(analyse_type, output_item):
    """过滤有效的输出结果"""
    if output_item and output_item.pad:
        if base_name(analyse_type) in output_item.pad.lower():
            return True
    return False


def parse_native_trace_from_file(analyse_type, res_dir):
    """根据待分析的类型，解析连续输入中，native_trace的部分

    :param res_dir: 工作路径，包括待读取的文件路径和输出文件路径
    :param analyse_type: 待分析的类型
    """
    filename = get_analyse_output_filename(res_dir, analyse_type)

    # 先将文件中所有的nativeTrace信息读取出来
    native_trace_items = []
    with open(filename) as f:
        line_number = 0
        for line in f:
            result = pre_parse_native_trace_json_line(line, line_number)
            if result:
                native_trace_items.append(result)
            line_number += 1

    # 按照cost排序，取前5位
    native_trace_items.sort(key=lambda item: item[1]["cost"], reverse=True)
    native_trace_items = native_trace_items[:5]

    for item in native_trace_items:
        parse_native_trace_json_line(item[1], item[0], res_dir)


def pre_parse_native_trace_json_line(line, line_number):
    """预解析一行json数据，获取native trace信息

    :param line_number: line在原始文件中的行号
    :param line: 待解析的json字符串
    :return (lineNumber, json_obj)的元组对
    """
    if line:
        json_obj = json.loads(line)
        if json_obj and ("type" in json_obj) and ("stack" in json_obj) and ("cost" in json_obj):
            return line_number, json_obj
    return None


def parse_native_trace_json_line(json_obj, line_number, res_dir):
    """解析一行json数据，获取native trace信息

    :param res_dir: 工作路径
    :param line_number: json在原始文件中的行号
    :param json_obj: 待解析的json
    """
    if json_obj and ("type" in json_obj) and ("stack" in json_obj):
        if json_obj["type"] == "NATIVE_TRACE":
            parse(json_obj["stack"], line_number, res_dir)


def parse_output_file(analyse_type, res_dir):
    """根据待分析的类型，解析连续输入后的输出文件(即待解析的文件)

    :param analyse_type: 待分析的类型
    :return: 接口输出信息ProfilerOutputItem的List
    """
    result = []
    filename = get_analyse_output_filename(res_dir, analyse_type)
    with open(filename) as f:
        for line in f:
            output_item = parse_json_line(analyse_type, line, filter_valid_continuous_output_item)
            if output_item:
                result.append(output_item)
    result.sort(key=lambda item: item.cost)
    return result


def parse_json_line(analyse_type, line, filter_func):
    """解析一行json数据

    :param line: 待解析的json字符串
    :param filter_func: 对解析获得的输出对象，进行过滤的函数
    :return: ProfilerOutputItem的对象，无效的返回None
    """
    if line:
        json_obj = json.loads(line)
        if json_obj and ("time" in json_obj) and ("action" in json_obj) and ("cost" in json_obj):
            output_item = ProfilerOutputItem(
                json_obj["time"],
                json_obj["action"],
                json_obj["cost"],
                json_obj["costDetail"] if "costDetail" in json_obj else None,
                json_obj["pad"] if "pad" in json_obj else None,
                json_obj["code"] if "code" in json_obj else None,
                json_obj["firstWord"] if "firstWord" in json_obj else None,
            )
            if filter_func and filter_func(analyse_type, output_item):
                return output_item
    return None


def generate_bar_plot(analyse_type, res_dir, result, show_plot):
    """根据解析的结果列表，生成直方图

    :param analyse_type: 待分析的类型
    :param result: 解析过的ProfilerOutputItem的列表
    :param show_plot: 是否直接展示图表
    """

    # 直方图的x轴, y轴值
    values = []
    for item in result:
        values.append(item.cost)
    index = np.arange(len(result))

    # 设置figsize, 默认的像素：[6.0,4.0]，分辨率为100，图片尺寸为 600&400
    # 指定dpi=200，图片尺寸为 1200*800，以此类推
    plt.rcParams['savefig.dpi'] = 200
    plt.rcParams['figure.dpi'] = 100

    # 绘制柱状图
    plt.bar(index, values, color="#87CEFA")
    plt.xlabel('Times')
    plt.ylabel('Cost')
    plt.title('Continuous Input Result ' + str(analyse_type))

    # 保存图片
    plt.savefig(os.path.join(os.getcwd(), get_analyse_png_filename(res_dir, analyse_type)))

    # 展示图表
    if show_plot:
        plt.show()


def report(analyse_type, res_dir, result):
    """根据解析的结果列表，生成Report

    :param analyse_type: 待分析的类型
    :param result: 解析过的ProfilerOutputItem的列表
    """
    report_lines = []

    # 1. 总览：总数、平均数、中位数
    values = []
    for item in result:
        values.append(item.cost)

    total_cnt = len(result)
    total_avg = np.mean(values)
    total_median = np.median(values)
    total_max = max(values)

    report_lines.append("# Report For %s " % analyse_type)
    report_lines.append("\n## 概览")
    report_lines.append("测试总数量: %d  " % total_cnt)
    report_lines.append("总平均耗时: %.2f  " % total_avg)
    report_lines.append("所有接口耗时的中位数: %.2f  " % total_median)
    report_lines.append("所有接口耗时的最大值: %.2f  " % total_max)
    report_lines.append("![image](./%s)  " % get_analyse_png_filename(res_dir, analyse_type))

    # 2. top10耗时详细信息
    top_output_items = []
    for i in range(len(result) - 10, len(result)):
        top_output_items.append(result[i])

    report_lines.append("\n## Top 10 耗时详情：")
    for i in range(0, 10):
        report_lines.append(str(top_output_items[i]) + "  ")

    # 3. 分输入码个数统计
    code_result_dict = {}
    for item in result:
        code_len = item.valid_code_len()
        if code_len in code_result_dict:
            code_result_dict[code_len].append(item)
        else:
            code_result_dict[code_len] = []
            code_result_dict[code_len].append(item)

    report_lines.append("\n## 分Code长度耗时分布详情：")
    for key, value in sorted(code_result_dict.items()):
        sub_values = []
        for item in value:
            sub_values.append(item.cost)
        cnt = len(sub_values)
        avg = np.mean(sub_values)
        report_lines.append("code_len [%d] 统计信息: 总数量 %d, 平均耗时 %.2f  " % (key, cnt, avg))

    # 4. report
    for line in report_lines:
        print(line)
    report_filename = get_analyse_report_filename(res_dir, analyse_type)
    with open(report_filename, 'w') as f:
        for line in report_lines:
            f.write(line)
            f.write('\n')


def analyse(cmd, analyse_type, res_dir):
    """针对终端指定的参数，执行分析

    :param cmd: 当前的分析命令
    :param analyse_type: 当前的分析类型
    """
    result = parse_output_file(analyse_type, res_dir)
    generate_bar_plot(analyse_type, res_dir, result, True if cmd == CMD.ANALYSE_AND_PLOT else False)

    parse_native_trace_from_file(analyse_type, res_dir)
    report(analyse_type, res_dir, result)


def is_all_num(code):
    """
    是否全部都是数字
    :param code: 给定的输入码
    :return: 是否全部都是数字
    """
    for a in code:
        if '0' <= a <= '9' or a == '#':
            continue
        else:
            return False
    return True


def is_all_alpha(code):
    """
    是否全部都是字母
    :param code: 给定的输入码
    :return: 是否全部都是字母
    """
    for a in code:
        if 'a' <= a <= 'z' or a == '#':
            continue
        else:
            return False
    return True


if __name__ == '__main__':
    if len(sys.argv) == 5:
        cmd = get_cmd(str(sys.argv[1]))
        analyse_type = get_type(str(sys.argv[2]))
        if cmd == CMD.UNKNOWN or analyse_type == TYPE.UNKNOWN:
            help()
        elif cmd == CMD.PULL:
            pull(analyse_type, str(sys.argv[3]), str(sys.argv[4]))
        else:
            analyse(cmd, analyse_type, str(sys.argv[4]))
    else:
        help()

    # cmd = get_cmd("ana")
    # analyse_type = get_type("py26")
    # analyse(cmd, analyse_type, "res/")



