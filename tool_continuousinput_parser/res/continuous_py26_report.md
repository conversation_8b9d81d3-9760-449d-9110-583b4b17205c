# Report For TYPE.PY26 

## 概览
测试总数量: 185  
总平均耗时: 7.32  
所有接口耗时的中位数: 6.00  
所有接口耗时的最大值: 39.00  
![image](././res/continuous_py26_main_plot.png)  

## Top 10 耗时详情：
{time:2021-06-16 14:03:52, action:actKeyClicked@CoreThread: keyId=115, inputType=0, uni=null, x=266, y=239, power=0, area=5, cost=18, costDetail=(env:1, data:4, key:14), pad=PY26_11, code=j's'j's, firstWord=就是就是}  
{time:2021-06-16 14:04:02, action:actKeyClicked@CoreThread: keyId=100, inputType=0, uni=null, x=313, y=355, power=0, area=5, cost=18, costDetail=(env:1, data:11, key:7), pad=PY26_11, code=j's'n'd, firstWord=就是你的}  
{time:2021-06-16 14:04:04, action:actKeyClicked@CoreThread: keyId=110, inputType=0, uni=null, x=715, y=377, power=0, area=4, cost=19, costDetail=(env:1, data:10, key:8), pad=PY26_11, code=j's'j's'n, firstWord=就是就是你}  
{time:2021-06-16 14:03:58, action:actKeyClicked@CoreThread: keyId=104, inputType=0, uni=null, x=691, y=370, power=0, area=5, cost=23, costDetail=(env:0, data:7, key:15), pad=PY26_11, code=j'd'j'd'h, firstWord=记得记得好}  
{time:2021-06-16 14:03:48, action:actKeyClicked@CoreThread: keyId=106, inputType=0, uni=null, x=722, y=297, power=0, area=4, cost=25, costDetail=(env:1, data:9, key:15), pad=PY26_11, code=j's'j'd'j, firstWord=技术监督局}  
{time:2021-06-16 14:03:52, action:actKeyClicked@CoreThread: keyId=106, inputType=0, uni=null, x=721, y=346, power=0, area=3, cost=25, costDetail=(env:0, data:17, key:7), pad=PY26_11, code=j's'j, firstWord=计算机}  
{time:2021-06-16 14:04:05, action:actKeyClicked@CoreThread: keyId=100, inputType=0, uni=null, x=291, y=344, power=0, area=5, cost=27, costDetail=(env:1, data:6, key:20), pad=PY26_11, code=j's'j'd, firstWord=就是觉得}  
{time:2021-06-16 14:04:04, action:actKeyClicked@CoreThread: keyId=115, inputType=0, uni=null, x=256, y=324, power=0, area=5, cost=37, costDetail=(env:5, data:12, key:24), pad=PY26_11, code=j's'j's, firstWord=就是就是}  
{time:2021-06-16 14:04:01, action:actKeyClicked@CoreThread: keyId=120, inputType=0, uni=null, x=302, y=440, power=0, area=4, cost=38, costDetail=(env:0, data:14, key:22), pad=PY26_11, code=j's'n'x, firstWord=就是那些}  
{time:2021-06-16 14:03:57, action:actKeyClicked@CoreThread: keyId=106, inputType=0, uni=null, x=715, y=300, power=0, area=4, cost=39, costDetail=(env:3, data:15, key:18), pad=PY26_11, code=j's'n'x'j, firstWord=就是那些就}  

## 分Code长度耗时分布详情：
code_len [0] 统计信息: 总数量 98, 平均耗时 2.80  
code_len [1] 统计信息: 总数量 15, 平均耗时 8.60  
code_len [2] 统计信息: 总数量 15, 平均耗时 11.13  
code_len [3] 统计信息: 总数量 15, 平均耗时 11.73  
code_len [4] 统计信息: 总数量 15, 平均耗时 15.60  
code_len [5] 统计信息: 总数量 14, 平均耗时 14.86  
code_len [6] 统计信息: 总数量 8, 平均耗时 13.12  
code_len [7] 统计信息: 总数量 5, 平均耗时 12.40  
